"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@opentelemetry+instrumentat_340b4582e894fcadfee9bcc3d638a1af";
exports.ids = ["vendor-chunks/@opentelemetry+instrumentat_340b4582e894fcadfee9bcc3d638a1af"];
exports.modules = {

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_340b4582e894fcadfee9bcc3d638a1af/node_modules/@opentelemetry/instrumentation-ioredis/build/src/index.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_340b4582e894fcadfee9bcc3d638a1af/node_modules/@opentelemetry/instrumentation-ioredis/build/src/index.js ***!
  \********************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_340b4582e894fcadfee9bcc3d638a1af/node_modules/@opentelemetry/instrumentation-ioredis/build/src/instrumentation.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_340b4582e894fcadfee9bcc3d638a1af/node_modules/@opentelemetry/instrumentation-ioredis/build/src/types.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_340b4582e894fcadfee9bcc3d638a1af/node_modules/@opentelemetry/instrumentation-ioredis/build/src/index.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_340b4582e894fcadfee9bcc3d638a1af/node_modules/@opentelemetry/instrumentation-ioredis/build/src/instrumentation.js":
/*!******************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_340b4582e894fcadfee9bcc3d638a1af/node_modules/@opentelemetry/instrumentation-ioredis/build/src/instrumentation.js ***!
  \******************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.IORedisInstrumentation = void 0;\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst instrumentation_2 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_340b4582e894fcadfee9bcc3d638a1af/node_modules/@opentelemetry/instrumentation-ioredis/build/src/utils.js\");\nconst redis_common_1 = __webpack_require__(/*! @opentelemetry/redis-common */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+redis-common@0.36.2/node_modules/@opentelemetry/redis-common/build/src/index.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_340b4582e894fcadfee9bcc3d638a1af/node_modules/@opentelemetry/instrumentation-ioredis/build/src/version.js\");\nconst DEFAULT_CONFIG = {\n    requireParentSpan: true,\n};\nclass IORedisInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, Object.assign(Object.assign({}, DEFAULT_CONFIG), config));\n    }\n    setConfig(config = {}) {\n        super.setConfig(Object.assign(Object.assign({}, DEFAULT_CONFIG), config));\n    }\n    init() {\n        return [\n            new instrumentation_1.InstrumentationNodeModuleDefinition('ioredis', ['>=2.0.0 <6'], (module, moduleVersion) => {\n                const moduleExports = module[Symbol.toStringTag] === 'Module'\n                    ? module.default // ESM\n                    : module; // CommonJS\n                if ((0, instrumentation_1.isWrapped)(moduleExports.prototype.sendCommand)) {\n                    this._unwrap(moduleExports.prototype, 'sendCommand');\n                }\n                this._wrap(moduleExports.prototype, 'sendCommand', this._patchSendCommand(moduleVersion));\n                if ((0, instrumentation_1.isWrapped)(moduleExports.prototype.connect)) {\n                    this._unwrap(moduleExports.prototype, 'connect');\n                }\n                this._wrap(moduleExports.prototype, 'connect', this._patchConnection());\n                return module;\n            }, module => {\n                if (module === undefined)\n                    return;\n                const moduleExports = module[Symbol.toStringTag] === 'Module'\n                    ? module.default // ESM\n                    : module; // CommonJS\n                this._unwrap(moduleExports.prototype, 'sendCommand');\n                this._unwrap(moduleExports.prototype, 'connect');\n            }),\n        ];\n    }\n    /**\n     * Patch send command internal to trace requests\n     */\n    _patchSendCommand(moduleVersion) {\n        return (original) => {\n            return this._traceSendCommand(original, moduleVersion);\n        };\n    }\n    _patchConnection() {\n        return (original) => {\n            return this._traceConnection(original);\n        };\n    }\n    _traceSendCommand(original, moduleVersion) {\n        const instrumentation = this;\n        return function (cmd) {\n            if (arguments.length < 1 || typeof cmd !== 'object') {\n                return original.apply(this, arguments);\n            }\n            const config = instrumentation.getConfig();\n            const dbStatementSerializer = config.dbStatementSerializer || redis_common_1.defaultDbStatementSerializer;\n            const hasNoParentSpan = api_1.trace.getSpan(api_1.context.active()) === undefined;\n            if (config.requireParentSpan === true && hasNoParentSpan) {\n                return original.apply(this, arguments);\n            }\n            const span = instrumentation.tracer.startSpan(cmd.name, {\n                kind: api_1.SpanKind.CLIENT,\n                attributes: {\n                    [semantic_conventions_1.SEMATTRS_DB_SYSTEM]: semantic_conventions_1.DBSYSTEMVALUES_REDIS,\n                    [semantic_conventions_1.SEMATTRS_DB_STATEMENT]: dbStatementSerializer(cmd.name, cmd.args),\n                },\n            });\n            const { requestHook } = config;\n            if (requestHook) {\n                (0, instrumentation_2.safeExecuteInTheMiddle)(() => requestHook(span, {\n                    moduleVersion,\n                    cmdName: cmd.name,\n                    cmdArgs: cmd.args,\n                }), e => {\n                    if (e) {\n                        api_1.diag.error('ioredis instrumentation: request hook failed', e);\n                    }\n                }, true);\n            }\n            const { host, port } = this.options;\n            span.setAttributes({\n                [semantic_conventions_1.SEMATTRS_NET_PEER_NAME]: host,\n                [semantic_conventions_1.SEMATTRS_NET_PEER_PORT]: port,\n                [semantic_conventions_1.SEMATTRS_DB_CONNECTION_STRING]: `redis://${host}:${port}`,\n            });\n            try {\n                const result = original.apply(this, arguments);\n                const origResolve = cmd.resolve;\n                /* eslint-disable @typescript-eslint/no-explicit-any */\n                cmd.resolve = function (result) {\n                    (0, instrumentation_2.safeExecuteInTheMiddle)(() => { var _a; return (_a = config.responseHook) === null || _a === void 0 ? void 0 : _a.call(config, span, cmd.name, cmd.args, result); }, e => {\n                        if (e) {\n                            api_1.diag.error('ioredis instrumentation: response hook failed', e);\n                        }\n                    }, true);\n                    (0, utils_1.endSpan)(span, null);\n                    origResolve(result);\n                };\n                const origReject = cmd.reject;\n                cmd.reject = function (err) {\n                    (0, utils_1.endSpan)(span, err);\n                    origReject(err);\n                };\n                return result;\n            }\n            catch (error) {\n                (0, utils_1.endSpan)(span, error);\n                throw error;\n            }\n        };\n    }\n    _traceConnection(original) {\n        const instrumentation = this;\n        return function () {\n            const hasNoParentSpan = api_1.trace.getSpan(api_1.context.active()) === undefined;\n            if (instrumentation.getConfig().requireParentSpan === true &&\n                hasNoParentSpan) {\n                return original.apply(this, arguments);\n            }\n            const span = instrumentation.tracer.startSpan('connect', {\n                kind: api_1.SpanKind.CLIENT,\n                attributes: {\n                    [semantic_conventions_1.SEMATTRS_DB_SYSTEM]: semantic_conventions_1.DBSYSTEMVALUES_REDIS,\n                    [semantic_conventions_1.SEMATTRS_DB_STATEMENT]: 'connect',\n                },\n            });\n            const { host, port } = this.options;\n            span.setAttributes({\n                [semantic_conventions_1.SEMATTRS_NET_PEER_NAME]: host,\n                [semantic_conventions_1.SEMATTRS_NET_PEER_PORT]: port,\n                [semantic_conventions_1.SEMATTRS_DB_CONNECTION_STRING]: `redis://${host}:${port}`,\n            });\n            try {\n                const client = original.apply(this, arguments);\n                (0, utils_1.endSpan)(span, null);\n                return client;\n            }\n            catch (error) {\n                (0, utils_1.endSpan)(span, error);\n                throw error;\n            }\n        };\n    }\n}\nexports.IORedisInstrumentation = IORedisInstrumentation;\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_340b4582e894fcadfee9bcc3d638a1af/node_modules/@opentelemetry/instrumentation-ioredis/build/src/instrumentation.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_340b4582e894fcadfee9bcc3d638a1af/node_modules/@opentelemetry/instrumentation-ioredis/build/src/types.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_340b4582e894fcadfee9bcc3d638a1af/node_modules/@opentelemetry/instrumentation-ioredis/build/src/types.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9Ab3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfMzQwYjQ1ODJlODk0ZmNhZGZlZTliY2MzZDYzOGExYWYvbm9kZV9tb2R1bGVzL0BvcGVudGVsZW1ldHJ5L2luc3RydW1lbnRhdGlvbi1pb3JlZGlzL2J1aWxkL3NyYy90eXBlcy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbndhclxcRG9jdW1lbnRzXFxDdWJlbnR3ZWJcXGN1YmVudFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQG9wZW50ZWxlbWV0cnkraW5zdHJ1bWVudGF0XzM0MGI0NTgyZTg5NGZjYWRmZWU5YmNjM2Q2MzhhMWFmXFxub2RlX21vZHVsZXNcXEBvcGVudGVsZW1ldHJ5XFxpbnN0cnVtZW50YXRpb24taW9yZWRpc1xcYnVpbGRcXHNyY1xcdHlwZXMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG4vKlxuICogQ29weXJpZ2h0IFRoZSBPcGVuVGVsZW1ldHJ5IEF1dGhvcnNcbiAqXG4gKiBMaWNlbnNlZCB1bmRlciB0aGUgQXBhY2hlIExpY2Vuc2UsIFZlcnNpb24gMi4wICh0aGUgXCJMaWNlbnNlXCIpO1xuICogeW91IG1heSBub3QgdXNlIHRoaXMgZmlsZSBleGNlcHQgaW4gY29tcGxpYW5jZSB3aXRoIHRoZSBMaWNlbnNlLlxuICogWW91IG1heSBvYnRhaW4gYSBjb3B5IG9mIHRoZSBMaWNlbnNlIGF0XG4gKlxuICogICAgICBodHRwczovL3d3dy5hcGFjaGUub3JnL2xpY2Vuc2VzL0xJQ0VOU0UtMi4wXG4gKlxuICogVW5sZXNzIHJlcXVpcmVkIGJ5IGFwcGxpY2FibGUgbGF3IG9yIGFncmVlZCB0byBpbiB3cml0aW5nLCBzb2Z0d2FyZVxuICogZGlzdHJpYnV0ZWQgdW5kZXIgdGhlIExpY2Vuc2UgaXMgZGlzdHJpYnV0ZWQgb24gYW4gXCJBUyBJU1wiIEJBU0lTLFxuICogV0lUSE9VVCBXQVJSQU5USUVTIE9SIENPTkRJVElPTlMgT0YgQU5ZIEtJTkQsIGVpdGhlciBleHByZXNzIG9yIGltcGxpZWQuXG4gKiBTZWUgdGhlIExpY2Vuc2UgZm9yIHRoZSBzcGVjaWZpYyBsYW5ndWFnZSBnb3Zlcm5pbmcgcGVybWlzc2lvbnMgYW5kXG4gKiBsaW1pdGF0aW9ucyB1bmRlciB0aGUgTGljZW5zZS5cbiAqL1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dHlwZXMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_340b4582e894fcadfee9bcc3d638a1af/node_modules/@opentelemetry/instrumentation-ioredis/build/src/types.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_340b4582e894fcadfee9bcc3d638a1af/node_modules/@opentelemetry/instrumentation-ioredis/build/src/utils.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_340b4582e894fcadfee9bcc3d638a1af/node_modules/@opentelemetry/instrumentation-ioredis/build/src/utils.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.endSpan = void 0;\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst endSpan = (span, err) => {\n    if (err) {\n        span.recordException(err);\n        span.setStatus({\n            code: api_1.SpanStatusCode.ERROR,\n            message: err.message,\n        });\n    }\n    span.end();\n};\nexports.endSpan = endSpan;\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9Ab3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfMzQwYjQ1ODJlODk0ZmNhZGZlZTliY2MzZDYzOGExYWYvbm9kZV9tb2R1bGVzL0BvcGVudGVsZW1ldHJ5L2luc3RydW1lbnRhdGlvbi1pb3JlZGlzL2J1aWxkL3NyYy91dGlscy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxlQUFlO0FBQ2YsY0FBYyxtQkFBTyxDQUFDLDZJQUFvQjtBQUMxQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0EsZUFBZTtBQUNmIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFud2FyXFxEb2N1bWVudHNcXEN1YmVudHdlYlxcY3ViZW50XFxub2RlX21vZHVsZXNcXC5wbnBtXFxAb3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfMzQwYjQ1ODJlODk0ZmNhZGZlZTliY2MzZDYzOGExYWZcXG5vZGVfbW9kdWxlc1xcQG9wZW50ZWxlbWV0cnlcXGluc3RydW1lbnRhdGlvbi1pb3JlZGlzXFxidWlsZFxcc3JjXFx1dGlscy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbi8qXG4gKiBDb3B5cmlnaHQgVGhlIE9wZW5UZWxlbWV0cnkgQXV0aG9yc1xuICpcbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4gKiB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuXG4gKiBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbiAqXG4gKiAgICAgIGh0dHBzOi8vd3d3LmFwYWNoZS5vcmcvbGljZW5zZXMvTElDRU5TRS0yLjBcbiAqXG4gKiBVbmxlc3MgcmVxdWlyZWQgYnkgYXBwbGljYWJsZSBsYXcgb3IgYWdyZWVkIHRvIGluIHdyaXRpbmcsIHNvZnR3YXJlXG4gKiBkaXN0cmlidXRlZCB1bmRlciB0aGUgTGljZW5zZSBpcyBkaXN0cmlidXRlZCBvbiBhbiBcIkFTIElTXCIgQkFTSVMsXG4gKiBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgQ09ORElUSU9OUyBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC5cbiAqIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmRcbiAqIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuICovXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLmVuZFNwYW4gPSB2b2lkIDA7XG5jb25zdCBhcGlfMSA9IHJlcXVpcmUoXCJAb3BlbnRlbGVtZXRyeS9hcGlcIik7XG5jb25zdCBlbmRTcGFuID0gKHNwYW4sIGVycikgPT4ge1xuICAgIGlmIChlcnIpIHtcbiAgICAgICAgc3Bhbi5yZWNvcmRFeGNlcHRpb24oZXJyKTtcbiAgICAgICAgc3Bhbi5zZXRTdGF0dXMoe1xuICAgICAgICAgICAgY29kZTogYXBpXzEuU3BhblN0YXR1c0NvZGUuRVJST1IsXG4gICAgICAgICAgICBtZXNzYWdlOiBlcnIubWVzc2FnZSxcbiAgICAgICAgfSk7XG4gICAgfVxuICAgIHNwYW4uZW5kKCk7XG59O1xuZXhwb3J0cy5lbmRTcGFuID0gZW5kU3Bhbjtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXV0aWxzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_340b4582e894fcadfee9bcc3d638a1af/node_modules/@opentelemetry/instrumentation-ioredis/build/src/utils.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_340b4582e894fcadfee9bcc3d638a1af/node_modules/@opentelemetry/instrumentation-ioredis/build/src/version.js":
/*!**********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_340b4582e894fcadfee9bcc3d638a1af/node_modules/@opentelemetry/instrumentation-ioredis/build/src/version.js ***!
  \**********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.47.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-ioredis';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9Ab3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfMzQwYjQ1ODJlODk0ZmNhZGZlZTliY2MzZDYzOGExYWYvbm9kZV9tb2R1bGVzL0BvcGVudGVsZW1ldHJ5L2luc3RydW1lbnRhdGlvbi1pb3JlZGlzL2J1aWxkL3NyYy92ZXJzaW9uLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELG9CQUFvQixHQUFHLHVCQUF1QjtBQUM5QztBQUNBLHVCQUF1QjtBQUN2QixvQkFBb0I7QUFDcEIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYW53YXJcXERvY3VtZW50c1xcQ3ViZW50d2ViXFxjdWJlbnRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF8zNDBiNDU4MmU4OTRmY2FkZmVlOWJjYzNkNjM4YTFhZlxcbm9kZV9tb2R1bGVzXFxAb3BlbnRlbGVtZXRyeVxcaW5zdHJ1bWVudGF0aW9uLWlvcmVkaXNcXGJ1aWxkXFxzcmNcXHZlcnNpb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG4vKlxuICogQ29weXJpZ2h0IFRoZSBPcGVuVGVsZW1ldHJ5IEF1dGhvcnNcbiAqXG4gKiBMaWNlbnNlZCB1bmRlciB0aGUgQXBhY2hlIExpY2Vuc2UsIFZlcnNpb24gMi4wICh0aGUgXCJMaWNlbnNlXCIpO1xuICogeW91IG1heSBub3QgdXNlIHRoaXMgZmlsZSBleGNlcHQgaW4gY29tcGxpYW5jZSB3aXRoIHRoZSBMaWNlbnNlLlxuICogWW91IG1heSBvYnRhaW4gYSBjb3B5IG9mIHRoZSBMaWNlbnNlIGF0XG4gKlxuICogICAgICBodHRwczovL3d3dy5hcGFjaGUub3JnL2xpY2Vuc2VzL0xJQ0VOU0UtMi4wXG4gKlxuICogVW5sZXNzIHJlcXVpcmVkIGJ5IGFwcGxpY2FibGUgbGF3IG9yIGFncmVlZCB0byBpbiB3cml0aW5nLCBzb2Z0d2FyZVxuICogZGlzdHJpYnV0ZWQgdW5kZXIgdGhlIExpY2Vuc2UgaXMgZGlzdHJpYnV0ZWQgb24gYW4gXCJBUyBJU1wiIEJBU0lTLFxuICogV0lUSE9VVCBXQVJSQU5USUVTIE9SIENPTkRJVElPTlMgT0YgQU5ZIEtJTkQsIGVpdGhlciBleHByZXNzIG9yIGltcGxpZWQuXG4gKiBTZWUgdGhlIExpY2Vuc2UgZm9yIHRoZSBzcGVjaWZpYyBsYW5ndWFnZSBnb3Zlcm5pbmcgcGVybWlzc2lvbnMgYW5kXG4gKiBsaW1pdGF0aW9ucyB1bmRlciB0aGUgTGljZW5zZS5cbiAqL1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5QQUNLQUdFX05BTUUgPSBleHBvcnRzLlBBQ0tBR0VfVkVSU0lPTiA9IHZvaWQgMDtcbi8vIHRoaXMgaXMgYXV0b2dlbmVyYXRlZCBmaWxlLCBzZWUgc2NyaXB0cy92ZXJzaW9uLXVwZGF0ZS5qc1xuZXhwb3J0cy5QQUNLQUdFX1ZFUlNJT04gPSAnMC40Ny4xJztcbmV4cG9ydHMuUEFDS0FHRV9OQU1FID0gJ0BvcGVudGVsZW1ldHJ5L2luc3RydW1lbnRhdGlvbi1pb3JlZGlzJztcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXZlcnNpb24uanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_340b4582e894fcadfee9bcc3d638a1af/node_modules/@opentelemetry/instrumentation-ioredis/build/src/version.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_340b4582e894fcadfee9bcc3d638a1af/node_modules/@opentelemetry/instrumentation-ioredis/build/src/index.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_340b4582e894fcadfee9bcc3d638a1af/node_modules/@opentelemetry/instrumentation-ioredis/build/src/index.js ***!
  \********************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_340b4582e894fcadfee9bcc3d638a1af/node_modules/@opentelemetry/instrumentation-ioredis/build/src/instrumentation.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_340b4582e894fcadfee9bcc3d638a1af/node_modules/@opentelemetry/instrumentation-ioredis/build/src/types.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_340b4582e894fcadfee9bcc3d638a1af/node_modules/@opentelemetry/instrumentation-ioredis/build/src/index.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_340b4582e894fcadfee9bcc3d638a1af/node_modules/@opentelemetry/instrumentation-ioredis/build/src/instrumentation.js":
/*!******************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_340b4582e894fcadfee9bcc3d638a1af/node_modules/@opentelemetry/instrumentation-ioredis/build/src/instrumentation.js ***!
  \******************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.IORedisInstrumentation = void 0;\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst instrumentation_2 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_340b4582e894fcadfee9bcc3d638a1af/node_modules/@opentelemetry/instrumentation-ioredis/build/src/utils.js\");\nconst redis_common_1 = __webpack_require__(/*! @opentelemetry/redis-common */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+redis-common@0.36.2/node_modules/@opentelemetry/redis-common/build/src/index.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_340b4582e894fcadfee9bcc3d638a1af/node_modules/@opentelemetry/instrumentation-ioredis/build/src/version.js\");\nconst DEFAULT_CONFIG = {\n    requireParentSpan: true,\n};\nclass IORedisInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, Object.assign(Object.assign({}, DEFAULT_CONFIG), config));\n    }\n    setConfig(config = {}) {\n        super.setConfig(Object.assign(Object.assign({}, DEFAULT_CONFIG), config));\n    }\n    init() {\n        return [\n            new instrumentation_1.InstrumentationNodeModuleDefinition('ioredis', ['>=2.0.0 <6'], (module, moduleVersion) => {\n                const moduleExports = module[Symbol.toStringTag] === 'Module'\n                    ? module.default // ESM\n                    : module; // CommonJS\n                if ((0, instrumentation_1.isWrapped)(moduleExports.prototype.sendCommand)) {\n                    this._unwrap(moduleExports.prototype, 'sendCommand');\n                }\n                this._wrap(moduleExports.prototype, 'sendCommand', this._patchSendCommand(moduleVersion));\n                if ((0, instrumentation_1.isWrapped)(moduleExports.prototype.connect)) {\n                    this._unwrap(moduleExports.prototype, 'connect');\n                }\n                this._wrap(moduleExports.prototype, 'connect', this._patchConnection());\n                return module;\n            }, module => {\n                if (module === undefined)\n                    return;\n                const moduleExports = module[Symbol.toStringTag] === 'Module'\n                    ? module.default // ESM\n                    : module; // CommonJS\n                this._unwrap(moduleExports.prototype, 'sendCommand');\n                this._unwrap(moduleExports.prototype, 'connect');\n            }),\n        ];\n    }\n    /**\n     * Patch send command internal to trace requests\n     */\n    _patchSendCommand(moduleVersion) {\n        return (original) => {\n            return this._traceSendCommand(original, moduleVersion);\n        };\n    }\n    _patchConnection() {\n        return (original) => {\n            return this._traceConnection(original);\n        };\n    }\n    _traceSendCommand(original, moduleVersion) {\n        const instrumentation = this;\n        return function (cmd) {\n            if (arguments.length < 1 || typeof cmd !== 'object') {\n                return original.apply(this, arguments);\n            }\n            const config = instrumentation.getConfig();\n            const dbStatementSerializer = config.dbStatementSerializer || redis_common_1.defaultDbStatementSerializer;\n            const hasNoParentSpan = api_1.trace.getSpan(api_1.context.active()) === undefined;\n            if (config.requireParentSpan === true && hasNoParentSpan) {\n                return original.apply(this, arguments);\n            }\n            const span = instrumentation.tracer.startSpan(cmd.name, {\n                kind: api_1.SpanKind.CLIENT,\n                attributes: {\n                    [semantic_conventions_1.SEMATTRS_DB_SYSTEM]: semantic_conventions_1.DBSYSTEMVALUES_REDIS,\n                    [semantic_conventions_1.SEMATTRS_DB_STATEMENT]: dbStatementSerializer(cmd.name, cmd.args),\n                },\n            });\n            const { requestHook } = config;\n            if (requestHook) {\n                (0, instrumentation_2.safeExecuteInTheMiddle)(() => requestHook(span, {\n                    moduleVersion,\n                    cmdName: cmd.name,\n                    cmdArgs: cmd.args,\n                }), e => {\n                    if (e) {\n                        api_1.diag.error('ioredis instrumentation: request hook failed', e);\n                    }\n                }, true);\n            }\n            const { host, port } = this.options;\n            span.setAttributes({\n                [semantic_conventions_1.SEMATTRS_NET_PEER_NAME]: host,\n                [semantic_conventions_1.SEMATTRS_NET_PEER_PORT]: port,\n                [semantic_conventions_1.SEMATTRS_DB_CONNECTION_STRING]: `redis://${host}:${port}`,\n            });\n            try {\n                const result = original.apply(this, arguments);\n                const origResolve = cmd.resolve;\n                /* eslint-disable @typescript-eslint/no-explicit-any */\n                cmd.resolve = function (result) {\n                    (0, instrumentation_2.safeExecuteInTheMiddle)(() => { var _a; return (_a = config.responseHook) === null || _a === void 0 ? void 0 : _a.call(config, span, cmd.name, cmd.args, result); }, e => {\n                        if (e) {\n                            api_1.diag.error('ioredis instrumentation: response hook failed', e);\n                        }\n                    }, true);\n                    (0, utils_1.endSpan)(span, null);\n                    origResolve(result);\n                };\n                const origReject = cmd.reject;\n                cmd.reject = function (err) {\n                    (0, utils_1.endSpan)(span, err);\n                    origReject(err);\n                };\n                return result;\n            }\n            catch (error) {\n                (0, utils_1.endSpan)(span, error);\n                throw error;\n            }\n        };\n    }\n    _traceConnection(original) {\n        const instrumentation = this;\n        return function () {\n            const hasNoParentSpan = api_1.trace.getSpan(api_1.context.active()) === undefined;\n            if (instrumentation.getConfig().requireParentSpan === true &&\n                hasNoParentSpan) {\n                return original.apply(this, arguments);\n            }\n            const span = instrumentation.tracer.startSpan('connect', {\n                kind: api_1.SpanKind.CLIENT,\n                attributes: {\n                    [semantic_conventions_1.SEMATTRS_DB_SYSTEM]: semantic_conventions_1.DBSYSTEMVALUES_REDIS,\n                    [semantic_conventions_1.SEMATTRS_DB_STATEMENT]: 'connect',\n                },\n            });\n            const { host, port } = this.options;\n            span.setAttributes({\n                [semantic_conventions_1.SEMATTRS_NET_PEER_NAME]: host,\n                [semantic_conventions_1.SEMATTRS_NET_PEER_PORT]: port,\n                [semantic_conventions_1.SEMATTRS_DB_CONNECTION_STRING]: `redis://${host}:${port}`,\n            });\n            try {\n                const client = original.apply(this, arguments);\n                (0, utils_1.endSpan)(span, null);\n                return client;\n            }\n            catch (error) {\n                (0, utils_1.endSpan)(span, error);\n                throw error;\n            }\n        };\n    }\n}\nexports.IORedisInstrumentation = IORedisInstrumentation;\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_340b4582e894fcadfee9bcc3d638a1af/node_modules/@opentelemetry/instrumentation-ioredis/build/src/instrumentation.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_340b4582e894fcadfee9bcc3d638a1af/node_modules/@opentelemetry/instrumentation-ioredis/build/src/types.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_340b4582e894fcadfee9bcc3d638a1af/node_modules/@opentelemetry/instrumentation-ioredis/build/src/types.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF8zNDBiNDU4MmU4OTRmY2FkZmVlOWJjYzNkNjM4YTFhZi9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLWlvcmVkaXMvYnVpbGQvc3JjL3R5cGVzLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdEIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFud2FyXFxEb2N1bWVudHNcXEN1YmVudHdlYlxcY3ViZW50XFxub2RlX21vZHVsZXNcXC5wbnBtXFxAb3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfMzQwYjQ1ODJlODk0ZmNhZGZlZTliY2MzZDYzOGExYWZcXG5vZGVfbW9kdWxlc1xcQG9wZW50ZWxlbWV0cnlcXGluc3RydW1lbnRhdGlvbi1pb3JlZGlzXFxidWlsZFxcc3JjXFx0eXBlcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbi8qXG4gKiBDb3B5cmlnaHQgVGhlIE9wZW5UZWxlbWV0cnkgQXV0aG9yc1xuICpcbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4gKiB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuXG4gKiBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbiAqXG4gKiAgICAgIGh0dHBzOi8vd3d3LmFwYWNoZS5vcmcvbGljZW5zZXMvTElDRU5TRS0yLjBcbiAqXG4gKiBVbmxlc3MgcmVxdWlyZWQgYnkgYXBwbGljYWJsZSBsYXcgb3IgYWdyZWVkIHRvIGluIHdyaXRpbmcsIHNvZnR3YXJlXG4gKiBkaXN0cmlidXRlZCB1bmRlciB0aGUgTGljZW5zZSBpcyBkaXN0cmlidXRlZCBvbiBhbiBcIkFTIElTXCIgQkFTSVMsXG4gKiBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgQ09ORElUSU9OUyBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC5cbiAqIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmRcbiAqIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuICovXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD10eXBlcy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_340b4582e894fcadfee9bcc3d638a1af/node_modules/@opentelemetry/instrumentation-ioredis/build/src/types.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_340b4582e894fcadfee9bcc3d638a1af/node_modules/@opentelemetry/instrumentation-ioredis/build/src/utils.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_340b4582e894fcadfee9bcc3d638a1af/node_modules/@opentelemetry/instrumentation-ioredis/build/src/utils.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.endSpan = void 0;\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst endSpan = (span, err) => {\n    if (err) {\n        span.recordException(err);\n        span.setStatus({\n            code: api_1.SpanStatusCode.ERROR,\n            message: err.message,\n        });\n    }\n    span.end();\n};\nexports.endSpan = endSpan;\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_340b4582e894fcadfee9bcc3d638a1af/node_modules/@opentelemetry/instrumentation-ioredis/build/src/utils.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_340b4582e894fcadfee9bcc3d638a1af/node_modules/@opentelemetry/instrumentation-ioredis/build/src/version.js":
/*!**********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_340b4582e894fcadfee9bcc3d638a1af/node_modules/@opentelemetry/instrumentation-ioredis/build/src/version.js ***!
  \**********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.47.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-ioredis';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_340b4582e894fcadfee9bcc3d638a1af/node_modules/@opentelemetry/instrumentation-ioredis/build/src/version.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_340b4582e894fcadfee9bcc3d638a1af/node_modules/@opentelemetry/instrumentation-ioredis/build/src/index.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_340b4582e894fcadfee9bcc3d638a1af/node_modules/@opentelemetry/instrumentation-ioredis/build/src/index.js ***!
  \********************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_340b4582e894fcadfee9bcc3d638a1af/node_modules/@opentelemetry/instrumentation-ioredis/build/src/instrumentation.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_340b4582e894fcadfee9bcc3d638a1af/node_modules/@opentelemetry/instrumentation-ioredis/build/src/types.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_340b4582e894fcadfee9bcc3d638a1af/node_modules/@opentelemetry/instrumentation-ioredis/build/src/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_340b4582e894fcadfee9bcc3d638a1af/node_modules/@opentelemetry/instrumentation-ioredis/build/src/instrumentation.js":
/*!******************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_340b4582e894fcadfee9bcc3d638a1af/node_modules/@opentelemetry/instrumentation-ioredis/build/src/instrumentation.js ***!
  \******************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.IORedisInstrumentation = void 0;\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst instrumentation_2 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_340b4582e894fcadfee9bcc3d638a1af/node_modules/@opentelemetry/instrumentation-ioredis/build/src/utils.js\");\nconst redis_common_1 = __webpack_require__(/*! @opentelemetry/redis-common */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+redis-common@0.36.2/node_modules/@opentelemetry/redis-common/build/src/index.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_340b4582e894fcadfee9bcc3d638a1af/node_modules/@opentelemetry/instrumentation-ioredis/build/src/version.js\");\nconst DEFAULT_CONFIG = {\n    requireParentSpan: true,\n};\nclass IORedisInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, Object.assign(Object.assign({}, DEFAULT_CONFIG), config));\n    }\n    setConfig(config = {}) {\n        super.setConfig(Object.assign(Object.assign({}, DEFAULT_CONFIG), config));\n    }\n    init() {\n        return [\n            new instrumentation_1.InstrumentationNodeModuleDefinition('ioredis', ['>=2.0.0 <6'], (module, moduleVersion) => {\n                const moduleExports = module[Symbol.toStringTag] === 'Module'\n                    ? module.default // ESM\n                    : module; // CommonJS\n                if ((0, instrumentation_1.isWrapped)(moduleExports.prototype.sendCommand)) {\n                    this._unwrap(moduleExports.prototype, 'sendCommand');\n                }\n                this._wrap(moduleExports.prototype, 'sendCommand', this._patchSendCommand(moduleVersion));\n                if ((0, instrumentation_1.isWrapped)(moduleExports.prototype.connect)) {\n                    this._unwrap(moduleExports.prototype, 'connect');\n                }\n                this._wrap(moduleExports.prototype, 'connect', this._patchConnection());\n                return module;\n            }, module => {\n                if (module === undefined)\n                    return;\n                const moduleExports = module[Symbol.toStringTag] === 'Module'\n                    ? module.default // ESM\n                    : module; // CommonJS\n                this._unwrap(moduleExports.prototype, 'sendCommand');\n                this._unwrap(moduleExports.prototype, 'connect');\n            }),\n        ];\n    }\n    /**\n     * Patch send command internal to trace requests\n     */\n    _patchSendCommand(moduleVersion) {\n        return (original) => {\n            return this._traceSendCommand(original, moduleVersion);\n        };\n    }\n    _patchConnection() {\n        return (original) => {\n            return this._traceConnection(original);\n        };\n    }\n    _traceSendCommand(original, moduleVersion) {\n        const instrumentation = this;\n        return function (cmd) {\n            if (arguments.length < 1 || typeof cmd !== 'object') {\n                return original.apply(this, arguments);\n            }\n            const config = instrumentation.getConfig();\n            const dbStatementSerializer = config.dbStatementSerializer || redis_common_1.defaultDbStatementSerializer;\n            const hasNoParentSpan = api_1.trace.getSpan(api_1.context.active()) === undefined;\n            if (config.requireParentSpan === true && hasNoParentSpan) {\n                return original.apply(this, arguments);\n            }\n            const span = instrumentation.tracer.startSpan(cmd.name, {\n                kind: api_1.SpanKind.CLIENT,\n                attributes: {\n                    [semantic_conventions_1.SEMATTRS_DB_SYSTEM]: semantic_conventions_1.DBSYSTEMVALUES_REDIS,\n                    [semantic_conventions_1.SEMATTRS_DB_STATEMENT]: dbStatementSerializer(cmd.name, cmd.args),\n                },\n            });\n            const { requestHook } = config;\n            if (requestHook) {\n                (0, instrumentation_2.safeExecuteInTheMiddle)(() => requestHook(span, {\n                    moduleVersion,\n                    cmdName: cmd.name,\n                    cmdArgs: cmd.args,\n                }), e => {\n                    if (e) {\n                        api_1.diag.error('ioredis instrumentation: request hook failed', e);\n                    }\n                }, true);\n            }\n            const { host, port } = this.options;\n            span.setAttributes({\n                [semantic_conventions_1.SEMATTRS_NET_PEER_NAME]: host,\n                [semantic_conventions_1.SEMATTRS_NET_PEER_PORT]: port,\n                [semantic_conventions_1.SEMATTRS_DB_CONNECTION_STRING]: `redis://${host}:${port}`,\n            });\n            try {\n                const result = original.apply(this, arguments);\n                const origResolve = cmd.resolve;\n                /* eslint-disable @typescript-eslint/no-explicit-any */\n                cmd.resolve = function (result) {\n                    (0, instrumentation_2.safeExecuteInTheMiddle)(() => { var _a; return (_a = config.responseHook) === null || _a === void 0 ? void 0 : _a.call(config, span, cmd.name, cmd.args, result); }, e => {\n                        if (e) {\n                            api_1.diag.error('ioredis instrumentation: response hook failed', e);\n                        }\n                    }, true);\n                    (0, utils_1.endSpan)(span, null);\n                    origResolve(result);\n                };\n                const origReject = cmd.reject;\n                cmd.reject = function (err) {\n                    (0, utils_1.endSpan)(span, err);\n                    origReject(err);\n                };\n                return result;\n            }\n            catch (error) {\n                (0, utils_1.endSpan)(span, error);\n                throw error;\n            }\n        };\n    }\n    _traceConnection(original) {\n        const instrumentation = this;\n        return function () {\n            const hasNoParentSpan = api_1.trace.getSpan(api_1.context.active()) === undefined;\n            if (instrumentation.getConfig().requireParentSpan === true &&\n                hasNoParentSpan) {\n                return original.apply(this, arguments);\n            }\n            const span = instrumentation.tracer.startSpan('connect', {\n                kind: api_1.SpanKind.CLIENT,\n                attributes: {\n                    [semantic_conventions_1.SEMATTRS_DB_SYSTEM]: semantic_conventions_1.DBSYSTEMVALUES_REDIS,\n                    [semantic_conventions_1.SEMATTRS_DB_STATEMENT]: 'connect',\n                },\n            });\n            const { host, port } = this.options;\n            span.setAttributes({\n                [semantic_conventions_1.SEMATTRS_NET_PEER_NAME]: host,\n                [semantic_conventions_1.SEMATTRS_NET_PEER_PORT]: port,\n                [semantic_conventions_1.SEMATTRS_DB_CONNECTION_STRING]: `redis://${host}:${port}`,\n            });\n            try {\n                const client = original.apply(this, arguments);\n                (0, utils_1.endSpan)(span, null);\n                return client;\n            }\n            catch (error) {\n                (0, utils_1.endSpan)(span, error);\n                throw error;\n            }\n        };\n    }\n}\nexports.IORedisInstrumentation = IORedisInstrumentation;\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_340b4582e894fcadfee9bcc3d638a1af/node_modules/@opentelemetry/instrumentation-ioredis/build/src/instrumentation.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_340b4582e894fcadfee9bcc3d638a1af/node_modules/@opentelemetry/instrumentation-ioredis/build/src/types.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_340b4582e894fcadfee9bcc3d638a1af/node_modules/@opentelemetry/instrumentation-ioredis/build/src/types.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF8zNDBiNDU4MmU4OTRmY2FkZmVlOWJjYzNkNjM4YTFhZi9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLWlvcmVkaXMvYnVpbGQvc3JjL3R5cGVzLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdEIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFud2FyXFxEb2N1bWVudHNcXEN1YmVudHdlYlxcY3ViZW50XFxub2RlX21vZHVsZXNcXC5wbnBtXFxAb3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfMzQwYjQ1ODJlODk0ZmNhZGZlZTliY2MzZDYzOGExYWZcXG5vZGVfbW9kdWxlc1xcQG9wZW50ZWxlbWV0cnlcXGluc3RydW1lbnRhdGlvbi1pb3JlZGlzXFxidWlsZFxcc3JjXFx0eXBlcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbi8qXG4gKiBDb3B5cmlnaHQgVGhlIE9wZW5UZWxlbWV0cnkgQXV0aG9yc1xuICpcbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4gKiB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuXG4gKiBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbiAqXG4gKiAgICAgIGh0dHBzOi8vd3d3LmFwYWNoZS5vcmcvbGljZW5zZXMvTElDRU5TRS0yLjBcbiAqXG4gKiBVbmxlc3MgcmVxdWlyZWQgYnkgYXBwbGljYWJsZSBsYXcgb3IgYWdyZWVkIHRvIGluIHdyaXRpbmcsIHNvZnR3YXJlXG4gKiBkaXN0cmlidXRlZCB1bmRlciB0aGUgTGljZW5zZSBpcyBkaXN0cmlidXRlZCBvbiBhbiBcIkFTIElTXCIgQkFTSVMsXG4gKiBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgQ09ORElUSU9OUyBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC5cbiAqIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmRcbiAqIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuICovXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD10eXBlcy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_340b4582e894fcadfee9bcc3d638a1af/node_modules/@opentelemetry/instrumentation-ioredis/build/src/types.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_340b4582e894fcadfee9bcc3d638a1af/node_modules/@opentelemetry/instrumentation-ioredis/build/src/utils.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_340b4582e894fcadfee9bcc3d638a1af/node_modules/@opentelemetry/instrumentation-ioredis/build/src/utils.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.endSpan = void 0;\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst endSpan = (span, err) => {\n    if (err) {\n        span.recordException(err);\n        span.setStatus({\n            code: api_1.SpanStatusCode.ERROR,\n            message: err.message,\n        });\n    }\n    span.end();\n};\nexports.endSpan = endSpan;\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_340b4582e894fcadfee9bcc3d638a1af/node_modules/@opentelemetry/instrumentation-ioredis/build/src/utils.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_340b4582e894fcadfee9bcc3d638a1af/node_modules/@opentelemetry/instrumentation-ioredis/build/src/version.js":
/*!**********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_340b4582e894fcadfee9bcc3d638a1af/node_modules/@opentelemetry/instrumentation-ioredis/build/src/version.js ***!
  \**********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.47.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-ioredis';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_340b4582e894fcadfee9bcc3d638a1af/node_modules/@opentelemetry/instrumentation-ioredis/build/src/version.js\n");

/***/ })

};
;