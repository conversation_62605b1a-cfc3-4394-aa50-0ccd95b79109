"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d";
exports.ids = ["vendor-chunks/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d"];
exports.modules = {

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/index.js":
/*!*******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/index.js ***!
  \*******************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/instrumentation.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/types.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/index.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/instrumentation.js":
/*!*****************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/instrumentation.js ***!
  \*****************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.MySQL2Instrumentation = void 0;\nconst api = __webpack_require__(/*! @opentelemetry/api */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst sql_common_1 = __webpack_require__(/*! @opentelemetry/sql-common */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+sql-common@0.40.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/sql-common/build/src/index.js\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/utils.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/version.js\");\nconst supportedVersions = ['>=1.4.2 <4'];\nclass MySQL2Instrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, config);\n    }\n    init() {\n        let format;\n        function setFormatFunction(moduleExports) {\n            if (!format && moduleExports.format) {\n                format = moduleExports.format;\n            }\n        }\n        const patch = (ConnectionPrototype) => {\n            if ((0, instrumentation_1.isWrapped)(ConnectionPrototype.query)) {\n                this._unwrap(ConnectionPrototype, 'query');\n            }\n            this._wrap(ConnectionPrototype, 'query', this._patchQuery(format, false));\n            if ((0, instrumentation_1.isWrapped)(ConnectionPrototype.execute)) {\n                this._unwrap(ConnectionPrototype, 'execute');\n            }\n            this._wrap(ConnectionPrototype, 'execute', this._patchQuery(format, true));\n        };\n        const unpatch = (ConnectionPrototype) => {\n            this._unwrap(ConnectionPrototype, 'query');\n            this._unwrap(ConnectionPrototype, 'execute');\n        };\n        return [\n            new instrumentation_1.InstrumentationNodeModuleDefinition('mysql2', supportedVersions, (moduleExports) => {\n                setFormatFunction(moduleExports);\n                return moduleExports;\n            }, () => { }, [\n                new instrumentation_1.InstrumentationNodeModuleFile('mysql2/promise.js', supportedVersions, (moduleExports) => {\n                    setFormatFunction(moduleExports);\n                    return moduleExports;\n                }, () => { }),\n                new instrumentation_1.InstrumentationNodeModuleFile('mysql2/lib/connection.js', supportedVersions, (moduleExports) => {\n                    const ConnectionPrototype = (0, utils_1.getConnectionPrototypeToInstrument)(moduleExports);\n                    patch(ConnectionPrototype);\n                    return moduleExports;\n                }, (moduleExports) => {\n                    if (moduleExports === undefined)\n                        return;\n                    const ConnectionPrototype = (0, utils_1.getConnectionPrototypeToInstrument)(moduleExports);\n                    unpatch(ConnectionPrototype);\n                }),\n            ]),\n        ];\n    }\n    _patchQuery(format, isPrepared) {\n        return (originalQuery) => {\n            const thisPlugin = this;\n            return function query(query, _valuesOrCallback, _callback) {\n                let values;\n                if (Array.isArray(_valuesOrCallback)) {\n                    values = _valuesOrCallback;\n                }\n                else if (arguments[2]) {\n                    values = [_valuesOrCallback];\n                }\n                const span = thisPlugin.tracer.startSpan((0, utils_1.getSpanName)(query), {\n                    kind: api.SpanKind.CLIENT,\n                    attributes: Object.assign(Object.assign(Object.assign({}, MySQL2Instrumentation.COMMON_ATTRIBUTES), (0, utils_1.getConnectionAttributes)(this.config)), { [semantic_conventions_1.SEMATTRS_DB_STATEMENT]: (0, utils_1.getDbStatement)(query, format, values) }),\n                });\n                if (!isPrepared &&\n                    thisPlugin.getConfig().addSqlCommenterCommentToQueries) {\n                    arguments[0] = query =\n                        typeof query === 'string'\n                            ? (0, sql_common_1.addSqlCommenterComment)(span, query)\n                            : Object.assign(query, {\n                                sql: (0, sql_common_1.addSqlCommenterComment)(span, query.sql),\n                            });\n                }\n                const endSpan = (0, utils_1.once)((err, results) => {\n                    if (err) {\n                        span.setStatus({\n                            code: api.SpanStatusCode.ERROR,\n                            message: err.message,\n                        });\n                    }\n                    else {\n                        const { responseHook } = thisPlugin.getConfig();\n                        if (typeof responseHook === 'function') {\n                            (0, instrumentation_1.safeExecuteInTheMiddle)(() => {\n                                responseHook(span, {\n                                    queryResults: results,\n                                });\n                            }, err => {\n                                if (err) {\n                                    thisPlugin._diag.warn('Failed executing responseHook', err);\n                                }\n                            }, true);\n                        }\n                    }\n                    span.end();\n                });\n                if (arguments.length === 1) {\n                    if (typeof query.onResult === 'function') {\n                        thisPlugin._wrap(query, 'onResult', thisPlugin._patchCallbackQuery(endSpan));\n                    }\n                    const streamableQuery = originalQuery.apply(this, arguments);\n                    // `end` in mysql behaves similarly to `result` in mysql2.\n                    streamableQuery\n                        .once('error', err => {\n                        endSpan(err);\n                    })\n                        .once('result', results => {\n                        endSpan(undefined, results);\n                    });\n                    return streamableQuery;\n                }\n                if (typeof arguments[1] === 'function') {\n                    thisPlugin._wrap(arguments, 1, thisPlugin._patchCallbackQuery(endSpan));\n                }\n                else if (typeof arguments[2] === 'function') {\n                    thisPlugin._wrap(arguments, 2, thisPlugin._patchCallbackQuery(endSpan));\n                }\n                return originalQuery.apply(this, arguments);\n            };\n        };\n    }\n    _patchCallbackQuery(endSpan) {\n        return (originalCallback) => {\n            return function (err, results, fields) {\n                endSpan(err, results);\n                return originalCallback(...arguments);\n            };\n        };\n    }\n}\nexports.MySQL2Instrumentation = MySQL2Instrumentation;\nMySQL2Instrumentation.COMMON_ATTRIBUTES = {\n    [semantic_conventions_1.SEMATTRS_DB_SYSTEM]: semantic_conventions_1.DBSYSTEMVALUES_MYSQL,\n};\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9Ab3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfOGZiMGIzNzc5ZmVlNzg1ZGExZGFlMGEyYmRiZmFjMGQvbm9kZV9tb2R1bGVzL0BvcGVudGVsZW1ldHJ5L2luc3RydW1lbnRhdGlvbi1teXNxbDIvYnVpbGQvc3JjL2luc3RydW1lbnRhdGlvbi5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCw2QkFBNkI7QUFDN0IsWUFBWSxtQkFBTyxDQUFDLDZJQUFvQjtBQUN4QywwQkFBMEIsbUJBQU8sQ0FBQyx5TUFBZ0M7QUFDbEUsK0JBQStCLG1CQUFPLENBQUMsaU1BQXFDO0FBQzVFLHFCQUFxQixtQkFBTyxDQUFDLDRMQUEyQjtBQUN4RCxnQkFBZ0IsbUJBQU8sQ0FBQyx5TEFBUztBQUNqQztBQUNBLGtCQUFrQixtQkFBTyxDQUFDLDZMQUFXO0FBQ3JDO0FBQ0E7QUFDQSwyQkFBMkI7QUFDM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYSxXQUFXO0FBQ3hCO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQixXQUFXO0FBQzVCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNEVBQTRFLGtHQUFrRyxvR0FBb0c7QUFDbFIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkJBQTZCO0FBQzdCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlCQUF5QjtBQUN6QjtBQUNBO0FBQ0EsZ0NBQWdDLGVBQWU7QUFDL0M7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQ0FBaUM7QUFDakMsNkJBQTZCO0FBQzdCO0FBQ0E7QUFDQTtBQUNBLDZCQUE2QjtBQUM3QjtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQSxxQkFBcUI7QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkJBQTZCO0FBQzdCO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFud2FyXFxEb2N1bWVudHNcXEN1YmVudHdlYlxcY3ViZW50XFxub2RlX21vZHVsZXNcXC5wbnBtXFxAb3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfOGZiMGIzNzc5ZmVlNzg1ZGExZGFlMGEyYmRiZmFjMGRcXG5vZGVfbW9kdWxlc1xcQG9wZW50ZWxlbWV0cnlcXGluc3RydW1lbnRhdGlvbi1teXNxbDJcXGJ1aWxkXFxzcmNcXGluc3RydW1lbnRhdGlvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbi8qXG4gKiBDb3B5cmlnaHQgVGhlIE9wZW5UZWxlbWV0cnkgQXV0aG9yc1xuICpcbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4gKiB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuXG4gKiBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbiAqXG4gKiAgICAgIGh0dHBzOi8vd3d3LmFwYWNoZS5vcmcvbGljZW5zZXMvTElDRU5TRS0yLjBcbiAqXG4gKiBVbmxlc3MgcmVxdWlyZWQgYnkgYXBwbGljYWJsZSBsYXcgb3IgYWdyZWVkIHRvIGluIHdyaXRpbmcsIHNvZnR3YXJlXG4gKiBkaXN0cmlidXRlZCB1bmRlciB0aGUgTGljZW5zZSBpcyBkaXN0cmlidXRlZCBvbiBhbiBcIkFTIElTXCIgQkFTSVMsXG4gKiBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgQ09ORElUSU9OUyBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC5cbiAqIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmRcbiAqIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuICovXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLk15U1FMMkluc3RydW1lbnRhdGlvbiA9IHZvaWQgMDtcbmNvbnN0IGFwaSA9IHJlcXVpcmUoXCJAb3BlbnRlbGVtZXRyeS9hcGlcIik7XG5jb25zdCBpbnN0cnVtZW50YXRpb25fMSA9IHJlcXVpcmUoXCJAb3BlbnRlbGVtZXRyeS9pbnN0cnVtZW50YXRpb25cIik7XG5jb25zdCBzZW1hbnRpY19jb252ZW50aW9uc18xID0gcmVxdWlyZShcIkBvcGVudGVsZW1ldHJ5L3NlbWFudGljLWNvbnZlbnRpb25zXCIpO1xuY29uc3Qgc3FsX2NvbW1vbl8xID0gcmVxdWlyZShcIkBvcGVudGVsZW1ldHJ5L3NxbC1jb21tb25cIik7XG5jb25zdCB1dGlsc18xID0gcmVxdWlyZShcIi4vdXRpbHNcIik7XG4vKiogQGtuaXBpZ25vcmUgKi9cbmNvbnN0IHZlcnNpb25fMSA9IHJlcXVpcmUoXCIuL3ZlcnNpb25cIik7XG5jb25zdCBzdXBwb3J0ZWRWZXJzaW9ucyA9IFsnPj0xLjQuMiA8NCddO1xuY2xhc3MgTXlTUUwySW5zdHJ1bWVudGF0aW9uIGV4dGVuZHMgaW5zdHJ1bWVudGF0aW9uXzEuSW5zdHJ1bWVudGF0aW9uQmFzZSB7XG4gICAgY29uc3RydWN0b3IoY29uZmlnID0ge30pIHtcbiAgICAgICAgc3VwZXIodmVyc2lvbl8xLlBBQ0tBR0VfTkFNRSwgdmVyc2lvbl8xLlBBQ0tBR0VfVkVSU0lPTiwgY29uZmlnKTtcbiAgICB9XG4gICAgaW5pdCgpIHtcbiAgICAgICAgbGV0IGZvcm1hdDtcbiAgICAgICAgZnVuY3Rpb24gc2V0Rm9ybWF0RnVuY3Rpb24obW9kdWxlRXhwb3J0cykge1xuICAgICAgICAgICAgaWYgKCFmb3JtYXQgJiYgbW9kdWxlRXhwb3J0cy5mb3JtYXQpIHtcbiAgICAgICAgICAgICAgICBmb3JtYXQgPSBtb2R1bGVFeHBvcnRzLmZvcm1hdDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBjb25zdCBwYXRjaCA9IChDb25uZWN0aW9uUHJvdG90eXBlKSA9PiB7XG4gICAgICAgICAgICBpZiAoKDAsIGluc3RydW1lbnRhdGlvbl8xLmlzV3JhcHBlZCkoQ29ubmVjdGlvblByb3RvdHlwZS5xdWVyeSkpIHtcbiAgICAgICAgICAgICAgICB0aGlzLl91bndyYXAoQ29ubmVjdGlvblByb3RvdHlwZSwgJ3F1ZXJ5Jyk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICB0aGlzLl93cmFwKENvbm5lY3Rpb25Qcm90b3R5cGUsICdxdWVyeScsIHRoaXMuX3BhdGNoUXVlcnkoZm9ybWF0LCBmYWxzZSkpO1xuICAgICAgICAgICAgaWYgKCgwLCBpbnN0cnVtZW50YXRpb25fMS5pc1dyYXBwZWQpKENvbm5lY3Rpb25Qcm90b3R5cGUuZXhlY3V0ZSkpIHtcbiAgICAgICAgICAgICAgICB0aGlzLl91bndyYXAoQ29ubmVjdGlvblByb3RvdHlwZSwgJ2V4ZWN1dGUnKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHRoaXMuX3dyYXAoQ29ubmVjdGlvblByb3RvdHlwZSwgJ2V4ZWN1dGUnLCB0aGlzLl9wYXRjaFF1ZXJ5KGZvcm1hdCwgdHJ1ZSkpO1xuICAgICAgICB9O1xuICAgICAgICBjb25zdCB1bnBhdGNoID0gKENvbm5lY3Rpb25Qcm90b3R5cGUpID0+IHtcbiAgICAgICAgICAgIHRoaXMuX3Vud3JhcChDb25uZWN0aW9uUHJvdG90eXBlLCAncXVlcnknKTtcbiAgICAgICAgICAgIHRoaXMuX3Vud3JhcChDb25uZWN0aW9uUHJvdG90eXBlLCAnZXhlY3V0ZScpO1xuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gW1xuICAgICAgICAgICAgbmV3IGluc3RydW1lbnRhdGlvbl8xLkluc3RydW1lbnRhdGlvbk5vZGVNb2R1bGVEZWZpbml0aW9uKCdteXNxbDInLCBzdXBwb3J0ZWRWZXJzaW9ucywgKG1vZHVsZUV4cG9ydHMpID0+IHtcbiAgICAgICAgICAgICAgICBzZXRGb3JtYXRGdW5jdGlvbihtb2R1bGVFeHBvcnRzKTtcbiAgICAgICAgICAgICAgICByZXR1cm4gbW9kdWxlRXhwb3J0cztcbiAgICAgICAgICAgIH0sICgpID0+IHsgfSwgW1xuICAgICAgICAgICAgICAgIG5ldyBpbnN0cnVtZW50YXRpb25fMS5JbnN0cnVtZW50YXRpb25Ob2RlTW9kdWxlRmlsZSgnbXlzcWwyL3Byb21pc2UuanMnLCBzdXBwb3J0ZWRWZXJzaW9ucywgKG1vZHVsZUV4cG9ydHMpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgc2V0Rm9ybWF0RnVuY3Rpb24obW9kdWxlRXhwb3J0cyk7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBtb2R1bGVFeHBvcnRzO1xuICAgICAgICAgICAgICAgIH0sICgpID0+IHsgfSksXG4gICAgICAgICAgICAgICAgbmV3IGluc3RydW1lbnRhdGlvbl8xLkluc3RydW1lbnRhdGlvbk5vZGVNb2R1bGVGaWxlKCdteXNxbDIvbGliL2Nvbm5lY3Rpb24uanMnLCBzdXBwb3J0ZWRWZXJzaW9ucywgKG1vZHVsZUV4cG9ydHMpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgQ29ubmVjdGlvblByb3RvdHlwZSA9ICgwLCB1dGlsc18xLmdldENvbm5lY3Rpb25Qcm90b3R5cGVUb0luc3RydW1lbnQpKG1vZHVsZUV4cG9ydHMpO1xuICAgICAgICAgICAgICAgICAgICBwYXRjaChDb25uZWN0aW9uUHJvdG90eXBlKTtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIG1vZHVsZUV4cG9ydHM7XG4gICAgICAgICAgICAgICAgfSwgKG1vZHVsZUV4cG9ydHMpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgaWYgKG1vZHVsZUV4cG9ydHMgPT09IHVuZGVmaW5lZClcbiAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgQ29ubmVjdGlvblByb3RvdHlwZSA9ICgwLCB1dGlsc18xLmdldENvbm5lY3Rpb25Qcm90b3R5cGVUb0luc3RydW1lbnQpKG1vZHVsZUV4cG9ydHMpO1xuICAgICAgICAgICAgICAgICAgICB1bnBhdGNoKENvbm5lY3Rpb25Qcm90b3R5cGUpO1xuICAgICAgICAgICAgICAgIH0pLFxuICAgICAgICAgICAgXSksXG4gICAgICAgIF07XG4gICAgfVxuICAgIF9wYXRjaFF1ZXJ5KGZvcm1hdCwgaXNQcmVwYXJlZCkge1xuICAgICAgICByZXR1cm4gKG9yaWdpbmFsUXVlcnkpID0+IHtcbiAgICAgICAgICAgIGNvbnN0IHRoaXNQbHVnaW4gPSB0aGlzO1xuICAgICAgICAgICAgcmV0dXJuIGZ1bmN0aW9uIHF1ZXJ5KHF1ZXJ5LCBfdmFsdWVzT3JDYWxsYmFjaywgX2NhbGxiYWNrKSB7XG4gICAgICAgICAgICAgICAgbGV0IHZhbHVlcztcbiAgICAgICAgICAgICAgICBpZiAoQXJyYXkuaXNBcnJheShfdmFsdWVzT3JDYWxsYmFjaykpIHtcbiAgICAgICAgICAgICAgICAgICAgdmFsdWVzID0gX3ZhbHVlc09yQ2FsbGJhY2s7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGVsc2UgaWYgKGFyZ3VtZW50c1syXSkge1xuICAgICAgICAgICAgICAgICAgICB2YWx1ZXMgPSBbX3ZhbHVlc09yQ2FsbGJhY2tdO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBjb25zdCBzcGFuID0gdGhpc1BsdWdpbi50cmFjZXIuc3RhcnRTcGFuKCgwLCB1dGlsc18xLmdldFNwYW5OYW1lKShxdWVyeSksIHtcbiAgICAgICAgICAgICAgICAgICAga2luZDogYXBpLlNwYW5LaW5kLkNMSUVOVCxcbiAgICAgICAgICAgICAgICAgICAgYXR0cmlidXRlczogT2JqZWN0LmFzc2lnbihPYmplY3QuYXNzaWduKE9iamVjdC5hc3NpZ24oe30sIE15U1FMMkluc3RydW1lbnRhdGlvbi5DT01NT05fQVRUUklCVVRFUyksICgwLCB1dGlsc18xLmdldENvbm5lY3Rpb25BdHRyaWJ1dGVzKSh0aGlzLmNvbmZpZykpLCB7IFtzZW1hbnRpY19jb252ZW50aW9uc18xLlNFTUFUVFJTX0RCX1NUQVRFTUVOVF06ICgwLCB1dGlsc18xLmdldERiU3RhdGVtZW50KShxdWVyeSwgZm9ybWF0LCB2YWx1ZXMpIH0pLFxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgIGlmICghaXNQcmVwYXJlZCAmJlxuICAgICAgICAgICAgICAgICAgICB0aGlzUGx1Z2luLmdldENvbmZpZygpLmFkZFNxbENvbW1lbnRlckNvbW1lbnRUb1F1ZXJpZXMpIHtcbiAgICAgICAgICAgICAgICAgICAgYXJndW1lbnRzWzBdID0gcXVlcnkgPVxuICAgICAgICAgICAgICAgICAgICAgICAgdHlwZW9mIHF1ZXJ5ID09PSAnc3RyaW5nJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gKDAsIHNxbF9jb21tb25fMS5hZGRTcWxDb21tZW50ZXJDb21tZW50KShzcGFuLCBxdWVyeSlcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IE9iamVjdC5hc3NpZ24ocXVlcnksIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3FsOiAoMCwgc3FsX2NvbW1vbl8xLmFkZFNxbENvbW1lbnRlckNvbW1lbnQpKHNwYW4sIHF1ZXJ5LnNxbCksXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGNvbnN0IGVuZFNwYW4gPSAoMCwgdXRpbHNfMS5vbmNlKSgoZXJyLCByZXN1bHRzKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIGlmIChlcnIpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHNwYW4uc2V0U3RhdHVzKHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2RlOiBhcGkuU3BhblN0YXR1c0NvZGUuRVJST1IsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbWVzc2FnZTogZXJyLm1lc3NhZ2UsXG4gICAgICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHsgcmVzcG9uc2VIb29rIH0gPSB0aGlzUGx1Z2luLmdldENvbmZpZygpO1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHR5cGVvZiByZXNwb25zZUhvb2sgPT09ICdmdW5jdGlvbicpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAoMCwgaW5zdHJ1bWVudGF0aW9uXzEuc2FmZUV4ZWN1dGVJblRoZU1pZGRsZSkoKCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXNwb25zZUhvb2soc3Bhbiwge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcXVlcnlSZXN1bHRzOiByZXN1bHRzLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LCBlcnIgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAoZXJyKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzUGx1Z2luLl9kaWFnLndhcm4oJ0ZhaWxlZCBleGVjdXRpbmcgcmVzcG9uc2VIb29rJywgZXJyKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sIHRydWUpO1xuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIHNwYW4uZW5kKCk7XG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgaWYgKGFyZ3VtZW50cy5sZW5ndGggPT09IDEpIHtcbiAgICAgICAgICAgICAgICAgICAgaWYgKHR5cGVvZiBxdWVyeS5vblJlc3VsdCA9PT0gJ2Z1bmN0aW9uJykge1xuICAgICAgICAgICAgICAgICAgICAgICAgdGhpc1BsdWdpbi5fd3JhcChxdWVyeSwgJ29uUmVzdWx0JywgdGhpc1BsdWdpbi5fcGF0Y2hDYWxsYmFja1F1ZXJ5KGVuZFNwYW4pKTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBjb25zdCBzdHJlYW1hYmxlUXVlcnkgPSBvcmlnaW5hbFF1ZXJ5LmFwcGx5KHRoaXMsIGFyZ3VtZW50cyk7XG4gICAgICAgICAgICAgICAgICAgIC8vIGBlbmRgIGluIG15c3FsIGJlaGF2ZXMgc2ltaWxhcmx5IHRvIGByZXN1bHRgIGluIG15c3FsMi5cbiAgICAgICAgICAgICAgICAgICAgc3RyZWFtYWJsZVF1ZXJ5XG4gICAgICAgICAgICAgICAgICAgICAgICAub25jZSgnZXJyb3InLCBlcnIgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgZW5kU3BhbihlcnIpO1xuICAgICAgICAgICAgICAgICAgICB9KVxuICAgICAgICAgICAgICAgICAgICAgICAgLm9uY2UoJ3Jlc3VsdCcsIHJlc3VsdHMgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgZW5kU3Bhbih1bmRlZmluZWQsIHJlc3VsdHMpO1xuICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHN0cmVhbWFibGVRdWVyeTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgaWYgKHR5cGVvZiBhcmd1bWVudHNbMV0gPT09ICdmdW5jdGlvbicpIHtcbiAgICAgICAgICAgICAgICAgICAgdGhpc1BsdWdpbi5fd3JhcChhcmd1bWVudHMsIDEsIHRoaXNQbHVnaW4uX3BhdGNoQ2FsbGJhY2tRdWVyeShlbmRTcGFuKSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGVsc2UgaWYgKHR5cGVvZiBhcmd1bWVudHNbMl0gPT09ICdmdW5jdGlvbicpIHtcbiAgICAgICAgICAgICAgICAgICAgdGhpc1BsdWdpbi5fd3JhcChhcmd1bWVudHMsIDIsIHRoaXNQbHVnaW4uX3BhdGNoQ2FsbGJhY2tRdWVyeShlbmRTcGFuKSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIHJldHVybiBvcmlnaW5hbFF1ZXJ5LmFwcGx5KHRoaXMsIGFyZ3VtZW50cyk7XG4gICAgICAgICAgICB9O1xuICAgICAgICB9O1xuICAgIH1cbiAgICBfcGF0Y2hDYWxsYmFja1F1ZXJ5KGVuZFNwYW4pIHtcbiAgICAgICAgcmV0dXJuIChvcmlnaW5hbENhbGxiYWNrKSA9PiB7XG4gICAgICAgICAgICByZXR1cm4gZnVuY3Rpb24gKGVyciwgcmVzdWx0cywgZmllbGRzKSB7XG4gICAgICAgICAgICAgICAgZW5kU3BhbihlcnIsIHJlc3VsdHMpO1xuICAgICAgICAgICAgICAgIHJldHVybiBvcmlnaW5hbENhbGxiYWNrKC4uLmFyZ3VtZW50cyk7XG4gICAgICAgICAgICB9O1xuICAgICAgICB9O1xuICAgIH1cbn1cbmV4cG9ydHMuTXlTUUwySW5zdHJ1bWVudGF0aW9uID0gTXlTUUwySW5zdHJ1bWVudGF0aW9uO1xuTXlTUUwySW5zdHJ1bWVudGF0aW9uLkNPTU1PTl9BVFRSSUJVVEVTID0ge1xuICAgIFtzZW1hbnRpY19jb252ZW50aW9uc18xLlNFTUFUVFJTX0RCX1NZU1RFTV06IHNlbWFudGljX2NvbnZlbnRpb25zXzEuREJTWVNURU1WQUxVRVNfTVlTUUwsXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5zdHJ1bWVudGF0aW9uLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/instrumentation.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/types.js":
/*!*******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/types.js ***!
  \*******************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9Ab3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfOGZiMGIzNzc5ZmVlNzg1ZGExZGFlMGEyYmRiZmFjMGQvbm9kZV9tb2R1bGVzL0BvcGVudGVsZW1ldHJ5L2luc3RydW1lbnRhdGlvbi1teXNxbDIvYnVpbGQvc3JjL3R5cGVzLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdEIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFud2FyXFxEb2N1bWVudHNcXEN1YmVudHdlYlxcY3ViZW50XFxub2RlX21vZHVsZXNcXC5wbnBtXFxAb3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfOGZiMGIzNzc5ZmVlNzg1ZGExZGFlMGEyYmRiZmFjMGRcXG5vZGVfbW9kdWxlc1xcQG9wZW50ZWxlbWV0cnlcXGluc3RydW1lbnRhdGlvbi1teXNxbDJcXGJ1aWxkXFxzcmNcXHR5cGVzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuLypcbiAqIENvcHlyaWdodCBUaGUgT3BlblRlbGVtZXRyeSBBdXRob3JzXG4gKlxuICogTGljZW5zZWQgdW5kZXIgdGhlIEFwYWNoZSBMaWNlbnNlLCBWZXJzaW9uIDIuMCAodGhlIFwiTGljZW5zZVwiKTtcbiAqIHlvdSBtYXkgbm90IHVzZSB0aGlzIGZpbGUgZXhjZXB0IGluIGNvbXBsaWFuY2Ugd2l0aCB0aGUgTGljZW5zZS5cbiAqIFlvdSBtYXkgb2J0YWluIGEgY29weSBvZiB0aGUgTGljZW5zZSBhdFxuICpcbiAqICAgICAgaHR0cHM6Ly93d3cuYXBhY2hlLm9yZy9saWNlbnNlcy9MSUNFTlNFLTIuMFxuICpcbiAqIFVubGVzcyByZXF1aXJlZCBieSBhcHBsaWNhYmxlIGxhdyBvciBhZ3JlZWQgdG8gaW4gd3JpdGluZywgc29mdHdhcmVcbiAqIGRpc3RyaWJ1dGVkIHVuZGVyIHRoZSBMaWNlbnNlIGlzIGRpc3RyaWJ1dGVkIG9uIGFuIFwiQVMgSVNcIiBCQVNJUyxcbiAqIFdJVEhPVVQgV0FSUkFOVElFUyBPUiBDT05ESVRJT05TIE9GIEFOWSBLSU5ELCBlaXRoZXIgZXhwcmVzcyBvciBpbXBsaWVkLlxuICogU2VlIHRoZSBMaWNlbnNlIGZvciB0aGUgc3BlY2lmaWMgbGFuZ3VhZ2UgZ292ZXJuaW5nIHBlcm1pc3Npb25zIGFuZFxuICogbGltaXRhdGlvbnMgdW5kZXIgdGhlIExpY2Vuc2UuXG4gKi9cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXR5cGVzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/types.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/utils.js":
/*!*******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/utils.js ***!
  \*******************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getConnectionPrototypeToInstrument = exports.once = exports.getSpanName = exports.getDbStatement = exports.getConnectionAttributes = void 0;\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\n/**\n * Get an Attributes map from a mysql connection config object\n *\n * @param config ConnectionConfig\n */\nfunction getConnectionAttributes(config) {\n    const { host, port, database, user } = getConfig(config);\n    const portNumber = parseInt(port, 10);\n    if (!isNaN(portNumber)) {\n        return {\n            [semantic_conventions_1.SEMATTRS_NET_PEER_NAME]: host,\n            [semantic_conventions_1.SEMATTRS_NET_PEER_PORT]: portNumber,\n            [semantic_conventions_1.SEMATTRS_DB_CONNECTION_STRING]: getJDBCString(host, port, database),\n            [semantic_conventions_1.SEMATTRS_DB_NAME]: database,\n            [semantic_conventions_1.SEMATTRS_DB_USER]: user,\n        };\n    }\n    return {\n        [semantic_conventions_1.SEMATTRS_NET_PEER_NAME]: host,\n        [semantic_conventions_1.SEMATTRS_DB_CONNECTION_STRING]: getJDBCString(host, port, database),\n        [semantic_conventions_1.SEMATTRS_DB_NAME]: database,\n        [semantic_conventions_1.SEMATTRS_DB_USER]: user,\n    };\n}\nexports.getConnectionAttributes = getConnectionAttributes;\nfunction getConfig(config) {\n    const { host, port, database, user } = (config && config.connectionConfig) || config || {};\n    return { host, port, database, user };\n}\nfunction getJDBCString(host, port, database) {\n    let jdbcString = `jdbc:mysql://${host || 'localhost'}`;\n    if (typeof port === 'number') {\n        jdbcString += `:${port}`;\n    }\n    if (typeof database === 'string') {\n        jdbcString += `/${database}`;\n    }\n    return jdbcString;\n}\n/**\n * Conjures up the value for the db.statement attribute by formatting a SQL query.\n *\n * @returns the database statement being executed.\n */\nfunction getDbStatement(query, format, values) {\n    if (!format) {\n        return typeof query === 'string' ? query : query.sql;\n    }\n    if (typeof query === 'string') {\n        return values ? format(query, values) : query;\n    }\n    else {\n        // According to https://github.com/mysqljs/mysql#performing-queries\n        // The values argument will override the values in the option object.\n        return values || query.values\n            ? format(query.sql, values || query.values)\n            : query.sql;\n    }\n}\nexports.getDbStatement = getDbStatement;\n/**\n * The span name SHOULD be set to a low cardinality value\n * representing the statement executed on the database.\n *\n * @returns SQL statement without variable arguments or SQL verb\n */\nfunction getSpanName(query) {\n    const rawQuery = typeof query === 'object' ? query.sql : query;\n    // Extract the SQL verb\n    const firstSpace = rawQuery === null || rawQuery === void 0 ? void 0 : rawQuery.indexOf(' ');\n    if (typeof firstSpace === 'number' && firstSpace !== -1) {\n        return rawQuery === null || rawQuery === void 0 ? void 0 : rawQuery.substring(0, firstSpace);\n    }\n    return rawQuery;\n}\nexports.getSpanName = getSpanName;\nconst once = (fn) => {\n    let called = false;\n    return (...args) => {\n        if (called)\n            return;\n        called = true;\n        return fn(...args);\n    };\n};\nexports.once = once;\nfunction getConnectionPrototypeToInstrument(connection) {\n    const connectionPrototype = connection.prototype;\n    const basePrototype = Object.getPrototypeOf(connectionPrototype);\n    // mysql2@3.11.5 included a refactoring, where most code was moved out of the `Connection` class and into a shared base\n    // so we need to instrument that instead, see https://github.com/sidorares/node-mysql2/pull/3081\n    // This checks if the functions we're instrumenting are there on the base - we cannot use the presence of a base\n    // prototype since EventEmitter is the base for mysql2@<=3.11.4\n    if (typeof (basePrototype === null || basePrototype === void 0 ? void 0 : basePrototype.query) === 'function' &&\n        typeof (basePrototype === null || basePrototype === void 0 ? void 0 : basePrototype.execute) === 'function') {\n        return basePrototype;\n    }\n    // otherwise instrument the connection directly.\n    return connectionPrototype;\n}\nexports.getConnectionPrototypeToInstrument = getConnectionPrototypeToInstrument;\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/utils.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/version.js":
/*!*********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/version.js ***!
  \*********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.45.2';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-mysql2';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/version.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/index.js":
/*!*******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/index.js ***!
  \*******************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/instrumentation.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/types.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/index.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/instrumentation.js":
/*!*****************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/instrumentation.js ***!
  \*****************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.MySQL2Instrumentation = void 0;\nconst api = __webpack_require__(/*! @opentelemetry/api */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst sql_common_1 = __webpack_require__(/*! @opentelemetry/sql-common */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+sql-common@0.40.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/sql-common/build/src/index.js\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/utils.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/version.js\");\nconst supportedVersions = ['>=1.4.2 <4'];\nclass MySQL2Instrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, config);\n    }\n    init() {\n        let format;\n        function setFormatFunction(moduleExports) {\n            if (!format && moduleExports.format) {\n                format = moduleExports.format;\n            }\n        }\n        const patch = (ConnectionPrototype) => {\n            if ((0, instrumentation_1.isWrapped)(ConnectionPrototype.query)) {\n                this._unwrap(ConnectionPrototype, 'query');\n            }\n            this._wrap(ConnectionPrototype, 'query', this._patchQuery(format, false));\n            if ((0, instrumentation_1.isWrapped)(ConnectionPrototype.execute)) {\n                this._unwrap(ConnectionPrototype, 'execute');\n            }\n            this._wrap(ConnectionPrototype, 'execute', this._patchQuery(format, true));\n        };\n        const unpatch = (ConnectionPrototype) => {\n            this._unwrap(ConnectionPrototype, 'query');\n            this._unwrap(ConnectionPrototype, 'execute');\n        };\n        return [\n            new instrumentation_1.InstrumentationNodeModuleDefinition('mysql2', supportedVersions, (moduleExports) => {\n                setFormatFunction(moduleExports);\n                return moduleExports;\n            }, () => { }, [\n                new instrumentation_1.InstrumentationNodeModuleFile('mysql2/promise.js', supportedVersions, (moduleExports) => {\n                    setFormatFunction(moduleExports);\n                    return moduleExports;\n                }, () => { }),\n                new instrumentation_1.InstrumentationNodeModuleFile('mysql2/lib/connection.js', supportedVersions, (moduleExports) => {\n                    const ConnectionPrototype = (0, utils_1.getConnectionPrototypeToInstrument)(moduleExports);\n                    patch(ConnectionPrototype);\n                    return moduleExports;\n                }, (moduleExports) => {\n                    if (moduleExports === undefined)\n                        return;\n                    const ConnectionPrototype = (0, utils_1.getConnectionPrototypeToInstrument)(moduleExports);\n                    unpatch(ConnectionPrototype);\n                }),\n            ]),\n        ];\n    }\n    _patchQuery(format, isPrepared) {\n        return (originalQuery) => {\n            const thisPlugin = this;\n            return function query(query, _valuesOrCallback, _callback) {\n                let values;\n                if (Array.isArray(_valuesOrCallback)) {\n                    values = _valuesOrCallback;\n                }\n                else if (arguments[2]) {\n                    values = [_valuesOrCallback];\n                }\n                const span = thisPlugin.tracer.startSpan((0, utils_1.getSpanName)(query), {\n                    kind: api.SpanKind.CLIENT,\n                    attributes: Object.assign(Object.assign(Object.assign({}, MySQL2Instrumentation.COMMON_ATTRIBUTES), (0, utils_1.getConnectionAttributes)(this.config)), { [semantic_conventions_1.SEMATTRS_DB_STATEMENT]: (0, utils_1.getDbStatement)(query, format, values) }),\n                });\n                if (!isPrepared &&\n                    thisPlugin.getConfig().addSqlCommenterCommentToQueries) {\n                    arguments[0] = query =\n                        typeof query === 'string'\n                            ? (0, sql_common_1.addSqlCommenterComment)(span, query)\n                            : Object.assign(query, {\n                                sql: (0, sql_common_1.addSqlCommenterComment)(span, query.sql),\n                            });\n                }\n                const endSpan = (0, utils_1.once)((err, results) => {\n                    if (err) {\n                        span.setStatus({\n                            code: api.SpanStatusCode.ERROR,\n                            message: err.message,\n                        });\n                    }\n                    else {\n                        const { responseHook } = thisPlugin.getConfig();\n                        if (typeof responseHook === 'function') {\n                            (0, instrumentation_1.safeExecuteInTheMiddle)(() => {\n                                responseHook(span, {\n                                    queryResults: results,\n                                });\n                            }, err => {\n                                if (err) {\n                                    thisPlugin._diag.warn('Failed executing responseHook', err);\n                                }\n                            }, true);\n                        }\n                    }\n                    span.end();\n                });\n                if (arguments.length === 1) {\n                    if (typeof query.onResult === 'function') {\n                        thisPlugin._wrap(query, 'onResult', thisPlugin._patchCallbackQuery(endSpan));\n                    }\n                    const streamableQuery = originalQuery.apply(this, arguments);\n                    // `end` in mysql behaves similarly to `result` in mysql2.\n                    streamableQuery\n                        .once('error', err => {\n                        endSpan(err);\n                    })\n                        .once('result', results => {\n                        endSpan(undefined, results);\n                    });\n                    return streamableQuery;\n                }\n                if (typeof arguments[1] === 'function') {\n                    thisPlugin._wrap(arguments, 1, thisPlugin._patchCallbackQuery(endSpan));\n                }\n                else if (typeof arguments[2] === 'function') {\n                    thisPlugin._wrap(arguments, 2, thisPlugin._patchCallbackQuery(endSpan));\n                }\n                return originalQuery.apply(this, arguments);\n            };\n        };\n    }\n    _patchCallbackQuery(endSpan) {\n        return (originalCallback) => {\n            return function (err, results, fields) {\n                endSpan(err, results);\n                return originalCallback(...arguments);\n            };\n        };\n    }\n}\nexports.MySQL2Instrumentation = MySQL2Instrumentation;\nMySQL2Instrumentation.COMMON_ATTRIBUTES = {\n    [semantic_conventions_1.SEMATTRS_DB_SYSTEM]: semantic_conventions_1.DBSYSTEMVALUES_MYSQL,\n};\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/instrumentation.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/types.js":
/*!*******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/types.js ***!
  \*******************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF84ZmIwYjM3NzlmZWU3ODVkYTFkYWUwYTJiZGJmYWMwZC9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLW15c3FsMi9idWlsZC9zcmMvdHlwZXMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYW53YXJcXERvY3VtZW50c1xcQ3ViZW50d2ViXFxjdWJlbnRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF84ZmIwYjM3NzlmZWU3ODVkYTFkYWUwYTJiZGJmYWMwZFxcbm9kZV9tb2R1bGVzXFxAb3BlbnRlbGVtZXRyeVxcaW5zdHJ1bWVudGF0aW9uLW15c3FsMlxcYnVpbGRcXHNyY1xcdHlwZXMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG4vKlxuICogQ29weXJpZ2h0IFRoZSBPcGVuVGVsZW1ldHJ5IEF1dGhvcnNcbiAqXG4gKiBMaWNlbnNlZCB1bmRlciB0aGUgQXBhY2hlIExpY2Vuc2UsIFZlcnNpb24gMi4wICh0aGUgXCJMaWNlbnNlXCIpO1xuICogeW91IG1heSBub3QgdXNlIHRoaXMgZmlsZSBleGNlcHQgaW4gY29tcGxpYW5jZSB3aXRoIHRoZSBMaWNlbnNlLlxuICogWW91IG1heSBvYnRhaW4gYSBjb3B5IG9mIHRoZSBMaWNlbnNlIGF0XG4gKlxuICogICAgICBodHRwczovL3d3dy5hcGFjaGUub3JnL2xpY2Vuc2VzL0xJQ0VOU0UtMi4wXG4gKlxuICogVW5sZXNzIHJlcXVpcmVkIGJ5IGFwcGxpY2FibGUgbGF3IG9yIGFncmVlZCB0byBpbiB3cml0aW5nLCBzb2Z0d2FyZVxuICogZGlzdHJpYnV0ZWQgdW5kZXIgdGhlIExpY2Vuc2UgaXMgZGlzdHJpYnV0ZWQgb24gYW4gXCJBUyBJU1wiIEJBU0lTLFxuICogV0lUSE9VVCBXQVJSQU5USUVTIE9SIENPTkRJVElPTlMgT0YgQU5ZIEtJTkQsIGVpdGhlciBleHByZXNzIG9yIGltcGxpZWQuXG4gKiBTZWUgdGhlIExpY2Vuc2UgZm9yIHRoZSBzcGVjaWZpYyBsYW5ndWFnZSBnb3Zlcm5pbmcgcGVybWlzc2lvbnMgYW5kXG4gKiBsaW1pdGF0aW9ucyB1bmRlciB0aGUgTGljZW5zZS5cbiAqL1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dHlwZXMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/types.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/utils.js":
/*!*******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/utils.js ***!
  \*******************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getConnectionPrototypeToInstrument = exports.once = exports.getSpanName = exports.getDbStatement = exports.getConnectionAttributes = void 0;\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\n/**\n * Get an Attributes map from a mysql connection config object\n *\n * @param config ConnectionConfig\n */\nfunction getConnectionAttributes(config) {\n    const { host, port, database, user } = getConfig(config);\n    const portNumber = parseInt(port, 10);\n    if (!isNaN(portNumber)) {\n        return {\n            [semantic_conventions_1.SEMATTRS_NET_PEER_NAME]: host,\n            [semantic_conventions_1.SEMATTRS_NET_PEER_PORT]: portNumber,\n            [semantic_conventions_1.SEMATTRS_DB_CONNECTION_STRING]: getJDBCString(host, port, database),\n            [semantic_conventions_1.SEMATTRS_DB_NAME]: database,\n            [semantic_conventions_1.SEMATTRS_DB_USER]: user,\n        };\n    }\n    return {\n        [semantic_conventions_1.SEMATTRS_NET_PEER_NAME]: host,\n        [semantic_conventions_1.SEMATTRS_DB_CONNECTION_STRING]: getJDBCString(host, port, database),\n        [semantic_conventions_1.SEMATTRS_DB_NAME]: database,\n        [semantic_conventions_1.SEMATTRS_DB_USER]: user,\n    };\n}\nexports.getConnectionAttributes = getConnectionAttributes;\nfunction getConfig(config) {\n    const { host, port, database, user } = (config && config.connectionConfig) || config || {};\n    return { host, port, database, user };\n}\nfunction getJDBCString(host, port, database) {\n    let jdbcString = `jdbc:mysql://${host || 'localhost'}`;\n    if (typeof port === 'number') {\n        jdbcString += `:${port}`;\n    }\n    if (typeof database === 'string') {\n        jdbcString += `/${database}`;\n    }\n    return jdbcString;\n}\n/**\n * Conjures up the value for the db.statement attribute by formatting a SQL query.\n *\n * @returns the database statement being executed.\n */\nfunction getDbStatement(query, format, values) {\n    if (!format) {\n        return typeof query === 'string' ? query : query.sql;\n    }\n    if (typeof query === 'string') {\n        return values ? format(query, values) : query;\n    }\n    else {\n        // According to https://github.com/mysqljs/mysql#performing-queries\n        // The values argument will override the values in the option object.\n        return values || query.values\n            ? format(query.sql, values || query.values)\n            : query.sql;\n    }\n}\nexports.getDbStatement = getDbStatement;\n/**\n * The span name SHOULD be set to a low cardinality value\n * representing the statement executed on the database.\n *\n * @returns SQL statement without variable arguments or SQL verb\n */\nfunction getSpanName(query) {\n    const rawQuery = typeof query === 'object' ? query.sql : query;\n    // Extract the SQL verb\n    const firstSpace = rawQuery === null || rawQuery === void 0 ? void 0 : rawQuery.indexOf(' ');\n    if (typeof firstSpace === 'number' && firstSpace !== -1) {\n        return rawQuery === null || rawQuery === void 0 ? void 0 : rawQuery.substring(0, firstSpace);\n    }\n    return rawQuery;\n}\nexports.getSpanName = getSpanName;\nconst once = (fn) => {\n    let called = false;\n    return (...args) => {\n        if (called)\n            return;\n        called = true;\n        return fn(...args);\n    };\n};\nexports.once = once;\nfunction getConnectionPrototypeToInstrument(connection) {\n    const connectionPrototype = connection.prototype;\n    const basePrototype = Object.getPrototypeOf(connectionPrototype);\n    // mysql2@3.11.5 included a refactoring, where most code was moved out of the `Connection` class and into a shared base\n    // so we need to instrument that instead, see https://github.com/sidorares/node-mysql2/pull/3081\n    // This checks if the functions we're instrumenting are there on the base - we cannot use the presence of a base\n    // prototype since EventEmitter is the base for mysql2@<=3.11.4\n    if (typeof (basePrototype === null || basePrototype === void 0 ? void 0 : basePrototype.query) === 'function' &&\n        typeof (basePrototype === null || basePrototype === void 0 ? void 0 : basePrototype.execute) === 'function') {\n        return basePrototype;\n    }\n    // otherwise instrument the connection directly.\n    return connectionPrototype;\n}\nexports.getConnectionPrototypeToInstrument = getConnectionPrototypeToInstrument;\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/utils.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/version.js":
/*!*********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/version.js ***!
  \*********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.45.2';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-mysql2';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/version.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/index.js":
/*!*******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/index.js ***!
  \*******************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/instrumentation.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/types.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/instrumentation.js":
/*!*****************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/instrumentation.js ***!
  \*****************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.MySQL2Instrumentation = void 0;\nconst api = __webpack_require__(/*! @opentelemetry/api */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst sql_common_1 = __webpack_require__(/*! @opentelemetry/sql-common */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+sql-common@0.40.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/sql-common/build/src/index.js\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/utils.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/version.js\");\nconst supportedVersions = ['>=1.4.2 <4'];\nclass MySQL2Instrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, config);\n    }\n    init() {\n        let format;\n        function setFormatFunction(moduleExports) {\n            if (!format && moduleExports.format) {\n                format = moduleExports.format;\n            }\n        }\n        const patch = (ConnectionPrototype) => {\n            if ((0, instrumentation_1.isWrapped)(ConnectionPrototype.query)) {\n                this._unwrap(ConnectionPrototype, 'query');\n            }\n            this._wrap(ConnectionPrototype, 'query', this._patchQuery(format, false));\n            if ((0, instrumentation_1.isWrapped)(ConnectionPrototype.execute)) {\n                this._unwrap(ConnectionPrototype, 'execute');\n            }\n            this._wrap(ConnectionPrototype, 'execute', this._patchQuery(format, true));\n        };\n        const unpatch = (ConnectionPrototype) => {\n            this._unwrap(ConnectionPrototype, 'query');\n            this._unwrap(ConnectionPrototype, 'execute');\n        };\n        return [\n            new instrumentation_1.InstrumentationNodeModuleDefinition('mysql2', supportedVersions, (moduleExports) => {\n                setFormatFunction(moduleExports);\n                return moduleExports;\n            }, () => { }, [\n                new instrumentation_1.InstrumentationNodeModuleFile('mysql2/promise.js', supportedVersions, (moduleExports) => {\n                    setFormatFunction(moduleExports);\n                    return moduleExports;\n                }, () => { }),\n                new instrumentation_1.InstrumentationNodeModuleFile('mysql2/lib/connection.js', supportedVersions, (moduleExports) => {\n                    const ConnectionPrototype = (0, utils_1.getConnectionPrototypeToInstrument)(moduleExports);\n                    patch(ConnectionPrototype);\n                    return moduleExports;\n                }, (moduleExports) => {\n                    if (moduleExports === undefined)\n                        return;\n                    const ConnectionPrototype = (0, utils_1.getConnectionPrototypeToInstrument)(moduleExports);\n                    unpatch(ConnectionPrototype);\n                }),\n            ]),\n        ];\n    }\n    _patchQuery(format, isPrepared) {\n        return (originalQuery) => {\n            const thisPlugin = this;\n            return function query(query, _valuesOrCallback, _callback) {\n                let values;\n                if (Array.isArray(_valuesOrCallback)) {\n                    values = _valuesOrCallback;\n                }\n                else if (arguments[2]) {\n                    values = [_valuesOrCallback];\n                }\n                const span = thisPlugin.tracer.startSpan((0, utils_1.getSpanName)(query), {\n                    kind: api.SpanKind.CLIENT,\n                    attributes: Object.assign(Object.assign(Object.assign({}, MySQL2Instrumentation.COMMON_ATTRIBUTES), (0, utils_1.getConnectionAttributes)(this.config)), { [semantic_conventions_1.SEMATTRS_DB_STATEMENT]: (0, utils_1.getDbStatement)(query, format, values) }),\n                });\n                if (!isPrepared &&\n                    thisPlugin.getConfig().addSqlCommenterCommentToQueries) {\n                    arguments[0] = query =\n                        typeof query === 'string'\n                            ? (0, sql_common_1.addSqlCommenterComment)(span, query)\n                            : Object.assign(query, {\n                                sql: (0, sql_common_1.addSqlCommenterComment)(span, query.sql),\n                            });\n                }\n                const endSpan = (0, utils_1.once)((err, results) => {\n                    if (err) {\n                        span.setStatus({\n                            code: api.SpanStatusCode.ERROR,\n                            message: err.message,\n                        });\n                    }\n                    else {\n                        const { responseHook } = thisPlugin.getConfig();\n                        if (typeof responseHook === 'function') {\n                            (0, instrumentation_1.safeExecuteInTheMiddle)(() => {\n                                responseHook(span, {\n                                    queryResults: results,\n                                });\n                            }, err => {\n                                if (err) {\n                                    thisPlugin._diag.warn('Failed executing responseHook', err);\n                                }\n                            }, true);\n                        }\n                    }\n                    span.end();\n                });\n                if (arguments.length === 1) {\n                    if (typeof query.onResult === 'function') {\n                        thisPlugin._wrap(query, 'onResult', thisPlugin._patchCallbackQuery(endSpan));\n                    }\n                    const streamableQuery = originalQuery.apply(this, arguments);\n                    // `end` in mysql behaves similarly to `result` in mysql2.\n                    streamableQuery\n                        .once('error', err => {\n                        endSpan(err);\n                    })\n                        .once('result', results => {\n                        endSpan(undefined, results);\n                    });\n                    return streamableQuery;\n                }\n                if (typeof arguments[1] === 'function') {\n                    thisPlugin._wrap(arguments, 1, thisPlugin._patchCallbackQuery(endSpan));\n                }\n                else if (typeof arguments[2] === 'function') {\n                    thisPlugin._wrap(arguments, 2, thisPlugin._patchCallbackQuery(endSpan));\n                }\n                return originalQuery.apply(this, arguments);\n            };\n        };\n    }\n    _patchCallbackQuery(endSpan) {\n        return (originalCallback) => {\n            return function (err, results, fields) {\n                endSpan(err, results);\n                return originalCallback(...arguments);\n            };\n        };\n    }\n}\nexports.MySQL2Instrumentation = MySQL2Instrumentation;\nMySQL2Instrumentation.COMMON_ATTRIBUTES = {\n    [semantic_conventions_1.SEMATTRS_DB_SYSTEM]: semantic_conventions_1.DBSYSTEMVALUES_MYSQL,\n};\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/instrumentation.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/types.js":
/*!*******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/types.js ***!
  \*******************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF84ZmIwYjM3NzlmZWU3ODVkYTFkYWUwYTJiZGJmYWMwZC9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLW15c3FsMi9idWlsZC9zcmMvdHlwZXMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYW53YXJcXERvY3VtZW50c1xcQ3ViZW50d2ViXFxjdWJlbnRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF84ZmIwYjM3NzlmZWU3ODVkYTFkYWUwYTJiZGJmYWMwZFxcbm9kZV9tb2R1bGVzXFxAb3BlbnRlbGVtZXRyeVxcaW5zdHJ1bWVudGF0aW9uLW15c3FsMlxcYnVpbGRcXHNyY1xcdHlwZXMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG4vKlxuICogQ29weXJpZ2h0IFRoZSBPcGVuVGVsZW1ldHJ5IEF1dGhvcnNcbiAqXG4gKiBMaWNlbnNlZCB1bmRlciB0aGUgQXBhY2hlIExpY2Vuc2UsIFZlcnNpb24gMi4wICh0aGUgXCJMaWNlbnNlXCIpO1xuICogeW91IG1heSBub3QgdXNlIHRoaXMgZmlsZSBleGNlcHQgaW4gY29tcGxpYW5jZSB3aXRoIHRoZSBMaWNlbnNlLlxuICogWW91IG1heSBvYnRhaW4gYSBjb3B5IG9mIHRoZSBMaWNlbnNlIGF0XG4gKlxuICogICAgICBodHRwczovL3d3dy5hcGFjaGUub3JnL2xpY2Vuc2VzL0xJQ0VOU0UtMi4wXG4gKlxuICogVW5sZXNzIHJlcXVpcmVkIGJ5IGFwcGxpY2FibGUgbGF3IG9yIGFncmVlZCB0byBpbiB3cml0aW5nLCBzb2Z0d2FyZVxuICogZGlzdHJpYnV0ZWQgdW5kZXIgdGhlIExpY2Vuc2UgaXMgZGlzdHJpYnV0ZWQgb24gYW4gXCJBUyBJU1wiIEJBU0lTLFxuICogV0lUSE9VVCBXQVJSQU5USUVTIE9SIENPTkRJVElPTlMgT0YgQU5ZIEtJTkQsIGVpdGhlciBleHByZXNzIG9yIGltcGxpZWQuXG4gKiBTZWUgdGhlIExpY2Vuc2UgZm9yIHRoZSBzcGVjaWZpYyBsYW5ndWFnZSBnb3Zlcm5pbmcgcGVybWlzc2lvbnMgYW5kXG4gKiBsaW1pdGF0aW9ucyB1bmRlciB0aGUgTGljZW5zZS5cbiAqL1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dHlwZXMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/types.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/utils.js":
/*!*******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/utils.js ***!
  \*******************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getConnectionPrototypeToInstrument = exports.once = exports.getSpanName = exports.getDbStatement = exports.getConnectionAttributes = void 0;\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\n/**\n * Get an Attributes map from a mysql connection config object\n *\n * @param config ConnectionConfig\n */\nfunction getConnectionAttributes(config) {\n    const { host, port, database, user } = getConfig(config);\n    const portNumber = parseInt(port, 10);\n    if (!isNaN(portNumber)) {\n        return {\n            [semantic_conventions_1.SEMATTRS_NET_PEER_NAME]: host,\n            [semantic_conventions_1.SEMATTRS_NET_PEER_PORT]: portNumber,\n            [semantic_conventions_1.SEMATTRS_DB_CONNECTION_STRING]: getJDBCString(host, port, database),\n            [semantic_conventions_1.SEMATTRS_DB_NAME]: database,\n            [semantic_conventions_1.SEMATTRS_DB_USER]: user,\n        };\n    }\n    return {\n        [semantic_conventions_1.SEMATTRS_NET_PEER_NAME]: host,\n        [semantic_conventions_1.SEMATTRS_DB_CONNECTION_STRING]: getJDBCString(host, port, database),\n        [semantic_conventions_1.SEMATTRS_DB_NAME]: database,\n        [semantic_conventions_1.SEMATTRS_DB_USER]: user,\n    };\n}\nexports.getConnectionAttributes = getConnectionAttributes;\nfunction getConfig(config) {\n    const { host, port, database, user } = (config && config.connectionConfig) || config || {};\n    return { host, port, database, user };\n}\nfunction getJDBCString(host, port, database) {\n    let jdbcString = `jdbc:mysql://${host || 'localhost'}`;\n    if (typeof port === 'number') {\n        jdbcString += `:${port}`;\n    }\n    if (typeof database === 'string') {\n        jdbcString += `/${database}`;\n    }\n    return jdbcString;\n}\n/**\n * Conjures up the value for the db.statement attribute by formatting a SQL query.\n *\n * @returns the database statement being executed.\n */\nfunction getDbStatement(query, format, values) {\n    if (!format) {\n        return typeof query === 'string' ? query : query.sql;\n    }\n    if (typeof query === 'string') {\n        return values ? format(query, values) : query;\n    }\n    else {\n        // According to https://github.com/mysqljs/mysql#performing-queries\n        // The values argument will override the values in the option object.\n        return values || query.values\n            ? format(query.sql, values || query.values)\n            : query.sql;\n    }\n}\nexports.getDbStatement = getDbStatement;\n/**\n * The span name SHOULD be set to a low cardinality value\n * representing the statement executed on the database.\n *\n * @returns SQL statement without variable arguments or SQL verb\n */\nfunction getSpanName(query) {\n    const rawQuery = typeof query === 'object' ? query.sql : query;\n    // Extract the SQL verb\n    const firstSpace = rawQuery === null || rawQuery === void 0 ? void 0 : rawQuery.indexOf(' ');\n    if (typeof firstSpace === 'number' && firstSpace !== -1) {\n        return rawQuery === null || rawQuery === void 0 ? void 0 : rawQuery.substring(0, firstSpace);\n    }\n    return rawQuery;\n}\nexports.getSpanName = getSpanName;\nconst once = (fn) => {\n    let called = false;\n    return (...args) => {\n        if (called)\n            return;\n        called = true;\n        return fn(...args);\n    };\n};\nexports.once = once;\nfunction getConnectionPrototypeToInstrument(connection) {\n    const connectionPrototype = connection.prototype;\n    const basePrototype = Object.getPrototypeOf(connectionPrototype);\n    // mysql2@3.11.5 included a refactoring, where most code was moved out of the `Connection` class and into a shared base\n    // so we need to instrument that instead, see https://github.com/sidorares/node-mysql2/pull/3081\n    // This checks if the functions we're instrumenting are there on the base - we cannot use the presence of a base\n    // prototype since EventEmitter is the base for mysql2@<=3.11.4\n    if (typeof (basePrototype === null || basePrototype === void 0 ? void 0 : basePrototype.query) === 'function' &&\n        typeof (basePrototype === null || basePrototype === void 0 ? void 0 : basePrototype.execute) === 'function') {\n        return basePrototype;\n    }\n    // otherwise instrument the connection directly.\n    return connectionPrototype;\n}\nexports.getConnectionPrototypeToInstrument = getConnectionPrototypeToInstrument;\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/utils.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/version.js":
/*!*********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/version.js ***!
  \*********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.45.2';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-mysql2';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/version.js\n");

/***/ })

};
;