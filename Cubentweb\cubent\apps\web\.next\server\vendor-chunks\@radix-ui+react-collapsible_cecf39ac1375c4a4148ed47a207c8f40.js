"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-collapsible_cecf39ac1375c4a4148ed47a207c8f40";
exports.ids = ["vendor-chunks/@radix-ui+react-collapsible_cecf39ac1375c4a4148ed47a207c8f40"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/@radix-ui+react-collapsible_cecf39ac1375c4a4148ed47a207c8f40/node_modules/@radix-ui/react-collapsible/dist/index.mjs":
/*!*****************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@radix-ui+react-collapsible_cecf39ac1375c4a4148ed47a207c8f40/node_modules/@radix-ui/react-collapsible/dist/index.mjs ***!
  \*****************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Collapsible: () => (/* binding */ Collapsible),\n/* harmony export */   CollapsibleContent: () => (/* binding */ CollapsibleContent),\n/* harmony export */   CollapsibleTrigger: () => (/* binding */ CollapsibleTrigger),\n/* harmony export */   Content: () => (/* binding */ Content),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   Trigger: () => (/* binding */ Trigger),\n/* harmony export */   createCollapsibleScope: () => (/* binding */ createCollapsibleScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+primitive@1.1.2/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-context@1.1_efc475efe2315f1e47666d242c3ea3f4/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-use-control_038f968d6df614ae636f20523f1cb043/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-use-layout-_ac935108a899bda0f74539c291abe4dd/node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-compose-ref_3a8f72d8524cae11dbbe71796c2b6a49/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-primitive@2_0f3a82528133a7d37b0572d0c112c6a5/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-presence@1._5ce11ab2a6bdc144f66b9a99b0ba8012/node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-id */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-id@1.1.1_@types+react@19.1.5_react@19.1.0/node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Collapsible,CollapsibleContent,CollapsibleTrigger,Content,Root,Trigger,createCollapsibleScope auto */ // src/collapsible.tsx\n\n\n\n\n\n\n\n\n\n\nvar COLLAPSIBLE_NAME = \"Collapsible\";\nvar [createCollapsibleContext, createCollapsibleScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(COLLAPSIBLE_NAME);\nvar [CollapsibleProvider, useCollapsibleContext] = createCollapsibleContext(COLLAPSIBLE_NAME);\nvar Collapsible = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeCollapsible, open: openProp, defaultOpen, disabled, onOpenChange, ...collapsibleProps } = props;\n    const [open, setOpen] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_3__.useControllableState)({\n        prop: openProp,\n        defaultProp: defaultOpen ?? false,\n        onChange: onOpenChange,\n        caller: COLLAPSIBLE_NAME\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollapsibleProvider, {\n        scope: __scopeCollapsible,\n        disabled,\n        contentId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__.useId)(),\n        open,\n        onOpenToggle: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"Collapsible.useCallback\": ()=>setOpen({\n                    \"Collapsible.useCallback\": (prevOpen)=>!prevOpen\n                }[\"Collapsible.useCallback\"])\n        }[\"Collapsible.useCallback\"], [\n            setOpen\n        ]),\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div, {\n            \"data-state\": getState(open),\n            \"data-disabled\": disabled ? \"\" : void 0,\n            ...collapsibleProps,\n            ref: forwardedRef\n        })\n    });\n});\nCollapsible.displayName = COLLAPSIBLE_NAME;\nvar TRIGGER_NAME = \"CollapsibleTrigger\";\nvar CollapsibleTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeCollapsible, ...triggerProps } = props;\n    const context = useCollapsibleContext(TRIGGER_NAME, __scopeCollapsible);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.button, {\n        type: \"button\",\n        \"aria-controls\": context.contentId,\n        \"aria-expanded\": context.open || false,\n        \"data-state\": getState(context.open),\n        \"data-disabled\": context.disabled ? \"\" : void 0,\n        disabled: context.disabled,\n        ...triggerProps,\n        ref: forwardedRef,\n        onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__.composeEventHandlers)(props.onClick, context.onOpenToggle)\n    });\n});\nCollapsibleTrigger.displayName = TRIGGER_NAME;\nvar CONTENT_NAME = \"CollapsibleContent\";\nvar CollapsibleContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { forceMount, ...contentProps } = props;\n    const context = useCollapsibleContext(CONTENT_NAME, props.__scopeCollapsible);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_7__.Presence, {\n        present: forceMount || context.open,\n        children: ({ present })=>/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollapsibleContentImpl, {\n                ...contentProps,\n                ref: forwardedRef,\n                present\n            })\n    });\n});\nCollapsibleContent.displayName = CONTENT_NAME;\nvar CollapsibleContentImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeCollapsible, present, children, ...contentProps } = props;\n    const context = useCollapsibleContext(CONTENT_NAME, __scopeCollapsible);\n    const [isPresent, setIsPresent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(present);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_8__.useComposedRefs)(forwardedRef, ref);\n    const heightRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const height = heightRef.current;\n    const widthRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const width = widthRef.current;\n    const isOpen = context.open || isPresent;\n    const isMountAnimationPreventedRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(isOpen);\n    const originalStylesRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(void 0);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"CollapsibleContentImpl.useEffect\": ()=>{\n            const rAF = requestAnimationFrame({\n                \"CollapsibleContentImpl.useEffect.rAF\": ()=>isMountAnimationPreventedRef.current = false\n            }[\"CollapsibleContentImpl.useEffect.rAF\"]);\n            return ({\n                \"CollapsibleContentImpl.useEffect\": ()=>cancelAnimationFrame(rAF)\n            })[\"CollapsibleContentImpl.useEffect\"];\n        }\n    }[\"CollapsibleContentImpl.useEffect\"], []);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_9__.useLayoutEffect)({\n        \"CollapsibleContentImpl.useLayoutEffect\": ()=>{\n            const node = ref.current;\n            if (node) {\n                originalStylesRef.current = originalStylesRef.current || {\n                    transitionDuration: node.style.transitionDuration,\n                    animationName: node.style.animationName\n                };\n                node.style.transitionDuration = \"0s\";\n                node.style.animationName = \"none\";\n                const rect = node.getBoundingClientRect();\n                heightRef.current = rect.height;\n                widthRef.current = rect.width;\n                if (!isMountAnimationPreventedRef.current) {\n                    node.style.transitionDuration = originalStylesRef.current.transitionDuration;\n                    node.style.animationName = originalStylesRef.current.animationName;\n                }\n                setIsPresent(present);\n            }\n        }\n    }[\"CollapsibleContentImpl.useLayoutEffect\"], [\n        context.open,\n        present\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div, {\n        \"data-state\": getState(context.open),\n        \"data-disabled\": context.disabled ? \"\" : void 0,\n        id: context.contentId,\n        hidden: !isOpen,\n        ...contentProps,\n        ref: composedRefs,\n        style: {\n            [`--radix-collapsible-content-height`]: height ? `${height}px` : void 0,\n            [`--radix-collapsible-content-width`]: width ? `${width}px` : void 0,\n            ...props.style\n        },\n        children: isOpen && children\n    });\n});\nfunction getState(open) {\n    return open ? \"open\" : \"closed\";\n}\nvar Root = Collapsible;\nvar Trigger = CollapsibleTrigger;\nvar Content = CollapsibleContent;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@radix-ui+react-collapsible_cecf39ac1375c4a4148ed47a207c8f40/node_modules/@radix-ui/react-collapsible/dist/index.mjs\n");

/***/ })

};
;