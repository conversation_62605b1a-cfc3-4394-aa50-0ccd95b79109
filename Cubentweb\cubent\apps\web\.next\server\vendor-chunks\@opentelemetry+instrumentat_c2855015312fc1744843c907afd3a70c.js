"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@opentelemetry+instrumentat_c2855015312fc1744843c907afd3a70c";
exports.ids = ["vendor-chunks/@opentelemetry+instrumentat_c2855015312fc1744843c907afd3a70c"];
exports.modules = {

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c2855015312fc1744843c907afd3a70c/node_modules/@opentelemetry/instrumentation-tedious/build/src/index.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_c2855015312fc1744843c907afd3a70c/node_modules/@opentelemetry/instrumentation-tedious/build/src/index.js ***!
  \********************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c2855015312fc1744843c907afd3a70c/node_modules/@opentelemetry/instrumentation-tedious/build/src/instrumentation.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c2855015312fc1744843c907afd3a70c/node_modules/@opentelemetry/instrumentation-tedious/build/src/types.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9Ab3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfYzI4NTUwMTUzMTJmYzE3NDQ4NDNjOTA3YWZkM2E3MGMvbm9kZV9tb2R1bGVzL0BvcGVudGVsZW1ldHJ5L2luc3RydW1lbnRhdGlvbi10ZWRpb3VzL2J1aWxkL3NyYy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQ0FBbUMsb0NBQW9DLGdCQUFnQjtBQUN2RixDQUFDO0FBQ0Q7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQSw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsYUFBYSxtQkFBTyxDQUFDLDhNQUFtQjtBQUN4QyxhQUFhLG1CQUFPLENBQUMsMExBQVM7QUFDOUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYW53YXJcXERvY3VtZW50c1xcQ3ViZW50d2ViXFxjdWJlbnRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF9jMjg1NTAxNTMxMmZjMTc0NDg0M2M5MDdhZmQzYTcwY1xcbm9kZV9tb2R1bGVzXFxAb3BlbnRlbGVtZXRyeVxcaW5zdHJ1bWVudGF0aW9uLXRlZGlvdXNcXGJ1aWxkXFxzcmNcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuLypcbiAqIENvcHlyaWdodCBUaGUgT3BlblRlbGVtZXRyeSBBdXRob3JzXG4gKlxuICogTGljZW5zZWQgdW5kZXIgdGhlIEFwYWNoZSBMaWNlbnNlLCBWZXJzaW9uIDIuMCAodGhlIFwiTGljZW5zZVwiKTtcbiAqIHlvdSBtYXkgbm90IHVzZSB0aGlzIGZpbGUgZXhjZXB0IGluIGNvbXBsaWFuY2Ugd2l0aCB0aGUgTGljZW5zZS5cbiAqIFlvdSBtYXkgb2J0YWluIGEgY29weSBvZiB0aGUgTGljZW5zZSBhdFxuICpcbiAqICAgICAgaHR0cHM6Ly93d3cuYXBhY2hlLm9yZy9saWNlbnNlcy9MSUNFTlNFLTIuMFxuICpcbiAqIFVubGVzcyByZXF1aXJlZCBieSBhcHBsaWNhYmxlIGxhdyBvciBhZ3JlZWQgdG8gaW4gd3JpdGluZywgc29mdHdhcmVcbiAqIGRpc3RyaWJ1dGVkIHVuZGVyIHRoZSBMaWNlbnNlIGlzIGRpc3RyaWJ1dGVkIG9uIGFuIFwiQVMgSVNcIiBCQVNJUyxcbiAqIFdJVEhPVVQgV0FSUkFOVElFUyBPUiBDT05ESVRJT05TIE9GIEFOWSBLSU5ELCBlaXRoZXIgZXhwcmVzcyBvciBpbXBsaWVkLlxuICogU2VlIHRoZSBMaWNlbnNlIGZvciB0aGUgc3BlY2lmaWMgbGFuZ3VhZ2UgZ292ZXJuaW5nIHBlcm1pc3Npb25zIGFuZFxuICogbGltaXRhdGlvbnMgdW5kZXIgdGhlIExpY2Vuc2UuXG4gKi9cbnZhciBfX2NyZWF0ZUJpbmRpbmcgPSAodGhpcyAmJiB0aGlzLl9fY3JlYXRlQmluZGluZykgfHwgKE9iamVjdC5jcmVhdGUgPyAoZnVuY3Rpb24obywgbSwgaywgazIpIHtcbiAgICBpZiAoazIgPT09IHVuZGVmaW5lZCkgazIgPSBrO1xuICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eShvLCBrMiwgeyBlbnVtZXJhYmxlOiB0cnVlLCBnZXQ6IGZ1bmN0aW9uKCkgeyByZXR1cm4gbVtrXTsgfSB9KTtcbn0pIDogKGZ1bmN0aW9uKG8sIG0sIGssIGsyKSB7XG4gICAgaWYgKGsyID09PSB1bmRlZmluZWQpIGsyID0gaztcbiAgICBvW2syXSA9IG1ba107XG59KSk7XG52YXIgX19leHBvcnRTdGFyID0gKHRoaXMgJiYgdGhpcy5fX2V4cG9ydFN0YXIpIHx8IGZ1bmN0aW9uKG0sIGV4cG9ydHMpIHtcbiAgICBmb3IgKHZhciBwIGluIG0pIGlmIChwICE9PSBcImRlZmF1bHRcIiAmJiAhT2JqZWN0LnByb3RvdHlwZS5oYXNPd25Qcm9wZXJ0eS5jYWxsKGV4cG9ydHMsIHApKSBfX2NyZWF0ZUJpbmRpbmcoZXhwb3J0cywgbSwgcCk7XG59O1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuX19leHBvcnRTdGFyKHJlcXVpcmUoXCIuL2luc3RydW1lbnRhdGlvblwiKSwgZXhwb3J0cyk7XG5fX2V4cG9ydFN0YXIocmVxdWlyZShcIi4vdHlwZXNcIiksIGV4cG9ydHMpO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXguanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c2855015312fc1744843c907afd3a70c/node_modules/@opentelemetry/instrumentation-tedious/build/src/index.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c2855015312fc1744843c907afd3a70c/node_modules/@opentelemetry/instrumentation-tedious/build/src/instrumentation.js":
/*!******************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_c2855015312fc1744843c907afd3a70c/node_modules/@opentelemetry/instrumentation-tedious/build/src/instrumentation.js ***!
  \******************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.TediousInstrumentation = void 0;\nconst api = __webpack_require__(/*! @opentelemetry/api */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst events_1 = __webpack_require__(/*! events */ \"events\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c2855015312fc1744843c907afd3a70c/node_modules/@opentelemetry/instrumentation-tedious/build/src/utils.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c2855015312fc1744843c907afd3a70c/node_modules/@opentelemetry/instrumentation-tedious/build/src/version.js\");\nconst CURRENT_DATABASE = Symbol('opentelemetry.instrumentation-tedious.current-database');\nconst PATCHED_METHODS = [\n    'callProcedure',\n    'execSql',\n    'execSqlBatch',\n    'execBulkLoad',\n    'prepare',\n    'execute',\n];\nfunction setDatabase(databaseName) {\n    Object.defineProperty(this, CURRENT_DATABASE, {\n        value: databaseName,\n        writable: true,\n    });\n}\nclass TediousInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, config);\n    }\n    init() {\n        return [\n            new instrumentation_1.InstrumentationNodeModuleDefinition(TediousInstrumentation.COMPONENT, ['>=1.11.0 <20'], (moduleExports) => {\n                const ConnectionPrototype = moduleExports.Connection.prototype;\n                for (const method of PATCHED_METHODS) {\n                    if ((0, instrumentation_1.isWrapped)(ConnectionPrototype[method])) {\n                        this._unwrap(ConnectionPrototype, method);\n                    }\n                    this._wrap(ConnectionPrototype, method, this._patchQuery(method));\n                }\n                if ((0, instrumentation_1.isWrapped)(ConnectionPrototype.connect)) {\n                    this._unwrap(ConnectionPrototype, 'connect');\n                }\n                this._wrap(ConnectionPrototype, 'connect', this._patchConnect);\n                return moduleExports;\n            }, (moduleExports) => {\n                if (moduleExports === undefined)\n                    return;\n                const ConnectionPrototype = moduleExports.Connection.prototype;\n                for (const method of PATCHED_METHODS) {\n                    this._unwrap(ConnectionPrototype, method);\n                }\n                this._unwrap(ConnectionPrototype, 'connect');\n            }),\n        ];\n    }\n    _patchConnect(original) {\n        return function patchedConnect() {\n            var _a, _b;\n            setDatabase.call(this, (_b = (_a = this.config) === null || _a === void 0 ? void 0 : _a.options) === null || _b === void 0 ? void 0 : _b.database);\n            // remove the listener first in case it's already added\n            this.removeListener('databaseChange', setDatabase);\n            this.on('databaseChange', setDatabase);\n            this.once('end', () => {\n                this.removeListener('databaseChange', setDatabase);\n            });\n            return original.apply(this, arguments);\n        };\n    }\n    _patchQuery(operation) {\n        return (originalMethod) => {\n            const thisPlugin = this;\n            function patchedMethod(request) {\n                var _a, _b, _c, _d, _e, _f, _g, _h;\n                if (!(request instanceof events_1.EventEmitter)) {\n                    thisPlugin._diag.warn(`Unexpected invocation of patched ${operation} method. Span not recorded`);\n                    return originalMethod.apply(this, arguments);\n                }\n                let procCount = 0;\n                let statementCount = 0;\n                const incrementStatementCount = () => statementCount++;\n                const incrementProcCount = () => procCount++;\n                const databaseName = this[CURRENT_DATABASE];\n                const sql = (request => {\n                    var _a, _b;\n                    // Required for <11.0.9\n                    if (request.sqlTextOrProcedure === 'sp_prepare' &&\n                        ((_b = (_a = request.parametersByName) === null || _a === void 0 ? void 0 : _a.stmt) === null || _b === void 0 ? void 0 : _b.value)) {\n                        return request.parametersByName.stmt.value;\n                    }\n                    return request.sqlTextOrProcedure;\n                })(request);\n                const span = thisPlugin.tracer.startSpan((0, utils_1.getSpanName)(operation, databaseName, sql, request.table), {\n                    kind: api.SpanKind.CLIENT,\n                    attributes: {\n                        [semantic_conventions_1.SEMATTRS_DB_SYSTEM]: semantic_conventions_1.DBSYSTEMVALUES_MSSQL,\n                        [semantic_conventions_1.SEMATTRS_DB_NAME]: databaseName,\n                        [semantic_conventions_1.SEMATTRS_NET_PEER_PORT]: (_b = (_a = this.config) === null || _a === void 0 ? void 0 : _a.options) === null || _b === void 0 ? void 0 : _b.port,\n                        [semantic_conventions_1.SEMATTRS_NET_PEER_NAME]: (_c = this.config) === null || _c === void 0 ? void 0 : _c.server,\n                        // >=4 uses `authentication` object, older versions just userName and password pair\n                        [semantic_conventions_1.SEMATTRS_DB_USER]: (_e = (_d = this.config) === null || _d === void 0 ? void 0 : _d.userName) !== null && _e !== void 0 ? _e : (_h = (_g = (_f = this.config) === null || _f === void 0 ? void 0 : _f.authentication) === null || _g === void 0 ? void 0 : _g.options) === null || _h === void 0 ? void 0 : _h.userName,\n                        [semantic_conventions_1.SEMATTRS_DB_STATEMENT]: sql,\n                        [semantic_conventions_1.SEMATTRS_DB_SQL_TABLE]: request.table,\n                    },\n                });\n                const endSpan = (0, utils_1.once)((err) => {\n                    request.removeListener('done', incrementStatementCount);\n                    request.removeListener('doneInProc', incrementStatementCount);\n                    request.removeListener('doneProc', incrementProcCount);\n                    request.removeListener('error', endSpan);\n                    this.removeListener('end', endSpan);\n                    span.setAttribute('tedious.procedure_count', procCount);\n                    span.setAttribute('tedious.statement_count', statementCount);\n                    if (err) {\n                        span.setStatus({\n                            code: api.SpanStatusCode.ERROR,\n                            message: err.message,\n                        });\n                    }\n                    span.end();\n                });\n                request.on('done', incrementStatementCount);\n                request.on('doneInProc', incrementStatementCount);\n                request.on('doneProc', incrementProcCount);\n                request.once('error', endSpan);\n                this.on('end', endSpan);\n                if (typeof request.callback === 'function') {\n                    thisPlugin._wrap(request, 'callback', thisPlugin._patchCallbackQuery(endSpan));\n                }\n                else {\n                    thisPlugin._diag.error('Expected request.callback to be a function');\n                }\n                return api.context.with(api.trace.setSpan(api.context.active(), span), originalMethod, this, ...arguments);\n            }\n            Object.defineProperty(patchedMethod, 'length', {\n                value: originalMethod.length,\n                writable: false,\n            });\n            return patchedMethod;\n        };\n    }\n    _patchCallbackQuery(endSpan) {\n        return (originalCallback) => {\n            return function (err, rowCount, rows) {\n                endSpan(err);\n                return originalCallback.apply(this, arguments);\n            };\n        };\n    }\n}\nexports.TediousInstrumentation = TediousInstrumentation;\nTediousInstrumentation.COMPONENT = 'tedious';\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9Ab3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfYzI4NTUwMTUzMTJmYzE3NDQ4NDNjOTA3YWZkM2E3MGMvbm9kZV9tb2R1bGVzL0BvcGVudGVsZW1ldHJ5L2luc3RydW1lbnRhdGlvbi10ZWRpb3VzL2J1aWxkL3NyYy9pbnN0cnVtZW50YXRpb24uanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsOEJBQThCO0FBQzlCLFlBQVksbUJBQU8sQ0FBQyw2SUFBb0I7QUFDeEMsaUJBQWlCLG1CQUFPLENBQUMsc0JBQVE7QUFDakMsMEJBQTBCLG1CQUFPLENBQUMseU1BQWdDO0FBQ2xFLCtCQUErQixtQkFBTyxDQUFDLGlNQUFxQztBQUM1RSxnQkFBZ0IsbUJBQU8sQ0FBQywwTEFBUztBQUNqQztBQUNBLGtCQUFrQixtQkFBTyxDQUFDLDhMQUFXO0FBQ3JDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSwyQkFBMkI7QUFDM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhFQUE4RSxXQUFXO0FBQ3pGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUI7QUFDekI7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4QkFBOEI7QUFDOUI7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbndhclxcRG9jdW1lbnRzXFxDdWJlbnR3ZWJcXGN1YmVudFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQG9wZW50ZWxlbWV0cnkraW5zdHJ1bWVudGF0X2MyODU1MDE1MzEyZmMxNzQ0ODQzYzkwN2FmZDNhNzBjXFxub2RlX21vZHVsZXNcXEBvcGVudGVsZW1ldHJ5XFxpbnN0cnVtZW50YXRpb24tdGVkaW91c1xcYnVpbGRcXHNyY1xcaW5zdHJ1bWVudGF0aW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuLypcbiAqIENvcHlyaWdodCBUaGUgT3BlblRlbGVtZXRyeSBBdXRob3JzXG4gKlxuICogTGljZW5zZWQgdW5kZXIgdGhlIEFwYWNoZSBMaWNlbnNlLCBWZXJzaW9uIDIuMCAodGhlIFwiTGljZW5zZVwiKTtcbiAqIHlvdSBtYXkgbm90IHVzZSB0aGlzIGZpbGUgZXhjZXB0IGluIGNvbXBsaWFuY2Ugd2l0aCB0aGUgTGljZW5zZS5cbiAqIFlvdSBtYXkgb2J0YWluIGEgY29weSBvZiB0aGUgTGljZW5zZSBhdFxuICpcbiAqICAgICAgaHR0cHM6Ly93d3cuYXBhY2hlLm9yZy9saWNlbnNlcy9MSUNFTlNFLTIuMFxuICpcbiAqIFVubGVzcyByZXF1aXJlZCBieSBhcHBsaWNhYmxlIGxhdyBvciBhZ3JlZWQgdG8gaW4gd3JpdGluZywgc29mdHdhcmVcbiAqIGRpc3RyaWJ1dGVkIHVuZGVyIHRoZSBMaWNlbnNlIGlzIGRpc3RyaWJ1dGVkIG9uIGFuIFwiQVMgSVNcIiBCQVNJUyxcbiAqIFdJVEhPVVQgV0FSUkFOVElFUyBPUiBDT05ESVRJT05TIE9GIEFOWSBLSU5ELCBlaXRoZXIgZXhwcmVzcyBvciBpbXBsaWVkLlxuICogU2VlIHRoZSBMaWNlbnNlIGZvciB0aGUgc3BlY2lmaWMgbGFuZ3VhZ2UgZ292ZXJuaW5nIHBlcm1pc3Npb25zIGFuZFxuICogbGltaXRhdGlvbnMgdW5kZXIgdGhlIExpY2Vuc2UuXG4gKi9cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuVGVkaW91c0luc3RydW1lbnRhdGlvbiA9IHZvaWQgMDtcbmNvbnN0IGFwaSA9IHJlcXVpcmUoXCJAb3BlbnRlbGVtZXRyeS9hcGlcIik7XG5jb25zdCBldmVudHNfMSA9IHJlcXVpcmUoXCJldmVudHNcIik7XG5jb25zdCBpbnN0cnVtZW50YXRpb25fMSA9IHJlcXVpcmUoXCJAb3BlbnRlbGVtZXRyeS9pbnN0cnVtZW50YXRpb25cIik7XG5jb25zdCBzZW1hbnRpY19jb252ZW50aW9uc18xID0gcmVxdWlyZShcIkBvcGVudGVsZW1ldHJ5L3NlbWFudGljLWNvbnZlbnRpb25zXCIpO1xuY29uc3QgdXRpbHNfMSA9IHJlcXVpcmUoXCIuL3V0aWxzXCIpO1xuLyoqIEBrbmlwaWdub3JlICovXG5jb25zdCB2ZXJzaW9uXzEgPSByZXF1aXJlKFwiLi92ZXJzaW9uXCIpO1xuY29uc3QgQ1VSUkVOVF9EQVRBQkFTRSA9IFN5bWJvbCgnb3BlbnRlbGVtZXRyeS5pbnN0cnVtZW50YXRpb24tdGVkaW91cy5jdXJyZW50LWRhdGFiYXNlJyk7XG5jb25zdCBQQVRDSEVEX01FVEhPRFMgPSBbXG4gICAgJ2NhbGxQcm9jZWR1cmUnLFxuICAgICdleGVjU3FsJyxcbiAgICAnZXhlY1NxbEJhdGNoJyxcbiAgICAnZXhlY0J1bGtMb2FkJyxcbiAgICAncHJlcGFyZScsXG4gICAgJ2V4ZWN1dGUnLFxuXTtcbmZ1bmN0aW9uIHNldERhdGFiYXNlKGRhdGFiYXNlTmFtZSkge1xuICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0aGlzLCBDVVJSRU5UX0RBVEFCQVNFLCB7XG4gICAgICAgIHZhbHVlOiBkYXRhYmFzZU5hbWUsXG4gICAgICAgIHdyaXRhYmxlOiB0cnVlLFxuICAgIH0pO1xufVxuY2xhc3MgVGVkaW91c0luc3RydW1lbnRhdGlvbiBleHRlbmRzIGluc3RydW1lbnRhdGlvbl8xLkluc3RydW1lbnRhdGlvbkJhc2Uge1xuICAgIGNvbnN0cnVjdG9yKGNvbmZpZyA9IHt9KSB7XG4gICAgICAgIHN1cGVyKHZlcnNpb25fMS5QQUNLQUdFX05BTUUsIHZlcnNpb25fMS5QQUNLQUdFX1ZFUlNJT04sIGNvbmZpZyk7XG4gICAgfVxuICAgIGluaXQoKSB7XG4gICAgICAgIHJldHVybiBbXG4gICAgICAgICAgICBuZXcgaW5zdHJ1bWVudGF0aW9uXzEuSW5zdHJ1bWVudGF0aW9uTm9kZU1vZHVsZURlZmluaXRpb24oVGVkaW91c0luc3RydW1lbnRhdGlvbi5DT01QT05FTlQsIFsnPj0xLjExLjAgPDIwJ10sIChtb2R1bGVFeHBvcnRzKSA9PiB7XG4gICAgICAgICAgICAgICAgY29uc3QgQ29ubmVjdGlvblByb3RvdHlwZSA9IG1vZHVsZUV4cG9ydHMuQ29ubmVjdGlvbi5wcm90b3R5cGU7XG4gICAgICAgICAgICAgICAgZm9yIChjb25zdCBtZXRob2Qgb2YgUEFUQ0hFRF9NRVRIT0RTKSB7XG4gICAgICAgICAgICAgICAgICAgIGlmICgoMCwgaW5zdHJ1bWVudGF0aW9uXzEuaXNXcmFwcGVkKShDb25uZWN0aW9uUHJvdG90eXBlW21ldGhvZF0pKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzLl91bndyYXAoQ29ubmVjdGlvblByb3RvdHlwZSwgbWV0aG9kKTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICB0aGlzLl93cmFwKENvbm5lY3Rpb25Qcm90b3R5cGUsIG1ldGhvZCwgdGhpcy5fcGF0Y2hRdWVyeShtZXRob2QpKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgaWYgKCgwLCBpbnN0cnVtZW50YXRpb25fMS5pc1dyYXBwZWQpKENvbm5lY3Rpb25Qcm90b3R5cGUuY29ubmVjdCkpIHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5fdW53cmFwKENvbm5lY3Rpb25Qcm90b3R5cGUsICdjb25uZWN0Jyk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIHRoaXMuX3dyYXAoQ29ubmVjdGlvblByb3RvdHlwZSwgJ2Nvbm5lY3QnLCB0aGlzLl9wYXRjaENvbm5lY3QpO1xuICAgICAgICAgICAgICAgIHJldHVybiBtb2R1bGVFeHBvcnRzO1xuICAgICAgICAgICAgfSwgKG1vZHVsZUV4cG9ydHMpID0+IHtcbiAgICAgICAgICAgICAgICBpZiAobW9kdWxlRXhwb3J0cyA9PT0gdW5kZWZpbmVkKVxuICAgICAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICAgICAgY29uc3QgQ29ubmVjdGlvblByb3RvdHlwZSA9IG1vZHVsZUV4cG9ydHMuQ29ubmVjdGlvbi5wcm90b3R5cGU7XG4gICAgICAgICAgICAgICAgZm9yIChjb25zdCBtZXRob2Qgb2YgUEFUQ0hFRF9NRVRIT0RTKSB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuX3Vud3JhcChDb25uZWN0aW9uUHJvdG90eXBlLCBtZXRob2QpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB0aGlzLl91bndyYXAoQ29ubmVjdGlvblByb3RvdHlwZSwgJ2Nvbm5lY3QnKTtcbiAgICAgICAgICAgIH0pLFxuICAgICAgICBdO1xuICAgIH1cbiAgICBfcGF0Y2hDb25uZWN0KG9yaWdpbmFsKSB7XG4gICAgICAgIHJldHVybiBmdW5jdGlvbiBwYXRjaGVkQ29ubmVjdCgpIHtcbiAgICAgICAgICAgIHZhciBfYSwgX2I7XG4gICAgICAgICAgICBzZXREYXRhYmFzZS5jYWxsKHRoaXMsIChfYiA9IChfYSA9IHRoaXMuY29uZmlnKSA9PT0gbnVsbCB8fCBfYSA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2Eub3B0aW9ucykgPT09IG51bGwgfHwgX2IgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9iLmRhdGFiYXNlKTtcbiAgICAgICAgICAgIC8vIHJlbW92ZSB0aGUgbGlzdGVuZXIgZmlyc3QgaW4gY2FzZSBpdCdzIGFscmVhZHkgYWRkZWRcbiAgICAgICAgICAgIHRoaXMucmVtb3ZlTGlzdGVuZXIoJ2RhdGFiYXNlQ2hhbmdlJywgc2V0RGF0YWJhc2UpO1xuICAgICAgICAgICAgdGhpcy5vbignZGF0YWJhc2VDaGFuZ2UnLCBzZXREYXRhYmFzZSk7XG4gICAgICAgICAgICB0aGlzLm9uY2UoJ2VuZCcsICgpID0+IHtcbiAgICAgICAgICAgICAgICB0aGlzLnJlbW92ZUxpc3RlbmVyKCdkYXRhYmFzZUNoYW5nZScsIHNldERhdGFiYXNlKTtcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgcmV0dXJuIG9yaWdpbmFsLmFwcGx5KHRoaXMsIGFyZ3VtZW50cyk7XG4gICAgICAgIH07XG4gICAgfVxuICAgIF9wYXRjaFF1ZXJ5KG9wZXJhdGlvbikge1xuICAgICAgICByZXR1cm4gKG9yaWdpbmFsTWV0aG9kKSA9PiB7XG4gICAgICAgICAgICBjb25zdCB0aGlzUGx1Z2luID0gdGhpcztcbiAgICAgICAgICAgIGZ1bmN0aW9uIHBhdGNoZWRNZXRob2QocmVxdWVzdCkge1xuICAgICAgICAgICAgICAgIHZhciBfYSwgX2IsIF9jLCBfZCwgX2UsIF9mLCBfZywgX2g7XG4gICAgICAgICAgICAgICAgaWYgKCEocmVxdWVzdCBpbnN0YW5jZW9mIGV2ZW50c18xLkV2ZW50RW1pdHRlcikpIHtcbiAgICAgICAgICAgICAgICAgICAgdGhpc1BsdWdpbi5fZGlhZy53YXJuKGBVbmV4cGVjdGVkIGludm9jYXRpb24gb2YgcGF0Y2hlZCAke29wZXJhdGlvbn0gbWV0aG9kLiBTcGFuIG5vdCByZWNvcmRlZGApO1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4gb3JpZ2luYWxNZXRob2QuYXBwbHkodGhpcywgYXJndW1lbnRzKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgbGV0IHByb2NDb3VudCA9IDA7XG4gICAgICAgICAgICAgICAgbGV0IHN0YXRlbWVudENvdW50ID0gMDtcbiAgICAgICAgICAgICAgICBjb25zdCBpbmNyZW1lbnRTdGF0ZW1lbnRDb3VudCA9ICgpID0+IHN0YXRlbWVudENvdW50Kys7XG4gICAgICAgICAgICAgICAgY29uc3QgaW5jcmVtZW50UHJvY0NvdW50ID0gKCkgPT4gcHJvY0NvdW50Kys7XG4gICAgICAgICAgICAgICAgY29uc3QgZGF0YWJhc2VOYW1lID0gdGhpc1tDVVJSRU5UX0RBVEFCQVNFXTtcbiAgICAgICAgICAgICAgICBjb25zdCBzcWwgPSAocmVxdWVzdCA9PiB7XG4gICAgICAgICAgICAgICAgICAgIHZhciBfYSwgX2I7XG4gICAgICAgICAgICAgICAgICAgIC8vIFJlcXVpcmVkIGZvciA8MTEuMC45XG4gICAgICAgICAgICAgICAgICAgIGlmIChyZXF1ZXN0LnNxbFRleHRPclByb2NlZHVyZSA9PT0gJ3NwX3ByZXBhcmUnICYmXG4gICAgICAgICAgICAgICAgICAgICAgICAoKF9iID0gKF9hID0gcmVxdWVzdC5wYXJhbWV0ZXJzQnlOYW1lKSA9PT0gbnVsbCB8fCBfYSA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2Euc3RtdCkgPT09IG51bGwgfHwgX2IgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9iLnZhbHVlKSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHJlcXVlc3QucGFyYW1ldGVyc0J5TmFtZS5zdG10LnZhbHVlO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiByZXF1ZXN0LnNxbFRleHRPclByb2NlZHVyZTtcbiAgICAgICAgICAgICAgICB9KShyZXF1ZXN0KTtcbiAgICAgICAgICAgICAgICBjb25zdCBzcGFuID0gdGhpc1BsdWdpbi50cmFjZXIuc3RhcnRTcGFuKCgwLCB1dGlsc18xLmdldFNwYW5OYW1lKShvcGVyYXRpb24sIGRhdGFiYXNlTmFtZSwgc3FsLCByZXF1ZXN0LnRhYmxlKSwge1xuICAgICAgICAgICAgICAgICAgICBraW5kOiBhcGkuU3BhbktpbmQuQ0xJRU5ULFxuICAgICAgICAgICAgICAgICAgICBhdHRyaWJ1dGVzOiB7XG4gICAgICAgICAgICAgICAgICAgICAgICBbc2VtYW50aWNfY29udmVudGlvbnNfMS5TRU1BVFRSU19EQl9TWVNURU1dOiBzZW1hbnRpY19jb252ZW50aW9uc18xLkRCU1lTVEVNVkFMVUVTX01TU1FMLFxuICAgICAgICAgICAgICAgICAgICAgICAgW3NlbWFudGljX2NvbnZlbnRpb25zXzEuU0VNQVRUUlNfREJfTkFNRV06IGRhdGFiYXNlTmFtZSxcbiAgICAgICAgICAgICAgICAgICAgICAgIFtzZW1hbnRpY19jb252ZW50aW9uc18xLlNFTUFUVFJTX05FVF9QRUVSX1BPUlRdOiAoX2IgPSAoX2EgPSB0aGlzLmNvbmZpZykgPT09IG51bGwgfHwgX2EgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9hLm9wdGlvbnMpID09PSBudWxsIHx8IF9iID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYi5wb3J0LFxuICAgICAgICAgICAgICAgICAgICAgICAgW3NlbWFudGljX2NvbnZlbnRpb25zXzEuU0VNQVRUUlNfTkVUX1BFRVJfTkFNRV06IChfYyA9IHRoaXMuY29uZmlnKSA9PT0gbnVsbCB8fCBfYyA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2Muc2VydmVyLFxuICAgICAgICAgICAgICAgICAgICAgICAgLy8gPj00IHVzZXMgYGF1dGhlbnRpY2F0aW9uYCBvYmplY3QsIG9sZGVyIHZlcnNpb25zIGp1c3QgdXNlck5hbWUgYW5kIHBhc3N3b3JkIHBhaXJcbiAgICAgICAgICAgICAgICAgICAgICAgIFtzZW1hbnRpY19jb252ZW50aW9uc18xLlNFTUFUVFJTX0RCX1VTRVJdOiAoX2UgPSAoX2QgPSB0aGlzLmNvbmZpZykgPT09IG51bGwgfHwgX2QgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9kLnVzZXJOYW1lKSAhPT0gbnVsbCAmJiBfZSAhPT0gdm9pZCAwID8gX2UgOiAoX2ggPSAoX2cgPSAoX2YgPSB0aGlzLmNvbmZpZykgPT09IG51bGwgfHwgX2YgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9mLmF1dGhlbnRpY2F0aW9uKSA9PT0gbnVsbCB8fCBfZyA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2cub3B0aW9ucykgPT09IG51bGwgfHwgX2ggPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9oLnVzZXJOYW1lLFxuICAgICAgICAgICAgICAgICAgICAgICAgW3NlbWFudGljX2NvbnZlbnRpb25zXzEuU0VNQVRUUlNfREJfU1RBVEVNRU5UXTogc3FsLFxuICAgICAgICAgICAgICAgICAgICAgICAgW3NlbWFudGljX2NvbnZlbnRpb25zXzEuU0VNQVRUUlNfREJfU1FMX1RBQkxFXTogcmVxdWVzdC50YWJsZSxcbiAgICAgICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICBjb25zdCBlbmRTcGFuID0gKDAsIHV0aWxzXzEub25jZSkoKGVycikgPT4ge1xuICAgICAgICAgICAgICAgICAgICByZXF1ZXN0LnJlbW92ZUxpc3RlbmVyKCdkb25lJywgaW5jcmVtZW50U3RhdGVtZW50Q291bnQpO1xuICAgICAgICAgICAgICAgICAgICByZXF1ZXN0LnJlbW92ZUxpc3RlbmVyKCdkb25lSW5Qcm9jJywgaW5jcmVtZW50U3RhdGVtZW50Q291bnQpO1xuICAgICAgICAgICAgICAgICAgICByZXF1ZXN0LnJlbW92ZUxpc3RlbmVyKCdkb25lUHJvYycsIGluY3JlbWVudFByb2NDb3VudCk7XG4gICAgICAgICAgICAgICAgICAgIHJlcXVlc3QucmVtb3ZlTGlzdGVuZXIoJ2Vycm9yJywgZW5kU3Bhbik7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMucmVtb3ZlTGlzdGVuZXIoJ2VuZCcsIGVuZFNwYW4pO1xuICAgICAgICAgICAgICAgICAgICBzcGFuLnNldEF0dHJpYnV0ZSgndGVkaW91cy5wcm9jZWR1cmVfY291bnQnLCBwcm9jQ291bnQpO1xuICAgICAgICAgICAgICAgICAgICBzcGFuLnNldEF0dHJpYnV0ZSgndGVkaW91cy5zdGF0ZW1lbnRfY291bnQnLCBzdGF0ZW1lbnRDb3VudCk7XG4gICAgICAgICAgICAgICAgICAgIGlmIChlcnIpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHNwYW4uc2V0U3RhdHVzKHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2RlOiBhcGkuU3BhblN0YXR1c0NvZGUuRVJST1IsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbWVzc2FnZTogZXJyLm1lc3NhZ2UsXG4gICAgICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBzcGFuLmVuZCgpO1xuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgIHJlcXVlc3Qub24oJ2RvbmUnLCBpbmNyZW1lbnRTdGF0ZW1lbnRDb3VudCk7XG4gICAgICAgICAgICAgICAgcmVxdWVzdC5vbignZG9uZUluUHJvYycsIGluY3JlbWVudFN0YXRlbWVudENvdW50KTtcbiAgICAgICAgICAgICAgICByZXF1ZXN0Lm9uKCdkb25lUHJvYycsIGluY3JlbWVudFByb2NDb3VudCk7XG4gICAgICAgICAgICAgICAgcmVxdWVzdC5vbmNlKCdlcnJvcicsIGVuZFNwYW4pO1xuICAgICAgICAgICAgICAgIHRoaXMub24oJ2VuZCcsIGVuZFNwYW4pO1xuICAgICAgICAgICAgICAgIGlmICh0eXBlb2YgcmVxdWVzdC5jYWxsYmFjayA9PT0gJ2Z1bmN0aW9uJykge1xuICAgICAgICAgICAgICAgICAgICB0aGlzUGx1Z2luLl93cmFwKHJlcXVlc3QsICdjYWxsYmFjaycsIHRoaXNQbHVnaW4uX3BhdGNoQ2FsbGJhY2tRdWVyeShlbmRTcGFuKSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICB0aGlzUGx1Z2luLl9kaWFnLmVycm9yKCdFeHBlY3RlZCByZXF1ZXN0LmNhbGxiYWNrIHRvIGJlIGEgZnVuY3Rpb24nKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgcmV0dXJuIGFwaS5jb250ZXh0LndpdGgoYXBpLnRyYWNlLnNldFNwYW4oYXBpLmNvbnRleHQuYWN0aXZlKCksIHNwYW4pLCBvcmlnaW5hbE1ldGhvZCwgdGhpcywgLi4uYXJndW1lbnRzKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eShwYXRjaGVkTWV0aG9kLCAnbGVuZ3RoJywge1xuICAgICAgICAgICAgICAgIHZhbHVlOiBvcmlnaW5hbE1ldGhvZC5sZW5ndGgsXG4gICAgICAgICAgICAgICAgd3JpdGFibGU6IGZhbHNlLFxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICByZXR1cm4gcGF0Y2hlZE1ldGhvZDtcbiAgICAgICAgfTtcbiAgICB9XG4gICAgX3BhdGNoQ2FsbGJhY2tRdWVyeShlbmRTcGFuKSB7XG4gICAgICAgIHJldHVybiAob3JpZ2luYWxDYWxsYmFjaykgPT4ge1xuICAgICAgICAgICAgcmV0dXJuIGZ1bmN0aW9uIChlcnIsIHJvd0NvdW50LCByb3dzKSB7XG4gICAgICAgICAgICAgICAgZW5kU3BhbihlcnIpO1xuICAgICAgICAgICAgICAgIHJldHVybiBvcmlnaW5hbENhbGxiYWNrLmFwcGx5KHRoaXMsIGFyZ3VtZW50cyk7XG4gICAgICAgICAgICB9O1xuICAgICAgICB9O1xuICAgIH1cbn1cbmV4cG9ydHMuVGVkaW91c0luc3RydW1lbnRhdGlvbiA9IFRlZGlvdXNJbnN0cnVtZW50YXRpb247XG5UZWRpb3VzSW5zdHJ1bWVudGF0aW9uLkNPTVBPTkVOVCA9ICd0ZWRpb3VzJztcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluc3RydW1lbnRhdGlvbi5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c2855015312fc1744843c907afd3a70c/node_modules/@opentelemetry/instrumentation-tedious/build/src/instrumentation.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c2855015312fc1744843c907afd3a70c/node_modules/@opentelemetry/instrumentation-tedious/build/src/types.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_c2855015312fc1744843c907afd3a70c/node_modules/@opentelemetry/instrumentation-tedious/build/src/types.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9Ab3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfYzI4NTUwMTUzMTJmYzE3NDQ4NDNjOTA3YWZkM2E3MGMvbm9kZV9tb2R1bGVzL0BvcGVudGVsZW1ldHJ5L2luc3RydW1lbnRhdGlvbi10ZWRpb3VzL2J1aWxkL3NyYy90eXBlcy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbndhclxcRG9jdW1lbnRzXFxDdWJlbnR3ZWJcXGN1YmVudFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQG9wZW50ZWxlbWV0cnkraW5zdHJ1bWVudGF0X2MyODU1MDE1MzEyZmMxNzQ0ODQzYzkwN2FmZDNhNzBjXFxub2RlX21vZHVsZXNcXEBvcGVudGVsZW1ldHJ5XFxpbnN0cnVtZW50YXRpb24tdGVkaW91c1xcYnVpbGRcXHNyY1xcdHlwZXMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG4vKlxuICogQ29weXJpZ2h0IFRoZSBPcGVuVGVsZW1ldHJ5IEF1dGhvcnNcbiAqXG4gKiBMaWNlbnNlZCB1bmRlciB0aGUgQXBhY2hlIExpY2Vuc2UsIFZlcnNpb24gMi4wICh0aGUgXCJMaWNlbnNlXCIpO1xuICogeW91IG1heSBub3QgdXNlIHRoaXMgZmlsZSBleGNlcHQgaW4gY29tcGxpYW5jZSB3aXRoIHRoZSBMaWNlbnNlLlxuICogWW91IG1heSBvYnRhaW4gYSBjb3B5IG9mIHRoZSBMaWNlbnNlIGF0XG4gKlxuICogICAgICBodHRwczovL3d3dy5hcGFjaGUub3JnL2xpY2Vuc2VzL0xJQ0VOU0UtMi4wXG4gKlxuICogVW5sZXNzIHJlcXVpcmVkIGJ5IGFwcGxpY2FibGUgbGF3IG9yIGFncmVlZCB0byBpbiB3cml0aW5nLCBzb2Z0d2FyZVxuICogZGlzdHJpYnV0ZWQgdW5kZXIgdGhlIExpY2Vuc2UgaXMgZGlzdHJpYnV0ZWQgb24gYW4gXCJBUyBJU1wiIEJBU0lTLFxuICogV0lUSE9VVCBXQVJSQU5USUVTIE9SIENPTkRJVElPTlMgT0YgQU5ZIEtJTkQsIGVpdGhlciBleHByZXNzIG9yIGltcGxpZWQuXG4gKiBTZWUgdGhlIExpY2Vuc2UgZm9yIHRoZSBzcGVjaWZpYyBsYW5ndWFnZSBnb3Zlcm5pbmcgcGVybWlzc2lvbnMgYW5kXG4gKiBsaW1pdGF0aW9ucyB1bmRlciB0aGUgTGljZW5zZS5cbiAqL1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dHlwZXMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c2855015312fc1744843c907afd3a70c/node_modules/@opentelemetry/instrumentation-tedious/build/src/types.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c2855015312fc1744843c907afd3a70c/node_modules/@opentelemetry/instrumentation-tedious/build/src/utils.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_c2855015312fc1744843c907afd3a70c/node_modules/@opentelemetry/instrumentation-tedious/build/src/utils.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.once = exports.getSpanName = void 0;\n/**\n * The span name SHOULD be set to a low cardinality value\n * representing the statement executed on the database.\n *\n * @returns Operation executed on Tedious Connection. Does not map to SQL statement in any way.\n */\nfunction getSpanName(operation, db, sql, bulkLoadTable) {\n    if (operation === 'execBulkLoad' && bulkLoadTable && db) {\n        return `${operation} ${bulkLoadTable} ${db}`;\n    }\n    if (operation === 'callProcedure') {\n        // `sql` refers to procedure name with `callProcedure`\n        if (db) {\n            return `${operation} ${sql} ${db}`;\n        }\n        return `${operation} ${sql}`;\n    }\n    // do not use `sql` in general case because of high-cardinality\n    if (db) {\n        return `${operation} ${db}`;\n    }\n    return `${operation}`;\n}\nexports.getSpanName = getSpanName;\nconst once = (fn) => {\n    let called = false;\n    return (...args) => {\n        if (called)\n            return;\n        called = true;\n        return fn(...args);\n    };\n};\nexports.once = once;\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c2855015312fc1744843c907afd3a70c/node_modules/@opentelemetry/instrumentation-tedious/build/src/utils.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c2855015312fc1744843c907afd3a70c/node_modules/@opentelemetry/instrumentation-tedious/build/src/version.js":
/*!**********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_c2855015312fc1744843c907afd3a70c/node_modules/@opentelemetry/instrumentation-tedious/build/src/version.js ***!
  \**********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.18.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-tedious';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c2855015312fc1744843c907afd3a70c/node_modules/@opentelemetry/instrumentation-tedious/build/src/version.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c2855015312fc1744843c907afd3a70c/node_modules/@opentelemetry/instrumentation-tedious/build/src/index.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_c2855015312fc1744843c907afd3a70c/node_modules/@opentelemetry/instrumentation-tedious/build/src/index.js ***!
  \********************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c2855015312fc1744843c907afd3a70c/node_modules/@opentelemetry/instrumentation-tedious/build/src/instrumentation.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c2855015312fc1744843c907afd3a70c/node_modules/@opentelemetry/instrumentation-tedious/build/src/types.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c2855015312fc1744843c907afd3a70c/node_modules/@opentelemetry/instrumentation-tedious/build/src/index.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c2855015312fc1744843c907afd3a70c/node_modules/@opentelemetry/instrumentation-tedious/build/src/instrumentation.js":
/*!******************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_c2855015312fc1744843c907afd3a70c/node_modules/@opentelemetry/instrumentation-tedious/build/src/instrumentation.js ***!
  \******************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.TediousInstrumentation = void 0;\nconst api = __webpack_require__(/*! @opentelemetry/api */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst events_1 = __webpack_require__(/*! events */ \"events\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c2855015312fc1744843c907afd3a70c/node_modules/@opentelemetry/instrumentation-tedious/build/src/utils.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c2855015312fc1744843c907afd3a70c/node_modules/@opentelemetry/instrumentation-tedious/build/src/version.js\");\nconst CURRENT_DATABASE = Symbol('opentelemetry.instrumentation-tedious.current-database');\nconst PATCHED_METHODS = [\n    'callProcedure',\n    'execSql',\n    'execSqlBatch',\n    'execBulkLoad',\n    'prepare',\n    'execute',\n];\nfunction setDatabase(databaseName) {\n    Object.defineProperty(this, CURRENT_DATABASE, {\n        value: databaseName,\n        writable: true,\n    });\n}\nclass TediousInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, config);\n    }\n    init() {\n        return [\n            new instrumentation_1.InstrumentationNodeModuleDefinition(TediousInstrumentation.COMPONENT, ['>=1.11.0 <20'], (moduleExports) => {\n                const ConnectionPrototype = moduleExports.Connection.prototype;\n                for (const method of PATCHED_METHODS) {\n                    if ((0, instrumentation_1.isWrapped)(ConnectionPrototype[method])) {\n                        this._unwrap(ConnectionPrototype, method);\n                    }\n                    this._wrap(ConnectionPrototype, method, this._patchQuery(method));\n                }\n                if ((0, instrumentation_1.isWrapped)(ConnectionPrototype.connect)) {\n                    this._unwrap(ConnectionPrototype, 'connect');\n                }\n                this._wrap(ConnectionPrototype, 'connect', this._patchConnect);\n                return moduleExports;\n            }, (moduleExports) => {\n                if (moduleExports === undefined)\n                    return;\n                const ConnectionPrototype = moduleExports.Connection.prototype;\n                for (const method of PATCHED_METHODS) {\n                    this._unwrap(ConnectionPrototype, method);\n                }\n                this._unwrap(ConnectionPrototype, 'connect');\n            }),\n        ];\n    }\n    _patchConnect(original) {\n        return function patchedConnect() {\n            var _a, _b;\n            setDatabase.call(this, (_b = (_a = this.config) === null || _a === void 0 ? void 0 : _a.options) === null || _b === void 0 ? void 0 : _b.database);\n            // remove the listener first in case it's already added\n            this.removeListener('databaseChange', setDatabase);\n            this.on('databaseChange', setDatabase);\n            this.once('end', () => {\n                this.removeListener('databaseChange', setDatabase);\n            });\n            return original.apply(this, arguments);\n        };\n    }\n    _patchQuery(operation) {\n        return (originalMethod) => {\n            const thisPlugin = this;\n            function patchedMethod(request) {\n                var _a, _b, _c, _d, _e, _f, _g, _h;\n                if (!(request instanceof events_1.EventEmitter)) {\n                    thisPlugin._diag.warn(`Unexpected invocation of patched ${operation} method. Span not recorded`);\n                    return originalMethod.apply(this, arguments);\n                }\n                let procCount = 0;\n                let statementCount = 0;\n                const incrementStatementCount = () => statementCount++;\n                const incrementProcCount = () => procCount++;\n                const databaseName = this[CURRENT_DATABASE];\n                const sql = (request => {\n                    var _a, _b;\n                    // Required for <11.0.9\n                    if (request.sqlTextOrProcedure === 'sp_prepare' &&\n                        ((_b = (_a = request.parametersByName) === null || _a === void 0 ? void 0 : _a.stmt) === null || _b === void 0 ? void 0 : _b.value)) {\n                        return request.parametersByName.stmt.value;\n                    }\n                    return request.sqlTextOrProcedure;\n                })(request);\n                const span = thisPlugin.tracer.startSpan((0, utils_1.getSpanName)(operation, databaseName, sql, request.table), {\n                    kind: api.SpanKind.CLIENT,\n                    attributes: {\n                        [semantic_conventions_1.SEMATTRS_DB_SYSTEM]: semantic_conventions_1.DBSYSTEMVALUES_MSSQL,\n                        [semantic_conventions_1.SEMATTRS_DB_NAME]: databaseName,\n                        [semantic_conventions_1.SEMATTRS_NET_PEER_PORT]: (_b = (_a = this.config) === null || _a === void 0 ? void 0 : _a.options) === null || _b === void 0 ? void 0 : _b.port,\n                        [semantic_conventions_1.SEMATTRS_NET_PEER_NAME]: (_c = this.config) === null || _c === void 0 ? void 0 : _c.server,\n                        // >=4 uses `authentication` object, older versions just userName and password pair\n                        [semantic_conventions_1.SEMATTRS_DB_USER]: (_e = (_d = this.config) === null || _d === void 0 ? void 0 : _d.userName) !== null && _e !== void 0 ? _e : (_h = (_g = (_f = this.config) === null || _f === void 0 ? void 0 : _f.authentication) === null || _g === void 0 ? void 0 : _g.options) === null || _h === void 0 ? void 0 : _h.userName,\n                        [semantic_conventions_1.SEMATTRS_DB_STATEMENT]: sql,\n                        [semantic_conventions_1.SEMATTRS_DB_SQL_TABLE]: request.table,\n                    },\n                });\n                const endSpan = (0, utils_1.once)((err) => {\n                    request.removeListener('done', incrementStatementCount);\n                    request.removeListener('doneInProc', incrementStatementCount);\n                    request.removeListener('doneProc', incrementProcCount);\n                    request.removeListener('error', endSpan);\n                    this.removeListener('end', endSpan);\n                    span.setAttribute('tedious.procedure_count', procCount);\n                    span.setAttribute('tedious.statement_count', statementCount);\n                    if (err) {\n                        span.setStatus({\n                            code: api.SpanStatusCode.ERROR,\n                            message: err.message,\n                        });\n                    }\n                    span.end();\n                });\n                request.on('done', incrementStatementCount);\n                request.on('doneInProc', incrementStatementCount);\n                request.on('doneProc', incrementProcCount);\n                request.once('error', endSpan);\n                this.on('end', endSpan);\n                if (typeof request.callback === 'function') {\n                    thisPlugin._wrap(request, 'callback', thisPlugin._patchCallbackQuery(endSpan));\n                }\n                else {\n                    thisPlugin._diag.error('Expected request.callback to be a function');\n                }\n                return api.context.with(api.trace.setSpan(api.context.active(), span), originalMethod, this, ...arguments);\n            }\n            Object.defineProperty(patchedMethod, 'length', {\n                value: originalMethod.length,\n                writable: false,\n            });\n            return patchedMethod;\n        };\n    }\n    _patchCallbackQuery(endSpan) {\n        return (originalCallback) => {\n            return function (err, rowCount, rows) {\n                endSpan(err);\n                return originalCallback.apply(this, arguments);\n            };\n        };\n    }\n}\nexports.TediousInstrumentation = TediousInstrumentation;\nTediousInstrumentation.COMPONENT = 'tedious';\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF9jMjg1NTAxNTMxMmZjMTc0NDg0M2M5MDdhZmQzYTcwYy9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLXRlZGlvdXMvYnVpbGQvc3JjL2luc3RydW1lbnRhdGlvbi5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCw4QkFBOEI7QUFDOUIsWUFBWSxtQkFBTyxDQUFDLHNJQUFvQjtBQUN4QyxpQkFBaUIsbUJBQU8sQ0FBQyxzQkFBUTtBQUNqQywwQkFBMEIsbUJBQU8sQ0FBQyxrTUFBZ0M7QUFDbEUsK0JBQStCLG1CQUFPLENBQUMsMExBQXFDO0FBQzVFLGdCQUFnQixtQkFBTyxDQUFDLG1MQUFTO0FBQ2pDO0FBQ0Esa0JBQWtCLG1CQUFPLENBQUMsdUxBQVc7QUFDckM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLDJCQUEyQjtBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOEVBQThFLFdBQVc7QUFDekY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUI7QUFDckIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlCQUF5QjtBQUN6QjtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhCQUE4QjtBQUM5QjtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFud2FyXFxEb2N1bWVudHNcXEN1YmVudHdlYlxcY3ViZW50XFxub2RlX21vZHVsZXNcXC5wbnBtXFxAb3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfYzI4NTUwMTUzMTJmYzE3NDQ4NDNjOTA3YWZkM2E3MGNcXG5vZGVfbW9kdWxlc1xcQG9wZW50ZWxlbWV0cnlcXGluc3RydW1lbnRhdGlvbi10ZWRpb3VzXFxidWlsZFxcc3JjXFxpbnN0cnVtZW50YXRpb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG4vKlxuICogQ29weXJpZ2h0IFRoZSBPcGVuVGVsZW1ldHJ5IEF1dGhvcnNcbiAqXG4gKiBMaWNlbnNlZCB1bmRlciB0aGUgQXBhY2hlIExpY2Vuc2UsIFZlcnNpb24gMi4wICh0aGUgXCJMaWNlbnNlXCIpO1xuICogeW91IG1heSBub3QgdXNlIHRoaXMgZmlsZSBleGNlcHQgaW4gY29tcGxpYW5jZSB3aXRoIHRoZSBMaWNlbnNlLlxuICogWW91IG1heSBvYnRhaW4gYSBjb3B5IG9mIHRoZSBMaWNlbnNlIGF0XG4gKlxuICogICAgICBodHRwczovL3d3dy5hcGFjaGUub3JnL2xpY2Vuc2VzL0xJQ0VOU0UtMi4wXG4gKlxuICogVW5sZXNzIHJlcXVpcmVkIGJ5IGFwcGxpY2FibGUgbGF3IG9yIGFncmVlZCB0byBpbiB3cml0aW5nLCBzb2Z0d2FyZVxuICogZGlzdHJpYnV0ZWQgdW5kZXIgdGhlIExpY2Vuc2UgaXMgZGlzdHJpYnV0ZWQgb24gYW4gXCJBUyBJU1wiIEJBU0lTLFxuICogV0lUSE9VVCBXQVJSQU5USUVTIE9SIENPTkRJVElPTlMgT0YgQU5ZIEtJTkQsIGVpdGhlciBleHByZXNzIG9yIGltcGxpZWQuXG4gKiBTZWUgdGhlIExpY2Vuc2UgZm9yIHRoZSBzcGVjaWZpYyBsYW5ndWFnZSBnb3Zlcm5pbmcgcGVybWlzc2lvbnMgYW5kXG4gKiBsaW1pdGF0aW9ucyB1bmRlciB0aGUgTGljZW5zZS5cbiAqL1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5UZWRpb3VzSW5zdHJ1bWVudGF0aW9uID0gdm9pZCAwO1xuY29uc3QgYXBpID0gcmVxdWlyZShcIkBvcGVudGVsZW1ldHJ5L2FwaVwiKTtcbmNvbnN0IGV2ZW50c18xID0gcmVxdWlyZShcImV2ZW50c1wiKTtcbmNvbnN0IGluc3RydW1lbnRhdGlvbl8xID0gcmVxdWlyZShcIkBvcGVudGVsZW1ldHJ5L2luc3RydW1lbnRhdGlvblwiKTtcbmNvbnN0IHNlbWFudGljX2NvbnZlbnRpb25zXzEgPSByZXF1aXJlKFwiQG9wZW50ZWxlbWV0cnkvc2VtYW50aWMtY29udmVudGlvbnNcIik7XG5jb25zdCB1dGlsc18xID0gcmVxdWlyZShcIi4vdXRpbHNcIik7XG4vKiogQGtuaXBpZ25vcmUgKi9cbmNvbnN0IHZlcnNpb25fMSA9IHJlcXVpcmUoXCIuL3ZlcnNpb25cIik7XG5jb25zdCBDVVJSRU5UX0RBVEFCQVNFID0gU3ltYm9sKCdvcGVudGVsZW1ldHJ5Lmluc3RydW1lbnRhdGlvbi10ZWRpb3VzLmN1cnJlbnQtZGF0YWJhc2UnKTtcbmNvbnN0IFBBVENIRURfTUVUSE9EUyA9IFtcbiAgICAnY2FsbFByb2NlZHVyZScsXG4gICAgJ2V4ZWNTcWwnLFxuICAgICdleGVjU3FsQmF0Y2gnLFxuICAgICdleGVjQnVsa0xvYWQnLFxuICAgICdwcmVwYXJlJyxcbiAgICAnZXhlY3V0ZScsXG5dO1xuZnVuY3Rpb24gc2V0RGF0YWJhc2UoZGF0YWJhc2VOYW1lKSB7XG4gICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KHRoaXMsIENVUlJFTlRfREFUQUJBU0UsIHtcbiAgICAgICAgdmFsdWU6IGRhdGFiYXNlTmFtZSxcbiAgICAgICAgd3JpdGFibGU6IHRydWUsXG4gICAgfSk7XG59XG5jbGFzcyBUZWRpb3VzSW5zdHJ1bWVudGF0aW9uIGV4dGVuZHMgaW5zdHJ1bWVudGF0aW9uXzEuSW5zdHJ1bWVudGF0aW9uQmFzZSB7XG4gICAgY29uc3RydWN0b3IoY29uZmlnID0ge30pIHtcbiAgICAgICAgc3VwZXIodmVyc2lvbl8xLlBBQ0tBR0VfTkFNRSwgdmVyc2lvbl8xLlBBQ0tBR0VfVkVSU0lPTiwgY29uZmlnKTtcbiAgICB9XG4gICAgaW5pdCgpIHtcbiAgICAgICAgcmV0dXJuIFtcbiAgICAgICAgICAgIG5ldyBpbnN0cnVtZW50YXRpb25fMS5JbnN0cnVtZW50YXRpb25Ob2RlTW9kdWxlRGVmaW5pdGlvbihUZWRpb3VzSW5zdHJ1bWVudGF0aW9uLkNPTVBPTkVOVCwgWyc+PTEuMTEuMCA8MjAnXSwgKG1vZHVsZUV4cG9ydHMpID0+IHtcbiAgICAgICAgICAgICAgICBjb25zdCBDb25uZWN0aW9uUHJvdG90eXBlID0gbW9kdWxlRXhwb3J0cy5Db25uZWN0aW9uLnByb3RvdHlwZTtcbiAgICAgICAgICAgICAgICBmb3IgKGNvbnN0IG1ldGhvZCBvZiBQQVRDSEVEX01FVEhPRFMpIHtcbiAgICAgICAgICAgICAgICAgICAgaWYgKCgwLCBpbnN0cnVtZW50YXRpb25fMS5pc1dyYXBwZWQpKENvbm5lY3Rpb25Qcm90b3R5cGVbbWV0aG9kXSkpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuX3Vud3JhcChDb25uZWN0aW9uUHJvdG90eXBlLCBtZXRob2QpO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuX3dyYXAoQ29ubmVjdGlvblByb3RvdHlwZSwgbWV0aG9kLCB0aGlzLl9wYXRjaFF1ZXJ5KG1ldGhvZCkpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBpZiAoKDAsIGluc3RydW1lbnRhdGlvbl8xLmlzV3JhcHBlZCkoQ29ubmVjdGlvblByb3RvdHlwZS5jb25uZWN0KSkge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLl91bndyYXAoQ29ubmVjdGlvblByb3RvdHlwZSwgJ2Nvbm5lY3QnKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgdGhpcy5fd3JhcChDb25uZWN0aW9uUHJvdG90eXBlLCAnY29ubmVjdCcsIHRoaXMuX3BhdGNoQ29ubmVjdCk7XG4gICAgICAgICAgICAgICAgcmV0dXJuIG1vZHVsZUV4cG9ydHM7XG4gICAgICAgICAgICB9LCAobW9kdWxlRXhwb3J0cykgPT4ge1xuICAgICAgICAgICAgICAgIGlmIChtb2R1bGVFeHBvcnRzID09PSB1bmRlZmluZWQpXG4gICAgICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgICAgICBjb25zdCBDb25uZWN0aW9uUHJvdG90eXBlID0gbW9kdWxlRXhwb3J0cy5Db25uZWN0aW9uLnByb3RvdHlwZTtcbiAgICAgICAgICAgICAgICBmb3IgKGNvbnN0IG1ldGhvZCBvZiBQQVRDSEVEX01FVEhPRFMpIHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5fdW53cmFwKENvbm5lY3Rpb25Qcm90b3R5cGUsIG1ldGhvZCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIHRoaXMuX3Vud3JhcChDb25uZWN0aW9uUHJvdG90eXBlLCAnY29ubmVjdCcpO1xuICAgICAgICAgICAgfSksXG4gICAgICAgIF07XG4gICAgfVxuICAgIF9wYXRjaENvbm5lY3Qob3JpZ2luYWwpIHtcbiAgICAgICAgcmV0dXJuIGZ1bmN0aW9uIHBhdGNoZWRDb25uZWN0KCkge1xuICAgICAgICAgICAgdmFyIF9hLCBfYjtcbiAgICAgICAgICAgIHNldERhdGFiYXNlLmNhbGwodGhpcywgKF9iID0gKF9hID0gdGhpcy5jb25maWcpID09PSBudWxsIHx8IF9hID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYS5vcHRpb25zKSA9PT0gbnVsbCB8fCBfYiA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2IuZGF0YWJhc2UpO1xuICAgICAgICAgICAgLy8gcmVtb3ZlIHRoZSBsaXN0ZW5lciBmaXJzdCBpbiBjYXNlIGl0J3MgYWxyZWFkeSBhZGRlZFxuICAgICAgICAgICAgdGhpcy5yZW1vdmVMaXN0ZW5lcignZGF0YWJhc2VDaGFuZ2UnLCBzZXREYXRhYmFzZSk7XG4gICAgICAgICAgICB0aGlzLm9uKCdkYXRhYmFzZUNoYW5nZScsIHNldERhdGFiYXNlKTtcbiAgICAgICAgICAgIHRoaXMub25jZSgnZW5kJywgKCkgPT4ge1xuICAgICAgICAgICAgICAgIHRoaXMucmVtb3ZlTGlzdGVuZXIoJ2RhdGFiYXNlQ2hhbmdlJywgc2V0RGF0YWJhc2UpO1xuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICByZXR1cm4gb3JpZ2luYWwuYXBwbHkodGhpcywgYXJndW1lbnRzKTtcbiAgICAgICAgfTtcbiAgICB9XG4gICAgX3BhdGNoUXVlcnkob3BlcmF0aW9uKSB7XG4gICAgICAgIHJldHVybiAob3JpZ2luYWxNZXRob2QpID0+IHtcbiAgICAgICAgICAgIGNvbnN0IHRoaXNQbHVnaW4gPSB0aGlzO1xuICAgICAgICAgICAgZnVuY3Rpb24gcGF0Y2hlZE1ldGhvZChyZXF1ZXN0KSB7XG4gICAgICAgICAgICAgICAgdmFyIF9hLCBfYiwgX2MsIF9kLCBfZSwgX2YsIF9nLCBfaDtcbiAgICAgICAgICAgICAgICBpZiAoIShyZXF1ZXN0IGluc3RhbmNlb2YgZXZlbnRzXzEuRXZlbnRFbWl0dGVyKSkge1xuICAgICAgICAgICAgICAgICAgICB0aGlzUGx1Z2luLl9kaWFnLndhcm4oYFVuZXhwZWN0ZWQgaW52b2NhdGlvbiBvZiBwYXRjaGVkICR7b3BlcmF0aW9ufSBtZXRob2QuIFNwYW4gbm90IHJlY29yZGVkYCk7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBvcmlnaW5hbE1ldGhvZC5hcHBseSh0aGlzLCBhcmd1bWVudHMpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBsZXQgcHJvY0NvdW50ID0gMDtcbiAgICAgICAgICAgICAgICBsZXQgc3RhdGVtZW50Q291bnQgPSAwO1xuICAgICAgICAgICAgICAgIGNvbnN0IGluY3JlbWVudFN0YXRlbWVudENvdW50ID0gKCkgPT4gc3RhdGVtZW50Q291bnQrKztcbiAgICAgICAgICAgICAgICBjb25zdCBpbmNyZW1lbnRQcm9jQ291bnQgPSAoKSA9PiBwcm9jQ291bnQrKztcbiAgICAgICAgICAgICAgICBjb25zdCBkYXRhYmFzZU5hbWUgPSB0aGlzW0NVUlJFTlRfREFUQUJBU0VdO1xuICAgICAgICAgICAgICAgIGNvbnN0IHNxbCA9IChyZXF1ZXN0ID0+IHtcbiAgICAgICAgICAgICAgICAgICAgdmFyIF9hLCBfYjtcbiAgICAgICAgICAgICAgICAgICAgLy8gUmVxdWlyZWQgZm9yIDwxMS4wLjlcbiAgICAgICAgICAgICAgICAgICAgaWYgKHJlcXVlc3Quc3FsVGV4dE9yUHJvY2VkdXJlID09PSAnc3BfcHJlcGFyZScgJiZcbiAgICAgICAgICAgICAgICAgICAgICAgICgoX2IgPSAoX2EgPSByZXF1ZXN0LnBhcmFtZXRlcnNCeU5hbWUpID09PSBudWxsIHx8IF9hID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYS5zdG10KSA9PT0gbnVsbCB8fCBfYiA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2IudmFsdWUpKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gcmVxdWVzdC5wYXJhbWV0ZXJzQnlOYW1lLnN0bXQudmFsdWU7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHJlcXVlc3Quc3FsVGV4dE9yUHJvY2VkdXJlO1xuICAgICAgICAgICAgICAgIH0pKHJlcXVlc3QpO1xuICAgICAgICAgICAgICAgIGNvbnN0IHNwYW4gPSB0aGlzUGx1Z2luLnRyYWNlci5zdGFydFNwYW4oKDAsIHV0aWxzXzEuZ2V0U3Bhbk5hbWUpKG9wZXJhdGlvbiwgZGF0YWJhc2VOYW1lLCBzcWwsIHJlcXVlc3QudGFibGUpLCB7XG4gICAgICAgICAgICAgICAgICAgIGtpbmQ6IGFwaS5TcGFuS2luZC5DTElFTlQsXG4gICAgICAgICAgICAgICAgICAgIGF0dHJpYnV0ZXM6IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIFtzZW1hbnRpY19jb252ZW50aW9uc18xLlNFTUFUVFJTX0RCX1NZU1RFTV06IHNlbWFudGljX2NvbnZlbnRpb25zXzEuREJTWVNURU1WQUxVRVNfTVNTUUwsXG4gICAgICAgICAgICAgICAgICAgICAgICBbc2VtYW50aWNfY29udmVudGlvbnNfMS5TRU1BVFRSU19EQl9OQU1FXTogZGF0YWJhc2VOYW1lLFxuICAgICAgICAgICAgICAgICAgICAgICAgW3NlbWFudGljX2NvbnZlbnRpb25zXzEuU0VNQVRUUlNfTkVUX1BFRVJfUE9SVF06IChfYiA9IChfYSA9IHRoaXMuY29uZmlnKSA9PT0gbnVsbCB8fCBfYSA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2Eub3B0aW9ucykgPT09IG51bGwgfHwgX2IgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9iLnBvcnQsXG4gICAgICAgICAgICAgICAgICAgICAgICBbc2VtYW50aWNfY29udmVudGlvbnNfMS5TRU1BVFRSU19ORVRfUEVFUl9OQU1FXTogKF9jID0gdGhpcy5jb25maWcpID09PSBudWxsIHx8IF9jID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYy5zZXJ2ZXIsXG4gICAgICAgICAgICAgICAgICAgICAgICAvLyA+PTQgdXNlcyBgYXV0aGVudGljYXRpb25gIG9iamVjdCwgb2xkZXIgdmVyc2lvbnMganVzdCB1c2VyTmFtZSBhbmQgcGFzc3dvcmQgcGFpclxuICAgICAgICAgICAgICAgICAgICAgICAgW3NlbWFudGljX2NvbnZlbnRpb25zXzEuU0VNQVRUUlNfREJfVVNFUl06IChfZSA9IChfZCA9IHRoaXMuY29uZmlnKSA9PT0gbnVsbCB8fCBfZCA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2QudXNlck5hbWUpICE9PSBudWxsICYmIF9lICE9PSB2b2lkIDAgPyBfZSA6IChfaCA9IChfZyA9IChfZiA9IHRoaXMuY29uZmlnKSA9PT0gbnVsbCB8fCBfZiA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2YuYXV0aGVudGljYXRpb24pID09PSBudWxsIHx8IF9nID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfZy5vcHRpb25zKSA9PT0gbnVsbCB8fCBfaCA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2gudXNlck5hbWUsXG4gICAgICAgICAgICAgICAgICAgICAgICBbc2VtYW50aWNfY29udmVudGlvbnNfMS5TRU1BVFRSU19EQl9TVEFURU1FTlRdOiBzcWwsXG4gICAgICAgICAgICAgICAgICAgICAgICBbc2VtYW50aWNfY29udmVudGlvbnNfMS5TRU1BVFRSU19EQl9TUUxfVEFCTEVdOiByZXF1ZXN0LnRhYmxlLFxuICAgICAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgIGNvbnN0IGVuZFNwYW4gPSAoMCwgdXRpbHNfMS5vbmNlKSgoZXJyKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIHJlcXVlc3QucmVtb3ZlTGlzdGVuZXIoJ2RvbmUnLCBpbmNyZW1lbnRTdGF0ZW1lbnRDb3VudCk7XG4gICAgICAgICAgICAgICAgICAgIHJlcXVlc3QucmVtb3ZlTGlzdGVuZXIoJ2RvbmVJblByb2MnLCBpbmNyZW1lbnRTdGF0ZW1lbnRDb3VudCk7XG4gICAgICAgICAgICAgICAgICAgIHJlcXVlc3QucmVtb3ZlTGlzdGVuZXIoJ2RvbmVQcm9jJywgaW5jcmVtZW50UHJvY0NvdW50KTtcbiAgICAgICAgICAgICAgICAgICAgcmVxdWVzdC5yZW1vdmVMaXN0ZW5lcignZXJyb3InLCBlbmRTcGFuKTtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5yZW1vdmVMaXN0ZW5lcignZW5kJywgZW5kU3Bhbik7XG4gICAgICAgICAgICAgICAgICAgIHNwYW4uc2V0QXR0cmlidXRlKCd0ZWRpb3VzLnByb2NlZHVyZV9jb3VudCcsIHByb2NDb3VudCk7XG4gICAgICAgICAgICAgICAgICAgIHNwYW4uc2V0QXR0cmlidXRlKCd0ZWRpb3VzLnN0YXRlbWVudF9jb3VudCcsIHN0YXRlbWVudENvdW50KTtcbiAgICAgICAgICAgICAgICAgICAgaWYgKGVycikge1xuICAgICAgICAgICAgICAgICAgICAgICAgc3Bhbi5zZXRTdGF0dXMoe1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvZGU6IGFwaS5TcGFuU3RhdHVzQ29kZS5FUlJPUixcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBtZXNzYWdlOiBlcnIubWVzc2FnZSxcbiAgICAgICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIHNwYW4uZW5kKCk7XG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgcmVxdWVzdC5vbignZG9uZScsIGluY3JlbWVudFN0YXRlbWVudENvdW50KTtcbiAgICAgICAgICAgICAgICByZXF1ZXN0Lm9uKCdkb25lSW5Qcm9jJywgaW5jcmVtZW50U3RhdGVtZW50Q291bnQpO1xuICAgICAgICAgICAgICAgIHJlcXVlc3Qub24oJ2RvbmVQcm9jJywgaW5jcmVtZW50UHJvY0NvdW50KTtcbiAgICAgICAgICAgICAgICByZXF1ZXN0Lm9uY2UoJ2Vycm9yJywgZW5kU3Bhbik7XG4gICAgICAgICAgICAgICAgdGhpcy5vbignZW5kJywgZW5kU3Bhbik7XG4gICAgICAgICAgICAgICAgaWYgKHR5cGVvZiByZXF1ZXN0LmNhbGxiYWNrID09PSAnZnVuY3Rpb24nKSB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXNQbHVnaW4uX3dyYXAocmVxdWVzdCwgJ2NhbGxiYWNrJywgdGhpc1BsdWdpbi5fcGF0Y2hDYWxsYmFja1F1ZXJ5KGVuZFNwYW4pKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXNQbHVnaW4uX2RpYWcuZXJyb3IoJ0V4cGVjdGVkIHJlcXVlc3QuY2FsbGJhY2sgdG8gYmUgYSBmdW5jdGlvbicpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICByZXR1cm4gYXBpLmNvbnRleHQud2l0aChhcGkudHJhY2Uuc2V0U3BhbihhcGkuY29udGV4dC5hY3RpdmUoKSwgc3BhbiksIG9yaWdpbmFsTWV0aG9kLCB0aGlzLCAuLi5hcmd1bWVudHMpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KHBhdGNoZWRNZXRob2QsICdsZW5ndGgnLCB7XG4gICAgICAgICAgICAgICAgdmFsdWU6IG9yaWdpbmFsTWV0aG9kLmxlbmd0aCxcbiAgICAgICAgICAgICAgICB3cml0YWJsZTogZmFsc2UsXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIHJldHVybiBwYXRjaGVkTWV0aG9kO1xuICAgICAgICB9O1xuICAgIH1cbiAgICBfcGF0Y2hDYWxsYmFja1F1ZXJ5KGVuZFNwYW4pIHtcbiAgICAgICAgcmV0dXJuIChvcmlnaW5hbENhbGxiYWNrKSA9PiB7XG4gICAgICAgICAgICByZXR1cm4gZnVuY3Rpb24gKGVyciwgcm93Q291bnQsIHJvd3MpIHtcbiAgICAgICAgICAgICAgICBlbmRTcGFuKGVycik7XG4gICAgICAgICAgICAgICAgcmV0dXJuIG9yaWdpbmFsQ2FsbGJhY2suYXBwbHkodGhpcywgYXJndW1lbnRzKTtcbiAgICAgICAgICAgIH07XG4gICAgICAgIH07XG4gICAgfVxufVxuZXhwb3J0cy5UZWRpb3VzSW5zdHJ1bWVudGF0aW9uID0gVGVkaW91c0luc3RydW1lbnRhdGlvbjtcblRlZGlvdXNJbnN0cnVtZW50YXRpb24uQ09NUE9ORU5UID0gJ3RlZGlvdXMnO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5zdHJ1bWVudGF0aW9uLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c2855015312fc1744843c907afd3a70c/node_modules/@opentelemetry/instrumentation-tedious/build/src/instrumentation.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c2855015312fc1744843c907afd3a70c/node_modules/@opentelemetry/instrumentation-tedious/build/src/types.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_c2855015312fc1744843c907afd3a70c/node_modules/@opentelemetry/instrumentation-tedious/build/src/types.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF9jMjg1NTAxNTMxMmZjMTc0NDg0M2M5MDdhZmQzYTcwYy9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLXRlZGlvdXMvYnVpbGQvc3JjL3R5cGVzLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdEIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFud2FyXFxEb2N1bWVudHNcXEN1YmVudHdlYlxcY3ViZW50XFxub2RlX21vZHVsZXNcXC5wbnBtXFxAb3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfYzI4NTUwMTUzMTJmYzE3NDQ4NDNjOTA3YWZkM2E3MGNcXG5vZGVfbW9kdWxlc1xcQG9wZW50ZWxlbWV0cnlcXGluc3RydW1lbnRhdGlvbi10ZWRpb3VzXFxidWlsZFxcc3JjXFx0eXBlcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbi8qXG4gKiBDb3B5cmlnaHQgVGhlIE9wZW5UZWxlbWV0cnkgQXV0aG9yc1xuICpcbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4gKiB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuXG4gKiBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbiAqXG4gKiAgICAgIGh0dHBzOi8vd3d3LmFwYWNoZS5vcmcvbGljZW5zZXMvTElDRU5TRS0yLjBcbiAqXG4gKiBVbmxlc3MgcmVxdWlyZWQgYnkgYXBwbGljYWJsZSBsYXcgb3IgYWdyZWVkIHRvIGluIHdyaXRpbmcsIHNvZnR3YXJlXG4gKiBkaXN0cmlidXRlZCB1bmRlciB0aGUgTGljZW5zZSBpcyBkaXN0cmlidXRlZCBvbiBhbiBcIkFTIElTXCIgQkFTSVMsXG4gKiBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgQ09ORElUSU9OUyBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC5cbiAqIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmRcbiAqIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuICovXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD10eXBlcy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c2855015312fc1744843c907afd3a70c/node_modules/@opentelemetry/instrumentation-tedious/build/src/types.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c2855015312fc1744843c907afd3a70c/node_modules/@opentelemetry/instrumentation-tedious/build/src/utils.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_c2855015312fc1744843c907afd3a70c/node_modules/@opentelemetry/instrumentation-tedious/build/src/utils.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.once = exports.getSpanName = void 0;\n/**\n * The span name SHOULD be set to a low cardinality value\n * representing the statement executed on the database.\n *\n * @returns Operation executed on Tedious Connection. Does not map to SQL statement in any way.\n */\nfunction getSpanName(operation, db, sql, bulkLoadTable) {\n    if (operation === 'execBulkLoad' && bulkLoadTable && db) {\n        return `${operation} ${bulkLoadTable} ${db}`;\n    }\n    if (operation === 'callProcedure') {\n        // `sql` refers to procedure name with `callProcedure`\n        if (db) {\n            return `${operation} ${sql} ${db}`;\n        }\n        return `${operation} ${sql}`;\n    }\n    // do not use `sql` in general case because of high-cardinality\n    if (db) {\n        return `${operation} ${db}`;\n    }\n    return `${operation}`;\n}\nexports.getSpanName = getSpanName;\nconst once = (fn) => {\n    let called = false;\n    return (...args) => {\n        if (called)\n            return;\n        called = true;\n        return fn(...args);\n    };\n};\nexports.once = once;\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c2855015312fc1744843c907afd3a70c/node_modules/@opentelemetry/instrumentation-tedious/build/src/utils.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c2855015312fc1744843c907afd3a70c/node_modules/@opentelemetry/instrumentation-tedious/build/src/version.js":
/*!**********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_c2855015312fc1744843c907afd3a70c/node_modules/@opentelemetry/instrumentation-tedious/build/src/version.js ***!
  \**********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.18.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-tedious';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c2855015312fc1744843c907afd3a70c/node_modules/@opentelemetry/instrumentation-tedious/build/src/version.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c2855015312fc1744843c907afd3a70c/node_modules/@opentelemetry/instrumentation-tedious/build/src/index.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_c2855015312fc1744843c907afd3a70c/node_modules/@opentelemetry/instrumentation-tedious/build/src/index.js ***!
  \********************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c2855015312fc1744843c907afd3a70c/node_modules/@opentelemetry/instrumentation-tedious/build/src/instrumentation.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c2855015312fc1744843c907afd3a70c/node_modules/@opentelemetry/instrumentation-tedious/build/src/types.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c2855015312fc1744843c907afd3a70c/node_modules/@opentelemetry/instrumentation-tedious/build/src/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c2855015312fc1744843c907afd3a70c/node_modules/@opentelemetry/instrumentation-tedious/build/src/instrumentation.js":
/*!******************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_c2855015312fc1744843c907afd3a70c/node_modules/@opentelemetry/instrumentation-tedious/build/src/instrumentation.js ***!
  \******************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.TediousInstrumentation = void 0;\nconst api = __webpack_require__(/*! @opentelemetry/api */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst events_1 = __webpack_require__(/*! events */ \"events\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c2855015312fc1744843c907afd3a70c/node_modules/@opentelemetry/instrumentation-tedious/build/src/utils.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c2855015312fc1744843c907afd3a70c/node_modules/@opentelemetry/instrumentation-tedious/build/src/version.js\");\nconst CURRENT_DATABASE = Symbol('opentelemetry.instrumentation-tedious.current-database');\nconst PATCHED_METHODS = [\n    'callProcedure',\n    'execSql',\n    'execSqlBatch',\n    'execBulkLoad',\n    'prepare',\n    'execute',\n];\nfunction setDatabase(databaseName) {\n    Object.defineProperty(this, CURRENT_DATABASE, {\n        value: databaseName,\n        writable: true,\n    });\n}\nclass TediousInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, config);\n    }\n    init() {\n        return [\n            new instrumentation_1.InstrumentationNodeModuleDefinition(TediousInstrumentation.COMPONENT, ['>=1.11.0 <20'], (moduleExports) => {\n                const ConnectionPrototype = moduleExports.Connection.prototype;\n                for (const method of PATCHED_METHODS) {\n                    if ((0, instrumentation_1.isWrapped)(ConnectionPrototype[method])) {\n                        this._unwrap(ConnectionPrototype, method);\n                    }\n                    this._wrap(ConnectionPrototype, method, this._patchQuery(method));\n                }\n                if ((0, instrumentation_1.isWrapped)(ConnectionPrototype.connect)) {\n                    this._unwrap(ConnectionPrototype, 'connect');\n                }\n                this._wrap(ConnectionPrototype, 'connect', this._patchConnect);\n                return moduleExports;\n            }, (moduleExports) => {\n                if (moduleExports === undefined)\n                    return;\n                const ConnectionPrototype = moduleExports.Connection.prototype;\n                for (const method of PATCHED_METHODS) {\n                    this._unwrap(ConnectionPrototype, method);\n                }\n                this._unwrap(ConnectionPrototype, 'connect');\n            }),\n        ];\n    }\n    _patchConnect(original) {\n        return function patchedConnect() {\n            var _a, _b;\n            setDatabase.call(this, (_b = (_a = this.config) === null || _a === void 0 ? void 0 : _a.options) === null || _b === void 0 ? void 0 : _b.database);\n            // remove the listener first in case it's already added\n            this.removeListener('databaseChange', setDatabase);\n            this.on('databaseChange', setDatabase);\n            this.once('end', () => {\n                this.removeListener('databaseChange', setDatabase);\n            });\n            return original.apply(this, arguments);\n        };\n    }\n    _patchQuery(operation) {\n        return (originalMethod) => {\n            const thisPlugin = this;\n            function patchedMethod(request) {\n                var _a, _b, _c, _d, _e, _f, _g, _h;\n                if (!(request instanceof events_1.EventEmitter)) {\n                    thisPlugin._diag.warn(`Unexpected invocation of patched ${operation} method. Span not recorded`);\n                    return originalMethod.apply(this, arguments);\n                }\n                let procCount = 0;\n                let statementCount = 0;\n                const incrementStatementCount = () => statementCount++;\n                const incrementProcCount = () => procCount++;\n                const databaseName = this[CURRENT_DATABASE];\n                const sql = (request => {\n                    var _a, _b;\n                    // Required for <11.0.9\n                    if (request.sqlTextOrProcedure === 'sp_prepare' &&\n                        ((_b = (_a = request.parametersByName) === null || _a === void 0 ? void 0 : _a.stmt) === null || _b === void 0 ? void 0 : _b.value)) {\n                        return request.parametersByName.stmt.value;\n                    }\n                    return request.sqlTextOrProcedure;\n                })(request);\n                const span = thisPlugin.tracer.startSpan((0, utils_1.getSpanName)(operation, databaseName, sql, request.table), {\n                    kind: api.SpanKind.CLIENT,\n                    attributes: {\n                        [semantic_conventions_1.SEMATTRS_DB_SYSTEM]: semantic_conventions_1.DBSYSTEMVALUES_MSSQL,\n                        [semantic_conventions_1.SEMATTRS_DB_NAME]: databaseName,\n                        [semantic_conventions_1.SEMATTRS_NET_PEER_PORT]: (_b = (_a = this.config) === null || _a === void 0 ? void 0 : _a.options) === null || _b === void 0 ? void 0 : _b.port,\n                        [semantic_conventions_1.SEMATTRS_NET_PEER_NAME]: (_c = this.config) === null || _c === void 0 ? void 0 : _c.server,\n                        // >=4 uses `authentication` object, older versions just userName and password pair\n                        [semantic_conventions_1.SEMATTRS_DB_USER]: (_e = (_d = this.config) === null || _d === void 0 ? void 0 : _d.userName) !== null && _e !== void 0 ? _e : (_h = (_g = (_f = this.config) === null || _f === void 0 ? void 0 : _f.authentication) === null || _g === void 0 ? void 0 : _g.options) === null || _h === void 0 ? void 0 : _h.userName,\n                        [semantic_conventions_1.SEMATTRS_DB_STATEMENT]: sql,\n                        [semantic_conventions_1.SEMATTRS_DB_SQL_TABLE]: request.table,\n                    },\n                });\n                const endSpan = (0, utils_1.once)((err) => {\n                    request.removeListener('done', incrementStatementCount);\n                    request.removeListener('doneInProc', incrementStatementCount);\n                    request.removeListener('doneProc', incrementProcCount);\n                    request.removeListener('error', endSpan);\n                    this.removeListener('end', endSpan);\n                    span.setAttribute('tedious.procedure_count', procCount);\n                    span.setAttribute('tedious.statement_count', statementCount);\n                    if (err) {\n                        span.setStatus({\n                            code: api.SpanStatusCode.ERROR,\n                            message: err.message,\n                        });\n                    }\n                    span.end();\n                });\n                request.on('done', incrementStatementCount);\n                request.on('doneInProc', incrementStatementCount);\n                request.on('doneProc', incrementProcCount);\n                request.once('error', endSpan);\n                this.on('end', endSpan);\n                if (typeof request.callback === 'function') {\n                    thisPlugin._wrap(request, 'callback', thisPlugin._patchCallbackQuery(endSpan));\n                }\n                else {\n                    thisPlugin._diag.error('Expected request.callback to be a function');\n                }\n                return api.context.with(api.trace.setSpan(api.context.active(), span), originalMethod, this, ...arguments);\n            }\n            Object.defineProperty(patchedMethod, 'length', {\n                value: originalMethod.length,\n                writable: false,\n            });\n            return patchedMethod;\n        };\n    }\n    _patchCallbackQuery(endSpan) {\n        return (originalCallback) => {\n            return function (err, rowCount, rows) {\n                endSpan(err);\n                return originalCallback.apply(this, arguments);\n            };\n        };\n    }\n}\nexports.TediousInstrumentation = TediousInstrumentation;\nTediousInstrumentation.COMPONENT = 'tedious';\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c2855015312fc1744843c907afd3a70c/node_modules/@opentelemetry/instrumentation-tedious/build/src/instrumentation.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c2855015312fc1744843c907afd3a70c/node_modules/@opentelemetry/instrumentation-tedious/build/src/types.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_c2855015312fc1744843c907afd3a70c/node_modules/@opentelemetry/instrumentation-tedious/build/src/types.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF9jMjg1NTAxNTMxMmZjMTc0NDg0M2M5MDdhZmQzYTcwYy9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLXRlZGlvdXMvYnVpbGQvc3JjL3R5cGVzLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdEIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFud2FyXFxEb2N1bWVudHNcXEN1YmVudHdlYlxcY3ViZW50XFxub2RlX21vZHVsZXNcXC5wbnBtXFxAb3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfYzI4NTUwMTUzMTJmYzE3NDQ4NDNjOTA3YWZkM2E3MGNcXG5vZGVfbW9kdWxlc1xcQG9wZW50ZWxlbWV0cnlcXGluc3RydW1lbnRhdGlvbi10ZWRpb3VzXFxidWlsZFxcc3JjXFx0eXBlcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbi8qXG4gKiBDb3B5cmlnaHQgVGhlIE9wZW5UZWxlbWV0cnkgQXV0aG9yc1xuICpcbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4gKiB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuXG4gKiBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbiAqXG4gKiAgICAgIGh0dHBzOi8vd3d3LmFwYWNoZS5vcmcvbGljZW5zZXMvTElDRU5TRS0yLjBcbiAqXG4gKiBVbmxlc3MgcmVxdWlyZWQgYnkgYXBwbGljYWJsZSBsYXcgb3IgYWdyZWVkIHRvIGluIHdyaXRpbmcsIHNvZnR3YXJlXG4gKiBkaXN0cmlidXRlZCB1bmRlciB0aGUgTGljZW5zZSBpcyBkaXN0cmlidXRlZCBvbiBhbiBcIkFTIElTXCIgQkFTSVMsXG4gKiBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgQ09ORElUSU9OUyBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC5cbiAqIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmRcbiAqIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuICovXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD10eXBlcy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c2855015312fc1744843c907afd3a70c/node_modules/@opentelemetry/instrumentation-tedious/build/src/types.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c2855015312fc1744843c907afd3a70c/node_modules/@opentelemetry/instrumentation-tedious/build/src/utils.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_c2855015312fc1744843c907afd3a70c/node_modules/@opentelemetry/instrumentation-tedious/build/src/utils.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.once = exports.getSpanName = void 0;\n/**\n * The span name SHOULD be set to a low cardinality value\n * representing the statement executed on the database.\n *\n * @returns Operation executed on Tedious Connection. Does not map to SQL statement in any way.\n */\nfunction getSpanName(operation, db, sql, bulkLoadTable) {\n    if (operation === 'execBulkLoad' && bulkLoadTable && db) {\n        return `${operation} ${bulkLoadTable} ${db}`;\n    }\n    if (operation === 'callProcedure') {\n        // `sql` refers to procedure name with `callProcedure`\n        if (db) {\n            return `${operation} ${sql} ${db}`;\n        }\n        return `${operation} ${sql}`;\n    }\n    // do not use `sql` in general case because of high-cardinality\n    if (db) {\n        return `${operation} ${db}`;\n    }\n    return `${operation}`;\n}\nexports.getSpanName = getSpanName;\nconst once = (fn) => {\n    let called = false;\n    return (...args) => {\n        if (called)\n            return;\n        called = true;\n        return fn(...args);\n    };\n};\nexports.once = once;\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c2855015312fc1744843c907afd3a70c/node_modules/@opentelemetry/instrumentation-tedious/build/src/utils.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c2855015312fc1744843c907afd3a70c/node_modules/@opentelemetry/instrumentation-tedious/build/src/version.js":
/*!**********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_c2855015312fc1744843c907afd3a70c/node_modules/@opentelemetry/instrumentation-tedious/build/src/version.js ***!
  \**********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.18.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-tedious';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c2855015312fc1744843c907afd3a70c/node_modules/@opentelemetry/instrumentation-tedious/build/src/version.js\n");

/***/ })

};
;