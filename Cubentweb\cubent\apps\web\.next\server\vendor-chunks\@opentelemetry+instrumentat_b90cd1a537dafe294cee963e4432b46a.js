"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a";
exports.ids = ["vendor-chunks/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a"];
exports.modules = {

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/enums/AttributeNames.js":
/*!******************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/enums/AttributeNames.js ***!
  \******************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.AttributeNames = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// Postgresql specific attributes not covered by semantic conventions\nvar AttributeNames;\n(function (AttributeNames) {\n    AttributeNames[\"PG_VALUES\"] = \"db.postgresql.values\";\n    AttributeNames[\"PG_PLAN\"] = \"db.postgresql.plan\";\n    AttributeNames[\"IDLE_TIMEOUT_MILLIS\"] = \"db.postgresql.idle.timeout.millis\";\n    AttributeNames[\"MAX_CLIENT\"] = \"db.postgresql.max.client\";\n})(AttributeNames = exports.AttributeNames || (exports.AttributeNames = {}));\n//# sourceMappingURL=AttributeNames.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/enums/AttributeNames.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/enums/SpanNames.js":
/*!*************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/enums/SpanNames.js ***!
  \*************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.SpanNames = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// Contains span names produced by instrumentation\nvar SpanNames;\n(function (SpanNames) {\n    SpanNames[\"QUERY_PREFIX\"] = \"pg.query\";\n    SpanNames[\"CONNECT\"] = \"pg.connect\";\n    SpanNames[\"POOL_CONNECT\"] = \"pg-pool.connect\";\n})(SpanNames = exports.SpanNames || (exports.SpanNames = {}));\n//# sourceMappingURL=SpanNames.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/enums/SpanNames.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/index.js":
/*!***************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/index.js ***!
  \***************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/instrumentation.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/types.js\"), exports);\n__exportStar(__webpack_require__(/*! ./enums/AttributeNames */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/enums/AttributeNames.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/index.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/instrumentation.js":
/*!*************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/instrumentation.js ***!
  \*************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PgInstrumentation = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst internal_types_1 = __webpack_require__(/*! ./internal-types */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/internal-types.js\");\nconst utils = __webpack_require__(/*! ./utils */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/utils.js\");\nconst sql_common_1 = __webpack_require__(/*! @opentelemetry/sql-common */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+sql-common@0.40.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/sql-common/build/src/index.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/version.js\");\nconst SpanNames_1 = __webpack_require__(/*! ./enums/SpanNames */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/enums/SpanNames.js\");\nconst core_1 = __webpack_require__(/*! @opentelemetry/core */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+core@1.30.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/core/build/esm/index.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst semconv_1 = __webpack_require__(/*! ./semconv */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/semconv.js\");\nfunction extractModuleExports(module) {\n    return module[Symbol.toStringTag] === 'Module'\n        ? module.default // ESM\n        : module; // CommonJS\n}\nclass PgInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, config);\n        // Pool events connect, acquire, release and remove can be called\n        // multiple times without changing the values of total, idle and waiting\n        // connections. The _connectionsCounter is used to keep track of latest\n        // values and only update the metrics _connectionsCount and _connectionPendingRequests\n        // when the value change.\n        this._connectionsCounter = {\n            used: 0,\n            idle: 0,\n            pending: 0,\n        };\n    }\n    _updateMetricInstruments() {\n        this._operationDuration = this.meter.createHistogram(semconv_1.METRIC_DB_CLIENT_OPERATION_DURATION, {\n            description: 'Duration of database client operations.',\n            unit: 's',\n            valueType: api_1.ValueType.DOUBLE,\n            advice: {\n                explicitBucketBoundaries: [\n                    0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1, 5, 10,\n                ],\n            },\n        });\n        this._connectionsCounter = {\n            idle: 0,\n            pending: 0,\n            used: 0,\n        };\n        this._connectionsCount = this.meter.createUpDownCounter(semconv_1.METRIC_DB_CLIENT_CONNECTION_COUNT, {\n            description: 'The number of connections that are currently in state described by the state attribute.',\n            unit: '{connection}',\n        });\n        this._connectionPendingRequests = this.meter.createUpDownCounter(semconv_1.METRIC_DB_CLIENT_CONNECTION_PENDING_REQUESTS, {\n            description: 'The number of current pending requests for an open connection.',\n            unit: '{connection}',\n        });\n    }\n    init() {\n        const SUPPORTED_PG_VERSIONS = ['>=8.0.3 <9'];\n        const modulePgNativeClient = new instrumentation_1.InstrumentationNodeModuleFile('pg/lib/native/client.js', SUPPORTED_PG_VERSIONS, this._patchPgClient.bind(this), this._unpatchPgClient.bind(this));\n        const modulePgClient = new instrumentation_1.InstrumentationNodeModuleFile('pg/lib/client.js', SUPPORTED_PG_VERSIONS, this._patchPgClient.bind(this), this._unpatchPgClient.bind(this));\n        const modulePG = new instrumentation_1.InstrumentationNodeModuleDefinition('pg', SUPPORTED_PG_VERSIONS, (module) => {\n            const moduleExports = extractModuleExports(module);\n            this._patchPgClient(moduleExports.Client);\n            return module;\n        }, (module) => {\n            const moduleExports = extractModuleExports(module);\n            this._unpatchPgClient(moduleExports.Client);\n            return module;\n        }, [modulePgClient, modulePgNativeClient]);\n        const modulePGPool = new instrumentation_1.InstrumentationNodeModuleDefinition('pg-pool', ['>=2.0.0 <4'], (moduleExports) => {\n            if ((0, instrumentation_1.isWrapped)(moduleExports.prototype.connect)) {\n                this._unwrap(moduleExports.prototype, 'connect');\n            }\n            this._wrap(moduleExports.prototype, 'connect', this._getPoolConnectPatch());\n            return moduleExports;\n        }, (moduleExports) => {\n            if ((0, instrumentation_1.isWrapped)(moduleExports.prototype.connect)) {\n                this._unwrap(moduleExports.prototype, 'connect');\n            }\n        });\n        return [modulePG, modulePGPool];\n    }\n    _patchPgClient(module) {\n        if (!module) {\n            return;\n        }\n        const moduleExports = extractModuleExports(module);\n        if ((0, instrumentation_1.isWrapped)(moduleExports.prototype.query)) {\n            this._unwrap(moduleExports.prototype, 'query');\n        }\n        if ((0, instrumentation_1.isWrapped)(moduleExports.prototype.connect)) {\n            this._unwrap(moduleExports.prototype, 'connect');\n        }\n        this._wrap(moduleExports.prototype, 'query', this._getClientQueryPatch());\n        this._wrap(moduleExports.prototype, 'connect', this._getClientConnectPatch());\n        return module;\n    }\n    _unpatchPgClient(module) {\n        const moduleExports = extractModuleExports(module);\n        if ((0, instrumentation_1.isWrapped)(moduleExports.prototype.query)) {\n            this._unwrap(moduleExports.prototype, 'query');\n        }\n        if ((0, instrumentation_1.isWrapped)(moduleExports.prototype.connect)) {\n            this._unwrap(moduleExports.prototype, 'connect');\n        }\n        return module;\n    }\n    _getClientConnectPatch() {\n        const plugin = this;\n        return (original) => {\n            return function connect(callback) {\n                if (utils.shouldSkipInstrumentation(plugin.getConfig())) {\n                    return original.call(this, callback);\n                }\n                const span = plugin.tracer.startSpan(SpanNames_1.SpanNames.CONNECT, {\n                    kind: api_1.SpanKind.CLIENT,\n                    attributes: utils.getSemanticAttributesFromConnection(this),\n                });\n                if (callback) {\n                    const parentSpan = api_1.trace.getSpan(api_1.context.active());\n                    callback = utils.patchClientConnectCallback(span, callback);\n                    if (parentSpan) {\n                        callback = api_1.context.bind(api_1.context.active(), callback);\n                    }\n                }\n                const connectResult = api_1.context.with(api_1.trace.setSpan(api_1.context.active(), span), () => {\n                    return original.call(this, callback);\n                });\n                return handleConnectResult(span, connectResult);\n            };\n        };\n    }\n    recordOperationDuration(attributes, startTime) {\n        const metricsAttributes = {};\n        const keysToCopy = [\n            semantic_conventions_1.SEMATTRS_DB_SYSTEM,\n            semconv_1.ATTR_DB_NAMESPACE,\n            semantic_conventions_1.ATTR_ERROR_TYPE,\n            semantic_conventions_1.ATTR_SERVER_PORT,\n            semantic_conventions_1.ATTR_SERVER_ADDRESS,\n            semconv_1.ATTR_DB_OPERATION_NAME,\n        ];\n        keysToCopy.forEach(key => {\n            if (key in attributes) {\n                metricsAttributes[key] = attributes[key];\n            }\n        });\n        const durationSeconds = (0, core_1.hrTimeToMilliseconds)((0, core_1.hrTimeDuration)(startTime, (0, core_1.hrTime)())) / 1000;\n        this._operationDuration.record(durationSeconds, metricsAttributes);\n    }\n    _getClientQueryPatch() {\n        const plugin = this;\n        return (original) => {\n            this._diag.debug('Patching pg.Client.prototype.query');\n            return function query(...args) {\n                if (utils.shouldSkipInstrumentation(plugin.getConfig())) {\n                    return original.apply(this, args);\n                }\n                const startTime = (0, core_1.hrTime)();\n                // client.query(text, cb?), client.query(text, values, cb?), and\n                // client.query(configObj, cb?) are all valid signatures. We construct\n                // a queryConfig obj from all (valid) signatures to build the span in a\n                // unified way. We verify that we at least have query text, and code\n                // defensively when dealing with `queryConfig` after that (to handle all\n                // the other invalid cases, like a non-array for values being provided).\n                // The type casts here reflect only what we've actually validated.\n                const arg0 = args[0];\n                const firstArgIsString = typeof arg0 === 'string';\n                const firstArgIsQueryObjectWithText = utils.isObjectWithTextString(arg0);\n                // TODO: remove the `as ...` casts below when the TS version is upgraded.\n                // Newer TS versions will use the result of firstArgIsQueryObjectWithText\n                // to properly narrow arg0, but TS 4.3.5 does not.\n                const queryConfig = firstArgIsString\n                    ? {\n                        text: arg0,\n                        values: Array.isArray(args[1]) ? args[1] : undefined,\n                    }\n                    : firstArgIsQueryObjectWithText\n                        ? arg0\n                        : undefined;\n                const attributes = {\n                    [semantic_conventions_1.SEMATTRS_DB_SYSTEM]: semantic_conventions_1.DBSYSTEMVALUES_POSTGRESQL,\n                    [semconv_1.ATTR_DB_NAMESPACE]: this.database,\n                    [semantic_conventions_1.ATTR_SERVER_PORT]: this.connectionParameters.port,\n                    [semantic_conventions_1.ATTR_SERVER_ADDRESS]: this.connectionParameters.host,\n                };\n                if (queryConfig === null || queryConfig === void 0 ? void 0 : queryConfig.text) {\n                    attributes[semconv_1.ATTR_DB_OPERATION_NAME] =\n                        utils.parseNormalizedOperationName(queryConfig === null || queryConfig === void 0 ? void 0 : queryConfig.text);\n                }\n                const recordDuration = () => {\n                    plugin.recordOperationDuration(attributes, startTime);\n                };\n                const instrumentationConfig = plugin.getConfig();\n                const span = utils.handleConfigQuery.call(this, plugin.tracer, instrumentationConfig, queryConfig);\n                // Modify query text w/ a tracing comment before invoking original for\n                // tracing, but only if args[0] has one of our expected shapes.\n                if (instrumentationConfig.addSqlCommenterCommentToQueries) {\n                    if (firstArgIsString) {\n                        args[0] = (0, sql_common_1.addSqlCommenterComment)(span, arg0);\n                    }\n                    else if (firstArgIsQueryObjectWithText && !('name' in arg0)) {\n                        // In the case of a query object, we need to ensure there's no name field\n                        // as this indicates a prepared query, where the comment would remain the same\n                        // for every invocation and contain an outdated trace context.\n                        args[0] = Object.assign(Object.assign({}, arg0), { text: (0, sql_common_1.addSqlCommenterComment)(span, arg0.text) });\n                    }\n                }\n                // Bind callback (if any) to parent span (if any)\n                if (args.length > 0) {\n                    const parentSpan = api_1.trace.getSpan(api_1.context.active());\n                    if (typeof args[args.length - 1] === 'function') {\n                        // Patch ParameterQuery callback\n                        args[args.length - 1] = utils.patchCallback(instrumentationConfig, span, args[args.length - 1], // nb: not type safe.\n                        attributes, recordDuration);\n                        // If a parent span exists, bind the callback\n                        if (parentSpan) {\n                            args[args.length - 1] = api_1.context.bind(api_1.context.active(), args[args.length - 1]);\n                        }\n                    }\n                    else if (typeof (queryConfig === null || queryConfig === void 0 ? void 0 : queryConfig.callback) === 'function') {\n                        // Patch ConfigQuery callback\n                        let callback = utils.patchCallback(plugin.getConfig(), span, queryConfig.callback, // nb: not type safe.\n                        attributes, recordDuration);\n                        // If a parent span existed, bind the callback\n                        if (parentSpan) {\n                            callback = api_1.context.bind(api_1.context.active(), callback);\n                        }\n                        args[0].callback = callback;\n                    }\n                }\n                const { requestHook } = instrumentationConfig;\n                if (typeof requestHook === 'function' && queryConfig) {\n                    (0, instrumentation_1.safeExecuteInTheMiddle)(() => {\n                        // pick keys to expose explicitly, so we're not leaking pg package\n                        // internals that are subject to change\n                        const { database, host, port, user } = this.connectionParameters;\n                        const connection = { database, host, port, user };\n                        requestHook(span, {\n                            connection,\n                            query: {\n                                text: queryConfig.text,\n                                // nb: if `client.query` is called with illegal arguments\n                                // (e.g., if `queryConfig.values` is passed explicitly, but a\n                                // non-array is given), then the type casts will be wrong. But\n                                // we leave it up to the queryHook to handle that, and we\n                                // catch and swallow any errors it throws. The other options\n                                // are all worse. E.g., we could leave `queryConfig.values`\n                                // and `queryConfig.name` as `unknown`, but then the hook body\n                                // would be forced to validate (or cast) them before using\n                                // them, which seems incredibly cumbersome given that these\n                                // casts will be correct 99.9% of the time -- and pg.query\n                                // will immediately throw during development in the other .1%\n                                // of cases. Alternatively, we could simply skip calling the\n                                // hook when `values` or `name` don't have the expected type,\n                                // but that would add unnecessary validation overhead to every\n                                // hook invocation and possibly be even more confusing/unexpected.\n                                values: queryConfig.values,\n                                name: queryConfig.name,\n                            },\n                        });\n                    }, err => {\n                        if (err) {\n                            plugin._diag.error('Error running query hook', err);\n                        }\n                    }, true);\n                }\n                let result;\n                try {\n                    result = original.apply(this, args);\n                }\n                catch (e) {\n                    span.setStatus({\n                        code: api_1.SpanStatusCode.ERROR,\n                        message: utils.getErrorMessage(e),\n                    });\n                    span.end();\n                    throw e;\n                }\n                // Bind promise to parent span and end the span\n                if (result instanceof Promise) {\n                    return result\n                        .then((result) => {\n                        // Return a pass-along promise which ends the span and then goes to user's orig resolvers\n                        return new Promise(resolve => {\n                            utils.handleExecutionResult(plugin.getConfig(), span, result);\n                            recordDuration();\n                            span.end();\n                            resolve(result);\n                        });\n                    })\n                        .catch((error) => {\n                        return new Promise((_, reject) => {\n                            span.setStatus({\n                                code: api_1.SpanStatusCode.ERROR,\n                                message: error.message,\n                            });\n                            recordDuration();\n                            span.end();\n                            reject(error);\n                        });\n                    });\n                }\n                // else returns void\n                return result; // void\n            };\n        };\n    }\n    _setPoolConnectEventListeners(pgPool) {\n        if (pgPool[internal_types_1.EVENT_LISTENERS_SET])\n            return;\n        const poolName = utils.getPoolName(pgPool.options);\n        pgPool.on('connect', () => {\n            this._connectionsCounter = utils.updateCounter(poolName, pgPool, this._connectionsCount, this._connectionPendingRequests, this._connectionsCounter);\n        });\n        pgPool.on('acquire', () => {\n            this._connectionsCounter = utils.updateCounter(poolName, pgPool, this._connectionsCount, this._connectionPendingRequests, this._connectionsCounter);\n        });\n        pgPool.on('remove', () => {\n            this._connectionsCounter = utils.updateCounter(poolName, pgPool, this._connectionsCount, this._connectionPendingRequests, this._connectionsCounter);\n        });\n        pgPool.on('release', () => {\n            this._connectionsCounter = utils.updateCounter(poolName, pgPool, this._connectionsCount, this._connectionPendingRequests, this._connectionsCounter);\n        });\n        pgPool[internal_types_1.EVENT_LISTENERS_SET] = true;\n    }\n    _getPoolConnectPatch() {\n        const plugin = this;\n        return (originalConnect) => {\n            return function connect(callback) {\n                if (utils.shouldSkipInstrumentation(plugin.getConfig())) {\n                    return originalConnect.call(this, callback);\n                }\n                // setup span\n                const span = plugin.tracer.startSpan(SpanNames_1.SpanNames.POOL_CONNECT, {\n                    kind: api_1.SpanKind.CLIENT,\n                    attributes: utils.getSemanticAttributesFromPool(this.options),\n                });\n                plugin._setPoolConnectEventListeners(this);\n                if (callback) {\n                    const parentSpan = api_1.trace.getSpan(api_1.context.active());\n                    callback = utils.patchCallbackPGPool(span, callback);\n                    // If a parent span exists, bind the callback\n                    if (parentSpan) {\n                        callback = api_1.context.bind(api_1.context.active(), callback);\n                    }\n                }\n                const connectResult = api_1.context.with(api_1.trace.setSpan(api_1.context.active(), span), () => {\n                    return originalConnect.call(this, callback);\n                });\n                return handleConnectResult(span, connectResult);\n            };\n        };\n    }\n}\nexports.PgInstrumentation = PgInstrumentation;\nfunction handleConnectResult(span, connectResult) {\n    if (!(connectResult instanceof Promise)) {\n        return connectResult;\n    }\n    const connectResultPromise = connectResult;\n    return api_1.context.bind(api_1.context.active(), connectResultPromise\n        .then(result => {\n        span.end();\n        return result;\n    })\n        .catch((error) => {\n        span.setStatus({\n            code: api_1.SpanStatusCode.ERROR,\n            message: utils.getErrorMessage(error),\n        });\n        span.end();\n        return Promise.reject(error);\n    }));\n}\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/instrumentation.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/internal-types.js":
/*!************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/internal-types.js ***!
  \************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.EVENT_LISTENERS_SET = void 0;\nexports.EVENT_LISTENERS_SET = Symbol('opentelemetry.instrumentation.pg.eventListenersSet');\n//# sourceMappingURL=internal-types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/internal-types.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/semconv.js":
/*!*****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/semconv.js ***!
  \*****************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.METRIC_DB_CLIENT_OPERATION_DURATION = exports.METRIC_DB_CLIENT_CONNECTION_PENDING_REQUESTS = exports.METRIC_DB_CLIENT_CONNECTION_COUNT = exports.DB_CLIENT_CONNECTION_STATE_VALUE_IDLE = exports.DB_CLIENT_CONNECTION_STATE_VALUE_USED = exports.ATTR_DB_OPERATION_NAME = exports.ATTR_DB_NAMESPACE = exports.ATTR_DB_CLIENT_CONNECTION_STATE = exports.ATTR_DB_CLIENT_CONNECTION_POOL_NAME = void 0;\n/**\n * The name of the connection pool; unique within the instrumented application. In case the connection pool implementation doesn't provide a name, instrumentation **SHOULD** use a combination of parameters that would make the name unique, for example, combining attributes `server.address`, `server.port`, and `db.namespace`, formatted as `server.address:server.port/db.namespace`. Instrumentations that generate connection pool name following different patterns **SHOULD** document it.\n *\n * @example myDataSource\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexports.ATTR_DB_CLIENT_CONNECTION_POOL_NAME = 'db.client.connection.pool.name';\n/**\n * The state of a connection in the pool\n *\n * @example idle\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexports.ATTR_DB_CLIENT_CONNECTION_STATE = 'db.client.connection.state';\n/**\n * The name of the database, fully qualified within the server address and port.\n *\n * @example customers\n * @example test.users\n *\n * @note If a database system has multiple namespace components, they **SHOULD** be concatenated (potentially using database system specific conventions) from most general to most specific namespace component, and more specific namespaces **SHOULD NOT** be captured without the more general namespaces, to ensure that \"startswith\" queries for the more general namespaces will be valid.\n * Semantic conventions for individual database systems **SHOULD** document what `db.namespace` means in the context of that system.\n * It is **RECOMMENDED** to capture the value as provided by the application without attempting to do any case normalization.\n * This attribute has stability level RELEASE CANDIDATE.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexports.ATTR_DB_NAMESPACE = 'db.namespace';\n/**\n * The name of the operation or command being executed.\n *\n * @example findAndModify\n * @example HMSET\n * @example SELECT\n *\n * @note It is **RECOMMENDED** to capture the value as provided by the application without attempting to do any case normalization.\n * If the operation name is parsed from the query text, it **SHOULD** be the first operation name found in the query.\n * For batch operations, if the individual operations are known to have the same operation name then that operation name **SHOULD** be used prepended by `BATCH `, otherwise `db.operation.name` **SHOULD** be `BATCH` or some other database system specific term if more applicable.\n * This attribute has stability level RELEASE CANDIDATE.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexports.ATTR_DB_OPERATION_NAME = 'db.operation.name';\n/**\n * Enum value \"used\" for attribute {@link ATTR_DB_CLIENT_CONNECTION_STATE}.\n */\nexports.DB_CLIENT_CONNECTION_STATE_VALUE_USED = 'used';\n/**\n * Enum value \"idle\" for attribute {@link ATTR_DB_CLIENT_CONNECTION_STATE}.\n */\nexports.DB_CLIENT_CONNECTION_STATE_VALUE_IDLE = 'idle';\n/**\n * The number of connections that are currently in state described by the `state` attribute\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexports.METRIC_DB_CLIENT_CONNECTION_COUNT = 'db.client.connection.count';\n/**\n * The number of current pending requests for an open connection\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexports.METRIC_DB_CLIENT_CONNECTION_PENDING_REQUESTS = 'db.client.connection.pending_requests';\n/**\n * Duration of database client operations.\n *\n * @note Batch operations **SHOULD** be recorded as a single operation.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexports.METRIC_DB_CLIENT_OPERATION_DURATION = 'db.client.operation.duration';\n//# sourceMappingURL=semconv.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/semconv.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/types.js":
/*!***************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/types.js ***!
  \***************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9Ab3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfYjkwY2QxYTUzN2RhZmUyOTRjZWU5NjNlNDQzMmI0NmEvbm9kZV9tb2R1bGVzL0BvcGVudGVsZW1ldHJ5L2luc3RydW1lbnRhdGlvbi1wZy9idWlsZC9zcmMvdHlwZXMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYW53YXJcXERvY3VtZW50c1xcQ3ViZW50d2ViXFxjdWJlbnRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF9iOTBjZDFhNTM3ZGFmZTI5NGNlZTk2M2U0NDMyYjQ2YVxcbm9kZV9tb2R1bGVzXFxAb3BlbnRlbGVtZXRyeVxcaW5zdHJ1bWVudGF0aW9uLXBnXFxidWlsZFxcc3JjXFx0eXBlcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbi8qXG4gKiBDb3B5cmlnaHQgVGhlIE9wZW5UZWxlbWV0cnkgQXV0aG9yc1xuICpcbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4gKiB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuXG4gKiBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbiAqXG4gKiAgICAgIGh0dHBzOi8vd3d3LmFwYWNoZS5vcmcvbGljZW5zZXMvTElDRU5TRS0yLjBcbiAqXG4gKiBVbmxlc3MgcmVxdWlyZWQgYnkgYXBwbGljYWJsZSBsYXcgb3IgYWdyZWVkIHRvIGluIHdyaXRpbmcsIHNvZnR3YXJlXG4gKiBkaXN0cmlidXRlZCB1bmRlciB0aGUgTGljZW5zZSBpcyBkaXN0cmlidXRlZCBvbiBhbiBcIkFTIElTXCIgQkFTSVMsXG4gKiBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgQ09ORElUSU9OUyBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC5cbiAqIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmRcbiAqIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuICovXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD10eXBlcy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/types.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/utils.js":
/*!***************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/utils.js ***!
  \***************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.isObjectWithTextString = exports.getErrorMessage = exports.patchClientConnectCallback = exports.patchCallbackPGPool = exports.updateCounter = exports.getPoolName = exports.patchCallback = exports.handleExecutionResult = exports.handleConfigQuery = exports.shouldSkipInstrumentation = exports.getSemanticAttributesFromPool = exports.getSemanticAttributesFromConnection = exports.getConnectionString = exports.parseNormalizedOperationName = exports.getQuerySpanName = void 0;\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst AttributeNames_1 = __webpack_require__(/*! ./enums/AttributeNames */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/enums/AttributeNames.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst semconv_1 = __webpack_require__(/*! ./semconv */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/semconv.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst SpanNames_1 = __webpack_require__(/*! ./enums/SpanNames */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/enums/SpanNames.js\");\n/**\n * Helper function to get a low cardinality span name from whatever info we have\n * about the query.\n *\n * This is tricky, because we don't have most of the information (table name,\n * operation name, etc) the spec recommends using to build a low-cardinality\n * value w/o parsing. So, we use db.name and assume that, if the query's a named\n * prepared statement, those `name` values will be low cardinality. If we don't\n * have a named prepared statement, we try to parse an operation (despite the\n * spec's warnings).\n *\n * @params dbName The name of the db against which this query is being issued,\n *   which could be missing if no db name was given at the time that the\n *   connection was established.\n * @params queryConfig Information we have about the query being issued, typed\n *   to reflect only the validation we've actually done on the args to\n *   `client.query()`. This will be undefined if `client.query()` was called\n *   with invalid arguments.\n */\nfunction getQuerySpanName(dbName, queryConfig) {\n    // NB: when the query config is invalid, we omit the dbName too, so that\n    // someone (or some tool) reading the span name doesn't misinterpret the\n    // dbName as being a prepared statement or sql commit name.\n    if (!queryConfig)\n        return SpanNames_1.SpanNames.QUERY_PREFIX;\n    // Either the name of a prepared statement; or an attempted parse\n    // of the SQL command, normalized to uppercase; or unknown.\n    const command = typeof queryConfig.name === 'string' && queryConfig.name\n        ? queryConfig.name\n        : parseNormalizedOperationName(queryConfig.text);\n    return `${SpanNames_1.SpanNames.QUERY_PREFIX}:${command}${dbName ? ` ${dbName}` : ''}`;\n}\nexports.getQuerySpanName = getQuerySpanName;\nfunction parseNormalizedOperationName(queryText) {\n    const indexOfFirstSpace = queryText.indexOf(' ');\n    let sqlCommand = indexOfFirstSpace === -1\n        ? queryText\n        : queryText.slice(0, indexOfFirstSpace);\n    sqlCommand = sqlCommand.toUpperCase();\n    // Handle query text being \"COMMIT;\", which has an extra semicolon before the space.\n    return sqlCommand.endsWith(';') ? sqlCommand.slice(0, -1) : sqlCommand;\n}\nexports.parseNormalizedOperationName = parseNormalizedOperationName;\nfunction getConnectionString(params) {\n    const host = params.host || 'localhost';\n    const port = params.port || 5432;\n    const database = params.database || '';\n    return `postgresql://${host}:${port}/${database}`;\n}\nexports.getConnectionString = getConnectionString;\nfunction getPort(port) {\n    // Port may be NaN as parseInt() is used on the value, passing null will result in NaN being parsed.\n    // https://github.com/brianc/node-postgres/blob/2a8efbee09a284be12748ed3962bc9b816965e36/packages/pg/lib/connection-parameters.js#L66\n    if (Number.isInteger(port)) {\n        return port;\n    }\n    // Unable to find the default used in pg code, so falling back to 'undefined'.\n    return undefined;\n}\nfunction getSemanticAttributesFromConnection(params) {\n    return {\n        [semantic_conventions_1.SEMATTRS_DB_SYSTEM]: semantic_conventions_1.DBSYSTEMVALUES_POSTGRESQL,\n        [semantic_conventions_1.SEMATTRS_DB_NAME]: params.database,\n        [semantic_conventions_1.SEMATTRS_DB_CONNECTION_STRING]: getConnectionString(params),\n        [semantic_conventions_1.SEMATTRS_NET_PEER_NAME]: params.host,\n        [semantic_conventions_1.SEMATTRS_NET_PEER_PORT]: getPort(params.port),\n        [semantic_conventions_1.SEMATTRS_DB_USER]: params.user,\n    };\n}\nexports.getSemanticAttributesFromConnection = getSemanticAttributesFromConnection;\nfunction getSemanticAttributesFromPool(params) {\n    return {\n        [semantic_conventions_1.SEMATTRS_DB_SYSTEM]: semantic_conventions_1.DBSYSTEMVALUES_POSTGRESQL,\n        [semantic_conventions_1.SEMATTRS_DB_NAME]: params.database,\n        [semantic_conventions_1.SEMATTRS_DB_CONNECTION_STRING]: getConnectionString(params),\n        [semantic_conventions_1.SEMATTRS_NET_PEER_NAME]: params.host,\n        [semantic_conventions_1.SEMATTRS_NET_PEER_PORT]: getPort(params.port),\n        [semantic_conventions_1.SEMATTRS_DB_USER]: params.user,\n        [AttributeNames_1.AttributeNames.IDLE_TIMEOUT_MILLIS]: params.idleTimeoutMillis,\n        [AttributeNames_1.AttributeNames.MAX_CLIENT]: params.maxClient,\n    };\n}\nexports.getSemanticAttributesFromPool = getSemanticAttributesFromPool;\nfunction shouldSkipInstrumentation(instrumentationConfig) {\n    return (instrumentationConfig.requireParentSpan === true &&\n        api_1.trace.getSpan(api_1.context.active()) === undefined);\n}\nexports.shouldSkipInstrumentation = shouldSkipInstrumentation;\n// Create a span from our normalized queryConfig object,\n// or return a basic span if no queryConfig was given/could be created.\nfunction handleConfigQuery(tracer, instrumentationConfig, queryConfig) {\n    // Create child span.\n    const { connectionParameters } = this;\n    const dbName = connectionParameters.database;\n    const spanName = getQuerySpanName(dbName, queryConfig);\n    const span = tracer.startSpan(spanName, {\n        kind: api_1.SpanKind.CLIENT,\n        attributes: getSemanticAttributesFromConnection(connectionParameters),\n    });\n    if (!queryConfig) {\n        return span;\n    }\n    // Set attributes\n    if (queryConfig.text) {\n        span.setAttribute(semantic_conventions_1.SEMATTRS_DB_STATEMENT, queryConfig.text);\n    }\n    if (instrumentationConfig.enhancedDatabaseReporting &&\n        Array.isArray(queryConfig.values)) {\n        try {\n            const convertedValues = queryConfig.values.map(value => {\n                if (value == null) {\n                    return 'null';\n                }\n                else if (value instanceof Buffer) {\n                    return value.toString();\n                }\n                else if (typeof value === 'object') {\n                    if (typeof value.toPostgres === 'function') {\n                        return value.toPostgres();\n                    }\n                    return JSON.stringify(value);\n                }\n                else {\n                    //string, number\n                    return value.toString();\n                }\n            });\n            span.setAttribute(AttributeNames_1.AttributeNames.PG_VALUES, convertedValues);\n        }\n        catch (e) {\n            api_1.diag.error('failed to stringify ', queryConfig.values, e);\n        }\n    }\n    // Set plan name attribute, if present\n    if (typeof queryConfig.name === 'string') {\n        span.setAttribute(AttributeNames_1.AttributeNames.PG_PLAN, queryConfig.name);\n    }\n    return span;\n}\nexports.handleConfigQuery = handleConfigQuery;\nfunction handleExecutionResult(config, span, pgResult) {\n    if (typeof config.responseHook === 'function') {\n        (0, instrumentation_1.safeExecuteInTheMiddle)(() => {\n            config.responseHook(span, {\n                data: pgResult,\n            });\n        }, err => {\n            if (err) {\n                api_1.diag.error('Error running response hook', err);\n            }\n        }, true);\n    }\n}\nexports.handleExecutionResult = handleExecutionResult;\nfunction patchCallback(instrumentationConfig, span, cb, attributes, recordDuration) {\n    return function patchedCallback(err, res) {\n        if (err) {\n            if (Object.prototype.hasOwnProperty.call(err, 'code')) {\n                attributes[semantic_conventions_1.ATTR_ERROR_TYPE] = err['code'];\n            }\n            span.setStatus({\n                code: api_1.SpanStatusCode.ERROR,\n                message: err.message,\n            });\n        }\n        else {\n            handleExecutionResult(instrumentationConfig, span, res);\n        }\n        recordDuration();\n        span.end();\n        cb.call(this, err, res);\n    };\n}\nexports.patchCallback = patchCallback;\nfunction getPoolName(pool) {\n    let poolName = '';\n    poolName += ((pool === null || pool === void 0 ? void 0 : pool.host) ? `${pool.host}` : 'unknown_host') + ':';\n    poolName += ((pool === null || pool === void 0 ? void 0 : pool.port) ? `${pool.port}` : 'unknown_port') + '/';\n    poolName += (pool === null || pool === void 0 ? void 0 : pool.database) ? `${pool.database}` : 'unknown_database';\n    return poolName.trim();\n}\nexports.getPoolName = getPoolName;\nfunction updateCounter(poolName, pool, connectionCount, connectionPendingRequests, latestCounter) {\n    const all = pool.totalCount;\n    const pending = pool.waitingCount;\n    const idle = pool.idleCount;\n    const used = all - idle;\n    connectionCount.add(used - latestCounter.used, {\n        [semconv_1.ATTR_DB_CLIENT_CONNECTION_STATE]: semconv_1.DB_CLIENT_CONNECTION_STATE_VALUE_USED,\n        [semconv_1.ATTR_DB_CLIENT_CONNECTION_POOL_NAME]: poolName,\n    });\n    connectionCount.add(idle - latestCounter.idle, {\n        [semconv_1.ATTR_DB_CLIENT_CONNECTION_STATE]: semconv_1.DB_CLIENT_CONNECTION_STATE_VALUE_IDLE,\n        [semconv_1.ATTR_DB_CLIENT_CONNECTION_POOL_NAME]: poolName,\n    });\n    connectionPendingRequests.add(pending - latestCounter.pending, {\n        [semconv_1.ATTR_DB_CLIENT_CONNECTION_POOL_NAME]: poolName,\n    });\n    return { used: used, idle: idle, pending: pending };\n}\nexports.updateCounter = updateCounter;\nfunction patchCallbackPGPool(span, cb) {\n    return function patchedCallback(err, res, done) {\n        if (err) {\n            span.setStatus({\n                code: api_1.SpanStatusCode.ERROR,\n                message: err.message,\n            });\n        }\n        span.end();\n        cb.call(this, err, res, done);\n    };\n}\nexports.patchCallbackPGPool = patchCallbackPGPool;\nfunction patchClientConnectCallback(span, cb) {\n    return function patchedClientConnectCallback(err) {\n        if (err) {\n            span.setStatus({\n                code: api_1.SpanStatusCode.ERROR,\n                message: err.message,\n            });\n        }\n        span.end();\n        cb.apply(this, arguments);\n    };\n}\nexports.patchClientConnectCallback = patchClientConnectCallback;\n/**\n * Attempt to get a message string from a thrown value, while being quite\n * defensive, to recognize the fact that, in JS, any kind of value (even\n * primitives) can be thrown.\n */\nfunction getErrorMessage(e) {\n    return typeof e === 'object' && e !== null && 'message' in e\n        ? String(e.message)\n        : undefined;\n}\nexports.getErrorMessage = getErrorMessage;\nfunction isObjectWithTextString(it) {\n    var _a;\n    return (typeof it === 'object' &&\n        typeof ((_a = it) === null || _a === void 0 ? void 0 : _a.text) === 'string');\n}\nexports.isObjectWithTextString = isObjectWithTextString;\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/utils.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/version.js":
/*!*****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/version.js ***!
  \*****************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.51.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-pg';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/version.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/enums/AttributeNames.js":
/*!******************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/enums/AttributeNames.js ***!
  \******************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.AttributeNames = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// Postgresql specific attributes not covered by semantic conventions\nvar AttributeNames;\n(function (AttributeNames) {\n    AttributeNames[\"PG_VALUES\"] = \"db.postgresql.values\";\n    AttributeNames[\"PG_PLAN\"] = \"db.postgresql.plan\";\n    AttributeNames[\"IDLE_TIMEOUT_MILLIS\"] = \"db.postgresql.idle.timeout.millis\";\n    AttributeNames[\"MAX_CLIENT\"] = \"db.postgresql.max.client\";\n})(AttributeNames = exports.AttributeNames || (exports.AttributeNames = {}));\n//# sourceMappingURL=AttributeNames.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/enums/AttributeNames.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/enums/SpanNames.js":
/*!*************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/enums/SpanNames.js ***!
  \*************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.SpanNames = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// Contains span names produced by instrumentation\nvar SpanNames;\n(function (SpanNames) {\n    SpanNames[\"QUERY_PREFIX\"] = \"pg.query\";\n    SpanNames[\"CONNECT\"] = \"pg.connect\";\n    SpanNames[\"POOL_CONNECT\"] = \"pg-pool.connect\";\n})(SpanNames = exports.SpanNames || (exports.SpanNames = {}));\n//# sourceMappingURL=SpanNames.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/enums/SpanNames.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/index.js":
/*!***************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/index.js ***!
  \***************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/instrumentation.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/types.js\"), exports);\n__exportStar(__webpack_require__(/*! ./enums/AttributeNames */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/enums/AttributeNames.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/index.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/instrumentation.js":
/*!*************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/instrumentation.js ***!
  \*************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PgInstrumentation = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst internal_types_1 = __webpack_require__(/*! ./internal-types */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/internal-types.js\");\nconst utils = __webpack_require__(/*! ./utils */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/utils.js\");\nconst sql_common_1 = __webpack_require__(/*! @opentelemetry/sql-common */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+sql-common@0.40.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/sql-common/build/src/index.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/version.js\");\nconst SpanNames_1 = __webpack_require__(/*! ./enums/SpanNames */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/enums/SpanNames.js\");\nconst core_1 = __webpack_require__(/*! @opentelemetry/core */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+core@1.30.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/core/build/esm/index.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst semconv_1 = __webpack_require__(/*! ./semconv */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/semconv.js\");\nfunction extractModuleExports(module) {\n    return module[Symbol.toStringTag] === 'Module'\n        ? module.default // ESM\n        : module; // CommonJS\n}\nclass PgInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, config);\n        // Pool events connect, acquire, release and remove can be called\n        // multiple times without changing the values of total, idle and waiting\n        // connections. The _connectionsCounter is used to keep track of latest\n        // values and only update the metrics _connectionsCount and _connectionPendingRequests\n        // when the value change.\n        this._connectionsCounter = {\n            used: 0,\n            idle: 0,\n            pending: 0,\n        };\n    }\n    _updateMetricInstruments() {\n        this._operationDuration = this.meter.createHistogram(semconv_1.METRIC_DB_CLIENT_OPERATION_DURATION, {\n            description: 'Duration of database client operations.',\n            unit: 's',\n            valueType: api_1.ValueType.DOUBLE,\n            advice: {\n                explicitBucketBoundaries: [\n                    0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1, 5, 10,\n                ],\n            },\n        });\n        this._connectionsCounter = {\n            idle: 0,\n            pending: 0,\n            used: 0,\n        };\n        this._connectionsCount = this.meter.createUpDownCounter(semconv_1.METRIC_DB_CLIENT_CONNECTION_COUNT, {\n            description: 'The number of connections that are currently in state described by the state attribute.',\n            unit: '{connection}',\n        });\n        this._connectionPendingRequests = this.meter.createUpDownCounter(semconv_1.METRIC_DB_CLIENT_CONNECTION_PENDING_REQUESTS, {\n            description: 'The number of current pending requests for an open connection.',\n            unit: '{connection}',\n        });\n    }\n    init() {\n        const SUPPORTED_PG_VERSIONS = ['>=8.0.3 <9'];\n        const modulePgNativeClient = new instrumentation_1.InstrumentationNodeModuleFile('pg/lib/native/client.js', SUPPORTED_PG_VERSIONS, this._patchPgClient.bind(this), this._unpatchPgClient.bind(this));\n        const modulePgClient = new instrumentation_1.InstrumentationNodeModuleFile('pg/lib/client.js', SUPPORTED_PG_VERSIONS, this._patchPgClient.bind(this), this._unpatchPgClient.bind(this));\n        const modulePG = new instrumentation_1.InstrumentationNodeModuleDefinition('pg', SUPPORTED_PG_VERSIONS, (module) => {\n            const moduleExports = extractModuleExports(module);\n            this._patchPgClient(moduleExports.Client);\n            return module;\n        }, (module) => {\n            const moduleExports = extractModuleExports(module);\n            this._unpatchPgClient(moduleExports.Client);\n            return module;\n        }, [modulePgClient, modulePgNativeClient]);\n        const modulePGPool = new instrumentation_1.InstrumentationNodeModuleDefinition('pg-pool', ['>=2.0.0 <4'], (moduleExports) => {\n            if ((0, instrumentation_1.isWrapped)(moduleExports.prototype.connect)) {\n                this._unwrap(moduleExports.prototype, 'connect');\n            }\n            this._wrap(moduleExports.prototype, 'connect', this._getPoolConnectPatch());\n            return moduleExports;\n        }, (moduleExports) => {\n            if ((0, instrumentation_1.isWrapped)(moduleExports.prototype.connect)) {\n                this._unwrap(moduleExports.prototype, 'connect');\n            }\n        });\n        return [modulePG, modulePGPool];\n    }\n    _patchPgClient(module) {\n        if (!module) {\n            return;\n        }\n        const moduleExports = extractModuleExports(module);\n        if ((0, instrumentation_1.isWrapped)(moduleExports.prototype.query)) {\n            this._unwrap(moduleExports.prototype, 'query');\n        }\n        if ((0, instrumentation_1.isWrapped)(moduleExports.prototype.connect)) {\n            this._unwrap(moduleExports.prototype, 'connect');\n        }\n        this._wrap(moduleExports.prototype, 'query', this._getClientQueryPatch());\n        this._wrap(moduleExports.prototype, 'connect', this._getClientConnectPatch());\n        return module;\n    }\n    _unpatchPgClient(module) {\n        const moduleExports = extractModuleExports(module);\n        if ((0, instrumentation_1.isWrapped)(moduleExports.prototype.query)) {\n            this._unwrap(moduleExports.prototype, 'query');\n        }\n        if ((0, instrumentation_1.isWrapped)(moduleExports.prototype.connect)) {\n            this._unwrap(moduleExports.prototype, 'connect');\n        }\n        return module;\n    }\n    _getClientConnectPatch() {\n        const plugin = this;\n        return (original) => {\n            return function connect(callback) {\n                if (utils.shouldSkipInstrumentation(plugin.getConfig())) {\n                    return original.call(this, callback);\n                }\n                const span = plugin.tracer.startSpan(SpanNames_1.SpanNames.CONNECT, {\n                    kind: api_1.SpanKind.CLIENT,\n                    attributes: utils.getSemanticAttributesFromConnection(this),\n                });\n                if (callback) {\n                    const parentSpan = api_1.trace.getSpan(api_1.context.active());\n                    callback = utils.patchClientConnectCallback(span, callback);\n                    if (parentSpan) {\n                        callback = api_1.context.bind(api_1.context.active(), callback);\n                    }\n                }\n                const connectResult = api_1.context.with(api_1.trace.setSpan(api_1.context.active(), span), () => {\n                    return original.call(this, callback);\n                });\n                return handleConnectResult(span, connectResult);\n            };\n        };\n    }\n    recordOperationDuration(attributes, startTime) {\n        const metricsAttributes = {};\n        const keysToCopy = [\n            semantic_conventions_1.SEMATTRS_DB_SYSTEM,\n            semconv_1.ATTR_DB_NAMESPACE,\n            semantic_conventions_1.ATTR_ERROR_TYPE,\n            semantic_conventions_1.ATTR_SERVER_PORT,\n            semantic_conventions_1.ATTR_SERVER_ADDRESS,\n            semconv_1.ATTR_DB_OPERATION_NAME,\n        ];\n        keysToCopy.forEach(key => {\n            if (key in attributes) {\n                metricsAttributes[key] = attributes[key];\n            }\n        });\n        const durationSeconds = (0, core_1.hrTimeToMilliseconds)((0, core_1.hrTimeDuration)(startTime, (0, core_1.hrTime)())) / 1000;\n        this._operationDuration.record(durationSeconds, metricsAttributes);\n    }\n    _getClientQueryPatch() {\n        const plugin = this;\n        return (original) => {\n            this._diag.debug('Patching pg.Client.prototype.query');\n            return function query(...args) {\n                if (utils.shouldSkipInstrumentation(plugin.getConfig())) {\n                    return original.apply(this, args);\n                }\n                const startTime = (0, core_1.hrTime)();\n                // client.query(text, cb?), client.query(text, values, cb?), and\n                // client.query(configObj, cb?) are all valid signatures. We construct\n                // a queryConfig obj from all (valid) signatures to build the span in a\n                // unified way. We verify that we at least have query text, and code\n                // defensively when dealing with `queryConfig` after that (to handle all\n                // the other invalid cases, like a non-array for values being provided).\n                // The type casts here reflect only what we've actually validated.\n                const arg0 = args[0];\n                const firstArgIsString = typeof arg0 === 'string';\n                const firstArgIsQueryObjectWithText = utils.isObjectWithTextString(arg0);\n                // TODO: remove the `as ...` casts below when the TS version is upgraded.\n                // Newer TS versions will use the result of firstArgIsQueryObjectWithText\n                // to properly narrow arg0, but TS 4.3.5 does not.\n                const queryConfig = firstArgIsString\n                    ? {\n                        text: arg0,\n                        values: Array.isArray(args[1]) ? args[1] : undefined,\n                    }\n                    : firstArgIsQueryObjectWithText\n                        ? arg0\n                        : undefined;\n                const attributes = {\n                    [semantic_conventions_1.SEMATTRS_DB_SYSTEM]: semantic_conventions_1.DBSYSTEMVALUES_POSTGRESQL,\n                    [semconv_1.ATTR_DB_NAMESPACE]: this.database,\n                    [semantic_conventions_1.ATTR_SERVER_PORT]: this.connectionParameters.port,\n                    [semantic_conventions_1.ATTR_SERVER_ADDRESS]: this.connectionParameters.host,\n                };\n                if (queryConfig === null || queryConfig === void 0 ? void 0 : queryConfig.text) {\n                    attributes[semconv_1.ATTR_DB_OPERATION_NAME] =\n                        utils.parseNormalizedOperationName(queryConfig === null || queryConfig === void 0 ? void 0 : queryConfig.text);\n                }\n                const recordDuration = () => {\n                    plugin.recordOperationDuration(attributes, startTime);\n                };\n                const instrumentationConfig = plugin.getConfig();\n                const span = utils.handleConfigQuery.call(this, plugin.tracer, instrumentationConfig, queryConfig);\n                // Modify query text w/ a tracing comment before invoking original for\n                // tracing, but only if args[0] has one of our expected shapes.\n                if (instrumentationConfig.addSqlCommenterCommentToQueries) {\n                    if (firstArgIsString) {\n                        args[0] = (0, sql_common_1.addSqlCommenterComment)(span, arg0);\n                    }\n                    else if (firstArgIsQueryObjectWithText && !('name' in arg0)) {\n                        // In the case of a query object, we need to ensure there's no name field\n                        // as this indicates a prepared query, where the comment would remain the same\n                        // for every invocation and contain an outdated trace context.\n                        args[0] = Object.assign(Object.assign({}, arg0), { text: (0, sql_common_1.addSqlCommenterComment)(span, arg0.text) });\n                    }\n                }\n                // Bind callback (if any) to parent span (if any)\n                if (args.length > 0) {\n                    const parentSpan = api_1.trace.getSpan(api_1.context.active());\n                    if (typeof args[args.length - 1] === 'function') {\n                        // Patch ParameterQuery callback\n                        args[args.length - 1] = utils.patchCallback(instrumentationConfig, span, args[args.length - 1], // nb: not type safe.\n                        attributes, recordDuration);\n                        // If a parent span exists, bind the callback\n                        if (parentSpan) {\n                            args[args.length - 1] = api_1.context.bind(api_1.context.active(), args[args.length - 1]);\n                        }\n                    }\n                    else if (typeof (queryConfig === null || queryConfig === void 0 ? void 0 : queryConfig.callback) === 'function') {\n                        // Patch ConfigQuery callback\n                        let callback = utils.patchCallback(plugin.getConfig(), span, queryConfig.callback, // nb: not type safe.\n                        attributes, recordDuration);\n                        // If a parent span existed, bind the callback\n                        if (parentSpan) {\n                            callback = api_1.context.bind(api_1.context.active(), callback);\n                        }\n                        args[0].callback = callback;\n                    }\n                }\n                const { requestHook } = instrumentationConfig;\n                if (typeof requestHook === 'function' && queryConfig) {\n                    (0, instrumentation_1.safeExecuteInTheMiddle)(() => {\n                        // pick keys to expose explicitly, so we're not leaking pg package\n                        // internals that are subject to change\n                        const { database, host, port, user } = this.connectionParameters;\n                        const connection = { database, host, port, user };\n                        requestHook(span, {\n                            connection,\n                            query: {\n                                text: queryConfig.text,\n                                // nb: if `client.query` is called with illegal arguments\n                                // (e.g., if `queryConfig.values` is passed explicitly, but a\n                                // non-array is given), then the type casts will be wrong. But\n                                // we leave it up to the queryHook to handle that, and we\n                                // catch and swallow any errors it throws. The other options\n                                // are all worse. E.g., we could leave `queryConfig.values`\n                                // and `queryConfig.name` as `unknown`, but then the hook body\n                                // would be forced to validate (or cast) them before using\n                                // them, which seems incredibly cumbersome given that these\n                                // casts will be correct 99.9% of the time -- and pg.query\n                                // will immediately throw during development in the other .1%\n                                // of cases. Alternatively, we could simply skip calling the\n                                // hook when `values` or `name` don't have the expected type,\n                                // but that would add unnecessary validation overhead to every\n                                // hook invocation and possibly be even more confusing/unexpected.\n                                values: queryConfig.values,\n                                name: queryConfig.name,\n                            },\n                        });\n                    }, err => {\n                        if (err) {\n                            plugin._diag.error('Error running query hook', err);\n                        }\n                    }, true);\n                }\n                let result;\n                try {\n                    result = original.apply(this, args);\n                }\n                catch (e) {\n                    span.setStatus({\n                        code: api_1.SpanStatusCode.ERROR,\n                        message: utils.getErrorMessage(e),\n                    });\n                    span.end();\n                    throw e;\n                }\n                // Bind promise to parent span and end the span\n                if (result instanceof Promise) {\n                    return result\n                        .then((result) => {\n                        // Return a pass-along promise which ends the span and then goes to user's orig resolvers\n                        return new Promise(resolve => {\n                            utils.handleExecutionResult(plugin.getConfig(), span, result);\n                            recordDuration();\n                            span.end();\n                            resolve(result);\n                        });\n                    })\n                        .catch((error) => {\n                        return new Promise((_, reject) => {\n                            span.setStatus({\n                                code: api_1.SpanStatusCode.ERROR,\n                                message: error.message,\n                            });\n                            recordDuration();\n                            span.end();\n                            reject(error);\n                        });\n                    });\n                }\n                // else returns void\n                return result; // void\n            };\n        };\n    }\n    _setPoolConnectEventListeners(pgPool) {\n        if (pgPool[internal_types_1.EVENT_LISTENERS_SET])\n            return;\n        const poolName = utils.getPoolName(pgPool.options);\n        pgPool.on('connect', () => {\n            this._connectionsCounter = utils.updateCounter(poolName, pgPool, this._connectionsCount, this._connectionPendingRequests, this._connectionsCounter);\n        });\n        pgPool.on('acquire', () => {\n            this._connectionsCounter = utils.updateCounter(poolName, pgPool, this._connectionsCount, this._connectionPendingRequests, this._connectionsCounter);\n        });\n        pgPool.on('remove', () => {\n            this._connectionsCounter = utils.updateCounter(poolName, pgPool, this._connectionsCount, this._connectionPendingRequests, this._connectionsCounter);\n        });\n        pgPool.on('release', () => {\n            this._connectionsCounter = utils.updateCounter(poolName, pgPool, this._connectionsCount, this._connectionPendingRequests, this._connectionsCounter);\n        });\n        pgPool[internal_types_1.EVENT_LISTENERS_SET] = true;\n    }\n    _getPoolConnectPatch() {\n        const plugin = this;\n        return (originalConnect) => {\n            return function connect(callback) {\n                if (utils.shouldSkipInstrumentation(plugin.getConfig())) {\n                    return originalConnect.call(this, callback);\n                }\n                // setup span\n                const span = plugin.tracer.startSpan(SpanNames_1.SpanNames.POOL_CONNECT, {\n                    kind: api_1.SpanKind.CLIENT,\n                    attributes: utils.getSemanticAttributesFromPool(this.options),\n                });\n                plugin._setPoolConnectEventListeners(this);\n                if (callback) {\n                    const parentSpan = api_1.trace.getSpan(api_1.context.active());\n                    callback = utils.patchCallbackPGPool(span, callback);\n                    // If a parent span exists, bind the callback\n                    if (parentSpan) {\n                        callback = api_1.context.bind(api_1.context.active(), callback);\n                    }\n                }\n                const connectResult = api_1.context.with(api_1.trace.setSpan(api_1.context.active(), span), () => {\n                    return originalConnect.call(this, callback);\n                });\n                return handleConnectResult(span, connectResult);\n            };\n        };\n    }\n}\nexports.PgInstrumentation = PgInstrumentation;\nfunction handleConnectResult(span, connectResult) {\n    if (!(connectResult instanceof Promise)) {\n        return connectResult;\n    }\n    const connectResultPromise = connectResult;\n    return api_1.context.bind(api_1.context.active(), connectResultPromise\n        .then(result => {\n        span.end();\n        return result;\n    })\n        .catch((error) => {\n        span.setStatus({\n            code: api_1.SpanStatusCode.ERROR,\n            message: utils.getErrorMessage(error),\n        });\n        span.end();\n        return Promise.reject(error);\n    }));\n}\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/instrumentation.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/internal-types.js":
/*!************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/internal-types.js ***!
  \************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.EVENT_LISTENERS_SET = void 0;\nexports.EVENT_LISTENERS_SET = Symbol('opentelemetry.instrumentation.pg.eventListenersSet');\n//# sourceMappingURL=internal-types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/internal-types.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/semconv.js":
/*!*****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/semconv.js ***!
  \*****************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.METRIC_DB_CLIENT_OPERATION_DURATION = exports.METRIC_DB_CLIENT_CONNECTION_PENDING_REQUESTS = exports.METRIC_DB_CLIENT_CONNECTION_COUNT = exports.DB_CLIENT_CONNECTION_STATE_VALUE_IDLE = exports.DB_CLIENT_CONNECTION_STATE_VALUE_USED = exports.ATTR_DB_OPERATION_NAME = exports.ATTR_DB_NAMESPACE = exports.ATTR_DB_CLIENT_CONNECTION_STATE = exports.ATTR_DB_CLIENT_CONNECTION_POOL_NAME = void 0;\n/**\n * The name of the connection pool; unique within the instrumented application. In case the connection pool implementation doesn't provide a name, instrumentation **SHOULD** use a combination of parameters that would make the name unique, for example, combining attributes `server.address`, `server.port`, and `db.namespace`, formatted as `server.address:server.port/db.namespace`. Instrumentations that generate connection pool name following different patterns **SHOULD** document it.\n *\n * @example myDataSource\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexports.ATTR_DB_CLIENT_CONNECTION_POOL_NAME = 'db.client.connection.pool.name';\n/**\n * The state of a connection in the pool\n *\n * @example idle\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexports.ATTR_DB_CLIENT_CONNECTION_STATE = 'db.client.connection.state';\n/**\n * The name of the database, fully qualified within the server address and port.\n *\n * @example customers\n * @example test.users\n *\n * @note If a database system has multiple namespace components, they **SHOULD** be concatenated (potentially using database system specific conventions) from most general to most specific namespace component, and more specific namespaces **SHOULD NOT** be captured without the more general namespaces, to ensure that \"startswith\" queries for the more general namespaces will be valid.\n * Semantic conventions for individual database systems **SHOULD** document what `db.namespace` means in the context of that system.\n * It is **RECOMMENDED** to capture the value as provided by the application without attempting to do any case normalization.\n * This attribute has stability level RELEASE CANDIDATE.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexports.ATTR_DB_NAMESPACE = 'db.namespace';\n/**\n * The name of the operation or command being executed.\n *\n * @example findAndModify\n * @example HMSET\n * @example SELECT\n *\n * @note It is **RECOMMENDED** to capture the value as provided by the application without attempting to do any case normalization.\n * If the operation name is parsed from the query text, it **SHOULD** be the first operation name found in the query.\n * For batch operations, if the individual operations are known to have the same operation name then that operation name **SHOULD** be used prepended by `BATCH `, otherwise `db.operation.name` **SHOULD** be `BATCH` or some other database system specific term if more applicable.\n * This attribute has stability level RELEASE CANDIDATE.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexports.ATTR_DB_OPERATION_NAME = 'db.operation.name';\n/**\n * Enum value \"used\" for attribute {@link ATTR_DB_CLIENT_CONNECTION_STATE}.\n */\nexports.DB_CLIENT_CONNECTION_STATE_VALUE_USED = 'used';\n/**\n * Enum value \"idle\" for attribute {@link ATTR_DB_CLIENT_CONNECTION_STATE}.\n */\nexports.DB_CLIENT_CONNECTION_STATE_VALUE_IDLE = 'idle';\n/**\n * The number of connections that are currently in state described by the `state` attribute\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexports.METRIC_DB_CLIENT_CONNECTION_COUNT = 'db.client.connection.count';\n/**\n * The number of current pending requests for an open connection\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexports.METRIC_DB_CLIENT_CONNECTION_PENDING_REQUESTS = 'db.client.connection.pending_requests';\n/**\n * Duration of database client operations.\n *\n * @note Batch operations **SHOULD** be recorded as a single operation.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexports.METRIC_DB_CLIENT_OPERATION_DURATION = 'db.client.operation.duration';\n//# sourceMappingURL=semconv.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/semconv.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/types.js":
/*!***************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/types.js ***!
  \***************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF9iOTBjZDFhNTM3ZGFmZTI5NGNlZTk2M2U0NDMyYjQ2YS9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLXBnL2J1aWxkL3NyYy90eXBlcy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbndhclxcRG9jdW1lbnRzXFxDdWJlbnR3ZWJcXGN1YmVudFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQG9wZW50ZWxlbWV0cnkraW5zdHJ1bWVudGF0X2I5MGNkMWE1MzdkYWZlMjk0Y2VlOTYzZTQ0MzJiNDZhXFxub2RlX21vZHVsZXNcXEBvcGVudGVsZW1ldHJ5XFxpbnN0cnVtZW50YXRpb24tcGdcXGJ1aWxkXFxzcmNcXHR5cGVzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuLypcbiAqIENvcHlyaWdodCBUaGUgT3BlblRlbGVtZXRyeSBBdXRob3JzXG4gKlxuICogTGljZW5zZWQgdW5kZXIgdGhlIEFwYWNoZSBMaWNlbnNlLCBWZXJzaW9uIDIuMCAodGhlIFwiTGljZW5zZVwiKTtcbiAqIHlvdSBtYXkgbm90IHVzZSB0aGlzIGZpbGUgZXhjZXB0IGluIGNvbXBsaWFuY2Ugd2l0aCB0aGUgTGljZW5zZS5cbiAqIFlvdSBtYXkgb2J0YWluIGEgY29weSBvZiB0aGUgTGljZW5zZSBhdFxuICpcbiAqICAgICAgaHR0cHM6Ly93d3cuYXBhY2hlLm9yZy9saWNlbnNlcy9MSUNFTlNFLTIuMFxuICpcbiAqIFVubGVzcyByZXF1aXJlZCBieSBhcHBsaWNhYmxlIGxhdyBvciBhZ3JlZWQgdG8gaW4gd3JpdGluZywgc29mdHdhcmVcbiAqIGRpc3RyaWJ1dGVkIHVuZGVyIHRoZSBMaWNlbnNlIGlzIGRpc3RyaWJ1dGVkIG9uIGFuIFwiQVMgSVNcIiBCQVNJUyxcbiAqIFdJVEhPVVQgV0FSUkFOVElFUyBPUiBDT05ESVRJT05TIE9GIEFOWSBLSU5ELCBlaXRoZXIgZXhwcmVzcyBvciBpbXBsaWVkLlxuICogU2VlIHRoZSBMaWNlbnNlIGZvciB0aGUgc3BlY2lmaWMgbGFuZ3VhZ2UgZ292ZXJuaW5nIHBlcm1pc3Npb25zIGFuZFxuICogbGltaXRhdGlvbnMgdW5kZXIgdGhlIExpY2Vuc2UuXG4gKi9cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXR5cGVzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/types.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/utils.js":
/*!***************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/utils.js ***!
  \***************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.isObjectWithTextString = exports.getErrorMessage = exports.patchClientConnectCallback = exports.patchCallbackPGPool = exports.updateCounter = exports.getPoolName = exports.patchCallback = exports.handleExecutionResult = exports.handleConfigQuery = exports.shouldSkipInstrumentation = exports.getSemanticAttributesFromPool = exports.getSemanticAttributesFromConnection = exports.getConnectionString = exports.parseNormalizedOperationName = exports.getQuerySpanName = void 0;\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst AttributeNames_1 = __webpack_require__(/*! ./enums/AttributeNames */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/enums/AttributeNames.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst semconv_1 = __webpack_require__(/*! ./semconv */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/semconv.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst SpanNames_1 = __webpack_require__(/*! ./enums/SpanNames */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/enums/SpanNames.js\");\n/**\n * Helper function to get a low cardinality span name from whatever info we have\n * about the query.\n *\n * This is tricky, because we don't have most of the information (table name,\n * operation name, etc) the spec recommends using to build a low-cardinality\n * value w/o parsing. So, we use db.name and assume that, if the query's a named\n * prepared statement, those `name` values will be low cardinality. If we don't\n * have a named prepared statement, we try to parse an operation (despite the\n * spec's warnings).\n *\n * @params dbName The name of the db against which this query is being issued,\n *   which could be missing if no db name was given at the time that the\n *   connection was established.\n * @params queryConfig Information we have about the query being issued, typed\n *   to reflect only the validation we've actually done on the args to\n *   `client.query()`. This will be undefined if `client.query()` was called\n *   with invalid arguments.\n */\nfunction getQuerySpanName(dbName, queryConfig) {\n    // NB: when the query config is invalid, we omit the dbName too, so that\n    // someone (or some tool) reading the span name doesn't misinterpret the\n    // dbName as being a prepared statement or sql commit name.\n    if (!queryConfig)\n        return SpanNames_1.SpanNames.QUERY_PREFIX;\n    // Either the name of a prepared statement; or an attempted parse\n    // of the SQL command, normalized to uppercase; or unknown.\n    const command = typeof queryConfig.name === 'string' && queryConfig.name\n        ? queryConfig.name\n        : parseNormalizedOperationName(queryConfig.text);\n    return `${SpanNames_1.SpanNames.QUERY_PREFIX}:${command}${dbName ? ` ${dbName}` : ''}`;\n}\nexports.getQuerySpanName = getQuerySpanName;\nfunction parseNormalizedOperationName(queryText) {\n    const indexOfFirstSpace = queryText.indexOf(' ');\n    let sqlCommand = indexOfFirstSpace === -1\n        ? queryText\n        : queryText.slice(0, indexOfFirstSpace);\n    sqlCommand = sqlCommand.toUpperCase();\n    // Handle query text being \"COMMIT;\", which has an extra semicolon before the space.\n    return sqlCommand.endsWith(';') ? sqlCommand.slice(0, -1) : sqlCommand;\n}\nexports.parseNormalizedOperationName = parseNormalizedOperationName;\nfunction getConnectionString(params) {\n    const host = params.host || 'localhost';\n    const port = params.port || 5432;\n    const database = params.database || '';\n    return `postgresql://${host}:${port}/${database}`;\n}\nexports.getConnectionString = getConnectionString;\nfunction getPort(port) {\n    // Port may be NaN as parseInt() is used on the value, passing null will result in NaN being parsed.\n    // https://github.com/brianc/node-postgres/blob/2a8efbee09a284be12748ed3962bc9b816965e36/packages/pg/lib/connection-parameters.js#L66\n    if (Number.isInteger(port)) {\n        return port;\n    }\n    // Unable to find the default used in pg code, so falling back to 'undefined'.\n    return undefined;\n}\nfunction getSemanticAttributesFromConnection(params) {\n    return {\n        [semantic_conventions_1.SEMATTRS_DB_SYSTEM]: semantic_conventions_1.DBSYSTEMVALUES_POSTGRESQL,\n        [semantic_conventions_1.SEMATTRS_DB_NAME]: params.database,\n        [semantic_conventions_1.SEMATTRS_DB_CONNECTION_STRING]: getConnectionString(params),\n        [semantic_conventions_1.SEMATTRS_NET_PEER_NAME]: params.host,\n        [semantic_conventions_1.SEMATTRS_NET_PEER_PORT]: getPort(params.port),\n        [semantic_conventions_1.SEMATTRS_DB_USER]: params.user,\n    };\n}\nexports.getSemanticAttributesFromConnection = getSemanticAttributesFromConnection;\nfunction getSemanticAttributesFromPool(params) {\n    return {\n        [semantic_conventions_1.SEMATTRS_DB_SYSTEM]: semantic_conventions_1.DBSYSTEMVALUES_POSTGRESQL,\n        [semantic_conventions_1.SEMATTRS_DB_NAME]: params.database,\n        [semantic_conventions_1.SEMATTRS_DB_CONNECTION_STRING]: getConnectionString(params),\n        [semantic_conventions_1.SEMATTRS_NET_PEER_NAME]: params.host,\n        [semantic_conventions_1.SEMATTRS_NET_PEER_PORT]: getPort(params.port),\n        [semantic_conventions_1.SEMATTRS_DB_USER]: params.user,\n        [AttributeNames_1.AttributeNames.IDLE_TIMEOUT_MILLIS]: params.idleTimeoutMillis,\n        [AttributeNames_1.AttributeNames.MAX_CLIENT]: params.maxClient,\n    };\n}\nexports.getSemanticAttributesFromPool = getSemanticAttributesFromPool;\nfunction shouldSkipInstrumentation(instrumentationConfig) {\n    return (instrumentationConfig.requireParentSpan === true &&\n        api_1.trace.getSpan(api_1.context.active()) === undefined);\n}\nexports.shouldSkipInstrumentation = shouldSkipInstrumentation;\n// Create a span from our normalized queryConfig object,\n// or return a basic span if no queryConfig was given/could be created.\nfunction handleConfigQuery(tracer, instrumentationConfig, queryConfig) {\n    // Create child span.\n    const { connectionParameters } = this;\n    const dbName = connectionParameters.database;\n    const spanName = getQuerySpanName(dbName, queryConfig);\n    const span = tracer.startSpan(spanName, {\n        kind: api_1.SpanKind.CLIENT,\n        attributes: getSemanticAttributesFromConnection(connectionParameters),\n    });\n    if (!queryConfig) {\n        return span;\n    }\n    // Set attributes\n    if (queryConfig.text) {\n        span.setAttribute(semantic_conventions_1.SEMATTRS_DB_STATEMENT, queryConfig.text);\n    }\n    if (instrumentationConfig.enhancedDatabaseReporting &&\n        Array.isArray(queryConfig.values)) {\n        try {\n            const convertedValues = queryConfig.values.map(value => {\n                if (value == null) {\n                    return 'null';\n                }\n                else if (value instanceof Buffer) {\n                    return value.toString();\n                }\n                else if (typeof value === 'object') {\n                    if (typeof value.toPostgres === 'function') {\n                        return value.toPostgres();\n                    }\n                    return JSON.stringify(value);\n                }\n                else {\n                    //string, number\n                    return value.toString();\n                }\n            });\n            span.setAttribute(AttributeNames_1.AttributeNames.PG_VALUES, convertedValues);\n        }\n        catch (e) {\n            api_1.diag.error('failed to stringify ', queryConfig.values, e);\n        }\n    }\n    // Set plan name attribute, if present\n    if (typeof queryConfig.name === 'string') {\n        span.setAttribute(AttributeNames_1.AttributeNames.PG_PLAN, queryConfig.name);\n    }\n    return span;\n}\nexports.handleConfigQuery = handleConfigQuery;\nfunction handleExecutionResult(config, span, pgResult) {\n    if (typeof config.responseHook === 'function') {\n        (0, instrumentation_1.safeExecuteInTheMiddle)(() => {\n            config.responseHook(span, {\n                data: pgResult,\n            });\n        }, err => {\n            if (err) {\n                api_1.diag.error('Error running response hook', err);\n            }\n        }, true);\n    }\n}\nexports.handleExecutionResult = handleExecutionResult;\nfunction patchCallback(instrumentationConfig, span, cb, attributes, recordDuration) {\n    return function patchedCallback(err, res) {\n        if (err) {\n            if (Object.prototype.hasOwnProperty.call(err, 'code')) {\n                attributes[semantic_conventions_1.ATTR_ERROR_TYPE] = err['code'];\n            }\n            span.setStatus({\n                code: api_1.SpanStatusCode.ERROR,\n                message: err.message,\n            });\n        }\n        else {\n            handleExecutionResult(instrumentationConfig, span, res);\n        }\n        recordDuration();\n        span.end();\n        cb.call(this, err, res);\n    };\n}\nexports.patchCallback = patchCallback;\nfunction getPoolName(pool) {\n    let poolName = '';\n    poolName += ((pool === null || pool === void 0 ? void 0 : pool.host) ? `${pool.host}` : 'unknown_host') + ':';\n    poolName += ((pool === null || pool === void 0 ? void 0 : pool.port) ? `${pool.port}` : 'unknown_port') + '/';\n    poolName += (pool === null || pool === void 0 ? void 0 : pool.database) ? `${pool.database}` : 'unknown_database';\n    return poolName.trim();\n}\nexports.getPoolName = getPoolName;\nfunction updateCounter(poolName, pool, connectionCount, connectionPendingRequests, latestCounter) {\n    const all = pool.totalCount;\n    const pending = pool.waitingCount;\n    const idle = pool.idleCount;\n    const used = all - idle;\n    connectionCount.add(used - latestCounter.used, {\n        [semconv_1.ATTR_DB_CLIENT_CONNECTION_STATE]: semconv_1.DB_CLIENT_CONNECTION_STATE_VALUE_USED,\n        [semconv_1.ATTR_DB_CLIENT_CONNECTION_POOL_NAME]: poolName,\n    });\n    connectionCount.add(idle - latestCounter.idle, {\n        [semconv_1.ATTR_DB_CLIENT_CONNECTION_STATE]: semconv_1.DB_CLIENT_CONNECTION_STATE_VALUE_IDLE,\n        [semconv_1.ATTR_DB_CLIENT_CONNECTION_POOL_NAME]: poolName,\n    });\n    connectionPendingRequests.add(pending - latestCounter.pending, {\n        [semconv_1.ATTR_DB_CLIENT_CONNECTION_POOL_NAME]: poolName,\n    });\n    return { used: used, idle: idle, pending: pending };\n}\nexports.updateCounter = updateCounter;\nfunction patchCallbackPGPool(span, cb) {\n    return function patchedCallback(err, res, done) {\n        if (err) {\n            span.setStatus({\n                code: api_1.SpanStatusCode.ERROR,\n                message: err.message,\n            });\n        }\n        span.end();\n        cb.call(this, err, res, done);\n    };\n}\nexports.patchCallbackPGPool = patchCallbackPGPool;\nfunction patchClientConnectCallback(span, cb) {\n    return function patchedClientConnectCallback(err) {\n        if (err) {\n            span.setStatus({\n                code: api_1.SpanStatusCode.ERROR,\n                message: err.message,\n            });\n        }\n        span.end();\n        cb.apply(this, arguments);\n    };\n}\nexports.patchClientConnectCallback = patchClientConnectCallback;\n/**\n * Attempt to get a message string from a thrown value, while being quite\n * defensive, to recognize the fact that, in JS, any kind of value (even\n * primitives) can be thrown.\n */\nfunction getErrorMessage(e) {\n    return typeof e === 'object' && e !== null && 'message' in e\n        ? String(e.message)\n        : undefined;\n}\nexports.getErrorMessage = getErrorMessage;\nfunction isObjectWithTextString(it) {\n    var _a;\n    return (typeof it === 'object' &&\n        typeof ((_a = it) === null || _a === void 0 ? void 0 : _a.text) === 'string');\n}\nexports.isObjectWithTextString = isObjectWithTextString;\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/utils.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/version.js":
/*!*****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/version.js ***!
  \*****************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.51.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-pg';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/version.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/enums/AttributeNames.js":
/*!******************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/enums/AttributeNames.js ***!
  \******************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.AttributeNames = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// Postgresql specific attributes not covered by semantic conventions\nvar AttributeNames;\n(function (AttributeNames) {\n    AttributeNames[\"PG_VALUES\"] = \"db.postgresql.values\";\n    AttributeNames[\"PG_PLAN\"] = \"db.postgresql.plan\";\n    AttributeNames[\"IDLE_TIMEOUT_MILLIS\"] = \"db.postgresql.idle.timeout.millis\";\n    AttributeNames[\"MAX_CLIENT\"] = \"db.postgresql.max.client\";\n})(AttributeNames = exports.AttributeNames || (exports.AttributeNames = {}));\n//# sourceMappingURL=AttributeNames.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/enums/AttributeNames.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/enums/SpanNames.js":
/*!*************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/enums/SpanNames.js ***!
  \*************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.SpanNames = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// Contains span names produced by instrumentation\nvar SpanNames;\n(function (SpanNames) {\n    SpanNames[\"QUERY_PREFIX\"] = \"pg.query\";\n    SpanNames[\"CONNECT\"] = \"pg.connect\";\n    SpanNames[\"POOL_CONNECT\"] = \"pg-pool.connect\";\n})(SpanNames = exports.SpanNames || (exports.SpanNames = {}));\n//# sourceMappingURL=SpanNames.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/enums/SpanNames.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/index.js":
/*!***************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/index.js ***!
  \***************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/instrumentation.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/types.js\"), exports);\n__exportStar(__webpack_require__(/*! ./enums/AttributeNames */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/enums/AttributeNames.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/instrumentation.js":
/*!*************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/instrumentation.js ***!
  \*************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PgInstrumentation = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst internal_types_1 = __webpack_require__(/*! ./internal-types */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/internal-types.js\");\nconst utils = __webpack_require__(/*! ./utils */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/utils.js\");\nconst sql_common_1 = __webpack_require__(/*! @opentelemetry/sql-common */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+sql-common@0.40.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/sql-common/build/src/index.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/version.js\");\nconst SpanNames_1 = __webpack_require__(/*! ./enums/SpanNames */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/enums/SpanNames.js\");\nconst core_1 = __webpack_require__(/*! @opentelemetry/core */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+core@1.30.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/core/build/esm/index.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst semconv_1 = __webpack_require__(/*! ./semconv */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/semconv.js\");\nfunction extractModuleExports(module) {\n    return module[Symbol.toStringTag] === 'Module'\n        ? module.default // ESM\n        : module; // CommonJS\n}\nclass PgInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, config);\n        // Pool events connect, acquire, release and remove can be called\n        // multiple times without changing the values of total, idle and waiting\n        // connections. The _connectionsCounter is used to keep track of latest\n        // values and only update the metrics _connectionsCount and _connectionPendingRequests\n        // when the value change.\n        this._connectionsCounter = {\n            used: 0,\n            idle: 0,\n            pending: 0,\n        };\n    }\n    _updateMetricInstruments() {\n        this._operationDuration = this.meter.createHistogram(semconv_1.METRIC_DB_CLIENT_OPERATION_DURATION, {\n            description: 'Duration of database client operations.',\n            unit: 's',\n            valueType: api_1.ValueType.DOUBLE,\n            advice: {\n                explicitBucketBoundaries: [\n                    0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1, 5, 10,\n                ],\n            },\n        });\n        this._connectionsCounter = {\n            idle: 0,\n            pending: 0,\n            used: 0,\n        };\n        this._connectionsCount = this.meter.createUpDownCounter(semconv_1.METRIC_DB_CLIENT_CONNECTION_COUNT, {\n            description: 'The number of connections that are currently in state described by the state attribute.',\n            unit: '{connection}',\n        });\n        this._connectionPendingRequests = this.meter.createUpDownCounter(semconv_1.METRIC_DB_CLIENT_CONNECTION_PENDING_REQUESTS, {\n            description: 'The number of current pending requests for an open connection.',\n            unit: '{connection}',\n        });\n    }\n    init() {\n        const SUPPORTED_PG_VERSIONS = ['>=8.0.3 <9'];\n        const modulePgNativeClient = new instrumentation_1.InstrumentationNodeModuleFile('pg/lib/native/client.js', SUPPORTED_PG_VERSIONS, this._patchPgClient.bind(this), this._unpatchPgClient.bind(this));\n        const modulePgClient = new instrumentation_1.InstrumentationNodeModuleFile('pg/lib/client.js', SUPPORTED_PG_VERSIONS, this._patchPgClient.bind(this), this._unpatchPgClient.bind(this));\n        const modulePG = new instrumentation_1.InstrumentationNodeModuleDefinition('pg', SUPPORTED_PG_VERSIONS, (module) => {\n            const moduleExports = extractModuleExports(module);\n            this._patchPgClient(moduleExports.Client);\n            return module;\n        }, (module) => {\n            const moduleExports = extractModuleExports(module);\n            this._unpatchPgClient(moduleExports.Client);\n            return module;\n        }, [modulePgClient, modulePgNativeClient]);\n        const modulePGPool = new instrumentation_1.InstrumentationNodeModuleDefinition('pg-pool', ['>=2.0.0 <4'], (moduleExports) => {\n            if ((0, instrumentation_1.isWrapped)(moduleExports.prototype.connect)) {\n                this._unwrap(moduleExports.prototype, 'connect');\n            }\n            this._wrap(moduleExports.prototype, 'connect', this._getPoolConnectPatch());\n            return moduleExports;\n        }, (moduleExports) => {\n            if ((0, instrumentation_1.isWrapped)(moduleExports.prototype.connect)) {\n                this._unwrap(moduleExports.prototype, 'connect');\n            }\n        });\n        return [modulePG, modulePGPool];\n    }\n    _patchPgClient(module) {\n        if (!module) {\n            return;\n        }\n        const moduleExports = extractModuleExports(module);\n        if ((0, instrumentation_1.isWrapped)(moduleExports.prototype.query)) {\n            this._unwrap(moduleExports.prototype, 'query');\n        }\n        if ((0, instrumentation_1.isWrapped)(moduleExports.prototype.connect)) {\n            this._unwrap(moduleExports.prototype, 'connect');\n        }\n        this._wrap(moduleExports.prototype, 'query', this._getClientQueryPatch());\n        this._wrap(moduleExports.prototype, 'connect', this._getClientConnectPatch());\n        return module;\n    }\n    _unpatchPgClient(module) {\n        const moduleExports = extractModuleExports(module);\n        if ((0, instrumentation_1.isWrapped)(moduleExports.prototype.query)) {\n            this._unwrap(moduleExports.prototype, 'query');\n        }\n        if ((0, instrumentation_1.isWrapped)(moduleExports.prototype.connect)) {\n            this._unwrap(moduleExports.prototype, 'connect');\n        }\n        return module;\n    }\n    _getClientConnectPatch() {\n        const plugin = this;\n        return (original) => {\n            return function connect(callback) {\n                if (utils.shouldSkipInstrumentation(plugin.getConfig())) {\n                    return original.call(this, callback);\n                }\n                const span = plugin.tracer.startSpan(SpanNames_1.SpanNames.CONNECT, {\n                    kind: api_1.SpanKind.CLIENT,\n                    attributes: utils.getSemanticAttributesFromConnection(this),\n                });\n                if (callback) {\n                    const parentSpan = api_1.trace.getSpan(api_1.context.active());\n                    callback = utils.patchClientConnectCallback(span, callback);\n                    if (parentSpan) {\n                        callback = api_1.context.bind(api_1.context.active(), callback);\n                    }\n                }\n                const connectResult = api_1.context.with(api_1.trace.setSpan(api_1.context.active(), span), () => {\n                    return original.call(this, callback);\n                });\n                return handleConnectResult(span, connectResult);\n            };\n        };\n    }\n    recordOperationDuration(attributes, startTime) {\n        const metricsAttributes = {};\n        const keysToCopy = [\n            semantic_conventions_1.SEMATTRS_DB_SYSTEM,\n            semconv_1.ATTR_DB_NAMESPACE,\n            semantic_conventions_1.ATTR_ERROR_TYPE,\n            semantic_conventions_1.ATTR_SERVER_PORT,\n            semantic_conventions_1.ATTR_SERVER_ADDRESS,\n            semconv_1.ATTR_DB_OPERATION_NAME,\n        ];\n        keysToCopy.forEach(key => {\n            if (key in attributes) {\n                metricsAttributes[key] = attributes[key];\n            }\n        });\n        const durationSeconds = (0, core_1.hrTimeToMilliseconds)((0, core_1.hrTimeDuration)(startTime, (0, core_1.hrTime)())) / 1000;\n        this._operationDuration.record(durationSeconds, metricsAttributes);\n    }\n    _getClientQueryPatch() {\n        const plugin = this;\n        return (original) => {\n            this._diag.debug('Patching pg.Client.prototype.query');\n            return function query(...args) {\n                if (utils.shouldSkipInstrumentation(plugin.getConfig())) {\n                    return original.apply(this, args);\n                }\n                const startTime = (0, core_1.hrTime)();\n                // client.query(text, cb?), client.query(text, values, cb?), and\n                // client.query(configObj, cb?) are all valid signatures. We construct\n                // a queryConfig obj from all (valid) signatures to build the span in a\n                // unified way. We verify that we at least have query text, and code\n                // defensively when dealing with `queryConfig` after that (to handle all\n                // the other invalid cases, like a non-array for values being provided).\n                // The type casts here reflect only what we've actually validated.\n                const arg0 = args[0];\n                const firstArgIsString = typeof arg0 === 'string';\n                const firstArgIsQueryObjectWithText = utils.isObjectWithTextString(arg0);\n                // TODO: remove the `as ...` casts below when the TS version is upgraded.\n                // Newer TS versions will use the result of firstArgIsQueryObjectWithText\n                // to properly narrow arg0, but TS 4.3.5 does not.\n                const queryConfig = firstArgIsString\n                    ? {\n                        text: arg0,\n                        values: Array.isArray(args[1]) ? args[1] : undefined,\n                    }\n                    : firstArgIsQueryObjectWithText\n                        ? arg0\n                        : undefined;\n                const attributes = {\n                    [semantic_conventions_1.SEMATTRS_DB_SYSTEM]: semantic_conventions_1.DBSYSTEMVALUES_POSTGRESQL,\n                    [semconv_1.ATTR_DB_NAMESPACE]: this.database,\n                    [semantic_conventions_1.ATTR_SERVER_PORT]: this.connectionParameters.port,\n                    [semantic_conventions_1.ATTR_SERVER_ADDRESS]: this.connectionParameters.host,\n                };\n                if (queryConfig === null || queryConfig === void 0 ? void 0 : queryConfig.text) {\n                    attributes[semconv_1.ATTR_DB_OPERATION_NAME] =\n                        utils.parseNormalizedOperationName(queryConfig === null || queryConfig === void 0 ? void 0 : queryConfig.text);\n                }\n                const recordDuration = () => {\n                    plugin.recordOperationDuration(attributes, startTime);\n                };\n                const instrumentationConfig = plugin.getConfig();\n                const span = utils.handleConfigQuery.call(this, plugin.tracer, instrumentationConfig, queryConfig);\n                // Modify query text w/ a tracing comment before invoking original for\n                // tracing, but only if args[0] has one of our expected shapes.\n                if (instrumentationConfig.addSqlCommenterCommentToQueries) {\n                    if (firstArgIsString) {\n                        args[0] = (0, sql_common_1.addSqlCommenterComment)(span, arg0);\n                    }\n                    else if (firstArgIsQueryObjectWithText && !('name' in arg0)) {\n                        // In the case of a query object, we need to ensure there's no name field\n                        // as this indicates a prepared query, where the comment would remain the same\n                        // for every invocation and contain an outdated trace context.\n                        args[0] = Object.assign(Object.assign({}, arg0), { text: (0, sql_common_1.addSqlCommenterComment)(span, arg0.text) });\n                    }\n                }\n                // Bind callback (if any) to parent span (if any)\n                if (args.length > 0) {\n                    const parentSpan = api_1.trace.getSpan(api_1.context.active());\n                    if (typeof args[args.length - 1] === 'function') {\n                        // Patch ParameterQuery callback\n                        args[args.length - 1] = utils.patchCallback(instrumentationConfig, span, args[args.length - 1], // nb: not type safe.\n                        attributes, recordDuration);\n                        // If a parent span exists, bind the callback\n                        if (parentSpan) {\n                            args[args.length - 1] = api_1.context.bind(api_1.context.active(), args[args.length - 1]);\n                        }\n                    }\n                    else if (typeof (queryConfig === null || queryConfig === void 0 ? void 0 : queryConfig.callback) === 'function') {\n                        // Patch ConfigQuery callback\n                        let callback = utils.patchCallback(plugin.getConfig(), span, queryConfig.callback, // nb: not type safe.\n                        attributes, recordDuration);\n                        // If a parent span existed, bind the callback\n                        if (parentSpan) {\n                            callback = api_1.context.bind(api_1.context.active(), callback);\n                        }\n                        args[0].callback = callback;\n                    }\n                }\n                const { requestHook } = instrumentationConfig;\n                if (typeof requestHook === 'function' && queryConfig) {\n                    (0, instrumentation_1.safeExecuteInTheMiddle)(() => {\n                        // pick keys to expose explicitly, so we're not leaking pg package\n                        // internals that are subject to change\n                        const { database, host, port, user } = this.connectionParameters;\n                        const connection = { database, host, port, user };\n                        requestHook(span, {\n                            connection,\n                            query: {\n                                text: queryConfig.text,\n                                // nb: if `client.query` is called with illegal arguments\n                                // (e.g., if `queryConfig.values` is passed explicitly, but a\n                                // non-array is given), then the type casts will be wrong. But\n                                // we leave it up to the queryHook to handle that, and we\n                                // catch and swallow any errors it throws. The other options\n                                // are all worse. E.g., we could leave `queryConfig.values`\n                                // and `queryConfig.name` as `unknown`, but then the hook body\n                                // would be forced to validate (or cast) them before using\n                                // them, which seems incredibly cumbersome given that these\n                                // casts will be correct 99.9% of the time -- and pg.query\n                                // will immediately throw during development in the other .1%\n                                // of cases. Alternatively, we could simply skip calling the\n                                // hook when `values` or `name` don't have the expected type,\n                                // but that would add unnecessary validation overhead to every\n                                // hook invocation and possibly be even more confusing/unexpected.\n                                values: queryConfig.values,\n                                name: queryConfig.name,\n                            },\n                        });\n                    }, err => {\n                        if (err) {\n                            plugin._diag.error('Error running query hook', err);\n                        }\n                    }, true);\n                }\n                let result;\n                try {\n                    result = original.apply(this, args);\n                }\n                catch (e) {\n                    span.setStatus({\n                        code: api_1.SpanStatusCode.ERROR,\n                        message: utils.getErrorMessage(e),\n                    });\n                    span.end();\n                    throw e;\n                }\n                // Bind promise to parent span and end the span\n                if (result instanceof Promise) {\n                    return result\n                        .then((result) => {\n                        // Return a pass-along promise which ends the span and then goes to user's orig resolvers\n                        return new Promise(resolve => {\n                            utils.handleExecutionResult(plugin.getConfig(), span, result);\n                            recordDuration();\n                            span.end();\n                            resolve(result);\n                        });\n                    })\n                        .catch((error) => {\n                        return new Promise((_, reject) => {\n                            span.setStatus({\n                                code: api_1.SpanStatusCode.ERROR,\n                                message: error.message,\n                            });\n                            recordDuration();\n                            span.end();\n                            reject(error);\n                        });\n                    });\n                }\n                // else returns void\n                return result; // void\n            };\n        };\n    }\n    _setPoolConnectEventListeners(pgPool) {\n        if (pgPool[internal_types_1.EVENT_LISTENERS_SET])\n            return;\n        const poolName = utils.getPoolName(pgPool.options);\n        pgPool.on('connect', () => {\n            this._connectionsCounter = utils.updateCounter(poolName, pgPool, this._connectionsCount, this._connectionPendingRequests, this._connectionsCounter);\n        });\n        pgPool.on('acquire', () => {\n            this._connectionsCounter = utils.updateCounter(poolName, pgPool, this._connectionsCount, this._connectionPendingRequests, this._connectionsCounter);\n        });\n        pgPool.on('remove', () => {\n            this._connectionsCounter = utils.updateCounter(poolName, pgPool, this._connectionsCount, this._connectionPendingRequests, this._connectionsCounter);\n        });\n        pgPool.on('release', () => {\n            this._connectionsCounter = utils.updateCounter(poolName, pgPool, this._connectionsCount, this._connectionPendingRequests, this._connectionsCounter);\n        });\n        pgPool[internal_types_1.EVENT_LISTENERS_SET] = true;\n    }\n    _getPoolConnectPatch() {\n        const plugin = this;\n        return (originalConnect) => {\n            return function connect(callback) {\n                if (utils.shouldSkipInstrumentation(plugin.getConfig())) {\n                    return originalConnect.call(this, callback);\n                }\n                // setup span\n                const span = plugin.tracer.startSpan(SpanNames_1.SpanNames.POOL_CONNECT, {\n                    kind: api_1.SpanKind.CLIENT,\n                    attributes: utils.getSemanticAttributesFromPool(this.options),\n                });\n                plugin._setPoolConnectEventListeners(this);\n                if (callback) {\n                    const parentSpan = api_1.trace.getSpan(api_1.context.active());\n                    callback = utils.patchCallbackPGPool(span, callback);\n                    // If a parent span exists, bind the callback\n                    if (parentSpan) {\n                        callback = api_1.context.bind(api_1.context.active(), callback);\n                    }\n                }\n                const connectResult = api_1.context.with(api_1.trace.setSpan(api_1.context.active(), span), () => {\n                    return originalConnect.call(this, callback);\n                });\n                return handleConnectResult(span, connectResult);\n            };\n        };\n    }\n}\nexports.PgInstrumentation = PgInstrumentation;\nfunction handleConnectResult(span, connectResult) {\n    if (!(connectResult instanceof Promise)) {\n        return connectResult;\n    }\n    const connectResultPromise = connectResult;\n    return api_1.context.bind(api_1.context.active(), connectResultPromise\n        .then(result => {\n        span.end();\n        return result;\n    })\n        .catch((error) => {\n        span.setStatus({\n            code: api_1.SpanStatusCode.ERROR,\n            message: utils.getErrorMessage(error),\n        });\n        span.end();\n        return Promise.reject(error);\n    }));\n}\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/instrumentation.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/internal-types.js":
/*!************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/internal-types.js ***!
  \************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.EVENT_LISTENERS_SET = void 0;\nexports.EVENT_LISTENERS_SET = Symbol('opentelemetry.instrumentation.pg.eventListenersSet');\n//# sourceMappingURL=internal-types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/internal-types.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/semconv.js":
/*!*****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/semconv.js ***!
  \*****************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.METRIC_DB_CLIENT_OPERATION_DURATION = exports.METRIC_DB_CLIENT_CONNECTION_PENDING_REQUESTS = exports.METRIC_DB_CLIENT_CONNECTION_COUNT = exports.DB_CLIENT_CONNECTION_STATE_VALUE_IDLE = exports.DB_CLIENT_CONNECTION_STATE_VALUE_USED = exports.ATTR_DB_OPERATION_NAME = exports.ATTR_DB_NAMESPACE = exports.ATTR_DB_CLIENT_CONNECTION_STATE = exports.ATTR_DB_CLIENT_CONNECTION_POOL_NAME = void 0;\n/**\n * The name of the connection pool; unique within the instrumented application. In case the connection pool implementation doesn't provide a name, instrumentation **SHOULD** use a combination of parameters that would make the name unique, for example, combining attributes `server.address`, `server.port`, and `db.namespace`, formatted as `server.address:server.port/db.namespace`. Instrumentations that generate connection pool name following different patterns **SHOULD** document it.\n *\n * @example myDataSource\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexports.ATTR_DB_CLIENT_CONNECTION_POOL_NAME = 'db.client.connection.pool.name';\n/**\n * The state of a connection in the pool\n *\n * @example idle\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexports.ATTR_DB_CLIENT_CONNECTION_STATE = 'db.client.connection.state';\n/**\n * The name of the database, fully qualified within the server address and port.\n *\n * @example customers\n * @example test.users\n *\n * @note If a database system has multiple namespace components, they **SHOULD** be concatenated (potentially using database system specific conventions) from most general to most specific namespace component, and more specific namespaces **SHOULD NOT** be captured without the more general namespaces, to ensure that \"startswith\" queries for the more general namespaces will be valid.\n * Semantic conventions for individual database systems **SHOULD** document what `db.namespace` means in the context of that system.\n * It is **RECOMMENDED** to capture the value as provided by the application without attempting to do any case normalization.\n * This attribute has stability level RELEASE CANDIDATE.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexports.ATTR_DB_NAMESPACE = 'db.namespace';\n/**\n * The name of the operation or command being executed.\n *\n * @example findAndModify\n * @example HMSET\n * @example SELECT\n *\n * @note It is **RECOMMENDED** to capture the value as provided by the application without attempting to do any case normalization.\n * If the operation name is parsed from the query text, it **SHOULD** be the first operation name found in the query.\n * For batch operations, if the individual operations are known to have the same operation name then that operation name **SHOULD** be used prepended by `BATCH `, otherwise `db.operation.name` **SHOULD** be `BATCH` or some other database system specific term if more applicable.\n * This attribute has stability level RELEASE CANDIDATE.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexports.ATTR_DB_OPERATION_NAME = 'db.operation.name';\n/**\n * Enum value \"used\" for attribute {@link ATTR_DB_CLIENT_CONNECTION_STATE}.\n */\nexports.DB_CLIENT_CONNECTION_STATE_VALUE_USED = 'used';\n/**\n * Enum value \"idle\" for attribute {@link ATTR_DB_CLIENT_CONNECTION_STATE}.\n */\nexports.DB_CLIENT_CONNECTION_STATE_VALUE_IDLE = 'idle';\n/**\n * The number of connections that are currently in state described by the `state` attribute\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexports.METRIC_DB_CLIENT_CONNECTION_COUNT = 'db.client.connection.count';\n/**\n * The number of current pending requests for an open connection\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexports.METRIC_DB_CLIENT_CONNECTION_PENDING_REQUESTS = 'db.client.connection.pending_requests';\n/**\n * Duration of database client operations.\n *\n * @note Batch operations **SHOULD** be recorded as a single operation.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexports.METRIC_DB_CLIENT_OPERATION_DURATION = 'db.client.operation.duration';\n//# sourceMappingURL=semconv.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/semconv.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/types.js":
/*!***************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/types.js ***!
  \***************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF9iOTBjZDFhNTM3ZGFmZTI5NGNlZTk2M2U0NDMyYjQ2YS9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLXBnL2J1aWxkL3NyYy90eXBlcy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbndhclxcRG9jdW1lbnRzXFxDdWJlbnR3ZWJcXGN1YmVudFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQG9wZW50ZWxlbWV0cnkraW5zdHJ1bWVudGF0X2I5MGNkMWE1MzdkYWZlMjk0Y2VlOTYzZTQ0MzJiNDZhXFxub2RlX21vZHVsZXNcXEBvcGVudGVsZW1ldHJ5XFxpbnN0cnVtZW50YXRpb24tcGdcXGJ1aWxkXFxzcmNcXHR5cGVzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuLypcbiAqIENvcHlyaWdodCBUaGUgT3BlblRlbGVtZXRyeSBBdXRob3JzXG4gKlxuICogTGljZW5zZWQgdW5kZXIgdGhlIEFwYWNoZSBMaWNlbnNlLCBWZXJzaW9uIDIuMCAodGhlIFwiTGljZW5zZVwiKTtcbiAqIHlvdSBtYXkgbm90IHVzZSB0aGlzIGZpbGUgZXhjZXB0IGluIGNvbXBsaWFuY2Ugd2l0aCB0aGUgTGljZW5zZS5cbiAqIFlvdSBtYXkgb2J0YWluIGEgY29weSBvZiB0aGUgTGljZW5zZSBhdFxuICpcbiAqICAgICAgaHR0cHM6Ly93d3cuYXBhY2hlLm9yZy9saWNlbnNlcy9MSUNFTlNFLTIuMFxuICpcbiAqIFVubGVzcyByZXF1aXJlZCBieSBhcHBsaWNhYmxlIGxhdyBvciBhZ3JlZWQgdG8gaW4gd3JpdGluZywgc29mdHdhcmVcbiAqIGRpc3RyaWJ1dGVkIHVuZGVyIHRoZSBMaWNlbnNlIGlzIGRpc3RyaWJ1dGVkIG9uIGFuIFwiQVMgSVNcIiBCQVNJUyxcbiAqIFdJVEhPVVQgV0FSUkFOVElFUyBPUiBDT05ESVRJT05TIE9GIEFOWSBLSU5ELCBlaXRoZXIgZXhwcmVzcyBvciBpbXBsaWVkLlxuICogU2VlIHRoZSBMaWNlbnNlIGZvciB0aGUgc3BlY2lmaWMgbGFuZ3VhZ2UgZ292ZXJuaW5nIHBlcm1pc3Npb25zIGFuZFxuICogbGltaXRhdGlvbnMgdW5kZXIgdGhlIExpY2Vuc2UuXG4gKi9cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXR5cGVzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/types.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/utils.js":
/*!***************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/utils.js ***!
  \***************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.isObjectWithTextString = exports.getErrorMessage = exports.patchClientConnectCallback = exports.patchCallbackPGPool = exports.updateCounter = exports.getPoolName = exports.patchCallback = exports.handleExecutionResult = exports.handleConfigQuery = exports.shouldSkipInstrumentation = exports.getSemanticAttributesFromPool = exports.getSemanticAttributesFromConnection = exports.getConnectionString = exports.parseNormalizedOperationName = exports.getQuerySpanName = void 0;\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst AttributeNames_1 = __webpack_require__(/*! ./enums/AttributeNames */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/enums/AttributeNames.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst semconv_1 = __webpack_require__(/*! ./semconv */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/semconv.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst SpanNames_1 = __webpack_require__(/*! ./enums/SpanNames */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/enums/SpanNames.js\");\n/**\n * Helper function to get a low cardinality span name from whatever info we have\n * about the query.\n *\n * This is tricky, because we don't have most of the information (table name,\n * operation name, etc) the spec recommends using to build a low-cardinality\n * value w/o parsing. So, we use db.name and assume that, if the query's a named\n * prepared statement, those `name` values will be low cardinality. If we don't\n * have a named prepared statement, we try to parse an operation (despite the\n * spec's warnings).\n *\n * @params dbName The name of the db against which this query is being issued,\n *   which could be missing if no db name was given at the time that the\n *   connection was established.\n * @params queryConfig Information we have about the query being issued, typed\n *   to reflect only the validation we've actually done on the args to\n *   `client.query()`. This will be undefined if `client.query()` was called\n *   with invalid arguments.\n */\nfunction getQuerySpanName(dbName, queryConfig) {\n    // NB: when the query config is invalid, we omit the dbName too, so that\n    // someone (or some tool) reading the span name doesn't misinterpret the\n    // dbName as being a prepared statement or sql commit name.\n    if (!queryConfig)\n        return SpanNames_1.SpanNames.QUERY_PREFIX;\n    // Either the name of a prepared statement; or an attempted parse\n    // of the SQL command, normalized to uppercase; or unknown.\n    const command = typeof queryConfig.name === 'string' && queryConfig.name\n        ? queryConfig.name\n        : parseNormalizedOperationName(queryConfig.text);\n    return `${SpanNames_1.SpanNames.QUERY_PREFIX}:${command}${dbName ? ` ${dbName}` : ''}`;\n}\nexports.getQuerySpanName = getQuerySpanName;\nfunction parseNormalizedOperationName(queryText) {\n    const indexOfFirstSpace = queryText.indexOf(' ');\n    let sqlCommand = indexOfFirstSpace === -1\n        ? queryText\n        : queryText.slice(0, indexOfFirstSpace);\n    sqlCommand = sqlCommand.toUpperCase();\n    // Handle query text being \"COMMIT;\", which has an extra semicolon before the space.\n    return sqlCommand.endsWith(';') ? sqlCommand.slice(0, -1) : sqlCommand;\n}\nexports.parseNormalizedOperationName = parseNormalizedOperationName;\nfunction getConnectionString(params) {\n    const host = params.host || 'localhost';\n    const port = params.port || 5432;\n    const database = params.database || '';\n    return `postgresql://${host}:${port}/${database}`;\n}\nexports.getConnectionString = getConnectionString;\nfunction getPort(port) {\n    // Port may be NaN as parseInt() is used on the value, passing null will result in NaN being parsed.\n    // https://github.com/brianc/node-postgres/blob/2a8efbee09a284be12748ed3962bc9b816965e36/packages/pg/lib/connection-parameters.js#L66\n    if (Number.isInteger(port)) {\n        return port;\n    }\n    // Unable to find the default used in pg code, so falling back to 'undefined'.\n    return undefined;\n}\nfunction getSemanticAttributesFromConnection(params) {\n    return {\n        [semantic_conventions_1.SEMATTRS_DB_SYSTEM]: semantic_conventions_1.DBSYSTEMVALUES_POSTGRESQL,\n        [semantic_conventions_1.SEMATTRS_DB_NAME]: params.database,\n        [semantic_conventions_1.SEMATTRS_DB_CONNECTION_STRING]: getConnectionString(params),\n        [semantic_conventions_1.SEMATTRS_NET_PEER_NAME]: params.host,\n        [semantic_conventions_1.SEMATTRS_NET_PEER_PORT]: getPort(params.port),\n        [semantic_conventions_1.SEMATTRS_DB_USER]: params.user,\n    };\n}\nexports.getSemanticAttributesFromConnection = getSemanticAttributesFromConnection;\nfunction getSemanticAttributesFromPool(params) {\n    return {\n        [semantic_conventions_1.SEMATTRS_DB_SYSTEM]: semantic_conventions_1.DBSYSTEMVALUES_POSTGRESQL,\n        [semantic_conventions_1.SEMATTRS_DB_NAME]: params.database,\n        [semantic_conventions_1.SEMATTRS_DB_CONNECTION_STRING]: getConnectionString(params),\n        [semantic_conventions_1.SEMATTRS_NET_PEER_NAME]: params.host,\n        [semantic_conventions_1.SEMATTRS_NET_PEER_PORT]: getPort(params.port),\n        [semantic_conventions_1.SEMATTRS_DB_USER]: params.user,\n        [AttributeNames_1.AttributeNames.IDLE_TIMEOUT_MILLIS]: params.idleTimeoutMillis,\n        [AttributeNames_1.AttributeNames.MAX_CLIENT]: params.maxClient,\n    };\n}\nexports.getSemanticAttributesFromPool = getSemanticAttributesFromPool;\nfunction shouldSkipInstrumentation(instrumentationConfig) {\n    return (instrumentationConfig.requireParentSpan === true &&\n        api_1.trace.getSpan(api_1.context.active()) === undefined);\n}\nexports.shouldSkipInstrumentation = shouldSkipInstrumentation;\n// Create a span from our normalized queryConfig object,\n// or return a basic span if no queryConfig was given/could be created.\nfunction handleConfigQuery(tracer, instrumentationConfig, queryConfig) {\n    // Create child span.\n    const { connectionParameters } = this;\n    const dbName = connectionParameters.database;\n    const spanName = getQuerySpanName(dbName, queryConfig);\n    const span = tracer.startSpan(spanName, {\n        kind: api_1.SpanKind.CLIENT,\n        attributes: getSemanticAttributesFromConnection(connectionParameters),\n    });\n    if (!queryConfig) {\n        return span;\n    }\n    // Set attributes\n    if (queryConfig.text) {\n        span.setAttribute(semantic_conventions_1.SEMATTRS_DB_STATEMENT, queryConfig.text);\n    }\n    if (instrumentationConfig.enhancedDatabaseReporting &&\n        Array.isArray(queryConfig.values)) {\n        try {\n            const convertedValues = queryConfig.values.map(value => {\n                if (value == null) {\n                    return 'null';\n                }\n                else if (value instanceof Buffer) {\n                    return value.toString();\n                }\n                else if (typeof value === 'object') {\n                    if (typeof value.toPostgres === 'function') {\n                        return value.toPostgres();\n                    }\n                    return JSON.stringify(value);\n                }\n                else {\n                    //string, number\n                    return value.toString();\n                }\n            });\n            span.setAttribute(AttributeNames_1.AttributeNames.PG_VALUES, convertedValues);\n        }\n        catch (e) {\n            api_1.diag.error('failed to stringify ', queryConfig.values, e);\n        }\n    }\n    // Set plan name attribute, if present\n    if (typeof queryConfig.name === 'string') {\n        span.setAttribute(AttributeNames_1.AttributeNames.PG_PLAN, queryConfig.name);\n    }\n    return span;\n}\nexports.handleConfigQuery = handleConfigQuery;\nfunction handleExecutionResult(config, span, pgResult) {\n    if (typeof config.responseHook === 'function') {\n        (0, instrumentation_1.safeExecuteInTheMiddle)(() => {\n            config.responseHook(span, {\n                data: pgResult,\n            });\n        }, err => {\n            if (err) {\n                api_1.diag.error('Error running response hook', err);\n            }\n        }, true);\n    }\n}\nexports.handleExecutionResult = handleExecutionResult;\nfunction patchCallback(instrumentationConfig, span, cb, attributes, recordDuration) {\n    return function patchedCallback(err, res) {\n        if (err) {\n            if (Object.prototype.hasOwnProperty.call(err, 'code')) {\n                attributes[semantic_conventions_1.ATTR_ERROR_TYPE] = err['code'];\n            }\n            span.setStatus({\n                code: api_1.SpanStatusCode.ERROR,\n                message: err.message,\n            });\n        }\n        else {\n            handleExecutionResult(instrumentationConfig, span, res);\n        }\n        recordDuration();\n        span.end();\n        cb.call(this, err, res);\n    };\n}\nexports.patchCallback = patchCallback;\nfunction getPoolName(pool) {\n    let poolName = '';\n    poolName += ((pool === null || pool === void 0 ? void 0 : pool.host) ? `${pool.host}` : 'unknown_host') + ':';\n    poolName += ((pool === null || pool === void 0 ? void 0 : pool.port) ? `${pool.port}` : 'unknown_port') + '/';\n    poolName += (pool === null || pool === void 0 ? void 0 : pool.database) ? `${pool.database}` : 'unknown_database';\n    return poolName.trim();\n}\nexports.getPoolName = getPoolName;\nfunction updateCounter(poolName, pool, connectionCount, connectionPendingRequests, latestCounter) {\n    const all = pool.totalCount;\n    const pending = pool.waitingCount;\n    const idle = pool.idleCount;\n    const used = all - idle;\n    connectionCount.add(used - latestCounter.used, {\n        [semconv_1.ATTR_DB_CLIENT_CONNECTION_STATE]: semconv_1.DB_CLIENT_CONNECTION_STATE_VALUE_USED,\n        [semconv_1.ATTR_DB_CLIENT_CONNECTION_POOL_NAME]: poolName,\n    });\n    connectionCount.add(idle - latestCounter.idle, {\n        [semconv_1.ATTR_DB_CLIENT_CONNECTION_STATE]: semconv_1.DB_CLIENT_CONNECTION_STATE_VALUE_IDLE,\n        [semconv_1.ATTR_DB_CLIENT_CONNECTION_POOL_NAME]: poolName,\n    });\n    connectionPendingRequests.add(pending - latestCounter.pending, {\n        [semconv_1.ATTR_DB_CLIENT_CONNECTION_POOL_NAME]: poolName,\n    });\n    return { used: used, idle: idle, pending: pending };\n}\nexports.updateCounter = updateCounter;\nfunction patchCallbackPGPool(span, cb) {\n    return function patchedCallback(err, res, done) {\n        if (err) {\n            span.setStatus({\n                code: api_1.SpanStatusCode.ERROR,\n                message: err.message,\n            });\n        }\n        span.end();\n        cb.call(this, err, res, done);\n    };\n}\nexports.patchCallbackPGPool = patchCallbackPGPool;\nfunction patchClientConnectCallback(span, cb) {\n    return function patchedClientConnectCallback(err) {\n        if (err) {\n            span.setStatus({\n                code: api_1.SpanStatusCode.ERROR,\n                message: err.message,\n            });\n        }\n        span.end();\n        cb.apply(this, arguments);\n    };\n}\nexports.patchClientConnectCallback = patchClientConnectCallback;\n/**\n * Attempt to get a message string from a thrown value, while being quite\n * defensive, to recognize the fact that, in JS, any kind of value (even\n * primitives) can be thrown.\n */\nfunction getErrorMessage(e) {\n    return typeof e === 'object' && e !== null && 'message' in e\n        ? String(e.message)\n        : undefined;\n}\nexports.getErrorMessage = getErrorMessage;\nfunction isObjectWithTextString(it) {\n    var _a;\n    return (typeof it === 'object' &&\n        typeof ((_a = it) === null || _a === void 0 ? void 0 : _a.text) === 'string');\n}\nexports.isObjectWithTextString = isObjectWithTextString;\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/utils.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/version.js":
/*!*****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/version.js ***!
  \*****************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.51.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-pg';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/version.js\n");

/***/ })

};
;