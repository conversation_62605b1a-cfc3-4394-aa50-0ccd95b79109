/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6";
exports.ids = ["vendor-chunks/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6"];
exports.modules = {

/***/ "(rsc)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/config.js":
/*!***************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/config.js ***!
  \***************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.config = exports.isEdgeRuntime = exports.isBrowser = exports.isWebWorker = exports.isNetlify = exports.isVercel = exports.isVercelIntegration = exports.Version = void 0;\nconst generic_1 = __importDefault(__webpack_require__(/*! ./platform/generic */ \"(rsc)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/platform/generic.js\"));\nconst vercel_1 = __importDefault(__webpack_require__(/*! ./platform/vercel */ \"(rsc)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/platform/vercel.js\"));\nconst netlify_1 = __importDefault(__webpack_require__(/*! ./platform/netlify */ \"(rsc)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/platform/netlify.js\"));\nexports.Version = __webpack_require__(/*! ../package.json */ \"(rsc)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/package.json\").version;\n// detect if Vercel integration & logdrain is enabled\nexports.isVercelIntegration = process.env.NEXT_PUBLIC_BETTER_STACK_INGESTING_URL || process.env.BETTER_STACK_INGEST_ENDPOINT;\n// detect if app is running on the Vercel platform\nexports.isVercel = process.env.NEXT_PUBLIC_VERCEL || process.env.VERCEL;\nexports.isNetlify = process.env.NETLIFY == 'true';\nexports.isWebWorker = typeof self !== 'undefined' &&\n    typeof globalThis.WorkerGlobalScope !== 'undefined' &&\n    self instanceof WorkerGlobalScope;\nexports.isBrowser = typeof window !== 'undefined' || exports.isWebWorker;\nexports.isEdgeRuntime = globalThis.EdgeRuntime ? true : false;\n// Detect the platform provider, and return the appropriate config\n// fallback to generic config if no provider is detected\nlet config = new generic_1.default();\nexports.config = config;\nif (exports.isVercel) {\n    exports.config = config = new vercel_1.default();\n}\nelse if (exports.isNetlify) {\n    exports.config = config = new netlify_1.default();\n}\n//# sourceMappingURL=config.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0Bsb2d0YWlsK25leHRAMC4yLjBfbmV4dEAxNV9lMTk1OWU3NDFjNmJmNDc2MWY0N2UxYzhlNjgyZmVkNi9ub2RlX21vZHVsZXMvQGxvZ3RhaWwvbmV4dC9kaXN0L2NvbmZpZy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0EsNkNBQTZDO0FBQzdDO0FBQ0EsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELGNBQWMsR0FBRyxxQkFBcUIsR0FBRyxpQkFBaUIsR0FBRyxtQkFBbUIsR0FBRyxpQkFBaUIsR0FBRyxnQkFBZ0IsR0FBRywyQkFBMkIsR0FBRyxlQUFlO0FBQ3ZLLGtDQUFrQyxtQkFBTyxDQUFDLDJLQUFvQjtBQUM5RCxpQ0FBaUMsbUJBQU8sQ0FBQyx5S0FBbUI7QUFDNUQsa0NBQWtDLG1CQUFPLENBQUMsMktBQW9CO0FBQzlELDJNQUFvRDtBQUNwRDtBQUNBLDJCQUEyQjtBQUMzQjtBQUNBLGdCQUFnQjtBQUNoQixpQkFBaUI7QUFDakIsbUJBQW1CO0FBQ25CO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakIscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBLGNBQWM7QUFDZDtBQUNBLElBQUksY0FBYztBQUNsQjtBQUNBO0FBQ0EsSUFBSSxjQUFjO0FBQ2xCO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYW53YXJcXERvY3VtZW50c1xcQ3ViZW50d2ViXFxjdWJlbnRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBsb2d0YWlsK25leHRAMC4yLjBfbmV4dEAxNV9lMTk1OWU3NDFjNmJmNDc2MWY0N2UxYzhlNjgyZmVkNlxcbm9kZV9tb2R1bGVzXFxAbG9ndGFpbFxcbmV4dFxcZGlzdFxcY29uZmlnLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xudmFyIF9faW1wb3J0RGVmYXVsdCA9ICh0aGlzICYmIHRoaXMuX19pbXBvcnREZWZhdWx0KSB8fCBmdW5jdGlvbiAobW9kKSB7XG4gICAgcmV0dXJuIChtb2QgJiYgbW9kLl9fZXNNb2R1bGUpID8gbW9kIDogeyBcImRlZmF1bHRcIjogbW9kIH07XG59O1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5jb25maWcgPSBleHBvcnRzLmlzRWRnZVJ1bnRpbWUgPSBleHBvcnRzLmlzQnJvd3NlciA9IGV4cG9ydHMuaXNXZWJXb3JrZXIgPSBleHBvcnRzLmlzTmV0bGlmeSA9IGV4cG9ydHMuaXNWZXJjZWwgPSBleHBvcnRzLmlzVmVyY2VsSW50ZWdyYXRpb24gPSBleHBvcnRzLlZlcnNpb24gPSB2b2lkIDA7XG5jb25zdCBnZW5lcmljXzEgPSBfX2ltcG9ydERlZmF1bHQocmVxdWlyZShcIi4vcGxhdGZvcm0vZ2VuZXJpY1wiKSk7XG5jb25zdCB2ZXJjZWxfMSA9IF9faW1wb3J0RGVmYXVsdChyZXF1aXJlKFwiLi9wbGF0Zm9ybS92ZXJjZWxcIikpO1xuY29uc3QgbmV0bGlmeV8xID0gX19pbXBvcnREZWZhdWx0KHJlcXVpcmUoXCIuL3BsYXRmb3JtL25ldGxpZnlcIikpO1xuZXhwb3J0cy5WZXJzaW9uID0gcmVxdWlyZSgnLi4vcGFja2FnZS5qc29uJykudmVyc2lvbjtcbi8vIGRldGVjdCBpZiBWZXJjZWwgaW50ZWdyYXRpb24gJiBsb2dkcmFpbiBpcyBlbmFibGVkXG5leHBvcnRzLmlzVmVyY2VsSW50ZWdyYXRpb24gPSBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19CRVRURVJfU1RBQ0tfSU5HRVNUSU5HX1VSTCB8fCBwcm9jZXNzLmVudi5CRVRURVJfU1RBQ0tfSU5HRVNUX0VORFBPSU5UO1xuLy8gZGV0ZWN0IGlmIGFwcCBpcyBydW5uaW5nIG9uIHRoZSBWZXJjZWwgcGxhdGZvcm1cbmV4cG9ydHMuaXNWZXJjZWwgPSBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19WRVJDRUwgfHwgcHJvY2Vzcy5lbnYuVkVSQ0VMO1xuZXhwb3J0cy5pc05ldGxpZnkgPSBwcm9jZXNzLmVudi5ORVRMSUZZID09ICd0cnVlJztcbmV4cG9ydHMuaXNXZWJXb3JrZXIgPSB0eXBlb2Ygc2VsZiAhPT0gJ3VuZGVmaW5lZCcgJiZcbiAgICB0eXBlb2YgZ2xvYmFsVGhpcy5Xb3JrZXJHbG9iYWxTY29wZSAhPT0gJ3VuZGVmaW5lZCcgJiZcbiAgICBzZWxmIGluc3RhbmNlb2YgV29ya2VyR2xvYmFsU2NvcGU7XG5leHBvcnRzLmlzQnJvd3NlciA9IHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnIHx8IGV4cG9ydHMuaXNXZWJXb3JrZXI7XG5leHBvcnRzLmlzRWRnZVJ1bnRpbWUgPSBnbG9iYWxUaGlzLkVkZ2VSdW50aW1lID8gdHJ1ZSA6IGZhbHNlO1xuLy8gRGV0ZWN0IHRoZSBwbGF0Zm9ybSBwcm92aWRlciwgYW5kIHJldHVybiB0aGUgYXBwcm9wcmlhdGUgY29uZmlnXG4vLyBmYWxsYmFjayB0byBnZW5lcmljIGNvbmZpZyBpZiBubyBwcm92aWRlciBpcyBkZXRlY3RlZFxubGV0IGNvbmZpZyA9IG5ldyBnZW5lcmljXzEuZGVmYXVsdCgpO1xuZXhwb3J0cy5jb25maWcgPSBjb25maWc7XG5pZiAoZXhwb3J0cy5pc1ZlcmNlbCkge1xuICAgIGV4cG9ydHMuY29uZmlnID0gY29uZmlnID0gbmV3IHZlcmNlbF8xLmRlZmF1bHQoKTtcbn1cbmVsc2UgaWYgKGV4cG9ydHMuaXNOZXRsaWZ5KSB7XG4gICAgZXhwb3J0cy5jb25maWcgPSBjb25maWcgPSBuZXcgbmV0bGlmeV8xLmRlZmF1bHQoKTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWNvbmZpZy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/config.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/hooks.js":
/*!**************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/hooks.js ***!
  \**************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.useLogger = void 0;\nconst navigation_1 = __webpack_require__(/*! next/navigation */ \"(rsc)/../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/api/navigation.react-server.js\");\nconst logger_1 = __webpack_require__(/*! ./logger */ \"(rsc)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/logger.js\");\nconst react_1 = __webpack_require__(/*! react */ \"(rsc)/../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\nconst use_deep_compare_1 = __webpack_require__(/*! use-deep-compare */ \"(rsc)/../../node_modules/.pnpm/use-deep-compare@1.3.0_react@19.1.0/node_modules/use-deep-compare/dist-web/index.js\");\nfunction useLogger(config = {}) {\n    const path = (0, navigation_1.usePathname)();\n    const memoizedConfig = (0, use_deep_compare_1.useDeepCompareMemo)(() => {\n        var _a;\n        return (Object.assign(Object.assign({}, config), { args: Object.assign(Object.assign({}, ((_a = config.args) !== null && _a !== void 0 ? _a : {})), { path }) }));\n    }, [config, path]);\n    const logger = (0, react_1.useMemo)(() => new logger_1.Logger(memoizedConfig), [memoizedConfig]);\n    (0, react_1.useEffect)(() => {\n        return () => {\n            if (logger) {\n                logger.flush();\n            }\n        };\n    }, [path]);\n    return logger;\n}\nexports.useLogger = useLogger;\n//# sourceMappingURL=hooks.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/hooks.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/index.js":
/*!**************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/index.js ***!
  \**************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.useLogger = exports.withLogtailRouteHandler = exports.withLogtailNextConfig = exports.withLogtail = exports.withBetterStackRouteHandler = exports.withBetterStackNextConfig = exports.withBetterStack = exports.throttle = exports.EndpointType = exports.LogLevel = exports.Logger = exports.log = void 0;\nvar logger_1 = __webpack_require__(/*! ./logger */ \"(rsc)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/logger.js\");\nObject.defineProperty(exports, \"log\", ({ enumerable: true, get: function () { return logger_1.log; } }));\nObject.defineProperty(exports, \"Logger\", ({ enumerable: true, get: function () { return logger_1.Logger; } }));\nObject.defineProperty(exports, \"LogLevel\", ({ enumerable: true, get: function () { return logger_1.LogLevel; } }));\nvar shared_1 = __webpack_require__(/*! ./shared */ \"(rsc)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/shared.js\");\nObject.defineProperty(exports, \"EndpointType\", ({ enumerable: true, get: function () { return shared_1.EndpointType; } }));\nObject.defineProperty(exports, \"throttle\", ({ enumerable: true, get: function () { return shared_1.throttle; } }));\n__exportStar(__webpack_require__(/*! ./platform/base */ \"(rsc)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/platform/base.js\"), exports);\n__exportStar(__webpack_require__(/*! ./config */ \"(rsc)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/config.js\"), exports);\nvar withBetterStack_1 = __webpack_require__(/*! ./withBetterStack */ \"(rsc)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/withBetterStack.js\");\nObject.defineProperty(exports, \"withBetterStack\", ({ enumerable: true, get: function () { return withBetterStack_1.withBetterStack; } }));\nObject.defineProperty(exports, \"withBetterStackNextConfig\", ({ enumerable: true, get: function () { return withBetterStack_1.withBetterStackNextConfig; } }));\nObject.defineProperty(exports, \"withBetterStackRouteHandler\", ({ enumerable: true, get: function () { return withBetterStack_1.withBetterStackRouteHandler; } }));\nObject.defineProperty(exports, \"withLogtail\", ({ enumerable: true, get: function () { return withBetterStack_1.withBetterStack; } }));\nObject.defineProperty(exports, \"withLogtailNextConfig\", ({ enumerable: true, get: function () { return withBetterStack_1.withBetterStackNextConfig; } }));\nObject.defineProperty(exports, \"withLogtailRouteHandler\", ({ enumerable: true, get: function () { return withBetterStack_1.withBetterStackRouteHandler; } }));\n__exportStar(__webpack_require__(/*! ./webVitals */ \"(rsc)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/webVitals/index.js\"), exports);\nvar hooks_1 = __webpack_require__(/*! ./hooks */ \"(rsc)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/hooks.js\");\nObject.defineProperty(exports, \"useLogger\", ({ enumerable: true, get: function () { return hooks_1.useLogger; } }));\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/index.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/logger.js":
/*!***************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/logger.js ***!
  \***************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval("\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.prettyPrint = exports.log = exports.Logger = exports.LogLevel = void 0;\nconst config_1 = __webpack_require__(/*! ./config */ \"(rsc)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/config.js\");\nconst shared_1 = __webpack_require__(/*! ./shared */ \"(rsc)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/shared.js\");\nconst url = config_1.config.getLogsEndpoint();\nconst LOG_LEVEL = process.env.NEXT_PUBLIC_BETTER_STACK_LOG_LEVEL || 'debug';\nvar LogLevel;\n(function (LogLevel) {\n    LogLevel[LogLevel[\"debug\"] = 0] = \"debug\";\n    LogLevel[LogLevel[\"info\"] = 1] = \"info\";\n    LogLevel[LogLevel[\"warn\"] = 2] = \"warn\";\n    LogLevel[LogLevel[\"error\"] = 3] = \"error\";\n    LogLevel[LogLevel[\"off\"] = 100] = \"off\";\n})(LogLevel || (exports.LogLevel = LogLevel = {}));\nclass Logger {\n    constructor(initConfig = {}) {\n        this.initConfig = initConfig;\n        this.logEvents = [];\n        this.throttledSendLogs = (0, shared_1.throttle)(this.sendLogs, 1000);\n        this.children = [];\n        this.logLevel = LogLevel.debug;\n        this.config = {\n            autoFlush: true,\n            source: 'frontend-log',\n            prettyPrint: prettyPrint,\n        };\n        this.debug = (message, args = {}) => {\n            this.log(LogLevel.debug, message, args);\n        };\n        this.info = (message, args = {}) => {\n            this.log(LogLevel.info, message, args);\n        };\n        this.warn = (message, args = {}) => {\n            this.log(LogLevel.warn, message, args);\n        };\n        this.error = (message, args = {}) => {\n            this.log(LogLevel.error, message, args);\n        };\n        this.with = (args) => {\n            const config = Object.assign(Object.assign({}, this.config), { args: Object.assign(Object.assign({}, this.config.args), args) });\n            const child = new Logger(config);\n            this.children.push(child);\n            return child;\n        };\n        this.withRequest = (req) => {\n            return new Logger(Object.assign(Object.assign({}, this.config), { req: Object.assign(Object.assign({}, this.config.req), req) }));\n        };\n        this._transformEvent = (level, message, args = {}) => {\n            const logEvent = {\n                level: LogLevel[level].toString(),\n                message,\n                dt: new Date(Date.now()).toISOString(),\n                source: this.config.source,\n                fields: this.config.args || {},\n                '@app': {\n                    'next-logtail-version': config_1.Version,\n                },\n            };\n            // check if passed args is an object, if its not an object, add it to fields.args\n            if (args instanceof Error) {\n                logEvent.fields = Object.assign(Object.assign({}, logEvent.fields), { message: args.message, stack: args.stack, name: args.name });\n            }\n            else if (typeof args === 'object' && args !== null && Object.keys(args).length > 0) {\n                const parsedArgs = JSON.parse(JSON.stringify(args, jsonFriendlyErrorReplacer));\n                logEvent.fields = Object.assign(Object.assign({}, logEvent.fields), parsedArgs);\n            }\n            else if (args && args.length) {\n                logEvent.fields = Object.assign(Object.assign({}, logEvent.fields), { args: args });\n            }\n            config_1.config.injectPlatformMetadata(logEvent, this.config.source);\n            if (this.config.req != null) {\n                logEvent.request = this.config.req;\n                if (logEvent.platform) {\n                    logEvent.platform.route = this.config.req.path;\n                }\n                else if (logEvent.vercel) {\n                    logEvent.vercel.route = this.config.req.path;\n                }\n            }\n            return logEvent;\n        };\n        this.log = (level, message, args = {}) => {\n            if (level < this.logLevel) {\n                return;\n            }\n            const logEvent = this._transformEvent(level, message, args);\n            this.logEvents.push(logEvent);\n            if (this.config.autoFlush) {\n                this.throttledSendLogs();\n            }\n        };\n        this.attachResponseStatus = (statusCode) => {\n            this.logEvents = this.logEvents.map((log) => {\n                if (log.request) {\n                    log.request.statusCode = statusCode;\n                }\n                return log;\n            });\n        };\n        this.flush = () => __awaiter(this, void 0, void 0, function* () {\n            yield Promise.all([this.sendLogs(), ...this.children.map((c) => c.flush())]);\n        });\n        // check if user passed a log level, if not the default init value will be used as is.\n        if (this.initConfig.logLevel != undefined && this.initConfig.logLevel >= 0) {\n            this.logLevel = this.initConfig.logLevel;\n        }\n        else if (LOG_LEVEL) {\n            this.logLevel = LogLevel[LOG_LEVEL];\n        }\n        this.config = Object.assign(Object.assign({}, this.config), initConfig);\n    }\n    logHttpRequest(level, message, request, args) {\n        const logEvent = this._transformEvent(level, message, args);\n        logEvent.request = request;\n        this.logEvents.push(logEvent);\n        if (this.config.autoFlush) {\n            this.throttledSendLogs();\n        }\n    }\n    middleware(request, config) {\n        var _a;\n        const req = {\n            // @ts-ignore NextRequest.ip was removed in Next 15, works with undefined\n            ip: request.ip,\n            // @ts-ignore NextRequest.ip was removed in Next 15, works with undefined\n            region: (_a = request.geo) === null || _a === void 0 ? void 0 : _a.region,\n            method: request.method,\n            host: request.nextUrl.hostname,\n            path: request.nextUrl.pathname,\n            scheme: request.nextUrl.protocol.split(':')[0],\n            referer: request.headers.get('Referer'),\n            userAgent: request.headers.get('user-agent'),\n        };\n        const message = `${request.method} ${request.nextUrl.pathname}`;\n        if (config === null || config === void 0 ? void 0 : config.logRequestDetails) {\n            return (0, shared_1.requestToJSON)(request).then((details) => {\n                const newReq = Object.assign(Object.assign({}, req), { details: Array.isArray(config.logRequestDetails)\n                        ? Object.fromEntries(Object.entries(details).filter(([key]) => config.logRequestDetails.includes(key)))\n                        : details });\n                return this.logHttpRequest(LogLevel.info, message, newReq, {});\n            });\n        }\n        return this.logHttpRequest(LogLevel.info, message, req, {});\n    }\n    sendLogs() {\n        return __awaiter(this, void 0, void 0, function* () {\n            if (!this.logEvents.length) {\n                return;\n            }\n            // To send logs over the network, we need one of:\n            //\n            // - Ingesting URL and source token\n            // - Custom endpoint\n            //\n            // We fall back to printing to console to avoid network errors in\n            // development environments.\n            if (!config_1.config.isEnvVarsSet()) {\n                this.logEvents.forEach((ev) => (this.config.prettyPrint ? this.config.prettyPrint(ev) : prettyPrint(ev)));\n                this.logEvents = [];\n                return;\n            }\n            const method = 'POST';\n            const keepalive = true;\n            const body = JSON.stringify(this.logEvents);\n            // clear pending logs\n            this.logEvents = [];\n            const headers = {\n                'Content-Type': 'application/json',\n                'User-Agent': 'next-logtail/v' + config_1.Version,\n            };\n            if (config_1.config.token) {\n                headers['Authorization'] = `Bearer ${config_1.config.token}`;\n            }\n            const reqOptions = { body, method, keepalive, headers };\n            function sendFallback() {\n                // Do not leak network errors; does not affect the running app\n                return fetch(url, reqOptions).catch(console.error);\n            }\n            try {\n                if (typeof fetch === 'undefined') {\n                    const fetch = yield __webpack_require__(/*! whatwg-fetch */ \"(rsc)/../../node_modules/.pnpm/whatwg-fetch@3.6.20/node_modules/whatwg-fetch/fetch.js\");\n                    return fetch(url, reqOptions).catch(console.error);\n                }\n                else if (config_1.isBrowser && config_1.isVercel && navigator.sendBeacon) {\n                    // sendBeacon fails if message size is greater than 64kb, so\n                    // we fall back to fetch.\n                    // Navigator has to be bound to ensure it does not error in some browsers\n                    // https://xgwang.me/posts/you-may-not-know-beacon/#it-may-throw-error%2C-be-sure-to-catch\n                    try {\n                        if (!navigator.sendBeacon.bind(navigator)(url, body)) {\n                            return sendFallback();\n                        }\n                    }\n                    catch (error) {\n                        return sendFallback();\n                    }\n                }\n                else {\n                    return sendFallback();\n                }\n            }\n            catch (e) {\n                console.warn(`Failed to send logs to BetterStack: ${e}`);\n                // put the log events back in the queue\n                this.logEvents = [...this.logEvents, JSON.parse(body)];\n            }\n        });\n    }\n}\nexports.Logger = Logger;\nexports.log = new Logger({});\nconst levelColors = {\n    info: {\n        terminal: '32',\n        browser: 'lightgreen',\n    },\n    debug: {\n        terminal: '36',\n        browser: 'lightblue',\n    },\n    warn: {\n        terminal: '33',\n        browser: 'yellow',\n    },\n    error: {\n        terminal: '31',\n        browser: 'red',\n    },\n};\nfunction prettyPrint(ev) {\n    const hasFields = Object.keys(ev.fields).length > 0;\n    // check whether pretty print is disabled\n    if (shared_1.isNoPrettyPrint) {\n        let msg = `${ev.level} - ${ev.message}`;\n        if (hasFields) {\n            msg += ' ' + JSON.stringify(ev.fields);\n        }\n        console.log(msg);\n        return;\n    }\n    // print indented message, instead of [object]\n    // We use the %o modifier instead of JSON.stringify because stringify will print the\n    // object as normal text, it loses all the functionality the browser gives for viewing\n    // objects in the console, such as expanding and collapsing the object.\n    let msgString = '';\n    let args = [ev.level, ev.message];\n    if (config_1.isBrowser) {\n        msgString = '%c%s - %s';\n        args = [`color: ${levelColors[ev.level].browser};`, ...args];\n    }\n    else {\n        msgString = `\\x1b[${levelColors[ev.level].terminal}m%s\\x1b[0m - %s`;\n    }\n    // we check if the fields object is not empty, otherwise its printed as <empty string>\n    // or just \"\".\n    if (hasFields) {\n        msgString += ' %o';\n        args.push(ev.fields);\n    }\n    if (ev.request) {\n        msgString += ' %o';\n        args.push(ev.request);\n    }\n    console.log.apply(console, [msgString, ...args]);\n}\nexports.prettyPrint = prettyPrint;\nfunction jsonFriendlyErrorReplacer(key, value) {\n    if (value instanceof Error) {\n        return Object.assign(Object.assign({}, value), { \n            // Explicitly pull Error's non-enumerable properties\n            name: value.name, message: value.message, stack: value.stack });\n    }\n    return value;\n}\n//# sourceMappingURL=logger.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0Bsb2d0YWlsK25leHRAMC4yLjBfbmV4dEAxNV9lMTk1OWU3NDFjNmJmNDc2MWY0N2UxYzhlNjgyZmVkNi9ub2RlX21vZHVsZXMvQGxvZ3RhaWwvbmV4dC9kaXN0L2xvZ2dlci5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0EsNEJBQTRCLCtEQUErRCxpQkFBaUI7QUFDNUc7QUFDQSxvQ0FBb0MsTUFBTSwrQkFBK0IsWUFBWTtBQUNyRixtQ0FBbUMsTUFBTSxtQ0FBbUMsWUFBWTtBQUN4RixnQ0FBZ0M7QUFDaEM7QUFDQSxLQUFLO0FBQ0w7QUFDQSw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsbUJBQW1CLEdBQUcsV0FBVyxHQUFHLGNBQWMsR0FBRyxnQkFBZ0I7QUFDckUsaUJBQWlCLG1CQUFPLENBQUMsdUpBQVU7QUFDbkMsaUJBQWlCLG1CQUFPLENBQUMsdUpBQVU7QUFDbkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxlQUFlLGdCQUFnQixnQkFBZ0I7QUFDaEQ7QUFDQSwrQkFBK0I7QUFDL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3Q0FBd0M7QUFDeEM7QUFDQTtBQUNBLHVDQUF1QztBQUN2QztBQUNBO0FBQ0EsdUNBQXVDO0FBQ3ZDO0FBQ0E7QUFDQSx3Q0FBd0M7QUFDeEM7QUFDQTtBQUNBO0FBQ0EseURBQXlELGtCQUFrQixvQ0FBb0MsNEJBQTRCO0FBQzNJO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0REFBNEQsa0JBQWtCLG1DQUFtQywwQkFBMEI7QUFDM0k7QUFDQSx5REFBeUQ7QUFDekQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhDQUE4QztBQUM5QztBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBLGdFQUFnRSxzQkFBc0IsMkRBQTJEO0FBQ2pKO0FBQ0E7QUFDQTtBQUNBLGdFQUFnRTtBQUNoRTtBQUNBO0FBQ0EsZ0VBQWdFLHNCQUFzQixZQUFZO0FBQ2xHO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkNBQTZDO0FBQzdDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvREFBb0Q7QUFDcEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJCQUEyQixnQkFBZ0IsRUFBRSx5QkFBeUI7QUFDdEU7QUFDQTtBQUNBLDZEQUE2RCxVQUFVO0FBQ3ZFO0FBQ0EsbUNBQW1DO0FBQ25DLDZFQUE2RTtBQUM3RSxhQUFhO0FBQ2I7QUFDQSxrRUFBa0U7QUFDbEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxREFBcUQsc0JBQXNCO0FBQzNFO0FBQ0EsaUNBQWlDO0FBQ2pDO0FBQ0EsK0NBQStDO0FBQy9DO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0NBQXdDLG1CQUFPLENBQUMsMkdBQWM7QUFDOUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0VBQW9FLEVBQUU7QUFDdEU7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSxjQUFjO0FBQ2QsV0FBVyxnQkFBZ0I7QUFDM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQixVQUFVLElBQUksV0FBVztBQUM5QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMEJBQTBCLCtCQUErQjtBQUN6RDtBQUNBO0FBQ0EsNEJBQTRCLCtCQUErQjtBQUMzRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQjtBQUNuQjtBQUNBO0FBQ0EsNkNBQTZDO0FBQzdDO0FBQ0EsMEVBQTBFO0FBQzFFO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFud2FyXFxEb2N1bWVudHNcXEN1YmVudHdlYlxcY3ViZW50XFxub2RlX21vZHVsZXNcXC5wbnBtXFxAbG9ndGFpbCtuZXh0QDAuMi4wX25leHRAMTVfZTE5NTllNzQxYzZiZjQ3NjFmNDdlMWM4ZTY4MmZlZDZcXG5vZGVfbW9kdWxlc1xcQGxvZ3RhaWxcXG5leHRcXGRpc3RcXGxvZ2dlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbnZhciBfX2F3YWl0ZXIgPSAodGhpcyAmJiB0aGlzLl9fYXdhaXRlcikgfHwgZnVuY3Rpb24gKHRoaXNBcmcsIF9hcmd1bWVudHMsIFAsIGdlbmVyYXRvcikge1xuICAgIGZ1bmN0aW9uIGFkb3B0KHZhbHVlKSB7IHJldHVybiB2YWx1ZSBpbnN0YW5jZW9mIFAgPyB2YWx1ZSA6IG5ldyBQKGZ1bmN0aW9uIChyZXNvbHZlKSB7IHJlc29sdmUodmFsdWUpOyB9KTsgfVxuICAgIHJldHVybiBuZXcgKFAgfHwgKFAgPSBQcm9taXNlKSkoZnVuY3Rpb24gKHJlc29sdmUsIHJlamVjdCkge1xuICAgICAgICBmdW5jdGlvbiBmdWxmaWxsZWQodmFsdWUpIHsgdHJ5IHsgc3RlcChnZW5lcmF0b3IubmV4dCh2YWx1ZSkpOyB9IGNhdGNoIChlKSB7IHJlamVjdChlKTsgfSB9XG4gICAgICAgIGZ1bmN0aW9uIHJlamVjdGVkKHZhbHVlKSB7IHRyeSB7IHN0ZXAoZ2VuZXJhdG9yW1widGhyb3dcIl0odmFsdWUpKTsgfSBjYXRjaCAoZSkgeyByZWplY3QoZSk7IH0gfVxuICAgICAgICBmdW5jdGlvbiBzdGVwKHJlc3VsdCkgeyByZXN1bHQuZG9uZSA/IHJlc29sdmUocmVzdWx0LnZhbHVlKSA6IGFkb3B0KHJlc3VsdC52YWx1ZSkudGhlbihmdWxmaWxsZWQsIHJlamVjdGVkKTsgfVxuICAgICAgICBzdGVwKChnZW5lcmF0b3IgPSBnZW5lcmF0b3IuYXBwbHkodGhpc0FyZywgX2FyZ3VtZW50cyB8fCBbXSkpLm5leHQoKSk7XG4gICAgfSk7XG59O1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5wcmV0dHlQcmludCA9IGV4cG9ydHMubG9nID0gZXhwb3J0cy5Mb2dnZXIgPSBleHBvcnRzLkxvZ0xldmVsID0gdm9pZCAwO1xuY29uc3QgY29uZmlnXzEgPSByZXF1aXJlKFwiLi9jb25maWdcIik7XG5jb25zdCBzaGFyZWRfMSA9IHJlcXVpcmUoXCIuL3NoYXJlZFwiKTtcbmNvbnN0IHVybCA9IGNvbmZpZ18xLmNvbmZpZy5nZXRMb2dzRW5kcG9pbnQoKTtcbmNvbnN0IExPR19MRVZFTCA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0JFVFRFUl9TVEFDS19MT0dfTEVWRUwgfHwgJ2RlYnVnJztcbnZhciBMb2dMZXZlbDtcbihmdW5jdGlvbiAoTG9nTGV2ZWwpIHtcbiAgICBMb2dMZXZlbFtMb2dMZXZlbFtcImRlYnVnXCJdID0gMF0gPSBcImRlYnVnXCI7XG4gICAgTG9nTGV2ZWxbTG9nTGV2ZWxbXCJpbmZvXCJdID0gMV0gPSBcImluZm9cIjtcbiAgICBMb2dMZXZlbFtMb2dMZXZlbFtcIndhcm5cIl0gPSAyXSA9IFwid2FyblwiO1xuICAgIExvZ0xldmVsW0xvZ0xldmVsW1wiZXJyb3JcIl0gPSAzXSA9IFwiZXJyb3JcIjtcbiAgICBMb2dMZXZlbFtMb2dMZXZlbFtcIm9mZlwiXSA9IDEwMF0gPSBcIm9mZlwiO1xufSkoTG9nTGV2ZWwgfHwgKGV4cG9ydHMuTG9nTGV2ZWwgPSBMb2dMZXZlbCA9IHt9KSk7XG5jbGFzcyBMb2dnZXIge1xuICAgIGNvbnN0cnVjdG9yKGluaXRDb25maWcgPSB7fSkge1xuICAgICAgICB0aGlzLmluaXRDb25maWcgPSBpbml0Q29uZmlnO1xuICAgICAgICB0aGlzLmxvZ0V2ZW50cyA9IFtdO1xuICAgICAgICB0aGlzLnRocm90dGxlZFNlbmRMb2dzID0gKDAsIHNoYXJlZF8xLnRocm90dGxlKSh0aGlzLnNlbmRMb2dzLCAxMDAwKTtcbiAgICAgICAgdGhpcy5jaGlsZHJlbiA9IFtdO1xuICAgICAgICB0aGlzLmxvZ0xldmVsID0gTG9nTGV2ZWwuZGVidWc7XG4gICAgICAgIHRoaXMuY29uZmlnID0ge1xuICAgICAgICAgICAgYXV0b0ZsdXNoOiB0cnVlLFxuICAgICAgICAgICAgc291cmNlOiAnZnJvbnRlbmQtbG9nJyxcbiAgICAgICAgICAgIHByZXR0eVByaW50OiBwcmV0dHlQcmludCxcbiAgICAgICAgfTtcbiAgICAgICAgdGhpcy5kZWJ1ZyA9IChtZXNzYWdlLCBhcmdzID0ge30pID0+IHtcbiAgICAgICAgICAgIHRoaXMubG9nKExvZ0xldmVsLmRlYnVnLCBtZXNzYWdlLCBhcmdzKTtcbiAgICAgICAgfTtcbiAgICAgICAgdGhpcy5pbmZvID0gKG1lc3NhZ2UsIGFyZ3MgPSB7fSkgPT4ge1xuICAgICAgICAgICAgdGhpcy5sb2coTG9nTGV2ZWwuaW5mbywgbWVzc2FnZSwgYXJncyk7XG4gICAgICAgIH07XG4gICAgICAgIHRoaXMud2FybiA9IChtZXNzYWdlLCBhcmdzID0ge30pID0+IHtcbiAgICAgICAgICAgIHRoaXMubG9nKExvZ0xldmVsLndhcm4sIG1lc3NhZ2UsIGFyZ3MpO1xuICAgICAgICB9O1xuICAgICAgICB0aGlzLmVycm9yID0gKG1lc3NhZ2UsIGFyZ3MgPSB7fSkgPT4ge1xuICAgICAgICAgICAgdGhpcy5sb2coTG9nTGV2ZWwuZXJyb3IsIG1lc3NhZ2UsIGFyZ3MpO1xuICAgICAgICB9O1xuICAgICAgICB0aGlzLndpdGggPSAoYXJncykgPT4ge1xuICAgICAgICAgICAgY29uc3QgY29uZmlnID0gT2JqZWN0LmFzc2lnbihPYmplY3QuYXNzaWduKHt9LCB0aGlzLmNvbmZpZyksIHsgYXJnczogT2JqZWN0LmFzc2lnbihPYmplY3QuYXNzaWduKHt9LCB0aGlzLmNvbmZpZy5hcmdzKSwgYXJncykgfSk7XG4gICAgICAgICAgICBjb25zdCBjaGlsZCA9IG5ldyBMb2dnZXIoY29uZmlnKTtcbiAgICAgICAgICAgIHRoaXMuY2hpbGRyZW4ucHVzaChjaGlsZCk7XG4gICAgICAgICAgICByZXR1cm4gY2hpbGQ7XG4gICAgICAgIH07XG4gICAgICAgIHRoaXMud2l0aFJlcXVlc3QgPSAocmVxKSA9PiB7XG4gICAgICAgICAgICByZXR1cm4gbmV3IExvZ2dlcihPYmplY3QuYXNzaWduKE9iamVjdC5hc3NpZ24oe30sIHRoaXMuY29uZmlnKSwgeyByZXE6IE9iamVjdC5hc3NpZ24oT2JqZWN0LmFzc2lnbih7fSwgdGhpcy5jb25maWcucmVxKSwgcmVxKSB9KSk7XG4gICAgICAgIH07XG4gICAgICAgIHRoaXMuX3RyYW5zZm9ybUV2ZW50ID0gKGxldmVsLCBtZXNzYWdlLCBhcmdzID0ge30pID0+IHtcbiAgICAgICAgICAgIGNvbnN0IGxvZ0V2ZW50ID0ge1xuICAgICAgICAgICAgICAgIGxldmVsOiBMb2dMZXZlbFtsZXZlbF0udG9TdHJpbmcoKSxcbiAgICAgICAgICAgICAgICBtZXNzYWdlLFxuICAgICAgICAgICAgICAgIGR0OiBuZXcgRGF0ZShEYXRlLm5vdygpKS50b0lTT1N0cmluZygpLFxuICAgICAgICAgICAgICAgIHNvdXJjZTogdGhpcy5jb25maWcuc291cmNlLFxuICAgICAgICAgICAgICAgIGZpZWxkczogdGhpcy5jb25maWcuYXJncyB8fCB7fSxcbiAgICAgICAgICAgICAgICAnQGFwcCc6IHtcbiAgICAgICAgICAgICAgICAgICAgJ25leHQtbG9ndGFpbC12ZXJzaW9uJzogY29uZmlnXzEuVmVyc2lvbixcbiAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgfTtcbiAgICAgICAgICAgIC8vIGNoZWNrIGlmIHBhc3NlZCBhcmdzIGlzIGFuIG9iamVjdCwgaWYgaXRzIG5vdCBhbiBvYmplY3QsIGFkZCBpdCB0byBmaWVsZHMuYXJnc1xuICAgICAgICAgICAgaWYgKGFyZ3MgaW5zdGFuY2VvZiBFcnJvcikge1xuICAgICAgICAgICAgICAgIGxvZ0V2ZW50LmZpZWxkcyA9IE9iamVjdC5hc3NpZ24oT2JqZWN0LmFzc2lnbih7fSwgbG9nRXZlbnQuZmllbGRzKSwgeyBtZXNzYWdlOiBhcmdzLm1lc3NhZ2UsIHN0YWNrOiBhcmdzLnN0YWNrLCBuYW1lOiBhcmdzLm5hbWUgfSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIGlmICh0eXBlb2YgYXJncyA9PT0gJ29iamVjdCcgJiYgYXJncyAhPT0gbnVsbCAmJiBPYmplY3Qua2V5cyhhcmdzKS5sZW5ndGggPiAwKSB7XG4gICAgICAgICAgICAgICAgY29uc3QgcGFyc2VkQXJncyA9IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkoYXJncywganNvbkZyaWVuZGx5RXJyb3JSZXBsYWNlcikpO1xuICAgICAgICAgICAgICAgIGxvZ0V2ZW50LmZpZWxkcyA9IE9iamVjdC5hc3NpZ24oT2JqZWN0LmFzc2lnbih7fSwgbG9nRXZlbnQuZmllbGRzKSwgcGFyc2VkQXJncyk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIGlmIChhcmdzICYmIGFyZ3MubGVuZ3RoKSB7XG4gICAgICAgICAgICAgICAgbG9nRXZlbnQuZmllbGRzID0gT2JqZWN0LmFzc2lnbihPYmplY3QuYXNzaWduKHt9LCBsb2dFdmVudC5maWVsZHMpLCB7IGFyZ3M6IGFyZ3MgfSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjb25maWdfMS5jb25maWcuaW5qZWN0UGxhdGZvcm1NZXRhZGF0YShsb2dFdmVudCwgdGhpcy5jb25maWcuc291cmNlKTtcbiAgICAgICAgICAgIGlmICh0aGlzLmNvbmZpZy5yZXEgIT0gbnVsbCkge1xuICAgICAgICAgICAgICAgIGxvZ0V2ZW50LnJlcXVlc3QgPSB0aGlzLmNvbmZpZy5yZXE7XG4gICAgICAgICAgICAgICAgaWYgKGxvZ0V2ZW50LnBsYXRmb3JtKSB7XG4gICAgICAgICAgICAgICAgICAgIGxvZ0V2ZW50LnBsYXRmb3JtLnJvdXRlID0gdGhpcy5jb25maWcucmVxLnBhdGg7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGVsc2UgaWYgKGxvZ0V2ZW50LnZlcmNlbCkge1xuICAgICAgICAgICAgICAgICAgICBsb2dFdmVudC52ZXJjZWwucm91dGUgPSB0aGlzLmNvbmZpZy5yZXEucGF0aDtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm4gbG9nRXZlbnQ7XG4gICAgICAgIH07XG4gICAgICAgIHRoaXMubG9nID0gKGxldmVsLCBtZXNzYWdlLCBhcmdzID0ge30pID0+IHtcbiAgICAgICAgICAgIGlmIChsZXZlbCA8IHRoaXMubG9nTGV2ZWwpIHtcbiAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjb25zdCBsb2dFdmVudCA9IHRoaXMuX3RyYW5zZm9ybUV2ZW50KGxldmVsLCBtZXNzYWdlLCBhcmdzKTtcbiAgICAgICAgICAgIHRoaXMubG9nRXZlbnRzLnB1c2gobG9nRXZlbnQpO1xuICAgICAgICAgICAgaWYgKHRoaXMuY29uZmlnLmF1dG9GbHVzaCkge1xuICAgICAgICAgICAgICAgIHRoaXMudGhyb3R0bGVkU2VuZExvZ3MoKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfTtcbiAgICAgICAgdGhpcy5hdHRhY2hSZXNwb25zZVN0YXR1cyA9IChzdGF0dXNDb2RlKSA9PiB7XG4gICAgICAgICAgICB0aGlzLmxvZ0V2ZW50cyA9IHRoaXMubG9nRXZlbnRzLm1hcCgobG9nKSA9PiB7XG4gICAgICAgICAgICAgICAgaWYgKGxvZy5yZXF1ZXN0KSB7XG4gICAgICAgICAgICAgICAgICAgIGxvZy5yZXF1ZXN0LnN0YXR1c0NvZGUgPSBzdGF0dXNDb2RlO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICByZXR1cm4gbG9nO1xuICAgICAgICAgICAgfSk7XG4gICAgICAgIH07XG4gICAgICAgIHRoaXMuZmx1c2ggPSAoKSA9PiBfX2F3YWl0ZXIodGhpcywgdm9pZCAwLCB2b2lkIDAsIGZ1bmN0aW9uKiAoKSB7XG4gICAgICAgICAgICB5aWVsZCBQcm9taXNlLmFsbChbdGhpcy5zZW5kTG9ncygpLCAuLi50aGlzLmNoaWxkcmVuLm1hcCgoYykgPT4gYy5mbHVzaCgpKV0pO1xuICAgICAgICB9KTtcbiAgICAgICAgLy8gY2hlY2sgaWYgdXNlciBwYXNzZWQgYSBsb2cgbGV2ZWwsIGlmIG5vdCB0aGUgZGVmYXVsdCBpbml0IHZhbHVlIHdpbGwgYmUgdXNlZCBhcyBpcy5cbiAgICAgICAgaWYgKHRoaXMuaW5pdENvbmZpZy5sb2dMZXZlbCAhPSB1bmRlZmluZWQgJiYgdGhpcy5pbml0Q29uZmlnLmxvZ0xldmVsID49IDApIHtcbiAgICAgICAgICAgIHRoaXMubG9nTGV2ZWwgPSB0aGlzLmluaXRDb25maWcubG9nTGV2ZWw7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAoTE9HX0xFVkVMKSB7XG4gICAgICAgICAgICB0aGlzLmxvZ0xldmVsID0gTG9nTGV2ZWxbTE9HX0xFVkVMXTtcbiAgICAgICAgfVxuICAgICAgICB0aGlzLmNvbmZpZyA9IE9iamVjdC5hc3NpZ24oT2JqZWN0LmFzc2lnbih7fSwgdGhpcy5jb25maWcpLCBpbml0Q29uZmlnKTtcbiAgICB9XG4gICAgbG9nSHR0cFJlcXVlc3QobGV2ZWwsIG1lc3NhZ2UsIHJlcXVlc3QsIGFyZ3MpIHtcbiAgICAgICAgY29uc3QgbG9nRXZlbnQgPSB0aGlzLl90cmFuc2Zvcm1FdmVudChsZXZlbCwgbWVzc2FnZSwgYXJncyk7XG4gICAgICAgIGxvZ0V2ZW50LnJlcXVlc3QgPSByZXF1ZXN0O1xuICAgICAgICB0aGlzLmxvZ0V2ZW50cy5wdXNoKGxvZ0V2ZW50KTtcbiAgICAgICAgaWYgKHRoaXMuY29uZmlnLmF1dG9GbHVzaCkge1xuICAgICAgICAgICAgdGhpcy50aHJvdHRsZWRTZW5kTG9ncygpO1xuICAgICAgICB9XG4gICAgfVxuICAgIG1pZGRsZXdhcmUocmVxdWVzdCwgY29uZmlnKSB7XG4gICAgICAgIHZhciBfYTtcbiAgICAgICAgY29uc3QgcmVxID0ge1xuICAgICAgICAgICAgLy8gQHRzLWlnbm9yZSBOZXh0UmVxdWVzdC5pcCB3YXMgcmVtb3ZlZCBpbiBOZXh0IDE1LCB3b3JrcyB3aXRoIHVuZGVmaW5lZFxuICAgICAgICAgICAgaXA6IHJlcXVlc3QuaXAsXG4gICAgICAgICAgICAvLyBAdHMtaWdub3JlIE5leHRSZXF1ZXN0LmlwIHdhcyByZW1vdmVkIGluIE5leHQgMTUsIHdvcmtzIHdpdGggdW5kZWZpbmVkXG4gICAgICAgICAgICByZWdpb246IChfYSA9IHJlcXVlc3QuZ2VvKSA9PT0gbnVsbCB8fCBfYSA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2EucmVnaW9uLFxuICAgICAgICAgICAgbWV0aG9kOiByZXF1ZXN0Lm1ldGhvZCxcbiAgICAgICAgICAgIGhvc3Q6IHJlcXVlc3QubmV4dFVybC5ob3N0bmFtZSxcbiAgICAgICAgICAgIHBhdGg6IHJlcXVlc3QubmV4dFVybC5wYXRobmFtZSxcbiAgICAgICAgICAgIHNjaGVtZTogcmVxdWVzdC5uZXh0VXJsLnByb3RvY29sLnNwbGl0KCc6JylbMF0sXG4gICAgICAgICAgICByZWZlcmVyOiByZXF1ZXN0LmhlYWRlcnMuZ2V0KCdSZWZlcmVyJyksXG4gICAgICAgICAgICB1c2VyQWdlbnQ6IHJlcXVlc3QuaGVhZGVycy5nZXQoJ3VzZXItYWdlbnQnKSxcbiAgICAgICAgfTtcbiAgICAgICAgY29uc3QgbWVzc2FnZSA9IGAke3JlcXVlc3QubWV0aG9kfSAke3JlcXVlc3QubmV4dFVybC5wYXRobmFtZX1gO1xuICAgICAgICBpZiAoY29uZmlnID09PSBudWxsIHx8IGNvbmZpZyA9PT0gdm9pZCAwID8gdm9pZCAwIDogY29uZmlnLmxvZ1JlcXVlc3REZXRhaWxzKSB7XG4gICAgICAgICAgICByZXR1cm4gKDAsIHNoYXJlZF8xLnJlcXVlc3RUb0pTT04pKHJlcXVlc3QpLnRoZW4oKGRldGFpbHMpID0+IHtcbiAgICAgICAgICAgICAgICBjb25zdCBuZXdSZXEgPSBPYmplY3QuYXNzaWduKE9iamVjdC5hc3NpZ24oe30sIHJlcSksIHsgZGV0YWlsczogQXJyYXkuaXNBcnJheShjb25maWcubG9nUmVxdWVzdERldGFpbHMpXG4gICAgICAgICAgICAgICAgICAgICAgICA/IE9iamVjdC5mcm9tRW50cmllcyhPYmplY3QuZW50cmllcyhkZXRhaWxzKS5maWx0ZXIoKFtrZXldKSA9PiBjb25maWcubG9nUmVxdWVzdERldGFpbHMuaW5jbHVkZXMoa2V5KSkpXG4gICAgICAgICAgICAgICAgICAgICAgICA6IGRldGFpbHMgfSk7XG4gICAgICAgICAgICAgICAgcmV0dXJuIHRoaXMubG9nSHR0cFJlcXVlc3QoTG9nTGV2ZWwuaW5mbywgbWVzc2FnZSwgbmV3UmVxLCB7fSk7XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gdGhpcy5sb2dIdHRwUmVxdWVzdChMb2dMZXZlbC5pbmZvLCBtZXNzYWdlLCByZXEsIHt9KTtcbiAgICB9XG4gICAgc2VuZExvZ3MoKSB7XG4gICAgICAgIHJldHVybiBfX2F3YWl0ZXIodGhpcywgdm9pZCAwLCB2b2lkIDAsIGZ1bmN0aW9uKiAoKSB7XG4gICAgICAgICAgICBpZiAoIXRoaXMubG9nRXZlbnRzLmxlbmd0aCkge1xuICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIC8vIFRvIHNlbmQgbG9ncyBvdmVyIHRoZSBuZXR3b3JrLCB3ZSBuZWVkIG9uZSBvZjpcbiAgICAgICAgICAgIC8vXG4gICAgICAgICAgICAvLyAtIEluZ2VzdGluZyBVUkwgYW5kIHNvdXJjZSB0b2tlblxuICAgICAgICAgICAgLy8gLSBDdXN0b20gZW5kcG9pbnRcbiAgICAgICAgICAgIC8vXG4gICAgICAgICAgICAvLyBXZSBmYWxsIGJhY2sgdG8gcHJpbnRpbmcgdG8gY29uc29sZSB0byBhdm9pZCBuZXR3b3JrIGVycm9ycyBpblxuICAgICAgICAgICAgLy8gZGV2ZWxvcG1lbnQgZW52aXJvbm1lbnRzLlxuICAgICAgICAgICAgaWYgKCFjb25maWdfMS5jb25maWcuaXNFbnZWYXJzU2V0KCkpIHtcbiAgICAgICAgICAgICAgICB0aGlzLmxvZ0V2ZW50cy5mb3JFYWNoKChldikgPT4gKHRoaXMuY29uZmlnLnByZXR0eVByaW50ID8gdGhpcy5jb25maWcucHJldHR5UHJpbnQoZXYpIDogcHJldHR5UHJpbnQoZXYpKSk7XG4gICAgICAgICAgICAgICAgdGhpcy5sb2dFdmVudHMgPSBbXTtcbiAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjb25zdCBtZXRob2QgPSAnUE9TVCc7XG4gICAgICAgICAgICBjb25zdCBrZWVwYWxpdmUgPSB0cnVlO1xuICAgICAgICAgICAgY29uc3QgYm9keSA9IEpTT04uc3RyaW5naWZ5KHRoaXMubG9nRXZlbnRzKTtcbiAgICAgICAgICAgIC8vIGNsZWFyIHBlbmRpbmcgbG9nc1xuICAgICAgICAgICAgdGhpcy5sb2dFdmVudHMgPSBbXTtcbiAgICAgICAgICAgIGNvbnN0IGhlYWRlcnMgPSB7XG4gICAgICAgICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgICAgICAgICAnVXNlci1BZ2VudCc6ICduZXh0LWxvZ3RhaWwvdicgKyBjb25maWdfMS5WZXJzaW9uLFxuICAgICAgICAgICAgfTtcbiAgICAgICAgICAgIGlmIChjb25maWdfMS5jb25maWcudG9rZW4pIHtcbiAgICAgICAgICAgICAgICBoZWFkZXJzWydBdXRob3JpemF0aW9uJ10gPSBgQmVhcmVyICR7Y29uZmlnXzEuY29uZmlnLnRva2VufWA7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjb25zdCByZXFPcHRpb25zID0geyBib2R5LCBtZXRob2QsIGtlZXBhbGl2ZSwgaGVhZGVycyB9O1xuICAgICAgICAgICAgZnVuY3Rpb24gc2VuZEZhbGxiYWNrKCkge1xuICAgICAgICAgICAgICAgIC8vIERvIG5vdCBsZWFrIG5ldHdvcmsgZXJyb3JzOyBkb2VzIG5vdCBhZmZlY3QgdGhlIHJ1bm5pbmcgYXBwXG4gICAgICAgICAgICAgICAgcmV0dXJuIGZldGNoKHVybCwgcmVxT3B0aW9ucykuY2F0Y2goY29uc29sZS5lcnJvcik7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgIGlmICh0eXBlb2YgZmV0Y2ggPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGZldGNoID0geWllbGQgcmVxdWlyZSgnd2hhdHdnLWZldGNoJyk7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBmZXRjaCh1cmwsIHJlcU9wdGlvbnMpLmNhdGNoKGNvbnNvbGUuZXJyb3IpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBlbHNlIGlmIChjb25maWdfMS5pc0Jyb3dzZXIgJiYgY29uZmlnXzEuaXNWZXJjZWwgJiYgbmF2aWdhdG9yLnNlbmRCZWFjb24pIHtcbiAgICAgICAgICAgICAgICAgICAgLy8gc2VuZEJlYWNvbiBmYWlscyBpZiBtZXNzYWdlIHNpemUgaXMgZ3JlYXRlciB0aGFuIDY0a2IsIHNvXG4gICAgICAgICAgICAgICAgICAgIC8vIHdlIGZhbGwgYmFjayB0byBmZXRjaC5cbiAgICAgICAgICAgICAgICAgICAgLy8gTmF2aWdhdG9yIGhhcyB0byBiZSBib3VuZCB0byBlbnN1cmUgaXQgZG9lcyBub3QgZXJyb3IgaW4gc29tZSBicm93c2Vyc1xuICAgICAgICAgICAgICAgICAgICAvLyBodHRwczovL3hnd2FuZy5tZS9wb3N0cy95b3UtbWF5LW5vdC1rbm93LWJlYWNvbi8jaXQtbWF5LXRocm93LWVycm9yJTJDLWJlLXN1cmUtdG8tY2F0Y2hcbiAgICAgICAgICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmICghbmF2aWdhdG9yLnNlbmRCZWFjb24uYmluZChuYXZpZ2F0b3IpKHVybCwgYm9keSkpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gc2VuZEZhbGxiYWNrKCk7XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gc2VuZEZhbGxiYWNrKCk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBzZW5kRmFsbGJhY2soKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjYXRjaCAoZSkge1xuICAgICAgICAgICAgICAgIGNvbnNvbGUud2FybihgRmFpbGVkIHRvIHNlbmQgbG9ncyB0byBCZXR0ZXJTdGFjazogJHtlfWApO1xuICAgICAgICAgICAgICAgIC8vIHB1dCB0aGUgbG9nIGV2ZW50cyBiYWNrIGluIHRoZSBxdWV1ZVxuICAgICAgICAgICAgICAgIHRoaXMubG9nRXZlbnRzID0gWy4uLnRoaXMubG9nRXZlbnRzLCBKU09OLnBhcnNlKGJvZHkpXTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSk7XG4gICAgfVxufVxuZXhwb3J0cy5Mb2dnZXIgPSBMb2dnZXI7XG5leHBvcnRzLmxvZyA9IG5ldyBMb2dnZXIoe30pO1xuY29uc3QgbGV2ZWxDb2xvcnMgPSB7XG4gICAgaW5mbzoge1xuICAgICAgICB0ZXJtaW5hbDogJzMyJyxcbiAgICAgICAgYnJvd3NlcjogJ2xpZ2h0Z3JlZW4nLFxuICAgIH0sXG4gICAgZGVidWc6IHtcbiAgICAgICAgdGVybWluYWw6ICczNicsXG4gICAgICAgIGJyb3dzZXI6ICdsaWdodGJsdWUnLFxuICAgIH0sXG4gICAgd2Fybjoge1xuICAgICAgICB0ZXJtaW5hbDogJzMzJyxcbiAgICAgICAgYnJvd3NlcjogJ3llbGxvdycsXG4gICAgfSxcbiAgICBlcnJvcjoge1xuICAgICAgICB0ZXJtaW5hbDogJzMxJyxcbiAgICAgICAgYnJvd3NlcjogJ3JlZCcsXG4gICAgfSxcbn07XG5mdW5jdGlvbiBwcmV0dHlQcmludChldikge1xuICAgIGNvbnN0IGhhc0ZpZWxkcyA9IE9iamVjdC5rZXlzKGV2LmZpZWxkcykubGVuZ3RoID4gMDtcbiAgICAvLyBjaGVjayB3aGV0aGVyIHByZXR0eSBwcmludCBpcyBkaXNhYmxlZFxuICAgIGlmIChzaGFyZWRfMS5pc05vUHJldHR5UHJpbnQpIHtcbiAgICAgICAgbGV0IG1zZyA9IGAke2V2LmxldmVsfSAtICR7ZXYubWVzc2FnZX1gO1xuICAgICAgICBpZiAoaGFzRmllbGRzKSB7XG4gICAgICAgICAgICBtc2cgKz0gJyAnICsgSlNPTi5zdHJpbmdpZnkoZXYuZmllbGRzKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zb2xlLmxvZyhtc2cpO1xuICAgICAgICByZXR1cm47XG4gICAgfVxuICAgIC8vIHByaW50IGluZGVudGVkIG1lc3NhZ2UsIGluc3RlYWQgb2YgW29iamVjdF1cbiAgICAvLyBXZSB1c2UgdGhlICVvIG1vZGlmaWVyIGluc3RlYWQgb2YgSlNPTi5zdHJpbmdpZnkgYmVjYXVzZSBzdHJpbmdpZnkgd2lsbCBwcmludCB0aGVcbiAgICAvLyBvYmplY3QgYXMgbm9ybWFsIHRleHQsIGl0IGxvc2VzIGFsbCB0aGUgZnVuY3Rpb25hbGl0eSB0aGUgYnJvd3NlciBnaXZlcyBmb3Igdmlld2luZ1xuICAgIC8vIG9iamVjdHMgaW4gdGhlIGNvbnNvbGUsIHN1Y2ggYXMgZXhwYW5kaW5nIGFuZCBjb2xsYXBzaW5nIHRoZSBvYmplY3QuXG4gICAgbGV0IG1zZ1N0cmluZyA9ICcnO1xuICAgIGxldCBhcmdzID0gW2V2LmxldmVsLCBldi5tZXNzYWdlXTtcbiAgICBpZiAoY29uZmlnXzEuaXNCcm93c2VyKSB7XG4gICAgICAgIG1zZ1N0cmluZyA9ICclYyVzIC0gJXMnO1xuICAgICAgICBhcmdzID0gW2Bjb2xvcjogJHtsZXZlbENvbG9yc1tldi5sZXZlbF0uYnJvd3Nlcn07YCwgLi4uYXJnc107XG4gICAgfVxuICAgIGVsc2Uge1xuICAgICAgICBtc2dTdHJpbmcgPSBgXFx4MWJbJHtsZXZlbENvbG9yc1tldi5sZXZlbF0udGVybWluYWx9bSVzXFx4MWJbMG0gLSAlc2A7XG4gICAgfVxuICAgIC8vIHdlIGNoZWNrIGlmIHRoZSBmaWVsZHMgb2JqZWN0IGlzIG5vdCBlbXB0eSwgb3RoZXJ3aXNlIGl0cyBwcmludGVkIGFzIDxlbXB0eSBzdHJpbmc+XG4gICAgLy8gb3IganVzdCBcIlwiLlxuICAgIGlmIChoYXNGaWVsZHMpIHtcbiAgICAgICAgbXNnU3RyaW5nICs9ICcgJW8nO1xuICAgICAgICBhcmdzLnB1c2goZXYuZmllbGRzKTtcbiAgICB9XG4gICAgaWYgKGV2LnJlcXVlc3QpIHtcbiAgICAgICAgbXNnU3RyaW5nICs9ICcgJW8nO1xuICAgICAgICBhcmdzLnB1c2goZXYucmVxdWVzdCk7XG4gICAgfVxuICAgIGNvbnNvbGUubG9nLmFwcGx5KGNvbnNvbGUsIFttc2dTdHJpbmcsIC4uLmFyZ3NdKTtcbn1cbmV4cG9ydHMucHJldHR5UHJpbnQgPSBwcmV0dHlQcmludDtcbmZ1bmN0aW9uIGpzb25GcmllbmRseUVycm9yUmVwbGFjZXIoa2V5LCB2YWx1ZSkge1xuICAgIGlmICh2YWx1ZSBpbnN0YW5jZW9mIEVycm9yKSB7XG4gICAgICAgIHJldHVybiBPYmplY3QuYXNzaWduKE9iamVjdC5hc3NpZ24oe30sIHZhbHVlKSwgeyBcbiAgICAgICAgICAgIC8vIEV4cGxpY2l0bHkgcHVsbCBFcnJvcidzIG5vbi1lbnVtZXJhYmxlIHByb3BlcnRpZXNcbiAgICAgICAgICAgIG5hbWU6IHZhbHVlLm5hbWUsIG1lc3NhZ2U6IHZhbHVlLm1lc3NhZ2UsIHN0YWNrOiB2YWx1ZS5zdGFjayB9KTtcbiAgICB9XG4gICAgcmV0dXJuIHZhbHVlO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bG9nZ2VyLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/logger.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/platform/base.js":
/*!**********************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/platform/base.js ***!
  \**********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=base.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0Bsb2d0YWlsK25leHRAMC4yLjBfbmV4dEAxNV9lMTk1OWU3NDFjNmJmNDc2MWY0N2UxYzhlNjgyZmVkNi9ub2RlX21vZHVsZXMvQGxvZ3RhaWwvbmV4dC9kaXN0L3BsYXRmb3JtL2Jhc2UuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYW53YXJcXERvY3VtZW50c1xcQ3ViZW50d2ViXFxjdWJlbnRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBsb2d0YWlsK25leHRAMC4yLjBfbmV4dEAxNV9lMTk1OWU3NDFjNmJmNDc2MWY0N2UxYzhlNjgyZmVkNlxcbm9kZV9tb2R1bGVzXFxAbG9ndGFpbFxcbmV4dFxcZGlzdFxccGxhdGZvcm1cXGJhc2UuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1iYXNlLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/platform/base.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/platform/generic.js":
/*!*************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/platform/generic.js ***!
  \*************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst shared_1 = __webpack_require__(/*! ../shared */ \"(rsc)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/shared.js\");\nconst config_1 = __webpack_require__(/*! ../config */ \"(rsc)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/config.js\");\n// This is the generic config class for all platforms that doesn't have a special\n// implementation (e.g: vercel, netlify). All config classes extends this one.\nclass GenericConfig {\n    constructor() {\n        this.proxyPath = '/_betterstack';\n        this.shouldSendEdgeReport = false;\n        this.token = process.env.NEXT_PUBLIC_BETTER_STACK_SOURCE_TOKEN || process.env.BETTER_STACK_SOURCE_TOKEN || process.env.NEXT_PUBLIC_LOGTAIL_SOURCE_TOKEN || process.env.LOGTAIL_SOURCE_TOKEN;\n        this.environment = \"development\";\n        this.ingestingUrl = process.env.NEXT_PUBLIC_BETTER_STACK_INGESTING_URL || process.env.BETTER_STACK_INGESTING_URL || process.env.NEXT_PUBLIC_LOGTAIL_URL || process.env.LOGTAIL_URL;\n        this.region = process.env.REGION || undefined;\n        this.customEndpoint = process.env.NEXT_PUBLIC_BETTER_STACK_CUSTOM_ENDPOINT;\n    }\n    isEnvVarsSet() {\n        return !!(this.ingestingUrl && this.token) || !!this.customEndpoint;\n    }\n    getIngestURL(_) {\n        return this.ingestingUrl || \"\";\n    }\n    getLogsEndpoint() {\n        if (config_1.isBrowser && this.customEndpoint) {\n            return this.customEndpoint;\n        }\n        return config_1.isBrowser ? `${this.proxyPath}/logs` : this.getIngestURL(shared_1.EndpointType.logs);\n    }\n    getWebVitalsEndpoint() {\n        if (config_1.isBrowser && this.customEndpoint) {\n            return this.customEndpoint;\n        }\n        return config_1.isBrowser ? `${this.proxyPath}/web-vitals` : this.getIngestURL(shared_1.EndpointType.webVitals);\n    }\n    wrapWebVitalsObject(metrics) {\n        return metrics.map(m => ({\n            webVital: m,\n            dt: new Date().getTime(),\n            platform: {\n                environment: this.environment,\n                source: 'web-vital',\n            },\n            source: 'web-vital'\n        }));\n    }\n    injectPlatformMetadata(logEvent, source) {\n        logEvent.source = source;\n        logEvent.platform = {\n            environment: this.environment,\n            region: this.region,\n            source: source,\n        };\n    }\n    getHeaderOrDefault(req, headerName, defaultValue) {\n        return req.headers[headerName] ? req.headers[headerName] : defaultValue;\n    }\n}\nexports[\"default\"] = GenericConfig;\n//# sourceMappingURL=generic.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0Bsb2d0YWlsK25leHRAMC4yLjBfbmV4dEAxNV9lMTk1OWU3NDFjNmJmNDc2MWY0N2UxYzhlNjgyZmVkNi9ub2RlX21vZHVsZXMvQGxvZ3RhaWwvbmV4dC9kaXN0L3BsYXRmb3JtL2dlbmVyaWMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsaUJBQWlCLG1CQUFPLENBQUMsd0pBQVc7QUFDcEMsaUJBQWlCLG1CQUFPLENBQUMsd0pBQVc7QUFDcEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQkFBMkIsYUFBb0I7QUFDL0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVDQUF1QyxlQUFlO0FBQ3REO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1Q0FBdUMsZUFBZTtBQUN0RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWU7QUFDZiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbndhclxcRG9jdW1lbnRzXFxDdWJlbnR3ZWJcXGN1YmVudFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQGxvZ3RhaWwrbmV4dEAwLjIuMF9uZXh0QDE1X2UxOTU5ZTc0MWM2YmY0NzYxZjQ3ZTFjOGU2ODJmZWQ2XFxub2RlX21vZHVsZXNcXEBsb2d0YWlsXFxuZXh0XFxkaXN0XFxwbGF0Zm9ybVxcZ2VuZXJpYy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmNvbnN0IHNoYXJlZF8xID0gcmVxdWlyZShcIi4uL3NoYXJlZFwiKTtcbmNvbnN0IGNvbmZpZ18xID0gcmVxdWlyZShcIi4uL2NvbmZpZ1wiKTtcbi8vIFRoaXMgaXMgdGhlIGdlbmVyaWMgY29uZmlnIGNsYXNzIGZvciBhbGwgcGxhdGZvcm1zIHRoYXQgZG9lc24ndCBoYXZlIGEgc3BlY2lhbFxuLy8gaW1wbGVtZW50YXRpb24gKGUuZzogdmVyY2VsLCBuZXRsaWZ5KS4gQWxsIGNvbmZpZyBjbGFzc2VzIGV4dGVuZHMgdGhpcyBvbmUuXG5jbGFzcyBHZW5lcmljQ29uZmlnIHtcbiAgICBjb25zdHJ1Y3RvcigpIHtcbiAgICAgICAgdGhpcy5wcm94eVBhdGggPSAnL19iZXR0ZXJzdGFjayc7XG4gICAgICAgIHRoaXMuc2hvdWxkU2VuZEVkZ2VSZXBvcnQgPSBmYWxzZTtcbiAgICAgICAgdGhpcy50b2tlbiA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0JFVFRFUl9TVEFDS19TT1VSQ0VfVE9LRU4gfHwgcHJvY2Vzcy5lbnYuQkVUVEVSX1NUQUNLX1NPVVJDRV9UT0tFTiB8fCBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19MT0dUQUlMX1NPVVJDRV9UT0tFTiB8fCBwcm9jZXNzLmVudi5MT0dUQUlMX1NPVVJDRV9UT0tFTjtcbiAgICAgICAgdGhpcy5lbnZpcm9ubWVudCA9IHByb2Nlc3MuZW52Lk5PREVfRU5WO1xuICAgICAgICB0aGlzLmluZ2VzdGluZ1VybCA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0JFVFRFUl9TVEFDS19JTkdFU1RJTkdfVVJMIHx8IHByb2Nlc3MuZW52LkJFVFRFUl9TVEFDS19JTkdFU1RJTkdfVVJMIHx8IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0xPR1RBSUxfVVJMIHx8IHByb2Nlc3MuZW52LkxPR1RBSUxfVVJMO1xuICAgICAgICB0aGlzLnJlZ2lvbiA9IHByb2Nlc3MuZW52LlJFR0lPTiB8fCB1bmRlZmluZWQ7XG4gICAgICAgIHRoaXMuY3VzdG9tRW5kcG9pbnQgPSBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19CRVRURVJfU1RBQ0tfQ1VTVE9NX0VORFBPSU5UO1xuICAgIH1cbiAgICBpc0VudlZhcnNTZXQoKSB7XG4gICAgICAgIHJldHVybiAhISh0aGlzLmluZ2VzdGluZ1VybCAmJiB0aGlzLnRva2VuKSB8fCAhIXRoaXMuY3VzdG9tRW5kcG9pbnQ7XG4gICAgfVxuICAgIGdldEluZ2VzdFVSTChfKSB7XG4gICAgICAgIHJldHVybiB0aGlzLmluZ2VzdGluZ1VybCB8fCBcIlwiO1xuICAgIH1cbiAgICBnZXRMb2dzRW5kcG9pbnQoKSB7XG4gICAgICAgIGlmIChjb25maWdfMS5pc0Jyb3dzZXIgJiYgdGhpcy5jdXN0b21FbmRwb2ludCkge1xuICAgICAgICAgICAgcmV0dXJuIHRoaXMuY3VzdG9tRW5kcG9pbnQ7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIGNvbmZpZ18xLmlzQnJvd3NlciA/IGAke3RoaXMucHJveHlQYXRofS9sb2dzYCA6IHRoaXMuZ2V0SW5nZXN0VVJMKHNoYXJlZF8xLkVuZHBvaW50VHlwZS5sb2dzKTtcbiAgICB9XG4gICAgZ2V0V2ViVml0YWxzRW5kcG9pbnQoKSB7XG4gICAgICAgIGlmIChjb25maWdfMS5pc0Jyb3dzZXIgJiYgdGhpcy5jdXN0b21FbmRwb2ludCkge1xuICAgICAgICAgICAgcmV0dXJuIHRoaXMuY3VzdG9tRW5kcG9pbnQ7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIGNvbmZpZ18xLmlzQnJvd3NlciA/IGAke3RoaXMucHJveHlQYXRofS93ZWItdml0YWxzYCA6IHRoaXMuZ2V0SW5nZXN0VVJMKHNoYXJlZF8xLkVuZHBvaW50VHlwZS53ZWJWaXRhbHMpO1xuICAgIH1cbiAgICB3cmFwV2ViVml0YWxzT2JqZWN0KG1ldHJpY3MpIHtcbiAgICAgICAgcmV0dXJuIG1ldHJpY3MubWFwKG0gPT4gKHtcbiAgICAgICAgICAgIHdlYlZpdGFsOiBtLFxuICAgICAgICAgICAgZHQ6IG5ldyBEYXRlKCkuZ2V0VGltZSgpLFxuICAgICAgICAgICAgcGxhdGZvcm06IHtcbiAgICAgICAgICAgICAgICBlbnZpcm9ubWVudDogdGhpcy5lbnZpcm9ubWVudCxcbiAgICAgICAgICAgICAgICBzb3VyY2U6ICd3ZWItdml0YWwnLFxuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIHNvdXJjZTogJ3dlYi12aXRhbCdcbiAgICAgICAgfSkpO1xuICAgIH1cbiAgICBpbmplY3RQbGF0Zm9ybU1ldGFkYXRhKGxvZ0V2ZW50LCBzb3VyY2UpIHtcbiAgICAgICAgbG9nRXZlbnQuc291cmNlID0gc291cmNlO1xuICAgICAgICBsb2dFdmVudC5wbGF0Zm9ybSA9IHtcbiAgICAgICAgICAgIGVudmlyb25tZW50OiB0aGlzLmVudmlyb25tZW50LFxuICAgICAgICAgICAgcmVnaW9uOiB0aGlzLnJlZ2lvbixcbiAgICAgICAgICAgIHNvdXJjZTogc291cmNlLFxuICAgICAgICB9O1xuICAgIH1cbiAgICBnZXRIZWFkZXJPckRlZmF1bHQocmVxLCBoZWFkZXJOYW1lLCBkZWZhdWx0VmFsdWUpIHtcbiAgICAgICAgcmV0dXJuIHJlcS5oZWFkZXJzW2hlYWRlck5hbWVdID8gcmVxLmhlYWRlcnNbaGVhZGVyTmFtZV0gOiBkZWZhdWx0VmFsdWU7XG4gICAgfVxufVxuZXhwb3J0cy5kZWZhdWx0ID0gR2VuZXJpY0NvbmZpZztcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWdlbmVyaWMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/platform/generic.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/platform/netlify.js":
/*!*************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/platform/netlify.js ***!
  \*************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst generic_1 = __importDefault(__webpack_require__(/*! ./generic */ \"(rsc)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/platform/generic.js\"));\nconst netlifySiteId = process.env.SITE_ID;\nconst netlifyBuildId = process.env.BUILD_ID;\nconst netlifyContext = process.env.CONTEXT;\nconst netlifyDeploymentUrl = process.env.DEPLOYMENT_URL;\nconst netlifyDeploymentId = process.env.DEPLOYMENT_ID;\nclass NetlifyConfig extends generic_1.default {\n    wrapWebVitalsObject(metrics) {\n        return metrics.map(m => ({\n            webVital: m,\n            dt: new Date().getTime(),\n            netlify: {\n                environment: this.environment,\n                source: 'web-vital',\n                siteId: netlifySiteId,\n                buildId: netlifyBuildId,\n                context: netlifyContext,\n                deploymentUrl: netlifyDeploymentUrl,\n                deploymentId: netlifyDeploymentId,\n            },\n        }));\n    }\n    injectPlatformMetadata(logEvent, source) {\n        logEvent.netlify = {\n            environment: this.environment,\n            region: source === 'edge' ? process.env.DENO_REGION : process.env.AWS_REGION,\n            source: source,\n            siteId: netlifySiteId,\n            buildId: netlifyBuildId,\n            context: netlifyContext,\n            deploymentUrl: netlifyDeploymentUrl,\n            deploymentId: source === 'edge' ? process.env.DENO_DEPLOYMENT_ID : netlifyDeploymentId,\n        };\n    }\n}\nexports[\"default\"] = NetlifyConfig;\n//# sourceMappingURL=netlify.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/platform/netlify.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/platform/vercel.js":
/*!************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/platform/vercel.js ***!
  \************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst generic_1 = __importDefault(__webpack_require__(/*! ./generic */ \"(rsc)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/platform/generic.js\"));\nclass VercelConfig extends generic_1.default {\n    constructor() {\n        super(...arguments);\n        this.shouldSendEdgeReport = true;\n        this.region = process.env.VERCEL_REGION || undefined;\n        this.environment = process.env.VERCEL_ENV || \"development\" || 0;\n    }\n    wrapWebVitalsObject(metrics) {\n        return metrics.map(m => ({\n            webVital: m,\n            dt: new Date().getTime(),\n            vercel: {\n                environment: this.environment,\n                source: 'web-vital',\n                deploymentId: process.env.VERCEL_DEPLOYMENT_ID,\n                deploymentUrl: process.env.NEXT_PUBLIC_VERCEL_URL,\n                project: process.env.NEXT_PUBLIC_VERCEL_PROJECT_PRODUCTION_URL,\n                git: {\n                    commit: process.env.NEXT_PUBLIC_VERCEL_GIT_COMMIT_SHA,\n                    repo: process.env.NEXT_PUBLIC_VERCEL_GIT_REPO_SLUG,\n                    ref: process.env.NEXT_PUBLIC_VERCEL_GIT_COMMIT_REF,\n                },\n            },\n        }));\n    }\n    injectPlatformMetadata(logEvent, source) {\n        logEvent.vercel = {\n            environment: this.environment,\n            region: this.region,\n            source: source,\n            deploymentId: process.env.VERCEL_DEPLOYMENT_ID,\n            deploymentUrl: process.env.NEXT_PUBLIC_VERCEL_URL,\n            project: process.env.NEXT_PUBLIC_VERCEL_PROJECT_PRODUCTION_URL,\n            git: {\n                commit: process.env.NEXT_PUBLIC_VERCEL_GIT_COMMIT_SHA,\n                repo: process.env.NEXT_PUBLIC_VERCEL_GIT_REPO_SLUG,\n                ref: process.env.NEXT_PUBLIC_VERCEL_GIT_COMMIT_REF,\n            },\n        };\n    }\n}\nexports[\"default\"] = VercelConfig;\n//# sourceMappingURL=vercel.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/platform/vercel.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/shared.js":
/*!***************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/shared.js ***!
  \***************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports) {

"use strict";
eval("\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.throttle = exports.requestToJSON = exports.EndpointType = exports.isNoPrettyPrint = void 0;\nexports.isNoPrettyPrint = process.env.BETTER_STACK_NO_PRETTY_PRINT == 'true' ? true : false;\nvar EndpointType;\n(function (EndpointType) {\n    EndpointType[\"webVitals\"] = \"web-vitals\";\n    EndpointType[\"logs\"] = \"logs\";\n})(EndpointType || (exports.EndpointType = EndpointType = {}));\n/**\n * Transforms a NextRequest or Request object into a JSON-serializable object\n */\nfunction requestToJSON(request) {\n    var _a;\n    return __awaiter(this, void 0, void 0, function* () {\n        // Get all headers\n        const headers = {};\n        request.headers.forEach((value, key) => {\n            headers[key] = value;\n        });\n        let cookiesData = {};\n        if ('cookies' in request) {\n            request.cookies.getAll().forEach((cookie) => {\n                cookiesData[cookie.name] = cookie.value;\n            });\n        }\n        else {\n            const cookieHeader = headers['cookie'];\n            if (cookieHeader) {\n                cookiesData = Object.fromEntries(cookieHeader.split(';').map((cookie) => {\n                    const [key, value] = cookie.trim().split('=');\n                    return [key, value];\n                }));\n            }\n        }\n        let nextUrlData;\n        if ('nextUrl' in request) {\n            const nextUrl = request.nextUrl;\n            nextUrlData = {\n                basePath: nextUrl.basePath,\n                buildId: nextUrl.buildId,\n                hash: nextUrl.hash,\n                host: nextUrl.host,\n                hostname: nextUrl.hostname,\n                href: nextUrl.href,\n                origin: nextUrl.origin,\n                password: nextUrl.password,\n                pathname: nextUrl.pathname,\n                port: nextUrl.port,\n                protocol: nextUrl.protocol,\n                search: nextUrl.search,\n                searchParams: Object.fromEntries(nextUrl.searchParams.entries()),\n                username: nextUrl.username,\n            };\n        }\n        let body;\n        if (request.body) {\n            try {\n                const clonedRequest = request.clone();\n                try {\n                    body = yield clonedRequest.json();\n                    (_a = clonedRequest.body) === null || _a === void 0 ? void 0 : _a.getReader;\n                }\n                catch (_b) {\n                    body = yield clonedRequest.text();\n                }\n            }\n            catch (error) {\n                console.warn('Could not parse request body:', error);\n            }\n        }\n        const cache = {\n            mode: request.cache,\n            credentials: request.credentials,\n            redirect: request.redirect,\n            referrerPolicy: request.referrerPolicy,\n            integrity: request.integrity,\n        };\n        let ip;\n        if ('ip' in request) {\n            // @ts-ignore NextRequest.ip was removed in Next 15, works with undefined\n            ip = request.ip;\n        }\n        // @ts-ignore NextRequest.ip was removed in Next 15, works with undefined\n        let geo;\n        if ('geo' in request) {\n            geo = request.geo;\n        }\n        return {\n            method: request.method,\n            url: request.url,\n            headers,\n            cookies: cookiesData,\n            nextUrl: nextUrlData,\n            ip,\n            geo,\n            body,\n            cache,\n            mode: request.mode,\n            destination: request.destination,\n            referrer: request.referrer,\n            keepalive: request.keepalive,\n            signal: {\n                aborted: request.signal.aborted,\n                reason: request.signal.reason,\n            },\n        };\n    });\n}\nexports.requestToJSON = requestToJSON;\nconst throttle = (fn, wait) => {\n    let lastFn, lastTime;\n    return function () {\n        const context = this, args = arguments;\n        // First call, set lastTime\n        if (lastTime == null) {\n            lastTime = Date.now();\n        }\n        clearTimeout(lastFn);\n        lastFn = setTimeout(() => {\n            if (Date.now() - lastTime >= wait) {\n                fn.apply(context, args);\n                lastTime = Date.now();\n            }\n        }, Math.max(wait - (Date.now() - lastTime), 0));\n    };\n};\nexports.throttle = throttle;\n//# sourceMappingURL=shared.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/shared.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/webVitals/components.js":
/*!*****************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/webVitals/components.js ***!
  \*****************************************************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

const { createProxy } = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js")

module.exports = createProxy("C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\node_modules\\.pnpm\\@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6\\node_modules\\@logtail\\next\\dist\\webVitals\\components.js")


/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/webVitals/index.js":
/*!************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/webVitals/index.js ***!
  \************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.useReportWebVitals = exports.BetterStackWebVitals = void 0;\nconst navigation_1 = __webpack_require__(/*! next/navigation */ \"(rsc)/../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/api/navigation.react-server.js\");\nconst web_vitals_1 = __webpack_require__(/*! next/web-vitals */ \"(rsc)/../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/web-vitals.js\");\nconst webVitals_1 = __webpack_require__(/*! ./webVitals */ \"(rsc)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/webVitals/webVitals.js\");\nconst react_1 = __webpack_require__(/*! react */ \"(rsc)/../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\nvar components_1 = __webpack_require__(/*! ./components */ \"(rsc)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/webVitals/components.js\");\nObject.defineProperty(exports, \"BetterStackWebVitals\", ({ enumerable: true, get: function () { return components_1.BetterStackWebVitals; } }));\nfunction useReportWebVitals(path) {\n    const pathName = (0, navigation_1.usePathname)();\n    /**\n     * Refs allows us to stabilize the path name so that we can properly stabilize the reportWebVitalsFn\n     */\n    const stabilizedPath = (0, react_1.useRef)(path || pathName);\n    /**\n     * If the path changes, we update the stabilizedPath ref\n     */\n    if (typeof path === 'string' && path !== stabilizedPath.current) {\n        stabilizedPath.current = pathName;\n    }\n    else if (typeof path === 'string' && path === stabilizedPath.current) {\n        stabilizedPath.current = path;\n    }\n    /**\n     * Stabilizing the reportWebVitalsFn avoids reporting the same metrics from multiple paths, it happens because internally\n     * the useReportWebVitals from next uses a useEffect to report the metrics, and the reportWebVitalsFn is passed as a dependency\n     * to the useEffect, so when the path changes, the useEffect is re-run, and the same metrics are reported again.\n     */\n    const reportWebVitalsFn = (0, react_1.useCallback)((metric) => (0, webVitals_1.reportWebVitalsWithPath)(metric, stabilizedPath.current), []);\n    (0, web_vitals_1.useReportWebVitals)(reportWebVitalsFn);\n}\nexports.useReportWebVitals = useReportWebVitals;\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/webVitals/index.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/webVitals/webVitals.js":
/*!****************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/webVitals/webVitals.js ***!
  \****************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.reportWebVitalsWithPath = void 0;\nconst config_1 = __webpack_require__(/*! ../config */ \"(rsc)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/config.js\");\nconst shared_1 = __webpack_require__(/*! ../shared */ \"(rsc)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/shared.js\");\nconst url = config_1.config.getWebVitalsEndpoint();\nconst throttledSendMetrics = (0, shared_1.throttle)(sendMetrics, 1000);\nlet collectedMetrics = [];\nfunction reportWebVitalsWithPath(metric, route) {\n    collectedMetrics.push(Object.assign({ route }, metric));\n    // if env vars are not set, do nothing,\n    // otherwise devs will get errors on dev environments\n    if (!config_1.config.isEnvVarsSet()) {\n        return;\n    }\n    throttledSendMetrics();\n}\nexports.reportWebVitalsWithPath = reportWebVitalsWithPath;\nfunction sendMetrics() {\n    const body = JSON.stringify(config_1.config.wrapWebVitalsObject(collectedMetrics));\n    const headers = {\n        'Content-Type': 'application/json',\n        'User-Agent': 'next-logtail/v' + config_1.Version,\n    };\n    if (config_1.config.token) {\n        headers['Authorization'] = `Bearer ${config_1.config.token}`;\n    }\n    const reqOptions = { body, method: 'POST', keepalive: true, headers };\n    function sendFallback() {\n        // Do not leak network errors; does not affect the running app\n        fetch(url, reqOptions).catch(console.error);\n    }\n    if (config_1.isBrowser && config_1.isVercel && navigator.sendBeacon) {\n        try {\n            // See https://github.com/vercel/next.js/pull/26601\n            // Navigator has to be bound to ensure it does not error in some browsers\n            // https://xgwang.me/posts/you-may-not-know-beacon/#it-may-throw-error%2C-be-sure-to-catch\n            navigator.sendBeacon.bind(navigator)(url, body);\n        }\n        catch (err) {\n            sendFallback();\n        }\n    }\n    else {\n        sendFallback();\n    }\n    collectedMetrics = [];\n}\n//# sourceMappingURL=webVitals.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/webVitals/webVitals.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/withBetterStack.js":
/*!************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/withBetterStack.js ***!
  \************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval("\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.withBetterStack = exports.withBetterStackRouteHandler = exports.withBetterStackNextConfig = void 0;\nconst config_1 = __webpack_require__(/*! ./config */ \"(rsc)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/config.js\");\nconst logger_1 = __webpack_require__(/*! ./logger */ \"(rsc)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/logger.js\");\nconst shared_1 = __webpack_require__(/*! ./shared */ \"(rsc)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/shared.js\");\nfunction withBetterStackNextConfig(nextConfig) {\n    return Object.assign(Object.assign({}, nextConfig), { rewrites: () => __awaiter(this, void 0, void 0, function* () {\n            var _a;\n            const rewrites = yield ((_a = nextConfig.rewrites) === null || _a === void 0 ? void 0 : _a.call(nextConfig));\n            const webVitalsEndpoint = config_1.config.getIngestURL(shared_1.EndpointType.webVitals);\n            const logsEndpoint = config_1.config.getIngestURL(shared_1.EndpointType.logs);\n            if (!webVitalsEndpoint && !logsEndpoint) {\n                const log = new logger_1.Logger();\n                log.warn('Envvars not detected. If this is production please see https://betterstack.com/docs/logs/javascript/nextjs/ for help');\n                log.warn('Sending Web Vitals to /dev/null');\n                log.warn('Sending logs to console');\n                return rewrites || []; // nothing to do\n            }\n            const betterStackRewrites = [\n                {\n                    source: `${config_1.config.proxyPath}/web-vitals`,\n                    destination: webVitalsEndpoint,\n                    basePath: false,\n                },\n                {\n                    source: `${config_1.config.proxyPath}/logs`,\n                    destination: logsEndpoint,\n                    basePath: false,\n                },\n            ];\n            if (!rewrites) {\n                return betterStackRewrites;\n            }\n            else if (Array.isArray(rewrites)) {\n                return rewrites.concat(betterStackRewrites);\n            }\n            else {\n                rewrites.afterFiles = (rewrites.afterFiles || []).concat(betterStackRewrites);\n                return rewrites;\n            }\n        }) });\n}\nexports.withBetterStackNextConfig = withBetterStackNextConfig;\nfunction withBetterStackRouteHandler(handler, config) {\n    return (req, arg) => __awaiter(this, void 0, void 0, function* () {\n        var _a, _b, _c, _d;\n        let region = '';\n        if ('geo' in req) {\n            // @ts-ignore NextRequest.ip was removed in Next 15, works with undefined\n            region = (_b = (_a = req.geo) === null || _a === void 0 ? void 0 : _a.region) !== null && _b !== void 0 ? _b : '';\n        }\n        let pathname = '';\n        if ('nextUrl' in req) {\n            pathname = req.nextUrl.pathname;\n        }\n        else if (req instanceof Request) {\n            // pathname = req.url.substring(req.headers.get('host')?.length || 0)\n            pathname = new URL(req.url).pathname;\n        }\n        const requestDetails = Array.isArray(config === null || config === void 0 ? void 0 : config.logRequestDetails) || (config === null || config === void 0 ? void 0 : config.logRequestDetails) === true\n            ? yield (0, shared_1.requestToJSON)(req)\n            : undefined;\n        const report = {\n            startTime: new Date().getTime(),\n            endTime: new Date().getTime(),\n            path: pathname,\n            method: req.method,\n            host: req.headers.get('host'),\n            userAgent: req.headers.get('user-agent'),\n            scheme: req.url.split('://')[0],\n            ip: req.headers.get('x-forwarded-for'),\n            region,\n            details: Array.isArray(config === null || config === void 0 ? void 0 : config.logRequestDetails)\n                ? Object.fromEntries(Object.entries(requestDetails).filter(([key]) => (config === null || config === void 0 ? void 0 : config.logRequestDetails).includes(key)))\n                : requestDetails,\n        };\n        // main logger, mainly used to log reporting on the incoming HTTP request\n        const logger = new logger_1.Logger({ req: report, source: config_1.isEdgeRuntime ? 'edge' : 'lambda' });\n        // child logger to be used by the users within the handler\n        const log = logger.with({});\n        log.config.source = `${config_1.isEdgeRuntime ? 'edge' : 'lambda'}-log`;\n        const betterStackContext = req;\n        const args = arg;\n        betterStackContext.log = log;\n        try {\n            const result = yield handler(betterStackContext, args);\n            report.endTime = new Date().getTime();\n            // report log record\n            report.statusCode = result.status;\n            report.durationMs = report.endTime - report.startTime;\n            // record the request\n            if (!config_1.isVercel) {\n                logger.logHttpRequest(logger_1.LogLevel.info, `${req.method} ${report.path} ${report.statusCode} in ${report.endTime - report.startTime}ms`, report, {});\n            }\n            // attach the response status to all children logs\n            log.attachResponseStatus(result.status);\n            // flush the logger along with the child logger\n            yield logger.flush();\n            return result;\n        }\n        catch (error) {\n            // capture request endTime first for more accurate reporting\n            report.endTime = new Date().getTime();\n            // set default values for statusCode and logLevel\n            let statusCode = 500;\n            let logLevel = logger_1.LogLevel.error;\n            // handle navigation errors like notFound and redirect\n            if (error instanceof Error) {\n                if (error.message === 'NEXT_NOT_FOUND') {\n                    logLevel = (_c = config === null || config === void 0 ? void 0 : config.notFoundLogLevel) !== null && _c !== void 0 ? _c : logger_1.LogLevel.warn;\n                    statusCode = 404;\n                }\n                else if (error.message === 'NEXT_REDIRECT') {\n                    logLevel = (_d = config === null || config === void 0 ? void 0 : config.redirectLogLevel) !== null && _d !== void 0 ? _d : logger_1.LogLevel.info;\n                    // according to Next.js docs, values are: 307 (Temporary) or 308 (Permanent)\n                    // see: https://nextjs.org/docs/app/api-reference/functions/redirect#why-does-redirect-use-307-and-308\n                    // extract status code from digest, if exists\n                    const e = error;\n                    if (e.digest) {\n                        const d = e.digest.split(';');\n                        statusCode = parseInt(d[3]);\n                    }\n                    else {\n                        statusCode = 307;\n                    }\n                }\n            }\n            // report log record\n            report.statusCode = statusCode;\n            report.durationMs = report.endTime - report.startTime;\n            // record the request\n            if (!config_1.isVercel) {\n                logger.logHttpRequest(logLevel, `${req.method} ${report.path} ${report.statusCode} in ${report.endTime - report.startTime}ms`, report, {});\n            }\n            // forward the error message as a log event\n            log.log(logLevel, error.message, { error });\n            log.attachResponseStatus(statusCode);\n            yield logger.flush();\n            throw error;\n        }\n    });\n}\nexports.withBetterStackRouteHandler = withBetterStackRouteHandler;\nfunction isNextConfig(param) {\n    return typeof param == 'object';\n}\nfunction withBetterStack(param, config) {\n    if (typeof param == 'function') {\n        return withBetterStackRouteHandler(param, config);\n    }\n    else if (isNextConfig(param)) {\n        return withBetterStackNextConfig(param);\n    }\n    return withBetterStackRouteHandler(param, config);\n}\nexports.withBetterStack = withBetterStack;\n//# sourceMappingURL=withBetterStack.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/withBetterStack.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/package.json":
/*!*************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/package.json ***!
  \*************************************************************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"name":"@logtail/next","description":"Better Stack Telemetry Next.js client","version":"0.2.0","author":"Better Stack <<EMAIL>>","license":"MIT","engines":{"node":">=18"},"main":"dist/index.js","types":"dist/index.d.ts","scripts":{"build":"tsc","prepare":"npm run build","format":"prettier --write src/*.ts tests/*.ts","check-format":"prettier -c src/*.ts tests/*.ts","test":"vitest run tests --coverage"},"repository":{"type":"git","url":"git+https://github.com/logtail/logtail-nextjs.git"},"keywords":["web-vitals","better-stack","logtail","observability","performance","telemetry","nextjs"],"bugs":{"url":"https://github.com/logtail/logtail-nextjs/issues"},"homepage":"https://github.com/logtail/logtail-nextjs#readme","peerDependencies":{"next":">=14.0","react":">=18.0.0"},"devDependencies":{"@types/node":"^20.4.2","@types/react":"^18.2.15","@vitest/coverage-v8":"^2.1.5","prettier":"^3.0.0","ts-node":"^10.9.1","typescript":"^5.1.6","vitest":"^2.1.5"},"dependencies":{"whatwg-fetch":"^3.6.2","use-deep-compare":"^1.2.1"}}');

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/config.js":
/*!***************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/config.js ***!
  \***************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.config = exports.isEdgeRuntime = exports.isBrowser = exports.isWebWorker = exports.isNetlify = exports.isVercel = exports.isVercelIntegration = exports.Version = void 0;\nconst generic_1 = __importDefault(__webpack_require__(/*! ./platform/generic */ \"(ssr)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/platform/generic.js\"));\nconst vercel_1 = __importDefault(__webpack_require__(/*! ./platform/vercel */ \"(ssr)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/platform/vercel.js\"));\nconst netlify_1 = __importDefault(__webpack_require__(/*! ./platform/netlify */ \"(ssr)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/platform/netlify.js\"));\nexports.Version = __webpack_require__(/*! ../package.json */ \"(ssr)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/package.json\").version;\n// detect if Vercel integration & logdrain is enabled\nexports.isVercelIntegration = process.env.NEXT_PUBLIC_BETTER_STACK_INGESTING_URL || process.env.BETTER_STACK_INGEST_ENDPOINT;\n// detect if app is running on the Vercel platform\nexports.isVercel = process.env.NEXT_PUBLIC_VERCEL || process.env.VERCEL;\nexports.isNetlify = process.env.NETLIFY == 'true';\nexports.isWebWorker = typeof self !== 'undefined' &&\n    typeof globalThis.WorkerGlobalScope !== 'undefined' &&\n    self instanceof WorkerGlobalScope;\nexports.isBrowser = typeof window !== 'undefined' || exports.isWebWorker;\nexports.isEdgeRuntime = globalThis.EdgeRuntime ? true : false;\n// Detect the platform provider, and return the appropriate config\n// fallback to generic config if no provider is detected\nlet config = new generic_1.default();\nexports.config = config;\nif (exports.isVercel) {\n    exports.config = config = new vercel_1.default();\n}\nelse if (exports.isNetlify) {\n    exports.config = config = new netlify_1.default();\n}\n//# sourceMappingURL=config.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/config.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/hooks.js":
/*!**************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/hooks.js ***!
  \**************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.useLogger = void 0;\nconst navigation_1 = __webpack_require__(/*! next/navigation */ \"(ssr)/../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/api/navigation.js\");\nconst logger_1 = __webpack_require__(/*! ./logger */ \"(ssr)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/logger.js\");\nconst react_1 = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\nconst use_deep_compare_1 = __webpack_require__(/*! use-deep-compare */ \"(ssr)/../../node_modules/.pnpm/use-deep-compare@1.3.0_react@19.1.0/node_modules/use-deep-compare/dist-web/index.js\");\nfunction useLogger(config = {}) {\n    const path = (0, navigation_1.usePathname)();\n    const memoizedConfig = (0, use_deep_compare_1.useDeepCompareMemo)(() => {\n        var _a;\n        return (Object.assign(Object.assign({}, config), { args: Object.assign(Object.assign({}, ((_a = config.args) !== null && _a !== void 0 ? _a : {})), { path }) }));\n    }, [config, path]);\n    const logger = (0, react_1.useMemo)(() => new logger_1.Logger(memoizedConfig), [memoizedConfig]);\n    (0, react_1.useEffect)(() => {\n        return () => {\n            if (logger) {\n                logger.flush();\n            }\n        };\n    }, [path]);\n    return logger;\n}\nexports.useLogger = useLogger;\n//# sourceMappingURL=hooks.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/hooks.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/index.js":
/*!**************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/index.js ***!
  \**************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.useLogger = exports.withLogtailRouteHandler = exports.withLogtailNextConfig = exports.withLogtail = exports.withBetterStackRouteHandler = exports.withBetterStackNextConfig = exports.withBetterStack = exports.throttle = exports.EndpointType = exports.LogLevel = exports.Logger = exports.log = void 0;\nvar logger_1 = __webpack_require__(/*! ./logger */ \"(ssr)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/logger.js\");\nObject.defineProperty(exports, \"log\", ({ enumerable: true, get: function () { return logger_1.log; } }));\nObject.defineProperty(exports, \"Logger\", ({ enumerable: true, get: function () { return logger_1.Logger; } }));\nObject.defineProperty(exports, \"LogLevel\", ({ enumerable: true, get: function () { return logger_1.LogLevel; } }));\nvar shared_1 = __webpack_require__(/*! ./shared */ \"(ssr)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/shared.js\");\nObject.defineProperty(exports, \"EndpointType\", ({ enumerable: true, get: function () { return shared_1.EndpointType; } }));\nObject.defineProperty(exports, \"throttle\", ({ enumerable: true, get: function () { return shared_1.throttle; } }));\n__exportStar(__webpack_require__(/*! ./platform/base */ \"(ssr)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/platform/base.js\"), exports);\n__exportStar(__webpack_require__(/*! ./config */ \"(ssr)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/config.js\"), exports);\nvar withBetterStack_1 = __webpack_require__(/*! ./withBetterStack */ \"(ssr)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/withBetterStack.js\");\nObject.defineProperty(exports, \"withBetterStack\", ({ enumerable: true, get: function () { return withBetterStack_1.withBetterStack; } }));\nObject.defineProperty(exports, \"withBetterStackNextConfig\", ({ enumerable: true, get: function () { return withBetterStack_1.withBetterStackNextConfig; } }));\nObject.defineProperty(exports, \"withBetterStackRouteHandler\", ({ enumerable: true, get: function () { return withBetterStack_1.withBetterStackRouteHandler; } }));\nObject.defineProperty(exports, \"withLogtail\", ({ enumerable: true, get: function () { return withBetterStack_1.withBetterStack; } }));\nObject.defineProperty(exports, \"withLogtailNextConfig\", ({ enumerable: true, get: function () { return withBetterStack_1.withBetterStackNextConfig; } }));\nObject.defineProperty(exports, \"withLogtailRouteHandler\", ({ enumerable: true, get: function () { return withBetterStack_1.withBetterStackRouteHandler; } }));\n__exportStar(__webpack_require__(/*! ./webVitals */ \"(ssr)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/webVitals/index.js\"), exports);\nvar hooks_1 = __webpack_require__(/*! ./hooks */ \"(ssr)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/hooks.js\");\nObject.defineProperty(exports, \"useLogger\", ({ enumerable: true, get: function () { return hooks_1.useLogger; } }));\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/logger.js":
/*!***************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/logger.js ***!
  \***************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval("\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.prettyPrint = exports.log = exports.Logger = exports.LogLevel = void 0;\nconst config_1 = __webpack_require__(/*! ./config */ \"(ssr)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/config.js\");\nconst shared_1 = __webpack_require__(/*! ./shared */ \"(ssr)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/shared.js\");\nconst url = config_1.config.getLogsEndpoint();\nconst LOG_LEVEL = process.env.NEXT_PUBLIC_BETTER_STACK_LOG_LEVEL || 'debug';\nvar LogLevel;\n(function (LogLevel) {\n    LogLevel[LogLevel[\"debug\"] = 0] = \"debug\";\n    LogLevel[LogLevel[\"info\"] = 1] = \"info\";\n    LogLevel[LogLevel[\"warn\"] = 2] = \"warn\";\n    LogLevel[LogLevel[\"error\"] = 3] = \"error\";\n    LogLevel[LogLevel[\"off\"] = 100] = \"off\";\n})(LogLevel || (exports.LogLevel = LogLevel = {}));\nclass Logger {\n    constructor(initConfig = {}) {\n        this.initConfig = initConfig;\n        this.logEvents = [];\n        this.throttledSendLogs = (0, shared_1.throttle)(this.sendLogs, 1000);\n        this.children = [];\n        this.logLevel = LogLevel.debug;\n        this.config = {\n            autoFlush: true,\n            source: 'frontend-log',\n            prettyPrint: prettyPrint,\n        };\n        this.debug = (message, args = {}) => {\n            this.log(LogLevel.debug, message, args);\n        };\n        this.info = (message, args = {}) => {\n            this.log(LogLevel.info, message, args);\n        };\n        this.warn = (message, args = {}) => {\n            this.log(LogLevel.warn, message, args);\n        };\n        this.error = (message, args = {}) => {\n            this.log(LogLevel.error, message, args);\n        };\n        this.with = (args) => {\n            const config = Object.assign(Object.assign({}, this.config), { args: Object.assign(Object.assign({}, this.config.args), args) });\n            const child = new Logger(config);\n            this.children.push(child);\n            return child;\n        };\n        this.withRequest = (req) => {\n            return new Logger(Object.assign(Object.assign({}, this.config), { req: Object.assign(Object.assign({}, this.config.req), req) }));\n        };\n        this._transformEvent = (level, message, args = {}) => {\n            const logEvent = {\n                level: LogLevel[level].toString(),\n                message,\n                dt: new Date(Date.now()).toISOString(),\n                source: this.config.source,\n                fields: this.config.args || {},\n                '@app': {\n                    'next-logtail-version': config_1.Version,\n                },\n            };\n            // check if passed args is an object, if its not an object, add it to fields.args\n            if (args instanceof Error) {\n                logEvent.fields = Object.assign(Object.assign({}, logEvent.fields), { message: args.message, stack: args.stack, name: args.name });\n            }\n            else if (typeof args === 'object' && args !== null && Object.keys(args).length > 0) {\n                const parsedArgs = JSON.parse(JSON.stringify(args, jsonFriendlyErrorReplacer));\n                logEvent.fields = Object.assign(Object.assign({}, logEvent.fields), parsedArgs);\n            }\n            else if (args && args.length) {\n                logEvent.fields = Object.assign(Object.assign({}, logEvent.fields), { args: args });\n            }\n            config_1.config.injectPlatformMetadata(logEvent, this.config.source);\n            if (this.config.req != null) {\n                logEvent.request = this.config.req;\n                if (logEvent.platform) {\n                    logEvent.platform.route = this.config.req.path;\n                }\n                else if (logEvent.vercel) {\n                    logEvent.vercel.route = this.config.req.path;\n                }\n            }\n            return logEvent;\n        };\n        this.log = (level, message, args = {}) => {\n            if (level < this.logLevel) {\n                return;\n            }\n            const logEvent = this._transformEvent(level, message, args);\n            this.logEvents.push(logEvent);\n            if (this.config.autoFlush) {\n                this.throttledSendLogs();\n            }\n        };\n        this.attachResponseStatus = (statusCode) => {\n            this.logEvents = this.logEvents.map((log) => {\n                if (log.request) {\n                    log.request.statusCode = statusCode;\n                }\n                return log;\n            });\n        };\n        this.flush = () => __awaiter(this, void 0, void 0, function* () {\n            yield Promise.all([this.sendLogs(), ...this.children.map((c) => c.flush())]);\n        });\n        // check if user passed a log level, if not the default init value will be used as is.\n        if (this.initConfig.logLevel != undefined && this.initConfig.logLevel >= 0) {\n            this.logLevel = this.initConfig.logLevel;\n        }\n        else if (LOG_LEVEL) {\n            this.logLevel = LogLevel[LOG_LEVEL];\n        }\n        this.config = Object.assign(Object.assign({}, this.config), initConfig);\n    }\n    logHttpRequest(level, message, request, args) {\n        const logEvent = this._transformEvent(level, message, args);\n        logEvent.request = request;\n        this.logEvents.push(logEvent);\n        if (this.config.autoFlush) {\n            this.throttledSendLogs();\n        }\n    }\n    middleware(request, config) {\n        var _a;\n        const req = {\n            // @ts-ignore NextRequest.ip was removed in Next 15, works with undefined\n            ip: request.ip,\n            // @ts-ignore NextRequest.ip was removed in Next 15, works with undefined\n            region: (_a = request.geo) === null || _a === void 0 ? void 0 : _a.region,\n            method: request.method,\n            host: request.nextUrl.hostname,\n            path: request.nextUrl.pathname,\n            scheme: request.nextUrl.protocol.split(':')[0],\n            referer: request.headers.get('Referer'),\n            userAgent: request.headers.get('user-agent'),\n        };\n        const message = `${request.method} ${request.nextUrl.pathname}`;\n        if (config === null || config === void 0 ? void 0 : config.logRequestDetails) {\n            return (0, shared_1.requestToJSON)(request).then((details) => {\n                const newReq = Object.assign(Object.assign({}, req), { details: Array.isArray(config.logRequestDetails)\n                        ? Object.fromEntries(Object.entries(details).filter(([key]) => config.logRequestDetails.includes(key)))\n                        : details });\n                return this.logHttpRequest(LogLevel.info, message, newReq, {});\n            });\n        }\n        return this.logHttpRequest(LogLevel.info, message, req, {});\n    }\n    sendLogs() {\n        return __awaiter(this, void 0, void 0, function* () {\n            if (!this.logEvents.length) {\n                return;\n            }\n            // To send logs over the network, we need one of:\n            //\n            // - Ingesting URL and source token\n            // - Custom endpoint\n            //\n            // We fall back to printing to console to avoid network errors in\n            // development environments.\n            if (!config_1.config.isEnvVarsSet()) {\n                this.logEvents.forEach((ev) => (this.config.prettyPrint ? this.config.prettyPrint(ev) : prettyPrint(ev)));\n                this.logEvents = [];\n                return;\n            }\n            const method = 'POST';\n            const keepalive = true;\n            const body = JSON.stringify(this.logEvents);\n            // clear pending logs\n            this.logEvents = [];\n            const headers = {\n                'Content-Type': 'application/json',\n                'User-Agent': 'next-logtail/v' + config_1.Version,\n            };\n            if (config_1.config.token) {\n                headers['Authorization'] = `Bearer ${config_1.config.token}`;\n            }\n            const reqOptions = { body, method, keepalive, headers };\n            function sendFallback() {\n                // Do not leak network errors; does not affect the running app\n                return fetch(url, reqOptions).catch(console.error);\n            }\n            try {\n                if (typeof fetch === 'undefined') {\n                    const fetch = yield __webpack_require__(/*! whatwg-fetch */ \"(ssr)/../../node_modules/.pnpm/whatwg-fetch@3.6.20/node_modules/whatwg-fetch/fetch.js\");\n                    return fetch(url, reqOptions).catch(console.error);\n                }\n                else if (config_1.isBrowser && config_1.isVercel && navigator.sendBeacon) {\n                    // sendBeacon fails if message size is greater than 64kb, so\n                    // we fall back to fetch.\n                    // Navigator has to be bound to ensure it does not error in some browsers\n                    // https://xgwang.me/posts/you-may-not-know-beacon/#it-may-throw-error%2C-be-sure-to-catch\n                    try {\n                        if (!navigator.sendBeacon.bind(navigator)(url, body)) {\n                            return sendFallback();\n                        }\n                    }\n                    catch (error) {\n                        return sendFallback();\n                    }\n                }\n                else {\n                    return sendFallback();\n                }\n            }\n            catch (e) {\n                console.warn(`Failed to send logs to BetterStack: ${e}`);\n                // put the log events back in the queue\n                this.logEvents = [...this.logEvents, JSON.parse(body)];\n            }\n        });\n    }\n}\nexports.Logger = Logger;\nexports.log = new Logger({});\nconst levelColors = {\n    info: {\n        terminal: '32',\n        browser: 'lightgreen',\n    },\n    debug: {\n        terminal: '36',\n        browser: 'lightblue',\n    },\n    warn: {\n        terminal: '33',\n        browser: 'yellow',\n    },\n    error: {\n        terminal: '31',\n        browser: 'red',\n    },\n};\nfunction prettyPrint(ev) {\n    const hasFields = Object.keys(ev.fields).length > 0;\n    // check whether pretty print is disabled\n    if (shared_1.isNoPrettyPrint) {\n        let msg = `${ev.level} - ${ev.message}`;\n        if (hasFields) {\n            msg += ' ' + JSON.stringify(ev.fields);\n        }\n        console.log(msg);\n        return;\n    }\n    // print indented message, instead of [object]\n    // We use the %o modifier instead of JSON.stringify because stringify will print the\n    // object as normal text, it loses all the functionality the browser gives for viewing\n    // objects in the console, such as expanding and collapsing the object.\n    let msgString = '';\n    let args = [ev.level, ev.message];\n    if (config_1.isBrowser) {\n        msgString = '%c%s - %s';\n        args = [`color: ${levelColors[ev.level].browser};`, ...args];\n    }\n    else {\n        msgString = `\\x1b[${levelColors[ev.level].terminal}m%s\\x1b[0m - %s`;\n    }\n    // we check if the fields object is not empty, otherwise its printed as <empty string>\n    // or just \"\".\n    if (hasFields) {\n        msgString += ' %o';\n        args.push(ev.fields);\n    }\n    if (ev.request) {\n        msgString += ' %o';\n        args.push(ev.request);\n    }\n    console.log.apply(console, [msgString, ...args]);\n}\nexports.prettyPrint = prettyPrint;\nfunction jsonFriendlyErrorReplacer(key, value) {\n    if (value instanceof Error) {\n        return Object.assign(Object.assign({}, value), { \n            // Explicitly pull Error's non-enumerable properties\n            name: value.name, message: value.message, stack: value.stack });\n    }\n    return value;\n}\n//# sourceMappingURL=logger.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0Bsb2d0YWlsK25leHRAMC4yLjBfbmV4dEAxNV9lMTk1OWU3NDFjNmJmNDc2MWY0N2UxYzhlNjgyZmVkNi9ub2RlX21vZHVsZXMvQGxvZ3RhaWwvbmV4dC9kaXN0L2xvZ2dlci5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0EsNEJBQTRCLCtEQUErRCxpQkFBaUI7QUFDNUc7QUFDQSxvQ0FBb0MsTUFBTSwrQkFBK0IsWUFBWTtBQUNyRixtQ0FBbUMsTUFBTSxtQ0FBbUMsWUFBWTtBQUN4RixnQ0FBZ0M7QUFDaEM7QUFDQSxLQUFLO0FBQ0w7QUFDQSw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsbUJBQW1CLEdBQUcsV0FBVyxHQUFHLGNBQWMsR0FBRyxnQkFBZ0I7QUFDckUsaUJBQWlCLG1CQUFPLENBQUMsdUpBQVU7QUFDbkMsaUJBQWlCLG1CQUFPLENBQUMsdUpBQVU7QUFDbkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxlQUFlLGdCQUFnQixnQkFBZ0I7QUFDaEQ7QUFDQSwrQkFBK0I7QUFDL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3Q0FBd0M7QUFDeEM7QUFDQTtBQUNBLHVDQUF1QztBQUN2QztBQUNBO0FBQ0EsdUNBQXVDO0FBQ3ZDO0FBQ0E7QUFDQSx3Q0FBd0M7QUFDeEM7QUFDQTtBQUNBO0FBQ0EseURBQXlELGtCQUFrQixvQ0FBb0MsNEJBQTRCO0FBQzNJO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0REFBNEQsa0JBQWtCLG1DQUFtQywwQkFBMEI7QUFDM0k7QUFDQSx5REFBeUQ7QUFDekQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhDQUE4QztBQUM5QztBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBLGdFQUFnRSxzQkFBc0IsMkRBQTJEO0FBQ2pKO0FBQ0E7QUFDQTtBQUNBLGdFQUFnRTtBQUNoRTtBQUNBO0FBQ0EsZ0VBQWdFLHNCQUFzQixZQUFZO0FBQ2xHO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkNBQTZDO0FBQzdDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvREFBb0Q7QUFDcEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJCQUEyQixnQkFBZ0IsRUFBRSx5QkFBeUI7QUFDdEU7QUFDQTtBQUNBLDZEQUE2RCxVQUFVO0FBQ3ZFO0FBQ0EsbUNBQW1DO0FBQ25DLDZFQUE2RTtBQUM3RSxhQUFhO0FBQ2I7QUFDQSxrRUFBa0U7QUFDbEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxREFBcUQsc0JBQXNCO0FBQzNFO0FBQ0EsaUNBQWlDO0FBQ2pDO0FBQ0EsK0NBQStDO0FBQy9DO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0NBQXdDLG1CQUFPLENBQUMsMkdBQWM7QUFDOUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0VBQW9FLEVBQUU7QUFDdEU7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSxjQUFjO0FBQ2QsV0FBVyxnQkFBZ0I7QUFDM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQixVQUFVLElBQUksV0FBVztBQUM5QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMEJBQTBCLCtCQUErQjtBQUN6RDtBQUNBO0FBQ0EsNEJBQTRCLCtCQUErQjtBQUMzRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQjtBQUNuQjtBQUNBO0FBQ0EsNkNBQTZDO0FBQzdDO0FBQ0EsMEVBQTBFO0FBQzFFO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFud2FyXFxEb2N1bWVudHNcXEN1YmVudHdlYlxcY3ViZW50XFxub2RlX21vZHVsZXNcXC5wbnBtXFxAbG9ndGFpbCtuZXh0QDAuMi4wX25leHRAMTVfZTE5NTllNzQxYzZiZjQ3NjFmNDdlMWM4ZTY4MmZlZDZcXG5vZGVfbW9kdWxlc1xcQGxvZ3RhaWxcXG5leHRcXGRpc3RcXGxvZ2dlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbnZhciBfX2F3YWl0ZXIgPSAodGhpcyAmJiB0aGlzLl9fYXdhaXRlcikgfHwgZnVuY3Rpb24gKHRoaXNBcmcsIF9hcmd1bWVudHMsIFAsIGdlbmVyYXRvcikge1xuICAgIGZ1bmN0aW9uIGFkb3B0KHZhbHVlKSB7IHJldHVybiB2YWx1ZSBpbnN0YW5jZW9mIFAgPyB2YWx1ZSA6IG5ldyBQKGZ1bmN0aW9uIChyZXNvbHZlKSB7IHJlc29sdmUodmFsdWUpOyB9KTsgfVxuICAgIHJldHVybiBuZXcgKFAgfHwgKFAgPSBQcm9taXNlKSkoZnVuY3Rpb24gKHJlc29sdmUsIHJlamVjdCkge1xuICAgICAgICBmdW5jdGlvbiBmdWxmaWxsZWQodmFsdWUpIHsgdHJ5IHsgc3RlcChnZW5lcmF0b3IubmV4dCh2YWx1ZSkpOyB9IGNhdGNoIChlKSB7IHJlamVjdChlKTsgfSB9XG4gICAgICAgIGZ1bmN0aW9uIHJlamVjdGVkKHZhbHVlKSB7IHRyeSB7IHN0ZXAoZ2VuZXJhdG9yW1widGhyb3dcIl0odmFsdWUpKTsgfSBjYXRjaCAoZSkgeyByZWplY3QoZSk7IH0gfVxuICAgICAgICBmdW5jdGlvbiBzdGVwKHJlc3VsdCkgeyByZXN1bHQuZG9uZSA/IHJlc29sdmUocmVzdWx0LnZhbHVlKSA6IGFkb3B0KHJlc3VsdC52YWx1ZSkudGhlbihmdWxmaWxsZWQsIHJlamVjdGVkKTsgfVxuICAgICAgICBzdGVwKChnZW5lcmF0b3IgPSBnZW5lcmF0b3IuYXBwbHkodGhpc0FyZywgX2FyZ3VtZW50cyB8fCBbXSkpLm5leHQoKSk7XG4gICAgfSk7XG59O1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5wcmV0dHlQcmludCA9IGV4cG9ydHMubG9nID0gZXhwb3J0cy5Mb2dnZXIgPSBleHBvcnRzLkxvZ0xldmVsID0gdm9pZCAwO1xuY29uc3QgY29uZmlnXzEgPSByZXF1aXJlKFwiLi9jb25maWdcIik7XG5jb25zdCBzaGFyZWRfMSA9IHJlcXVpcmUoXCIuL3NoYXJlZFwiKTtcbmNvbnN0IHVybCA9IGNvbmZpZ18xLmNvbmZpZy5nZXRMb2dzRW5kcG9pbnQoKTtcbmNvbnN0IExPR19MRVZFTCA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0JFVFRFUl9TVEFDS19MT0dfTEVWRUwgfHwgJ2RlYnVnJztcbnZhciBMb2dMZXZlbDtcbihmdW5jdGlvbiAoTG9nTGV2ZWwpIHtcbiAgICBMb2dMZXZlbFtMb2dMZXZlbFtcImRlYnVnXCJdID0gMF0gPSBcImRlYnVnXCI7XG4gICAgTG9nTGV2ZWxbTG9nTGV2ZWxbXCJpbmZvXCJdID0gMV0gPSBcImluZm9cIjtcbiAgICBMb2dMZXZlbFtMb2dMZXZlbFtcIndhcm5cIl0gPSAyXSA9IFwid2FyblwiO1xuICAgIExvZ0xldmVsW0xvZ0xldmVsW1wiZXJyb3JcIl0gPSAzXSA9IFwiZXJyb3JcIjtcbiAgICBMb2dMZXZlbFtMb2dMZXZlbFtcIm9mZlwiXSA9IDEwMF0gPSBcIm9mZlwiO1xufSkoTG9nTGV2ZWwgfHwgKGV4cG9ydHMuTG9nTGV2ZWwgPSBMb2dMZXZlbCA9IHt9KSk7XG5jbGFzcyBMb2dnZXIge1xuICAgIGNvbnN0cnVjdG9yKGluaXRDb25maWcgPSB7fSkge1xuICAgICAgICB0aGlzLmluaXRDb25maWcgPSBpbml0Q29uZmlnO1xuICAgICAgICB0aGlzLmxvZ0V2ZW50cyA9IFtdO1xuICAgICAgICB0aGlzLnRocm90dGxlZFNlbmRMb2dzID0gKDAsIHNoYXJlZF8xLnRocm90dGxlKSh0aGlzLnNlbmRMb2dzLCAxMDAwKTtcbiAgICAgICAgdGhpcy5jaGlsZHJlbiA9IFtdO1xuICAgICAgICB0aGlzLmxvZ0xldmVsID0gTG9nTGV2ZWwuZGVidWc7XG4gICAgICAgIHRoaXMuY29uZmlnID0ge1xuICAgICAgICAgICAgYXV0b0ZsdXNoOiB0cnVlLFxuICAgICAgICAgICAgc291cmNlOiAnZnJvbnRlbmQtbG9nJyxcbiAgICAgICAgICAgIHByZXR0eVByaW50OiBwcmV0dHlQcmludCxcbiAgICAgICAgfTtcbiAgICAgICAgdGhpcy5kZWJ1ZyA9IChtZXNzYWdlLCBhcmdzID0ge30pID0+IHtcbiAgICAgICAgICAgIHRoaXMubG9nKExvZ0xldmVsLmRlYnVnLCBtZXNzYWdlLCBhcmdzKTtcbiAgICAgICAgfTtcbiAgICAgICAgdGhpcy5pbmZvID0gKG1lc3NhZ2UsIGFyZ3MgPSB7fSkgPT4ge1xuICAgICAgICAgICAgdGhpcy5sb2coTG9nTGV2ZWwuaW5mbywgbWVzc2FnZSwgYXJncyk7XG4gICAgICAgIH07XG4gICAgICAgIHRoaXMud2FybiA9IChtZXNzYWdlLCBhcmdzID0ge30pID0+IHtcbiAgICAgICAgICAgIHRoaXMubG9nKExvZ0xldmVsLndhcm4sIG1lc3NhZ2UsIGFyZ3MpO1xuICAgICAgICB9O1xuICAgICAgICB0aGlzLmVycm9yID0gKG1lc3NhZ2UsIGFyZ3MgPSB7fSkgPT4ge1xuICAgICAgICAgICAgdGhpcy5sb2coTG9nTGV2ZWwuZXJyb3IsIG1lc3NhZ2UsIGFyZ3MpO1xuICAgICAgICB9O1xuICAgICAgICB0aGlzLndpdGggPSAoYXJncykgPT4ge1xuICAgICAgICAgICAgY29uc3QgY29uZmlnID0gT2JqZWN0LmFzc2lnbihPYmplY3QuYXNzaWduKHt9LCB0aGlzLmNvbmZpZyksIHsgYXJnczogT2JqZWN0LmFzc2lnbihPYmplY3QuYXNzaWduKHt9LCB0aGlzLmNvbmZpZy5hcmdzKSwgYXJncykgfSk7XG4gICAgICAgICAgICBjb25zdCBjaGlsZCA9IG5ldyBMb2dnZXIoY29uZmlnKTtcbiAgICAgICAgICAgIHRoaXMuY2hpbGRyZW4ucHVzaChjaGlsZCk7XG4gICAgICAgICAgICByZXR1cm4gY2hpbGQ7XG4gICAgICAgIH07XG4gICAgICAgIHRoaXMud2l0aFJlcXVlc3QgPSAocmVxKSA9PiB7XG4gICAgICAgICAgICByZXR1cm4gbmV3IExvZ2dlcihPYmplY3QuYXNzaWduKE9iamVjdC5hc3NpZ24oe30sIHRoaXMuY29uZmlnKSwgeyByZXE6IE9iamVjdC5hc3NpZ24oT2JqZWN0LmFzc2lnbih7fSwgdGhpcy5jb25maWcucmVxKSwgcmVxKSB9KSk7XG4gICAgICAgIH07XG4gICAgICAgIHRoaXMuX3RyYW5zZm9ybUV2ZW50ID0gKGxldmVsLCBtZXNzYWdlLCBhcmdzID0ge30pID0+IHtcbiAgICAgICAgICAgIGNvbnN0IGxvZ0V2ZW50ID0ge1xuICAgICAgICAgICAgICAgIGxldmVsOiBMb2dMZXZlbFtsZXZlbF0udG9TdHJpbmcoKSxcbiAgICAgICAgICAgICAgICBtZXNzYWdlLFxuICAgICAgICAgICAgICAgIGR0OiBuZXcgRGF0ZShEYXRlLm5vdygpKS50b0lTT1N0cmluZygpLFxuICAgICAgICAgICAgICAgIHNvdXJjZTogdGhpcy5jb25maWcuc291cmNlLFxuICAgICAgICAgICAgICAgIGZpZWxkczogdGhpcy5jb25maWcuYXJncyB8fCB7fSxcbiAgICAgICAgICAgICAgICAnQGFwcCc6IHtcbiAgICAgICAgICAgICAgICAgICAgJ25leHQtbG9ndGFpbC12ZXJzaW9uJzogY29uZmlnXzEuVmVyc2lvbixcbiAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgfTtcbiAgICAgICAgICAgIC8vIGNoZWNrIGlmIHBhc3NlZCBhcmdzIGlzIGFuIG9iamVjdCwgaWYgaXRzIG5vdCBhbiBvYmplY3QsIGFkZCBpdCB0byBmaWVsZHMuYXJnc1xuICAgICAgICAgICAgaWYgKGFyZ3MgaW5zdGFuY2VvZiBFcnJvcikge1xuICAgICAgICAgICAgICAgIGxvZ0V2ZW50LmZpZWxkcyA9IE9iamVjdC5hc3NpZ24oT2JqZWN0LmFzc2lnbih7fSwgbG9nRXZlbnQuZmllbGRzKSwgeyBtZXNzYWdlOiBhcmdzLm1lc3NhZ2UsIHN0YWNrOiBhcmdzLnN0YWNrLCBuYW1lOiBhcmdzLm5hbWUgfSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIGlmICh0eXBlb2YgYXJncyA9PT0gJ29iamVjdCcgJiYgYXJncyAhPT0gbnVsbCAmJiBPYmplY3Qua2V5cyhhcmdzKS5sZW5ndGggPiAwKSB7XG4gICAgICAgICAgICAgICAgY29uc3QgcGFyc2VkQXJncyA9IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkoYXJncywganNvbkZyaWVuZGx5RXJyb3JSZXBsYWNlcikpO1xuICAgICAgICAgICAgICAgIGxvZ0V2ZW50LmZpZWxkcyA9IE9iamVjdC5hc3NpZ24oT2JqZWN0LmFzc2lnbih7fSwgbG9nRXZlbnQuZmllbGRzKSwgcGFyc2VkQXJncyk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIGlmIChhcmdzICYmIGFyZ3MubGVuZ3RoKSB7XG4gICAgICAgICAgICAgICAgbG9nRXZlbnQuZmllbGRzID0gT2JqZWN0LmFzc2lnbihPYmplY3QuYXNzaWduKHt9LCBsb2dFdmVudC5maWVsZHMpLCB7IGFyZ3M6IGFyZ3MgfSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjb25maWdfMS5jb25maWcuaW5qZWN0UGxhdGZvcm1NZXRhZGF0YShsb2dFdmVudCwgdGhpcy5jb25maWcuc291cmNlKTtcbiAgICAgICAgICAgIGlmICh0aGlzLmNvbmZpZy5yZXEgIT0gbnVsbCkge1xuICAgICAgICAgICAgICAgIGxvZ0V2ZW50LnJlcXVlc3QgPSB0aGlzLmNvbmZpZy5yZXE7XG4gICAgICAgICAgICAgICAgaWYgKGxvZ0V2ZW50LnBsYXRmb3JtKSB7XG4gICAgICAgICAgICAgICAgICAgIGxvZ0V2ZW50LnBsYXRmb3JtLnJvdXRlID0gdGhpcy5jb25maWcucmVxLnBhdGg7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGVsc2UgaWYgKGxvZ0V2ZW50LnZlcmNlbCkge1xuICAgICAgICAgICAgICAgICAgICBsb2dFdmVudC52ZXJjZWwucm91dGUgPSB0aGlzLmNvbmZpZy5yZXEucGF0aDtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm4gbG9nRXZlbnQ7XG4gICAgICAgIH07XG4gICAgICAgIHRoaXMubG9nID0gKGxldmVsLCBtZXNzYWdlLCBhcmdzID0ge30pID0+IHtcbiAgICAgICAgICAgIGlmIChsZXZlbCA8IHRoaXMubG9nTGV2ZWwpIHtcbiAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjb25zdCBsb2dFdmVudCA9IHRoaXMuX3RyYW5zZm9ybUV2ZW50KGxldmVsLCBtZXNzYWdlLCBhcmdzKTtcbiAgICAgICAgICAgIHRoaXMubG9nRXZlbnRzLnB1c2gobG9nRXZlbnQpO1xuICAgICAgICAgICAgaWYgKHRoaXMuY29uZmlnLmF1dG9GbHVzaCkge1xuICAgICAgICAgICAgICAgIHRoaXMudGhyb3R0bGVkU2VuZExvZ3MoKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfTtcbiAgICAgICAgdGhpcy5hdHRhY2hSZXNwb25zZVN0YXR1cyA9IChzdGF0dXNDb2RlKSA9PiB7XG4gICAgICAgICAgICB0aGlzLmxvZ0V2ZW50cyA9IHRoaXMubG9nRXZlbnRzLm1hcCgobG9nKSA9PiB7XG4gICAgICAgICAgICAgICAgaWYgKGxvZy5yZXF1ZXN0KSB7XG4gICAgICAgICAgICAgICAgICAgIGxvZy5yZXF1ZXN0LnN0YXR1c0NvZGUgPSBzdGF0dXNDb2RlO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICByZXR1cm4gbG9nO1xuICAgICAgICAgICAgfSk7XG4gICAgICAgIH07XG4gICAgICAgIHRoaXMuZmx1c2ggPSAoKSA9PiBfX2F3YWl0ZXIodGhpcywgdm9pZCAwLCB2b2lkIDAsIGZ1bmN0aW9uKiAoKSB7XG4gICAgICAgICAgICB5aWVsZCBQcm9taXNlLmFsbChbdGhpcy5zZW5kTG9ncygpLCAuLi50aGlzLmNoaWxkcmVuLm1hcCgoYykgPT4gYy5mbHVzaCgpKV0pO1xuICAgICAgICB9KTtcbiAgICAgICAgLy8gY2hlY2sgaWYgdXNlciBwYXNzZWQgYSBsb2cgbGV2ZWwsIGlmIG5vdCB0aGUgZGVmYXVsdCBpbml0IHZhbHVlIHdpbGwgYmUgdXNlZCBhcyBpcy5cbiAgICAgICAgaWYgKHRoaXMuaW5pdENvbmZpZy5sb2dMZXZlbCAhPSB1bmRlZmluZWQgJiYgdGhpcy5pbml0Q29uZmlnLmxvZ0xldmVsID49IDApIHtcbiAgICAgICAgICAgIHRoaXMubG9nTGV2ZWwgPSB0aGlzLmluaXRDb25maWcubG9nTGV2ZWw7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAoTE9HX0xFVkVMKSB7XG4gICAgICAgICAgICB0aGlzLmxvZ0xldmVsID0gTG9nTGV2ZWxbTE9HX0xFVkVMXTtcbiAgICAgICAgfVxuICAgICAgICB0aGlzLmNvbmZpZyA9IE9iamVjdC5hc3NpZ24oT2JqZWN0LmFzc2lnbih7fSwgdGhpcy5jb25maWcpLCBpbml0Q29uZmlnKTtcbiAgICB9XG4gICAgbG9nSHR0cFJlcXVlc3QobGV2ZWwsIG1lc3NhZ2UsIHJlcXVlc3QsIGFyZ3MpIHtcbiAgICAgICAgY29uc3QgbG9nRXZlbnQgPSB0aGlzLl90cmFuc2Zvcm1FdmVudChsZXZlbCwgbWVzc2FnZSwgYXJncyk7XG4gICAgICAgIGxvZ0V2ZW50LnJlcXVlc3QgPSByZXF1ZXN0O1xuICAgICAgICB0aGlzLmxvZ0V2ZW50cy5wdXNoKGxvZ0V2ZW50KTtcbiAgICAgICAgaWYgKHRoaXMuY29uZmlnLmF1dG9GbHVzaCkge1xuICAgICAgICAgICAgdGhpcy50aHJvdHRsZWRTZW5kTG9ncygpO1xuICAgICAgICB9XG4gICAgfVxuICAgIG1pZGRsZXdhcmUocmVxdWVzdCwgY29uZmlnKSB7XG4gICAgICAgIHZhciBfYTtcbiAgICAgICAgY29uc3QgcmVxID0ge1xuICAgICAgICAgICAgLy8gQHRzLWlnbm9yZSBOZXh0UmVxdWVzdC5pcCB3YXMgcmVtb3ZlZCBpbiBOZXh0IDE1LCB3b3JrcyB3aXRoIHVuZGVmaW5lZFxuICAgICAgICAgICAgaXA6IHJlcXVlc3QuaXAsXG4gICAgICAgICAgICAvLyBAdHMtaWdub3JlIE5leHRSZXF1ZXN0LmlwIHdhcyByZW1vdmVkIGluIE5leHQgMTUsIHdvcmtzIHdpdGggdW5kZWZpbmVkXG4gICAgICAgICAgICByZWdpb246IChfYSA9IHJlcXVlc3QuZ2VvKSA9PT0gbnVsbCB8fCBfYSA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2EucmVnaW9uLFxuICAgICAgICAgICAgbWV0aG9kOiByZXF1ZXN0Lm1ldGhvZCxcbiAgICAgICAgICAgIGhvc3Q6IHJlcXVlc3QubmV4dFVybC5ob3N0bmFtZSxcbiAgICAgICAgICAgIHBhdGg6IHJlcXVlc3QubmV4dFVybC5wYXRobmFtZSxcbiAgICAgICAgICAgIHNjaGVtZTogcmVxdWVzdC5uZXh0VXJsLnByb3RvY29sLnNwbGl0KCc6JylbMF0sXG4gICAgICAgICAgICByZWZlcmVyOiByZXF1ZXN0LmhlYWRlcnMuZ2V0KCdSZWZlcmVyJyksXG4gICAgICAgICAgICB1c2VyQWdlbnQ6IHJlcXVlc3QuaGVhZGVycy5nZXQoJ3VzZXItYWdlbnQnKSxcbiAgICAgICAgfTtcbiAgICAgICAgY29uc3QgbWVzc2FnZSA9IGAke3JlcXVlc3QubWV0aG9kfSAke3JlcXVlc3QubmV4dFVybC5wYXRobmFtZX1gO1xuICAgICAgICBpZiAoY29uZmlnID09PSBudWxsIHx8IGNvbmZpZyA9PT0gdm9pZCAwID8gdm9pZCAwIDogY29uZmlnLmxvZ1JlcXVlc3REZXRhaWxzKSB7XG4gICAgICAgICAgICByZXR1cm4gKDAsIHNoYXJlZF8xLnJlcXVlc3RUb0pTT04pKHJlcXVlc3QpLnRoZW4oKGRldGFpbHMpID0+IHtcbiAgICAgICAgICAgICAgICBjb25zdCBuZXdSZXEgPSBPYmplY3QuYXNzaWduKE9iamVjdC5hc3NpZ24oe30sIHJlcSksIHsgZGV0YWlsczogQXJyYXkuaXNBcnJheShjb25maWcubG9nUmVxdWVzdERldGFpbHMpXG4gICAgICAgICAgICAgICAgICAgICAgICA/IE9iamVjdC5mcm9tRW50cmllcyhPYmplY3QuZW50cmllcyhkZXRhaWxzKS5maWx0ZXIoKFtrZXldKSA9PiBjb25maWcubG9nUmVxdWVzdERldGFpbHMuaW5jbHVkZXMoa2V5KSkpXG4gICAgICAgICAgICAgICAgICAgICAgICA6IGRldGFpbHMgfSk7XG4gICAgICAgICAgICAgICAgcmV0dXJuIHRoaXMubG9nSHR0cFJlcXVlc3QoTG9nTGV2ZWwuaW5mbywgbWVzc2FnZSwgbmV3UmVxLCB7fSk7XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gdGhpcy5sb2dIdHRwUmVxdWVzdChMb2dMZXZlbC5pbmZvLCBtZXNzYWdlLCByZXEsIHt9KTtcbiAgICB9XG4gICAgc2VuZExvZ3MoKSB7XG4gICAgICAgIHJldHVybiBfX2F3YWl0ZXIodGhpcywgdm9pZCAwLCB2b2lkIDAsIGZ1bmN0aW9uKiAoKSB7XG4gICAgICAgICAgICBpZiAoIXRoaXMubG9nRXZlbnRzLmxlbmd0aCkge1xuICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIC8vIFRvIHNlbmQgbG9ncyBvdmVyIHRoZSBuZXR3b3JrLCB3ZSBuZWVkIG9uZSBvZjpcbiAgICAgICAgICAgIC8vXG4gICAgICAgICAgICAvLyAtIEluZ2VzdGluZyBVUkwgYW5kIHNvdXJjZSB0b2tlblxuICAgICAgICAgICAgLy8gLSBDdXN0b20gZW5kcG9pbnRcbiAgICAgICAgICAgIC8vXG4gICAgICAgICAgICAvLyBXZSBmYWxsIGJhY2sgdG8gcHJpbnRpbmcgdG8gY29uc29sZSB0byBhdm9pZCBuZXR3b3JrIGVycm9ycyBpblxuICAgICAgICAgICAgLy8gZGV2ZWxvcG1lbnQgZW52aXJvbm1lbnRzLlxuICAgICAgICAgICAgaWYgKCFjb25maWdfMS5jb25maWcuaXNFbnZWYXJzU2V0KCkpIHtcbiAgICAgICAgICAgICAgICB0aGlzLmxvZ0V2ZW50cy5mb3JFYWNoKChldikgPT4gKHRoaXMuY29uZmlnLnByZXR0eVByaW50ID8gdGhpcy5jb25maWcucHJldHR5UHJpbnQoZXYpIDogcHJldHR5UHJpbnQoZXYpKSk7XG4gICAgICAgICAgICAgICAgdGhpcy5sb2dFdmVudHMgPSBbXTtcbiAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjb25zdCBtZXRob2QgPSAnUE9TVCc7XG4gICAgICAgICAgICBjb25zdCBrZWVwYWxpdmUgPSB0cnVlO1xuICAgICAgICAgICAgY29uc3QgYm9keSA9IEpTT04uc3RyaW5naWZ5KHRoaXMubG9nRXZlbnRzKTtcbiAgICAgICAgICAgIC8vIGNsZWFyIHBlbmRpbmcgbG9nc1xuICAgICAgICAgICAgdGhpcy5sb2dFdmVudHMgPSBbXTtcbiAgICAgICAgICAgIGNvbnN0IGhlYWRlcnMgPSB7XG4gICAgICAgICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgICAgICAgICAnVXNlci1BZ2VudCc6ICduZXh0LWxvZ3RhaWwvdicgKyBjb25maWdfMS5WZXJzaW9uLFxuICAgICAgICAgICAgfTtcbiAgICAgICAgICAgIGlmIChjb25maWdfMS5jb25maWcudG9rZW4pIHtcbiAgICAgICAgICAgICAgICBoZWFkZXJzWydBdXRob3JpemF0aW9uJ10gPSBgQmVhcmVyICR7Y29uZmlnXzEuY29uZmlnLnRva2VufWA7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjb25zdCByZXFPcHRpb25zID0geyBib2R5LCBtZXRob2QsIGtlZXBhbGl2ZSwgaGVhZGVycyB9O1xuICAgICAgICAgICAgZnVuY3Rpb24gc2VuZEZhbGxiYWNrKCkge1xuICAgICAgICAgICAgICAgIC8vIERvIG5vdCBsZWFrIG5ldHdvcmsgZXJyb3JzOyBkb2VzIG5vdCBhZmZlY3QgdGhlIHJ1bm5pbmcgYXBwXG4gICAgICAgICAgICAgICAgcmV0dXJuIGZldGNoKHVybCwgcmVxT3B0aW9ucykuY2F0Y2goY29uc29sZS5lcnJvcik7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgIGlmICh0eXBlb2YgZmV0Y2ggPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGZldGNoID0geWllbGQgcmVxdWlyZSgnd2hhdHdnLWZldGNoJyk7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBmZXRjaCh1cmwsIHJlcU9wdGlvbnMpLmNhdGNoKGNvbnNvbGUuZXJyb3IpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBlbHNlIGlmIChjb25maWdfMS5pc0Jyb3dzZXIgJiYgY29uZmlnXzEuaXNWZXJjZWwgJiYgbmF2aWdhdG9yLnNlbmRCZWFjb24pIHtcbiAgICAgICAgICAgICAgICAgICAgLy8gc2VuZEJlYWNvbiBmYWlscyBpZiBtZXNzYWdlIHNpemUgaXMgZ3JlYXRlciB0aGFuIDY0a2IsIHNvXG4gICAgICAgICAgICAgICAgICAgIC8vIHdlIGZhbGwgYmFjayB0byBmZXRjaC5cbiAgICAgICAgICAgICAgICAgICAgLy8gTmF2aWdhdG9yIGhhcyB0byBiZSBib3VuZCB0byBlbnN1cmUgaXQgZG9lcyBub3QgZXJyb3IgaW4gc29tZSBicm93c2Vyc1xuICAgICAgICAgICAgICAgICAgICAvLyBodHRwczovL3hnd2FuZy5tZS9wb3N0cy95b3UtbWF5LW5vdC1rbm93LWJlYWNvbi8jaXQtbWF5LXRocm93LWVycm9yJTJDLWJlLXN1cmUtdG8tY2F0Y2hcbiAgICAgICAgICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmICghbmF2aWdhdG9yLnNlbmRCZWFjb24uYmluZChuYXZpZ2F0b3IpKHVybCwgYm9keSkpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gc2VuZEZhbGxiYWNrKCk7XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gc2VuZEZhbGxiYWNrKCk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBzZW5kRmFsbGJhY2soKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjYXRjaCAoZSkge1xuICAgICAgICAgICAgICAgIGNvbnNvbGUud2FybihgRmFpbGVkIHRvIHNlbmQgbG9ncyB0byBCZXR0ZXJTdGFjazogJHtlfWApO1xuICAgICAgICAgICAgICAgIC8vIHB1dCB0aGUgbG9nIGV2ZW50cyBiYWNrIGluIHRoZSBxdWV1ZVxuICAgICAgICAgICAgICAgIHRoaXMubG9nRXZlbnRzID0gWy4uLnRoaXMubG9nRXZlbnRzLCBKU09OLnBhcnNlKGJvZHkpXTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSk7XG4gICAgfVxufVxuZXhwb3J0cy5Mb2dnZXIgPSBMb2dnZXI7XG5leHBvcnRzLmxvZyA9IG5ldyBMb2dnZXIoe30pO1xuY29uc3QgbGV2ZWxDb2xvcnMgPSB7XG4gICAgaW5mbzoge1xuICAgICAgICB0ZXJtaW5hbDogJzMyJyxcbiAgICAgICAgYnJvd3NlcjogJ2xpZ2h0Z3JlZW4nLFxuICAgIH0sXG4gICAgZGVidWc6IHtcbiAgICAgICAgdGVybWluYWw6ICczNicsXG4gICAgICAgIGJyb3dzZXI6ICdsaWdodGJsdWUnLFxuICAgIH0sXG4gICAgd2Fybjoge1xuICAgICAgICB0ZXJtaW5hbDogJzMzJyxcbiAgICAgICAgYnJvd3NlcjogJ3llbGxvdycsXG4gICAgfSxcbiAgICBlcnJvcjoge1xuICAgICAgICB0ZXJtaW5hbDogJzMxJyxcbiAgICAgICAgYnJvd3NlcjogJ3JlZCcsXG4gICAgfSxcbn07XG5mdW5jdGlvbiBwcmV0dHlQcmludChldikge1xuICAgIGNvbnN0IGhhc0ZpZWxkcyA9IE9iamVjdC5rZXlzKGV2LmZpZWxkcykubGVuZ3RoID4gMDtcbiAgICAvLyBjaGVjayB3aGV0aGVyIHByZXR0eSBwcmludCBpcyBkaXNhYmxlZFxuICAgIGlmIChzaGFyZWRfMS5pc05vUHJldHR5UHJpbnQpIHtcbiAgICAgICAgbGV0IG1zZyA9IGAke2V2LmxldmVsfSAtICR7ZXYubWVzc2FnZX1gO1xuICAgICAgICBpZiAoaGFzRmllbGRzKSB7XG4gICAgICAgICAgICBtc2cgKz0gJyAnICsgSlNPTi5zdHJpbmdpZnkoZXYuZmllbGRzKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zb2xlLmxvZyhtc2cpO1xuICAgICAgICByZXR1cm47XG4gICAgfVxuICAgIC8vIHByaW50IGluZGVudGVkIG1lc3NhZ2UsIGluc3RlYWQgb2YgW29iamVjdF1cbiAgICAvLyBXZSB1c2UgdGhlICVvIG1vZGlmaWVyIGluc3RlYWQgb2YgSlNPTi5zdHJpbmdpZnkgYmVjYXVzZSBzdHJpbmdpZnkgd2lsbCBwcmludCB0aGVcbiAgICAvLyBvYmplY3QgYXMgbm9ybWFsIHRleHQsIGl0IGxvc2VzIGFsbCB0aGUgZnVuY3Rpb25hbGl0eSB0aGUgYnJvd3NlciBnaXZlcyBmb3Igdmlld2luZ1xuICAgIC8vIG9iamVjdHMgaW4gdGhlIGNvbnNvbGUsIHN1Y2ggYXMgZXhwYW5kaW5nIGFuZCBjb2xsYXBzaW5nIHRoZSBvYmplY3QuXG4gICAgbGV0IG1zZ1N0cmluZyA9ICcnO1xuICAgIGxldCBhcmdzID0gW2V2LmxldmVsLCBldi5tZXNzYWdlXTtcbiAgICBpZiAoY29uZmlnXzEuaXNCcm93c2VyKSB7XG4gICAgICAgIG1zZ1N0cmluZyA9ICclYyVzIC0gJXMnO1xuICAgICAgICBhcmdzID0gW2Bjb2xvcjogJHtsZXZlbENvbG9yc1tldi5sZXZlbF0uYnJvd3Nlcn07YCwgLi4uYXJnc107XG4gICAgfVxuICAgIGVsc2Uge1xuICAgICAgICBtc2dTdHJpbmcgPSBgXFx4MWJbJHtsZXZlbENvbG9yc1tldi5sZXZlbF0udGVybWluYWx9bSVzXFx4MWJbMG0gLSAlc2A7XG4gICAgfVxuICAgIC8vIHdlIGNoZWNrIGlmIHRoZSBmaWVsZHMgb2JqZWN0IGlzIG5vdCBlbXB0eSwgb3RoZXJ3aXNlIGl0cyBwcmludGVkIGFzIDxlbXB0eSBzdHJpbmc+XG4gICAgLy8gb3IganVzdCBcIlwiLlxuICAgIGlmIChoYXNGaWVsZHMpIHtcbiAgICAgICAgbXNnU3RyaW5nICs9ICcgJW8nO1xuICAgICAgICBhcmdzLnB1c2goZXYuZmllbGRzKTtcbiAgICB9XG4gICAgaWYgKGV2LnJlcXVlc3QpIHtcbiAgICAgICAgbXNnU3RyaW5nICs9ICcgJW8nO1xuICAgICAgICBhcmdzLnB1c2goZXYucmVxdWVzdCk7XG4gICAgfVxuICAgIGNvbnNvbGUubG9nLmFwcGx5KGNvbnNvbGUsIFttc2dTdHJpbmcsIC4uLmFyZ3NdKTtcbn1cbmV4cG9ydHMucHJldHR5UHJpbnQgPSBwcmV0dHlQcmludDtcbmZ1bmN0aW9uIGpzb25GcmllbmRseUVycm9yUmVwbGFjZXIoa2V5LCB2YWx1ZSkge1xuICAgIGlmICh2YWx1ZSBpbnN0YW5jZW9mIEVycm9yKSB7XG4gICAgICAgIHJldHVybiBPYmplY3QuYXNzaWduKE9iamVjdC5hc3NpZ24oe30sIHZhbHVlKSwgeyBcbiAgICAgICAgICAgIC8vIEV4cGxpY2l0bHkgcHVsbCBFcnJvcidzIG5vbi1lbnVtZXJhYmxlIHByb3BlcnRpZXNcbiAgICAgICAgICAgIG5hbWU6IHZhbHVlLm5hbWUsIG1lc3NhZ2U6IHZhbHVlLm1lc3NhZ2UsIHN0YWNrOiB2YWx1ZS5zdGFjayB9KTtcbiAgICB9XG4gICAgcmV0dXJuIHZhbHVlO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bG9nZ2VyLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/logger.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/platform/base.js":
/*!**********************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/platform/base.js ***!
  \**********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=base.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0Bsb2d0YWlsK25leHRAMC4yLjBfbmV4dEAxNV9lMTk1OWU3NDFjNmJmNDc2MWY0N2UxYzhlNjgyZmVkNi9ub2RlX21vZHVsZXMvQGxvZ3RhaWwvbmV4dC9kaXN0L3BsYXRmb3JtL2Jhc2UuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYW53YXJcXERvY3VtZW50c1xcQ3ViZW50d2ViXFxjdWJlbnRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBsb2d0YWlsK25leHRAMC4yLjBfbmV4dEAxNV9lMTk1OWU3NDFjNmJmNDc2MWY0N2UxYzhlNjgyZmVkNlxcbm9kZV9tb2R1bGVzXFxAbG9ndGFpbFxcbmV4dFxcZGlzdFxccGxhdGZvcm1cXGJhc2UuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1iYXNlLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/platform/base.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/platform/generic.js":
/*!*************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/platform/generic.js ***!
  \*************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst shared_1 = __webpack_require__(/*! ../shared */ \"(ssr)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/shared.js\");\nconst config_1 = __webpack_require__(/*! ../config */ \"(ssr)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/config.js\");\n// This is the generic config class for all platforms that doesn't have a special\n// implementation (e.g: vercel, netlify). All config classes extends this one.\nclass GenericConfig {\n    constructor() {\n        this.proxyPath = '/_betterstack';\n        this.shouldSendEdgeReport = false;\n        this.token = process.env.NEXT_PUBLIC_BETTER_STACK_SOURCE_TOKEN || process.env.BETTER_STACK_SOURCE_TOKEN || process.env.NEXT_PUBLIC_LOGTAIL_SOURCE_TOKEN || process.env.LOGTAIL_SOURCE_TOKEN;\n        this.environment = \"development\";\n        this.ingestingUrl = process.env.NEXT_PUBLIC_BETTER_STACK_INGESTING_URL || process.env.BETTER_STACK_INGESTING_URL || process.env.NEXT_PUBLIC_LOGTAIL_URL || process.env.LOGTAIL_URL;\n        this.region = process.env.REGION || undefined;\n        this.customEndpoint = process.env.NEXT_PUBLIC_BETTER_STACK_CUSTOM_ENDPOINT;\n    }\n    isEnvVarsSet() {\n        return !!(this.ingestingUrl && this.token) || !!this.customEndpoint;\n    }\n    getIngestURL(_) {\n        return this.ingestingUrl || \"\";\n    }\n    getLogsEndpoint() {\n        if (config_1.isBrowser && this.customEndpoint) {\n            return this.customEndpoint;\n        }\n        return config_1.isBrowser ? `${this.proxyPath}/logs` : this.getIngestURL(shared_1.EndpointType.logs);\n    }\n    getWebVitalsEndpoint() {\n        if (config_1.isBrowser && this.customEndpoint) {\n            return this.customEndpoint;\n        }\n        return config_1.isBrowser ? `${this.proxyPath}/web-vitals` : this.getIngestURL(shared_1.EndpointType.webVitals);\n    }\n    wrapWebVitalsObject(metrics) {\n        return metrics.map(m => ({\n            webVital: m,\n            dt: new Date().getTime(),\n            platform: {\n                environment: this.environment,\n                source: 'web-vital',\n            },\n            source: 'web-vital'\n        }));\n    }\n    injectPlatformMetadata(logEvent, source) {\n        logEvent.source = source;\n        logEvent.platform = {\n            environment: this.environment,\n            region: this.region,\n            source: source,\n        };\n    }\n    getHeaderOrDefault(req, headerName, defaultValue) {\n        return req.headers[headerName] ? req.headers[headerName] : defaultValue;\n    }\n}\nexports[\"default\"] = GenericConfig;\n//# sourceMappingURL=generic.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/platform/generic.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/platform/netlify.js":
/*!*************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/platform/netlify.js ***!
  \*************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst generic_1 = __importDefault(__webpack_require__(/*! ./generic */ \"(ssr)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/platform/generic.js\"));\nconst netlifySiteId = process.env.SITE_ID;\nconst netlifyBuildId = process.env.BUILD_ID;\nconst netlifyContext = process.env.CONTEXT;\nconst netlifyDeploymentUrl = process.env.DEPLOYMENT_URL;\nconst netlifyDeploymentId = process.env.DEPLOYMENT_ID;\nclass NetlifyConfig extends generic_1.default {\n    wrapWebVitalsObject(metrics) {\n        return metrics.map(m => ({\n            webVital: m,\n            dt: new Date().getTime(),\n            netlify: {\n                environment: this.environment,\n                source: 'web-vital',\n                siteId: netlifySiteId,\n                buildId: netlifyBuildId,\n                context: netlifyContext,\n                deploymentUrl: netlifyDeploymentUrl,\n                deploymentId: netlifyDeploymentId,\n            },\n        }));\n    }\n    injectPlatformMetadata(logEvent, source) {\n        logEvent.netlify = {\n            environment: this.environment,\n            region: source === 'edge' ? process.env.DENO_REGION : process.env.AWS_REGION,\n            source: source,\n            siteId: netlifySiteId,\n            buildId: netlifyBuildId,\n            context: netlifyContext,\n            deploymentUrl: netlifyDeploymentUrl,\n            deploymentId: source === 'edge' ? process.env.DENO_DEPLOYMENT_ID : netlifyDeploymentId,\n        };\n    }\n}\nexports[\"default\"] = NetlifyConfig;\n//# sourceMappingURL=netlify.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/platform/netlify.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/platform/vercel.js":
/*!************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/platform/vercel.js ***!
  \************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst generic_1 = __importDefault(__webpack_require__(/*! ./generic */ \"(ssr)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/platform/generic.js\"));\nclass VercelConfig extends generic_1.default {\n    constructor() {\n        super(...arguments);\n        this.shouldSendEdgeReport = true;\n        this.region = process.env.VERCEL_REGION || undefined;\n        this.environment = process.env.VERCEL_ENV || \"development\" || 0;\n    }\n    wrapWebVitalsObject(metrics) {\n        return metrics.map(m => ({\n            webVital: m,\n            dt: new Date().getTime(),\n            vercel: {\n                environment: this.environment,\n                source: 'web-vital',\n                deploymentId: process.env.VERCEL_DEPLOYMENT_ID,\n                deploymentUrl: process.env.NEXT_PUBLIC_VERCEL_URL,\n                project: process.env.NEXT_PUBLIC_VERCEL_PROJECT_PRODUCTION_URL,\n                git: {\n                    commit: process.env.NEXT_PUBLIC_VERCEL_GIT_COMMIT_SHA,\n                    repo: process.env.NEXT_PUBLIC_VERCEL_GIT_REPO_SLUG,\n                    ref: process.env.NEXT_PUBLIC_VERCEL_GIT_COMMIT_REF,\n                },\n            },\n        }));\n    }\n    injectPlatformMetadata(logEvent, source) {\n        logEvent.vercel = {\n            environment: this.environment,\n            region: this.region,\n            source: source,\n            deploymentId: process.env.VERCEL_DEPLOYMENT_ID,\n            deploymentUrl: process.env.NEXT_PUBLIC_VERCEL_URL,\n            project: process.env.NEXT_PUBLIC_VERCEL_PROJECT_PRODUCTION_URL,\n            git: {\n                commit: process.env.NEXT_PUBLIC_VERCEL_GIT_COMMIT_SHA,\n                repo: process.env.NEXT_PUBLIC_VERCEL_GIT_REPO_SLUG,\n                ref: process.env.NEXT_PUBLIC_VERCEL_GIT_COMMIT_REF,\n            },\n        };\n    }\n}\nexports[\"default\"] = VercelConfig;\n//# sourceMappingURL=vercel.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/platform/vercel.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/shared.js":
/*!***************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/shared.js ***!
  \***************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports) {

"use strict";
eval("\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.throttle = exports.requestToJSON = exports.EndpointType = exports.isNoPrettyPrint = void 0;\nexports.isNoPrettyPrint = process.env.BETTER_STACK_NO_PRETTY_PRINT == 'true' ? true : false;\nvar EndpointType;\n(function (EndpointType) {\n    EndpointType[\"webVitals\"] = \"web-vitals\";\n    EndpointType[\"logs\"] = \"logs\";\n})(EndpointType || (exports.EndpointType = EndpointType = {}));\n/**\n * Transforms a NextRequest or Request object into a JSON-serializable object\n */\nfunction requestToJSON(request) {\n    var _a;\n    return __awaiter(this, void 0, void 0, function* () {\n        // Get all headers\n        const headers = {};\n        request.headers.forEach((value, key) => {\n            headers[key] = value;\n        });\n        let cookiesData = {};\n        if ('cookies' in request) {\n            request.cookies.getAll().forEach((cookie) => {\n                cookiesData[cookie.name] = cookie.value;\n            });\n        }\n        else {\n            const cookieHeader = headers['cookie'];\n            if (cookieHeader) {\n                cookiesData = Object.fromEntries(cookieHeader.split(';').map((cookie) => {\n                    const [key, value] = cookie.trim().split('=');\n                    return [key, value];\n                }));\n            }\n        }\n        let nextUrlData;\n        if ('nextUrl' in request) {\n            const nextUrl = request.nextUrl;\n            nextUrlData = {\n                basePath: nextUrl.basePath,\n                buildId: nextUrl.buildId,\n                hash: nextUrl.hash,\n                host: nextUrl.host,\n                hostname: nextUrl.hostname,\n                href: nextUrl.href,\n                origin: nextUrl.origin,\n                password: nextUrl.password,\n                pathname: nextUrl.pathname,\n                port: nextUrl.port,\n                protocol: nextUrl.protocol,\n                search: nextUrl.search,\n                searchParams: Object.fromEntries(nextUrl.searchParams.entries()),\n                username: nextUrl.username,\n            };\n        }\n        let body;\n        if (request.body) {\n            try {\n                const clonedRequest = request.clone();\n                try {\n                    body = yield clonedRequest.json();\n                    (_a = clonedRequest.body) === null || _a === void 0 ? void 0 : _a.getReader;\n                }\n                catch (_b) {\n                    body = yield clonedRequest.text();\n                }\n            }\n            catch (error) {\n                console.warn('Could not parse request body:', error);\n            }\n        }\n        const cache = {\n            mode: request.cache,\n            credentials: request.credentials,\n            redirect: request.redirect,\n            referrerPolicy: request.referrerPolicy,\n            integrity: request.integrity,\n        };\n        let ip;\n        if ('ip' in request) {\n            // @ts-ignore NextRequest.ip was removed in Next 15, works with undefined\n            ip = request.ip;\n        }\n        // @ts-ignore NextRequest.ip was removed in Next 15, works with undefined\n        let geo;\n        if ('geo' in request) {\n            geo = request.geo;\n        }\n        return {\n            method: request.method,\n            url: request.url,\n            headers,\n            cookies: cookiesData,\n            nextUrl: nextUrlData,\n            ip,\n            geo,\n            body,\n            cache,\n            mode: request.mode,\n            destination: request.destination,\n            referrer: request.referrer,\n            keepalive: request.keepalive,\n            signal: {\n                aborted: request.signal.aborted,\n                reason: request.signal.reason,\n            },\n        };\n    });\n}\nexports.requestToJSON = requestToJSON;\nconst throttle = (fn, wait) => {\n    let lastFn, lastTime;\n    return function () {\n        const context = this, args = arguments;\n        // First call, set lastTime\n        if (lastTime == null) {\n            lastTime = Date.now();\n        }\n        clearTimeout(lastFn);\n        lastFn = setTimeout(() => {\n            if (Date.now() - lastTime >= wait) {\n                fn.apply(context, args);\n                lastTime = Date.now();\n            }\n        }, Math.max(wait - (Date.now() - lastTime), 0));\n    };\n};\nexports.throttle = throttle;\n//# sourceMappingURL=shared.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/shared.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/webVitals/components.js":
/*!*****************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/webVitals/components.js ***!
  \*****************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("/* __next_internal_client_entry_do_not_use__  cjs */ \nvar __importDefault = (void 0) && (void 0).__importDefault || function(mod) {\n    return mod && mod.__esModule ? mod : {\n        \"default\": mod\n    };\n};\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.BetterStackWebVitals = void 0;\nconst react_1 = __importDefault(__webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\"));\nconst _1 = __webpack_require__(/*! . */ \"(ssr)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/webVitals/index.js\");\nfunction BetterStackWebVitals({ path }) {\n    (0, _1.useReportWebVitals)(path);\n    return react_1.default.createElement(react_1.default.Fragment, null);\n}\nexports.BetterStackWebVitals = BetterStackWebVitals; //# sourceMappingURL=components.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0Bsb2d0YWlsK25leHRAMC4yLjBfbmV4dEAxNV9lMTk1OWU3NDFjNmJmNDc2MWY0N2UxYzhlNjgyZmVkNi9ub2RlX21vZHVsZXMvQGxvZ3RhaWwvbmV4dC9kaXN0L3dlYlZpdGFscy9jb21wb25lbnRzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFDQSw0T0FBMEI7QUFDMUIsMExBQXVDO0FBRXZDLFNBQWdCLG9CQUFvQixDQUFDLEVBQUUsSUFBSSxFQUFxQjtJQUM5RCwyQkFBbUIsSUFBSSxDQUFDLENBQUM7SUFDekIsT0FBTyw4QkFBQyxlQUFLLENBQUMsUUFBUSxPQUFrQixDQUFDO0FBQzNDLENBQUM7QUFIRCxvREFHQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbndhclxcRG9jdW1lbnRzXFxDdWJlbnR3ZWJcXGN1YmVudFxcc3JjXFx3ZWJWaXRhbHNcXGNvbXBvbmVudHMudHN4Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/webVitals/components.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/webVitals/index.js":
/*!************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/webVitals/index.js ***!
  \************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.useReportWebVitals = exports.BetterStackWebVitals = void 0;\nconst navigation_1 = __webpack_require__(/*! next/navigation */ \"(ssr)/../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/api/navigation.js\");\nconst web_vitals_1 = __webpack_require__(/*! next/web-vitals */ \"(ssr)/../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/web-vitals.js\");\nconst webVitals_1 = __webpack_require__(/*! ./webVitals */ \"(ssr)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/webVitals/webVitals.js\");\nconst react_1 = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\nvar components_1 = __webpack_require__(/*! ./components */ \"(ssr)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/webVitals/components.js\");\nObject.defineProperty(exports, \"BetterStackWebVitals\", ({ enumerable: true, get: function () { return components_1.BetterStackWebVitals; } }));\nfunction useReportWebVitals(path) {\n    const pathName = (0, navigation_1.usePathname)();\n    /**\n     * Refs allows us to stabilize the path name so that we can properly stabilize the reportWebVitalsFn\n     */\n    const stabilizedPath = (0, react_1.useRef)(path || pathName);\n    /**\n     * If the path changes, we update the stabilizedPath ref\n     */\n    if (typeof path === 'string' && path !== stabilizedPath.current) {\n        stabilizedPath.current = pathName;\n    }\n    else if (typeof path === 'string' && path === stabilizedPath.current) {\n        stabilizedPath.current = path;\n    }\n    /**\n     * Stabilizing the reportWebVitalsFn avoids reporting the same metrics from multiple paths, it happens because internally\n     * the useReportWebVitals from next uses a useEffect to report the metrics, and the reportWebVitalsFn is passed as a dependency\n     * to the useEffect, so when the path changes, the useEffect is re-run, and the same metrics are reported again.\n     */\n    const reportWebVitalsFn = (0, react_1.useCallback)((metric) => (0, webVitals_1.reportWebVitalsWithPath)(metric, stabilizedPath.current), []);\n    (0, web_vitals_1.useReportWebVitals)(reportWebVitalsFn);\n}\nexports.useReportWebVitals = useReportWebVitals;\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/webVitals/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/webVitals/webVitals.js":
/*!****************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/webVitals/webVitals.js ***!
  \****************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.reportWebVitalsWithPath = void 0;\nconst config_1 = __webpack_require__(/*! ../config */ \"(ssr)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/config.js\");\nconst shared_1 = __webpack_require__(/*! ../shared */ \"(ssr)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/shared.js\");\nconst url = config_1.config.getWebVitalsEndpoint();\nconst throttledSendMetrics = (0, shared_1.throttle)(sendMetrics, 1000);\nlet collectedMetrics = [];\nfunction reportWebVitalsWithPath(metric, route) {\n    collectedMetrics.push(Object.assign({ route }, metric));\n    // if env vars are not set, do nothing,\n    // otherwise devs will get errors on dev environments\n    if (!config_1.config.isEnvVarsSet()) {\n        return;\n    }\n    throttledSendMetrics();\n}\nexports.reportWebVitalsWithPath = reportWebVitalsWithPath;\nfunction sendMetrics() {\n    const body = JSON.stringify(config_1.config.wrapWebVitalsObject(collectedMetrics));\n    const headers = {\n        'Content-Type': 'application/json',\n        'User-Agent': 'next-logtail/v' + config_1.Version,\n    };\n    if (config_1.config.token) {\n        headers['Authorization'] = `Bearer ${config_1.config.token}`;\n    }\n    const reqOptions = { body, method: 'POST', keepalive: true, headers };\n    function sendFallback() {\n        // Do not leak network errors; does not affect the running app\n        fetch(url, reqOptions).catch(console.error);\n    }\n    if (config_1.isBrowser && config_1.isVercel && navigator.sendBeacon) {\n        try {\n            // See https://github.com/vercel/next.js/pull/26601\n            // Navigator has to be bound to ensure it does not error in some browsers\n            // https://xgwang.me/posts/you-may-not-know-beacon/#it-may-throw-error%2C-be-sure-to-catch\n            navigator.sendBeacon.bind(navigator)(url, body);\n        }\n        catch (err) {\n            sendFallback();\n        }\n    }\n    else {\n        sendFallback();\n    }\n    collectedMetrics = [];\n}\n//# sourceMappingURL=webVitals.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/webVitals/webVitals.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/withBetterStack.js":
/*!************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/withBetterStack.js ***!
  \************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval("\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.withBetterStack = exports.withBetterStackRouteHandler = exports.withBetterStackNextConfig = void 0;\nconst config_1 = __webpack_require__(/*! ./config */ \"(ssr)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/config.js\");\nconst logger_1 = __webpack_require__(/*! ./logger */ \"(ssr)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/logger.js\");\nconst shared_1 = __webpack_require__(/*! ./shared */ \"(ssr)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/shared.js\");\nfunction withBetterStackNextConfig(nextConfig) {\n    return Object.assign(Object.assign({}, nextConfig), { rewrites: () => __awaiter(this, void 0, void 0, function* () {\n            var _a;\n            const rewrites = yield ((_a = nextConfig.rewrites) === null || _a === void 0 ? void 0 : _a.call(nextConfig));\n            const webVitalsEndpoint = config_1.config.getIngestURL(shared_1.EndpointType.webVitals);\n            const logsEndpoint = config_1.config.getIngestURL(shared_1.EndpointType.logs);\n            if (!webVitalsEndpoint && !logsEndpoint) {\n                const log = new logger_1.Logger();\n                log.warn('Envvars not detected. If this is production please see https://betterstack.com/docs/logs/javascript/nextjs/ for help');\n                log.warn('Sending Web Vitals to /dev/null');\n                log.warn('Sending logs to console');\n                return rewrites || []; // nothing to do\n            }\n            const betterStackRewrites = [\n                {\n                    source: `${config_1.config.proxyPath}/web-vitals`,\n                    destination: webVitalsEndpoint,\n                    basePath: false,\n                },\n                {\n                    source: `${config_1.config.proxyPath}/logs`,\n                    destination: logsEndpoint,\n                    basePath: false,\n                },\n            ];\n            if (!rewrites) {\n                return betterStackRewrites;\n            }\n            else if (Array.isArray(rewrites)) {\n                return rewrites.concat(betterStackRewrites);\n            }\n            else {\n                rewrites.afterFiles = (rewrites.afterFiles || []).concat(betterStackRewrites);\n                return rewrites;\n            }\n        }) });\n}\nexports.withBetterStackNextConfig = withBetterStackNextConfig;\nfunction withBetterStackRouteHandler(handler, config) {\n    return (req, arg) => __awaiter(this, void 0, void 0, function* () {\n        var _a, _b, _c, _d;\n        let region = '';\n        if ('geo' in req) {\n            // @ts-ignore NextRequest.ip was removed in Next 15, works with undefined\n            region = (_b = (_a = req.geo) === null || _a === void 0 ? void 0 : _a.region) !== null && _b !== void 0 ? _b : '';\n        }\n        let pathname = '';\n        if ('nextUrl' in req) {\n            pathname = req.nextUrl.pathname;\n        }\n        else if (req instanceof Request) {\n            // pathname = req.url.substring(req.headers.get('host')?.length || 0)\n            pathname = new URL(req.url).pathname;\n        }\n        const requestDetails = Array.isArray(config === null || config === void 0 ? void 0 : config.logRequestDetails) || (config === null || config === void 0 ? void 0 : config.logRequestDetails) === true\n            ? yield (0, shared_1.requestToJSON)(req)\n            : undefined;\n        const report = {\n            startTime: new Date().getTime(),\n            endTime: new Date().getTime(),\n            path: pathname,\n            method: req.method,\n            host: req.headers.get('host'),\n            userAgent: req.headers.get('user-agent'),\n            scheme: req.url.split('://')[0],\n            ip: req.headers.get('x-forwarded-for'),\n            region,\n            details: Array.isArray(config === null || config === void 0 ? void 0 : config.logRequestDetails)\n                ? Object.fromEntries(Object.entries(requestDetails).filter(([key]) => (config === null || config === void 0 ? void 0 : config.logRequestDetails).includes(key)))\n                : requestDetails,\n        };\n        // main logger, mainly used to log reporting on the incoming HTTP request\n        const logger = new logger_1.Logger({ req: report, source: config_1.isEdgeRuntime ? 'edge' : 'lambda' });\n        // child logger to be used by the users within the handler\n        const log = logger.with({});\n        log.config.source = `${config_1.isEdgeRuntime ? 'edge' : 'lambda'}-log`;\n        const betterStackContext = req;\n        const args = arg;\n        betterStackContext.log = log;\n        try {\n            const result = yield handler(betterStackContext, args);\n            report.endTime = new Date().getTime();\n            // report log record\n            report.statusCode = result.status;\n            report.durationMs = report.endTime - report.startTime;\n            // record the request\n            if (!config_1.isVercel) {\n                logger.logHttpRequest(logger_1.LogLevel.info, `${req.method} ${report.path} ${report.statusCode} in ${report.endTime - report.startTime}ms`, report, {});\n            }\n            // attach the response status to all children logs\n            log.attachResponseStatus(result.status);\n            // flush the logger along with the child logger\n            yield logger.flush();\n            return result;\n        }\n        catch (error) {\n            // capture request endTime first for more accurate reporting\n            report.endTime = new Date().getTime();\n            // set default values for statusCode and logLevel\n            let statusCode = 500;\n            let logLevel = logger_1.LogLevel.error;\n            // handle navigation errors like notFound and redirect\n            if (error instanceof Error) {\n                if (error.message === 'NEXT_NOT_FOUND') {\n                    logLevel = (_c = config === null || config === void 0 ? void 0 : config.notFoundLogLevel) !== null && _c !== void 0 ? _c : logger_1.LogLevel.warn;\n                    statusCode = 404;\n                }\n                else if (error.message === 'NEXT_REDIRECT') {\n                    logLevel = (_d = config === null || config === void 0 ? void 0 : config.redirectLogLevel) !== null && _d !== void 0 ? _d : logger_1.LogLevel.info;\n                    // according to Next.js docs, values are: 307 (Temporary) or 308 (Permanent)\n                    // see: https://nextjs.org/docs/app/api-reference/functions/redirect#why-does-redirect-use-307-and-308\n                    // extract status code from digest, if exists\n                    const e = error;\n                    if (e.digest) {\n                        const d = e.digest.split(';');\n                        statusCode = parseInt(d[3]);\n                    }\n                    else {\n                        statusCode = 307;\n                    }\n                }\n            }\n            // report log record\n            report.statusCode = statusCode;\n            report.durationMs = report.endTime - report.startTime;\n            // record the request\n            if (!config_1.isVercel) {\n                logger.logHttpRequest(logLevel, `${req.method} ${report.path} ${report.statusCode} in ${report.endTime - report.startTime}ms`, report, {});\n            }\n            // forward the error message as a log event\n            log.log(logLevel, error.message, { error });\n            log.attachResponseStatus(statusCode);\n            yield logger.flush();\n            throw error;\n        }\n    });\n}\nexports.withBetterStackRouteHandler = withBetterStackRouteHandler;\nfunction isNextConfig(param) {\n    return typeof param == 'object';\n}\nfunction withBetterStack(param, config) {\n    if (typeof param == 'function') {\n        return withBetterStackRouteHandler(param, config);\n    }\n    else if (isNextConfig(param)) {\n        return withBetterStackNextConfig(param);\n    }\n    return withBetterStackRouteHandler(param, config);\n}\nexports.withBetterStack = withBetterStack;\n//# sourceMappingURL=withBetterStack.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0Bsb2d0YWlsK25leHRAMC4yLjBfbmV4dEAxNV9lMTk1OWU3NDFjNmJmNDc2MWY0N2UxYzhlNjgyZmVkNi9ub2RlX21vZHVsZXMvQGxvZ3RhaWwvbmV4dC9kaXN0L3dpdGhCZXR0ZXJTdGFjay5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0EsNEJBQTRCLCtEQUErRCxpQkFBaUI7QUFDNUc7QUFDQSxvQ0FBb0MsTUFBTSwrQkFBK0IsWUFBWTtBQUNyRixtQ0FBbUMsTUFBTSxtQ0FBbUMsWUFBWTtBQUN4RixnQ0FBZ0M7QUFDaEM7QUFDQSxLQUFLO0FBQ0w7QUFDQSw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsdUJBQXVCLEdBQUcsbUNBQW1DLEdBQUcsaUNBQWlDO0FBQ2pHLGlCQUFpQixtQkFBTyxDQUFDLHVKQUFVO0FBQ25DLGlCQUFpQixtQkFBTyxDQUFDLHVKQUFVO0FBQ25DLGlCQUFpQixtQkFBTyxDQUFDLHVKQUFVO0FBQ25DO0FBQ0EseUNBQXlDLGlCQUFpQjtBQUMxRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1Q0FBdUM7QUFDdkM7QUFDQTtBQUNBO0FBQ0EsK0JBQStCLDBCQUEwQjtBQUN6RDtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0EsK0JBQStCLDBCQUEwQjtBQUN6RDtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTLEdBQUc7QUFDWjtBQUNBLGlDQUFpQztBQUNqQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZDQUE2QyxpRUFBaUU7QUFDOUc7QUFDQSxrQ0FBa0M7QUFDbEMsK0JBQStCLDJDQUEyQztBQUMxRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUVBQWlFLFlBQVksRUFBRSxhQUFhLEVBQUUsbUJBQW1CLEtBQUssa0NBQWtDLGVBQWU7QUFDdks7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1EQUFtRDtBQUNuRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtREFBbUQsWUFBWSxFQUFFLGFBQWEsRUFBRSxtQkFBbUIsS0FBSyxrQ0FBa0MsZUFBZTtBQUN6SjtBQUNBO0FBQ0EsK0NBQStDLE9BQU87QUFDdEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQSxtQ0FBbUM7QUFDbkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCO0FBQ3ZCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFud2FyXFxEb2N1bWVudHNcXEN1YmVudHdlYlxcY3ViZW50XFxub2RlX21vZHVsZXNcXC5wbnBtXFxAbG9ndGFpbCtuZXh0QDAuMi4wX25leHRAMTVfZTE5NTllNzQxYzZiZjQ3NjFmNDdlMWM4ZTY4MmZlZDZcXG5vZGVfbW9kdWxlc1xcQGxvZ3RhaWxcXG5leHRcXGRpc3RcXHdpdGhCZXR0ZXJTdGFjay5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbnZhciBfX2F3YWl0ZXIgPSAodGhpcyAmJiB0aGlzLl9fYXdhaXRlcikgfHwgZnVuY3Rpb24gKHRoaXNBcmcsIF9hcmd1bWVudHMsIFAsIGdlbmVyYXRvcikge1xuICAgIGZ1bmN0aW9uIGFkb3B0KHZhbHVlKSB7IHJldHVybiB2YWx1ZSBpbnN0YW5jZW9mIFAgPyB2YWx1ZSA6IG5ldyBQKGZ1bmN0aW9uIChyZXNvbHZlKSB7IHJlc29sdmUodmFsdWUpOyB9KTsgfVxuICAgIHJldHVybiBuZXcgKFAgfHwgKFAgPSBQcm9taXNlKSkoZnVuY3Rpb24gKHJlc29sdmUsIHJlamVjdCkge1xuICAgICAgICBmdW5jdGlvbiBmdWxmaWxsZWQodmFsdWUpIHsgdHJ5IHsgc3RlcChnZW5lcmF0b3IubmV4dCh2YWx1ZSkpOyB9IGNhdGNoIChlKSB7IHJlamVjdChlKTsgfSB9XG4gICAgICAgIGZ1bmN0aW9uIHJlamVjdGVkKHZhbHVlKSB7IHRyeSB7IHN0ZXAoZ2VuZXJhdG9yW1widGhyb3dcIl0odmFsdWUpKTsgfSBjYXRjaCAoZSkgeyByZWplY3QoZSk7IH0gfVxuICAgICAgICBmdW5jdGlvbiBzdGVwKHJlc3VsdCkgeyByZXN1bHQuZG9uZSA/IHJlc29sdmUocmVzdWx0LnZhbHVlKSA6IGFkb3B0KHJlc3VsdC52YWx1ZSkudGhlbihmdWxmaWxsZWQsIHJlamVjdGVkKTsgfVxuICAgICAgICBzdGVwKChnZW5lcmF0b3IgPSBnZW5lcmF0b3IuYXBwbHkodGhpc0FyZywgX2FyZ3VtZW50cyB8fCBbXSkpLm5leHQoKSk7XG4gICAgfSk7XG59O1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy53aXRoQmV0dGVyU3RhY2sgPSBleHBvcnRzLndpdGhCZXR0ZXJTdGFja1JvdXRlSGFuZGxlciA9IGV4cG9ydHMud2l0aEJldHRlclN0YWNrTmV4dENvbmZpZyA9IHZvaWQgMDtcbmNvbnN0IGNvbmZpZ18xID0gcmVxdWlyZShcIi4vY29uZmlnXCIpO1xuY29uc3QgbG9nZ2VyXzEgPSByZXF1aXJlKFwiLi9sb2dnZXJcIik7XG5jb25zdCBzaGFyZWRfMSA9IHJlcXVpcmUoXCIuL3NoYXJlZFwiKTtcbmZ1bmN0aW9uIHdpdGhCZXR0ZXJTdGFja05leHRDb25maWcobmV4dENvbmZpZykge1xuICAgIHJldHVybiBPYmplY3QuYXNzaWduKE9iamVjdC5hc3NpZ24oe30sIG5leHRDb25maWcpLCB7IHJld3JpdGVzOiAoKSA9PiBfX2F3YWl0ZXIodGhpcywgdm9pZCAwLCB2b2lkIDAsIGZ1bmN0aW9uKiAoKSB7XG4gICAgICAgICAgICB2YXIgX2E7XG4gICAgICAgICAgICBjb25zdCByZXdyaXRlcyA9IHlpZWxkICgoX2EgPSBuZXh0Q29uZmlnLnJld3JpdGVzKSA9PT0gbnVsbCB8fCBfYSA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2EuY2FsbChuZXh0Q29uZmlnKSk7XG4gICAgICAgICAgICBjb25zdCB3ZWJWaXRhbHNFbmRwb2ludCA9IGNvbmZpZ18xLmNvbmZpZy5nZXRJbmdlc3RVUkwoc2hhcmVkXzEuRW5kcG9pbnRUeXBlLndlYlZpdGFscyk7XG4gICAgICAgICAgICBjb25zdCBsb2dzRW5kcG9pbnQgPSBjb25maWdfMS5jb25maWcuZ2V0SW5nZXN0VVJMKHNoYXJlZF8xLkVuZHBvaW50VHlwZS5sb2dzKTtcbiAgICAgICAgICAgIGlmICghd2ViVml0YWxzRW5kcG9pbnQgJiYgIWxvZ3NFbmRwb2ludCkge1xuICAgICAgICAgICAgICAgIGNvbnN0IGxvZyA9IG5ldyBsb2dnZXJfMS5Mb2dnZXIoKTtcbiAgICAgICAgICAgICAgICBsb2cud2FybignRW52dmFycyBub3QgZGV0ZWN0ZWQuIElmIHRoaXMgaXMgcHJvZHVjdGlvbiBwbGVhc2Ugc2VlIGh0dHBzOi8vYmV0dGVyc3RhY2suY29tL2RvY3MvbG9ncy9qYXZhc2NyaXB0L25leHRqcy8gZm9yIGhlbHAnKTtcbiAgICAgICAgICAgICAgICBsb2cud2FybignU2VuZGluZyBXZWIgVml0YWxzIHRvIC9kZXYvbnVsbCcpO1xuICAgICAgICAgICAgICAgIGxvZy53YXJuKCdTZW5kaW5nIGxvZ3MgdG8gY29uc29sZScpO1xuICAgICAgICAgICAgICAgIHJldHVybiByZXdyaXRlcyB8fCBbXTsgLy8gbm90aGluZyB0byBkb1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY29uc3QgYmV0dGVyU3RhY2tSZXdyaXRlcyA9IFtcbiAgICAgICAgICAgICAgICB7XG4gICAgICAgICAgICAgICAgICAgIHNvdXJjZTogYCR7Y29uZmlnXzEuY29uZmlnLnByb3h5UGF0aH0vd2ViLXZpdGFsc2AsXG4gICAgICAgICAgICAgICAgICAgIGRlc3RpbmF0aW9uOiB3ZWJWaXRhbHNFbmRwb2ludCxcbiAgICAgICAgICAgICAgICAgICAgYmFzZVBhdGg6IGZhbHNlLFxuICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAge1xuICAgICAgICAgICAgICAgICAgICBzb3VyY2U6IGAke2NvbmZpZ18xLmNvbmZpZy5wcm94eVBhdGh9L2xvZ3NgLFxuICAgICAgICAgICAgICAgICAgICBkZXN0aW5hdGlvbjogbG9nc0VuZHBvaW50LFxuICAgICAgICAgICAgICAgICAgICBiYXNlUGF0aDogZmFsc2UsXG4gICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIF07XG4gICAgICAgICAgICBpZiAoIXJld3JpdGVzKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGJldHRlclN0YWNrUmV3cml0ZXM7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIGlmIChBcnJheS5pc0FycmF5KHJld3JpdGVzKSkge1xuICAgICAgICAgICAgICAgIHJldHVybiByZXdyaXRlcy5jb25jYXQoYmV0dGVyU3RhY2tSZXdyaXRlcyk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICByZXdyaXRlcy5hZnRlckZpbGVzID0gKHJld3JpdGVzLmFmdGVyRmlsZXMgfHwgW10pLmNvbmNhdChiZXR0ZXJTdGFja1Jld3JpdGVzKTtcbiAgICAgICAgICAgICAgICByZXR1cm4gcmV3cml0ZXM7XG4gICAgICAgICAgICB9XG4gICAgICAgIH0pIH0pO1xufVxuZXhwb3J0cy53aXRoQmV0dGVyU3RhY2tOZXh0Q29uZmlnID0gd2l0aEJldHRlclN0YWNrTmV4dENvbmZpZztcbmZ1bmN0aW9uIHdpdGhCZXR0ZXJTdGFja1JvdXRlSGFuZGxlcihoYW5kbGVyLCBjb25maWcpIHtcbiAgICByZXR1cm4gKHJlcSwgYXJnKSA9PiBfX2F3YWl0ZXIodGhpcywgdm9pZCAwLCB2b2lkIDAsIGZ1bmN0aW9uKiAoKSB7XG4gICAgICAgIHZhciBfYSwgX2IsIF9jLCBfZDtcbiAgICAgICAgbGV0IHJlZ2lvbiA9ICcnO1xuICAgICAgICBpZiAoJ2dlbycgaW4gcmVxKSB7XG4gICAgICAgICAgICAvLyBAdHMtaWdub3JlIE5leHRSZXF1ZXN0LmlwIHdhcyByZW1vdmVkIGluIE5leHQgMTUsIHdvcmtzIHdpdGggdW5kZWZpbmVkXG4gICAgICAgICAgICByZWdpb24gPSAoX2IgPSAoX2EgPSByZXEuZ2VvKSA9PT0gbnVsbCB8fCBfYSA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2EucmVnaW9uKSAhPT0gbnVsbCAmJiBfYiAhPT0gdm9pZCAwID8gX2IgOiAnJztcbiAgICAgICAgfVxuICAgICAgICBsZXQgcGF0aG5hbWUgPSAnJztcbiAgICAgICAgaWYgKCduZXh0VXJsJyBpbiByZXEpIHtcbiAgICAgICAgICAgIHBhdGhuYW1lID0gcmVxLm5leHRVcmwucGF0aG5hbWU7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAocmVxIGluc3RhbmNlb2YgUmVxdWVzdCkge1xuICAgICAgICAgICAgLy8gcGF0aG5hbWUgPSByZXEudXJsLnN1YnN0cmluZyhyZXEuaGVhZGVycy5nZXQoJ2hvc3QnKT8ubGVuZ3RoIHx8IDApXG4gICAgICAgICAgICBwYXRobmFtZSA9IG5ldyBVUkwocmVxLnVybCkucGF0aG5hbWU7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgcmVxdWVzdERldGFpbHMgPSBBcnJheS5pc0FycmF5KGNvbmZpZyA9PT0gbnVsbCB8fCBjb25maWcgPT09IHZvaWQgMCA/IHZvaWQgMCA6IGNvbmZpZy5sb2dSZXF1ZXN0RGV0YWlscykgfHwgKGNvbmZpZyA9PT0gbnVsbCB8fCBjb25maWcgPT09IHZvaWQgMCA/IHZvaWQgMCA6IGNvbmZpZy5sb2dSZXF1ZXN0RGV0YWlscykgPT09IHRydWVcbiAgICAgICAgICAgID8geWllbGQgKDAsIHNoYXJlZF8xLnJlcXVlc3RUb0pTT04pKHJlcSlcbiAgICAgICAgICAgIDogdW5kZWZpbmVkO1xuICAgICAgICBjb25zdCByZXBvcnQgPSB7XG4gICAgICAgICAgICBzdGFydFRpbWU6IG5ldyBEYXRlKCkuZ2V0VGltZSgpLFxuICAgICAgICAgICAgZW5kVGltZTogbmV3IERhdGUoKS5nZXRUaW1lKCksXG4gICAgICAgICAgICBwYXRoOiBwYXRobmFtZSxcbiAgICAgICAgICAgIG1ldGhvZDogcmVxLm1ldGhvZCxcbiAgICAgICAgICAgIGhvc3Q6IHJlcS5oZWFkZXJzLmdldCgnaG9zdCcpLFxuICAgICAgICAgICAgdXNlckFnZW50OiByZXEuaGVhZGVycy5nZXQoJ3VzZXItYWdlbnQnKSxcbiAgICAgICAgICAgIHNjaGVtZTogcmVxLnVybC5zcGxpdCgnOi8vJylbMF0sXG4gICAgICAgICAgICBpcDogcmVxLmhlYWRlcnMuZ2V0KCd4LWZvcndhcmRlZC1mb3InKSxcbiAgICAgICAgICAgIHJlZ2lvbixcbiAgICAgICAgICAgIGRldGFpbHM6IEFycmF5LmlzQXJyYXkoY29uZmlnID09PSBudWxsIHx8IGNvbmZpZyA9PT0gdm9pZCAwID8gdm9pZCAwIDogY29uZmlnLmxvZ1JlcXVlc3REZXRhaWxzKVxuICAgICAgICAgICAgICAgID8gT2JqZWN0LmZyb21FbnRyaWVzKE9iamVjdC5lbnRyaWVzKHJlcXVlc3REZXRhaWxzKS5maWx0ZXIoKFtrZXldKSA9PiAoY29uZmlnID09PSBudWxsIHx8IGNvbmZpZyA9PT0gdm9pZCAwID8gdm9pZCAwIDogY29uZmlnLmxvZ1JlcXVlc3REZXRhaWxzKS5pbmNsdWRlcyhrZXkpKSlcbiAgICAgICAgICAgICAgICA6IHJlcXVlc3REZXRhaWxzLFxuICAgICAgICB9O1xuICAgICAgICAvLyBtYWluIGxvZ2dlciwgbWFpbmx5IHVzZWQgdG8gbG9nIHJlcG9ydGluZyBvbiB0aGUgaW5jb21pbmcgSFRUUCByZXF1ZXN0XG4gICAgICAgIGNvbnN0IGxvZ2dlciA9IG5ldyBsb2dnZXJfMS5Mb2dnZXIoeyByZXE6IHJlcG9ydCwgc291cmNlOiBjb25maWdfMS5pc0VkZ2VSdW50aW1lID8gJ2VkZ2UnIDogJ2xhbWJkYScgfSk7XG4gICAgICAgIC8vIGNoaWxkIGxvZ2dlciB0byBiZSB1c2VkIGJ5IHRoZSB1c2VycyB3aXRoaW4gdGhlIGhhbmRsZXJcbiAgICAgICAgY29uc3QgbG9nID0gbG9nZ2VyLndpdGgoe30pO1xuICAgICAgICBsb2cuY29uZmlnLnNvdXJjZSA9IGAke2NvbmZpZ18xLmlzRWRnZVJ1bnRpbWUgPyAnZWRnZScgOiAnbGFtYmRhJ30tbG9nYDtcbiAgICAgICAgY29uc3QgYmV0dGVyU3RhY2tDb250ZXh0ID0gcmVxO1xuICAgICAgICBjb25zdCBhcmdzID0gYXJnO1xuICAgICAgICBiZXR0ZXJTdGFja0NvbnRleHQubG9nID0gbG9nO1xuICAgICAgICB0cnkge1xuICAgICAgICAgICAgY29uc3QgcmVzdWx0ID0geWllbGQgaGFuZGxlcihiZXR0ZXJTdGFja0NvbnRleHQsIGFyZ3MpO1xuICAgICAgICAgICAgcmVwb3J0LmVuZFRpbWUgPSBuZXcgRGF0ZSgpLmdldFRpbWUoKTtcbiAgICAgICAgICAgIC8vIHJlcG9ydCBsb2cgcmVjb3JkXG4gICAgICAgICAgICByZXBvcnQuc3RhdHVzQ29kZSA9IHJlc3VsdC5zdGF0dXM7XG4gICAgICAgICAgICByZXBvcnQuZHVyYXRpb25NcyA9IHJlcG9ydC5lbmRUaW1lIC0gcmVwb3J0LnN0YXJ0VGltZTtcbiAgICAgICAgICAgIC8vIHJlY29yZCB0aGUgcmVxdWVzdFxuICAgICAgICAgICAgaWYgKCFjb25maWdfMS5pc1ZlcmNlbCkge1xuICAgICAgICAgICAgICAgIGxvZ2dlci5sb2dIdHRwUmVxdWVzdChsb2dnZXJfMS5Mb2dMZXZlbC5pbmZvLCBgJHtyZXEubWV0aG9kfSAke3JlcG9ydC5wYXRofSAke3JlcG9ydC5zdGF0dXNDb2RlfSBpbiAke3JlcG9ydC5lbmRUaW1lIC0gcmVwb3J0LnN0YXJ0VGltZX1tc2AsIHJlcG9ydCwge30pO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgLy8gYXR0YWNoIHRoZSByZXNwb25zZSBzdGF0dXMgdG8gYWxsIGNoaWxkcmVuIGxvZ3NcbiAgICAgICAgICAgIGxvZy5hdHRhY2hSZXNwb25zZVN0YXR1cyhyZXN1bHQuc3RhdHVzKTtcbiAgICAgICAgICAgIC8vIGZsdXNoIHRoZSBsb2dnZXIgYWxvbmcgd2l0aCB0aGUgY2hpbGQgbG9nZ2VyXG4gICAgICAgICAgICB5aWVsZCBsb2dnZXIuZmx1c2goKTtcbiAgICAgICAgICAgIHJldHVybiByZXN1bHQ7XG4gICAgICAgIH1cbiAgICAgICAgY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICAvLyBjYXB0dXJlIHJlcXVlc3QgZW5kVGltZSBmaXJzdCBmb3IgbW9yZSBhY2N1cmF0ZSByZXBvcnRpbmdcbiAgICAgICAgICAgIHJlcG9ydC5lbmRUaW1lID0gbmV3IERhdGUoKS5nZXRUaW1lKCk7XG4gICAgICAgICAgICAvLyBzZXQgZGVmYXVsdCB2YWx1ZXMgZm9yIHN0YXR1c0NvZGUgYW5kIGxvZ0xldmVsXG4gICAgICAgICAgICBsZXQgc3RhdHVzQ29kZSA9IDUwMDtcbiAgICAgICAgICAgIGxldCBsb2dMZXZlbCA9IGxvZ2dlcl8xLkxvZ0xldmVsLmVycm9yO1xuICAgICAgICAgICAgLy8gaGFuZGxlIG5hdmlnYXRpb24gZXJyb3JzIGxpa2Ugbm90Rm91bmQgYW5kIHJlZGlyZWN0XG4gICAgICAgICAgICBpZiAoZXJyb3IgaW5zdGFuY2VvZiBFcnJvcikge1xuICAgICAgICAgICAgICAgIGlmIChlcnJvci5tZXNzYWdlID09PSAnTkVYVF9OT1RfRk9VTkQnKSB7XG4gICAgICAgICAgICAgICAgICAgIGxvZ0xldmVsID0gKF9jID0gY29uZmlnID09PSBudWxsIHx8IGNvbmZpZyA9PT0gdm9pZCAwID8gdm9pZCAwIDogY29uZmlnLm5vdEZvdW5kTG9nTGV2ZWwpICE9PSBudWxsICYmIF9jICE9PSB2b2lkIDAgPyBfYyA6IGxvZ2dlcl8xLkxvZ0xldmVsLndhcm47XG4gICAgICAgICAgICAgICAgICAgIHN0YXR1c0NvZGUgPSA0MDQ7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGVsc2UgaWYgKGVycm9yLm1lc3NhZ2UgPT09ICdORVhUX1JFRElSRUNUJykge1xuICAgICAgICAgICAgICAgICAgICBsb2dMZXZlbCA9IChfZCA9IGNvbmZpZyA9PT0gbnVsbCB8fCBjb25maWcgPT09IHZvaWQgMCA/IHZvaWQgMCA6IGNvbmZpZy5yZWRpcmVjdExvZ0xldmVsKSAhPT0gbnVsbCAmJiBfZCAhPT0gdm9pZCAwID8gX2QgOiBsb2dnZXJfMS5Mb2dMZXZlbC5pbmZvO1xuICAgICAgICAgICAgICAgICAgICAvLyBhY2NvcmRpbmcgdG8gTmV4dC5qcyBkb2NzLCB2YWx1ZXMgYXJlOiAzMDcgKFRlbXBvcmFyeSkgb3IgMzA4IChQZXJtYW5lbnQpXG4gICAgICAgICAgICAgICAgICAgIC8vIHNlZTogaHR0cHM6Ly9uZXh0anMub3JnL2RvY3MvYXBwL2FwaS1yZWZlcmVuY2UvZnVuY3Rpb25zL3JlZGlyZWN0I3doeS1kb2VzLXJlZGlyZWN0LXVzZS0zMDctYW5kLTMwOFxuICAgICAgICAgICAgICAgICAgICAvLyBleHRyYWN0IHN0YXR1cyBjb2RlIGZyb20gZGlnZXN0LCBpZiBleGlzdHNcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgZSA9IGVycm9yO1xuICAgICAgICAgICAgICAgICAgICBpZiAoZS5kaWdlc3QpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGQgPSBlLmRpZ2VzdC5zcGxpdCgnOycpO1xuICAgICAgICAgICAgICAgICAgICAgICAgc3RhdHVzQ29kZSA9IHBhcnNlSW50KGRbM10pO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICAgICAgc3RhdHVzQ29kZSA9IDMwNztcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIC8vIHJlcG9ydCBsb2cgcmVjb3JkXG4gICAgICAgICAgICByZXBvcnQuc3RhdHVzQ29kZSA9IHN0YXR1c0NvZGU7XG4gICAgICAgICAgICByZXBvcnQuZHVyYXRpb25NcyA9IHJlcG9ydC5lbmRUaW1lIC0gcmVwb3J0LnN0YXJ0VGltZTtcbiAgICAgICAgICAgIC8vIHJlY29yZCB0aGUgcmVxdWVzdFxuICAgICAgICAgICAgaWYgKCFjb25maWdfMS5pc1ZlcmNlbCkge1xuICAgICAgICAgICAgICAgIGxvZ2dlci5sb2dIdHRwUmVxdWVzdChsb2dMZXZlbCwgYCR7cmVxLm1ldGhvZH0gJHtyZXBvcnQucGF0aH0gJHtyZXBvcnQuc3RhdHVzQ29kZX0gaW4gJHtyZXBvcnQuZW5kVGltZSAtIHJlcG9ydC5zdGFydFRpbWV9bXNgLCByZXBvcnQsIHt9KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIC8vIGZvcndhcmQgdGhlIGVycm9yIG1lc3NhZ2UgYXMgYSBsb2cgZXZlbnRcbiAgICAgICAgICAgIGxvZy5sb2cobG9nTGV2ZWwsIGVycm9yLm1lc3NhZ2UsIHsgZXJyb3IgfSk7XG4gICAgICAgICAgICBsb2cuYXR0YWNoUmVzcG9uc2VTdGF0dXMoc3RhdHVzQ29kZSk7XG4gICAgICAgICAgICB5aWVsZCBsb2dnZXIuZmx1c2goKTtcbiAgICAgICAgICAgIHRocm93IGVycm9yO1xuICAgICAgICB9XG4gICAgfSk7XG59XG5leHBvcnRzLndpdGhCZXR0ZXJTdGFja1JvdXRlSGFuZGxlciA9IHdpdGhCZXR0ZXJTdGFja1JvdXRlSGFuZGxlcjtcbmZ1bmN0aW9uIGlzTmV4dENvbmZpZyhwYXJhbSkge1xuICAgIHJldHVybiB0eXBlb2YgcGFyYW0gPT0gJ29iamVjdCc7XG59XG5mdW5jdGlvbiB3aXRoQmV0dGVyU3RhY2socGFyYW0sIGNvbmZpZykge1xuICAgIGlmICh0eXBlb2YgcGFyYW0gPT0gJ2Z1bmN0aW9uJykge1xuICAgICAgICByZXR1cm4gd2l0aEJldHRlclN0YWNrUm91dGVIYW5kbGVyKHBhcmFtLCBjb25maWcpO1xuICAgIH1cbiAgICBlbHNlIGlmIChpc05leHRDb25maWcocGFyYW0pKSB7XG4gICAgICAgIHJldHVybiB3aXRoQmV0dGVyU3RhY2tOZXh0Q29uZmlnKHBhcmFtKTtcbiAgICB9XG4gICAgcmV0dXJuIHdpdGhCZXR0ZXJTdGFja1JvdXRlSGFuZGxlcihwYXJhbSwgY29uZmlnKTtcbn1cbmV4cG9ydHMud2l0aEJldHRlclN0YWNrID0gd2l0aEJldHRlclN0YWNrO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9d2l0aEJldHRlclN0YWNrLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/dist/withBetterStack.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/package.json":
/*!*************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@logtail+next@0.2.0_next@15_e1959e741c6bf4761f47e1c8e682fed6/node_modules/@logtail/next/package.json ***!
  \*************************************************************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"name":"@logtail/next","description":"Better Stack Telemetry Next.js client","version":"0.2.0","author":"Better Stack <<EMAIL>>","license":"MIT","engines":{"node":">=18"},"main":"dist/index.js","types":"dist/index.d.ts","scripts":{"build":"tsc","prepare":"npm run build","format":"prettier --write src/*.ts tests/*.ts","check-format":"prettier -c src/*.ts tests/*.ts","test":"vitest run tests --coverage"},"repository":{"type":"git","url":"git+https://github.com/logtail/logtail-nextjs.git"},"keywords":["web-vitals","better-stack","logtail","observability","performance","telemetry","nextjs"],"bugs":{"url":"https://github.com/logtail/logtail-nextjs/issues"},"homepage":"https://github.com/logtail/logtail-nextjs#readme","peerDependencies":{"next":">=14.0","react":">=18.0.0"},"devDependencies":{"@types/node":"^20.4.2","@types/react":"^18.2.15","@vitest/coverage-v8":"^2.1.5","prettier":"^3.0.0","ts-node":"^10.9.1","typescript":"^5.1.6","vitest":"^2.1.5"},"dependencies":{"whatwg-fetch":"^3.6.2","use-deep-compare":"^1.2.1"}}');

/***/ })

};
;