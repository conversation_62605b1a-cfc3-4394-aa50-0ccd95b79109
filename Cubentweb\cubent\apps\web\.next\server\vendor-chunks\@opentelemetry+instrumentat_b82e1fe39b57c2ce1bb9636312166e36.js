"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36";
exports.ids = ["vendor-chunks/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36"];
exports.modules = {

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/enums/AttributeNames.js":
/*!***********************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/enums/AttributeNames.js ***!
  \***********************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.ConnectNames = exports.ConnectTypes = exports.AttributeNames = void 0;\nvar AttributeNames;\n(function (AttributeNames) {\n    AttributeNames[\"CONNECT_TYPE\"] = \"connect.type\";\n    AttributeNames[\"CONNECT_NAME\"] = \"connect.name\";\n})(AttributeNames = exports.AttributeNames || (exports.AttributeNames = {}));\nvar ConnectTypes;\n(function (ConnectTypes) {\n    ConnectTypes[\"MIDDLEWARE\"] = \"middleware\";\n    ConnectTypes[\"REQUEST_HANDLER\"] = \"request_handler\";\n})(ConnectTypes = exports.ConnectTypes || (exports.ConnectTypes = {}));\nvar ConnectNames;\n(function (ConnectNames) {\n    ConnectNames[\"MIDDLEWARE\"] = \"middleware\";\n    ConnectNames[\"REQUEST_HANDLER\"] = \"request handler\";\n})(ConnectNames = exports.ConnectNames || (exports.ConnectNames = {}));\n//# sourceMappingURL=AttributeNames.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9Ab3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfYjgyZTFmZTM5YjU3YzJjZTFiYjk2MzYzMTIxNjZlMzYvbm9kZV9tb2R1bGVzL0BvcGVudGVsZW1ldHJ5L2luc3RydW1lbnRhdGlvbi1jb25uZWN0L2J1aWxkL3NyYy9lbnVtcy9BdHRyaWJ1dGVOYW1lcy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxvQkFBb0IsR0FBRyxvQkFBb0IsR0FBRyxzQkFBc0I7QUFDcEU7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLDhDQUE4QyxzQkFBc0IsS0FBSztBQUMxRTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsMENBQTBDLG9CQUFvQixLQUFLO0FBQ3BFO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQywwQ0FBMEMsb0JBQW9CLEtBQUs7QUFDcEUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYW53YXJcXERvY3VtZW50c1xcQ3ViZW50d2ViXFxjdWJlbnRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF9iODJlMWZlMzliNTdjMmNlMWJiOTYzNjMxMjE2NmUzNlxcbm9kZV9tb2R1bGVzXFxAb3BlbnRlbGVtZXRyeVxcaW5zdHJ1bWVudGF0aW9uLWNvbm5lY3RcXGJ1aWxkXFxzcmNcXGVudW1zXFxBdHRyaWJ1dGVOYW1lcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbi8qXG4gKiBDb3B5cmlnaHQgVGhlIE9wZW5UZWxlbWV0cnkgQXV0aG9yc1xuICpcbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4gKiB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuXG4gKiBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbiAqXG4gKiAgICAgIGh0dHBzOi8vd3d3LmFwYWNoZS5vcmcvbGljZW5zZXMvTElDRU5TRS0yLjBcbiAqXG4gKiBVbmxlc3MgcmVxdWlyZWQgYnkgYXBwbGljYWJsZSBsYXcgb3IgYWdyZWVkIHRvIGluIHdyaXRpbmcsIHNvZnR3YXJlXG4gKiBkaXN0cmlidXRlZCB1bmRlciB0aGUgTGljZW5zZSBpcyBkaXN0cmlidXRlZCBvbiBhbiBcIkFTIElTXCIgQkFTSVMsXG4gKiBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgQ09ORElUSU9OUyBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC5cbiAqIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmRcbiAqIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuICovXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLkNvbm5lY3ROYW1lcyA9IGV4cG9ydHMuQ29ubmVjdFR5cGVzID0gZXhwb3J0cy5BdHRyaWJ1dGVOYW1lcyA9IHZvaWQgMDtcbnZhciBBdHRyaWJ1dGVOYW1lcztcbihmdW5jdGlvbiAoQXR0cmlidXRlTmFtZXMpIHtcbiAgICBBdHRyaWJ1dGVOYW1lc1tcIkNPTk5FQ1RfVFlQRVwiXSA9IFwiY29ubmVjdC50eXBlXCI7XG4gICAgQXR0cmlidXRlTmFtZXNbXCJDT05ORUNUX05BTUVcIl0gPSBcImNvbm5lY3QubmFtZVwiO1xufSkoQXR0cmlidXRlTmFtZXMgPSBleHBvcnRzLkF0dHJpYnV0ZU5hbWVzIHx8IChleHBvcnRzLkF0dHJpYnV0ZU5hbWVzID0ge30pKTtcbnZhciBDb25uZWN0VHlwZXM7XG4oZnVuY3Rpb24gKENvbm5lY3RUeXBlcykge1xuICAgIENvbm5lY3RUeXBlc1tcIk1JRERMRVdBUkVcIl0gPSBcIm1pZGRsZXdhcmVcIjtcbiAgICBDb25uZWN0VHlwZXNbXCJSRVFVRVNUX0hBTkRMRVJcIl0gPSBcInJlcXVlc3RfaGFuZGxlclwiO1xufSkoQ29ubmVjdFR5cGVzID0gZXhwb3J0cy5Db25uZWN0VHlwZXMgfHwgKGV4cG9ydHMuQ29ubmVjdFR5cGVzID0ge30pKTtcbnZhciBDb25uZWN0TmFtZXM7XG4oZnVuY3Rpb24gKENvbm5lY3ROYW1lcykge1xuICAgIENvbm5lY3ROYW1lc1tcIk1JRERMRVdBUkVcIl0gPSBcIm1pZGRsZXdhcmVcIjtcbiAgICBDb25uZWN0TmFtZXNbXCJSRVFVRVNUX0hBTkRMRVJcIl0gPSBcInJlcXVlc3QgaGFuZGxlclwiO1xufSkoQ29ubmVjdE5hbWVzID0gZXhwb3J0cy5Db25uZWN0TmFtZXMgfHwgKGV4cG9ydHMuQ29ubmVjdE5hbWVzID0ge30pKTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPUF0dHJpYnV0ZU5hbWVzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/enums/AttributeNames.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/index.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/index.js ***!
  \********************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./enums/AttributeNames */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/enums/AttributeNames.js\"), exports);\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/instrumentation.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/index.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/instrumentation.js":
/*!******************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/instrumentation.js ***!
  \******************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.ConnectInstrumentation = exports.ANONYMOUS_NAME = void 0;\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst core_1 = __webpack_require__(/*! @opentelemetry/core */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+core@1.30.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/core/build/esm/index.js\");\nconst AttributeNames_1 = __webpack_require__(/*! ./enums/AttributeNames */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/enums/AttributeNames.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/version.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/utils.js\");\nexports.ANONYMOUS_NAME = 'anonymous';\n/** Connect instrumentation for OpenTelemetry */\nclass ConnectInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, config);\n    }\n    init() {\n        return [\n            new instrumentation_1.InstrumentationNodeModuleDefinition('connect', ['>=3.0.0 <4'], moduleExports => {\n                return this._patchConstructor(moduleExports);\n            }),\n        ];\n    }\n    _patchApp(patchedApp) {\n        if (!(0, instrumentation_1.isWrapped)(patchedApp.use)) {\n            this._wrap(patchedApp, 'use', this._patchUse.bind(this));\n        }\n        if (!(0, instrumentation_1.isWrapped)(patchedApp.handle)) {\n            this._wrap(patchedApp, 'handle', this._patchHandle.bind(this));\n        }\n    }\n    _patchConstructor(original) {\n        const instrumentation = this;\n        return function (...args) {\n            const app = original.apply(this, args);\n            instrumentation._patchApp(app);\n            return app;\n        };\n    }\n    _patchNext(next, finishSpan) {\n        return function nextFunction(err) {\n            const result = next.apply(this, [err]);\n            finishSpan();\n            return result;\n        };\n    }\n    _startSpan(routeName, middleWare) {\n        let connectType;\n        let connectName;\n        let connectTypeName;\n        if (routeName) {\n            connectType = AttributeNames_1.ConnectTypes.REQUEST_HANDLER;\n            connectTypeName = AttributeNames_1.ConnectNames.REQUEST_HANDLER;\n            connectName = routeName;\n        }\n        else {\n            connectType = AttributeNames_1.ConnectTypes.MIDDLEWARE;\n            connectTypeName = AttributeNames_1.ConnectNames.MIDDLEWARE;\n            connectName = middleWare.name || exports.ANONYMOUS_NAME;\n        }\n        const spanName = `${connectTypeName} - ${connectName}`;\n        const options = {\n            attributes: {\n                [semantic_conventions_1.SEMATTRS_HTTP_ROUTE]: routeName.length > 0 ? routeName : '/',\n                [AttributeNames_1.AttributeNames.CONNECT_TYPE]: connectType,\n                [AttributeNames_1.AttributeNames.CONNECT_NAME]: connectName,\n            },\n        };\n        return this.tracer.startSpan(spanName, options);\n    }\n    _patchMiddleware(routeName, middleWare) {\n        const instrumentation = this;\n        const isErrorMiddleware = middleWare.length === 4;\n        function patchedMiddleware() {\n            if (!instrumentation.isEnabled()) {\n                return middleWare.apply(this, arguments);\n            }\n            const [reqArgIdx, resArgIdx, nextArgIdx] = isErrorMiddleware\n                ? [1, 2, 3]\n                : [0, 1, 2];\n            const req = arguments[reqArgIdx];\n            const res = arguments[resArgIdx];\n            const next = arguments[nextArgIdx];\n            (0, utils_1.replaceCurrentStackRoute)(req, routeName);\n            const rpcMetadata = (0, core_1.getRPCMetadata)(api_1.context.active());\n            if (routeName && (rpcMetadata === null || rpcMetadata === void 0 ? void 0 : rpcMetadata.type) === core_1.RPCType.HTTP) {\n                rpcMetadata.route = (0, utils_1.generateRoute)(req);\n            }\n            let spanName = '';\n            if (routeName) {\n                spanName = `request handler - ${routeName}`;\n            }\n            else {\n                spanName = `middleware - ${middleWare.name || exports.ANONYMOUS_NAME}`;\n            }\n            const span = instrumentation._startSpan(routeName, middleWare);\n            instrumentation._diag.debug('start span', spanName);\n            let spanFinished = false;\n            function finishSpan() {\n                if (!spanFinished) {\n                    spanFinished = true;\n                    instrumentation._diag.debug(`finishing span ${span.name}`);\n                    span.end();\n                }\n                else {\n                    instrumentation._diag.debug(`span ${span.name} - already finished`);\n                }\n                res.removeListener('close', finishSpan);\n            }\n            res.addListener('close', finishSpan);\n            arguments[nextArgIdx] = instrumentation._patchNext(next, finishSpan);\n            return middleWare.apply(this, arguments);\n        }\n        Object.defineProperty(patchedMiddleware, 'length', {\n            value: middleWare.length,\n            writable: false,\n            configurable: true,\n        });\n        return patchedMiddleware;\n    }\n    _patchUse(original) {\n        const instrumentation = this;\n        return function (...args) {\n            const middleWare = args[args.length - 1];\n            const routeName = (args[args.length - 2] || '');\n            args[args.length - 1] = instrumentation._patchMiddleware(routeName, middleWare);\n            return original.apply(this, args);\n        };\n    }\n    _patchHandle(original) {\n        const instrumentation = this;\n        return function () {\n            const [reqIdx, outIdx] = [0, 2];\n            const req = arguments[reqIdx];\n            const out = arguments[outIdx];\n            const completeStack = (0, utils_1.addNewStackLayer)(req);\n            if (typeof out === 'function') {\n                arguments[outIdx] = instrumentation._patchOut(out, completeStack);\n            }\n            return original.apply(this, arguments);\n        };\n    }\n    _patchOut(out, completeStack) {\n        return function nextFunction(...args) {\n            completeStack();\n            return Reflect.apply(out, this, args);\n        };\n    }\n}\nexports.ConnectInstrumentation = ConnectInstrumentation;\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/instrumentation.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/internal-types.js":
/*!*****************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/internal-types.js ***!
  \*****************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports._LAYERS_STORE_PROPERTY = void 0;\nexports._LAYERS_STORE_PROPERTY = Symbol('opentelemetry.instrumentation-connect.request-route-stack');\n//# sourceMappingURL=internal-types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/internal-types.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/utils.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/utils.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.generateRoute = exports.replaceCurrentStackRoute = exports.addNewStackLayer = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst internal_types_1 = __webpack_require__(/*! ./internal-types */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/internal-types.js\");\nconst addNewStackLayer = (request) => {\n    if (Array.isArray(request[internal_types_1._LAYERS_STORE_PROPERTY]) === false) {\n        Object.defineProperty(request, internal_types_1._LAYERS_STORE_PROPERTY, {\n            enumerable: false,\n            value: [],\n        });\n    }\n    request[internal_types_1._LAYERS_STORE_PROPERTY].push('/');\n    const stackLength = request[internal_types_1._LAYERS_STORE_PROPERTY].length;\n    return () => {\n        if (stackLength === request[internal_types_1._LAYERS_STORE_PROPERTY].length) {\n            request[internal_types_1._LAYERS_STORE_PROPERTY].pop();\n        }\n        else {\n            api_1.diag.warn('Connect: Trying to pop the stack multiple time');\n        }\n    };\n};\nexports.addNewStackLayer = addNewStackLayer;\nconst replaceCurrentStackRoute = (request, newRoute) => {\n    if (newRoute) {\n        request[internal_types_1._LAYERS_STORE_PROPERTY].splice(-1, 1, newRoute);\n    }\n};\nexports.replaceCurrentStackRoute = replaceCurrentStackRoute;\n// generate route from existing stack on request object.\n// splash between stack layer will be deduped\n// [\"/first/\", \"/second\", \"/third/\"] => /first/second/third/\nconst generateRoute = (request) => {\n    return request[internal_types_1._LAYERS_STORE_PROPERTY].reduce((acc, sub) => acc.replace(/\\/+$/, '') + sub);\n};\nexports.generateRoute = generateRoute;\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/utils.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/version.js":
/*!**********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/version.js ***!
  \**********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.43.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-connect';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/version.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/enums/AttributeNames.js":
/*!***********************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/enums/AttributeNames.js ***!
  \***********************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.ConnectNames = exports.ConnectTypes = exports.AttributeNames = void 0;\nvar AttributeNames;\n(function (AttributeNames) {\n    AttributeNames[\"CONNECT_TYPE\"] = \"connect.type\";\n    AttributeNames[\"CONNECT_NAME\"] = \"connect.name\";\n})(AttributeNames = exports.AttributeNames || (exports.AttributeNames = {}));\nvar ConnectTypes;\n(function (ConnectTypes) {\n    ConnectTypes[\"MIDDLEWARE\"] = \"middleware\";\n    ConnectTypes[\"REQUEST_HANDLER\"] = \"request_handler\";\n})(ConnectTypes = exports.ConnectTypes || (exports.ConnectTypes = {}));\nvar ConnectNames;\n(function (ConnectNames) {\n    ConnectNames[\"MIDDLEWARE\"] = \"middleware\";\n    ConnectNames[\"REQUEST_HANDLER\"] = \"request handler\";\n})(ConnectNames = exports.ConnectNames || (exports.ConnectNames = {}));\n//# sourceMappingURL=AttributeNames.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/enums/AttributeNames.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/index.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/index.js ***!
  \********************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./enums/AttributeNames */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/enums/AttributeNames.js\"), exports);\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/instrumentation.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/index.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/instrumentation.js":
/*!******************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/instrumentation.js ***!
  \******************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.ConnectInstrumentation = exports.ANONYMOUS_NAME = void 0;\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst core_1 = __webpack_require__(/*! @opentelemetry/core */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+core@1.30.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/core/build/esm/index.js\");\nconst AttributeNames_1 = __webpack_require__(/*! ./enums/AttributeNames */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/enums/AttributeNames.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/version.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/utils.js\");\nexports.ANONYMOUS_NAME = 'anonymous';\n/** Connect instrumentation for OpenTelemetry */\nclass ConnectInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, config);\n    }\n    init() {\n        return [\n            new instrumentation_1.InstrumentationNodeModuleDefinition('connect', ['>=3.0.0 <4'], moduleExports => {\n                return this._patchConstructor(moduleExports);\n            }),\n        ];\n    }\n    _patchApp(patchedApp) {\n        if (!(0, instrumentation_1.isWrapped)(patchedApp.use)) {\n            this._wrap(patchedApp, 'use', this._patchUse.bind(this));\n        }\n        if (!(0, instrumentation_1.isWrapped)(patchedApp.handle)) {\n            this._wrap(patchedApp, 'handle', this._patchHandle.bind(this));\n        }\n    }\n    _patchConstructor(original) {\n        const instrumentation = this;\n        return function (...args) {\n            const app = original.apply(this, args);\n            instrumentation._patchApp(app);\n            return app;\n        };\n    }\n    _patchNext(next, finishSpan) {\n        return function nextFunction(err) {\n            const result = next.apply(this, [err]);\n            finishSpan();\n            return result;\n        };\n    }\n    _startSpan(routeName, middleWare) {\n        let connectType;\n        let connectName;\n        let connectTypeName;\n        if (routeName) {\n            connectType = AttributeNames_1.ConnectTypes.REQUEST_HANDLER;\n            connectTypeName = AttributeNames_1.ConnectNames.REQUEST_HANDLER;\n            connectName = routeName;\n        }\n        else {\n            connectType = AttributeNames_1.ConnectTypes.MIDDLEWARE;\n            connectTypeName = AttributeNames_1.ConnectNames.MIDDLEWARE;\n            connectName = middleWare.name || exports.ANONYMOUS_NAME;\n        }\n        const spanName = `${connectTypeName} - ${connectName}`;\n        const options = {\n            attributes: {\n                [semantic_conventions_1.SEMATTRS_HTTP_ROUTE]: routeName.length > 0 ? routeName : '/',\n                [AttributeNames_1.AttributeNames.CONNECT_TYPE]: connectType,\n                [AttributeNames_1.AttributeNames.CONNECT_NAME]: connectName,\n            },\n        };\n        return this.tracer.startSpan(spanName, options);\n    }\n    _patchMiddleware(routeName, middleWare) {\n        const instrumentation = this;\n        const isErrorMiddleware = middleWare.length === 4;\n        function patchedMiddleware() {\n            if (!instrumentation.isEnabled()) {\n                return middleWare.apply(this, arguments);\n            }\n            const [reqArgIdx, resArgIdx, nextArgIdx] = isErrorMiddleware\n                ? [1, 2, 3]\n                : [0, 1, 2];\n            const req = arguments[reqArgIdx];\n            const res = arguments[resArgIdx];\n            const next = arguments[nextArgIdx];\n            (0, utils_1.replaceCurrentStackRoute)(req, routeName);\n            const rpcMetadata = (0, core_1.getRPCMetadata)(api_1.context.active());\n            if (routeName && (rpcMetadata === null || rpcMetadata === void 0 ? void 0 : rpcMetadata.type) === core_1.RPCType.HTTP) {\n                rpcMetadata.route = (0, utils_1.generateRoute)(req);\n            }\n            let spanName = '';\n            if (routeName) {\n                spanName = `request handler - ${routeName}`;\n            }\n            else {\n                spanName = `middleware - ${middleWare.name || exports.ANONYMOUS_NAME}`;\n            }\n            const span = instrumentation._startSpan(routeName, middleWare);\n            instrumentation._diag.debug('start span', spanName);\n            let spanFinished = false;\n            function finishSpan() {\n                if (!spanFinished) {\n                    spanFinished = true;\n                    instrumentation._diag.debug(`finishing span ${span.name}`);\n                    span.end();\n                }\n                else {\n                    instrumentation._diag.debug(`span ${span.name} - already finished`);\n                }\n                res.removeListener('close', finishSpan);\n            }\n            res.addListener('close', finishSpan);\n            arguments[nextArgIdx] = instrumentation._patchNext(next, finishSpan);\n            return middleWare.apply(this, arguments);\n        }\n        Object.defineProperty(patchedMiddleware, 'length', {\n            value: middleWare.length,\n            writable: false,\n            configurable: true,\n        });\n        return patchedMiddleware;\n    }\n    _patchUse(original) {\n        const instrumentation = this;\n        return function (...args) {\n            const middleWare = args[args.length - 1];\n            const routeName = (args[args.length - 2] || '');\n            args[args.length - 1] = instrumentation._patchMiddleware(routeName, middleWare);\n            return original.apply(this, args);\n        };\n    }\n    _patchHandle(original) {\n        const instrumentation = this;\n        return function () {\n            const [reqIdx, outIdx] = [0, 2];\n            const req = arguments[reqIdx];\n            const out = arguments[outIdx];\n            const completeStack = (0, utils_1.addNewStackLayer)(req);\n            if (typeof out === 'function') {\n                arguments[outIdx] = instrumentation._patchOut(out, completeStack);\n            }\n            return original.apply(this, arguments);\n        };\n    }\n    _patchOut(out, completeStack) {\n        return function nextFunction(...args) {\n            completeStack();\n            return Reflect.apply(out, this, args);\n        };\n    }\n}\nexports.ConnectInstrumentation = ConnectInstrumentation;\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/instrumentation.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/internal-types.js":
/*!*****************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/internal-types.js ***!
  \*****************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports._LAYERS_STORE_PROPERTY = void 0;\nexports._LAYERS_STORE_PROPERTY = Symbol('opentelemetry.instrumentation-connect.request-route-stack');\n//# sourceMappingURL=internal-types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/internal-types.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/utils.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/utils.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.generateRoute = exports.replaceCurrentStackRoute = exports.addNewStackLayer = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst internal_types_1 = __webpack_require__(/*! ./internal-types */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/internal-types.js\");\nconst addNewStackLayer = (request) => {\n    if (Array.isArray(request[internal_types_1._LAYERS_STORE_PROPERTY]) === false) {\n        Object.defineProperty(request, internal_types_1._LAYERS_STORE_PROPERTY, {\n            enumerable: false,\n            value: [],\n        });\n    }\n    request[internal_types_1._LAYERS_STORE_PROPERTY].push('/');\n    const stackLength = request[internal_types_1._LAYERS_STORE_PROPERTY].length;\n    return () => {\n        if (stackLength === request[internal_types_1._LAYERS_STORE_PROPERTY].length) {\n            request[internal_types_1._LAYERS_STORE_PROPERTY].pop();\n        }\n        else {\n            api_1.diag.warn('Connect: Trying to pop the stack multiple time');\n        }\n    };\n};\nexports.addNewStackLayer = addNewStackLayer;\nconst replaceCurrentStackRoute = (request, newRoute) => {\n    if (newRoute) {\n        request[internal_types_1._LAYERS_STORE_PROPERTY].splice(-1, 1, newRoute);\n    }\n};\nexports.replaceCurrentStackRoute = replaceCurrentStackRoute;\n// generate route from existing stack on request object.\n// splash between stack layer will be deduped\n// [\"/first/\", \"/second\", \"/third/\"] => /first/second/third/\nconst generateRoute = (request) => {\n    return request[internal_types_1._LAYERS_STORE_PROPERTY].reduce((acc, sub) => acc.replace(/\\/+$/, '') + sub);\n};\nexports.generateRoute = generateRoute;\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/utils.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/version.js":
/*!**********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/version.js ***!
  \**********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.43.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-connect';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/version.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/enums/AttributeNames.js":
/*!***********************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/enums/AttributeNames.js ***!
  \***********************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.ConnectNames = exports.ConnectTypes = exports.AttributeNames = void 0;\nvar AttributeNames;\n(function (AttributeNames) {\n    AttributeNames[\"CONNECT_TYPE\"] = \"connect.type\";\n    AttributeNames[\"CONNECT_NAME\"] = \"connect.name\";\n})(AttributeNames = exports.AttributeNames || (exports.AttributeNames = {}));\nvar ConnectTypes;\n(function (ConnectTypes) {\n    ConnectTypes[\"MIDDLEWARE\"] = \"middleware\";\n    ConnectTypes[\"REQUEST_HANDLER\"] = \"request_handler\";\n})(ConnectTypes = exports.ConnectTypes || (exports.ConnectTypes = {}));\nvar ConnectNames;\n(function (ConnectNames) {\n    ConnectNames[\"MIDDLEWARE\"] = \"middleware\";\n    ConnectNames[\"REQUEST_HANDLER\"] = \"request handler\";\n})(ConnectNames = exports.ConnectNames || (exports.ConnectNames = {}));\n//# sourceMappingURL=AttributeNames.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/enums/AttributeNames.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/index.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/index.js ***!
  \********************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./enums/AttributeNames */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/enums/AttributeNames.js\"), exports);\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/instrumentation.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF9iODJlMWZlMzliNTdjMmNlMWJiOTYzNjMxMjE2NmUzNi9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLWNvbm5lY3QvYnVpbGQvc3JjL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1DQUFtQyxvQ0FBb0MsZ0JBQWdCO0FBQ3ZGLENBQUM7QUFDRDtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxhQUFhLG1CQUFPLENBQUMsaU5BQXdCO0FBQzdDLGFBQWEsbUJBQU8sQ0FBQyx1TUFBbUI7QUFDeEMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYW53YXJcXERvY3VtZW50c1xcQ3ViZW50d2ViXFxjdWJlbnRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF9iODJlMWZlMzliNTdjMmNlMWJiOTYzNjMxMjE2NmUzNlxcbm9kZV9tb2R1bGVzXFxAb3BlbnRlbGVtZXRyeVxcaW5zdHJ1bWVudGF0aW9uLWNvbm5lY3RcXGJ1aWxkXFxzcmNcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuLypcbiAqIENvcHlyaWdodCBUaGUgT3BlblRlbGVtZXRyeSBBdXRob3JzXG4gKlxuICogTGljZW5zZWQgdW5kZXIgdGhlIEFwYWNoZSBMaWNlbnNlLCBWZXJzaW9uIDIuMCAodGhlIFwiTGljZW5zZVwiKTtcbiAqIHlvdSBtYXkgbm90IHVzZSB0aGlzIGZpbGUgZXhjZXB0IGluIGNvbXBsaWFuY2Ugd2l0aCB0aGUgTGljZW5zZS5cbiAqIFlvdSBtYXkgb2J0YWluIGEgY29weSBvZiB0aGUgTGljZW5zZSBhdFxuICpcbiAqICAgICAgaHR0cHM6Ly93d3cuYXBhY2hlLm9yZy9saWNlbnNlcy9MSUNFTlNFLTIuMFxuICpcbiAqIFVubGVzcyByZXF1aXJlZCBieSBhcHBsaWNhYmxlIGxhdyBvciBhZ3JlZWQgdG8gaW4gd3JpdGluZywgc29mdHdhcmVcbiAqIGRpc3RyaWJ1dGVkIHVuZGVyIHRoZSBMaWNlbnNlIGlzIGRpc3RyaWJ1dGVkIG9uIGFuIFwiQVMgSVNcIiBCQVNJUyxcbiAqIFdJVEhPVVQgV0FSUkFOVElFUyBPUiBDT05ESVRJT05TIE9GIEFOWSBLSU5ELCBlaXRoZXIgZXhwcmVzcyBvciBpbXBsaWVkLlxuICogU2VlIHRoZSBMaWNlbnNlIGZvciB0aGUgc3BlY2lmaWMgbGFuZ3VhZ2UgZ292ZXJuaW5nIHBlcm1pc3Npb25zIGFuZFxuICogbGltaXRhdGlvbnMgdW5kZXIgdGhlIExpY2Vuc2UuXG4gKi9cbnZhciBfX2NyZWF0ZUJpbmRpbmcgPSAodGhpcyAmJiB0aGlzLl9fY3JlYXRlQmluZGluZykgfHwgKE9iamVjdC5jcmVhdGUgPyAoZnVuY3Rpb24obywgbSwgaywgazIpIHtcbiAgICBpZiAoazIgPT09IHVuZGVmaW5lZCkgazIgPSBrO1xuICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eShvLCBrMiwgeyBlbnVtZXJhYmxlOiB0cnVlLCBnZXQ6IGZ1bmN0aW9uKCkgeyByZXR1cm4gbVtrXTsgfSB9KTtcbn0pIDogKGZ1bmN0aW9uKG8sIG0sIGssIGsyKSB7XG4gICAgaWYgKGsyID09PSB1bmRlZmluZWQpIGsyID0gaztcbiAgICBvW2syXSA9IG1ba107XG59KSk7XG52YXIgX19leHBvcnRTdGFyID0gKHRoaXMgJiYgdGhpcy5fX2V4cG9ydFN0YXIpIHx8IGZ1bmN0aW9uKG0sIGV4cG9ydHMpIHtcbiAgICBmb3IgKHZhciBwIGluIG0pIGlmIChwICE9PSBcImRlZmF1bHRcIiAmJiAhT2JqZWN0LnByb3RvdHlwZS5oYXNPd25Qcm9wZXJ0eS5jYWxsKGV4cG9ydHMsIHApKSBfX2NyZWF0ZUJpbmRpbmcoZXhwb3J0cywgbSwgcCk7XG59O1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuX19leHBvcnRTdGFyKHJlcXVpcmUoXCIuL2VudW1zL0F0dHJpYnV0ZU5hbWVzXCIpLCBleHBvcnRzKTtcbl9fZXhwb3J0U3RhcihyZXF1aXJlKFwiLi9pbnN0cnVtZW50YXRpb25cIiksIGV4cG9ydHMpO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXguanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/instrumentation.js":
/*!******************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/instrumentation.js ***!
  \******************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.ConnectInstrumentation = exports.ANONYMOUS_NAME = void 0;\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst core_1 = __webpack_require__(/*! @opentelemetry/core */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+core@1.30.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/core/build/esm/index.js\");\nconst AttributeNames_1 = __webpack_require__(/*! ./enums/AttributeNames */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/enums/AttributeNames.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/version.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/utils.js\");\nexports.ANONYMOUS_NAME = 'anonymous';\n/** Connect instrumentation for OpenTelemetry */\nclass ConnectInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, config);\n    }\n    init() {\n        return [\n            new instrumentation_1.InstrumentationNodeModuleDefinition('connect', ['>=3.0.0 <4'], moduleExports => {\n                return this._patchConstructor(moduleExports);\n            }),\n        ];\n    }\n    _patchApp(patchedApp) {\n        if (!(0, instrumentation_1.isWrapped)(patchedApp.use)) {\n            this._wrap(patchedApp, 'use', this._patchUse.bind(this));\n        }\n        if (!(0, instrumentation_1.isWrapped)(patchedApp.handle)) {\n            this._wrap(patchedApp, 'handle', this._patchHandle.bind(this));\n        }\n    }\n    _patchConstructor(original) {\n        const instrumentation = this;\n        return function (...args) {\n            const app = original.apply(this, args);\n            instrumentation._patchApp(app);\n            return app;\n        };\n    }\n    _patchNext(next, finishSpan) {\n        return function nextFunction(err) {\n            const result = next.apply(this, [err]);\n            finishSpan();\n            return result;\n        };\n    }\n    _startSpan(routeName, middleWare) {\n        let connectType;\n        let connectName;\n        let connectTypeName;\n        if (routeName) {\n            connectType = AttributeNames_1.ConnectTypes.REQUEST_HANDLER;\n            connectTypeName = AttributeNames_1.ConnectNames.REQUEST_HANDLER;\n            connectName = routeName;\n        }\n        else {\n            connectType = AttributeNames_1.ConnectTypes.MIDDLEWARE;\n            connectTypeName = AttributeNames_1.ConnectNames.MIDDLEWARE;\n            connectName = middleWare.name || exports.ANONYMOUS_NAME;\n        }\n        const spanName = `${connectTypeName} - ${connectName}`;\n        const options = {\n            attributes: {\n                [semantic_conventions_1.SEMATTRS_HTTP_ROUTE]: routeName.length > 0 ? routeName : '/',\n                [AttributeNames_1.AttributeNames.CONNECT_TYPE]: connectType,\n                [AttributeNames_1.AttributeNames.CONNECT_NAME]: connectName,\n            },\n        };\n        return this.tracer.startSpan(spanName, options);\n    }\n    _patchMiddleware(routeName, middleWare) {\n        const instrumentation = this;\n        const isErrorMiddleware = middleWare.length === 4;\n        function patchedMiddleware() {\n            if (!instrumentation.isEnabled()) {\n                return middleWare.apply(this, arguments);\n            }\n            const [reqArgIdx, resArgIdx, nextArgIdx] = isErrorMiddleware\n                ? [1, 2, 3]\n                : [0, 1, 2];\n            const req = arguments[reqArgIdx];\n            const res = arguments[resArgIdx];\n            const next = arguments[nextArgIdx];\n            (0, utils_1.replaceCurrentStackRoute)(req, routeName);\n            const rpcMetadata = (0, core_1.getRPCMetadata)(api_1.context.active());\n            if (routeName && (rpcMetadata === null || rpcMetadata === void 0 ? void 0 : rpcMetadata.type) === core_1.RPCType.HTTP) {\n                rpcMetadata.route = (0, utils_1.generateRoute)(req);\n            }\n            let spanName = '';\n            if (routeName) {\n                spanName = `request handler - ${routeName}`;\n            }\n            else {\n                spanName = `middleware - ${middleWare.name || exports.ANONYMOUS_NAME}`;\n            }\n            const span = instrumentation._startSpan(routeName, middleWare);\n            instrumentation._diag.debug('start span', spanName);\n            let spanFinished = false;\n            function finishSpan() {\n                if (!spanFinished) {\n                    spanFinished = true;\n                    instrumentation._diag.debug(`finishing span ${span.name}`);\n                    span.end();\n                }\n                else {\n                    instrumentation._diag.debug(`span ${span.name} - already finished`);\n                }\n                res.removeListener('close', finishSpan);\n            }\n            res.addListener('close', finishSpan);\n            arguments[nextArgIdx] = instrumentation._patchNext(next, finishSpan);\n            return middleWare.apply(this, arguments);\n        }\n        Object.defineProperty(patchedMiddleware, 'length', {\n            value: middleWare.length,\n            writable: false,\n            configurable: true,\n        });\n        return patchedMiddleware;\n    }\n    _patchUse(original) {\n        const instrumentation = this;\n        return function (...args) {\n            const middleWare = args[args.length - 1];\n            const routeName = (args[args.length - 2] || '');\n            args[args.length - 1] = instrumentation._patchMiddleware(routeName, middleWare);\n            return original.apply(this, args);\n        };\n    }\n    _patchHandle(original) {\n        const instrumentation = this;\n        return function () {\n            const [reqIdx, outIdx] = [0, 2];\n            const req = arguments[reqIdx];\n            const out = arguments[outIdx];\n            const completeStack = (0, utils_1.addNewStackLayer)(req);\n            if (typeof out === 'function') {\n                arguments[outIdx] = instrumentation._patchOut(out, completeStack);\n            }\n            return original.apply(this, arguments);\n        };\n    }\n    _patchOut(out, completeStack) {\n        return function nextFunction(...args) {\n            completeStack();\n            return Reflect.apply(out, this, args);\n        };\n    }\n}\nexports.ConnectInstrumentation = ConnectInstrumentation;\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/instrumentation.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/internal-types.js":
/*!*****************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/internal-types.js ***!
  \*****************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports._LAYERS_STORE_PROPERTY = void 0;\nexports._LAYERS_STORE_PROPERTY = Symbol('opentelemetry.instrumentation-connect.request-route-stack');\n//# sourceMappingURL=internal-types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/internal-types.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/utils.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/utils.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.generateRoute = exports.replaceCurrentStackRoute = exports.addNewStackLayer = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst internal_types_1 = __webpack_require__(/*! ./internal-types */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/internal-types.js\");\nconst addNewStackLayer = (request) => {\n    if (Array.isArray(request[internal_types_1._LAYERS_STORE_PROPERTY]) === false) {\n        Object.defineProperty(request, internal_types_1._LAYERS_STORE_PROPERTY, {\n            enumerable: false,\n            value: [],\n        });\n    }\n    request[internal_types_1._LAYERS_STORE_PROPERTY].push('/');\n    const stackLength = request[internal_types_1._LAYERS_STORE_PROPERTY].length;\n    return () => {\n        if (stackLength === request[internal_types_1._LAYERS_STORE_PROPERTY].length) {\n            request[internal_types_1._LAYERS_STORE_PROPERTY].pop();\n        }\n        else {\n            api_1.diag.warn('Connect: Trying to pop the stack multiple time');\n        }\n    };\n};\nexports.addNewStackLayer = addNewStackLayer;\nconst replaceCurrentStackRoute = (request, newRoute) => {\n    if (newRoute) {\n        request[internal_types_1._LAYERS_STORE_PROPERTY].splice(-1, 1, newRoute);\n    }\n};\nexports.replaceCurrentStackRoute = replaceCurrentStackRoute;\n// generate route from existing stack on request object.\n// splash between stack layer will be deduped\n// [\"/first/\", \"/second\", \"/third/\"] => /first/second/third/\nconst generateRoute = (request) => {\n    return request[internal_types_1._LAYERS_STORE_PROPERTY].reduce((acc, sub) => acc.replace(/\\/+$/, '') + sub);\n};\nexports.generateRoute = generateRoute;\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/utils.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/version.js":
/*!**********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/version.js ***!
  \**********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.43.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-connect';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF9iODJlMWZlMzliNTdjMmNlMWJiOTYzNjMxMjE2NmUzNi9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLWNvbm5lY3QvYnVpbGQvc3JjL3ZlcnNpb24uanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0Qsb0JBQW9CLEdBQUcsdUJBQXVCO0FBQzlDO0FBQ0EsdUJBQXVCO0FBQ3ZCLG9CQUFvQjtBQUNwQiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbndhclxcRG9jdW1lbnRzXFxDdWJlbnR3ZWJcXGN1YmVudFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQG9wZW50ZWxlbWV0cnkraW5zdHJ1bWVudGF0X2I4MmUxZmUzOWI1N2MyY2UxYmI5NjM2MzEyMTY2ZTM2XFxub2RlX21vZHVsZXNcXEBvcGVudGVsZW1ldHJ5XFxpbnN0cnVtZW50YXRpb24tY29ubmVjdFxcYnVpbGRcXHNyY1xcdmVyc2lvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbi8qXG4gKiBDb3B5cmlnaHQgVGhlIE9wZW5UZWxlbWV0cnkgQXV0aG9yc1xuICpcbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4gKiB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuXG4gKiBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbiAqXG4gKiAgICAgIGh0dHBzOi8vd3d3LmFwYWNoZS5vcmcvbGljZW5zZXMvTElDRU5TRS0yLjBcbiAqXG4gKiBVbmxlc3MgcmVxdWlyZWQgYnkgYXBwbGljYWJsZSBsYXcgb3IgYWdyZWVkIHRvIGluIHdyaXRpbmcsIHNvZnR3YXJlXG4gKiBkaXN0cmlidXRlZCB1bmRlciB0aGUgTGljZW5zZSBpcyBkaXN0cmlidXRlZCBvbiBhbiBcIkFTIElTXCIgQkFTSVMsXG4gKiBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgQ09ORElUSU9OUyBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC5cbiAqIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmRcbiAqIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuICovXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLlBBQ0tBR0VfTkFNRSA9IGV4cG9ydHMuUEFDS0FHRV9WRVJTSU9OID0gdm9pZCAwO1xuLy8gdGhpcyBpcyBhdXRvZ2VuZXJhdGVkIGZpbGUsIHNlZSBzY3JpcHRzL3ZlcnNpb24tdXBkYXRlLmpzXG5leHBvcnRzLlBBQ0tBR0VfVkVSU0lPTiA9ICcwLjQzLjEnO1xuZXhwb3J0cy5QQUNLQUdFX05BTUUgPSAnQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLWNvbm5lY3QnO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dmVyc2lvbi5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/version.js\n");

/***/ })

};
;