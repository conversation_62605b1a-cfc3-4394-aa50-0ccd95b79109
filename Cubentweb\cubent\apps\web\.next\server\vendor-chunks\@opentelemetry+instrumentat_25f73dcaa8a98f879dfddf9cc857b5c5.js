"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5";
exports.ids = ["vendor-chunks/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5"];
exports.modules = {

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/enums/AttributeNames.js":
/*!*******************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/enums/AttributeNames.js ***!
  \*******************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.AttributeNames = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar AttributeNames;\n(function (AttributeNames) {\n    AttributeNames[\"KOA_TYPE\"] = \"koa.type\";\n    AttributeNames[\"KOA_NAME\"] = \"koa.name\";\n})(AttributeNames = exports.AttributeNames || (exports.AttributeNames = {}));\n//# sourceMappingURL=AttributeNames.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/enums/AttributeNames.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/index.js":
/*!****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/index.js ***!
  \****************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/instrumentation.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/types.js\"), exports);\n__exportStar(__webpack_require__(/*! ./enums/AttributeNames */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/enums/AttributeNames.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/index.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/instrumentation.js":
/*!**************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/instrumentation.js ***!
  \**************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KoaInstrumentation = void 0;\nconst api = __webpack_require__(/*! @opentelemetry/api */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst types_1 = __webpack_require__(/*! ./types */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/types.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/version.js\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/utils.js\");\nconst core_1 = __webpack_require__(/*! @opentelemetry/core */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+core@1.30.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/core/build/esm/index.js\");\nconst internal_types_1 = __webpack_require__(/*! ./internal-types */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/internal-types.js\");\n/** Koa instrumentation for OpenTelemetry */\nclass KoaInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, config);\n    }\n    init() {\n        return new instrumentation_1.InstrumentationNodeModuleDefinition('koa', ['>=2.0.0 <3'], (module) => {\n            const moduleExports = module[Symbol.toStringTag] === 'Module'\n                ? module.default // ESM\n                : module; // CommonJS\n            if (moduleExports == null) {\n                return moduleExports;\n            }\n            if ((0, instrumentation_1.isWrapped)(moduleExports.prototype.use)) {\n                this._unwrap(moduleExports.prototype, 'use');\n            }\n            this._wrap(moduleExports.prototype, 'use', this._getKoaUsePatch.bind(this));\n            return module;\n        }, (module) => {\n            const moduleExports = module[Symbol.toStringTag] === 'Module'\n                ? module.default // ESM\n                : module; // CommonJS\n            if ((0, instrumentation_1.isWrapped)(moduleExports.prototype.use)) {\n                this._unwrap(moduleExports.prototype, 'use');\n            }\n        });\n    }\n    /**\n     * Patches the Koa.use function in order to instrument each original\n     * middleware layer which is introduced\n     * @param {KoaMiddleware} middleware - the original middleware function\n     */\n    _getKoaUsePatch(original) {\n        const plugin = this;\n        return function use(middlewareFunction) {\n            let patchedFunction;\n            if (middlewareFunction.router) {\n                patchedFunction = plugin._patchRouterDispatch(middlewareFunction);\n            }\n            else {\n                patchedFunction = plugin._patchLayer(middlewareFunction, false);\n            }\n            return original.apply(this, [patchedFunction]);\n        };\n    }\n    /**\n     * Patches the dispatch function used by @koa/router. This function\n     * goes through each routed middleware and adds instrumentation via a call\n     * to the @function _patchLayer function.\n     * @param {KoaMiddleware} dispatchLayer - the original dispatch function which dispatches\n     * routed middleware\n     */\n    _patchRouterDispatch(dispatchLayer) {\n        var _a;\n        api.diag.debug('Patching @koa/router dispatch');\n        const router = dispatchLayer.router;\n        const routesStack = (_a = router === null || router === void 0 ? void 0 : router.stack) !== null && _a !== void 0 ? _a : [];\n        for (const pathLayer of routesStack) {\n            const path = pathLayer.path;\n            const pathStack = pathLayer.stack;\n            for (let j = 0; j < pathStack.length; j++) {\n                const routedMiddleware = pathStack[j];\n                pathStack[j] = this._patchLayer(routedMiddleware, true, path);\n            }\n        }\n        return dispatchLayer;\n    }\n    /**\n     * Patches each individual @param middlewareLayer function in order to create the\n     * span and propagate context. It does not create spans when there is no parent span.\n     * @param {KoaMiddleware} middlewareLayer - the original middleware function.\n     * @param {boolean} isRouter - tracks whether the original middleware function\n     * was dispatched by the router originally\n     * @param {string?} layerPath - if present, provides additional data from the\n     * router about the routed path which the middleware is attached to\n     */\n    _patchLayer(middlewareLayer, isRouter, layerPath) {\n        const layerType = isRouter ? types_1.KoaLayerType.ROUTER : types_1.KoaLayerType.MIDDLEWARE;\n        // Skip patching layer if its ignored in the config\n        if (middlewareLayer[internal_types_1.kLayerPatched] === true ||\n            (0, utils_1.isLayerIgnored)(layerType, this.getConfig()))\n            return middlewareLayer;\n        if (middlewareLayer.constructor.name === 'GeneratorFunction' ||\n            middlewareLayer.constructor.name === 'AsyncGeneratorFunction') {\n            api.diag.debug('ignoring generator-based Koa middleware layer');\n            return middlewareLayer;\n        }\n        middlewareLayer[internal_types_1.kLayerPatched] = true;\n        api.diag.debug('patching Koa middleware layer');\n        return async (context, next) => {\n            const parent = api.trace.getSpan(api.context.active());\n            if (parent === undefined) {\n                return middlewareLayer(context, next);\n            }\n            const metadata = (0, utils_1.getMiddlewareMetadata)(context, middlewareLayer, isRouter, layerPath);\n            const span = this.tracer.startSpan(metadata.name, {\n                attributes: metadata.attributes,\n            });\n            const rpcMetadata = (0, core_1.getRPCMetadata)(api.context.active());\n            if ((rpcMetadata === null || rpcMetadata === void 0 ? void 0 : rpcMetadata.type) === core_1.RPCType.HTTP && context._matchedRoute) {\n                rpcMetadata.route = context._matchedRoute.toString();\n            }\n            const { requestHook } = this.getConfig();\n            if (requestHook) {\n                (0, instrumentation_1.safeExecuteInTheMiddle)(() => requestHook(span, {\n                    context,\n                    middlewareLayer,\n                    layerType,\n                }), e => {\n                    if (e) {\n                        api.diag.error('koa instrumentation: request hook failed', e);\n                    }\n                }, true);\n            }\n            const newContext = api.trace.setSpan(api.context.active(), span);\n            return api.context.with(newContext, async () => {\n                try {\n                    return await middlewareLayer(context, next);\n                }\n                catch (err) {\n                    span.recordException(err);\n                    throw err;\n                }\n                finally {\n                    span.end();\n                }\n            });\n        };\n    }\n}\nexports.KoaInstrumentation = KoaInstrumentation;\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/instrumentation.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/internal-types.js":
/*!*************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/internal-types.js ***!
  \*************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.kLayerPatched = void 0;\n/**\n * This symbol is used to mark a Koa layer as being already instrumented\n * since its possible to use a given layer multiple times (ex: middlewares)\n */\nexports.kLayerPatched = Symbol('koa-layer-patched');\n//# sourceMappingURL=internal-types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9Ab3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfMjVmNzNkY2FhOGE5OGY4NzlkZmRkZjljYzg1N2I1YzUvbm9kZV9tb2R1bGVzL0BvcGVudGVsZW1ldHJ5L2luc3RydW1lbnRhdGlvbi1rb2EvYnVpbGQvc3JjL2ludGVybmFsLXR5cGVzLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELHFCQUFxQjtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQjtBQUNyQiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbndhclxcRG9jdW1lbnRzXFxDdWJlbnR3ZWJcXGN1YmVudFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQG9wZW50ZWxlbWV0cnkraW5zdHJ1bWVudGF0XzI1ZjczZGNhYThhOThmODc5ZGZkZGY5Y2M4NTdiNWM1XFxub2RlX21vZHVsZXNcXEBvcGVudGVsZW1ldHJ5XFxpbnN0cnVtZW50YXRpb24ta29hXFxidWlsZFxcc3JjXFxpbnRlcm5hbC10eXBlcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMua0xheWVyUGF0Y2hlZCA9IHZvaWQgMDtcbi8qKlxuICogVGhpcyBzeW1ib2wgaXMgdXNlZCB0byBtYXJrIGEgS29hIGxheWVyIGFzIGJlaW5nIGFscmVhZHkgaW5zdHJ1bWVudGVkXG4gKiBzaW5jZSBpdHMgcG9zc2libGUgdG8gdXNlIGEgZ2l2ZW4gbGF5ZXIgbXVsdGlwbGUgdGltZXMgKGV4OiBtaWRkbGV3YXJlcylcbiAqL1xuZXhwb3J0cy5rTGF5ZXJQYXRjaGVkID0gU3ltYm9sKCdrb2EtbGF5ZXItcGF0Y2hlZCcpO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW50ZXJuYWwtdHlwZXMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/internal-types.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/types.js":
/*!****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/types.js ***!
  \****************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KoaLayerType = void 0;\nvar KoaLayerType;\n(function (KoaLayerType) {\n    KoaLayerType[\"ROUTER\"] = \"router\";\n    KoaLayerType[\"MIDDLEWARE\"] = \"middleware\";\n})(KoaLayerType = exports.KoaLayerType || (exports.KoaLayerType = {}));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9Ab3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfMjVmNzNkY2FhOGE5OGY4NzlkZmRkZjljYzg1N2I1YzUvbm9kZV9tb2R1bGVzL0BvcGVudGVsZW1ldHJ5L2luc3RydW1lbnRhdGlvbi1rb2EvYnVpbGQvc3JjL3R5cGVzLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELG9CQUFvQjtBQUNwQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsMENBQTBDLG9CQUFvQixLQUFLO0FBQ3BFIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFud2FyXFxEb2N1bWVudHNcXEN1YmVudHdlYlxcY3ViZW50XFxub2RlX21vZHVsZXNcXC5wbnBtXFxAb3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfMjVmNzNkY2FhOGE5OGY4NzlkZmRkZjljYzg1N2I1YzVcXG5vZGVfbW9kdWxlc1xcQG9wZW50ZWxlbWV0cnlcXGluc3RydW1lbnRhdGlvbi1rb2FcXGJ1aWxkXFxzcmNcXHR5cGVzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5Lb2FMYXllclR5cGUgPSB2b2lkIDA7XG52YXIgS29hTGF5ZXJUeXBlO1xuKGZ1bmN0aW9uIChLb2FMYXllclR5cGUpIHtcbiAgICBLb2FMYXllclR5cGVbXCJST1VURVJcIl0gPSBcInJvdXRlclwiO1xuICAgIEtvYUxheWVyVHlwZVtcIk1JRERMRVdBUkVcIl0gPSBcIm1pZGRsZXdhcmVcIjtcbn0pKEtvYUxheWVyVHlwZSA9IGV4cG9ydHMuS29hTGF5ZXJUeXBlIHx8IChleHBvcnRzLktvYUxheWVyVHlwZSA9IHt9KSk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD10eXBlcy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/types.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/utils.js":
/*!****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/utils.js ***!
  \****************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.isLayerIgnored = exports.getMiddlewareMetadata = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst types_1 = __webpack_require__(/*! ./types */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/types.js\");\nconst AttributeNames_1 = __webpack_require__(/*! ./enums/AttributeNames */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/enums/AttributeNames.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst getMiddlewareMetadata = (context, layer, isRouter, layerPath) => {\n    var _a;\n    if (isRouter) {\n        return {\n            attributes: {\n                [AttributeNames_1.AttributeNames.KOA_NAME]: layerPath === null || layerPath === void 0 ? void 0 : layerPath.toString(),\n                [AttributeNames_1.AttributeNames.KOA_TYPE]: types_1.KoaLayerType.ROUTER,\n                [semantic_conventions_1.SEMATTRS_HTTP_ROUTE]: layerPath === null || layerPath === void 0 ? void 0 : layerPath.toString(),\n            },\n            name: context._matchedRouteName || `router - ${layerPath}`,\n        };\n    }\n    else {\n        return {\n            attributes: {\n                [AttributeNames_1.AttributeNames.KOA_NAME]: (_a = layer.name) !== null && _a !== void 0 ? _a : 'middleware',\n                [AttributeNames_1.AttributeNames.KOA_TYPE]: types_1.KoaLayerType.MIDDLEWARE,\n            },\n            name: `middleware - ${layer.name}`,\n        };\n    }\n};\nexports.getMiddlewareMetadata = getMiddlewareMetadata;\n/**\n * Check whether the given request is ignored by configuration\n * @param [list] List of ignore patterns\n * @param [onException] callback for doing something when an exception has\n *     occurred\n */\nconst isLayerIgnored = (type, config) => {\n    var _a;\n    return !!(Array.isArray(config === null || config === void 0 ? void 0 : config.ignoreLayersType) &&\n        ((_a = config === null || config === void 0 ? void 0 : config.ignoreLayersType) === null || _a === void 0 ? void 0 : _a.includes(type)));\n};\nexports.isLayerIgnored = isLayerIgnored;\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/utils.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/version.js":
/*!******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/version.js ***!
  \******************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.47.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-koa';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/version.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/enums/AttributeNames.js":
/*!*******************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/enums/AttributeNames.js ***!
  \*******************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.AttributeNames = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar AttributeNames;\n(function (AttributeNames) {\n    AttributeNames[\"KOA_TYPE\"] = \"koa.type\";\n    AttributeNames[\"KOA_NAME\"] = \"koa.name\";\n})(AttributeNames = exports.AttributeNames || (exports.AttributeNames = {}));\n//# sourceMappingURL=AttributeNames.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/enums/AttributeNames.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/index.js":
/*!****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/index.js ***!
  \****************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/instrumentation.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/types.js\"), exports);\n__exportStar(__webpack_require__(/*! ./enums/AttributeNames */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/enums/AttributeNames.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/index.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/instrumentation.js":
/*!**************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/instrumentation.js ***!
  \**************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KoaInstrumentation = void 0;\nconst api = __webpack_require__(/*! @opentelemetry/api */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst types_1 = __webpack_require__(/*! ./types */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/types.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/version.js\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/utils.js\");\nconst core_1 = __webpack_require__(/*! @opentelemetry/core */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+core@1.30.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/core/build/esm/index.js\");\nconst internal_types_1 = __webpack_require__(/*! ./internal-types */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/internal-types.js\");\n/** Koa instrumentation for OpenTelemetry */\nclass KoaInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, config);\n    }\n    init() {\n        return new instrumentation_1.InstrumentationNodeModuleDefinition('koa', ['>=2.0.0 <3'], (module) => {\n            const moduleExports = module[Symbol.toStringTag] === 'Module'\n                ? module.default // ESM\n                : module; // CommonJS\n            if (moduleExports == null) {\n                return moduleExports;\n            }\n            if ((0, instrumentation_1.isWrapped)(moduleExports.prototype.use)) {\n                this._unwrap(moduleExports.prototype, 'use');\n            }\n            this._wrap(moduleExports.prototype, 'use', this._getKoaUsePatch.bind(this));\n            return module;\n        }, (module) => {\n            const moduleExports = module[Symbol.toStringTag] === 'Module'\n                ? module.default // ESM\n                : module; // CommonJS\n            if ((0, instrumentation_1.isWrapped)(moduleExports.prototype.use)) {\n                this._unwrap(moduleExports.prototype, 'use');\n            }\n        });\n    }\n    /**\n     * Patches the Koa.use function in order to instrument each original\n     * middleware layer which is introduced\n     * @param {KoaMiddleware} middleware - the original middleware function\n     */\n    _getKoaUsePatch(original) {\n        const plugin = this;\n        return function use(middlewareFunction) {\n            let patchedFunction;\n            if (middlewareFunction.router) {\n                patchedFunction = plugin._patchRouterDispatch(middlewareFunction);\n            }\n            else {\n                patchedFunction = plugin._patchLayer(middlewareFunction, false);\n            }\n            return original.apply(this, [patchedFunction]);\n        };\n    }\n    /**\n     * Patches the dispatch function used by @koa/router. This function\n     * goes through each routed middleware and adds instrumentation via a call\n     * to the @function _patchLayer function.\n     * @param {KoaMiddleware} dispatchLayer - the original dispatch function which dispatches\n     * routed middleware\n     */\n    _patchRouterDispatch(dispatchLayer) {\n        var _a;\n        api.diag.debug('Patching @koa/router dispatch');\n        const router = dispatchLayer.router;\n        const routesStack = (_a = router === null || router === void 0 ? void 0 : router.stack) !== null && _a !== void 0 ? _a : [];\n        for (const pathLayer of routesStack) {\n            const path = pathLayer.path;\n            const pathStack = pathLayer.stack;\n            for (let j = 0; j < pathStack.length; j++) {\n                const routedMiddleware = pathStack[j];\n                pathStack[j] = this._patchLayer(routedMiddleware, true, path);\n            }\n        }\n        return dispatchLayer;\n    }\n    /**\n     * Patches each individual @param middlewareLayer function in order to create the\n     * span and propagate context. It does not create spans when there is no parent span.\n     * @param {KoaMiddleware} middlewareLayer - the original middleware function.\n     * @param {boolean} isRouter - tracks whether the original middleware function\n     * was dispatched by the router originally\n     * @param {string?} layerPath - if present, provides additional data from the\n     * router about the routed path which the middleware is attached to\n     */\n    _patchLayer(middlewareLayer, isRouter, layerPath) {\n        const layerType = isRouter ? types_1.KoaLayerType.ROUTER : types_1.KoaLayerType.MIDDLEWARE;\n        // Skip patching layer if its ignored in the config\n        if (middlewareLayer[internal_types_1.kLayerPatched] === true ||\n            (0, utils_1.isLayerIgnored)(layerType, this.getConfig()))\n            return middlewareLayer;\n        if (middlewareLayer.constructor.name === 'GeneratorFunction' ||\n            middlewareLayer.constructor.name === 'AsyncGeneratorFunction') {\n            api.diag.debug('ignoring generator-based Koa middleware layer');\n            return middlewareLayer;\n        }\n        middlewareLayer[internal_types_1.kLayerPatched] = true;\n        api.diag.debug('patching Koa middleware layer');\n        return async (context, next) => {\n            const parent = api.trace.getSpan(api.context.active());\n            if (parent === undefined) {\n                return middlewareLayer(context, next);\n            }\n            const metadata = (0, utils_1.getMiddlewareMetadata)(context, middlewareLayer, isRouter, layerPath);\n            const span = this.tracer.startSpan(metadata.name, {\n                attributes: metadata.attributes,\n            });\n            const rpcMetadata = (0, core_1.getRPCMetadata)(api.context.active());\n            if ((rpcMetadata === null || rpcMetadata === void 0 ? void 0 : rpcMetadata.type) === core_1.RPCType.HTTP && context._matchedRoute) {\n                rpcMetadata.route = context._matchedRoute.toString();\n            }\n            const { requestHook } = this.getConfig();\n            if (requestHook) {\n                (0, instrumentation_1.safeExecuteInTheMiddle)(() => requestHook(span, {\n                    context,\n                    middlewareLayer,\n                    layerType,\n                }), e => {\n                    if (e) {\n                        api.diag.error('koa instrumentation: request hook failed', e);\n                    }\n                }, true);\n            }\n            const newContext = api.trace.setSpan(api.context.active(), span);\n            return api.context.with(newContext, async () => {\n                try {\n                    return await middlewareLayer(context, next);\n                }\n                catch (err) {\n                    span.recordException(err);\n                    throw err;\n                }\n                finally {\n                    span.end();\n                }\n            });\n        };\n    }\n}\nexports.KoaInstrumentation = KoaInstrumentation;\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/instrumentation.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/internal-types.js":
/*!*************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/internal-types.js ***!
  \*************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.kLayerPatched = void 0;\n/**\n * This symbol is used to mark a Koa layer as being already instrumented\n * since its possible to use a given layer multiple times (ex: middlewares)\n */\nexports.kLayerPatched = Symbol('koa-layer-patched');\n//# sourceMappingURL=internal-types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF8yNWY3M2RjYWE4YTk4Zjg3OWRmZGRmOWNjODU3YjVjNS9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLWtvYS9idWlsZC9zcmMvaW50ZXJuYWwtdHlwZXMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFud2FyXFxEb2N1bWVudHNcXEN1YmVudHdlYlxcY3ViZW50XFxub2RlX21vZHVsZXNcXC5wbnBtXFxAb3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfMjVmNzNkY2FhOGE5OGY4NzlkZmRkZjljYzg1N2I1YzVcXG5vZGVfbW9kdWxlc1xcQG9wZW50ZWxlbWV0cnlcXGluc3RydW1lbnRhdGlvbi1rb2FcXGJ1aWxkXFxzcmNcXGludGVybmFsLXR5cGVzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5rTGF5ZXJQYXRjaGVkID0gdm9pZCAwO1xuLyoqXG4gKiBUaGlzIHN5bWJvbCBpcyB1c2VkIHRvIG1hcmsgYSBLb2EgbGF5ZXIgYXMgYmVpbmcgYWxyZWFkeSBpbnN0cnVtZW50ZWRcbiAqIHNpbmNlIGl0cyBwb3NzaWJsZSB0byB1c2UgYSBnaXZlbiBsYXllciBtdWx0aXBsZSB0aW1lcyAoZXg6IG1pZGRsZXdhcmVzKVxuICovXG5leHBvcnRzLmtMYXllclBhdGNoZWQgPSBTeW1ib2woJ2tvYS1sYXllci1wYXRjaGVkJyk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbnRlcm5hbC10eXBlcy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/internal-types.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/types.js":
/*!****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/types.js ***!
  \****************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KoaLayerType = void 0;\nvar KoaLayerType;\n(function (KoaLayerType) {\n    KoaLayerType[\"ROUTER\"] = \"router\";\n    KoaLayerType[\"MIDDLEWARE\"] = \"middleware\";\n})(KoaLayerType = exports.KoaLayerType || (exports.KoaLayerType = {}));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF8yNWY3M2RjYWE4YTk4Zjg3OWRmZGRmOWNjODU3YjVjNS9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLWtvYS9idWlsZC9zcmMvdHlwZXMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0Qsb0JBQW9CO0FBQ3BCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQywwQ0FBMEMsb0JBQW9CLEtBQUs7QUFDcEUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYW53YXJcXERvY3VtZW50c1xcQ3ViZW50d2ViXFxjdWJlbnRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF8yNWY3M2RjYWE4YTk4Zjg3OWRmZGRmOWNjODU3YjVjNVxcbm9kZV9tb2R1bGVzXFxAb3BlbnRlbGVtZXRyeVxcaW5zdHJ1bWVudGF0aW9uLWtvYVxcYnVpbGRcXHNyY1xcdHlwZXMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLktvYUxheWVyVHlwZSA9IHZvaWQgMDtcbnZhciBLb2FMYXllclR5cGU7XG4oZnVuY3Rpb24gKEtvYUxheWVyVHlwZSkge1xuICAgIEtvYUxheWVyVHlwZVtcIlJPVVRFUlwiXSA9IFwicm91dGVyXCI7XG4gICAgS29hTGF5ZXJUeXBlW1wiTUlERExFV0FSRVwiXSA9IFwibWlkZGxld2FyZVwiO1xufSkoS29hTGF5ZXJUeXBlID0gZXhwb3J0cy5Lb2FMYXllclR5cGUgfHwgKGV4cG9ydHMuS29hTGF5ZXJUeXBlID0ge30pKTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXR5cGVzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/types.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/utils.js":
/*!****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/utils.js ***!
  \****************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.isLayerIgnored = exports.getMiddlewareMetadata = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst types_1 = __webpack_require__(/*! ./types */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/types.js\");\nconst AttributeNames_1 = __webpack_require__(/*! ./enums/AttributeNames */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/enums/AttributeNames.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst getMiddlewareMetadata = (context, layer, isRouter, layerPath) => {\n    var _a;\n    if (isRouter) {\n        return {\n            attributes: {\n                [AttributeNames_1.AttributeNames.KOA_NAME]: layerPath === null || layerPath === void 0 ? void 0 : layerPath.toString(),\n                [AttributeNames_1.AttributeNames.KOA_TYPE]: types_1.KoaLayerType.ROUTER,\n                [semantic_conventions_1.SEMATTRS_HTTP_ROUTE]: layerPath === null || layerPath === void 0 ? void 0 : layerPath.toString(),\n            },\n            name: context._matchedRouteName || `router - ${layerPath}`,\n        };\n    }\n    else {\n        return {\n            attributes: {\n                [AttributeNames_1.AttributeNames.KOA_NAME]: (_a = layer.name) !== null && _a !== void 0 ? _a : 'middleware',\n                [AttributeNames_1.AttributeNames.KOA_TYPE]: types_1.KoaLayerType.MIDDLEWARE,\n            },\n            name: `middleware - ${layer.name}`,\n        };\n    }\n};\nexports.getMiddlewareMetadata = getMiddlewareMetadata;\n/**\n * Check whether the given request is ignored by configuration\n * @param [list] List of ignore patterns\n * @param [onException] callback for doing something when an exception has\n *     occurred\n */\nconst isLayerIgnored = (type, config) => {\n    var _a;\n    return !!(Array.isArray(config === null || config === void 0 ? void 0 : config.ignoreLayersType) &&\n        ((_a = config === null || config === void 0 ? void 0 : config.ignoreLayersType) === null || _a === void 0 ? void 0 : _a.includes(type)));\n};\nexports.isLayerIgnored = isLayerIgnored;\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/utils.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/version.js":
/*!******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/version.js ***!
  \******************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.47.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-koa';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/version.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/enums/AttributeNames.js":
/*!*******************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/enums/AttributeNames.js ***!
  \*******************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.AttributeNames = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar AttributeNames;\n(function (AttributeNames) {\n    AttributeNames[\"KOA_TYPE\"] = \"koa.type\";\n    AttributeNames[\"KOA_NAME\"] = \"koa.name\";\n})(AttributeNames = exports.AttributeNames || (exports.AttributeNames = {}));\n//# sourceMappingURL=AttributeNames.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/enums/AttributeNames.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/index.js":
/*!****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/index.js ***!
  \****************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/instrumentation.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/types.js\"), exports);\n__exportStar(__webpack_require__(/*! ./enums/AttributeNames */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/enums/AttributeNames.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/instrumentation.js":
/*!**************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/instrumentation.js ***!
  \**************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KoaInstrumentation = void 0;\nconst api = __webpack_require__(/*! @opentelemetry/api */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst types_1 = __webpack_require__(/*! ./types */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/types.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/version.js\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/utils.js\");\nconst core_1 = __webpack_require__(/*! @opentelemetry/core */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+core@1.30.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/core/build/esm/index.js\");\nconst internal_types_1 = __webpack_require__(/*! ./internal-types */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/internal-types.js\");\n/** Koa instrumentation for OpenTelemetry */\nclass KoaInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, config);\n    }\n    init() {\n        return new instrumentation_1.InstrumentationNodeModuleDefinition('koa', ['>=2.0.0 <3'], (module) => {\n            const moduleExports = module[Symbol.toStringTag] === 'Module'\n                ? module.default // ESM\n                : module; // CommonJS\n            if (moduleExports == null) {\n                return moduleExports;\n            }\n            if ((0, instrumentation_1.isWrapped)(moduleExports.prototype.use)) {\n                this._unwrap(moduleExports.prototype, 'use');\n            }\n            this._wrap(moduleExports.prototype, 'use', this._getKoaUsePatch.bind(this));\n            return module;\n        }, (module) => {\n            const moduleExports = module[Symbol.toStringTag] === 'Module'\n                ? module.default // ESM\n                : module; // CommonJS\n            if ((0, instrumentation_1.isWrapped)(moduleExports.prototype.use)) {\n                this._unwrap(moduleExports.prototype, 'use');\n            }\n        });\n    }\n    /**\n     * Patches the Koa.use function in order to instrument each original\n     * middleware layer which is introduced\n     * @param {KoaMiddleware} middleware - the original middleware function\n     */\n    _getKoaUsePatch(original) {\n        const plugin = this;\n        return function use(middlewareFunction) {\n            let patchedFunction;\n            if (middlewareFunction.router) {\n                patchedFunction = plugin._patchRouterDispatch(middlewareFunction);\n            }\n            else {\n                patchedFunction = plugin._patchLayer(middlewareFunction, false);\n            }\n            return original.apply(this, [patchedFunction]);\n        };\n    }\n    /**\n     * Patches the dispatch function used by @koa/router. This function\n     * goes through each routed middleware and adds instrumentation via a call\n     * to the @function _patchLayer function.\n     * @param {KoaMiddleware} dispatchLayer - the original dispatch function which dispatches\n     * routed middleware\n     */\n    _patchRouterDispatch(dispatchLayer) {\n        var _a;\n        api.diag.debug('Patching @koa/router dispatch');\n        const router = dispatchLayer.router;\n        const routesStack = (_a = router === null || router === void 0 ? void 0 : router.stack) !== null && _a !== void 0 ? _a : [];\n        for (const pathLayer of routesStack) {\n            const path = pathLayer.path;\n            const pathStack = pathLayer.stack;\n            for (let j = 0; j < pathStack.length; j++) {\n                const routedMiddleware = pathStack[j];\n                pathStack[j] = this._patchLayer(routedMiddleware, true, path);\n            }\n        }\n        return dispatchLayer;\n    }\n    /**\n     * Patches each individual @param middlewareLayer function in order to create the\n     * span and propagate context. It does not create spans when there is no parent span.\n     * @param {KoaMiddleware} middlewareLayer - the original middleware function.\n     * @param {boolean} isRouter - tracks whether the original middleware function\n     * was dispatched by the router originally\n     * @param {string?} layerPath - if present, provides additional data from the\n     * router about the routed path which the middleware is attached to\n     */\n    _patchLayer(middlewareLayer, isRouter, layerPath) {\n        const layerType = isRouter ? types_1.KoaLayerType.ROUTER : types_1.KoaLayerType.MIDDLEWARE;\n        // Skip patching layer if its ignored in the config\n        if (middlewareLayer[internal_types_1.kLayerPatched] === true ||\n            (0, utils_1.isLayerIgnored)(layerType, this.getConfig()))\n            return middlewareLayer;\n        if (middlewareLayer.constructor.name === 'GeneratorFunction' ||\n            middlewareLayer.constructor.name === 'AsyncGeneratorFunction') {\n            api.diag.debug('ignoring generator-based Koa middleware layer');\n            return middlewareLayer;\n        }\n        middlewareLayer[internal_types_1.kLayerPatched] = true;\n        api.diag.debug('patching Koa middleware layer');\n        return async (context, next) => {\n            const parent = api.trace.getSpan(api.context.active());\n            if (parent === undefined) {\n                return middlewareLayer(context, next);\n            }\n            const metadata = (0, utils_1.getMiddlewareMetadata)(context, middlewareLayer, isRouter, layerPath);\n            const span = this.tracer.startSpan(metadata.name, {\n                attributes: metadata.attributes,\n            });\n            const rpcMetadata = (0, core_1.getRPCMetadata)(api.context.active());\n            if ((rpcMetadata === null || rpcMetadata === void 0 ? void 0 : rpcMetadata.type) === core_1.RPCType.HTTP && context._matchedRoute) {\n                rpcMetadata.route = context._matchedRoute.toString();\n            }\n            const { requestHook } = this.getConfig();\n            if (requestHook) {\n                (0, instrumentation_1.safeExecuteInTheMiddle)(() => requestHook(span, {\n                    context,\n                    middlewareLayer,\n                    layerType,\n                }), e => {\n                    if (e) {\n                        api.diag.error('koa instrumentation: request hook failed', e);\n                    }\n                }, true);\n            }\n            const newContext = api.trace.setSpan(api.context.active(), span);\n            return api.context.with(newContext, async () => {\n                try {\n                    return await middlewareLayer(context, next);\n                }\n                catch (err) {\n                    span.recordException(err);\n                    throw err;\n                }\n                finally {\n                    span.end();\n                }\n            });\n        };\n    }\n}\nexports.KoaInstrumentation = KoaInstrumentation;\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/instrumentation.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/internal-types.js":
/*!*************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/internal-types.js ***!
  \*************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.kLayerPatched = void 0;\n/**\n * This symbol is used to mark a Koa layer as being already instrumented\n * since its possible to use a given layer multiple times (ex: middlewares)\n */\nexports.kLayerPatched = Symbol('koa-layer-patched');\n//# sourceMappingURL=internal-types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF8yNWY3M2RjYWE4YTk4Zjg3OWRmZGRmOWNjODU3YjVjNS9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLWtvYS9idWlsZC9zcmMvaW50ZXJuYWwtdHlwZXMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFud2FyXFxEb2N1bWVudHNcXEN1YmVudHdlYlxcY3ViZW50XFxub2RlX21vZHVsZXNcXC5wbnBtXFxAb3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfMjVmNzNkY2FhOGE5OGY4NzlkZmRkZjljYzg1N2I1YzVcXG5vZGVfbW9kdWxlc1xcQG9wZW50ZWxlbWV0cnlcXGluc3RydW1lbnRhdGlvbi1rb2FcXGJ1aWxkXFxzcmNcXGludGVybmFsLXR5cGVzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5rTGF5ZXJQYXRjaGVkID0gdm9pZCAwO1xuLyoqXG4gKiBUaGlzIHN5bWJvbCBpcyB1c2VkIHRvIG1hcmsgYSBLb2EgbGF5ZXIgYXMgYmVpbmcgYWxyZWFkeSBpbnN0cnVtZW50ZWRcbiAqIHNpbmNlIGl0cyBwb3NzaWJsZSB0byB1c2UgYSBnaXZlbiBsYXllciBtdWx0aXBsZSB0aW1lcyAoZXg6IG1pZGRsZXdhcmVzKVxuICovXG5leHBvcnRzLmtMYXllclBhdGNoZWQgPSBTeW1ib2woJ2tvYS1sYXllci1wYXRjaGVkJyk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbnRlcm5hbC10eXBlcy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/internal-types.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/types.js":
/*!****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/types.js ***!
  \****************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KoaLayerType = void 0;\nvar KoaLayerType;\n(function (KoaLayerType) {\n    KoaLayerType[\"ROUTER\"] = \"router\";\n    KoaLayerType[\"MIDDLEWARE\"] = \"middleware\";\n})(KoaLayerType = exports.KoaLayerType || (exports.KoaLayerType = {}));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF8yNWY3M2RjYWE4YTk4Zjg3OWRmZGRmOWNjODU3YjVjNS9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLWtvYS9idWlsZC9zcmMvdHlwZXMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0Qsb0JBQW9CO0FBQ3BCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQywwQ0FBMEMsb0JBQW9CLEtBQUs7QUFDcEUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYW53YXJcXERvY3VtZW50c1xcQ3ViZW50d2ViXFxjdWJlbnRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF8yNWY3M2RjYWE4YTk4Zjg3OWRmZGRmOWNjODU3YjVjNVxcbm9kZV9tb2R1bGVzXFxAb3BlbnRlbGVtZXRyeVxcaW5zdHJ1bWVudGF0aW9uLWtvYVxcYnVpbGRcXHNyY1xcdHlwZXMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLktvYUxheWVyVHlwZSA9IHZvaWQgMDtcbnZhciBLb2FMYXllclR5cGU7XG4oZnVuY3Rpb24gKEtvYUxheWVyVHlwZSkge1xuICAgIEtvYUxheWVyVHlwZVtcIlJPVVRFUlwiXSA9IFwicm91dGVyXCI7XG4gICAgS29hTGF5ZXJUeXBlW1wiTUlERExFV0FSRVwiXSA9IFwibWlkZGxld2FyZVwiO1xufSkoS29hTGF5ZXJUeXBlID0gZXhwb3J0cy5Lb2FMYXllclR5cGUgfHwgKGV4cG9ydHMuS29hTGF5ZXJUeXBlID0ge30pKTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXR5cGVzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/types.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/utils.js":
/*!****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/utils.js ***!
  \****************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.isLayerIgnored = exports.getMiddlewareMetadata = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst types_1 = __webpack_require__(/*! ./types */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/types.js\");\nconst AttributeNames_1 = __webpack_require__(/*! ./enums/AttributeNames */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/enums/AttributeNames.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst getMiddlewareMetadata = (context, layer, isRouter, layerPath) => {\n    var _a;\n    if (isRouter) {\n        return {\n            attributes: {\n                [AttributeNames_1.AttributeNames.KOA_NAME]: layerPath === null || layerPath === void 0 ? void 0 : layerPath.toString(),\n                [AttributeNames_1.AttributeNames.KOA_TYPE]: types_1.KoaLayerType.ROUTER,\n                [semantic_conventions_1.SEMATTRS_HTTP_ROUTE]: layerPath === null || layerPath === void 0 ? void 0 : layerPath.toString(),\n            },\n            name: context._matchedRouteName || `router - ${layerPath}`,\n        };\n    }\n    else {\n        return {\n            attributes: {\n                [AttributeNames_1.AttributeNames.KOA_NAME]: (_a = layer.name) !== null && _a !== void 0 ? _a : 'middleware',\n                [AttributeNames_1.AttributeNames.KOA_TYPE]: types_1.KoaLayerType.MIDDLEWARE,\n            },\n            name: `middleware - ${layer.name}`,\n        };\n    }\n};\nexports.getMiddlewareMetadata = getMiddlewareMetadata;\n/**\n * Check whether the given request is ignored by configuration\n * @param [list] List of ignore patterns\n * @param [onException] callback for doing something when an exception has\n *     occurred\n */\nconst isLayerIgnored = (type, config) => {\n    var _a;\n    return !!(Array.isArray(config === null || config === void 0 ? void 0 : config.ignoreLayersType) &&\n        ((_a = config === null || config === void 0 ? void 0 : config.ignoreLayersType) === null || _a === void 0 ? void 0 : _a.includes(type)));\n};\nexports.isLayerIgnored = isLayerIgnored;\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/utils.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/version.js":
/*!******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/version.js ***!
  \******************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.47.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-koa';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/version.js\n");

/***/ })

};
;