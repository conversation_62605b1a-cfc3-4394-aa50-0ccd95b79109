"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc";
exports.ids = ["vendor-chunks/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc"];
exports.modules = {

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AbstractAsyncHooksContextManager.js":
/*!*******************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AbstractAsyncHooksContextManager.js ***!
  \*******************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.AbstractAsyncHooksContextManager = void 0;\nconst events_1 = __webpack_require__(/*! events */ \"events\");\nconst ADD_LISTENER_METHODS = [\n    'addListener',\n    'on',\n    'once',\n    'prependListener',\n    'prependOnceListener',\n];\nclass AbstractAsyncHooksContextManager {\n    constructor() {\n        this._kOtListeners = Symbol('OtListeners');\n        this._wrapped = false;\n    }\n    /**\n     * Binds a the certain context or the active one to the target function and then returns the target\n     * @param context A context (span) to be bind to target\n     * @param target a function or event emitter. When target or one of its callbacks is called,\n     *  the provided context will be used as the active context for the duration of the call.\n     */\n    bind(context, target) {\n        if (target instanceof events_1.EventEmitter) {\n            return this._bindEventEmitter(context, target);\n        }\n        if (typeof target === 'function') {\n            return this._bindFunction(context, target);\n        }\n        return target;\n    }\n    _bindFunction(context, target) {\n        const manager = this;\n        const contextWrapper = function (...args) {\n            return manager.with(context, () => target.apply(this, args));\n        };\n        Object.defineProperty(contextWrapper, 'length', {\n            enumerable: false,\n            configurable: true,\n            writable: false,\n            value: target.length,\n        });\n        /**\n         * It isn't possible to tell Typescript that contextWrapper is the same as T\n         * so we forced to cast as any here.\n         */\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        return contextWrapper;\n    }\n    /**\n     * By default, EventEmitter call their callback with their context, which we do\n     * not want, instead we will bind a specific context to all callbacks that\n     * go through it.\n     * @param context the context we want to bind\n     * @param ee EventEmitter an instance of EventEmitter to patch\n     */\n    _bindEventEmitter(context, ee) {\n        const map = this._getPatchMap(ee);\n        if (map !== undefined)\n            return ee;\n        this._createPatchMap(ee);\n        // patch methods that add a listener to propagate context\n        ADD_LISTENER_METHODS.forEach(methodName => {\n            if (ee[methodName] === undefined)\n                return;\n            ee[methodName] = this._patchAddListener(ee, ee[methodName], context);\n        });\n        // patch methods that remove a listener\n        if (typeof ee.removeListener === 'function') {\n            ee.removeListener = this._patchRemoveListener(ee, ee.removeListener);\n        }\n        if (typeof ee.off === 'function') {\n            ee.off = this._patchRemoveListener(ee, ee.off);\n        }\n        // patch method that remove all listeners\n        if (typeof ee.removeAllListeners === 'function') {\n            ee.removeAllListeners = this._patchRemoveAllListeners(ee, ee.removeAllListeners);\n        }\n        return ee;\n    }\n    /**\n     * Patch methods that remove a given listener so that we match the \"patched\"\n     * version of that listener (the one that propagate context).\n     * @param ee EventEmitter instance\n     * @param original reference to the patched method\n     */\n    _patchRemoveListener(ee, original) {\n        const contextManager = this;\n        return function (event, listener) {\n            var _a;\n            const events = (_a = contextManager._getPatchMap(ee)) === null || _a === void 0 ? void 0 : _a[event];\n            if (events === undefined) {\n                return original.call(this, event, listener);\n            }\n            const patchedListener = events.get(listener);\n            return original.call(this, event, patchedListener || listener);\n        };\n    }\n    /**\n     * Patch methods that remove all listeners so we remove our\n     * internal references for a given event.\n     * @param ee EventEmitter instance\n     * @param original reference to the patched method\n     */\n    _patchRemoveAllListeners(ee, original) {\n        const contextManager = this;\n        return function (event) {\n            const map = contextManager._getPatchMap(ee);\n            if (map !== undefined) {\n                if (arguments.length === 0) {\n                    contextManager._createPatchMap(ee);\n                }\n                else if (map[event] !== undefined) {\n                    delete map[event];\n                }\n            }\n            return original.apply(this, arguments);\n        };\n    }\n    /**\n     * Patch methods on an event emitter instance that can add listeners so we\n     * can force them to propagate a given context.\n     * @param ee EventEmitter instance\n     * @param original reference to the patched method\n     * @param [context] context to propagate when calling listeners\n     */\n    _patchAddListener(ee, original, context) {\n        const contextManager = this;\n        return function (event, listener) {\n            /**\n             * This check is required to prevent double-wrapping the listener.\n             * The implementation for ee.once wraps the listener and calls ee.on.\n             * Without this check, we would wrap that wrapped listener.\n             * This causes an issue because ee.removeListener depends on the onceWrapper\n             * to properly remove the listener. If we wrap their wrapper, we break\n             * that detection.\n             */\n            if (contextManager._wrapped) {\n                return original.call(this, event, listener);\n            }\n            let map = contextManager._getPatchMap(ee);\n            if (map === undefined) {\n                map = contextManager._createPatchMap(ee);\n            }\n            let listeners = map[event];\n            if (listeners === undefined) {\n                listeners = new WeakMap();\n                map[event] = listeners;\n            }\n            const patchedListener = contextManager.bind(context, listener);\n            // store a weak reference of the user listener to ours\n            listeners.set(listener, patchedListener);\n            /**\n             * See comment at the start of this function for the explanation of this property.\n             */\n            contextManager._wrapped = true;\n            try {\n                return original.call(this, event, patchedListener);\n            }\n            finally {\n                contextManager._wrapped = false;\n            }\n        };\n    }\n    _createPatchMap(ee) {\n        const map = Object.create(null);\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        ee[this._kOtListeners] = map;\n        return map;\n    }\n    _getPatchMap(ee) {\n        return ee[this._kOtListeners];\n    }\n}\nexports.AbstractAsyncHooksContextManager = AbstractAsyncHooksContextManager;\n//# sourceMappingURL=AbstractAsyncHooksContextManager.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AbstractAsyncHooksContextManager.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AsyncHooksContextManager.js":
/*!***********************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AsyncHooksContextManager.js ***!
  \***********************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.AsyncHooksContextManager = void 0;\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst asyncHooks = __webpack_require__(/*! async_hooks */ \"async_hooks\");\nconst AbstractAsyncHooksContextManager_1 = __webpack_require__(/*! ./AbstractAsyncHooksContextManager */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AbstractAsyncHooksContextManager.js\");\nclass AsyncHooksContextManager extends AbstractAsyncHooksContextManager_1.AbstractAsyncHooksContextManager {\n    constructor() {\n        super();\n        this._contexts = new Map();\n        this._stack = [];\n        this._asyncHook = asyncHooks.createHook({\n            init: this._init.bind(this),\n            before: this._before.bind(this),\n            after: this._after.bind(this),\n            destroy: this._destroy.bind(this),\n            promiseResolve: this._destroy.bind(this),\n        });\n    }\n    active() {\n        var _a;\n        return (_a = this._stack[this._stack.length - 1]) !== null && _a !== void 0 ? _a : api_1.ROOT_CONTEXT;\n    }\n    with(context, fn, thisArg, ...args) {\n        this._enterContext(context);\n        try {\n            return fn.call(thisArg, ...args);\n        }\n        finally {\n            this._exitContext();\n        }\n    }\n    enable() {\n        this._asyncHook.enable();\n        return this;\n    }\n    disable() {\n        this._asyncHook.disable();\n        this._contexts.clear();\n        this._stack = [];\n        return this;\n    }\n    /**\n     * Init hook will be called when userland create a async context, setting the\n     * context as the current one if it exist.\n     * @param uid id of the async context\n     * @param type the resource type\n     */\n    _init(uid, type) {\n        // ignore TIMERWRAP as they combine timers with same timeout which can lead to\n        // false context propagation. TIMERWRAP has been removed in node 11\n        // every timer has it's own `Timeout` resource anyway which is used to propagate\n        // context.\n        if (type === 'TIMERWRAP')\n            return;\n        const context = this._stack[this._stack.length - 1];\n        if (context !== undefined) {\n            this._contexts.set(uid, context);\n        }\n    }\n    /**\n     * Destroy hook will be called when a given context is no longer used so we can\n     * remove its attached context.\n     * @param uid uid of the async context\n     */\n    _destroy(uid) {\n        this._contexts.delete(uid);\n    }\n    /**\n     * Before hook is called just before executing a async context.\n     * @param uid uid of the async context\n     */\n    _before(uid) {\n        const context = this._contexts.get(uid);\n        if (context !== undefined) {\n            this._enterContext(context);\n        }\n    }\n    /**\n     * After hook is called just after completing the execution of a async context.\n     */\n    _after() {\n        this._exitContext();\n    }\n    /**\n     * Set the given context as active\n     */\n    _enterContext(context) {\n        this._stack.push(context);\n    }\n    /**\n     * Remove the context at the root of the stack\n     */\n    _exitContext() {\n        this._stack.pop();\n    }\n}\nexports.AsyncHooksContextManager = AsyncHooksContextManager;\n//# sourceMappingURL=AsyncHooksContextManager.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9Ab3BlbnRlbGVtZXRyeStjb250ZXh0LWFzeW5fYmMzNTU2MGNkNzY1OWQ5YzIzZTQyMGM2ZGZmNmNlZmMvbm9kZV9tb2R1bGVzL0BvcGVudGVsZW1ldHJ5L2NvbnRleHQtYXN5bmMtaG9va3MvYnVpbGQvc3JjL0FzeW5jSG9va3NDb250ZXh0TWFuYWdlci5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxnQ0FBZ0M7QUFDaEMsY0FBYyxtQkFBTyxDQUFDLDZJQUFvQjtBQUMxQyxtQkFBbUIsbUJBQU8sQ0FBQyxnQ0FBYTtBQUN4QywyQ0FBMkMsbUJBQU8sQ0FBQyw0T0FBb0M7QUFDdkY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdDQUFnQztBQUNoQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbndhclxcRG9jdW1lbnRzXFxDdWJlbnR3ZWJcXGN1YmVudFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQG9wZW50ZWxlbWV0cnkrY29udGV4dC1hc3luX2JjMzU1NjBjZDc2NTlkOWMyM2U0MjBjNmRmZjZjZWZjXFxub2RlX21vZHVsZXNcXEBvcGVudGVsZW1ldHJ5XFxjb250ZXh0LWFzeW5jLWhvb2tzXFxidWlsZFxcc3JjXFxBc3luY0hvb2tzQ29udGV4dE1hbmFnZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG4vKlxuICogQ29weXJpZ2h0IFRoZSBPcGVuVGVsZW1ldHJ5IEF1dGhvcnNcbiAqXG4gKiBMaWNlbnNlZCB1bmRlciB0aGUgQXBhY2hlIExpY2Vuc2UsIFZlcnNpb24gMi4wICh0aGUgXCJMaWNlbnNlXCIpO1xuICogeW91IG1heSBub3QgdXNlIHRoaXMgZmlsZSBleGNlcHQgaW4gY29tcGxpYW5jZSB3aXRoIHRoZSBMaWNlbnNlLlxuICogWW91IG1heSBvYnRhaW4gYSBjb3B5IG9mIHRoZSBMaWNlbnNlIGF0XG4gKlxuICogICAgICBodHRwczovL3d3dy5hcGFjaGUub3JnL2xpY2Vuc2VzL0xJQ0VOU0UtMi4wXG4gKlxuICogVW5sZXNzIHJlcXVpcmVkIGJ5IGFwcGxpY2FibGUgbGF3IG9yIGFncmVlZCB0byBpbiB3cml0aW5nLCBzb2Z0d2FyZVxuICogZGlzdHJpYnV0ZWQgdW5kZXIgdGhlIExpY2Vuc2UgaXMgZGlzdHJpYnV0ZWQgb24gYW4gXCJBUyBJU1wiIEJBU0lTLFxuICogV0lUSE9VVCBXQVJSQU5USUVTIE9SIENPTkRJVElPTlMgT0YgQU5ZIEtJTkQsIGVpdGhlciBleHByZXNzIG9yIGltcGxpZWQuXG4gKiBTZWUgdGhlIExpY2Vuc2UgZm9yIHRoZSBzcGVjaWZpYyBsYW5ndWFnZSBnb3Zlcm5pbmcgcGVybWlzc2lvbnMgYW5kXG4gKiBsaW1pdGF0aW9ucyB1bmRlciB0aGUgTGljZW5zZS5cbiAqL1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5Bc3luY0hvb2tzQ29udGV4dE1hbmFnZXIgPSB2b2lkIDA7XG5jb25zdCBhcGlfMSA9IHJlcXVpcmUoXCJAb3BlbnRlbGVtZXRyeS9hcGlcIik7XG5jb25zdCBhc3luY0hvb2tzID0gcmVxdWlyZShcImFzeW5jX2hvb2tzXCIpO1xuY29uc3QgQWJzdHJhY3RBc3luY0hvb2tzQ29udGV4dE1hbmFnZXJfMSA9IHJlcXVpcmUoXCIuL0Fic3RyYWN0QXN5bmNIb29rc0NvbnRleHRNYW5hZ2VyXCIpO1xuY2xhc3MgQXN5bmNIb29rc0NvbnRleHRNYW5hZ2VyIGV4dGVuZHMgQWJzdHJhY3RBc3luY0hvb2tzQ29udGV4dE1hbmFnZXJfMS5BYnN0cmFjdEFzeW5jSG9va3NDb250ZXh0TWFuYWdlciB7XG4gICAgY29uc3RydWN0b3IoKSB7XG4gICAgICAgIHN1cGVyKCk7XG4gICAgICAgIHRoaXMuX2NvbnRleHRzID0gbmV3IE1hcCgpO1xuICAgICAgICB0aGlzLl9zdGFjayA9IFtdO1xuICAgICAgICB0aGlzLl9hc3luY0hvb2sgPSBhc3luY0hvb2tzLmNyZWF0ZUhvb2soe1xuICAgICAgICAgICAgaW5pdDogdGhpcy5faW5pdC5iaW5kKHRoaXMpLFxuICAgICAgICAgICAgYmVmb3JlOiB0aGlzLl9iZWZvcmUuYmluZCh0aGlzKSxcbiAgICAgICAgICAgIGFmdGVyOiB0aGlzLl9hZnRlci5iaW5kKHRoaXMpLFxuICAgICAgICAgICAgZGVzdHJveTogdGhpcy5fZGVzdHJveS5iaW5kKHRoaXMpLFxuICAgICAgICAgICAgcHJvbWlzZVJlc29sdmU6IHRoaXMuX2Rlc3Ryb3kuYmluZCh0aGlzKSxcbiAgICAgICAgfSk7XG4gICAgfVxuICAgIGFjdGl2ZSgpIHtcbiAgICAgICAgdmFyIF9hO1xuICAgICAgICByZXR1cm4gKF9hID0gdGhpcy5fc3RhY2tbdGhpcy5fc3RhY2subGVuZ3RoIC0gMV0pICE9PSBudWxsICYmIF9hICE9PSB2b2lkIDAgPyBfYSA6IGFwaV8xLlJPT1RfQ09OVEVYVDtcbiAgICB9XG4gICAgd2l0aChjb250ZXh0LCBmbiwgdGhpc0FyZywgLi4uYXJncykge1xuICAgICAgICB0aGlzLl9lbnRlckNvbnRleHQoY29udGV4dCk7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICByZXR1cm4gZm4uY2FsbCh0aGlzQXJnLCAuLi5hcmdzKTtcbiAgICAgICAgfVxuICAgICAgICBmaW5hbGx5IHtcbiAgICAgICAgICAgIHRoaXMuX2V4aXRDb250ZXh0KCk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgZW5hYmxlKCkge1xuICAgICAgICB0aGlzLl9hc3luY0hvb2suZW5hYmxlKCk7XG4gICAgICAgIHJldHVybiB0aGlzO1xuICAgIH1cbiAgICBkaXNhYmxlKCkge1xuICAgICAgICB0aGlzLl9hc3luY0hvb2suZGlzYWJsZSgpO1xuICAgICAgICB0aGlzLl9jb250ZXh0cy5jbGVhcigpO1xuICAgICAgICB0aGlzLl9zdGFjayA9IFtdO1xuICAgICAgICByZXR1cm4gdGhpcztcbiAgICB9XG4gICAgLyoqXG4gICAgICogSW5pdCBob29rIHdpbGwgYmUgY2FsbGVkIHdoZW4gdXNlcmxhbmQgY3JlYXRlIGEgYXN5bmMgY29udGV4dCwgc2V0dGluZyB0aGVcbiAgICAgKiBjb250ZXh0IGFzIHRoZSBjdXJyZW50IG9uZSBpZiBpdCBleGlzdC5cbiAgICAgKiBAcGFyYW0gdWlkIGlkIG9mIHRoZSBhc3luYyBjb250ZXh0XG4gICAgICogQHBhcmFtIHR5cGUgdGhlIHJlc291cmNlIHR5cGVcbiAgICAgKi9cbiAgICBfaW5pdCh1aWQsIHR5cGUpIHtcbiAgICAgICAgLy8gaWdub3JlIFRJTUVSV1JBUCBhcyB0aGV5IGNvbWJpbmUgdGltZXJzIHdpdGggc2FtZSB0aW1lb3V0IHdoaWNoIGNhbiBsZWFkIHRvXG4gICAgICAgIC8vIGZhbHNlIGNvbnRleHQgcHJvcGFnYXRpb24uIFRJTUVSV1JBUCBoYXMgYmVlbiByZW1vdmVkIGluIG5vZGUgMTFcbiAgICAgICAgLy8gZXZlcnkgdGltZXIgaGFzIGl0J3Mgb3duIGBUaW1lb3V0YCByZXNvdXJjZSBhbnl3YXkgd2hpY2ggaXMgdXNlZCB0byBwcm9wYWdhdGVcbiAgICAgICAgLy8gY29udGV4dC5cbiAgICAgICAgaWYgKHR5cGUgPT09ICdUSU1FUldSQVAnKVxuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICBjb25zdCBjb250ZXh0ID0gdGhpcy5fc3RhY2tbdGhpcy5fc3RhY2subGVuZ3RoIC0gMV07XG4gICAgICAgIGlmIChjb250ZXh0ICE9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICAgIHRoaXMuX2NvbnRleHRzLnNldCh1aWQsIGNvbnRleHQpO1xuICAgICAgICB9XG4gICAgfVxuICAgIC8qKlxuICAgICAqIERlc3Ryb3kgaG9vayB3aWxsIGJlIGNhbGxlZCB3aGVuIGEgZ2l2ZW4gY29udGV4dCBpcyBubyBsb25nZXIgdXNlZCBzbyB3ZSBjYW5cbiAgICAgKiByZW1vdmUgaXRzIGF0dGFjaGVkIGNvbnRleHQuXG4gICAgICogQHBhcmFtIHVpZCB1aWQgb2YgdGhlIGFzeW5jIGNvbnRleHRcbiAgICAgKi9cbiAgICBfZGVzdHJveSh1aWQpIHtcbiAgICAgICAgdGhpcy5fY29udGV4dHMuZGVsZXRlKHVpZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIEJlZm9yZSBob29rIGlzIGNhbGxlZCBqdXN0IGJlZm9yZSBleGVjdXRpbmcgYSBhc3luYyBjb250ZXh0LlxuICAgICAqIEBwYXJhbSB1aWQgdWlkIG9mIHRoZSBhc3luYyBjb250ZXh0XG4gICAgICovXG4gICAgX2JlZm9yZSh1aWQpIHtcbiAgICAgICAgY29uc3QgY29udGV4dCA9IHRoaXMuX2NvbnRleHRzLmdldCh1aWQpO1xuICAgICAgICBpZiAoY29udGV4dCAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICB0aGlzLl9lbnRlckNvbnRleHQoY29udGV4dCk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgLyoqXG4gICAgICogQWZ0ZXIgaG9vayBpcyBjYWxsZWQganVzdCBhZnRlciBjb21wbGV0aW5nIHRoZSBleGVjdXRpb24gb2YgYSBhc3luYyBjb250ZXh0LlxuICAgICAqL1xuICAgIF9hZnRlcigpIHtcbiAgICAgICAgdGhpcy5fZXhpdENvbnRleHQoKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogU2V0IHRoZSBnaXZlbiBjb250ZXh0IGFzIGFjdGl2ZVxuICAgICAqL1xuICAgIF9lbnRlckNvbnRleHQoY29udGV4dCkge1xuICAgICAgICB0aGlzLl9zdGFjay5wdXNoKGNvbnRleHQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBSZW1vdmUgdGhlIGNvbnRleHQgYXQgdGhlIHJvb3Qgb2YgdGhlIHN0YWNrXG4gICAgICovXG4gICAgX2V4aXRDb250ZXh0KCkge1xuICAgICAgICB0aGlzLl9zdGFjay5wb3AoKTtcbiAgICB9XG59XG5leHBvcnRzLkFzeW5jSG9va3NDb250ZXh0TWFuYWdlciA9IEFzeW5jSG9va3NDb250ZXh0TWFuYWdlcjtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPUFzeW5jSG9va3NDb250ZXh0TWFuYWdlci5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AsyncHooksContextManager.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AsyncLocalStorageContextManager.js":
/*!******************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AsyncLocalStorageContextManager.js ***!
  \******************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.AsyncLocalStorageContextManager = void 0;\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst async_hooks_1 = __webpack_require__(/*! async_hooks */ \"async_hooks\");\nconst AbstractAsyncHooksContextManager_1 = __webpack_require__(/*! ./AbstractAsyncHooksContextManager */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AbstractAsyncHooksContextManager.js\");\nclass AsyncLocalStorageContextManager extends AbstractAsyncHooksContextManager_1.AbstractAsyncHooksContextManager {\n    constructor() {\n        super();\n        this._asyncLocalStorage = new async_hooks_1.AsyncLocalStorage();\n    }\n    active() {\n        var _a;\n        return (_a = this._asyncLocalStorage.getStore()) !== null && _a !== void 0 ? _a : api_1.ROOT_CONTEXT;\n    }\n    with(context, fn, thisArg, ...args) {\n        const cb = thisArg == null ? fn : fn.bind(thisArg);\n        return this._asyncLocalStorage.run(context, cb, ...args);\n    }\n    enable() {\n        return this;\n    }\n    disable() {\n        this._asyncLocalStorage.disable();\n        return this;\n    }\n}\nexports.AsyncLocalStorageContextManager = AsyncLocalStorageContextManager;\n//# sourceMappingURL=AsyncLocalStorageContextManager.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AsyncLocalStorageContextManager.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/index.js":
/*!****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/index.js ***!
  \****************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.AsyncLocalStorageContextManager = exports.AsyncHooksContextManager = void 0;\nvar AsyncHooksContextManager_1 = __webpack_require__(/*! ./AsyncHooksContextManager */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AsyncHooksContextManager.js\");\nObject.defineProperty(exports, \"AsyncHooksContextManager\", ({ enumerable: true, get: function () { return AsyncHooksContextManager_1.AsyncHooksContextManager; } }));\nvar AsyncLocalStorageContextManager_1 = __webpack_require__(/*! ./AsyncLocalStorageContextManager */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AsyncLocalStorageContextManager.js\");\nObject.defineProperty(exports, \"AsyncLocalStorageContextManager\", ({ enumerable: true, get: function () { return AsyncLocalStorageContextManager_1.AsyncLocalStorageContextManager; } }));\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/index.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AbstractAsyncHooksContextManager.js":
/*!*******************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AbstractAsyncHooksContextManager.js ***!
  \*******************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.AbstractAsyncHooksContextManager = void 0;\nconst events_1 = __webpack_require__(/*! events */ \"events\");\nconst ADD_LISTENER_METHODS = [\n    'addListener',\n    'on',\n    'once',\n    'prependListener',\n    'prependOnceListener',\n];\nclass AbstractAsyncHooksContextManager {\n    constructor() {\n        this._kOtListeners = Symbol('OtListeners');\n        this._wrapped = false;\n    }\n    /**\n     * Binds a the certain context or the active one to the target function and then returns the target\n     * @param context A context (span) to be bind to target\n     * @param target a function or event emitter. When target or one of its callbacks is called,\n     *  the provided context will be used as the active context for the duration of the call.\n     */\n    bind(context, target) {\n        if (target instanceof events_1.EventEmitter) {\n            return this._bindEventEmitter(context, target);\n        }\n        if (typeof target === 'function') {\n            return this._bindFunction(context, target);\n        }\n        return target;\n    }\n    _bindFunction(context, target) {\n        const manager = this;\n        const contextWrapper = function (...args) {\n            return manager.with(context, () => target.apply(this, args));\n        };\n        Object.defineProperty(contextWrapper, 'length', {\n            enumerable: false,\n            configurable: true,\n            writable: false,\n            value: target.length,\n        });\n        /**\n         * It isn't possible to tell Typescript that contextWrapper is the same as T\n         * so we forced to cast as any here.\n         */\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        return contextWrapper;\n    }\n    /**\n     * By default, EventEmitter call their callback with their context, which we do\n     * not want, instead we will bind a specific context to all callbacks that\n     * go through it.\n     * @param context the context we want to bind\n     * @param ee EventEmitter an instance of EventEmitter to patch\n     */\n    _bindEventEmitter(context, ee) {\n        const map = this._getPatchMap(ee);\n        if (map !== undefined)\n            return ee;\n        this._createPatchMap(ee);\n        // patch methods that add a listener to propagate context\n        ADD_LISTENER_METHODS.forEach(methodName => {\n            if (ee[methodName] === undefined)\n                return;\n            ee[methodName] = this._patchAddListener(ee, ee[methodName], context);\n        });\n        // patch methods that remove a listener\n        if (typeof ee.removeListener === 'function') {\n            ee.removeListener = this._patchRemoveListener(ee, ee.removeListener);\n        }\n        if (typeof ee.off === 'function') {\n            ee.off = this._patchRemoveListener(ee, ee.off);\n        }\n        // patch method that remove all listeners\n        if (typeof ee.removeAllListeners === 'function') {\n            ee.removeAllListeners = this._patchRemoveAllListeners(ee, ee.removeAllListeners);\n        }\n        return ee;\n    }\n    /**\n     * Patch methods that remove a given listener so that we match the \"patched\"\n     * version of that listener (the one that propagate context).\n     * @param ee EventEmitter instance\n     * @param original reference to the patched method\n     */\n    _patchRemoveListener(ee, original) {\n        const contextManager = this;\n        return function (event, listener) {\n            var _a;\n            const events = (_a = contextManager._getPatchMap(ee)) === null || _a === void 0 ? void 0 : _a[event];\n            if (events === undefined) {\n                return original.call(this, event, listener);\n            }\n            const patchedListener = events.get(listener);\n            return original.call(this, event, patchedListener || listener);\n        };\n    }\n    /**\n     * Patch methods that remove all listeners so we remove our\n     * internal references for a given event.\n     * @param ee EventEmitter instance\n     * @param original reference to the patched method\n     */\n    _patchRemoveAllListeners(ee, original) {\n        const contextManager = this;\n        return function (event) {\n            const map = contextManager._getPatchMap(ee);\n            if (map !== undefined) {\n                if (arguments.length === 0) {\n                    contextManager._createPatchMap(ee);\n                }\n                else if (map[event] !== undefined) {\n                    delete map[event];\n                }\n            }\n            return original.apply(this, arguments);\n        };\n    }\n    /**\n     * Patch methods on an event emitter instance that can add listeners so we\n     * can force them to propagate a given context.\n     * @param ee EventEmitter instance\n     * @param original reference to the patched method\n     * @param [context] context to propagate when calling listeners\n     */\n    _patchAddListener(ee, original, context) {\n        const contextManager = this;\n        return function (event, listener) {\n            /**\n             * This check is required to prevent double-wrapping the listener.\n             * The implementation for ee.once wraps the listener and calls ee.on.\n             * Without this check, we would wrap that wrapped listener.\n             * This causes an issue because ee.removeListener depends on the onceWrapper\n             * to properly remove the listener. If we wrap their wrapper, we break\n             * that detection.\n             */\n            if (contextManager._wrapped) {\n                return original.call(this, event, listener);\n            }\n            let map = contextManager._getPatchMap(ee);\n            if (map === undefined) {\n                map = contextManager._createPatchMap(ee);\n            }\n            let listeners = map[event];\n            if (listeners === undefined) {\n                listeners = new WeakMap();\n                map[event] = listeners;\n            }\n            const patchedListener = contextManager.bind(context, listener);\n            // store a weak reference of the user listener to ours\n            listeners.set(listener, patchedListener);\n            /**\n             * See comment at the start of this function for the explanation of this property.\n             */\n            contextManager._wrapped = true;\n            try {\n                return original.call(this, event, patchedListener);\n            }\n            finally {\n                contextManager._wrapped = false;\n            }\n        };\n    }\n    _createPatchMap(ee) {\n        const map = Object.create(null);\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        ee[this._kOtListeners] = map;\n        return map;\n    }\n    _getPatchMap(ee) {\n        return ee[this._kOtListeners];\n    }\n}\nexports.AbstractAsyncHooksContextManager = AbstractAsyncHooksContextManager;\n//# sourceMappingURL=AbstractAsyncHooksContextManager.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AbstractAsyncHooksContextManager.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AsyncHooksContextManager.js":
/*!***********************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AsyncHooksContextManager.js ***!
  \***********************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.AsyncHooksContextManager = void 0;\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst asyncHooks = __webpack_require__(/*! async_hooks */ \"async_hooks\");\nconst AbstractAsyncHooksContextManager_1 = __webpack_require__(/*! ./AbstractAsyncHooksContextManager */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AbstractAsyncHooksContextManager.js\");\nclass AsyncHooksContextManager extends AbstractAsyncHooksContextManager_1.AbstractAsyncHooksContextManager {\n    constructor() {\n        super();\n        this._contexts = new Map();\n        this._stack = [];\n        this._asyncHook = asyncHooks.createHook({\n            init: this._init.bind(this),\n            before: this._before.bind(this),\n            after: this._after.bind(this),\n            destroy: this._destroy.bind(this),\n            promiseResolve: this._destroy.bind(this),\n        });\n    }\n    active() {\n        var _a;\n        return (_a = this._stack[this._stack.length - 1]) !== null && _a !== void 0 ? _a : api_1.ROOT_CONTEXT;\n    }\n    with(context, fn, thisArg, ...args) {\n        this._enterContext(context);\n        try {\n            return fn.call(thisArg, ...args);\n        }\n        finally {\n            this._exitContext();\n        }\n    }\n    enable() {\n        this._asyncHook.enable();\n        return this;\n    }\n    disable() {\n        this._asyncHook.disable();\n        this._contexts.clear();\n        this._stack = [];\n        return this;\n    }\n    /**\n     * Init hook will be called when userland create a async context, setting the\n     * context as the current one if it exist.\n     * @param uid id of the async context\n     * @param type the resource type\n     */\n    _init(uid, type) {\n        // ignore TIMERWRAP as they combine timers with same timeout which can lead to\n        // false context propagation. TIMERWRAP has been removed in node 11\n        // every timer has it's own `Timeout` resource anyway which is used to propagate\n        // context.\n        if (type === 'TIMERWRAP')\n            return;\n        const context = this._stack[this._stack.length - 1];\n        if (context !== undefined) {\n            this._contexts.set(uid, context);\n        }\n    }\n    /**\n     * Destroy hook will be called when a given context is no longer used so we can\n     * remove its attached context.\n     * @param uid uid of the async context\n     */\n    _destroy(uid) {\n        this._contexts.delete(uid);\n    }\n    /**\n     * Before hook is called just before executing a async context.\n     * @param uid uid of the async context\n     */\n    _before(uid) {\n        const context = this._contexts.get(uid);\n        if (context !== undefined) {\n            this._enterContext(context);\n        }\n    }\n    /**\n     * After hook is called just after completing the execution of a async context.\n     */\n    _after() {\n        this._exitContext();\n    }\n    /**\n     * Set the given context as active\n     */\n    _enterContext(context) {\n        this._stack.push(context);\n    }\n    /**\n     * Remove the context at the root of the stack\n     */\n    _exitContext() {\n        this._stack.pop();\n    }\n}\nexports.AsyncHooksContextManager = AsyncHooksContextManager;\n//# sourceMappingURL=AsyncHooksContextManager.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AsyncHooksContextManager.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AsyncLocalStorageContextManager.js":
/*!******************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AsyncLocalStorageContextManager.js ***!
  \******************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.AsyncLocalStorageContextManager = void 0;\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst async_hooks_1 = __webpack_require__(/*! async_hooks */ \"async_hooks\");\nconst AbstractAsyncHooksContextManager_1 = __webpack_require__(/*! ./AbstractAsyncHooksContextManager */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AbstractAsyncHooksContextManager.js\");\nclass AsyncLocalStorageContextManager extends AbstractAsyncHooksContextManager_1.AbstractAsyncHooksContextManager {\n    constructor() {\n        super();\n        this._asyncLocalStorage = new async_hooks_1.AsyncLocalStorage();\n    }\n    active() {\n        var _a;\n        return (_a = this._asyncLocalStorage.getStore()) !== null && _a !== void 0 ? _a : api_1.ROOT_CONTEXT;\n    }\n    with(context, fn, thisArg, ...args) {\n        const cb = thisArg == null ? fn : fn.bind(thisArg);\n        return this._asyncLocalStorage.run(context, cb, ...args);\n    }\n    enable() {\n        return this;\n    }\n    disable() {\n        this._asyncLocalStorage.disable();\n        return this;\n    }\n}\nexports.AsyncLocalStorageContextManager = AsyncLocalStorageContextManager;\n//# sourceMappingURL=AsyncLocalStorageContextManager.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AsyncLocalStorageContextManager.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/index.js":
/*!****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/index.js ***!
  \****************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.AsyncLocalStorageContextManager = exports.AsyncHooksContextManager = void 0;\nvar AsyncHooksContextManager_1 = __webpack_require__(/*! ./AsyncHooksContextManager */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AsyncHooksContextManager.js\");\nObject.defineProperty(exports, \"AsyncHooksContextManager\", ({ enumerable: true, get: function () { return AsyncHooksContextManager_1.AsyncHooksContextManager; } }));\nvar AsyncLocalStorageContextManager_1 = __webpack_require__(/*! ./AsyncLocalStorageContextManager */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AsyncLocalStorageContextManager.js\");\nObject.defineProperty(exports, \"AsyncLocalStorageContextManager\", ({ enumerable: true, get: function () { return AsyncLocalStorageContextManager_1.AsyncLocalStorageContextManager; } }));\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AbstractAsyncHooksContextManager.js":
/*!*******************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AbstractAsyncHooksContextManager.js ***!
  \*******************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.AbstractAsyncHooksContextManager = void 0;\nconst events_1 = __webpack_require__(/*! events */ \"events\");\nconst ADD_LISTENER_METHODS = [\n    'addListener',\n    'on',\n    'once',\n    'prependListener',\n    'prependOnceListener',\n];\nclass AbstractAsyncHooksContextManager {\n    constructor() {\n        this._kOtListeners = Symbol('OtListeners');\n        this._wrapped = false;\n    }\n    /**\n     * Binds a the certain context or the active one to the target function and then returns the target\n     * @param context A context (span) to be bind to target\n     * @param target a function or event emitter. When target or one of its callbacks is called,\n     *  the provided context will be used as the active context for the duration of the call.\n     */\n    bind(context, target) {\n        if (target instanceof events_1.EventEmitter) {\n            return this._bindEventEmitter(context, target);\n        }\n        if (typeof target === 'function') {\n            return this._bindFunction(context, target);\n        }\n        return target;\n    }\n    _bindFunction(context, target) {\n        const manager = this;\n        const contextWrapper = function (...args) {\n            return manager.with(context, () => target.apply(this, args));\n        };\n        Object.defineProperty(contextWrapper, 'length', {\n            enumerable: false,\n            configurable: true,\n            writable: false,\n            value: target.length,\n        });\n        /**\n         * It isn't possible to tell Typescript that contextWrapper is the same as T\n         * so we forced to cast as any here.\n         */\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        return contextWrapper;\n    }\n    /**\n     * By default, EventEmitter call their callback with their context, which we do\n     * not want, instead we will bind a specific context to all callbacks that\n     * go through it.\n     * @param context the context we want to bind\n     * @param ee EventEmitter an instance of EventEmitter to patch\n     */\n    _bindEventEmitter(context, ee) {\n        const map = this._getPatchMap(ee);\n        if (map !== undefined)\n            return ee;\n        this._createPatchMap(ee);\n        // patch methods that add a listener to propagate context\n        ADD_LISTENER_METHODS.forEach(methodName => {\n            if (ee[methodName] === undefined)\n                return;\n            ee[methodName] = this._patchAddListener(ee, ee[methodName], context);\n        });\n        // patch methods that remove a listener\n        if (typeof ee.removeListener === 'function') {\n            ee.removeListener = this._patchRemoveListener(ee, ee.removeListener);\n        }\n        if (typeof ee.off === 'function') {\n            ee.off = this._patchRemoveListener(ee, ee.off);\n        }\n        // patch method that remove all listeners\n        if (typeof ee.removeAllListeners === 'function') {\n            ee.removeAllListeners = this._patchRemoveAllListeners(ee, ee.removeAllListeners);\n        }\n        return ee;\n    }\n    /**\n     * Patch methods that remove a given listener so that we match the \"patched\"\n     * version of that listener (the one that propagate context).\n     * @param ee EventEmitter instance\n     * @param original reference to the patched method\n     */\n    _patchRemoveListener(ee, original) {\n        const contextManager = this;\n        return function (event, listener) {\n            var _a;\n            const events = (_a = contextManager._getPatchMap(ee)) === null || _a === void 0 ? void 0 : _a[event];\n            if (events === undefined) {\n                return original.call(this, event, listener);\n            }\n            const patchedListener = events.get(listener);\n            return original.call(this, event, patchedListener || listener);\n        };\n    }\n    /**\n     * Patch methods that remove all listeners so we remove our\n     * internal references for a given event.\n     * @param ee EventEmitter instance\n     * @param original reference to the patched method\n     */\n    _patchRemoveAllListeners(ee, original) {\n        const contextManager = this;\n        return function (event) {\n            const map = contextManager._getPatchMap(ee);\n            if (map !== undefined) {\n                if (arguments.length === 0) {\n                    contextManager._createPatchMap(ee);\n                }\n                else if (map[event] !== undefined) {\n                    delete map[event];\n                }\n            }\n            return original.apply(this, arguments);\n        };\n    }\n    /**\n     * Patch methods on an event emitter instance that can add listeners so we\n     * can force them to propagate a given context.\n     * @param ee EventEmitter instance\n     * @param original reference to the patched method\n     * @param [context] context to propagate when calling listeners\n     */\n    _patchAddListener(ee, original, context) {\n        const contextManager = this;\n        return function (event, listener) {\n            /**\n             * This check is required to prevent double-wrapping the listener.\n             * The implementation for ee.once wraps the listener and calls ee.on.\n             * Without this check, we would wrap that wrapped listener.\n             * This causes an issue because ee.removeListener depends on the onceWrapper\n             * to properly remove the listener. If we wrap their wrapper, we break\n             * that detection.\n             */\n            if (contextManager._wrapped) {\n                return original.call(this, event, listener);\n            }\n            let map = contextManager._getPatchMap(ee);\n            if (map === undefined) {\n                map = contextManager._createPatchMap(ee);\n            }\n            let listeners = map[event];\n            if (listeners === undefined) {\n                listeners = new WeakMap();\n                map[event] = listeners;\n            }\n            const patchedListener = contextManager.bind(context, listener);\n            // store a weak reference of the user listener to ours\n            listeners.set(listener, patchedListener);\n            /**\n             * See comment at the start of this function for the explanation of this property.\n             */\n            contextManager._wrapped = true;\n            try {\n                return original.call(this, event, patchedListener);\n            }\n            finally {\n                contextManager._wrapped = false;\n            }\n        };\n    }\n    _createPatchMap(ee) {\n        const map = Object.create(null);\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        ee[this._kOtListeners] = map;\n        return map;\n    }\n    _getPatchMap(ee) {\n        return ee[this._kOtListeners];\n    }\n}\nexports.AbstractAsyncHooksContextManager = AbstractAsyncHooksContextManager;\n//# sourceMappingURL=AbstractAsyncHooksContextManager.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AbstractAsyncHooksContextManager.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AsyncHooksContextManager.js":
/*!***********************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AsyncHooksContextManager.js ***!
  \***********************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.AsyncHooksContextManager = void 0;\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst asyncHooks = __webpack_require__(/*! async_hooks */ \"async_hooks\");\nconst AbstractAsyncHooksContextManager_1 = __webpack_require__(/*! ./AbstractAsyncHooksContextManager */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AbstractAsyncHooksContextManager.js\");\nclass AsyncHooksContextManager extends AbstractAsyncHooksContextManager_1.AbstractAsyncHooksContextManager {\n    constructor() {\n        super();\n        this._contexts = new Map();\n        this._stack = [];\n        this._asyncHook = asyncHooks.createHook({\n            init: this._init.bind(this),\n            before: this._before.bind(this),\n            after: this._after.bind(this),\n            destroy: this._destroy.bind(this),\n            promiseResolve: this._destroy.bind(this),\n        });\n    }\n    active() {\n        var _a;\n        return (_a = this._stack[this._stack.length - 1]) !== null && _a !== void 0 ? _a : api_1.ROOT_CONTEXT;\n    }\n    with(context, fn, thisArg, ...args) {\n        this._enterContext(context);\n        try {\n            return fn.call(thisArg, ...args);\n        }\n        finally {\n            this._exitContext();\n        }\n    }\n    enable() {\n        this._asyncHook.enable();\n        return this;\n    }\n    disable() {\n        this._asyncHook.disable();\n        this._contexts.clear();\n        this._stack = [];\n        return this;\n    }\n    /**\n     * Init hook will be called when userland create a async context, setting the\n     * context as the current one if it exist.\n     * @param uid id of the async context\n     * @param type the resource type\n     */\n    _init(uid, type) {\n        // ignore TIMERWRAP as they combine timers with same timeout which can lead to\n        // false context propagation. TIMERWRAP has been removed in node 11\n        // every timer has it's own `Timeout` resource anyway which is used to propagate\n        // context.\n        if (type === 'TIMERWRAP')\n            return;\n        const context = this._stack[this._stack.length - 1];\n        if (context !== undefined) {\n            this._contexts.set(uid, context);\n        }\n    }\n    /**\n     * Destroy hook will be called when a given context is no longer used so we can\n     * remove its attached context.\n     * @param uid uid of the async context\n     */\n    _destroy(uid) {\n        this._contexts.delete(uid);\n    }\n    /**\n     * Before hook is called just before executing a async context.\n     * @param uid uid of the async context\n     */\n    _before(uid) {\n        const context = this._contexts.get(uid);\n        if (context !== undefined) {\n            this._enterContext(context);\n        }\n    }\n    /**\n     * After hook is called just after completing the execution of a async context.\n     */\n    _after() {\n        this._exitContext();\n    }\n    /**\n     * Set the given context as active\n     */\n    _enterContext(context) {\n        this._stack.push(context);\n    }\n    /**\n     * Remove the context at the root of the stack\n     */\n    _exitContext() {\n        this._stack.pop();\n    }\n}\nexports.AsyncHooksContextManager = AsyncHooksContextManager;\n//# sourceMappingURL=AsyncHooksContextManager.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AsyncHooksContextManager.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AsyncLocalStorageContextManager.js":
/*!******************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AsyncLocalStorageContextManager.js ***!
  \******************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.AsyncLocalStorageContextManager = void 0;\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst async_hooks_1 = __webpack_require__(/*! async_hooks */ \"async_hooks\");\nconst AbstractAsyncHooksContextManager_1 = __webpack_require__(/*! ./AbstractAsyncHooksContextManager */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AbstractAsyncHooksContextManager.js\");\nclass AsyncLocalStorageContextManager extends AbstractAsyncHooksContextManager_1.AbstractAsyncHooksContextManager {\n    constructor() {\n        super();\n        this._asyncLocalStorage = new async_hooks_1.AsyncLocalStorage();\n    }\n    active() {\n        var _a;\n        return (_a = this._asyncLocalStorage.getStore()) !== null && _a !== void 0 ? _a : api_1.ROOT_CONTEXT;\n    }\n    with(context, fn, thisArg, ...args) {\n        const cb = thisArg == null ? fn : fn.bind(thisArg);\n        return this._asyncLocalStorage.run(context, cb, ...args);\n    }\n    enable() {\n        return this;\n    }\n    disable() {\n        this._asyncLocalStorage.disable();\n        return this;\n    }\n}\nexports.AsyncLocalStorageContextManager = AsyncLocalStorageContextManager;\n//# sourceMappingURL=AsyncLocalStorageContextManager.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AsyncLocalStorageContextManager.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/index.js":
/*!****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/index.js ***!
  \****************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.AsyncLocalStorageContextManager = exports.AsyncHooksContextManager = void 0;\nvar AsyncHooksContextManager_1 = __webpack_require__(/*! ./AsyncHooksContextManager */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AsyncHooksContextManager.js\");\nObject.defineProperty(exports, \"AsyncHooksContextManager\", ({ enumerable: true, get: function () { return AsyncHooksContextManager_1.AsyncHooksContextManager; } }));\nvar AsyncLocalStorageContextManager_1 = __webpack_require__(/*! ./AsyncLocalStorageContextManager */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AsyncLocalStorageContextManager.js\");\nObject.defineProperty(exports, \"AsyncLocalStorageContextManager\", ({ enumerable: true, get: function () { return AsyncLocalStorageContextManager_1.AsyncLocalStorageContextManager; } }));\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/index.js\n");

/***/ })

};
;