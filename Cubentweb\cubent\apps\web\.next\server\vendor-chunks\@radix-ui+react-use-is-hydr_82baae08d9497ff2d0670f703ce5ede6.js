"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-use-is-hydr_82baae08d9497ff2d0670f703ce5ede6";
exports.ids = ["vendor-chunks/@radix-ui+react-use-is-hydr_82baae08d9497ff2d0670f703ce5ede6"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/@radix-ui+react-use-is-hydr_82baae08d9497ff2d0670f703ce5ede6/node_modules/@radix-ui/react-use-is-hydrated/dist/index.mjs":
/*!*********************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@radix-ui+react-use-is-hydr_82baae08d9497ff2d0670f703ce5ede6/node_modules/@radix-ui/react-use-is-hydrated/dist/index.mjs ***!
  \*********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useIsHydrated: () => (/* binding */ useIsHydrated)\n/* harmony export */ });\n/* harmony import */ var use_sync_external_store_shim__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! use-sync-external-store/shim */ \"(ssr)/../../node_modules/.pnpm/use-sync-external-store@1.5.0_react@19.1.0/node_modules/use-sync-external-store/shim/index.js\");\n// src/use-is-hydrated.tsx\n\nfunction useIsHydrated() {\n  return (0,use_sync_external_store_shim__WEBPACK_IMPORTED_MODULE_0__.useSyncExternalStore)(\n    subscribe,\n    () => true,\n    () => false\n  );\n}\nfunction subscribe() {\n  return () => {\n  };\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0ByYWRpeC11aStyZWFjdC11c2UtaXMtaHlkcl84MmJhYWUwOGQ5NDk3ZmYyZDA2NzBmNzAzY2U1ZWRlNi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1pcy1oeWRyYXRlZC9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ29FO0FBQ3BFO0FBQ0EsU0FBUyxrRkFBb0I7QUFDN0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBR0U7QUFDRiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbndhclxcRG9jdW1lbnRzXFxDdWJlbnR3ZWJcXGN1YmVudFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQHJhZGl4LXVpK3JlYWN0LXVzZS1pcy1oeWRyXzgyYmFhZTA4ZDk0OTdmZjJkMDY3MGY3MDNjZTVlZGU2XFxub2RlX21vZHVsZXNcXEByYWRpeC11aVxccmVhY3QtdXNlLWlzLWh5ZHJhdGVkXFxkaXN0XFxpbmRleC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL3VzZS1pcy1oeWRyYXRlZC50c3hcbmltcG9ydCB7IHVzZVN5bmNFeHRlcm5hbFN0b3JlIH0gZnJvbSBcInVzZS1zeW5jLWV4dGVybmFsLXN0b3JlL3NoaW1cIjtcbmZ1bmN0aW9uIHVzZUlzSHlkcmF0ZWQoKSB7XG4gIHJldHVybiB1c2VTeW5jRXh0ZXJuYWxTdG9yZShcbiAgICBzdWJzY3JpYmUsXG4gICAgKCkgPT4gdHJ1ZSxcbiAgICAoKSA9PiBmYWxzZVxuICApO1xufVxuZnVuY3Rpb24gc3Vic2NyaWJlKCkge1xuICByZXR1cm4gKCkgPT4ge1xuICB9O1xufVxuZXhwb3J0IHtcbiAgdXNlSXNIeWRyYXRlZFxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4Lm1qcy5tYXBcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@radix-ui+react-use-is-hydr_82baae08d9497ff2d0670f703ce5ede6/node_modules/@radix-ui/react-use-is-hydrated/dist/index.mjs\n");

/***/ })

};
;