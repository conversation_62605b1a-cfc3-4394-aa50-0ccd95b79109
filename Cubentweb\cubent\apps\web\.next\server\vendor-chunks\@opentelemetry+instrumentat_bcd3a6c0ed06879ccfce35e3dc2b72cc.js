"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc";
exports.ids = ["vendor-chunks/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc"];
exports.modules = {

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/index.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/index.js ***!
  \********************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/instrumentation.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/types.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/index.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/instrumentation.js":
/*!******************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/instrumentation.js ***!
  \******************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.RedisInstrumentation = void 0;\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/utils.js\");\nconst redis_common_1 = __webpack_require__(/*! @opentelemetry/redis-common */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+redis-common@0.36.2/node_modules/@opentelemetry/redis-common/build/src/index.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/version.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst OTEL_OPEN_SPANS = Symbol('opentelemetry.instrumentation.redis.open_spans');\nconst MULTI_COMMAND_OPTIONS = Symbol('opentelemetry.instrumentation.redis.multi_command_options');\nconst DEFAULT_CONFIG = {\n    requireParentSpan: false,\n};\nclass RedisInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, Object.assign(Object.assign({}, DEFAULT_CONFIG), config));\n    }\n    setConfig(config = {}) {\n        super.setConfig(Object.assign(Object.assign({}, DEFAULT_CONFIG), config));\n    }\n    init() {\n        // @node-redis/client is a new package introduced and consumed by 'redis 4.0.x'\n        // on redis@4.1.0 it was changed to @redis/client.\n        // we will instrument both packages\n        return [\n            this._getInstrumentationNodeModuleDefinition('@redis/client'),\n            this._getInstrumentationNodeModuleDefinition('@node-redis/client'),\n        ];\n    }\n    _getInstrumentationNodeModuleDefinition(basePackageName) {\n        const commanderModuleFile = new instrumentation_1.InstrumentationNodeModuleFile(`${basePackageName}/dist/lib/commander.js`, ['^1.0.0'], (moduleExports, moduleVersion) => {\n            const transformCommandArguments = moduleExports.transformCommandArguments;\n            if (!transformCommandArguments) {\n                this._diag.error('internal instrumentation error, missing transformCommandArguments function');\n                return moduleExports;\n            }\n            // function name and signature changed in redis 4.1.0 from 'extendWithCommands' to 'attachCommands'\n            // the matching internal package names starts with 1.0.x (for redis 4.0.x)\n            const functionToPatch = (moduleVersion === null || moduleVersion === void 0 ? void 0 : moduleVersion.startsWith('1.0.'))\n                ? 'extendWithCommands'\n                : 'attachCommands';\n            // this is the function that extend a redis client with a list of commands.\n            // the function patches the commandExecutor to record a span\n            if ((0, instrumentation_1.isWrapped)(moduleExports === null || moduleExports === void 0 ? void 0 : moduleExports[functionToPatch])) {\n                this._unwrap(moduleExports, functionToPatch);\n            }\n            this._wrap(moduleExports, functionToPatch, this._getPatchExtendWithCommands(transformCommandArguments));\n            return moduleExports;\n        }, (moduleExports) => {\n            if ((0, instrumentation_1.isWrapped)(moduleExports === null || moduleExports === void 0 ? void 0 : moduleExports.extendWithCommands)) {\n                this._unwrap(moduleExports, 'extendWithCommands');\n            }\n            if ((0, instrumentation_1.isWrapped)(moduleExports === null || moduleExports === void 0 ? void 0 : moduleExports.attachCommands)) {\n                this._unwrap(moduleExports, 'attachCommands');\n            }\n        });\n        const multiCommanderModule = new instrumentation_1.InstrumentationNodeModuleFile(`${basePackageName}/dist/lib/client/multi-command.js`, ['^1.0.0'], (moduleExports) => {\n            var _a;\n            const redisClientMultiCommandPrototype = (_a = moduleExports === null || moduleExports === void 0 ? void 0 : moduleExports.default) === null || _a === void 0 ? void 0 : _a.prototype;\n            if ((0, instrumentation_1.isWrapped)(redisClientMultiCommandPrototype === null || redisClientMultiCommandPrototype === void 0 ? void 0 : redisClientMultiCommandPrototype.exec)) {\n                this._unwrap(redisClientMultiCommandPrototype, 'exec');\n            }\n            this._wrap(redisClientMultiCommandPrototype, 'exec', this._getPatchMultiCommandsExec());\n            if ((0, instrumentation_1.isWrapped)(redisClientMultiCommandPrototype === null || redisClientMultiCommandPrototype === void 0 ? void 0 : redisClientMultiCommandPrototype.addCommand)) {\n                this._unwrap(redisClientMultiCommandPrototype, 'addCommand');\n            }\n            this._wrap(redisClientMultiCommandPrototype, 'addCommand', this._getPatchMultiCommandsAddCommand());\n            return moduleExports;\n        }, (moduleExports) => {\n            var _a;\n            const redisClientMultiCommandPrototype = (_a = moduleExports === null || moduleExports === void 0 ? void 0 : moduleExports.default) === null || _a === void 0 ? void 0 : _a.prototype;\n            if ((0, instrumentation_1.isWrapped)(redisClientMultiCommandPrototype === null || redisClientMultiCommandPrototype === void 0 ? void 0 : redisClientMultiCommandPrototype.exec)) {\n                this._unwrap(redisClientMultiCommandPrototype, 'exec');\n            }\n            if ((0, instrumentation_1.isWrapped)(redisClientMultiCommandPrototype === null || redisClientMultiCommandPrototype === void 0 ? void 0 : redisClientMultiCommandPrototype.addCommand)) {\n                this._unwrap(redisClientMultiCommandPrototype, 'addCommand');\n            }\n        });\n        const clientIndexModule = new instrumentation_1.InstrumentationNodeModuleFile(`${basePackageName}/dist/lib/client/index.js`, ['^1.0.0'], (moduleExports) => {\n            var _a;\n            const redisClientPrototype = (_a = moduleExports === null || moduleExports === void 0 ? void 0 : moduleExports.default) === null || _a === void 0 ? void 0 : _a.prototype;\n            // In some @redis/client versions 'multi' is a method. In later\n            // versions, as of https://github.com/redis/node-redis/pull/2324,\n            // 'MULTI' is a method and 'multi' is a property defined in the\n            // constructor that points to 'MULTI', and therefore it will not\n            // be defined on the prototype.\n            if (redisClientPrototype === null || redisClientPrototype === void 0 ? void 0 : redisClientPrototype.multi) {\n                if ((0, instrumentation_1.isWrapped)(redisClientPrototype === null || redisClientPrototype === void 0 ? void 0 : redisClientPrototype.multi)) {\n                    this._unwrap(redisClientPrototype, 'multi');\n                }\n                this._wrap(redisClientPrototype, 'multi', this._getPatchRedisClientMulti());\n            }\n            if (redisClientPrototype === null || redisClientPrototype === void 0 ? void 0 : redisClientPrototype.MULTI) {\n                if ((0, instrumentation_1.isWrapped)(redisClientPrototype === null || redisClientPrototype === void 0 ? void 0 : redisClientPrototype.MULTI)) {\n                    this._unwrap(redisClientPrototype, 'MULTI');\n                }\n                this._wrap(redisClientPrototype, 'MULTI', this._getPatchRedisClientMulti());\n            }\n            if ((0, instrumentation_1.isWrapped)(redisClientPrototype === null || redisClientPrototype === void 0 ? void 0 : redisClientPrototype.sendCommand)) {\n                this._unwrap(redisClientPrototype, 'sendCommand');\n            }\n            this._wrap(redisClientPrototype, 'sendCommand', this._getPatchRedisClientSendCommand());\n            this._wrap(redisClientPrototype, 'connect', this._getPatchedClientConnect());\n            return moduleExports;\n        }, (moduleExports) => {\n            var _a;\n            const redisClientPrototype = (_a = moduleExports === null || moduleExports === void 0 ? void 0 : moduleExports.default) === null || _a === void 0 ? void 0 : _a.prototype;\n            if ((0, instrumentation_1.isWrapped)(redisClientPrototype === null || redisClientPrototype === void 0 ? void 0 : redisClientPrototype.multi)) {\n                this._unwrap(redisClientPrototype, 'multi');\n            }\n            if ((0, instrumentation_1.isWrapped)(redisClientPrototype === null || redisClientPrototype === void 0 ? void 0 : redisClientPrototype.MULTI)) {\n                this._unwrap(redisClientPrototype, 'MULTI');\n            }\n            if ((0, instrumentation_1.isWrapped)(redisClientPrototype === null || redisClientPrototype === void 0 ? void 0 : redisClientPrototype.sendCommand)) {\n                this._unwrap(redisClientPrototype, 'sendCommand');\n            }\n        });\n        return new instrumentation_1.InstrumentationNodeModuleDefinition(basePackageName, ['^1.0.0'], (moduleExports) => {\n            return moduleExports;\n        }, () => { }, [commanderModuleFile, multiCommanderModule, clientIndexModule]);\n    }\n    // serves both for redis 4.0.x where function name is extendWithCommands\n    // and redis ^4.1.0 where function name is attachCommands\n    _getPatchExtendWithCommands(transformCommandArguments) {\n        const plugin = this;\n        return function extendWithCommandsPatchWrapper(original) {\n            return function extendWithCommandsPatch(config) {\n                var _a;\n                if (((_a = config === null || config === void 0 ? void 0 : config.BaseClass) === null || _a === void 0 ? void 0 : _a.name) !== 'RedisClient') {\n                    return original.apply(this, arguments);\n                }\n                const origExecutor = config.executor;\n                config.executor = function (command, args) {\n                    const redisCommandArguments = transformCommandArguments(command, args).args;\n                    return plugin._traceClientCommand(origExecutor, this, arguments, redisCommandArguments);\n                };\n                return original.apply(this, arguments);\n            };\n        };\n    }\n    _getPatchMultiCommandsExec() {\n        const plugin = this;\n        return function execPatchWrapper(original) {\n            return function execPatch() {\n                const execRes = original.apply(this, arguments);\n                if (typeof (execRes === null || execRes === void 0 ? void 0 : execRes.then) !== 'function') {\n                    plugin._diag.error('got non promise result when patching RedisClientMultiCommand.exec');\n                    return execRes;\n                }\n                return execRes\n                    .then((redisRes) => {\n                    const openSpans = this[OTEL_OPEN_SPANS];\n                    plugin._endSpansWithRedisReplies(openSpans, redisRes);\n                    return redisRes;\n                })\n                    .catch((err) => {\n                    const openSpans = this[OTEL_OPEN_SPANS];\n                    if (!openSpans) {\n                        plugin._diag.error('cannot find open spans to end for redis multi command');\n                    }\n                    else {\n                        const replies = err.constructor.name === 'MultiErrorReply'\n                            ? err.replies\n                            : new Array(openSpans.length).fill(err);\n                        plugin._endSpansWithRedisReplies(openSpans, replies);\n                    }\n                    return Promise.reject(err);\n                });\n            };\n        };\n    }\n    _getPatchMultiCommandsAddCommand() {\n        const plugin = this;\n        return function addCommandWrapper(original) {\n            return function addCommandPatch(args) {\n                return plugin._traceClientCommand(original, this, arguments, args);\n            };\n        };\n    }\n    _getPatchRedisClientMulti() {\n        return function multiPatchWrapper(original) {\n            return function multiPatch() {\n                const multiRes = original.apply(this, arguments);\n                multiRes[MULTI_COMMAND_OPTIONS] = this.options;\n                return multiRes;\n            };\n        };\n    }\n    _getPatchRedisClientSendCommand() {\n        const plugin = this;\n        return function sendCommandWrapper(original) {\n            return function sendCommandPatch(args) {\n                return plugin._traceClientCommand(original, this, arguments, args);\n            };\n        };\n    }\n    _getPatchedClientConnect() {\n        const plugin = this;\n        return function connectWrapper(original) {\n            return function patchedConnect() {\n                const options = this.options;\n                const attributes = (0, utils_1.getClientAttributes)(plugin._diag, options);\n                const span = plugin.tracer.startSpan(`${RedisInstrumentation.COMPONENT}-connect`, {\n                    kind: api_1.SpanKind.CLIENT,\n                    attributes,\n                });\n                const res = api_1.context.with(api_1.trace.setSpan(api_1.context.active(), span), () => {\n                    return original.apply(this);\n                });\n                return res\n                    .then((result) => {\n                    span.end();\n                    return result;\n                })\n                    .catch((error) => {\n                    span.recordException(error);\n                    span.setStatus({\n                        code: api_1.SpanStatusCode.ERROR,\n                        message: error.message,\n                    });\n                    span.end();\n                    return Promise.reject(error);\n                });\n            };\n        };\n    }\n    _traceClientCommand(origFunction, origThis, origArguments, redisCommandArguments) {\n        const hasNoParentSpan = api_1.trace.getSpan(api_1.context.active()) === undefined;\n        if (hasNoParentSpan && this.getConfig().requireParentSpan) {\n            return origFunction.apply(origThis, origArguments);\n        }\n        const clientOptions = origThis.options || origThis[MULTI_COMMAND_OPTIONS];\n        const commandName = redisCommandArguments[0]; // types also allows it to be a Buffer, but in practice it only string\n        const commandArgs = redisCommandArguments.slice(1);\n        const dbStatementSerializer = this.getConfig().dbStatementSerializer || redis_common_1.defaultDbStatementSerializer;\n        const attributes = (0, utils_1.getClientAttributes)(this._diag, clientOptions);\n        try {\n            const dbStatement = dbStatementSerializer(commandName, commandArgs);\n            if (dbStatement != null) {\n                attributes[semantic_conventions_1.SEMATTRS_DB_STATEMENT] = dbStatement;\n            }\n        }\n        catch (e) {\n            this._diag.error('dbStatementSerializer throw an exception', e, {\n                commandName,\n            });\n        }\n        const span = this.tracer.startSpan(`${RedisInstrumentation.COMPONENT}-${commandName}`, {\n            kind: api_1.SpanKind.CLIENT,\n            attributes,\n        });\n        const res = api_1.context.with(api_1.trace.setSpan(api_1.context.active(), span), () => {\n            return origFunction.apply(origThis, origArguments);\n        });\n        if (typeof (res === null || res === void 0 ? void 0 : res.then) === 'function') {\n            res.then((redisRes) => {\n                this._endSpanWithResponse(span, commandName, commandArgs, redisRes, undefined);\n            }, (err) => {\n                this._endSpanWithResponse(span, commandName, commandArgs, null, err);\n            });\n        }\n        else {\n            const redisClientMultiCommand = res;\n            redisClientMultiCommand[OTEL_OPEN_SPANS] =\n                redisClientMultiCommand[OTEL_OPEN_SPANS] || [];\n            redisClientMultiCommand[OTEL_OPEN_SPANS].push({\n                span,\n                commandName,\n                commandArgs,\n            });\n        }\n        return res;\n    }\n    _endSpansWithRedisReplies(openSpans, replies) {\n        if (!openSpans) {\n            return this._diag.error('cannot find open spans to end for redis multi command');\n        }\n        if (replies.length !== openSpans.length) {\n            return this._diag.error('number of multi command spans does not match response from redis');\n        }\n        for (let i = 0; i < openSpans.length; i++) {\n            const { span, commandName, commandArgs } = openSpans[i];\n            const currCommandRes = replies[i];\n            const [res, err] = currCommandRes instanceof Error\n                ? [null, currCommandRes]\n                : [currCommandRes, undefined];\n            this._endSpanWithResponse(span, commandName, commandArgs, res, err);\n        }\n    }\n    _endSpanWithResponse(span, commandName, commandArgs, response, error) {\n        const { responseHook } = this.getConfig();\n        if (!error && responseHook) {\n            try {\n                responseHook(span, commandName, commandArgs, response);\n            }\n            catch (err) {\n                this._diag.error('responseHook throw an exception', err);\n            }\n        }\n        if (error) {\n            span.recordException(error);\n            span.setStatus({ code: api_1.SpanStatusCode.ERROR, message: error === null || error === void 0 ? void 0 : error.message });\n        }\n        span.end();\n    }\n}\nexports.RedisInstrumentation = RedisInstrumentation;\nRedisInstrumentation.COMPONENT = 'redis';\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/instrumentation.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/types.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/types.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9Ab3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfYmNkM2E2YzBlZDA2ODc5Y2NmY2UzNWUzZGMyYjcyY2Mvbm9kZV9tb2R1bGVzL0BvcGVudGVsZW1ldHJ5L2luc3RydW1lbnRhdGlvbi1yZWRpcy00L2J1aWxkL3NyYy90eXBlcy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbndhclxcRG9jdW1lbnRzXFxDdWJlbnR3ZWJcXGN1YmVudFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQG9wZW50ZWxlbWV0cnkraW5zdHJ1bWVudGF0X2JjZDNhNmMwZWQwNjg3OWNjZmNlMzVlM2RjMmI3MmNjXFxub2RlX21vZHVsZXNcXEBvcGVudGVsZW1ldHJ5XFxpbnN0cnVtZW50YXRpb24tcmVkaXMtNFxcYnVpbGRcXHNyY1xcdHlwZXMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG4vKlxuICogQ29weXJpZ2h0IFRoZSBPcGVuVGVsZW1ldHJ5IEF1dGhvcnNcbiAqXG4gKiBMaWNlbnNlZCB1bmRlciB0aGUgQXBhY2hlIExpY2Vuc2UsIFZlcnNpb24gMi4wICh0aGUgXCJMaWNlbnNlXCIpO1xuICogeW91IG1heSBub3QgdXNlIHRoaXMgZmlsZSBleGNlcHQgaW4gY29tcGxpYW5jZSB3aXRoIHRoZSBMaWNlbnNlLlxuICogWW91IG1heSBvYnRhaW4gYSBjb3B5IG9mIHRoZSBMaWNlbnNlIGF0XG4gKlxuICogICAgICBodHRwczovL3d3dy5hcGFjaGUub3JnL2xpY2Vuc2VzL0xJQ0VOU0UtMi4wXG4gKlxuICogVW5sZXNzIHJlcXVpcmVkIGJ5IGFwcGxpY2FibGUgbGF3IG9yIGFncmVlZCB0byBpbiB3cml0aW5nLCBzb2Z0d2FyZVxuICogZGlzdHJpYnV0ZWQgdW5kZXIgdGhlIExpY2Vuc2UgaXMgZGlzdHJpYnV0ZWQgb24gYW4gXCJBUyBJU1wiIEJBU0lTLFxuICogV0lUSE9VVCBXQVJSQU5USUVTIE9SIENPTkRJVElPTlMgT0YgQU5ZIEtJTkQsIGVpdGhlciBleHByZXNzIG9yIGltcGxpZWQuXG4gKiBTZWUgdGhlIExpY2Vuc2UgZm9yIHRoZSBzcGVjaWZpYyBsYW5ndWFnZSBnb3Zlcm5pbmcgcGVybWlzc2lvbnMgYW5kXG4gKiBsaW1pdGF0aW9ucyB1bmRlciB0aGUgTGljZW5zZS5cbiAqL1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dHlwZXMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/types.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/utils.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/utils.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getClientAttributes = void 0;\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nfunction getClientAttributes(diag, options) {\n    var _a, _b;\n    return {\n        [semantic_conventions_1.SEMATTRS_DB_SYSTEM]: semantic_conventions_1.DBSYSTEMVALUES_REDIS,\n        [semantic_conventions_1.SEMATTRS_NET_PEER_NAME]: (_a = options === null || options === void 0 ? void 0 : options.socket) === null || _a === void 0 ? void 0 : _a.host,\n        [semantic_conventions_1.SEMATTRS_NET_PEER_PORT]: (_b = options === null || options === void 0 ? void 0 : options.socket) === null || _b === void 0 ? void 0 : _b.port,\n        [semantic_conventions_1.SEMATTRS_DB_CONNECTION_STRING]: removeCredentialsFromDBConnectionStringAttribute(diag, options === null || options === void 0 ? void 0 : options.url),\n    };\n}\nexports.getClientAttributes = getClientAttributes;\n/**\n * removeCredentialsFromDBConnectionStringAttribute removes basic auth from url and user_pwd from query string\n *\n * Examples:\n *   redis://user:pass@localhost:6379/mydb => redis://localhost:6379/mydb\n *   redis://localhost:6379?db=mydb&user_pwd=pass => redis://localhost:6379?db=mydb\n */\nfunction removeCredentialsFromDBConnectionStringAttribute(diag, url) {\n    if (typeof url !== 'string' || !url) {\n        return;\n    }\n    try {\n        const u = new URL(url);\n        u.searchParams.delete('user_pwd');\n        u.username = '';\n        u.password = '';\n        return u.href;\n    }\n    catch (err) {\n        diag.error('failed to sanitize redis connection url', err);\n    }\n    return;\n}\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/utils.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/version.js":
/*!**********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/version.js ***!
  \**********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.46.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-redis-4';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/version.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/index.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/index.js ***!
  \********************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/instrumentation.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/types.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/index.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/instrumentation.js":
/*!******************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/instrumentation.js ***!
  \******************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.RedisInstrumentation = void 0;\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/utils.js\");\nconst redis_common_1 = __webpack_require__(/*! @opentelemetry/redis-common */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+redis-common@0.36.2/node_modules/@opentelemetry/redis-common/build/src/index.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/version.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst OTEL_OPEN_SPANS = Symbol('opentelemetry.instrumentation.redis.open_spans');\nconst MULTI_COMMAND_OPTIONS = Symbol('opentelemetry.instrumentation.redis.multi_command_options');\nconst DEFAULT_CONFIG = {\n    requireParentSpan: false,\n};\nclass RedisInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, Object.assign(Object.assign({}, DEFAULT_CONFIG), config));\n    }\n    setConfig(config = {}) {\n        super.setConfig(Object.assign(Object.assign({}, DEFAULT_CONFIG), config));\n    }\n    init() {\n        // @node-redis/client is a new package introduced and consumed by 'redis 4.0.x'\n        // on redis@4.1.0 it was changed to @redis/client.\n        // we will instrument both packages\n        return [\n            this._getInstrumentationNodeModuleDefinition('@redis/client'),\n            this._getInstrumentationNodeModuleDefinition('@node-redis/client'),\n        ];\n    }\n    _getInstrumentationNodeModuleDefinition(basePackageName) {\n        const commanderModuleFile = new instrumentation_1.InstrumentationNodeModuleFile(`${basePackageName}/dist/lib/commander.js`, ['^1.0.0'], (moduleExports, moduleVersion) => {\n            const transformCommandArguments = moduleExports.transformCommandArguments;\n            if (!transformCommandArguments) {\n                this._diag.error('internal instrumentation error, missing transformCommandArguments function');\n                return moduleExports;\n            }\n            // function name and signature changed in redis 4.1.0 from 'extendWithCommands' to 'attachCommands'\n            // the matching internal package names starts with 1.0.x (for redis 4.0.x)\n            const functionToPatch = (moduleVersion === null || moduleVersion === void 0 ? void 0 : moduleVersion.startsWith('1.0.'))\n                ? 'extendWithCommands'\n                : 'attachCommands';\n            // this is the function that extend a redis client with a list of commands.\n            // the function patches the commandExecutor to record a span\n            if ((0, instrumentation_1.isWrapped)(moduleExports === null || moduleExports === void 0 ? void 0 : moduleExports[functionToPatch])) {\n                this._unwrap(moduleExports, functionToPatch);\n            }\n            this._wrap(moduleExports, functionToPatch, this._getPatchExtendWithCommands(transformCommandArguments));\n            return moduleExports;\n        }, (moduleExports) => {\n            if ((0, instrumentation_1.isWrapped)(moduleExports === null || moduleExports === void 0 ? void 0 : moduleExports.extendWithCommands)) {\n                this._unwrap(moduleExports, 'extendWithCommands');\n            }\n            if ((0, instrumentation_1.isWrapped)(moduleExports === null || moduleExports === void 0 ? void 0 : moduleExports.attachCommands)) {\n                this._unwrap(moduleExports, 'attachCommands');\n            }\n        });\n        const multiCommanderModule = new instrumentation_1.InstrumentationNodeModuleFile(`${basePackageName}/dist/lib/client/multi-command.js`, ['^1.0.0'], (moduleExports) => {\n            var _a;\n            const redisClientMultiCommandPrototype = (_a = moduleExports === null || moduleExports === void 0 ? void 0 : moduleExports.default) === null || _a === void 0 ? void 0 : _a.prototype;\n            if ((0, instrumentation_1.isWrapped)(redisClientMultiCommandPrototype === null || redisClientMultiCommandPrototype === void 0 ? void 0 : redisClientMultiCommandPrototype.exec)) {\n                this._unwrap(redisClientMultiCommandPrototype, 'exec');\n            }\n            this._wrap(redisClientMultiCommandPrototype, 'exec', this._getPatchMultiCommandsExec());\n            if ((0, instrumentation_1.isWrapped)(redisClientMultiCommandPrototype === null || redisClientMultiCommandPrototype === void 0 ? void 0 : redisClientMultiCommandPrototype.addCommand)) {\n                this._unwrap(redisClientMultiCommandPrototype, 'addCommand');\n            }\n            this._wrap(redisClientMultiCommandPrototype, 'addCommand', this._getPatchMultiCommandsAddCommand());\n            return moduleExports;\n        }, (moduleExports) => {\n            var _a;\n            const redisClientMultiCommandPrototype = (_a = moduleExports === null || moduleExports === void 0 ? void 0 : moduleExports.default) === null || _a === void 0 ? void 0 : _a.prototype;\n            if ((0, instrumentation_1.isWrapped)(redisClientMultiCommandPrototype === null || redisClientMultiCommandPrototype === void 0 ? void 0 : redisClientMultiCommandPrototype.exec)) {\n                this._unwrap(redisClientMultiCommandPrototype, 'exec');\n            }\n            if ((0, instrumentation_1.isWrapped)(redisClientMultiCommandPrototype === null || redisClientMultiCommandPrototype === void 0 ? void 0 : redisClientMultiCommandPrototype.addCommand)) {\n                this._unwrap(redisClientMultiCommandPrototype, 'addCommand');\n            }\n        });\n        const clientIndexModule = new instrumentation_1.InstrumentationNodeModuleFile(`${basePackageName}/dist/lib/client/index.js`, ['^1.0.0'], (moduleExports) => {\n            var _a;\n            const redisClientPrototype = (_a = moduleExports === null || moduleExports === void 0 ? void 0 : moduleExports.default) === null || _a === void 0 ? void 0 : _a.prototype;\n            // In some @redis/client versions 'multi' is a method. In later\n            // versions, as of https://github.com/redis/node-redis/pull/2324,\n            // 'MULTI' is a method and 'multi' is a property defined in the\n            // constructor that points to 'MULTI', and therefore it will not\n            // be defined on the prototype.\n            if (redisClientPrototype === null || redisClientPrototype === void 0 ? void 0 : redisClientPrototype.multi) {\n                if ((0, instrumentation_1.isWrapped)(redisClientPrototype === null || redisClientPrototype === void 0 ? void 0 : redisClientPrototype.multi)) {\n                    this._unwrap(redisClientPrototype, 'multi');\n                }\n                this._wrap(redisClientPrototype, 'multi', this._getPatchRedisClientMulti());\n            }\n            if (redisClientPrototype === null || redisClientPrototype === void 0 ? void 0 : redisClientPrototype.MULTI) {\n                if ((0, instrumentation_1.isWrapped)(redisClientPrototype === null || redisClientPrototype === void 0 ? void 0 : redisClientPrototype.MULTI)) {\n                    this._unwrap(redisClientPrototype, 'MULTI');\n                }\n                this._wrap(redisClientPrototype, 'MULTI', this._getPatchRedisClientMulti());\n            }\n            if ((0, instrumentation_1.isWrapped)(redisClientPrototype === null || redisClientPrototype === void 0 ? void 0 : redisClientPrototype.sendCommand)) {\n                this._unwrap(redisClientPrototype, 'sendCommand');\n            }\n            this._wrap(redisClientPrototype, 'sendCommand', this._getPatchRedisClientSendCommand());\n            this._wrap(redisClientPrototype, 'connect', this._getPatchedClientConnect());\n            return moduleExports;\n        }, (moduleExports) => {\n            var _a;\n            const redisClientPrototype = (_a = moduleExports === null || moduleExports === void 0 ? void 0 : moduleExports.default) === null || _a === void 0 ? void 0 : _a.prototype;\n            if ((0, instrumentation_1.isWrapped)(redisClientPrototype === null || redisClientPrototype === void 0 ? void 0 : redisClientPrototype.multi)) {\n                this._unwrap(redisClientPrototype, 'multi');\n            }\n            if ((0, instrumentation_1.isWrapped)(redisClientPrototype === null || redisClientPrototype === void 0 ? void 0 : redisClientPrototype.MULTI)) {\n                this._unwrap(redisClientPrototype, 'MULTI');\n            }\n            if ((0, instrumentation_1.isWrapped)(redisClientPrototype === null || redisClientPrototype === void 0 ? void 0 : redisClientPrototype.sendCommand)) {\n                this._unwrap(redisClientPrototype, 'sendCommand');\n            }\n        });\n        return new instrumentation_1.InstrumentationNodeModuleDefinition(basePackageName, ['^1.0.0'], (moduleExports) => {\n            return moduleExports;\n        }, () => { }, [commanderModuleFile, multiCommanderModule, clientIndexModule]);\n    }\n    // serves both for redis 4.0.x where function name is extendWithCommands\n    // and redis ^4.1.0 where function name is attachCommands\n    _getPatchExtendWithCommands(transformCommandArguments) {\n        const plugin = this;\n        return function extendWithCommandsPatchWrapper(original) {\n            return function extendWithCommandsPatch(config) {\n                var _a;\n                if (((_a = config === null || config === void 0 ? void 0 : config.BaseClass) === null || _a === void 0 ? void 0 : _a.name) !== 'RedisClient') {\n                    return original.apply(this, arguments);\n                }\n                const origExecutor = config.executor;\n                config.executor = function (command, args) {\n                    const redisCommandArguments = transformCommandArguments(command, args).args;\n                    return plugin._traceClientCommand(origExecutor, this, arguments, redisCommandArguments);\n                };\n                return original.apply(this, arguments);\n            };\n        };\n    }\n    _getPatchMultiCommandsExec() {\n        const plugin = this;\n        return function execPatchWrapper(original) {\n            return function execPatch() {\n                const execRes = original.apply(this, arguments);\n                if (typeof (execRes === null || execRes === void 0 ? void 0 : execRes.then) !== 'function') {\n                    plugin._diag.error('got non promise result when patching RedisClientMultiCommand.exec');\n                    return execRes;\n                }\n                return execRes\n                    .then((redisRes) => {\n                    const openSpans = this[OTEL_OPEN_SPANS];\n                    plugin._endSpansWithRedisReplies(openSpans, redisRes);\n                    return redisRes;\n                })\n                    .catch((err) => {\n                    const openSpans = this[OTEL_OPEN_SPANS];\n                    if (!openSpans) {\n                        plugin._diag.error('cannot find open spans to end for redis multi command');\n                    }\n                    else {\n                        const replies = err.constructor.name === 'MultiErrorReply'\n                            ? err.replies\n                            : new Array(openSpans.length).fill(err);\n                        plugin._endSpansWithRedisReplies(openSpans, replies);\n                    }\n                    return Promise.reject(err);\n                });\n            };\n        };\n    }\n    _getPatchMultiCommandsAddCommand() {\n        const plugin = this;\n        return function addCommandWrapper(original) {\n            return function addCommandPatch(args) {\n                return plugin._traceClientCommand(original, this, arguments, args);\n            };\n        };\n    }\n    _getPatchRedisClientMulti() {\n        return function multiPatchWrapper(original) {\n            return function multiPatch() {\n                const multiRes = original.apply(this, arguments);\n                multiRes[MULTI_COMMAND_OPTIONS] = this.options;\n                return multiRes;\n            };\n        };\n    }\n    _getPatchRedisClientSendCommand() {\n        const plugin = this;\n        return function sendCommandWrapper(original) {\n            return function sendCommandPatch(args) {\n                return plugin._traceClientCommand(original, this, arguments, args);\n            };\n        };\n    }\n    _getPatchedClientConnect() {\n        const plugin = this;\n        return function connectWrapper(original) {\n            return function patchedConnect() {\n                const options = this.options;\n                const attributes = (0, utils_1.getClientAttributes)(plugin._diag, options);\n                const span = plugin.tracer.startSpan(`${RedisInstrumentation.COMPONENT}-connect`, {\n                    kind: api_1.SpanKind.CLIENT,\n                    attributes,\n                });\n                const res = api_1.context.with(api_1.trace.setSpan(api_1.context.active(), span), () => {\n                    return original.apply(this);\n                });\n                return res\n                    .then((result) => {\n                    span.end();\n                    return result;\n                })\n                    .catch((error) => {\n                    span.recordException(error);\n                    span.setStatus({\n                        code: api_1.SpanStatusCode.ERROR,\n                        message: error.message,\n                    });\n                    span.end();\n                    return Promise.reject(error);\n                });\n            };\n        };\n    }\n    _traceClientCommand(origFunction, origThis, origArguments, redisCommandArguments) {\n        const hasNoParentSpan = api_1.trace.getSpan(api_1.context.active()) === undefined;\n        if (hasNoParentSpan && this.getConfig().requireParentSpan) {\n            return origFunction.apply(origThis, origArguments);\n        }\n        const clientOptions = origThis.options || origThis[MULTI_COMMAND_OPTIONS];\n        const commandName = redisCommandArguments[0]; // types also allows it to be a Buffer, but in practice it only string\n        const commandArgs = redisCommandArguments.slice(1);\n        const dbStatementSerializer = this.getConfig().dbStatementSerializer || redis_common_1.defaultDbStatementSerializer;\n        const attributes = (0, utils_1.getClientAttributes)(this._diag, clientOptions);\n        try {\n            const dbStatement = dbStatementSerializer(commandName, commandArgs);\n            if (dbStatement != null) {\n                attributes[semantic_conventions_1.SEMATTRS_DB_STATEMENT] = dbStatement;\n            }\n        }\n        catch (e) {\n            this._diag.error('dbStatementSerializer throw an exception', e, {\n                commandName,\n            });\n        }\n        const span = this.tracer.startSpan(`${RedisInstrumentation.COMPONENT}-${commandName}`, {\n            kind: api_1.SpanKind.CLIENT,\n            attributes,\n        });\n        const res = api_1.context.with(api_1.trace.setSpan(api_1.context.active(), span), () => {\n            return origFunction.apply(origThis, origArguments);\n        });\n        if (typeof (res === null || res === void 0 ? void 0 : res.then) === 'function') {\n            res.then((redisRes) => {\n                this._endSpanWithResponse(span, commandName, commandArgs, redisRes, undefined);\n            }, (err) => {\n                this._endSpanWithResponse(span, commandName, commandArgs, null, err);\n            });\n        }\n        else {\n            const redisClientMultiCommand = res;\n            redisClientMultiCommand[OTEL_OPEN_SPANS] =\n                redisClientMultiCommand[OTEL_OPEN_SPANS] || [];\n            redisClientMultiCommand[OTEL_OPEN_SPANS].push({\n                span,\n                commandName,\n                commandArgs,\n            });\n        }\n        return res;\n    }\n    _endSpansWithRedisReplies(openSpans, replies) {\n        if (!openSpans) {\n            return this._diag.error('cannot find open spans to end for redis multi command');\n        }\n        if (replies.length !== openSpans.length) {\n            return this._diag.error('number of multi command spans does not match response from redis');\n        }\n        for (let i = 0; i < openSpans.length; i++) {\n            const { span, commandName, commandArgs } = openSpans[i];\n            const currCommandRes = replies[i];\n            const [res, err] = currCommandRes instanceof Error\n                ? [null, currCommandRes]\n                : [currCommandRes, undefined];\n            this._endSpanWithResponse(span, commandName, commandArgs, res, err);\n        }\n    }\n    _endSpanWithResponse(span, commandName, commandArgs, response, error) {\n        const { responseHook } = this.getConfig();\n        if (!error && responseHook) {\n            try {\n                responseHook(span, commandName, commandArgs, response);\n            }\n            catch (err) {\n                this._diag.error('responseHook throw an exception', err);\n            }\n        }\n        if (error) {\n            span.recordException(error);\n            span.setStatus({ code: api_1.SpanStatusCode.ERROR, message: error === null || error === void 0 ? void 0 : error.message });\n        }\n        span.end();\n    }\n}\nexports.RedisInstrumentation = RedisInstrumentation;\nRedisInstrumentation.COMPONENT = 'redis';\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/instrumentation.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/types.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/types.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF9iY2QzYTZjMGVkMDY4NzljY2ZjZTM1ZTNkYzJiNzJjYy9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLXJlZGlzLTQvYnVpbGQvc3JjL3R5cGVzLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdEIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFud2FyXFxEb2N1bWVudHNcXEN1YmVudHdlYlxcY3ViZW50XFxub2RlX21vZHVsZXNcXC5wbnBtXFxAb3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfYmNkM2E2YzBlZDA2ODc5Y2NmY2UzNWUzZGMyYjcyY2NcXG5vZGVfbW9kdWxlc1xcQG9wZW50ZWxlbWV0cnlcXGluc3RydW1lbnRhdGlvbi1yZWRpcy00XFxidWlsZFxcc3JjXFx0eXBlcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbi8qXG4gKiBDb3B5cmlnaHQgVGhlIE9wZW5UZWxlbWV0cnkgQXV0aG9yc1xuICpcbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4gKiB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuXG4gKiBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbiAqXG4gKiAgICAgIGh0dHBzOi8vd3d3LmFwYWNoZS5vcmcvbGljZW5zZXMvTElDRU5TRS0yLjBcbiAqXG4gKiBVbmxlc3MgcmVxdWlyZWQgYnkgYXBwbGljYWJsZSBsYXcgb3IgYWdyZWVkIHRvIGluIHdyaXRpbmcsIHNvZnR3YXJlXG4gKiBkaXN0cmlidXRlZCB1bmRlciB0aGUgTGljZW5zZSBpcyBkaXN0cmlidXRlZCBvbiBhbiBcIkFTIElTXCIgQkFTSVMsXG4gKiBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgQ09ORElUSU9OUyBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC5cbiAqIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmRcbiAqIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuICovXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD10eXBlcy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/types.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/utils.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/utils.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getClientAttributes = void 0;\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nfunction getClientAttributes(diag, options) {\n    var _a, _b;\n    return {\n        [semantic_conventions_1.SEMATTRS_DB_SYSTEM]: semantic_conventions_1.DBSYSTEMVALUES_REDIS,\n        [semantic_conventions_1.SEMATTRS_NET_PEER_NAME]: (_a = options === null || options === void 0 ? void 0 : options.socket) === null || _a === void 0 ? void 0 : _a.host,\n        [semantic_conventions_1.SEMATTRS_NET_PEER_PORT]: (_b = options === null || options === void 0 ? void 0 : options.socket) === null || _b === void 0 ? void 0 : _b.port,\n        [semantic_conventions_1.SEMATTRS_DB_CONNECTION_STRING]: removeCredentialsFromDBConnectionStringAttribute(diag, options === null || options === void 0 ? void 0 : options.url),\n    };\n}\nexports.getClientAttributes = getClientAttributes;\n/**\n * removeCredentialsFromDBConnectionStringAttribute removes basic auth from url and user_pwd from query string\n *\n * Examples:\n *   redis://user:pass@localhost:6379/mydb => redis://localhost:6379/mydb\n *   redis://localhost:6379?db=mydb&user_pwd=pass => redis://localhost:6379?db=mydb\n */\nfunction removeCredentialsFromDBConnectionStringAttribute(diag, url) {\n    if (typeof url !== 'string' || !url) {\n        return;\n    }\n    try {\n        const u = new URL(url);\n        u.searchParams.delete('user_pwd');\n        u.username = '';\n        u.password = '';\n        return u.href;\n    }\n    catch (err) {\n        diag.error('failed to sanitize redis connection url', err);\n    }\n    return;\n}\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/utils.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/version.js":
/*!**********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/version.js ***!
  \**********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.46.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-redis-4';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/version.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/index.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/index.js ***!
  \********************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/instrumentation.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/types.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/instrumentation.js":
/*!******************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/instrumentation.js ***!
  \******************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.RedisInstrumentation = void 0;\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/utils.js\");\nconst redis_common_1 = __webpack_require__(/*! @opentelemetry/redis-common */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+redis-common@0.36.2/node_modules/@opentelemetry/redis-common/build/src/index.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/version.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst OTEL_OPEN_SPANS = Symbol('opentelemetry.instrumentation.redis.open_spans');\nconst MULTI_COMMAND_OPTIONS = Symbol('opentelemetry.instrumentation.redis.multi_command_options');\nconst DEFAULT_CONFIG = {\n    requireParentSpan: false,\n};\nclass RedisInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, Object.assign(Object.assign({}, DEFAULT_CONFIG), config));\n    }\n    setConfig(config = {}) {\n        super.setConfig(Object.assign(Object.assign({}, DEFAULT_CONFIG), config));\n    }\n    init() {\n        // @node-redis/client is a new package introduced and consumed by 'redis 4.0.x'\n        // on redis@4.1.0 it was changed to @redis/client.\n        // we will instrument both packages\n        return [\n            this._getInstrumentationNodeModuleDefinition('@redis/client'),\n            this._getInstrumentationNodeModuleDefinition('@node-redis/client'),\n        ];\n    }\n    _getInstrumentationNodeModuleDefinition(basePackageName) {\n        const commanderModuleFile = new instrumentation_1.InstrumentationNodeModuleFile(`${basePackageName}/dist/lib/commander.js`, ['^1.0.0'], (moduleExports, moduleVersion) => {\n            const transformCommandArguments = moduleExports.transformCommandArguments;\n            if (!transformCommandArguments) {\n                this._diag.error('internal instrumentation error, missing transformCommandArguments function');\n                return moduleExports;\n            }\n            // function name and signature changed in redis 4.1.0 from 'extendWithCommands' to 'attachCommands'\n            // the matching internal package names starts with 1.0.x (for redis 4.0.x)\n            const functionToPatch = (moduleVersion === null || moduleVersion === void 0 ? void 0 : moduleVersion.startsWith('1.0.'))\n                ? 'extendWithCommands'\n                : 'attachCommands';\n            // this is the function that extend a redis client with a list of commands.\n            // the function patches the commandExecutor to record a span\n            if ((0, instrumentation_1.isWrapped)(moduleExports === null || moduleExports === void 0 ? void 0 : moduleExports[functionToPatch])) {\n                this._unwrap(moduleExports, functionToPatch);\n            }\n            this._wrap(moduleExports, functionToPatch, this._getPatchExtendWithCommands(transformCommandArguments));\n            return moduleExports;\n        }, (moduleExports) => {\n            if ((0, instrumentation_1.isWrapped)(moduleExports === null || moduleExports === void 0 ? void 0 : moduleExports.extendWithCommands)) {\n                this._unwrap(moduleExports, 'extendWithCommands');\n            }\n            if ((0, instrumentation_1.isWrapped)(moduleExports === null || moduleExports === void 0 ? void 0 : moduleExports.attachCommands)) {\n                this._unwrap(moduleExports, 'attachCommands');\n            }\n        });\n        const multiCommanderModule = new instrumentation_1.InstrumentationNodeModuleFile(`${basePackageName}/dist/lib/client/multi-command.js`, ['^1.0.0'], (moduleExports) => {\n            var _a;\n            const redisClientMultiCommandPrototype = (_a = moduleExports === null || moduleExports === void 0 ? void 0 : moduleExports.default) === null || _a === void 0 ? void 0 : _a.prototype;\n            if ((0, instrumentation_1.isWrapped)(redisClientMultiCommandPrototype === null || redisClientMultiCommandPrototype === void 0 ? void 0 : redisClientMultiCommandPrototype.exec)) {\n                this._unwrap(redisClientMultiCommandPrototype, 'exec');\n            }\n            this._wrap(redisClientMultiCommandPrototype, 'exec', this._getPatchMultiCommandsExec());\n            if ((0, instrumentation_1.isWrapped)(redisClientMultiCommandPrototype === null || redisClientMultiCommandPrototype === void 0 ? void 0 : redisClientMultiCommandPrototype.addCommand)) {\n                this._unwrap(redisClientMultiCommandPrototype, 'addCommand');\n            }\n            this._wrap(redisClientMultiCommandPrototype, 'addCommand', this._getPatchMultiCommandsAddCommand());\n            return moduleExports;\n        }, (moduleExports) => {\n            var _a;\n            const redisClientMultiCommandPrototype = (_a = moduleExports === null || moduleExports === void 0 ? void 0 : moduleExports.default) === null || _a === void 0 ? void 0 : _a.prototype;\n            if ((0, instrumentation_1.isWrapped)(redisClientMultiCommandPrototype === null || redisClientMultiCommandPrototype === void 0 ? void 0 : redisClientMultiCommandPrototype.exec)) {\n                this._unwrap(redisClientMultiCommandPrototype, 'exec');\n            }\n            if ((0, instrumentation_1.isWrapped)(redisClientMultiCommandPrototype === null || redisClientMultiCommandPrototype === void 0 ? void 0 : redisClientMultiCommandPrototype.addCommand)) {\n                this._unwrap(redisClientMultiCommandPrototype, 'addCommand');\n            }\n        });\n        const clientIndexModule = new instrumentation_1.InstrumentationNodeModuleFile(`${basePackageName}/dist/lib/client/index.js`, ['^1.0.0'], (moduleExports) => {\n            var _a;\n            const redisClientPrototype = (_a = moduleExports === null || moduleExports === void 0 ? void 0 : moduleExports.default) === null || _a === void 0 ? void 0 : _a.prototype;\n            // In some @redis/client versions 'multi' is a method. In later\n            // versions, as of https://github.com/redis/node-redis/pull/2324,\n            // 'MULTI' is a method and 'multi' is a property defined in the\n            // constructor that points to 'MULTI', and therefore it will not\n            // be defined on the prototype.\n            if (redisClientPrototype === null || redisClientPrototype === void 0 ? void 0 : redisClientPrototype.multi) {\n                if ((0, instrumentation_1.isWrapped)(redisClientPrototype === null || redisClientPrototype === void 0 ? void 0 : redisClientPrototype.multi)) {\n                    this._unwrap(redisClientPrototype, 'multi');\n                }\n                this._wrap(redisClientPrototype, 'multi', this._getPatchRedisClientMulti());\n            }\n            if (redisClientPrototype === null || redisClientPrototype === void 0 ? void 0 : redisClientPrototype.MULTI) {\n                if ((0, instrumentation_1.isWrapped)(redisClientPrototype === null || redisClientPrototype === void 0 ? void 0 : redisClientPrototype.MULTI)) {\n                    this._unwrap(redisClientPrototype, 'MULTI');\n                }\n                this._wrap(redisClientPrototype, 'MULTI', this._getPatchRedisClientMulti());\n            }\n            if ((0, instrumentation_1.isWrapped)(redisClientPrototype === null || redisClientPrototype === void 0 ? void 0 : redisClientPrototype.sendCommand)) {\n                this._unwrap(redisClientPrototype, 'sendCommand');\n            }\n            this._wrap(redisClientPrototype, 'sendCommand', this._getPatchRedisClientSendCommand());\n            this._wrap(redisClientPrototype, 'connect', this._getPatchedClientConnect());\n            return moduleExports;\n        }, (moduleExports) => {\n            var _a;\n            const redisClientPrototype = (_a = moduleExports === null || moduleExports === void 0 ? void 0 : moduleExports.default) === null || _a === void 0 ? void 0 : _a.prototype;\n            if ((0, instrumentation_1.isWrapped)(redisClientPrototype === null || redisClientPrototype === void 0 ? void 0 : redisClientPrototype.multi)) {\n                this._unwrap(redisClientPrototype, 'multi');\n            }\n            if ((0, instrumentation_1.isWrapped)(redisClientPrototype === null || redisClientPrototype === void 0 ? void 0 : redisClientPrototype.MULTI)) {\n                this._unwrap(redisClientPrototype, 'MULTI');\n            }\n            if ((0, instrumentation_1.isWrapped)(redisClientPrototype === null || redisClientPrototype === void 0 ? void 0 : redisClientPrototype.sendCommand)) {\n                this._unwrap(redisClientPrototype, 'sendCommand');\n            }\n        });\n        return new instrumentation_1.InstrumentationNodeModuleDefinition(basePackageName, ['^1.0.0'], (moduleExports) => {\n            return moduleExports;\n        }, () => { }, [commanderModuleFile, multiCommanderModule, clientIndexModule]);\n    }\n    // serves both for redis 4.0.x where function name is extendWithCommands\n    // and redis ^4.1.0 where function name is attachCommands\n    _getPatchExtendWithCommands(transformCommandArguments) {\n        const plugin = this;\n        return function extendWithCommandsPatchWrapper(original) {\n            return function extendWithCommandsPatch(config) {\n                var _a;\n                if (((_a = config === null || config === void 0 ? void 0 : config.BaseClass) === null || _a === void 0 ? void 0 : _a.name) !== 'RedisClient') {\n                    return original.apply(this, arguments);\n                }\n                const origExecutor = config.executor;\n                config.executor = function (command, args) {\n                    const redisCommandArguments = transformCommandArguments(command, args).args;\n                    return plugin._traceClientCommand(origExecutor, this, arguments, redisCommandArguments);\n                };\n                return original.apply(this, arguments);\n            };\n        };\n    }\n    _getPatchMultiCommandsExec() {\n        const plugin = this;\n        return function execPatchWrapper(original) {\n            return function execPatch() {\n                const execRes = original.apply(this, arguments);\n                if (typeof (execRes === null || execRes === void 0 ? void 0 : execRes.then) !== 'function') {\n                    plugin._diag.error('got non promise result when patching RedisClientMultiCommand.exec');\n                    return execRes;\n                }\n                return execRes\n                    .then((redisRes) => {\n                    const openSpans = this[OTEL_OPEN_SPANS];\n                    plugin._endSpansWithRedisReplies(openSpans, redisRes);\n                    return redisRes;\n                })\n                    .catch((err) => {\n                    const openSpans = this[OTEL_OPEN_SPANS];\n                    if (!openSpans) {\n                        plugin._diag.error('cannot find open spans to end for redis multi command');\n                    }\n                    else {\n                        const replies = err.constructor.name === 'MultiErrorReply'\n                            ? err.replies\n                            : new Array(openSpans.length).fill(err);\n                        plugin._endSpansWithRedisReplies(openSpans, replies);\n                    }\n                    return Promise.reject(err);\n                });\n            };\n        };\n    }\n    _getPatchMultiCommandsAddCommand() {\n        const plugin = this;\n        return function addCommandWrapper(original) {\n            return function addCommandPatch(args) {\n                return plugin._traceClientCommand(original, this, arguments, args);\n            };\n        };\n    }\n    _getPatchRedisClientMulti() {\n        return function multiPatchWrapper(original) {\n            return function multiPatch() {\n                const multiRes = original.apply(this, arguments);\n                multiRes[MULTI_COMMAND_OPTIONS] = this.options;\n                return multiRes;\n            };\n        };\n    }\n    _getPatchRedisClientSendCommand() {\n        const plugin = this;\n        return function sendCommandWrapper(original) {\n            return function sendCommandPatch(args) {\n                return plugin._traceClientCommand(original, this, arguments, args);\n            };\n        };\n    }\n    _getPatchedClientConnect() {\n        const plugin = this;\n        return function connectWrapper(original) {\n            return function patchedConnect() {\n                const options = this.options;\n                const attributes = (0, utils_1.getClientAttributes)(plugin._diag, options);\n                const span = plugin.tracer.startSpan(`${RedisInstrumentation.COMPONENT}-connect`, {\n                    kind: api_1.SpanKind.CLIENT,\n                    attributes,\n                });\n                const res = api_1.context.with(api_1.trace.setSpan(api_1.context.active(), span), () => {\n                    return original.apply(this);\n                });\n                return res\n                    .then((result) => {\n                    span.end();\n                    return result;\n                })\n                    .catch((error) => {\n                    span.recordException(error);\n                    span.setStatus({\n                        code: api_1.SpanStatusCode.ERROR,\n                        message: error.message,\n                    });\n                    span.end();\n                    return Promise.reject(error);\n                });\n            };\n        };\n    }\n    _traceClientCommand(origFunction, origThis, origArguments, redisCommandArguments) {\n        const hasNoParentSpan = api_1.trace.getSpan(api_1.context.active()) === undefined;\n        if (hasNoParentSpan && this.getConfig().requireParentSpan) {\n            return origFunction.apply(origThis, origArguments);\n        }\n        const clientOptions = origThis.options || origThis[MULTI_COMMAND_OPTIONS];\n        const commandName = redisCommandArguments[0]; // types also allows it to be a Buffer, but in practice it only string\n        const commandArgs = redisCommandArguments.slice(1);\n        const dbStatementSerializer = this.getConfig().dbStatementSerializer || redis_common_1.defaultDbStatementSerializer;\n        const attributes = (0, utils_1.getClientAttributes)(this._diag, clientOptions);\n        try {\n            const dbStatement = dbStatementSerializer(commandName, commandArgs);\n            if (dbStatement != null) {\n                attributes[semantic_conventions_1.SEMATTRS_DB_STATEMENT] = dbStatement;\n            }\n        }\n        catch (e) {\n            this._diag.error('dbStatementSerializer throw an exception', e, {\n                commandName,\n            });\n        }\n        const span = this.tracer.startSpan(`${RedisInstrumentation.COMPONENT}-${commandName}`, {\n            kind: api_1.SpanKind.CLIENT,\n            attributes,\n        });\n        const res = api_1.context.with(api_1.trace.setSpan(api_1.context.active(), span), () => {\n            return origFunction.apply(origThis, origArguments);\n        });\n        if (typeof (res === null || res === void 0 ? void 0 : res.then) === 'function') {\n            res.then((redisRes) => {\n                this._endSpanWithResponse(span, commandName, commandArgs, redisRes, undefined);\n            }, (err) => {\n                this._endSpanWithResponse(span, commandName, commandArgs, null, err);\n            });\n        }\n        else {\n            const redisClientMultiCommand = res;\n            redisClientMultiCommand[OTEL_OPEN_SPANS] =\n                redisClientMultiCommand[OTEL_OPEN_SPANS] || [];\n            redisClientMultiCommand[OTEL_OPEN_SPANS].push({\n                span,\n                commandName,\n                commandArgs,\n            });\n        }\n        return res;\n    }\n    _endSpansWithRedisReplies(openSpans, replies) {\n        if (!openSpans) {\n            return this._diag.error('cannot find open spans to end for redis multi command');\n        }\n        if (replies.length !== openSpans.length) {\n            return this._diag.error('number of multi command spans does not match response from redis');\n        }\n        for (let i = 0; i < openSpans.length; i++) {\n            const { span, commandName, commandArgs } = openSpans[i];\n            const currCommandRes = replies[i];\n            const [res, err] = currCommandRes instanceof Error\n                ? [null, currCommandRes]\n                : [currCommandRes, undefined];\n            this._endSpanWithResponse(span, commandName, commandArgs, res, err);\n        }\n    }\n    _endSpanWithResponse(span, commandName, commandArgs, response, error) {\n        const { responseHook } = this.getConfig();\n        if (!error && responseHook) {\n            try {\n                responseHook(span, commandName, commandArgs, response);\n            }\n            catch (err) {\n                this._diag.error('responseHook throw an exception', err);\n            }\n        }\n        if (error) {\n            span.recordException(error);\n            span.setStatus({ code: api_1.SpanStatusCode.ERROR, message: error === null || error === void 0 ? void 0 : error.message });\n        }\n        span.end();\n    }\n}\nexports.RedisInstrumentation = RedisInstrumentation;\nRedisInstrumentation.COMPONENT = 'redis';\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/instrumentation.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/types.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/types.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF9iY2QzYTZjMGVkMDY4NzljY2ZjZTM1ZTNkYzJiNzJjYy9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLXJlZGlzLTQvYnVpbGQvc3JjL3R5cGVzLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdEIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFud2FyXFxEb2N1bWVudHNcXEN1YmVudHdlYlxcY3ViZW50XFxub2RlX21vZHVsZXNcXC5wbnBtXFxAb3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfYmNkM2E2YzBlZDA2ODc5Y2NmY2UzNWUzZGMyYjcyY2NcXG5vZGVfbW9kdWxlc1xcQG9wZW50ZWxlbWV0cnlcXGluc3RydW1lbnRhdGlvbi1yZWRpcy00XFxidWlsZFxcc3JjXFx0eXBlcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbi8qXG4gKiBDb3B5cmlnaHQgVGhlIE9wZW5UZWxlbWV0cnkgQXV0aG9yc1xuICpcbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4gKiB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuXG4gKiBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbiAqXG4gKiAgICAgIGh0dHBzOi8vd3d3LmFwYWNoZS5vcmcvbGljZW5zZXMvTElDRU5TRS0yLjBcbiAqXG4gKiBVbmxlc3MgcmVxdWlyZWQgYnkgYXBwbGljYWJsZSBsYXcgb3IgYWdyZWVkIHRvIGluIHdyaXRpbmcsIHNvZnR3YXJlXG4gKiBkaXN0cmlidXRlZCB1bmRlciB0aGUgTGljZW5zZSBpcyBkaXN0cmlidXRlZCBvbiBhbiBcIkFTIElTXCIgQkFTSVMsXG4gKiBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgQ09ORElUSU9OUyBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC5cbiAqIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmRcbiAqIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuICovXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD10eXBlcy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/types.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/utils.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/utils.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getClientAttributes = void 0;\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nfunction getClientAttributes(diag, options) {\n    var _a, _b;\n    return {\n        [semantic_conventions_1.SEMATTRS_DB_SYSTEM]: semantic_conventions_1.DBSYSTEMVALUES_REDIS,\n        [semantic_conventions_1.SEMATTRS_NET_PEER_NAME]: (_a = options === null || options === void 0 ? void 0 : options.socket) === null || _a === void 0 ? void 0 : _a.host,\n        [semantic_conventions_1.SEMATTRS_NET_PEER_PORT]: (_b = options === null || options === void 0 ? void 0 : options.socket) === null || _b === void 0 ? void 0 : _b.port,\n        [semantic_conventions_1.SEMATTRS_DB_CONNECTION_STRING]: removeCredentialsFromDBConnectionStringAttribute(diag, options === null || options === void 0 ? void 0 : options.url),\n    };\n}\nexports.getClientAttributes = getClientAttributes;\n/**\n * removeCredentialsFromDBConnectionStringAttribute removes basic auth from url and user_pwd from query string\n *\n * Examples:\n *   redis://user:pass@localhost:6379/mydb => redis://localhost:6379/mydb\n *   redis://localhost:6379?db=mydb&user_pwd=pass => redis://localhost:6379?db=mydb\n */\nfunction removeCredentialsFromDBConnectionStringAttribute(diag, url) {\n    if (typeof url !== 'string' || !url) {\n        return;\n    }\n    try {\n        const u = new URL(url);\n        u.searchParams.delete('user_pwd');\n        u.username = '';\n        u.password = '';\n        return u.href;\n    }\n    catch (err) {\n        diag.error('failed to sanitize redis connection url', err);\n    }\n    return;\n}\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/utils.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/version.js":
/*!**********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/version.js ***!
  \**********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.46.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-redis-4';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/version.js\n");

/***/ })

};
;