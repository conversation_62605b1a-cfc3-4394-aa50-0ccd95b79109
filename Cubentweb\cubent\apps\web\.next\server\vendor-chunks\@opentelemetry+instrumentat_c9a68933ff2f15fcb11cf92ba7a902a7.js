"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7";
exports.ids = ["vendor-chunks/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7"];
exports.modules = {

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/constants.js":
/*!*******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/constants.js ***!
  \*******************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.SYNC_FUNCTIONS = exports.CALLBACK_FUNCTIONS = exports.PROMISE_FUNCTIONS = void 0;\nexports.PROMISE_FUNCTIONS = [\n    'access',\n    'appendFile',\n    'chmod',\n    'chown',\n    'copyFile',\n    'cp',\n    'lchown',\n    'link',\n    'lstat',\n    'lutimes',\n    'mkdir',\n    'mkdtemp',\n    'open',\n    'opendir',\n    'readdir',\n    'readFile',\n    'readlink',\n    'realpath',\n    'rename',\n    'rm',\n    'rmdir',\n    'stat',\n    'symlink',\n    'truncate',\n    'unlink',\n    'utimes',\n    'writeFile',\n    // 'lchmod', // only implemented on macOS\n];\nexports.CALLBACK_FUNCTIONS = [\n    'access',\n    'appendFile',\n    'chmod',\n    'chown',\n    'copyFile',\n    'cp',\n    'exists',\n    'lchown',\n    'link',\n    'lstat',\n    'lutimes',\n    'mkdir',\n    'mkdtemp',\n    'open',\n    'opendir',\n    'readdir',\n    'readFile',\n    'readlink',\n    'realpath',\n    'realpath.native',\n    'rename',\n    'rm',\n    'rmdir',\n    'stat',\n    'symlink',\n    'truncate',\n    'unlink',\n    'utimes',\n    'writeFile',\n    // 'close', // functions on file descriptor\n    // 'fchmod', // functions on file descriptor\n    // 'fchown', // functions on file descriptor\n    // 'fdatasync', // functions on file descriptor\n    // 'fstat', // functions on file descriptor\n    // 'fsync', // functions on file descriptor\n    // 'ftruncate', // functions on file descriptor\n    // 'futimes', // functions on file descriptor\n    // 'lchmod', // only implemented on macOS\n    // 'read', // functions on file descriptor\n    // 'readv', // functions on file descriptor\n    // 'write', // functions on file descriptor\n    // 'writev', // functions on file descriptor\n];\nexports.SYNC_FUNCTIONS = [\n    'accessSync',\n    'appendFileSync',\n    'chmodSync',\n    'chownSync',\n    'copyFileSync',\n    'cpSync',\n    'existsSync',\n    'lchownSync',\n    'linkSync',\n    'lstatSync',\n    'lutimesSync',\n    'mkdirSync',\n    'mkdtempSync',\n    'opendirSync',\n    'openSync',\n    'readdirSync',\n    'readFileSync',\n    'readlinkSync',\n    'realpathSync',\n    'realpathSync.native',\n    'renameSync',\n    'rmdirSync',\n    'rmSync',\n    'statSync',\n    'symlinkSync',\n    'truncateSync',\n    'unlinkSync',\n    'utimesSync',\n    'writeFileSync',\n    // 'closeSync', // functions on file descriptor\n    // 'fchmodSync', // functions on file descriptor\n    // 'fchownSync', // functions on file descriptor\n    // 'fdatasyncSync', // functions on file descriptor\n    // 'fstatSync', // functions on file descriptor\n    // 'fsyncSync', // functions on file descriptor\n    // 'ftruncateSync', // functions on file descriptor\n    // 'futimesSync', // functions on file descriptor\n    // 'lchmodSync', // only implemented on macOS\n    // 'readSync', // functions on file descriptor\n    // 'readvSync', // functions on file descriptor\n    // 'writeSync', // functions on file descriptor\n    // 'writevSync', // functions on file descriptor\n];\n//# sourceMappingURL=constants.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/constants.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/index.js":
/*!***************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/index.js ***!
  \***************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/instrumentation.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/types.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9Ab3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfYzlhNjg5MzNmZjJmMTVmY2IxMWNmOTJiYTdhOTAyYTcvbm9kZV9tb2R1bGVzL0BvcGVudGVsZW1ldHJ5L2luc3RydW1lbnRhdGlvbi1mcy9idWlsZC9zcmMvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUNBQW1DLG9DQUFvQyxnQkFBZ0I7QUFDdkYsQ0FBQztBQUNEO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0EsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELGFBQWEsbUJBQU8sQ0FBQyx5TUFBbUI7QUFDeEMsYUFBYSxtQkFBTyxDQUFDLHFMQUFTO0FBQzlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFud2FyXFxEb2N1bWVudHNcXEN1YmVudHdlYlxcY3ViZW50XFxub2RlX21vZHVsZXNcXC5wbnBtXFxAb3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfYzlhNjg5MzNmZjJmMTVmY2IxMWNmOTJiYTdhOTAyYTdcXG5vZGVfbW9kdWxlc1xcQG9wZW50ZWxlbWV0cnlcXGluc3RydW1lbnRhdGlvbi1mc1xcYnVpbGRcXHNyY1xcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG4vKlxuICogQ29weXJpZ2h0IFRoZSBPcGVuVGVsZW1ldHJ5IEF1dGhvcnNcbiAqXG4gKiBMaWNlbnNlZCB1bmRlciB0aGUgQXBhY2hlIExpY2Vuc2UsIFZlcnNpb24gMi4wICh0aGUgXCJMaWNlbnNlXCIpO1xuICogeW91IG1heSBub3QgdXNlIHRoaXMgZmlsZSBleGNlcHQgaW4gY29tcGxpYW5jZSB3aXRoIHRoZSBMaWNlbnNlLlxuICogWW91IG1heSBvYnRhaW4gYSBjb3B5IG9mIHRoZSBMaWNlbnNlIGF0XG4gKlxuICogICAgICBodHRwczovL3d3dy5hcGFjaGUub3JnL2xpY2Vuc2VzL0xJQ0VOU0UtMi4wXG4gKlxuICogVW5sZXNzIHJlcXVpcmVkIGJ5IGFwcGxpY2FibGUgbGF3IG9yIGFncmVlZCB0byBpbiB3cml0aW5nLCBzb2Z0d2FyZVxuICogZGlzdHJpYnV0ZWQgdW5kZXIgdGhlIExpY2Vuc2UgaXMgZGlzdHJpYnV0ZWQgb24gYW4gXCJBUyBJU1wiIEJBU0lTLFxuICogV0lUSE9VVCBXQVJSQU5USUVTIE9SIENPTkRJVElPTlMgT0YgQU5ZIEtJTkQsIGVpdGhlciBleHByZXNzIG9yIGltcGxpZWQuXG4gKiBTZWUgdGhlIExpY2Vuc2UgZm9yIHRoZSBzcGVjaWZpYyBsYW5ndWFnZSBnb3Zlcm5pbmcgcGVybWlzc2lvbnMgYW5kXG4gKiBsaW1pdGF0aW9ucyB1bmRlciB0aGUgTGljZW5zZS5cbiAqL1xudmFyIF9fY3JlYXRlQmluZGluZyA9ICh0aGlzICYmIHRoaXMuX19jcmVhdGVCaW5kaW5nKSB8fCAoT2JqZWN0LmNyZWF0ZSA/IChmdW5jdGlvbihvLCBtLCBrLCBrMikge1xuICAgIGlmIChrMiA9PT0gdW5kZWZpbmVkKSBrMiA9IGs7XG4gICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KG8sIGsyLCB7IGVudW1lcmFibGU6IHRydWUsIGdldDogZnVuY3Rpb24oKSB7IHJldHVybiBtW2tdOyB9IH0pO1xufSkgOiAoZnVuY3Rpb24obywgbSwgaywgazIpIHtcbiAgICBpZiAoazIgPT09IHVuZGVmaW5lZCkgazIgPSBrO1xuICAgIG9bazJdID0gbVtrXTtcbn0pKTtcbnZhciBfX2V4cG9ydFN0YXIgPSAodGhpcyAmJiB0aGlzLl9fZXhwb3J0U3RhcikgfHwgZnVuY3Rpb24obSwgZXhwb3J0cykge1xuICAgIGZvciAodmFyIHAgaW4gbSkgaWYgKHAgIT09IFwiZGVmYXVsdFwiICYmICFPYmplY3QucHJvdG90eXBlLmhhc093blByb3BlcnR5LmNhbGwoZXhwb3J0cywgcCkpIF9fY3JlYXRlQmluZGluZyhleHBvcnRzLCBtLCBwKTtcbn07XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5fX2V4cG9ydFN0YXIocmVxdWlyZShcIi4vaW5zdHJ1bWVudGF0aW9uXCIpLCBleHBvcnRzKTtcbl9fZXhwb3J0U3RhcihyZXF1aXJlKFwiLi90eXBlc1wiKSwgZXhwb3J0cyk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/index.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/instrumentation.js":
/*!*************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/instrumentation.js ***!
  \*************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.FsInstrumentation = void 0;\nconst api = __webpack_require__(/*! @opentelemetry/api */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst core_1 = __webpack_require__(/*! @opentelemetry/core */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+core@1.30.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/core/build/esm/index.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/version.js\");\nconst constants_1 = __webpack_require__(/*! ./constants */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/constants.js\");\nconst util_1 = __webpack_require__(/*! util */ \"util\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/utils.js\");\n/**\n * This is important for 2-level functions like `realpath.native` to retain the 2nd-level\n * when patching the 1st-level.\n */\nfunction patchedFunctionWithOriginalProperties(patchedFunction, original) {\n    return Object.assign(patchedFunction, original);\n}\nclass FsInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, config);\n    }\n    init() {\n        return [\n            new instrumentation_1.InstrumentationNodeModuleDefinition('fs', ['*'], (fs) => {\n                for (const fName of constants_1.SYNC_FUNCTIONS) {\n                    const { objectToPatch, functionNameToPatch } = (0, utils_1.indexFs)(fs, fName);\n                    if ((0, instrumentation_1.isWrapped)(objectToPatch[functionNameToPatch])) {\n                        this._unwrap(objectToPatch, functionNameToPatch);\n                    }\n                    this._wrap(objectToPatch, functionNameToPatch, this._patchSyncFunction.bind(this, fName));\n                }\n                for (const fName of constants_1.CALLBACK_FUNCTIONS) {\n                    const { objectToPatch, functionNameToPatch } = (0, utils_1.indexFs)(fs, fName);\n                    if ((0, instrumentation_1.isWrapped)(objectToPatch[functionNameToPatch])) {\n                        this._unwrap(objectToPatch, functionNameToPatch);\n                    }\n                    if (fName === 'exists') {\n                        // handling separately because of the inconsistent cb style:\n                        // `exists` doesn't have error as the first argument, but the result\n                        this._wrap(objectToPatch, functionNameToPatch, this._patchExistsCallbackFunction.bind(this, fName));\n                        continue;\n                    }\n                    this._wrap(objectToPatch, functionNameToPatch, this._patchCallbackFunction.bind(this, fName));\n                }\n                for (const fName of constants_1.PROMISE_FUNCTIONS) {\n                    if ((0, instrumentation_1.isWrapped)(fs.promises[fName])) {\n                        this._unwrap(fs.promises, fName);\n                    }\n                    this._wrap(fs.promises, fName, this._patchPromiseFunction.bind(this, fName));\n                }\n                return fs;\n            }, (fs) => {\n                if (fs === undefined)\n                    return;\n                for (const fName of constants_1.SYNC_FUNCTIONS) {\n                    const { objectToPatch, functionNameToPatch } = (0, utils_1.indexFs)(fs, fName);\n                    if ((0, instrumentation_1.isWrapped)(objectToPatch[functionNameToPatch])) {\n                        this._unwrap(objectToPatch, functionNameToPatch);\n                    }\n                }\n                for (const fName of constants_1.CALLBACK_FUNCTIONS) {\n                    const { objectToPatch, functionNameToPatch } = (0, utils_1.indexFs)(fs, fName);\n                    if ((0, instrumentation_1.isWrapped)(objectToPatch[functionNameToPatch])) {\n                        this._unwrap(objectToPatch, functionNameToPatch);\n                    }\n                }\n                for (const fName of constants_1.PROMISE_FUNCTIONS) {\n                    if ((0, instrumentation_1.isWrapped)(fs.promises[fName])) {\n                        this._unwrap(fs.promises, fName);\n                    }\n                }\n            }),\n            new instrumentation_1.InstrumentationNodeModuleDefinition('fs/promises', ['*'], (fsPromises) => {\n                for (const fName of constants_1.PROMISE_FUNCTIONS) {\n                    if ((0, instrumentation_1.isWrapped)(fsPromises[fName])) {\n                        this._unwrap(fsPromises, fName);\n                    }\n                    this._wrap(fsPromises, fName, this._patchPromiseFunction.bind(this, fName));\n                }\n                return fsPromises;\n            }, (fsPromises) => {\n                if (fsPromises === undefined)\n                    return;\n                for (const fName of constants_1.PROMISE_FUNCTIONS) {\n                    if ((0, instrumentation_1.isWrapped)(fsPromises[fName])) {\n                        this._unwrap(fsPromises, fName);\n                    }\n                }\n            }),\n        ];\n    }\n    _patchSyncFunction(functionName, original) {\n        const instrumentation = this;\n        const patchedFunction = function (...args) {\n            const activeContext = api.context.active();\n            if (!instrumentation._shouldTrace(activeContext)) {\n                return original.apply(this, args);\n            }\n            if (instrumentation._runCreateHook(functionName, {\n                args: args,\n            }) === false) {\n                return api.context.with((0, core_1.suppressTracing)(activeContext), original, this, ...args);\n            }\n            const span = instrumentation.tracer.startSpan(`fs ${functionName}`);\n            try {\n                // Suppress tracing for internal fs calls\n                const res = api.context.with((0, core_1.suppressTracing)(api.trace.setSpan(activeContext, span)), original, this, ...args);\n                instrumentation._runEndHook(functionName, { args: args, span });\n                return res;\n            }\n            catch (error) {\n                span.recordException(error);\n                span.setStatus({\n                    message: error.message,\n                    code: api.SpanStatusCode.ERROR,\n                });\n                instrumentation._runEndHook(functionName, { args: args, span, error });\n                throw error;\n            }\n            finally {\n                span.end();\n            }\n        };\n        return patchedFunctionWithOriginalProperties(patchedFunction, original);\n    }\n    _patchCallbackFunction(functionName, original) {\n        const instrumentation = this;\n        const patchedFunction = function (...args) {\n            const activeContext = api.context.active();\n            if (!instrumentation._shouldTrace(activeContext)) {\n                return original.apply(this, args);\n            }\n            if (instrumentation._runCreateHook(functionName, {\n                args: args,\n            }) === false) {\n                return api.context.with((0, core_1.suppressTracing)(activeContext), original, this, ...args);\n            }\n            const lastIdx = args.length - 1;\n            const cb = args[lastIdx];\n            if (typeof cb === 'function') {\n                const span = instrumentation.tracer.startSpan(`fs ${functionName}`);\n                // Return to the context active during the call in the callback\n                args[lastIdx] = api.context.bind(activeContext, function (error) {\n                    if (error) {\n                        span.recordException(error);\n                        span.setStatus({\n                            message: error.message,\n                            code: api.SpanStatusCode.ERROR,\n                        });\n                    }\n                    instrumentation._runEndHook(functionName, {\n                        args: args,\n                        span,\n                        error,\n                    });\n                    span.end();\n                    return cb.apply(this, arguments);\n                });\n                try {\n                    // Suppress tracing for internal fs calls\n                    return api.context.with((0, core_1.suppressTracing)(api.trace.setSpan(activeContext, span)), original, this, ...args);\n                }\n                catch (error) {\n                    span.recordException(error);\n                    span.setStatus({\n                        message: error.message,\n                        code: api.SpanStatusCode.ERROR,\n                    });\n                    instrumentation._runEndHook(functionName, {\n                        args: args,\n                        span,\n                        error,\n                    });\n                    span.end();\n                    throw error;\n                }\n            }\n            else {\n                // TODO: what to do if we are pretty sure it's going to throw\n                return original.apply(this, args);\n            }\n        };\n        return patchedFunctionWithOriginalProperties(patchedFunction, original);\n    }\n    _patchExistsCallbackFunction(functionName, original) {\n        const instrumentation = this;\n        const patchedFunction = function (...args) {\n            const activeContext = api.context.active();\n            if (!instrumentation._shouldTrace(activeContext)) {\n                return original.apply(this, args);\n            }\n            if (instrumentation._runCreateHook(functionName, {\n                args: args,\n            }) === false) {\n                return api.context.with((0, core_1.suppressTracing)(activeContext), original, this, ...args);\n            }\n            const lastIdx = args.length - 1;\n            const cb = args[lastIdx];\n            if (typeof cb === 'function') {\n                const span = instrumentation.tracer.startSpan(`fs ${functionName}`);\n                // Return to the context active during the call in the callback\n                args[lastIdx] = api.context.bind(activeContext, function () {\n                    // `exists` never calls the callback with an error\n                    instrumentation._runEndHook(functionName, {\n                        args: args,\n                        span,\n                    });\n                    span.end();\n                    return cb.apply(this, arguments);\n                });\n                try {\n                    // Suppress tracing for internal fs calls\n                    return api.context.with((0, core_1.suppressTracing)(api.trace.setSpan(activeContext, span)), original, this, ...args);\n                }\n                catch (error) {\n                    span.recordException(error);\n                    span.setStatus({\n                        message: error.message,\n                        code: api.SpanStatusCode.ERROR,\n                    });\n                    instrumentation._runEndHook(functionName, {\n                        args: args,\n                        span,\n                        error,\n                    });\n                    span.end();\n                    throw error;\n                }\n            }\n            else {\n                return original.apply(this, args);\n            }\n        };\n        const functionWithOriginalProperties = patchedFunctionWithOriginalProperties(patchedFunction, original);\n        // `exists` has a custom promisify function because of the inconsistent signature\n        // replicating that on the patched function\n        const promisified = function (path) {\n            return new Promise(resolve => functionWithOriginalProperties(path, resolve));\n        };\n        Object.defineProperty(promisified, 'name', { value: functionName });\n        Object.defineProperty(functionWithOriginalProperties, util_1.promisify.custom, {\n            value: promisified,\n        });\n        return functionWithOriginalProperties;\n    }\n    _patchPromiseFunction(functionName, original) {\n        const instrumentation = this;\n        const patchedFunction = async function (...args) {\n            const activeContext = api.context.active();\n            if (!instrumentation._shouldTrace(activeContext)) {\n                return original.apply(this, args);\n            }\n            if (instrumentation._runCreateHook(functionName, {\n                args: args,\n            }) === false) {\n                return api.context.with((0, core_1.suppressTracing)(activeContext), original, this, ...args);\n            }\n            const span = instrumentation.tracer.startSpan(`fs ${functionName}`);\n            try {\n                // Suppress tracing for internal fs calls\n                const res = await api.context.with((0, core_1.suppressTracing)(api.trace.setSpan(activeContext, span)), original, this, ...args);\n                instrumentation._runEndHook(functionName, { args: args, span });\n                return res;\n            }\n            catch (error) {\n                span.recordException(error);\n                span.setStatus({\n                    message: error.message,\n                    code: api.SpanStatusCode.ERROR,\n                });\n                instrumentation._runEndHook(functionName, { args: args, span, error });\n                throw error;\n            }\n            finally {\n                span.end();\n            }\n        };\n        return patchedFunctionWithOriginalProperties(patchedFunction, original);\n    }\n    _runCreateHook(...args) {\n        const { createHook } = this.getConfig();\n        if (typeof createHook === 'function') {\n            try {\n                return createHook(...args);\n            }\n            catch (e) {\n                this._diag.error('caught createHook error', e);\n            }\n        }\n        return true;\n    }\n    _runEndHook(...args) {\n        const { endHook } = this.getConfig();\n        if (typeof endHook === 'function') {\n            try {\n                endHook(...args);\n            }\n            catch (e) {\n                this._diag.error('caught endHook error', e);\n            }\n        }\n    }\n    _shouldTrace(context) {\n        if ((0, core_1.isTracingSuppressed)(context)) {\n            // Performance optimization. Avoid creating additional contexts and spans\n            // if we already know that the tracing is being suppressed.\n            return false;\n        }\n        const { requireParentSpan } = this.getConfig();\n        if (requireParentSpan) {\n            const parentSpan = api.trace.getSpan(context);\n            if (parentSpan == null) {\n                return false;\n            }\n        }\n        return true;\n    }\n}\nexports.FsInstrumentation = FsInstrumentation;\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/instrumentation.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/types.js":
/*!***************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/types.js ***!
  \***************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9Ab3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfYzlhNjg5MzNmZjJmMTVmY2IxMWNmOTJiYTdhOTAyYTcvbm9kZV9tb2R1bGVzL0BvcGVudGVsZW1ldHJ5L2luc3RydW1lbnRhdGlvbi1mcy9idWlsZC9zcmMvdHlwZXMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYW53YXJcXERvY3VtZW50c1xcQ3ViZW50d2ViXFxjdWJlbnRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF9jOWE2ODkzM2ZmMmYxNWZjYjExY2Y5MmJhN2E5MDJhN1xcbm9kZV9tb2R1bGVzXFxAb3BlbnRlbGVtZXRyeVxcaW5zdHJ1bWVudGF0aW9uLWZzXFxidWlsZFxcc3JjXFx0eXBlcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXR5cGVzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/types.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/utils.js":
/*!***************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/utils.js ***!
  \***************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.indexFs = exports.splitTwoLevels = void 0;\nfunction splitTwoLevels(functionName) {\n    const memberParts = functionName.split('.');\n    if (memberParts.length > 1) {\n        if (memberParts.length !== 2)\n            throw Error(`Invalid member function name ${functionName}`);\n        return memberParts;\n    }\n    else {\n        return [functionName];\n    }\n}\nexports.splitTwoLevels = splitTwoLevels;\nfunction indexFs(fs, member) {\n    if (!member)\n        throw new Error(JSON.stringify({ member }));\n    const splitResult = splitTwoLevels(member);\n    const [functionName1, functionName2] = splitResult;\n    if (functionName2) {\n        return {\n            objectToPatch: fs[functionName1],\n            functionNameToPatch: functionName2,\n        };\n    }\n    else {\n        return {\n            objectToPatch: fs,\n            functionNameToPatch: functionName1,\n        };\n    }\n}\nexports.indexFs = indexFs;\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/utils.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/version.js":
/*!*****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/version.js ***!
  \*****************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.19.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-fs';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/version.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/constants.js":
/*!*******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/constants.js ***!
  \*******************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.SYNC_FUNCTIONS = exports.CALLBACK_FUNCTIONS = exports.PROMISE_FUNCTIONS = void 0;\nexports.PROMISE_FUNCTIONS = [\n    'access',\n    'appendFile',\n    'chmod',\n    'chown',\n    'copyFile',\n    'cp',\n    'lchown',\n    'link',\n    'lstat',\n    'lutimes',\n    'mkdir',\n    'mkdtemp',\n    'open',\n    'opendir',\n    'readdir',\n    'readFile',\n    'readlink',\n    'realpath',\n    'rename',\n    'rm',\n    'rmdir',\n    'stat',\n    'symlink',\n    'truncate',\n    'unlink',\n    'utimes',\n    'writeFile',\n    // 'lchmod', // only implemented on macOS\n];\nexports.CALLBACK_FUNCTIONS = [\n    'access',\n    'appendFile',\n    'chmod',\n    'chown',\n    'copyFile',\n    'cp',\n    'exists',\n    'lchown',\n    'link',\n    'lstat',\n    'lutimes',\n    'mkdir',\n    'mkdtemp',\n    'open',\n    'opendir',\n    'readdir',\n    'readFile',\n    'readlink',\n    'realpath',\n    'realpath.native',\n    'rename',\n    'rm',\n    'rmdir',\n    'stat',\n    'symlink',\n    'truncate',\n    'unlink',\n    'utimes',\n    'writeFile',\n    // 'close', // functions on file descriptor\n    // 'fchmod', // functions on file descriptor\n    // 'fchown', // functions on file descriptor\n    // 'fdatasync', // functions on file descriptor\n    // 'fstat', // functions on file descriptor\n    // 'fsync', // functions on file descriptor\n    // 'ftruncate', // functions on file descriptor\n    // 'futimes', // functions on file descriptor\n    // 'lchmod', // only implemented on macOS\n    // 'read', // functions on file descriptor\n    // 'readv', // functions on file descriptor\n    // 'write', // functions on file descriptor\n    // 'writev', // functions on file descriptor\n];\nexports.SYNC_FUNCTIONS = [\n    'accessSync',\n    'appendFileSync',\n    'chmodSync',\n    'chownSync',\n    'copyFileSync',\n    'cpSync',\n    'existsSync',\n    'lchownSync',\n    'linkSync',\n    'lstatSync',\n    'lutimesSync',\n    'mkdirSync',\n    'mkdtempSync',\n    'opendirSync',\n    'openSync',\n    'readdirSync',\n    'readFileSync',\n    'readlinkSync',\n    'realpathSync',\n    'realpathSync.native',\n    'renameSync',\n    'rmdirSync',\n    'rmSync',\n    'statSync',\n    'symlinkSync',\n    'truncateSync',\n    'unlinkSync',\n    'utimesSync',\n    'writeFileSync',\n    // 'closeSync', // functions on file descriptor\n    // 'fchmodSync', // functions on file descriptor\n    // 'fchownSync', // functions on file descriptor\n    // 'fdatasyncSync', // functions on file descriptor\n    // 'fstatSync', // functions on file descriptor\n    // 'fsyncSync', // functions on file descriptor\n    // 'ftruncateSync', // functions on file descriptor\n    // 'futimesSync', // functions on file descriptor\n    // 'lchmodSync', // only implemented on macOS\n    // 'readSync', // functions on file descriptor\n    // 'readvSync', // functions on file descriptor\n    // 'writeSync', // functions on file descriptor\n    // 'writevSync', // functions on file descriptor\n];\n//# sourceMappingURL=constants.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/constants.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/index.js":
/*!***************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/index.js ***!
  \***************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/instrumentation.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/types.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/index.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/instrumentation.js":
/*!*************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/instrumentation.js ***!
  \*************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.FsInstrumentation = void 0;\nconst api = __webpack_require__(/*! @opentelemetry/api */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst core_1 = __webpack_require__(/*! @opentelemetry/core */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+core@1.30.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/core/build/esm/index.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/version.js\");\nconst constants_1 = __webpack_require__(/*! ./constants */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/constants.js\");\nconst util_1 = __webpack_require__(/*! util */ \"util\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/utils.js\");\n/**\n * This is important for 2-level functions like `realpath.native` to retain the 2nd-level\n * when patching the 1st-level.\n */\nfunction patchedFunctionWithOriginalProperties(patchedFunction, original) {\n    return Object.assign(patchedFunction, original);\n}\nclass FsInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, config);\n    }\n    init() {\n        return [\n            new instrumentation_1.InstrumentationNodeModuleDefinition('fs', ['*'], (fs) => {\n                for (const fName of constants_1.SYNC_FUNCTIONS) {\n                    const { objectToPatch, functionNameToPatch } = (0, utils_1.indexFs)(fs, fName);\n                    if ((0, instrumentation_1.isWrapped)(objectToPatch[functionNameToPatch])) {\n                        this._unwrap(objectToPatch, functionNameToPatch);\n                    }\n                    this._wrap(objectToPatch, functionNameToPatch, this._patchSyncFunction.bind(this, fName));\n                }\n                for (const fName of constants_1.CALLBACK_FUNCTIONS) {\n                    const { objectToPatch, functionNameToPatch } = (0, utils_1.indexFs)(fs, fName);\n                    if ((0, instrumentation_1.isWrapped)(objectToPatch[functionNameToPatch])) {\n                        this._unwrap(objectToPatch, functionNameToPatch);\n                    }\n                    if (fName === 'exists') {\n                        // handling separately because of the inconsistent cb style:\n                        // `exists` doesn't have error as the first argument, but the result\n                        this._wrap(objectToPatch, functionNameToPatch, this._patchExistsCallbackFunction.bind(this, fName));\n                        continue;\n                    }\n                    this._wrap(objectToPatch, functionNameToPatch, this._patchCallbackFunction.bind(this, fName));\n                }\n                for (const fName of constants_1.PROMISE_FUNCTIONS) {\n                    if ((0, instrumentation_1.isWrapped)(fs.promises[fName])) {\n                        this._unwrap(fs.promises, fName);\n                    }\n                    this._wrap(fs.promises, fName, this._patchPromiseFunction.bind(this, fName));\n                }\n                return fs;\n            }, (fs) => {\n                if (fs === undefined)\n                    return;\n                for (const fName of constants_1.SYNC_FUNCTIONS) {\n                    const { objectToPatch, functionNameToPatch } = (0, utils_1.indexFs)(fs, fName);\n                    if ((0, instrumentation_1.isWrapped)(objectToPatch[functionNameToPatch])) {\n                        this._unwrap(objectToPatch, functionNameToPatch);\n                    }\n                }\n                for (const fName of constants_1.CALLBACK_FUNCTIONS) {\n                    const { objectToPatch, functionNameToPatch } = (0, utils_1.indexFs)(fs, fName);\n                    if ((0, instrumentation_1.isWrapped)(objectToPatch[functionNameToPatch])) {\n                        this._unwrap(objectToPatch, functionNameToPatch);\n                    }\n                }\n                for (const fName of constants_1.PROMISE_FUNCTIONS) {\n                    if ((0, instrumentation_1.isWrapped)(fs.promises[fName])) {\n                        this._unwrap(fs.promises, fName);\n                    }\n                }\n            }),\n            new instrumentation_1.InstrumentationNodeModuleDefinition('fs/promises', ['*'], (fsPromises) => {\n                for (const fName of constants_1.PROMISE_FUNCTIONS) {\n                    if ((0, instrumentation_1.isWrapped)(fsPromises[fName])) {\n                        this._unwrap(fsPromises, fName);\n                    }\n                    this._wrap(fsPromises, fName, this._patchPromiseFunction.bind(this, fName));\n                }\n                return fsPromises;\n            }, (fsPromises) => {\n                if (fsPromises === undefined)\n                    return;\n                for (const fName of constants_1.PROMISE_FUNCTIONS) {\n                    if ((0, instrumentation_1.isWrapped)(fsPromises[fName])) {\n                        this._unwrap(fsPromises, fName);\n                    }\n                }\n            }),\n        ];\n    }\n    _patchSyncFunction(functionName, original) {\n        const instrumentation = this;\n        const patchedFunction = function (...args) {\n            const activeContext = api.context.active();\n            if (!instrumentation._shouldTrace(activeContext)) {\n                return original.apply(this, args);\n            }\n            if (instrumentation._runCreateHook(functionName, {\n                args: args,\n            }) === false) {\n                return api.context.with((0, core_1.suppressTracing)(activeContext), original, this, ...args);\n            }\n            const span = instrumentation.tracer.startSpan(`fs ${functionName}`);\n            try {\n                // Suppress tracing for internal fs calls\n                const res = api.context.with((0, core_1.suppressTracing)(api.trace.setSpan(activeContext, span)), original, this, ...args);\n                instrumentation._runEndHook(functionName, { args: args, span });\n                return res;\n            }\n            catch (error) {\n                span.recordException(error);\n                span.setStatus({\n                    message: error.message,\n                    code: api.SpanStatusCode.ERROR,\n                });\n                instrumentation._runEndHook(functionName, { args: args, span, error });\n                throw error;\n            }\n            finally {\n                span.end();\n            }\n        };\n        return patchedFunctionWithOriginalProperties(patchedFunction, original);\n    }\n    _patchCallbackFunction(functionName, original) {\n        const instrumentation = this;\n        const patchedFunction = function (...args) {\n            const activeContext = api.context.active();\n            if (!instrumentation._shouldTrace(activeContext)) {\n                return original.apply(this, args);\n            }\n            if (instrumentation._runCreateHook(functionName, {\n                args: args,\n            }) === false) {\n                return api.context.with((0, core_1.suppressTracing)(activeContext), original, this, ...args);\n            }\n            const lastIdx = args.length - 1;\n            const cb = args[lastIdx];\n            if (typeof cb === 'function') {\n                const span = instrumentation.tracer.startSpan(`fs ${functionName}`);\n                // Return to the context active during the call in the callback\n                args[lastIdx] = api.context.bind(activeContext, function (error) {\n                    if (error) {\n                        span.recordException(error);\n                        span.setStatus({\n                            message: error.message,\n                            code: api.SpanStatusCode.ERROR,\n                        });\n                    }\n                    instrumentation._runEndHook(functionName, {\n                        args: args,\n                        span,\n                        error,\n                    });\n                    span.end();\n                    return cb.apply(this, arguments);\n                });\n                try {\n                    // Suppress tracing for internal fs calls\n                    return api.context.with((0, core_1.suppressTracing)(api.trace.setSpan(activeContext, span)), original, this, ...args);\n                }\n                catch (error) {\n                    span.recordException(error);\n                    span.setStatus({\n                        message: error.message,\n                        code: api.SpanStatusCode.ERROR,\n                    });\n                    instrumentation._runEndHook(functionName, {\n                        args: args,\n                        span,\n                        error,\n                    });\n                    span.end();\n                    throw error;\n                }\n            }\n            else {\n                // TODO: what to do if we are pretty sure it's going to throw\n                return original.apply(this, args);\n            }\n        };\n        return patchedFunctionWithOriginalProperties(patchedFunction, original);\n    }\n    _patchExistsCallbackFunction(functionName, original) {\n        const instrumentation = this;\n        const patchedFunction = function (...args) {\n            const activeContext = api.context.active();\n            if (!instrumentation._shouldTrace(activeContext)) {\n                return original.apply(this, args);\n            }\n            if (instrumentation._runCreateHook(functionName, {\n                args: args,\n            }) === false) {\n                return api.context.with((0, core_1.suppressTracing)(activeContext), original, this, ...args);\n            }\n            const lastIdx = args.length - 1;\n            const cb = args[lastIdx];\n            if (typeof cb === 'function') {\n                const span = instrumentation.tracer.startSpan(`fs ${functionName}`);\n                // Return to the context active during the call in the callback\n                args[lastIdx] = api.context.bind(activeContext, function () {\n                    // `exists` never calls the callback with an error\n                    instrumentation._runEndHook(functionName, {\n                        args: args,\n                        span,\n                    });\n                    span.end();\n                    return cb.apply(this, arguments);\n                });\n                try {\n                    // Suppress tracing for internal fs calls\n                    return api.context.with((0, core_1.suppressTracing)(api.trace.setSpan(activeContext, span)), original, this, ...args);\n                }\n                catch (error) {\n                    span.recordException(error);\n                    span.setStatus({\n                        message: error.message,\n                        code: api.SpanStatusCode.ERROR,\n                    });\n                    instrumentation._runEndHook(functionName, {\n                        args: args,\n                        span,\n                        error,\n                    });\n                    span.end();\n                    throw error;\n                }\n            }\n            else {\n                return original.apply(this, args);\n            }\n        };\n        const functionWithOriginalProperties = patchedFunctionWithOriginalProperties(patchedFunction, original);\n        // `exists` has a custom promisify function because of the inconsistent signature\n        // replicating that on the patched function\n        const promisified = function (path) {\n            return new Promise(resolve => functionWithOriginalProperties(path, resolve));\n        };\n        Object.defineProperty(promisified, 'name', { value: functionName });\n        Object.defineProperty(functionWithOriginalProperties, util_1.promisify.custom, {\n            value: promisified,\n        });\n        return functionWithOriginalProperties;\n    }\n    _patchPromiseFunction(functionName, original) {\n        const instrumentation = this;\n        const patchedFunction = async function (...args) {\n            const activeContext = api.context.active();\n            if (!instrumentation._shouldTrace(activeContext)) {\n                return original.apply(this, args);\n            }\n            if (instrumentation._runCreateHook(functionName, {\n                args: args,\n            }) === false) {\n                return api.context.with((0, core_1.suppressTracing)(activeContext), original, this, ...args);\n            }\n            const span = instrumentation.tracer.startSpan(`fs ${functionName}`);\n            try {\n                // Suppress tracing for internal fs calls\n                const res = await api.context.with((0, core_1.suppressTracing)(api.trace.setSpan(activeContext, span)), original, this, ...args);\n                instrumentation._runEndHook(functionName, { args: args, span });\n                return res;\n            }\n            catch (error) {\n                span.recordException(error);\n                span.setStatus({\n                    message: error.message,\n                    code: api.SpanStatusCode.ERROR,\n                });\n                instrumentation._runEndHook(functionName, { args: args, span, error });\n                throw error;\n            }\n            finally {\n                span.end();\n            }\n        };\n        return patchedFunctionWithOriginalProperties(patchedFunction, original);\n    }\n    _runCreateHook(...args) {\n        const { createHook } = this.getConfig();\n        if (typeof createHook === 'function') {\n            try {\n                return createHook(...args);\n            }\n            catch (e) {\n                this._diag.error('caught createHook error', e);\n            }\n        }\n        return true;\n    }\n    _runEndHook(...args) {\n        const { endHook } = this.getConfig();\n        if (typeof endHook === 'function') {\n            try {\n                endHook(...args);\n            }\n            catch (e) {\n                this._diag.error('caught endHook error', e);\n            }\n        }\n    }\n    _shouldTrace(context) {\n        if ((0, core_1.isTracingSuppressed)(context)) {\n            // Performance optimization. Avoid creating additional contexts and spans\n            // if we already know that the tracing is being suppressed.\n            return false;\n        }\n        const { requireParentSpan } = this.getConfig();\n        if (requireParentSpan) {\n            const parentSpan = api.trace.getSpan(context);\n            if (parentSpan == null) {\n                return false;\n            }\n        }\n        return true;\n    }\n}\nexports.FsInstrumentation = FsInstrumentation;\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF9jOWE2ODkzM2ZmMmYxNWZjYjExY2Y5MmJhN2E5MDJhNy9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLWZzL2J1aWxkL3NyYy9pbnN0cnVtZW50YXRpb24uanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QseUJBQXlCO0FBQ3pCLFlBQVksbUJBQU8sQ0FBQyxzSUFBb0I7QUFDeEMsZUFBZSxtQkFBTyxDQUFDLG1LQUFxQjtBQUM1QywwQkFBMEIsbUJBQU8sQ0FBQyxrTUFBZ0M7QUFDbEU7QUFDQSxrQkFBa0IsbUJBQU8sQ0FBQyxrTEFBVztBQUNyQyxvQkFBb0IsbUJBQU8sQ0FBQyxzTEFBYTtBQUN6QyxlQUFlLG1CQUFPLENBQUMsa0JBQU07QUFDN0IsZ0JBQWdCLG1CQUFPLENBQUMsOEtBQVM7QUFDakM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJCQUEyQjtBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0QkFBNEIscUNBQXFDO0FBQ2pFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRCQUE0QixxQ0FBcUM7QUFDakU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBLDRCQUE0QixxQ0FBcUM7QUFDakU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRCQUE0QixxQ0FBcUM7QUFDakU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0EsZ0VBQWdFLGFBQWE7QUFDN0U7QUFDQTtBQUNBO0FBQ0EsNERBQTRELGtCQUFrQjtBQUM5RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQiw0REFBNEQseUJBQXlCO0FBQ3JGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9FQUFvRSxhQUFhO0FBQ2pGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUJBQXlCO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUI7QUFDckI7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUI7QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUI7QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0VBQW9FLGFBQWE7QUFDakY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxREFBcUQscUJBQXFCO0FBQzFFO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0EsZ0VBQWdFLGFBQWE7QUFDN0U7QUFDQTtBQUNBO0FBQ0EsNERBQTRELGtCQUFrQjtBQUM5RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQiw0REFBNEQseUJBQXlCO0FBQ3JGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQixhQUFhO0FBQzdCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IsVUFBVTtBQUMxQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0Isb0JBQW9CO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlCQUF5QjtBQUN6QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbndhclxcRG9jdW1lbnRzXFxDdWJlbnR3ZWJcXGN1YmVudFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQG9wZW50ZWxlbWV0cnkraW5zdHJ1bWVudGF0X2M5YTY4OTMzZmYyZjE1ZmNiMTFjZjkyYmE3YTkwMmE3XFxub2RlX21vZHVsZXNcXEBvcGVudGVsZW1ldHJ5XFxpbnN0cnVtZW50YXRpb24tZnNcXGJ1aWxkXFxzcmNcXGluc3RydW1lbnRhdGlvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbi8qXG4gKiBDb3B5cmlnaHQgVGhlIE9wZW5UZWxlbWV0cnkgQXV0aG9yc1xuICpcbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4gKiB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuXG4gKiBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbiAqXG4gKiAgICAgIGh0dHBzOi8vd3d3LmFwYWNoZS5vcmcvbGljZW5zZXMvTElDRU5TRS0yLjBcbiAqXG4gKiBVbmxlc3MgcmVxdWlyZWQgYnkgYXBwbGljYWJsZSBsYXcgb3IgYWdyZWVkIHRvIGluIHdyaXRpbmcsIHNvZnR3YXJlXG4gKiBkaXN0cmlidXRlZCB1bmRlciB0aGUgTGljZW5zZSBpcyBkaXN0cmlidXRlZCBvbiBhbiBcIkFTIElTXCIgQkFTSVMsXG4gKiBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgQ09ORElUSU9OUyBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC5cbiAqIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmRcbiAqIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuICovXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLkZzSW5zdHJ1bWVudGF0aW9uID0gdm9pZCAwO1xuY29uc3QgYXBpID0gcmVxdWlyZShcIkBvcGVudGVsZW1ldHJ5L2FwaVwiKTtcbmNvbnN0IGNvcmVfMSA9IHJlcXVpcmUoXCJAb3BlbnRlbGVtZXRyeS9jb3JlXCIpO1xuY29uc3QgaW5zdHJ1bWVudGF0aW9uXzEgPSByZXF1aXJlKFwiQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uXCIpO1xuLyoqIEBrbmlwaWdub3JlICovXG5jb25zdCB2ZXJzaW9uXzEgPSByZXF1aXJlKFwiLi92ZXJzaW9uXCIpO1xuY29uc3QgY29uc3RhbnRzXzEgPSByZXF1aXJlKFwiLi9jb25zdGFudHNcIik7XG5jb25zdCB1dGlsXzEgPSByZXF1aXJlKFwidXRpbFwiKTtcbmNvbnN0IHV0aWxzXzEgPSByZXF1aXJlKFwiLi91dGlsc1wiKTtcbi8qKlxuICogVGhpcyBpcyBpbXBvcnRhbnQgZm9yIDItbGV2ZWwgZnVuY3Rpb25zIGxpa2UgYHJlYWxwYXRoLm5hdGl2ZWAgdG8gcmV0YWluIHRoZSAybmQtbGV2ZWxcbiAqIHdoZW4gcGF0Y2hpbmcgdGhlIDFzdC1sZXZlbC5cbiAqL1xuZnVuY3Rpb24gcGF0Y2hlZEZ1bmN0aW9uV2l0aE9yaWdpbmFsUHJvcGVydGllcyhwYXRjaGVkRnVuY3Rpb24sIG9yaWdpbmFsKSB7XG4gICAgcmV0dXJuIE9iamVjdC5hc3NpZ24ocGF0Y2hlZEZ1bmN0aW9uLCBvcmlnaW5hbCk7XG59XG5jbGFzcyBGc0luc3RydW1lbnRhdGlvbiBleHRlbmRzIGluc3RydW1lbnRhdGlvbl8xLkluc3RydW1lbnRhdGlvbkJhc2Uge1xuICAgIGNvbnN0cnVjdG9yKGNvbmZpZyA9IHt9KSB7XG4gICAgICAgIHN1cGVyKHZlcnNpb25fMS5QQUNLQUdFX05BTUUsIHZlcnNpb25fMS5QQUNLQUdFX1ZFUlNJT04sIGNvbmZpZyk7XG4gICAgfVxuICAgIGluaXQoKSB7XG4gICAgICAgIHJldHVybiBbXG4gICAgICAgICAgICBuZXcgaW5zdHJ1bWVudGF0aW9uXzEuSW5zdHJ1bWVudGF0aW9uTm9kZU1vZHVsZURlZmluaXRpb24oJ2ZzJywgWycqJ10sIChmcykgPT4ge1xuICAgICAgICAgICAgICAgIGZvciAoY29uc3QgZk5hbWUgb2YgY29uc3RhbnRzXzEuU1lOQ19GVU5DVElPTlMpIHtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgeyBvYmplY3RUb1BhdGNoLCBmdW5jdGlvbk5hbWVUb1BhdGNoIH0gPSAoMCwgdXRpbHNfMS5pbmRleEZzKShmcywgZk5hbWUpO1xuICAgICAgICAgICAgICAgICAgICBpZiAoKDAsIGluc3RydW1lbnRhdGlvbl8xLmlzV3JhcHBlZCkob2JqZWN0VG9QYXRjaFtmdW5jdGlvbk5hbWVUb1BhdGNoXSkpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuX3Vud3JhcChvYmplY3RUb1BhdGNoLCBmdW5jdGlvbk5hbWVUb1BhdGNoKTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICB0aGlzLl93cmFwKG9iamVjdFRvUGF0Y2gsIGZ1bmN0aW9uTmFtZVRvUGF0Y2gsIHRoaXMuX3BhdGNoU3luY0Z1bmN0aW9uLmJpbmQodGhpcywgZk5hbWUpKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZm9yIChjb25zdCBmTmFtZSBvZiBjb25zdGFudHNfMS5DQUxMQkFDS19GVU5DVElPTlMpIHtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgeyBvYmplY3RUb1BhdGNoLCBmdW5jdGlvbk5hbWVUb1BhdGNoIH0gPSAoMCwgdXRpbHNfMS5pbmRleEZzKShmcywgZk5hbWUpO1xuICAgICAgICAgICAgICAgICAgICBpZiAoKDAsIGluc3RydW1lbnRhdGlvbl8xLmlzV3JhcHBlZCkob2JqZWN0VG9QYXRjaFtmdW5jdGlvbk5hbWVUb1BhdGNoXSkpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuX3Vud3JhcChvYmplY3RUb1BhdGNoLCBmdW5jdGlvbk5hbWVUb1BhdGNoKTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBpZiAoZk5hbWUgPT09ICdleGlzdHMnKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBoYW5kbGluZyBzZXBhcmF0ZWx5IGJlY2F1c2Ugb2YgdGhlIGluY29uc2lzdGVudCBjYiBzdHlsZTpcbiAgICAgICAgICAgICAgICAgICAgICAgIC8vIGBleGlzdHNgIGRvZXNuJ3QgaGF2ZSBlcnJvciBhcyB0aGUgZmlyc3QgYXJndW1lbnQsIGJ1dCB0aGUgcmVzdWx0XG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzLl93cmFwKG9iamVjdFRvUGF0Y2gsIGZ1bmN0aW9uTmFtZVRvUGF0Y2gsIHRoaXMuX3BhdGNoRXhpc3RzQ2FsbGJhY2tGdW5jdGlvbi5iaW5kKHRoaXMsIGZOYW1lKSk7XG4gICAgICAgICAgICAgICAgICAgICAgICBjb250aW51ZTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICB0aGlzLl93cmFwKG9iamVjdFRvUGF0Y2gsIGZ1bmN0aW9uTmFtZVRvUGF0Y2gsIHRoaXMuX3BhdGNoQ2FsbGJhY2tGdW5jdGlvbi5iaW5kKHRoaXMsIGZOYW1lKSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGZvciAoY29uc3QgZk5hbWUgb2YgY29uc3RhbnRzXzEuUFJPTUlTRV9GVU5DVElPTlMpIHtcbiAgICAgICAgICAgICAgICAgICAgaWYgKCgwLCBpbnN0cnVtZW50YXRpb25fMS5pc1dyYXBwZWQpKGZzLnByb21pc2VzW2ZOYW1lXSkpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuX3Vud3JhcChmcy5wcm9taXNlcywgZk5hbWUpO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuX3dyYXAoZnMucHJvbWlzZXMsIGZOYW1lLCB0aGlzLl9wYXRjaFByb21pc2VGdW5jdGlvbi5iaW5kKHRoaXMsIGZOYW1lKSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIHJldHVybiBmcztcbiAgICAgICAgICAgIH0sIChmcykgPT4ge1xuICAgICAgICAgICAgICAgIGlmIChmcyA9PT0gdW5kZWZpbmVkKVxuICAgICAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICAgICAgZm9yIChjb25zdCBmTmFtZSBvZiBjb25zdGFudHNfMS5TWU5DX0ZVTkNUSU9OUykge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCB7IG9iamVjdFRvUGF0Y2gsIGZ1bmN0aW9uTmFtZVRvUGF0Y2ggfSA9ICgwLCB1dGlsc18xLmluZGV4RnMpKGZzLCBmTmFtZSk7XG4gICAgICAgICAgICAgICAgICAgIGlmICgoMCwgaW5zdHJ1bWVudGF0aW9uXzEuaXNXcmFwcGVkKShvYmplY3RUb1BhdGNoW2Z1bmN0aW9uTmFtZVRvUGF0Y2hdKSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5fdW53cmFwKG9iamVjdFRvUGF0Y2gsIGZ1bmN0aW9uTmFtZVRvUGF0Y2gpO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGZvciAoY29uc3QgZk5hbWUgb2YgY29uc3RhbnRzXzEuQ0FMTEJBQ0tfRlVOQ1RJT05TKSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHsgb2JqZWN0VG9QYXRjaCwgZnVuY3Rpb25OYW1lVG9QYXRjaCB9ID0gKDAsIHV0aWxzXzEuaW5kZXhGcykoZnMsIGZOYW1lKTtcbiAgICAgICAgICAgICAgICAgICAgaWYgKCgwLCBpbnN0cnVtZW50YXRpb25fMS5pc1dyYXBwZWQpKG9iamVjdFRvUGF0Y2hbZnVuY3Rpb25OYW1lVG9QYXRjaF0pKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzLl91bndyYXAob2JqZWN0VG9QYXRjaCwgZnVuY3Rpb25OYW1lVG9QYXRjaCk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZm9yIChjb25zdCBmTmFtZSBvZiBjb25zdGFudHNfMS5QUk9NSVNFX0ZVTkNUSU9OUykge1xuICAgICAgICAgICAgICAgICAgICBpZiAoKDAsIGluc3RydW1lbnRhdGlvbl8xLmlzV3JhcHBlZCkoZnMucHJvbWlzZXNbZk5hbWVdKSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5fdW53cmFwKGZzLnByb21pc2VzLCBmTmFtZSk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9KSxcbiAgICAgICAgICAgIG5ldyBpbnN0cnVtZW50YXRpb25fMS5JbnN0cnVtZW50YXRpb25Ob2RlTW9kdWxlRGVmaW5pdGlvbignZnMvcHJvbWlzZXMnLCBbJyonXSwgKGZzUHJvbWlzZXMpID0+IHtcbiAgICAgICAgICAgICAgICBmb3IgKGNvbnN0IGZOYW1lIG9mIGNvbnN0YW50c18xLlBST01JU0VfRlVOQ1RJT05TKSB7XG4gICAgICAgICAgICAgICAgICAgIGlmICgoMCwgaW5zdHJ1bWVudGF0aW9uXzEuaXNXcmFwcGVkKShmc1Byb21pc2VzW2ZOYW1lXSkpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuX3Vud3JhcChmc1Byb21pc2VzLCBmTmFtZSk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgdGhpcy5fd3JhcChmc1Byb21pc2VzLCBmTmFtZSwgdGhpcy5fcGF0Y2hQcm9taXNlRnVuY3Rpb24uYmluZCh0aGlzLCBmTmFtZSkpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICByZXR1cm4gZnNQcm9taXNlcztcbiAgICAgICAgICAgIH0sIChmc1Byb21pc2VzKSA9PiB7XG4gICAgICAgICAgICAgICAgaWYgKGZzUHJvbWlzZXMgPT09IHVuZGVmaW5lZClcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgICAgIGZvciAoY29uc3QgZk5hbWUgb2YgY29uc3RhbnRzXzEuUFJPTUlTRV9GVU5DVElPTlMpIHtcbiAgICAgICAgICAgICAgICAgICAgaWYgKCgwLCBpbnN0cnVtZW50YXRpb25fMS5pc1dyYXBwZWQpKGZzUHJvbWlzZXNbZk5hbWVdKSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5fdW53cmFwKGZzUHJvbWlzZXMsIGZOYW1lKTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0pLFxuICAgICAgICBdO1xuICAgIH1cbiAgICBfcGF0Y2hTeW5jRnVuY3Rpb24oZnVuY3Rpb25OYW1lLCBvcmlnaW5hbCkge1xuICAgICAgICBjb25zdCBpbnN0cnVtZW50YXRpb24gPSB0aGlzO1xuICAgICAgICBjb25zdCBwYXRjaGVkRnVuY3Rpb24gPSBmdW5jdGlvbiAoLi4uYXJncykge1xuICAgICAgICAgICAgY29uc3QgYWN0aXZlQ29udGV4dCA9IGFwaS5jb250ZXh0LmFjdGl2ZSgpO1xuICAgICAgICAgICAgaWYgKCFpbnN0cnVtZW50YXRpb24uX3Nob3VsZFRyYWNlKGFjdGl2ZUNvbnRleHQpKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIG9yaWdpbmFsLmFwcGx5KHRoaXMsIGFyZ3MpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKGluc3RydW1lbnRhdGlvbi5fcnVuQ3JlYXRlSG9vayhmdW5jdGlvbk5hbWUsIHtcbiAgICAgICAgICAgICAgICBhcmdzOiBhcmdzLFxuICAgICAgICAgICAgfSkgPT09IGZhbHNlKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGFwaS5jb250ZXh0LndpdGgoKDAsIGNvcmVfMS5zdXBwcmVzc1RyYWNpbmcpKGFjdGl2ZUNvbnRleHQpLCBvcmlnaW5hbCwgdGhpcywgLi4uYXJncyk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjb25zdCBzcGFuID0gaW5zdHJ1bWVudGF0aW9uLnRyYWNlci5zdGFydFNwYW4oYGZzICR7ZnVuY3Rpb25OYW1lfWApO1xuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICAvLyBTdXBwcmVzcyB0cmFjaW5nIGZvciBpbnRlcm5hbCBmcyBjYWxsc1xuICAgICAgICAgICAgICAgIGNvbnN0IHJlcyA9IGFwaS5jb250ZXh0LndpdGgoKDAsIGNvcmVfMS5zdXBwcmVzc1RyYWNpbmcpKGFwaS50cmFjZS5zZXRTcGFuKGFjdGl2ZUNvbnRleHQsIHNwYW4pKSwgb3JpZ2luYWwsIHRoaXMsIC4uLmFyZ3MpO1xuICAgICAgICAgICAgICAgIGluc3RydW1lbnRhdGlvbi5fcnVuRW5kSG9vayhmdW5jdGlvbk5hbWUsIHsgYXJnczogYXJncywgc3BhbiB9KTtcbiAgICAgICAgICAgICAgICByZXR1cm4gcmVzO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICAgICAgc3Bhbi5yZWNvcmRFeGNlcHRpb24oZXJyb3IpO1xuICAgICAgICAgICAgICAgIHNwYW4uc2V0U3RhdHVzKHtcbiAgICAgICAgICAgICAgICAgICAgbWVzc2FnZTogZXJyb3IubWVzc2FnZSxcbiAgICAgICAgICAgICAgICAgICAgY29kZTogYXBpLlNwYW5TdGF0dXNDb2RlLkVSUk9SLFxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgIGluc3RydW1lbnRhdGlvbi5fcnVuRW5kSG9vayhmdW5jdGlvbk5hbWUsIHsgYXJnczogYXJncywgc3BhbiwgZXJyb3IgfSk7XG4gICAgICAgICAgICAgICAgdGhyb3cgZXJyb3I7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBmaW5hbGx5IHtcbiAgICAgICAgICAgICAgICBzcGFuLmVuZCgpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gcGF0Y2hlZEZ1bmN0aW9uV2l0aE9yaWdpbmFsUHJvcGVydGllcyhwYXRjaGVkRnVuY3Rpb24sIG9yaWdpbmFsKTtcbiAgICB9XG4gICAgX3BhdGNoQ2FsbGJhY2tGdW5jdGlvbihmdW5jdGlvbk5hbWUsIG9yaWdpbmFsKSB7XG4gICAgICAgIGNvbnN0IGluc3RydW1lbnRhdGlvbiA9IHRoaXM7XG4gICAgICAgIGNvbnN0IHBhdGNoZWRGdW5jdGlvbiA9IGZ1bmN0aW9uICguLi5hcmdzKSB7XG4gICAgICAgICAgICBjb25zdCBhY3RpdmVDb250ZXh0ID0gYXBpLmNvbnRleHQuYWN0aXZlKCk7XG4gICAgICAgICAgICBpZiAoIWluc3RydW1lbnRhdGlvbi5fc2hvdWxkVHJhY2UoYWN0aXZlQ29udGV4dCkpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gb3JpZ2luYWwuYXBwbHkodGhpcywgYXJncyk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoaW5zdHJ1bWVudGF0aW9uLl9ydW5DcmVhdGVIb29rKGZ1bmN0aW9uTmFtZSwge1xuICAgICAgICAgICAgICAgIGFyZ3M6IGFyZ3MsXG4gICAgICAgICAgICB9KSA9PT0gZmFsc2UpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gYXBpLmNvbnRleHQud2l0aCgoMCwgY29yZV8xLnN1cHByZXNzVHJhY2luZykoYWN0aXZlQ29udGV4dCksIG9yaWdpbmFsLCB0aGlzLCAuLi5hcmdzKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNvbnN0IGxhc3RJZHggPSBhcmdzLmxlbmd0aCAtIDE7XG4gICAgICAgICAgICBjb25zdCBjYiA9IGFyZ3NbbGFzdElkeF07XG4gICAgICAgICAgICBpZiAodHlwZW9mIGNiID09PSAnZnVuY3Rpb24nKSB7XG4gICAgICAgICAgICAgICAgY29uc3Qgc3BhbiA9IGluc3RydW1lbnRhdGlvbi50cmFjZXIuc3RhcnRTcGFuKGBmcyAke2Z1bmN0aW9uTmFtZX1gKTtcbiAgICAgICAgICAgICAgICAvLyBSZXR1cm4gdG8gdGhlIGNvbnRleHQgYWN0aXZlIGR1cmluZyB0aGUgY2FsbCBpbiB0aGUgY2FsbGJhY2tcbiAgICAgICAgICAgICAgICBhcmdzW2xhc3RJZHhdID0gYXBpLmNvbnRleHQuYmluZChhY3RpdmVDb250ZXh0LCBmdW5jdGlvbiAoZXJyb3IpIHtcbiAgICAgICAgICAgICAgICAgICAgaWYgKGVycm9yKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBzcGFuLnJlY29yZEV4Y2VwdGlvbihlcnJvcik7XG4gICAgICAgICAgICAgICAgICAgICAgICBzcGFuLnNldFN0YXR1cyh7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbWVzc2FnZTogZXJyb3IubWVzc2FnZSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2RlOiBhcGkuU3BhblN0YXR1c0NvZGUuRVJST1IsXG4gICAgICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBpbnN0cnVtZW50YXRpb24uX3J1bkVuZEhvb2soZnVuY3Rpb25OYW1lLCB7XG4gICAgICAgICAgICAgICAgICAgICAgICBhcmdzOiBhcmdzLFxuICAgICAgICAgICAgICAgICAgICAgICAgc3BhbixcbiAgICAgICAgICAgICAgICAgICAgICAgIGVycm9yLFxuICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgc3Bhbi5lbmQoKTtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGNiLmFwcGx5KHRoaXMsIGFyZ3VtZW50cyk7XG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICAgICAgLy8gU3VwcHJlc3MgdHJhY2luZyBmb3IgaW50ZXJuYWwgZnMgY2FsbHNcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGFwaS5jb250ZXh0LndpdGgoKDAsIGNvcmVfMS5zdXBwcmVzc1RyYWNpbmcpKGFwaS50cmFjZS5zZXRTcGFuKGFjdGl2ZUNvbnRleHQsIHNwYW4pKSwgb3JpZ2luYWwsIHRoaXMsIC4uLmFyZ3MpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICAgICAgICAgICAgc3Bhbi5yZWNvcmRFeGNlcHRpb24oZXJyb3IpO1xuICAgICAgICAgICAgICAgICAgICBzcGFuLnNldFN0YXR1cyh7XG4gICAgICAgICAgICAgICAgICAgICAgICBtZXNzYWdlOiBlcnJvci5tZXNzYWdlLFxuICAgICAgICAgICAgICAgICAgICAgICAgY29kZTogYXBpLlNwYW5TdGF0dXNDb2RlLkVSUk9SLFxuICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgaW5zdHJ1bWVudGF0aW9uLl9ydW5FbmRIb29rKGZ1bmN0aW9uTmFtZSwge1xuICAgICAgICAgICAgICAgICAgICAgICAgYXJnczogYXJncyxcbiAgICAgICAgICAgICAgICAgICAgICAgIHNwYW4sXG4gICAgICAgICAgICAgICAgICAgICAgICBlcnJvcixcbiAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgICAgIHNwYW4uZW5kKCk7XG4gICAgICAgICAgICAgICAgICAgIHRocm93IGVycm9yO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgIC8vIFRPRE86IHdoYXQgdG8gZG8gaWYgd2UgYXJlIHByZXR0eSBzdXJlIGl0J3MgZ29pbmcgdG8gdGhyb3dcbiAgICAgICAgICAgICAgICByZXR1cm4gb3JpZ2luYWwuYXBwbHkodGhpcywgYXJncyk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH07XG4gICAgICAgIHJldHVybiBwYXRjaGVkRnVuY3Rpb25XaXRoT3JpZ2luYWxQcm9wZXJ0aWVzKHBhdGNoZWRGdW5jdGlvbiwgb3JpZ2luYWwpO1xuICAgIH1cbiAgICBfcGF0Y2hFeGlzdHNDYWxsYmFja0Z1bmN0aW9uKGZ1bmN0aW9uTmFtZSwgb3JpZ2luYWwpIHtcbiAgICAgICAgY29uc3QgaW5zdHJ1bWVudGF0aW9uID0gdGhpcztcbiAgICAgICAgY29uc3QgcGF0Y2hlZEZ1bmN0aW9uID0gZnVuY3Rpb24gKC4uLmFyZ3MpIHtcbiAgICAgICAgICAgIGNvbnN0IGFjdGl2ZUNvbnRleHQgPSBhcGkuY29udGV4dC5hY3RpdmUoKTtcbiAgICAgICAgICAgIGlmICghaW5zdHJ1bWVudGF0aW9uLl9zaG91bGRUcmFjZShhY3RpdmVDb250ZXh0KSkge1xuICAgICAgICAgICAgICAgIHJldHVybiBvcmlnaW5hbC5hcHBseSh0aGlzLCBhcmdzKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmIChpbnN0cnVtZW50YXRpb24uX3J1bkNyZWF0ZUhvb2soZnVuY3Rpb25OYW1lLCB7XG4gICAgICAgICAgICAgICAgYXJnczogYXJncyxcbiAgICAgICAgICAgIH0pID09PSBmYWxzZSkge1xuICAgICAgICAgICAgICAgIHJldHVybiBhcGkuY29udGV4dC53aXRoKCgwLCBjb3JlXzEuc3VwcHJlc3NUcmFjaW5nKShhY3RpdmVDb250ZXh0KSwgb3JpZ2luYWwsIHRoaXMsIC4uLmFyZ3MpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY29uc3QgbGFzdElkeCA9IGFyZ3MubGVuZ3RoIC0gMTtcbiAgICAgICAgICAgIGNvbnN0IGNiID0gYXJnc1tsYXN0SWR4XTtcbiAgICAgICAgICAgIGlmICh0eXBlb2YgY2IgPT09ICdmdW5jdGlvbicpIHtcbiAgICAgICAgICAgICAgICBjb25zdCBzcGFuID0gaW5zdHJ1bWVudGF0aW9uLnRyYWNlci5zdGFydFNwYW4oYGZzICR7ZnVuY3Rpb25OYW1lfWApO1xuICAgICAgICAgICAgICAgIC8vIFJldHVybiB0byB0aGUgY29udGV4dCBhY3RpdmUgZHVyaW5nIHRoZSBjYWxsIGluIHRoZSBjYWxsYmFja1xuICAgICAgICAgICAgICAgIGFyZ3NbbGFzdElkeF0gPSBhcGkuY29udGV4dC5iaW5kKGFjdGl2ZUNvbnRleHQsIGZ1bmN0aW9uICgpIHtcbiAgICAgICAgICAgICAgICAgICAgLy8gYGV4aXN0c2AgbmV2ZXIgY2FsbHMgdGhlIGNhbGxiYWNrIHdpdGggYW4gZXJyb3JcbiAgICAgICAgICAgICAgICAgICAgaW5zdHJ1bWVudGF0aW9uLl9ydW5FbmRIb29rKGZ1bmN0aW9uTmFtZSwge1xuICAgICAgICAgICAgICAgICAgICAgICAgYXJnczogYXJncyxcbiAgICAgICAgICAgICAgICAgICAgICAgIHNwYW4sXG4gICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgICAgICBzcGFuLmVuZCgpO1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4gY2IuYXBwbHkodGhpcywgYXJndW1lbnRzKTtcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgICAgICAvLyBTdXBwcmVzcyB0cmFjaW5nIGZvciBpbnRlcm5hbCBmcyBjYWxsc1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4gYXBpLmNvbnRleHQud2l0aCgoMCwgY29yZV8xLnN1cHByZXNzVHJhY2luZykoYXBpLnRyYWNlLnNldFNwYW4oYWN0aXZlQ29udGV4dCwgc3BhbikpLCBvcmlnaW5hbCwgdGhpcywgLi4uYXJncyk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgICAgICAgICBzcGFuLnJlY29yZEV4Y2VwdGlvbihlcnJvcik7XG4gICAgICAgICAgICAgICAgICAgIHNwYW4uc2V0U3RhdHVzKHtcbiAgICAgICAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6IGVycm9yLm1lc3NhZ2UsXG4gICAgICAgICAgICAgICAgICAgICAgICBjb2RlOiBhcGkuU3BhblN0YXR1c0NvZGUuRVJST1IsXG4gICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgICAgICBpbnN0cnVtZW50YXRpb24uX3J1bkVuZEhvb2soZnVuY3Rpb25OYW1lLCB7XG4gICAgICAgICAgICAgICAgICAgICAgICBhcmdzOiBhcmdzLFxuICAgICAgICAgICAgICAgICAgICAgICAgc3BhbixcbiAgICAgICAgICAgICAgICAgICAgICAgIGVycm9yLFxuICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgc3Bhbi5lbmQoKTtcbiAgICAgICAgICAgICAgICAgICAgdGhyb3cgZXJyb3I7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIG9yaWdpbmFsLmFwcGx5KHRoaXMsIGFyZ3MpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9O1xuICAgICAgICBjb25zdCBmdW5jdGlvbldpdGhPcmlnaW5hbFByb3BlcnRpZXMgPSBwYXRjaGVkRnVuY3Rpb25XaXRoT3JpZ2luYWxQcm9wZXJ0aWVzKHBhdGNoZWRGdW5jdGlvbiwgb3JpZ2luYWwpO1xuICAgICAgICAvLyBgZXhpc3RzYCBoYXMgYSBjdXN0b20gcHJvbWlzaWZ5IGZ1bmN0aW9uIGJlY2F1c2Ugb2YgdGhlIGluY29uc2lzdGVudCBzaWduYXR1cmVcbiAgICAgICAgLy8gcmVwbGljYXRpbmcgdGhhdCBvbiB0aGUgcGF0Y2hlZCBmdW5jdGlvblxuICAgICAgICBjb25zdCBwcm9taXNpZmllZCA9IGZ1bmN0aW9uIChwYXRoKSB7XG4gICAgICAgICAgICByZXR1cm4gbmV3IFByb21pc2UocmVzb2x2ZSA9PiBmdW5jdGlvbldpdGhPcmlnaW5hbFByb3BlcnRpZXMocGF0aCwgcmVzb2x2ZSkpO1xuICAgICAgICB9O1xuICAgICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkocHJvbWlzaWZpZWQsICduYW1lJywgeyB2YWx1ZTogZnVuY3Rpb25OYW1lIH0pO1xuICAgICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkoZnVuY3Rpb25XaXRoT3JpZ2luYWxQcm9wZXJ0aWVzLCB1dGlsXzEucHJvbWlzaWZ5LmN1c3RvbSwge1xuICAgICAgICAgICAgdmFsdWU6IHByb21pc2lmaWVkLFxuICAgICAgICB9KTtcbiAgICAgICAgcmV0dXJuIGZ1bmN0aW9uV2l0aE9yaWdpbmFsUHJvcGVydGllcztcbiAgICB9XG4gICAgX3BhdGNoUHJvbWlzZUZ1bmN0aW9uKGZ1bmN0aW9uTmFtZSwgb3JpZ2luYWwpIHtcbiAgICAgICAgY29uc3QgaW5zdHJ1bWVudGF0aW9uID0gdGhpcztcbiAgICAgICAgY29uc3QgcGF0Y2hlZEZ1bmN0aW9uID0gYXN5bmMgZnVuY3Rpb24gKC4uLmFyZ3MpIHtcbiAgICAgICAgICAgIGNvbnN0IGFjdGl2ZUNvbnRleHQgPSBhcGkuY29udGV4dC5hY3RpdmUoKTtcbiAgICAgICAgICAgIGlmICghaW5zdHJ1bWVudGF0aW9uLl9zaG91bGRUcmFjZShhY3RpdmVDb250ZXh0KSkge1xuICAgICAgICAgICAgICAgIHJldHVybiBvcmlnaW5hbC5hcHBseSh0aGlzLCBhcmdzKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmIChpbnN0cnVtZW50YXRpb24uX3J1bkNyZWF0ZUhvb2soZnVuY3Rpb25OYW1lLCB7XG4gICAgICAgICAgICAgICAgYXJnczogYXJncyxcbiAgICAgICAgICAgIH0pID09PSBmYWxzZSkge1xuICAgICAgICAgICAgICAgIHJldHVybiBhcGkuY29udGV4dC53aXRoKCgwLCBjb3JlXzEuc3VwcHJlc3NUcmFjaW5nKShhY3RpdmVDb250ZXh0KSwgb3JpZ2luYWwsIHRoaXMsIC4uLmFyZ3MpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY29uc3Qgc3BhbiA9IGluc3RydW1lbnRhdGlvbi50cmFjZXIuc3RhcnRTcGFuKGBmcyAke2Z1bmN0aW9uTmFtZX1gKTtcbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgLy8gU3VwcHJlc3MgdHJhY2luZyBmb3IgaW50ZXJuYWwgZnMgY2FsbHNcbiAgICAgICAgICAgICAgICBjb25zdCByZXMgPSBhd2FpdCBhcGkuY29udGV4dC53aXRoKCgwLCBjb3JlXzEuc3VwcHJlc3NUcmFjaW5nKShhcGkudHJhY2Uuc2V0U3BhbihhY3RpdmVDb250ZXh0LCBzcGFuKSksIG9yaWdpbmFsLCB0aGlzLCAuLi5hcmdzKTtcbiAgICAgICAgICAgICAgICBpbnN0cnVtZW50YXRpb24uX3J1bkVuZEhvb2soZnVuY3Rpb25OYW1lLCB7IGFyZ3M6IGFyZ3MsIHNwYW4gfSk7XG4gICAgICAgICAgICAgICAgcmV0dXJuIHJlcztcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgICAgIHNwYW4ucmVjb3JkRXhjZXB0aW9uKGVycm9yKTtcbiAgICAgICAgICAgICAgICBzcGFuLnNldFN0YXR1cyh7XG4gICAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6IGVycm9yLm1lc3NhZ2UsXG4gICAgICAgICAgICAgICAgICAgIGNvZGU6IGFwaS5TcGFuU3RhdHVzQ29kZS5FUlJPUixcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICBpbnN0cnVtZW50YXRpb24uX3J1bkVuZEhvb2soZnVuY3Rpb25OYW1lLCB7IGFyZ3M6IGFyZ3MsIHNwYW4sIGVycm9yIH0pO1xuICAgICAgICAgICAgICAgIHRocm93IGVycm9yO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZmluYWxseSB7XG4gICAgICAgICAgICAgICAgc3Bhbi5lbmQoKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHBhdGNoZWRGdW5jdGlvbldpdGhPcmlnaW5hbFByb3BlcnRpZXMocGF0Y2hlZEZ1bmN0aW9uLCBvcmlnaW5hbCk7XG4gICAgfVxuICAgIF9ydW5DcmVhdGVIb29rKC4uLmFyZ3MpIHtcbiAgICAgICAgY29uc3QgeyBjcmVhdGVIb29rIH0gPSB0aGlzLmdldENvbmZpZygpO1xuICAgICAgICBpZiAodHlwZW9mIGNyZWF0ZUhvb2sgPT09ICdmdW5jdGlvbicpIHtcbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGNyZWF0ZUhvb2soLi4uYXJncyk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjYXRjaCAoZSkge1xuICAgICAgICAgICAgICAgIHRoaXMuX2RpYWcuZXJyb3IoJ2NhdWdodCBjcmVhdGVIb29rIGVycm9yJywgZSk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgfVxuICAgIF9ydW5FbmRIb29rKC4uLmFyZ3MpIHtcbiAgICAgICAgY29uc3QgeyBlbmRIb29rIH0gPSB0aGlzLmdldENvbmZpZygpO1xuICAgICAgICBpZiAodHlwZW9mIGVuZEhvb2sgPT09ICdmdW5jdGlvbicpIHtcbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgZW5kSG9vayguLi5hcmdzKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNhdGNoIChlKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5fZGlhZy5lcnJvcignY2F1Z2h0IGVuZEhvb2sgZXJyb3InLCBlKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cbiAgICBfc2hvdWxkVHJhY2UoY29udGV4dCkge1xuICAgICAgICBpZiAoKDAsIGNvcmVfMS5pc1RyYWNpbmdTdXBwcmVzc2VkKShjb250ZXh0KSkge1xuICAgICAgICAgICAgLy8gUGVyZm9ybWFuY2Ugb3B0aW1pemF0aW9uLiBBdm9pZCBjcmVhdGluZyBhZGRpdGlvbmFsIGNvbnRleHRzIGFuZCBzcGFuc1xuICAgICAgICAgICAgLy8gaWYgd2UgYWxyZWFkeSBrbm93IHRoYXQgdGhlIHRyYWNpbmcgaXMgYmVpbmcgc3VwcHJlc3NlZC5cbiAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB7IHJlcXVpcmVQYXJlbnRTcGFuIH0gPSB0aGlzLmdldENvbmZpZygpO1xuICAgICAgICBpZiAocmVxdWlyZVBhcmVudFNwYW4pIHtcbiAgICAgICAgICAgIGNvbnN0IHBhcmVudFNwYW4gPSBhcGkudHJhY2UuZ2V0U3Bhbihjb250ZXh0KTtcbiAgICAgICAgICAgIGlmIChwYXJlbnRTcGFuID09IG51bGwpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgfVxufVxuZXhwb3J0cy5Gc0luc3RydW1lbnRhdGlvbiA9IEZzSW5zdHJ1bWVudGF0aW9uO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5zdHJ1bWVudGF0aW9uLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/instrumentation.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/types.js":
/*!***************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/types.js ***!
  \***************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF9jOWE2ODkzM2ZmMmYxNWZjYjExY2Y5MmJhN2E5MDJhNy9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLWZzL2J1aWxkL3NyYy90eXBlcy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbndhclxcRG9jdW1lbnRzXFxDdWJlbnR3ZWJcXGN1YmVudFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQG9wZW50ZWxlbWV0cnkraW5zdHJ1bWVudGF0X2M5YTY4OTMzZmYyZjE1ZmNiMTFjZjkyYmE3YTkwMmE3XFxub2RlX21vZHVsZXNcXEBvcGVudGVsZW1ldHJ5XFxpbnN0cnVtZW50YXRpb24tZnNcXGJ1aWxkXFxzcmNcXHR5cGVzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dHlwZXMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/types.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/utils.js":
/*!***************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/utils.js ***!
  \***************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.indexFs = exports.splitTwoLevels = void 0;\nfunction splitTwoLevels(functionName) {\n    const memberParts = functionName.split('.');\n    if (memberParts.length > 1) {\n        if (memberParts.length !== 2)\n            throw Error(`Invalid member function name ${functionName}`);\n        return memberParts;\n    }\n    else {\n        return [functionName];\n    }\n}\nexports.splitTwoLevels = splitTwoLevels;\nfunction indexFs(fs, member) {\n    if (!member)\n        throw new Error(JSON.stringify({ member }));\n    const splitResult = splitTwoLevels(member);\n    const [functionName1, functionName2] = splitResult;\n    if (functionName2) {\n        return {\n            objectToPatch: fs[functionName1],\n            functionNameToPatch: functionName2,\n        };\n    }\n    else {\n        return {\n            objectToPatch: fs,\n            functionNameToPatch: functionName1,\n        };\n    }\n}\nexports.indexFs = indexFs;\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/utils.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/version.js":
/*!*****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/version.js ***!
  \*****************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.19.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-fs';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/version.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/constants.js":
/*!*******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/constants.js ***!
  \*******************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.SYNC_FUNCTIONS = exports.CALLBACK_FUNCTIONS = exports.PROMISE_FUNCTIONS = void 0;\nexports.PROMISE_FUNCTIONS = [\n    'access',\n    'appendFile',\n    'chmod',\n    'chown',\n    'copyFile',\n    'cp',\n    'lchown',\n    'link',\n    'lstat',\n    'lutimes',\n    'mkdir',\n    'mkdtemp',\n    'open',\n    'opendir',\n    'readdir',\n    'readFile',\n    'readlink',\n    'realpath',\n    'rename',\n    'rm',\n    'rmdir',\n    'stat',\n    'symlink',\n    'truncate',\n    'unlink',\n    'utimes',\n    'writeFile',\n    // 'lchmod', // only implemented on macOS\n];\nexports.CALLBACK_FUNCTIONS = [\n    'access',\n    'appendFile',\n    'chmod',\n    'chown',\n    'copyFile',\n    'cp',\n    'exists',\n    'lchown',\n    'link',\n    'lstat',\n    'lutimes',\n    'mkdir',\n    'mkdtemp',\n    'open',\n    'opendir',\n    'readdir',\n    'readFile',\n    'readlink',\n    'realpath',\n    'realpath.native',\n    'rename',\n    'rm',\n    'rmdir',\n    'stat',\n    'symlink',\n    'truncate',\n    'unlink',\n    'utimes',\n    'writeFile',\n    // 'close', // functions on file descriptor\n    // 'fchmod', // functions on file descriptor\n    // 'fchown', // functions on file descriptor\n    // 'fdatasync', // functions on file descriptor\n    // 'fstat', // functions on file descriptor\n    // 'fsync', // functions on file descriptor\n    // 'ftruncate', // functions on file descriptor\n    // 'futimes', // functions on file descriptor\n    // 'lchmod', // only implemented on macOS\n    // 'read', // functions on file descriptor\n    // 'readv', // functions on file descriptor\n    // 'write', // functions on file descriptor\n    // 'writev', // functions on file descriptor\n];\nexports.SYNC_FUNCTIONS = [\n    'accessSync',\n    'appendFileSync',\n    'chmodSync',\n    'chownSync',\n    'copyFileSync',\n    'cpSync',\n    'existsSync',\n    'lchownSync',\n    'linkSync',\n    'lstatSync',\n    'lutimesSync',\n    'mkdirSync',\n    'mkdtempSync',\n    'opendirSync',\n    'openSync',\n    'readdirSync',\n    'readFileSync',\n    'readlinkSync',\n    'realpathSync',\n    'realpathSync.native',\n    'renameSync',\n    'rmdirSync',\n    'rmSync',\n    'statSync',\n    'symlinkSync',\n    'truncateSync',\n    'unlinkSync',\n    'utimesSync',\n    'writeFileSync',\n    // 'closeSync', // functions on file descriptor\n    // 'fchmodSync', // functions on file descriptor\n    // 'fchownSync', // functions on file descriptor\n    // 'fdatasyncSync', // functions on file descriptor\n    // 'fstatSync', // functions on file descriptor\n    // 'fsyncSync', // functions on file descriptor\n    // 'ftruncateSync', // functions on file descriptor\n    // 'futimesSync', // functions on file descriptor\n    // 'lchmodSync', // only implemented on macOS\n    // 'readSync', // functions on file descriptor\n    // 'readvSync', // functions on file descriptor\n    // 'writeSync', // functions on file descriptor\n    // 'writevSync', // functions on file descriptor\n];\n//# sourceMappingURL=constants.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/constants.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/index.js":
/*!***************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/index.js ***!
  \***************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/instrumentation.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/types.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/instrumentation.js":
/*!*************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/instrumentation.js ***!
  \*************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.FsInstrumentation = void 0;\nconst api = __webpack_require__(/*! @opentelemetry/api */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst core_1 = __webpack_require__(/*! @opentelemetry/core */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+core@1.30.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/core/build/esm/index.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/version.js\");\nconst constants_1 = __webpack_require__(/*! ./constants */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/constants.js\");\nconst util_1 = __webpack_require__(/*! util */ \"util\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/utils.js\");\n/**\n * This is important for 2-level functions like `realpath.native` to retain the 2nd-level\n * when patching the 1st-level.\n */\nfunction patchedFunctionWithOriginalProperties(patchedFunction, original) {\n    return Object.assign(patchedFunction, original);\n}\nclass FsInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, config);\n    }\n    init() {\n        return [\n            new instrumentation_1.InstrumentationNodeModuleDefinition('fs', ['*'], (fs) => {\n                for (const fName of constants_1.SYNC_FUNCTIONS) {\n                    const { objectToPatch, functionNameToPatch } = (0, utils_1.indexFs)(fs, fName);\n                    if ((0, instrumentation_1.isWrapped)(objectToPatch[functionNameToPatch])) {\n                        this._unwrap(objectToPatch, functionNameToPatch);\n                    }\n                    this._wrap(objectToPatch, functionNameToPatch, this._patchSyncFunction.bind(this, fName));\n                }\n                for (const fName of constants_1.CALLBACK_FUNCTIONS) {\n                    const { objectToPatch, functionNameToPatch } = (0, utils_1.indexFs)(fs, fName);\n                    if ((0, instrumentation_1.isWrapped)(objectToPatch[functionNameToPatch])) {\n                        this._unwrap(objectToPatch, functionNameToPatch);\n                    }\n                    if (fName === 'exists') {\n                        // handling separately because of the inconsistent cb style:\n                        // `exists` doesn't have error as the first argument, but the result\n                        this._wrap(objectToPatch, functionNameToPatch, this._patchExistsCallbackFunction.bind(this, fName));\n                        continue;\n                    }\n                    this._wrap(objectToPatch, functionNameToPatch, this._patchCallbackFunction.bind(this, fName));\n                }\n                for (const fName of constants_1.PROMISE_FUNCTIONS) {\n                    if ((0, instrumentation_1.isWrapped)(fs.promises[fName])) {\n                        this._unwrap(fs.promises, fName);\n                    }\n                    this._wrap(fs.promises, fName, this._patchPromiseFunction.bind(this, fName));\n                }\n                return fs;\n            }, (fs) => {\n                if (fs === undefined)\n                    return;\n                for (const fName of constants_1.SYNC_FUNCTIONS) {\n                    const { objectToPatch, functionNameToPatch } = (0, utils_1.indexFs)(fs, fName);\n                    if ((0, instrumentation_1.isWrapped)(objectToPatch[functionNameToPatch])) {\n                        this._unwrap(objectToPatch, functionNameToPatch);\n                    }\n                }\n                for (const fName of constants_1.CALLBACK_FUNCTIONS) {\n                    const { objectToPatch, functionNameToPatch } = (0, utils_1.indexFs)(fs, fName);\n                    if ((0, instrumentation_1.isWrapped)(objectToPatch[functionNameToPatch])) {\n                        this._unwrap(objectToPatch, functionNameToPatch);\n                    }\n                }\n                for (const fName of constants_1.PROMISE_FUNCTIONS) {\n                    if ((0, instrumentation_1.isWrapped)(fs.promises[fName])) {\n                        this._unwrap(fs.promises, fName);\n                    }\n                }\n            }),\n            new instrumentation_1.InstrumentationNodeModuleDefinition('fs/promises', ['*'], (fsPromises) => {\n                for (const fName of constants_1.PROMISE_FUNCTIONS) {\n                    if ((0, instrumentation_1.isWrapped)(fsPromises[fName])) {\n                        this._unwrap(fsPromises, fName);\n                    }\n                    this._wrap(fsPromises, fName, this._patchPromiseFunction.bind(this, fName));\n                }\n                return fsPromises;\n            }, (fsPromises) => {\n                if (fsPromises === undefined)\n                    return;\n                for (const fName of constants_1.PROMISE_FUNCTIONS) {\n                    if ((0, instrumentation_1.isWrapped)(fsPromises[fName])) {\n                        this._unwrap(fsPromises, fName);\n                    }\n                }\n            }),\n        ];\n    }\n    _patchSyncFunction(functionName, original) {\n        const instrumentation = this;\n        const patchedFunction = function (...args) {\n            const activeContext = api.context.active();\n            if (!instrumentation._shouldTrace(activeContext)) {\n                return original.apply(this, args);\n            }\n            if (instrumentation._runCreateHook(functionName, {\n                args: args,\n            }) === false) {\n                return api.context.with((0, core_1.suppressTracing)(activeContext), original, this, ...args);\n            }\n            const span = instrumentation.tracer.startSpan(`fs ${functionName}`);\n            try {\n                // Suppress tracing for internal fs calls\n                const res = api.context.with((0, core_1.suppressTracing)(api.trace.setSpan(activeContext, span)), original, this, ...args);\n                instrumentation._runEndHook(functionName, { args: args, span });\n                return res;\n            }\n            catch (error) {\n                span.recordException(error);\n                span.setStatus({\n                    message: error.message,\n                    code: api.SpanStatusCode.ERROR,\n                });\n                instrumentation._runEndHook(functionName, { args: args, span, error });\n                throw error;\n            }\n            finally {\n                span.end();\n            }\n        };\n        return patchedFunctionWithOriginalProperties(patchedFunction, original);\n    }\n    _patchCallbackFunction(functionName, original) {\n        const instrumentation = this;\n        const patchedFunction = function (...args) {\n            const activeContext = api.context.active();\n            if (!instrumentation._shouldTrace(activeContext)) {\n                return original.apply(this, args);\n            }\n            if (instrumentation._runCreateHook(functionName, {\n                args: args,\n            }) === false) {\n                return api.context.with((0, core_1.suppressTracing)(activeContext), original, this, ...args);\n            }\n            const lastIdx = args.length - 1;\n            const cb = args[lastIdx];\n            if (typeof cb === 'function') {\n                const span = instrumentation.tracer.startSpan(`fs ${functionName}`);\n                // Return to the context active during the call in the callback\n                args[lastIdx] = api.context.bind(activeContext, function (error) {\n                    if (error) {\n                        span.recordException(error);\n                        span.setStatus({\n                            message: error.message,\n                            code: api.SpanStatusCode.ERROR,\n                        });\n                    }\n                    instrumentation._runEndHook(functionName, {\n                        args: args,\n                        span,\n                        error,\n                    });\n                    span.end();\n                    return cb.apply(this, arguments);\n                });\n                try {\n                    // Suppress tracing for internal fs calls\n                    return api.context.with((0, core_1.suppressTracing)(api.trace.setSpan(activeContext, span)), original, this, ...args);\n                }\n                catch (error) {\n                    span.recordException(error);\n                    span.setStatus({\n                        message: error.message,\n                        code: api.SpanStatusCode.ERROR,\n                    });\n                    instrumentation._runEndHook(functionName, {\n                        args: args,\n                        span,\n                        error,\n                    });\n                    span.end();\n                    throw error;\n                }\n            }\n            else {\n                // TODO: what to do if we are pretty sure it's going to throw\n                return original.apply(this, args);\n            }\n        };\n        return patchedFunctionWithOriginalProperties(patchedFunction, original);\n    }\n    _patchExistsCallbackFunction(functionName, original) {\n        const instrumentation = this;\n        const patchedFunction = function (...args) {\n            const activeContext = api.context.active();\n            if (!instrumentation._shouldTrace(activeContext)) {\n                return original.apply(this, args);\n            }\n            if (instrumentation._runCreateHook(functionName, {\n                args: args,\n            }) === false) {\n                return api.context.with((0, core_1.suppressTracing)(activeContext), original, this, ...args);\n            }\n            const lastIdx = args.length - 1;\n            const cb = args[lastIdx];\n            if (typeof cb === 'function') {\n                const span = instrumentation.tracer.startSpan(`fs ${functionName}`);\n                // Return to the context active during the call in the callback\n                args[lastIdx] = api.context.bind(activeContext, function () {\n                    // `exists` never calls the callback with an error\n                    instrumentation._runEndHook(functionName, {\n                        args: args,\n                        span,\n                    });\n                    span.end();\n                    return cb.apply(this, arguments);\n                });\n                try {\n                    // Suppress tracing for internal fs calls\n                    return api.context.with((0, core_1.suppressTracing)(api.trace.setSpan(activeContext, span)), original, this, ...args);\n                }\n                catch (error) {\n                    span.recordException(error);\n                    span.setStatus({\n                        message: error.message,\n                        code: api.SpanStatusCode.ERROR,\n                    });\n                    instrumentation._runEndHook(functionName, {\n                        args: args,\n                        span,\n                        error,\n                    });\n                    span.end();\n                    throw error;\n                }\n            }\n            else {\n                return original.apply(this, args);\n            }\n        };\n        const functionWithOriginalProperties = patchedFunctionWithOriginalProperties(patchedFunction, original);\n        // `exists` has a custom promisify function because of the inconsistent signature\n        // replicating that on the patched function\n        const promisified = function (path) {\n            return new Promise(resolve => functionWithOriginalProperties(path, resolve));\n        };\n        Object.defineProperty(promisified, 'name', { value: functionName });\n        Object.defineProperty(functionWithOriginalProperties, util_1.promisify.custom, {\n            value: promisified,\n        });\n        return functionWithOriginalProperties;\n    }\n    _patchPromiseFunction(functionName, original) {\n        const instrumentation = this;\n        const patchedFunction = async function (...args) {\n            const activeContext = api.context.active();\n            if (!instrumentation._shouldTrace(activeContext)) {\n                return original.apply(this, args);\n            }\n            if (instrumentation._runCreateHook(functionName, {\n                args: args,\n            }) === false) {\n                return api.context.with((0, core_1.suppressTracing)(activeContext), original, this, ...args);\n            }\n            const span = instrumentation.tracer.startSpan(`fs ${functionName}`);\n            try {\n                // Suppress tracing for internal fs calls\n                const res = await api.context.with((0, core_1.suppressTracing)(api.trace.setSpan(activeContext, span)), original, this, ...args);\n                instrumentation._runEndHook(functionName, { args: args, span });\n                return res;\n            }\n            catch (error) {\n                span.recordException(error);\n                span.setStatus({\n                    message: error.message,\n                    code: api.SpanStatusCode.ERROR,\n                });\n                instrumentation._runEndHook(functionName, { args: args, span, error });\n                throw error;\n            }\n            finally {\n                span.end();\n            }\n        };\n        return patchedFunctionWithOriginalProperties(patchedFunction, original);\n    }\n    _runCreateHook(...args) {\n        const { createHook } = this.getConfig();\n        if (typeof createHook === 'function') {\n            try {\n                return createHook(...args);\n            }\n            catch (e) {\n                this._diag.error('caught createHook error', e);\n            }\n        }\n        return true;\n    }\n    _runEndHook(...args) {\n        const { endHook } = this.getConfig();\n        if (typeof endHook === 'function') {\n            try {\n                endHook(...args);\n            }\n            catch (e) {\n                this._diag.error('caught endHook error', e);\n            }\n        }\n    }\n    _shouldTrace(context) {\n        if ((0, core_1.isTracingSuppressed)(context)) {\n            // Performance optimization. Avoid creating additional contexts and spans\n            // if we already know that the tracing is being suppressed.\n            return false;\n        }\n        const { requireParentSpan } = this.getConfig();\n        if (requireParentSpan) {\n            const parentSpan = api.trace.getSpan(context);\n            if (parentSpan == null) {\n                return false;\n            }\n        }\n        return true;\n    }\n}\nexports.FsInstrumentation = FsInstrumentation;\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/instrumentation.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/types.js":
/*!***************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/types.js ***!
  \***************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF9jOWE2ODkzM2ZmMmYxNWZjYjExY2Y5MmJhN2E5MDJhNy9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLWZzL2J1aWxkL3NyYy90eXBlcy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbndhclxcRG9jdW1lbnRzXFxDdWJlbnR3ZWJcXGN1YmVudFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQG9wZW50ZWxlbWV0cnkraW5zdHJ1bWVudGF0X2M5YTY4OTMzZmYyZjE1ZmNiMTFjZjkyYmE3YTkwMmE3XFxub2RlX21vZHVsZXNcXEBvcGVudGVsZW1ldHJ5XFxpbnN0cnVtZW50YXRpb24tZnNcXGJ1aWxkXFxzcmNcXHR5cGVzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dHlwZXMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/types.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/utils.js":
/*!***************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/utils.js ***!
  \***************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.indexFs = exports.splitTwoLevels = void 0;\nfunction splitTwoLevels(functionName) {\n    const memberParts = functionName.split('.');\n    if (memberParts.length > 1) {\n        if (memberParts.length !== 2)\n            throw Error(`Invalid member function name ${functionName}`);\n        return memberParts;\n    }\n    else {\n        return [functionName];\n    }\n}\nexports.splitTwoLevels = splitTwoLevels;\nfunction indexFs(fs, member) {\n    if (!member)\n        throw new Error(JSON.stringify({ member }));\n    const splitResult = splitTwoLevels(member);\n    const [functionName1, functionName2] = splitResult;\n    if (functionName2) {\n        return {\n            objectToPatch: fs[functionName1],\n            functionNameToPatch: functionName2,\n        };\n    }\n    else {\n        return {\n            objectToPatch: fs,\n            functionNameToPatch: functionName1,\n        };\n    }\n}\nexports.indexFs = indexFs;\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/utils.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/version.js":
/*!*****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/version.js ***!
  \*****************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.19.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-fs';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_c9a68933ff2f15fcb11cf92ba7a902a7/node_modules/@opentelemetry/instrumentation-fs/build/src/version.js\n");

/***/ })

};
;