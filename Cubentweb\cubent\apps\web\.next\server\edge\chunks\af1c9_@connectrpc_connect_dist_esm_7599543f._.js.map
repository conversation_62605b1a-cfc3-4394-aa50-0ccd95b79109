{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@connectrpc+connect@1.6.1_@bufbuild+protobuf@1.10.1/node_modules/@connectrpc/connect/dist/esm/any-client.js"], "sourcesContent": ["// Copyright 2021-2024 The Connect Authors\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n/**\n * Create any client for the given service.\n *\n * The given createMethod function is called for each method definition\n * of the service. The function it returns is added to the client object\n * as a method.\n */\nexport function makeAnyClient(service, createMethod) {\n    const client = {};\n    for (const [localName, methodInfo] of Object.entries(service.methods)) {\n        const method = createMethod(Object.assign(Object.assign({}, methodInfo), { localName,\n            service }));\n        if (method != null) {\n            client[localName] = method;\n        }\n    }\n    return client;\n}\n"], "names": [], "mappings": "AAAA,0CAA0C;AAC1C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC;;;;;;CAMC;;;AACM,SAAS,cAAc,OAAO,EAAE,YAAY;IAC/C,MAAM,SAAS,CAAC;IAChB,KAAK,MAAM,CAAC,WAAW,WAAW,IAAI,OAAO,OAAO,CAAC,QAAQ,OAAO,EAAG;QACnE,MAAM,SAAS,aAAa,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,aAAa;YAAE;YACvE;QAAQ;QACZ,IAAI,UAAU,MAAM;YAChB,MAAM,CAAC,UAAU,GAAG;QACxB;IACJ;IACA,OAAO;AACX", "ignoreList": [0]}}, {"offset": {"line": 48, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@connectrpc+connect@1.6.1_@bufbuild+protobuf@1.10.1/node_modules/@connectrpc/connect/dist/esm/code.js"], "sourcesContent": ["// Copyright 2021-2024 The Connect Authors\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n/**\n * Connect represents categories of errors as codes, and each code maps to a\n * specific HTTP status code. The codes and their semantics were chosen to\n * match gRPC. Only the codes below are valid — there are no user-defined\n * codes.\n *\n * See the specification at https://connectrpc.com/docs/protocol#error-codes\n * for details.\n */\nexport var Code;\n(function (Code) {\n    /**\n     * Canceled, usually be the user\n     */\n    Code[Code[\"Canceled\"] = 1] = \"Canceled\";\n    /**\n     * Unknown error\n     */\n    Code[Code[\"Unknown\"] = 2] = \"Unknown\";\n    /**\n     * Argument invalid regardless of system state\n     */\n    Code[Code[\"InvalidArgument\"] = 3] = \"InvalidArgument\";\n    /**\n     * Operation expired, may or may not have completed.\n     */\n    Code[Code[\"DeadlineExceeded\"] = 4] = \"DeadlineExceeded\";\n    /**\n     * Entity not found.\n     */\n    Code[Code[\"NotFound\"] = 5] = \"NotFound\";\n    /**\n     * Entity already exists.\n     */\n    Code[Code[\"AlreadyExists\"] = 6] = \"AlreadyExists\";\n    /**\n     * Operation not authorized.\n     */\n    Code[Code[\"PermissionDenied\"] = 7] = \"PermissionDenied\";\n    /**\n     * Quota exhausted.\n     */\n    Code[Code[\"ResourceExhausted\"] = 8] = \"ResourceExhausted\";\n    /**\n     * Argument invalid in current system state.\n     */\n    Code[Code[\"FailedPrecondition\"] = 9] = \"FailedPrecondition\";\n    /**\n     * Operation aborted.\n     */\n    Code[Code[\"Aborted\"] = 10] = \"Aborted\";\n    /**\n     * Out of bounds, use instead of FailedPrecondition.\n     */\n    Code[Code[\"OutOfRange\"] = 11] = \"OutOfRange\";\n    /**\n     * Operation not implemented or disabled.\n     */\n    Code[Code[\"Unimplemented\"] = 12] = \"Unimplemented\";\n    /**\n     * Internal error, reserved for \"serious errors\".\n     */\n    Code[Code[\"Internal\"] = 13] = \"Internal\";\n    /**\n     * Unavailable, client should back off and retry.\n     */\n    Code[Code[\"Unavailable\"] = 14] = \"Unavailable\";\n    /**\n     * Unrecoverable data loss or corruption.\n     */\n    Code[Code[\"DataLoss\"] = 15] = \"DataLoss\";\n    /**\n     * Request isn't authenticated.\n     */\n    Code[Code[\"Unauthenticated\"] = 16] = \"Unauthenticated\";\n})(Code || (Code = {}));\n"], "names": [], "mappings": "AAAA,0CAA0C;AAC1C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC;;;;;;;;CAQC;;;AACM,IAAI;AACX,CAAC,SAAU,IAAI;IACX;;KAEC,GACD,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,EAAE,GAAG;IAC7B;;KAEC,GACD,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,EAAE,GAAG;IAC5B;;KAEC,GACD,IAAI,CAAC,IAAI,CAAC,kBAAkB,GAAG,EAAE,GAAG;IACpC;;KAEC,GACD,IAAI,CAAC,IAAI,CAAC,mBAAmB,GAAG,EAAE,GAAG;IACrC;;KAEC,GACD,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,EAAE,GAAG;IAC7B;;KAEC,GACD,IAAI,CAAC,IAAI,CAAC,gBAAgB,GAAG,EAAE,GAAG;IAClC;;KAEC,GACD,IAAI,CAAC,IAAI,CAAC,mBAAmB,GAAG,EAAE,GAAG;IACrC;;KAEC,GACD,IAAI,CAAC,IAAI,CAAC,oBAAoB,GAAG,EAAE,GAAG;IACtC;;KAEC,GACD,IAAI,CAAC,IAAI,CAAC,qBAAqB,GAAG,EAAE,GAAG;IACvC;;KAEC,GACD,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,GAAG,GAAG;IAC7B;;KAEC,GACD,IAAI,CAAC,IAAI,CAAC,aAAa,GAAG,GAAG,GAAG;IAChC;;KAEC,GACD,IAAI,CAAC,IAAI,CAAC,gBAAgB,GAAG,GAAG,GAAG;IACnC;;KAEC,GACD,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,GAAG,GAAG;IAC9B;;KAEC,GACD,IAAI,CAAC,IAAI,CAAC,cAAc,GAAG,GAAG,GAAG;IACjC;;KAEC,GACD,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,GAAG,GAAG;IAC9B;;KAEC,GACD,IAAI,CAAC,IAAI,CAAC,kBAAkB,GAAG,GAAG,GAAG;AACzC,CAAC,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC", "ignoreList": [0]}}, {"offset": {"line": 129, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@connectrpc+connect@1.6.1_@bufbuild+protobuf@1.10.1/node_modules/@connectrpc/connect/dist/esm/protocol-connect/code-string.js"], "sourcesContent": ["// Copyright 2021-2024 The Connect Authors\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nimport { Code } from \"../code.js\";\n/**\n * codeToString returns the string representation of a Code.\n *\n * @private Internal code, does not follow semantic versioning.\n */\nexport function codeToString(value) {\n    const name = Code[value];\n    if (typeof name != \"string\") {\n        return value.toString();\n    }\n    return (name[0].toLowerCase() +\n        name.substring(1).replace(/[A-Z]/g, (c) => \"_\" + c.toLowerCase()));\n}\nlet stringToCode;\n/**\n * codeFromString parses the string representation of a Code in snake_case.\n * For example, the string \"permission_denied\" parses into Code.PermissionDenied.\n *\n * If the given string cannot be parsed, the function returns undefined.\n *\n * @private Internal code, does not follow semantic versioning.\n */\nexport function codeFromString(value) {\n    if (!stringToCode) {\n        stringToCode = {};\n        for (const value of Object.values(Code)) {\n            if (typeof value == \"string\") {\n                continue;\n            }\n            stringToCode[codeToString(value)] = value;\n        }\n    }\n    return stringToCode[value];\n}\n"], "names": [], "mappings": "AAAA,0CAA0C;AAC1C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;;;;AACjC;;AAMO,SAAS,aAAa,KAAK;IAC9B,MAAM,OAAO,mRAAA,CAAA,OAAI,CAAC,MAAM;IACxB,IAAI,OAAO,QAAQ,UAAU;QACzB,OAAO,MAAM,QAAQ;IACzB;IACA,OAAQ,IAAI,CAAC,EAAE,CAAC,WAAW,KACvB,KAAK,SAAS,CAAC,GAAG,OAAO,CAAC,UAAU,CAAC,IAAM,MAAM,EAAE,WAAW;AACtE;AACA,IAAI;AASG,SAAS,eAAe,KAAK;IAChC,IAAI,CAAC,cAAc;QACf,eAAe,CAAC;QAChB,KAAK,MAAM,SAAS,OAAO,MAAM,CAAC,mRAAA,CAAA,OAAI,EAAG;YACrC,IAAI,OAAO,SAAS,UAAU;gBAC1B;YACJ;YACA,YAAY,CAAC,aAAa,OAAO,GAAG;QACxC;IACJ;IACA,OAAO,YAAY,CAAC,MAAM;AAC9B", "ignoreList": [0]}}, {"offset": {"line": 174, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@connectrpc+connect@1.6.1_@bufbuild+protobuf@1.10.1/node_modules/@connectrpc/connect/dist/esm/connect-error.js"], "sourcesContent": ["// Copyright 2021-2024 The Connect Authors\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nimport { Code } from \"./code.js\";\nimport { codeToString } from \"./protocol-connect/code-string.js\";\n/**\n * ConnectError captures four pieces of information: a Code, an error\n * message, an optional cause of the error, and an optional collection of\n * arbitrary Protobuf messages called  \"details\".\n *\n * Because developer tools typically show just the error message, we prefix\n * it with the status code, so that the most important information is always\n * visible immediately.\n *\n * Error details are wrapped with google.protobuf.Any on the wire, so that\n * a server or middleware can attach arbitrary data to an error. Use the\n * method findDetails() to retrieve the details.\n */\nexport class ConnectError extends Error {\n    /**\n     * Create a new ConnectError.\n     * If no code is provided, code \"unknown\" is used.\n     * Outgoing details are only relevant for the server side - a service may\n     * raise an error with details, and it is up to the protocol implementation\n     * to encode and send the details along with error.\n     */\n    constructor(message, code = Code.Unknown, metadata, outgoingDetails, cause) {\n        super(createMessage(message, code));\n        this.name = \"ConnectError\";\n        // see https://www.typescriptlang.org/docs/handbook/release-notes/typescript-2-2.html#example\n        Object.setPrototypeOf(this, new.target.prototype);\n        this.rawMessage = message;\n        this.code = code;\n        this.metadata = new Headers(metadata !== null && metadata !== void 0 ? metadata : {});\n        this.details = outgoingDetails !== null && outgoingDetails !== void 0 ? outgoingDetails : [];\n        this.cause = cause;\n    }\n    /**\n     * Convert any value - typically a caught error into a ConnectError,\n     * following these rules:\n     * - If the value is already a ConnectError, return it as is.\n     * - If the value is an AbortError from the fetch API, return the message\n     *   of the AbortError with code Canceled.\n     * - For other Errors, return the error message with code Unknown by default.\n     * - For other values, return the values String representation as a message,\n     *   with the code Unknown by default.\n     * The original value will be used for the \"cause\" property for the new\n     * ConnectError.\n     */\n    static from(reason, code = Code.Unknown) {\n        if (reason instanceof ConnectError) {\n            return reason;\n        }\n        if (reason instanceof Error) {\n            if (reason.name == \"AbortError\") {\n                // Fetch requests can only be canceled with an AbortController.\n                // We detect that condition by looking at the name of the raised\n                // error object, and translate to the appropriate status code.\n                return new ConnectError(reason.message, Code.Canceled);\n            }\n            return new ConnectError(reason.message, code, undefined, undefined, reason);\n        }\n        return new ConnectError(String(reason), code, undefined, undefined, reason);\n    }\n    static [Symbol.hasInstance](v) {\n        if (!(v instanceof Error)) {\n            return false;\n        }\n        if (Object.getPrototypeOf(v) === ConnectError.prototype) {\n            return true;\n        }\n        return (v.name === \"ConnectError\" &&\n            \"code\" in v &&\n            typeof v.code === \"number\" &&\n            \"metadata\" in v &&\n            \"details\" in v &&\n            Array.isArray(v.details) &&\n            \"rawMessage\" in v &&\n            typeof v.rawMessage == \"string\" &&\n            \"cause\" in v);\n    }\n    findDetails(typeOrRegistry) {\n        const registry = \"typeName\" in typeOrRegistry\n            ? {\n                findMessage: (typeName) => typeName === typeOrRegistry.typeName ? typeOrRegistry : undefined,\n            }\n            : typeOrRegistry;\n        const details = [];\n        for (const data of this.details) {\n            if (\"getType\" in data) {\n                if (registry.findMessage(data.getType().typeName)) {\n                    details.push(data);\n                }\n                continue;\n            }\n            const type = registry.findMessage(data.type);\n            if (type) {\n                try {\n                    details.push(type.fromBinary(data.value));\n                }\n                catch (_) {\n                    // We silently give up if we are unable to parse the detail, because\n                    // that appears to be the least worst behavior.\n                    // It is very unlikely that a user surrounds a catch body handling the\n                    // error with another try-catch statement, and we do not want to\n                    // recommend doing so.\n                }\n            }\n        }\n        return details;\n    }\n}\n/**\n * Create an error message, prefixing the given code.\n */\nfunction createMessage(message, code) {\n    return message.length\n        ? `[${codeToString(code)}] ${message}`\n        : `[${codeToString(code)}]`;\n}\n"], "names": [], "mappings": "AAAA,0CAA0C;AAC1C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;;;AACjC;AACA;;;AAcO,MAAM,qBAAqB;IAC9B;;;;;;KAMC,GACD,YAAY,OAAO,EAAE,OAAO,mRAAA,CAAA,OAAI,CAAC,OAAO,EAAE,QAAQ,EAAE,eAAe,EAAE,KAAK,CAAE;QACxE,KAAK,CAAC,cAAc,SAAS;QAC7B,IAAI,CAAC,IAAI,GAAG;QACZ,6FAA6F;QAC7F,OAAO,cAAc,CAAC,IAAI,EAAE,WAAW,SAAS;QAChD,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ,aAAa,QAAQ,aAAa,KAAK,IAAI,WAAW,CAAC;QACnF,IAAI,CAAC,OAAO,GAAG,oBAAoB,QAAQ,oBAAoB,KAAK,IAAI,kBAAkB,EAAE;QAC5F,IAAI,CAAC,KAAK,GAAG;IACjB;IACA;;;;;;;;;;;KAWC,GACD,OAAO,KAAK,MAAM,EAAE,OAAO,mRAAA,CAAA,OAAI,CAAC,OAAO,EAAE;QACrC,IAAI,kBAAkB,cAAc;YAChC,OAAO;QACX;QACA,IAAI,kBAAkB,OAAO;YACzB,IAAI,OAAO,IAAI,IAAI,cAAc;gBAC7B,+DAA+D;gBAC/D,gEAAgE;gBAChE,8DAA8D;gBAC9D,OAAO,IAAI,aAAa,OAAO,OAAO,EAAE,mRAAA,CAAA,OAAI,CAAC,QAAQ;YACzD;YACA,OAAO,IAAI,aAAa,OAAO,OAAO,EAAE,MAAM,WAAW,WAAW;QACxE;QACA,OAAO,IAAI,aAAa,OAAO,SAAS,MAAM,WAAW,WAAW;IACxE;IACA,OAAO,CAAC,OAAO,WAAW,CAAC,CAAC,CAAC,EAAE;QAC3B,IAAI,CAAC,CAAC,aAAa,KAAK,GAAG;YACvB,OAAO;QACX;QACA,IAAI,OAAO,cAAc,CAAC,OAAO,aAAa,SAAS,EAAE;YACrD,OAAO;QACX;QACA,OAAQ,EAAE,IAAI,KAAK,kBACf,UAAU,KACV,OAAO,EAAE,IAAI,KAAK,YAClB,cAAc,KACd,aAAa,KACb,MAAM,OAAO,CAAC,EAAE,OAAO,KACvB,gBAAgB,KAChB,OAAO,EAAE,UAAU,IAAI,YACvB,WAAW;IACnB;IACA,YAAY,cAAc,EAAE;QACxB,MAAM,WAAW,cAAc,iBACzB;YACE,aAAa,CAAC,WAAa,aAAa,eAAe,QAAQ,GAAG,iBAAiB;QACvF,IACE;QACN,MAAM,UAAU,EAAE;QAClB,KAAK,MAAM,QAAQ,IAAI,CAAC,OAAO,CAAE;YAC7B,IAAI,aAAa,MAAM;gBACnB,IAAI,SAAS,WAAW,CAAC,KAAK,OAAO,GAAG,QAAQ,GAAG;oBAC/C,QAAQ,IAAI,CAAC;gBACjB;gBACA;YACJ;YACA,MAAM,OAAO,SAAS,WAAW,CAAC,KAAK,IAAI;YAC3C,IAAI,MAAM;gBACN,IAAI;oBACA,QAAQ,IAAI,CAAC,KAAK,UAAU,CAAC,KAAK,KAAK;gBAC3C,EACA,OAAO,GAAG;gBACN,oEAAoE;gBACpE,+CAA+C;gBAC/C,sEAAsE;gBACtE,gEAAgE;gBAChE,sBAAsB;gBAC1B;YACJ;QACJ;QACA,OAAO;IACX;AACJ;AACA;;CAEC,GACD,SAAS,cAAc,OAAO,EAAE,IAAI;IAChC,OAAO,QAAQ,MAAM,GACf,CAAC,CAAC,EAAE,CAAA,GAAA,oTAAA,CAAA,eAAY,AAAD,EAAE,MAAM,EAAE,EAAE,SAAS,GACpC,CAAC,CAAC,EAAE,CAAA,GAAA,oTAAA,CAAA,eAAY,AAAD,EAAE,MAAM,CAAC,CAAC;AACnC", "ignoreList": [0]}}, {"offset": {"line": 286, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@connectrpc+connect@1.6.1_@bufbuild+protobuf@1.10.1/node_modules/@connectrpc/connect/dist/esm/protocol/compression.js"], "sourcesContent": ["// Copyright 2021-2024 The Connect Authors\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nimport { ConnectError } from \"../connect-error.js\";\nimport { Code } from \"../code.js\";\n/**\n * compressedFlag indicates that the data in a EnvelopedMessage is\n * compressed. It has the same meaning in the gRPC-Web, gRPC-HTTP2,\n * and Connect protocols.\n *\n * @private Internal code, does not follow semantic versioning.\n */\nexport const compressedFlag = 0b00000001;\n/**\n * Validates the request encoding and determines the accepted response encoding.\n *\n * Returns the request and response compression to use. If the client requested\n * an encoding that is not available, the returned object contains an error that\n * must be used for the response.\n *\n * @private Internal code, does not follow semantic versioning.\n */\nexport function compressionNegotiate(available, requested, // e.g. the value of the Grpc-Encoding header\naccepted, // e.g. the value of the Grpc-Accept-Encoding header\nheaderNameAcceptEncoding) {\n    let request = null;\n    let response = null;\n    let error = undefined;\n    if (requested !== null && requested !== \"identity\") {\n        const found = available.find((c) => c.name === requested);\n        if (found) {\n            request = found;\n        }\n        else {\n            // To comply with https://github.com/grpc/grpc/blob/master/doc/compression.md\n            // and the Connect protocol, we return code \"unimplemented\" and specify\n            // acceptable compression(s).\n            const acceptable = available.map((c) => c.name).join(\",\");\n            error = new ConnectError(`unknown compression \"${requested}\": supported encodings are ${acceptable}`, Code.Unimplemented, {\n                [headerNameAcceptEncoding]: acceptable,\n            });\n        }\n    }\n    if (accepted === null || accepted === \"\") {\n        // Support asymmetric compression. This logic follows\n        // https://github.com/grpc/grpc/blob/master/doc/compression.md and common\n        // sense.\n        response = request;\n    }\n    else {\n        const acceptNames = accepted.split(\",\").map((n) => n.trim());\n        for (const name of acceptNames) {\n            const found = available.find((c) => c.name === name);\n            if (found) {\n                response = found;\n                break;\n            }\n        }\n    }\n    return { request, response, error };\n}\n"], "names": [], "mappings": "AAAA,0CAA0C;AAC1C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;;;;AACjC;AACA;;;AAQO,MAAM,iBAAiB;AAUvB,SAAS,qBAAqB,SAAS,EAAE,SAAS,EACzD,QAAQ,EACR,wBAAwB;IACpB,IAAI,UAAU;IACd,IAAI,WAAW;IACf,IAAI,QAAQ;IACZ,IAAI,cAAc,QAAQ,cAAc,YAAY;QAChD,MAAM,QAAQ,UAAU,IAAI,CAAC,CAAC,IAAM,EAAE,IAAI,KAAK;QAC/C,IAAI,OAAO;YACP,UAAU;QACd,OACK;YACD,6EAA6E;YAC7E,uEAAuE;YACvE,6BAA6B;YAC7B,MAAM,aAAa,UAAU,GAAG,CAAC,CAAC,IAAM,EAAE,IAAI,EAAE,IAAI,CAAC;YACrD,QAAQ,IAAI,+RAAA,CAAA,eAAY,CAAC,CAAC,qBAAqB,EAAE,UAAU,2BAA2B,EAAE,YAAY,EAAE,mRAAA,CAAA,OAAI,CAAC,aAAa,EAAE;gBACtH,CAAC,yBAAyB,EAAE;YAChC;QACJ;IACJ;IACA,IAAI,aAAa,QAAQ,aAAa,IAAI;QACtC,qDAAqD;QACrD,yEAAyE;QACzE,SAAS;QACT,WAAW;IACf,OACK;QACD,MAAM,cAAc,SAAS,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,IAAM,EAAE,IAAI;QACzD,KAAK,MAAM,QAAQ,YAAa;YAC5B,MAAM,QAAQ,UAAU,IAAI,CAAC,CAAC,IAAM,EAAE,IAAI,KAAK;YAC/C,IAAI,OAAO;gBACP,WAAW;gBACX;YACJ;QACJ;IACJ;IACA,OAAO;QAAE;QAAS;QAAU;IAAM;AACtC", "ignoreList": [0]}}, {"offset": {"line": 353, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@connectrpc+connect@1.6.1_@bufbuild+protobuf@1.10.1/node_modules/@connectrpc/connect/dist/esm/protocol/envelope.js"], "sourcesContent": ["// Copyright 2021-2024 The Connect Authors\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nimport { ConnectError } from \"../connect-error.js\";\nimport { Code } from \"../code.js\";\nimport { compressedFlag } from \"./compression.js\";\n/**\n * Create a WHATWG ReadableStream of enveloped messages from a ReadableStream\n * of bytes.\n *\n * Ideally, this would simply be a TransformStream, but ReadableStream.pipeThrough\n * does not have the necessary availability at this time.\n *\n * @private Internal code, does not follow semantic versioning.\n */\nexport function createEnvelopeReadableStream(stream) {\n    let reader;\n    let buffer = new Uint8Array(0);\n    function append(chunk) {\n        const n = new Uint8Array(buffer.length + chunk.length);\n        n.set(buffer);\n        n.set(chunk, buffer.length);\n        buffer = n;\n    }\n    return new ReadableStream({\n        start() {\n            reader = stream.getReader();\n        },\n        async pull(controller) {\n            let header = undefined;\n            for (;;) {\n                if (header === undefined && buffer.byteLength >= 5) {\n                    let length = 0;\n                    for (let i = 1; i < 5; i++) {\n                        length = (length << 8) + buffer[i];\n                    }\n                    header = { flags: buffer[0], length };\n                }\n                if (header !== undefined && buffer.byteLength >= header.length + 5) {\n                    break;\n                }\n                const result = await reader.read();\n                if (result.done) {\n                    break;\n                }\n                append(result.value);\n            }\n            if (header === undefined) {\n                if (buffer.byteLength == 0) {\n                    controller.close();\n                    return;\n                }\n                controller.error(new ConnectError(\"premature end of stream\", Code.DataLoss));\n                return;\n            }\n            const data = buffer.subarray(5, 5 + header.length);\n            buffer = buffer.subarray(5 + header.length);\n            controller.enqueue({\n                flags: header.flags,\n                data,\n            });\n        },\n    });\n}\n/**\n * Compress an EnvelopedMessage.\n *\n * Raises Internal if an enveloped message is already compressed.\n *\n * @private Internal code, does not follow semantic versioning.\n */\nexport async function envelopeCompress(envelope, compression, compressMinBytes) {\n    let { flags, data } = envelope;\n    if ((flags & compressedFlag) === compressedFlag) {\n        throw new ConnectError(\"invalid envelope, already compressed\", Code.Internal);\n    }\n    if (compression && data.byteLength >= compressMinBytes) {\n        data = await compression.compress(data);\n        flags = flags | compressedFlag;\n    }\n    return { data, flags };\n}\n/**\n * Decompress an EnvelopedMessage.\n *\n * Raises InvalidArgument if an envelope is compressed, but compression is null.\n *\n * Relies on the provided Compression to raise ResourceExhausted if the\n * *decompressed* message size is larger than readMaxBytes. If the envelope is\n * not compressed, readMaxBytes is not honored.\n *\n * @private Internal code, does not follow semantic versioning.\n */\nexport async function envelopeDecompress(envelope, compression, readMaxBytes) {\n    let { flags, data } = envelope;\n    if ((flags & compressedFlag) === compressedFlag) {\n        if (!compression) {\n            throw new ConnectError(\"received compressed envelope, but do not know how to decompress\", Code.Internal);\n        }\n        data = await compression.decompress(data, readMaxBytes);\n        flags = flags ^ compressedFlag;\n    }\n    return { data, flags };\n}\n/**\n * Encode a single enveloped message.\n *\n * @private Internal code, does not follow semantic versioning.\n */\nexport function encodeEnvelope(flags, data) {\n    const bytes = new Uint8Array(data.length + 5);\n    bytes.set(data, 5);\n    const v = new DataView(bytes.buffer, bytes.byteOffset, bytes.byteLength);\n    v.setUint8(0, flags); // first byte is flags\n    v.setUint32(1, data.length); // 4 bytes message length\n    return bytes;\n}\n/**\n * Encode a set of enveloped messages.\n *\n * @private Internal code, does not follow semantic versioning.\n */\nexport function encodeEnvelopes(...envelopes) {\n    const len = envelopes.reduce((previousValue, currentValue) => previousValue + currentValue.data.length + 5, 0);\n    const bytes = new Uint8Array(len);\n    const v = new DataView(bytes.buffer);\n    let offset = 0;\n    for (const e of envelopes) {\n        v.setUint8(offset, e.flags); // first byte is flags\n        v.setUint32(offset + 1, e.data.length); // 4 bytes message length\n        bytes.set(e.data, offset + 5);\n        offset += e.data.length + 5;\n    }\n    return bytes;\n}\n"], "names": [], "mappings": "AAAA,0CAA0C;AAC1C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;;;;;;;AACjC;AACA;AACA;;;;AAUO,SAAS,6BAA6B,MAAM;IAC/C,IAAI;IACJ,IAAI,SAAS,IAAI,WAAW;IAC5B,SAAS,OAAO,KAAK;QACjB,MAAM,IAAI,IAAI,WAAW,OAAO,MAAM,GAAG,MAAM,MAAM;QACrD,EAAE,GAAG,CAAC;QACN,EAAE,GAAG,CAAC,OAAO,OAAO,MAAM;QAC1B,SAAS;IACb;IACA,OAAO,IAAI,eAAe;QACtB;YACI,SAAS,OAAO,SAAS;QAC7B;QACA,MAAM,MAAK,UAAU;YACjB,IAAI,SAAS;YACb,OAAS;gBACL,IAAI,WAAW,aAAa,OAAO,UAAU,IAAI,GAAG;oBAChD,IAAI,SAAS;oBACb,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;wBACxB,SAAS,CAAC,UAAU,CAAC,IAAI,MAAM,CAAC,EAAE;oBACtC;oBACA,SAAS;wBAAE,OAAO,MAAM,CAAC,EAAE;wBAAE;oBAAO;gBACxC;gBACA,IAAI,WAAW,aAAa,OAAO,UAAU,IAAI,OAAO,MAAM,GAAG,GAAG;oBAChE;gBACJ;gBACA,MAAM,SAAS,MAAM,OAAO,IAAI;gBAChC,IAAI,OAAO,IAAI,EAAE;oBACb;gBACJ;gBACA,OAAO,OAAO,KAAK;YACvB;YACA,IAAI,WAAW,WAAW;gBACtB,IAAI,OAAO,UAAU,IAAI,GAAG;oBACxB,WAAW,KAAK;oBAChB;gBACJ;gBACA,WAAW,KAAK,CAAC,IAAI,+RAAA,CAAA,eAAY,CAAC,2BAA2B,mRAAA,CAAA,OAAI,CAAC,QAAQ;gBAC1E;YACJ;YACA,MAAM,OAAO,OAAO,QAAQ,CAAC,GAAG,IAAI,OAAO,MAAM;YACjD,SAAS,OAAO,QAAQ,CAAC,IAAI,OAAO,MAAM;YAC1C,WAAW,OAAO,CAAC;gBACf,OAAO,OAAO,KAAK;gBACnB;YACJ;QACJ;IACJ;AACJ;AAQO,eAAe,iBAAiB,QAAQ,EAAE,WAAW,EAAE,gBAAgB;IAC1E,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG;IACtB,IAAI,CAAC,QAAQ,sSAAA,CAAA,iBAAc,MAAM,sSAAA,CAAA,iBAAc,EAAE;QAC7C,MAAM,IAAI,+RAAA,CAAA,eAAY,CAAC,wCAAwC,mRAAA,CAAA,OAAI,CAAC,QAAQ;IAChF;IACA,IAAI,eAAe,KAAK,UAAU,IAAI,kBAAkB;QACpD,OAAO,MAAM,YAAY,QAAQ,CAAC;QAClC,QAAQ,QAAQ,sSAAA,CAAA,iBAAc;IAClC;IACA,OAAO;QAAE;QAAM;IAAM;AACzB;AAYO,eAAe,mBAAmB,QAAQ,EAAE,WAAW,EAAE,YAAY;IACxE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG;IACtB,IAAI,CAAC,QAAQ,sSAAA,CAAA,iBAAc,MAAM,sSAAA,CAAA,iBAAc,EAAE;QAC7C,IAAI,CAAC,aAAa;YACd,MAAM,IAAI,+RAAA,CAAA,eAAY,CAAC,mEAAmE,mRAAA,CAAA,OAAI,CAAC,QAAQ;QAC3G;QACA,OAAO,MAAM,YAAY,UAAU,CAAC,MAAM;QAC1C,QAAQ,QAAQ,sSAAA,CAAA,iBAAc;IAClC;IACA,OAAO;QAAE;QAAM;IAAM;AACzB;AAMO,SAAS,eAAe,KAAK,EAAE,IAAI;IACtC,MAAM,QAAQ,IAAI,WAAW,KAAK,MAAM,GAAG;IAC3C,MAAM,GAAG,CAAC,MAAM;IAChB,MAAM,IAAI,IAAI,SAAS,MAAM,MAAM,EAAE,MAAM,UAAU,EAAE,MAAM,UAAU;IACvE,EAAE,QAAQ,CAAC,GAAG,QAAQ,sBAAsB;IAC5C,EAAE,SAAS,CAAC,GAAG,KAAK,MAAM,GAAG,yBAAyB;IACtD,OAAO;AACX;AAMO,SAAS,gBAAgB,GAAG,SAAS;IACxC,MAAM,MAAM,UAAU,MAAM,CAAC,CAAC,eAAe,eAAiB,gBAAgB,aAAa,IAAI,CAAC,MAAM,GAAG,GAAG;IAC5G,MAAM,QAAQ,IAAI,WAAW;IAC7B,MAAM,IAAI,IAAI,SAAS,MAAM,MAAM;IACnC,IAAI,SAAS;IACb,KAAK,MAAM,KAAK,UAAW;QACvB,EAAE,QAAQ,CAAC,QAAQ,EAAE,KAAK,GAAG,sBAAsB;QACnD,EAAE,SAAS,CAAC,SAAS,GAAG,EAAE,IAAI,CAAC,MAAM,GAAG,yBAAyB;QACjE,MAAM,GAAG,CAAC,EAAE,IAAI,EAAE,SAAS;QAC3B,UAAU,EAAE,IAAI,CAAC,MAAM,GAAG;IAC9B;IACA,OAAO;AACX", "ignoreList": [0]}}, {"offset": {"line": 486, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@connectrpc+connect@1.6.1_@bufbuild+protobuf@1.10.1/node_modules/@connectrpc/connect/dist/esm/protocol/limit-io.js"], "sourcesContent": ["// Copyright 2021-2024 The Connect Authors\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nimport { ConnectError } from \"../connect-error.js\";\nimport { Code } from \"../code.js\";\n/**\n * At most, allow ~4GiB to be received or sent per message.\n * zlib used by Node.js caps maxOutputLength at this value. It also happens to\n * be the maximum theoretical message size supported by protobuf-es.\n */\nconst maxReadMaxBytes = 0xffffffff;\nconst maxWriteMaxBytes = maxReadMaxBytes;\n/**\n * The default value for the compressMinBytes option. The CPU cost of compressing\n * very small messages usually isn't worth the small reduction in network I/O, so\n * the default value is 1 kibibyte.\n */\nconst defaultCompressMinBytes = 1024;\n/**\n * Asserts that the options writeMaxBytes, readMaxBytes, and compressMinBytes\n * are within sane limits, and returns default values where no value is\n * provided.\n *\n * @private Internal code, does not follow semantic versioning.\n */\nexport function validateReadWriteMaxBytes(readMaxBytes, writeMaxBytes, compressMinBytes) {\n    writeMaxBytes !== null && writeMaxBytes !== void 0 ? writeMaxBytes : (writeMaxBytes = maxWriteMaxBytes);\n    readMaxBytes !== null && readMaxBytes !== void 0 ? readMaxBytes : (readMaxBytes = maxReadMaxBytes);\n    compressMinBytes !== null && compressMinBytes !== void 0 ? compressMinBytes : (compressMinBytes = defaultCompressMinBytes);\n    if (writeMaxBytes < 1 || writeMaxBytes > maxWriteMaxBytes) {\n        throw new ConnectError(`writeMaxBytes ${writeMaxBytes} must be >= 1 and <= ${maxWriteMaxBytes}`, Code.Internal);\n    }\n    if (readMaxBytes < 1 || readMaxBytes > maxReadMaxBytes) {\n        throw new ConnectError(`readMaxBytes ${readMaxBytes} must be >= 1 and <= ${maxReadMaxBytes}`, Code.Internal);\n    }\n    return {\n        readMaxBytes,\n        writeMaxBytes,\n        compressMinBytes,\n    };\n}\n/**\n * Raise an error ResourceExhausted if more than writeMaxByte are written.\n *\n * @private Internal code, does not follow semantic versioning.\n */\nexport function assertWriteMaxBytes(writeMaxBytes, bytesWritten) {\n    if (bytesWritten > writeMaxBytes) {\n        throw new ConnectError(`message size ${bytesWritten} is larger than configured writeMaxBytes ${writeMaxBytes}`, Code.ResourceExhausted);\n    }\n}\n/**\n * Raise an error ResourceExhausted if more than readMaxBytes are read.\n *\n * @private Internal code, does not follow semantic versioning.\n */\nexport function assertReadMaxBytes(readMaxBytes, bytesRead, totalSizeKnown = false) {\n    if (bytesRead > readMaxBytes) {\n        let message = `message size is larger than configured readMaxBytes ${readMaxBytes}`;\n        if (totalSizeKnown) {\n            message = `message size ${bytesRead} is larger than configured readMaxBytes ${readMaxBytes}`;\n        }\n        throw new ConnectError(message, Code.ResourceExhausted);\n    }\n}\n"], "names": [], "mappings": "AAAA,0CAA0C;AAC1C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;;;;;AACjC;AACA;;;AACA;;;;CAIC,GACD,MAAM,kBAAkB;AACxB,MAAM,mBAAmB;AACzB;;;;CAIC,GACD,MAAM,0BAA0B;AAQzB,SAAS,0BAA0B,YAAY,EAAE,aAAa,EAAE,gBAAgB;IACnF,kBAAkB,QAAQ,kBAAkB,KAAK,IAAI,gBAAiB,gBAAgB;IACtF,iBAAiB,QAAQ,iBAAiB,KAAK,IAAI,eAAgB,eAAe;IAClF,qBAAqB,QAAQ,qBAAqB,KAAK,IAAI,mBAAoB,mBAAmB;IAClG,IAAI,gBAAgB,KAAK,gBAAgB,kBAAkB;QACvD,MAAM,IAAI,+RAAA,CAAA,eAAY,CAAC,CAAC,cAAc,EAAE,cAAc,qBAAqB,EAAE,kBAAkB,EAAE,mRAAA,CAAA,OAAI,CAAC,QAAQ;IAClH;IACA,IAAI,eAAe,KAAK,eAAe,iBAAiB;QACpD,MAAM,IAAI,+RAAA,CAAA,eAAY,CAAC,CAAC,aAAa,EAAE,aAAa,qBAAqB,EAAE,iBAAiB,EAAE,mRAAA,CAAA,OAAI,CAAC,QAAQ;IAC/G;IACA,OAAO;QACH;QACA;QACA;IACJ;AACJ;AAMO,SAAS,oBAAoB,aAAa,EAAE,YAAY;IAC3D,IAAI,eAAe,eAAe;QAC9B,MAAM,IAAI,+RAAA,CAAA,eAAY,CAAC,CAAC,aAAa,EAAE,aAAa,yCAAyC,EAAE,eAAe,EAAE,mRAAA,CAAA,OAAI,CAAC,iBAAiB;IAC1I;AACJ;AAMO,SAAS,mBAAmB,YAAY,EAAE,SAAS,EAAE,iBAAiB,KAAK;IAC9E,IAAI,YAAY,cAAc;QAC1B,IAAI,UAAU,CAAC,oDAAoD,EAAE,cAAc;QACnF,IAAI,gBAAgB;YAChB,UAAU,CAAC,aAAa,EAAE,UAAU,wCAAwC,EAAE,cAAc;QAChG;QACA,MAAM,IAAI,+RAAA,CAAA,eAAY,CAAC,SAAS,mRAAA,CAAA,OAAI,CAAC,iBAAiB;IAC1D;AACJ", "ignoreList": [0]}}, {"offset": {"line": 555, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@connectrpc+connect@1.6.1_@bufbuild+protobuf@1.10.1/node_modules/@connectrpc/connect/dist/esm/protocol/async-iterable.js"], "sourcesContent": ["// Copyright 2021-2024 The Connect Authors\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nvar __asyncValues = (this && this.__asyncValues) || function (o) {\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n    var m = o[Symbol.asyncIterator], i;\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\n};\nvar __await = (this && this.__await) || function (v) { return this instanceof __await ? (this.v = v, this) : new __await(v); }\nvar __asyncGenerator = (this && this.__asyncGenerator) || function (thisArg, _arguments, generator) {\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\", awaitReturn), i[Symbol.asyncIterator] = function () { return this; }, i;\n    function awaitReturn(f) { return function (v) { return Promise.resolve(v).then(f, reject); }; }\n    function verb(n, f) { if (g[n]) { i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; if (f) i[n] = f(i[n]); } }\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\n    function fulfill(value) { resume(\"next\", value); }\n    function reject(value) { resume(\"throw\", value); }\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\n};\nvar __asyncDelegator = (this && this.__asyncDelegator) || function (o) {\n    var i, p;\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\n};\nimport { Code } from \"../code.js\";\nimport { ConnectError } from \"../connect-error.js\";\nimport { encodeEnvelope, envelopeCompress, envelopeDecompress, } from \"./envelope.js\";\nimport { assertReadMaxBytes } from \"./limit-io.js\";\nexport function pipeTo(source, ...rest) {\n    const [transforms, sink, opt] = pickTransformsAndSink(rest);\n    let iterable = source;\n    let abortable;\n    if ((opt === null || opt === void 0 ? void 0 : opt.propagateDownStreamError) === true) {\n        iterable = abortable = makeIterableAbortable(iterable);\n    }\n    // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n    // @ts-ignore\n    iterable = pipe(iterable, ...transforms, { propagateDownStreamError: false });\n    return sink(iterable).catch((reason) => {\n        if (abortable) {\n            return abortable.abort(reason).then(() => Promise.reject(reason));\n        }\n        return Promise.reject(reason);\n    });\n}\n// pick transforms, the sink, and options from the pipeTo() rest parameter\nfunction pickTransformsAndSink(rest) {\n    let opt;\n    if (typeof rest[rest.length - 1] != \"function\") {\n        opt = rest.pop();\n    }\n    const sink = rest.pop();\n    return [rest, sink, opt];\n}\n/**\n * Creates an AsyncIterableSink that concatenates all elements from the input.\n *\n * @private Internal code, does not follow semantic versioning.\n */\nexport function sinkAll() {\n    return async function (iterable) {\n        var _a, e_1, _b, _c;\n        const all = [];\n        try {\n            for (var _d = true, iterable_1 = __asyncValues(iterable), iterable_1_1; iterable_1_1 = await iterable_1.next(), _a = iterable_1_1.done, !_a; _d = true) {\n                _c = iterable_1_1.value;\n                _d = false;\n                const chunk = _c;\n                all.push(chunk);\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (!_d && !_a && (_b = iterable_1.return)) await _b.call(iterable_1);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        return all;\n    };\n}\n/**\n * Creates an AsyncIterableSink that concatenates all chunks from the input into\n * a single Uint8Array.\n *\n * The iterable raises an error if the more than readMaxBytes are read.\n *\n * An optional length hint can be provided to optimize allocation and validation.\n * If more or less bytes are present in the source that the length hint indicates,\n * and error is raised.\n * If the length hint is larger than readMaxBytes, an error is raised.\n * If the length hint is not a positive integer, it is ignored.\n *\n * @private Internal code, does not follow semantic versioning.\n */\nexport function sinkAllBytes(readMaxBytes, lengthHint) {\n    return async function (iterable) {\n        return await readAllBytes(iterable, readMaxBytes, lengthHint);\n    };\n}\nexport function pipe(source, ...rest) {\n    return __asyncGenerator(this, arguments, function* pipe_1() {\n        var _a;\n        const [transforms, opt] = pickTransforms(rest);\n        let abortable;\n        const sourceIt = source[Symbol.asyncIterator]();\n        const cachedSource = {\n            [Symbol.asyncIterator]() {\n                return sourceIt;\n            },\n        };\n        let iterable = cachedSource;\n        if ((opt === null || opt === void 0 ? void 0 : opt.propagateDownStreamError) === true) {\n            iterable = abortable = makeIterableAbortable(iterable);\n        }\n        for (const t of transforms) {\n            iterable = t(iterable);\n        }\n        const it = iterable[Symbol.asyncIterator]();\n        try {\n            for (;;) {\n                const r = yield __await(it.next());\n                if (r.done === true) {\n                    break;\n                }\n                if (!abortable) {\n                    yield yield __await(r.value);\n                    continue;\n                }\n                try {\n                    yield yield __await(r.value);\n                }\n                catch (e) {\n                    yield __await(abortable.abort(e)); // propagate downstream error to the source\n                    throw e;\n                }\n            }\n        }\n        finally {\n            if ((opt === null || opt === void 0 ? void 0 : opt.propagateDownStreamError) === true) {\n                // Call return on the source iterable to indicate\n                // that we will no longer consume it and it should\n                // cleanup any allocated resources.\n                (_a = sourceIt.return) === null || _a === void 0 ? void 0 : _a.call(sourceIt).catch(() => {\n                    // return returns a promise, which we don't care about.\n                    //\n                    // Uncaught promises are thrown at sometime/somewhere by the event loop,\n                    // this is to ensure error is caught and ignored.\n                });\n            }\n        }\n    });\n}\nfunction pickTransforms(rest) {\n    let opt;\n    if (typeof rest[rest.length - 1] != \"function\") {\n        opt = rest.pop();\n    }\n    return [rest, opt];\n}\n/**\n * Creates an AsyncIterableTransform that catches any error from the input, and\n * passes it to the given catchError function.\n *\n * The catchError function may return a final value.\n *\n * @private Internal code, does not follow semantic versioning.\n */\nexport function transformCatch(catchError) {\n    return function (iterable) {\n        return __asyncGenerator(this, arguments, function* () {\n            // we deliberate avoid a for-await loop because we only want to catch upstream\n            // errors, not downstream errors (yield).\n            const it = iterable[Symbol.asyncIterator]();\n            for (;;) {\n                let r;\n                try {\n                    r = yield __await(it.next());\n                }\n                catch (e) {\n                    const caught = yield __await(catchError(e));\n                    if (caught !== undefined) {\n                        yield yield __await(caught);\n                    }\n                    break;\n                }\n                if (r.done === true) {\n                    break;\n                }\n                yield yield __await(r.value);\n            }\n        });\n    };\n}\n/**\n * Creates an AsyncIterableTransform that catches any error from the input, and\n * passes it to the given function. Unlike transformCatch(), the given function\n * is also called when no error is raised.\n *\n * @private Internal code, does not follow semantic versioning.\n */\nexport function transformCatchFinally(catchFinally) {\n    return function (iterable) {\n        return __asyncGenerator(this, arguments, function* () {\n            // we deliberate avoid a for-await loop because we only want to catch upstream\n            // errors, not downstream errors (yield).\n            let err;\n            const it = iterable[Symbol.asyncIterator]();\n            for (;;) {\n                let r;\n                try {\n                    r = yield __await(it.next());\n                }\n                catch (e) {\n                    err = e;\n                    break;\n                }\n                if (r.done === true) {\n                    break;\n                }\n                yield yield __await(r.value);\n            }\n            const caught = yield __await(catchFinally(err));\n            if (caught !== undefined) {\n                yield yield __await(caught);\n            }\n        });\n    };\n}\n/**\n * Creates an AsyncIterableTransform that appends a value.\n *\n * The element to append is provided by a function. If the function returns\n * undefined, no element is appended.\n *\n * @private Internal code, does not follow semantic versioning.\n */\nexport function transformAppend(provide) {\n    return function (iterable) {\n        return __asyncGenerator(this, arguments, function* () {\n            var _a, e_2, _b, _c;\n            try {\n                for (var _d = true, iterable_2 = __asyncValues(iterable), iterable_2_1; iterable_2_1 = yield __await(iterable_2.next()), _a = iterable_2_1.done, !_a; _d = true) {\n                    _c = iterable_2_1.value;\n                    _d = false;\n                    const chunk = _c;\n                    yield yield __await(chunk);\n                }\n            }\n            catch (e_2_1) { e_2 = { error: e_2_1 }; }\n            finally {\n                try {\n                    if (!_d && !_a && (_b = iterable_2.return)) yield __await(_b.call(iterable_2));\n                }\n                finally { if (e_2) throw e_2.error; }\n            }\n            const append = yield __await(provide());\n            if (append !== undefined) {\n                yield yield __await(append);\n            }\n        });\n    };\n}\n/**\n * Creates an AsyncIterableTransform that prepends an element.\n *\n * The element to prepend is provided by a function. If the function returns\n * undefined, no element is appended.\n *\n * @private Internal code, does not follow semantic versioning.\n */\nexport function transformPrepend(provide) {\n    return function (iterable) {\n        return __asyncGenerator(this, arguments, function* () {\n            var _a, e_3, _b, _c;\n            const prepend = yield __await(provide());\n            if (prepend !== undefined) {\n                yield yield __await(prepend);\n            }\n            try {\n                for (var _d = true, iterable_3 = __asyncValues(iterable), iterable_3_1; iterable_3_1 = yield __await(iterable_3.next()), _a = iterable_3_1.done, !_a; _d = true) {\n                    _c = iterable_3_1.value;\n                    _d = false;\n                    const chunk = _c;\n                    yield yield __await(chunk);\n                }\n            }\n            catch (e_3_1) { e_3 = { error: e_3_1 }; }\n            finally {\n                try {\n                    if (!_d && !_a && (_b = iterable_3.return)) yield __await(_b.call(iterable_3));\n                }\n                finally { if (e_3) throw e_3.error; }\n            }\n        });\n    };\n}\n/**\n * Creates an AsyncIterableTransform that reads all bytes from the input, and\n * concatenates them to a single Uint8Array.\n *\n * The iterable raises an error if the more than readMaxBytes are read.\n *\n * An optional length hint can be provided to optimize allocation and validation.\n * If more or less bytes are present in the source that the length hint indicates,\n * and error is raised.\n * If the length hint is larger than readMaxBytes, an error is raised.\n * If the length hint is not a positive integer, it is ignored.\n *\n * @private Internal code, does not follow semantic versioning.\n */\nexport function transformReadAllBytes(readMaxBytes, lengthHint) {\n    return function (iterable) {\n        return __asyncGenerator(this, arguments, function* () {\n            yield yield __await(yield __await(readAllBytes(iterable, readMaxBytes, lengthHint)));\n        });\n    };\n}\nexport function transformSerializeEnvelope(serialization, endStreamFlag, endSerialization) {\n    if (endStreamFlag === undefined || endSerialization === undefined) {\n        return function (iterable) {\n            return __asyncGenerator(this, arguments, function* () {\n                var _a, e_4, _b, _c;\n                try {\n                    for (var _d = true, iterable_4 = __asyncValues(iterable), iterable_4_1; iterable_4_1 = yield __await(iterable_4.next()), _a = iterable_4_1.done, !_a; _d = true) {\n                        _c = iterable_4_1.value;\n                        _d = false;\n                        const chunk = _c;\n                        const data = serialization.serialize(chunk);\n                        yield yield __await({ flags: 0, data });\n                    }\n                }\n                catch (e_4_1) { e_4 = { error: e_4_1 }; }\n                finally {\n                    try {\n                        if (!_d && !_a && (_b = iterable_4.return)) yield __await(_b.call(iterable_4));\n                    }\n                    finally { if (e_4) throw e_4.error; }\n                }\n            });\n        };\n    }\n    return function (iterable) {\n        return __asyncGenerator(this, arguments, function* () {\n            var _a, e_5, _b, _c;\n            try {\n                for (var _d = true, iterable_5 = __asyncValues(iterable), iterable_5_1; iterable_5_1 = yield __await(iterable_5.next()), _a = iterable_5_1.done, !_a; _d = true) {\n                    _c = iterable_5_1.value;\n                    _d = false;\n                    const chunk = _c;\n                    let data;\n                    let flags = 0;\n                    if (chunk.end) {\n                        flags = flags | endStreamFlag;\n                        data = endSerialization.serialize(chunk.value);\n                    }\n                    else {\n                        data = serialization.serialize(chunk.value);\n                    }\n                    yield yield __await({ flags, data });\n                }\n            }\n            catch (e_5_1) { e_5 = { error: e_5_1 }; }\n            finally {\n                try {\n                    if (!_d && !_a && (_b = iterable_5.return)) yield __await(_b.call(iterable_5));\n                }\n                finally { if (e_5) throw e_5.error; }\n            }\n        });\n    };\n}\nexport function transformParseEnvelope(serialization, endStreamFlag, endSerialization) {\n    // code path always yields ParsedEnvelopedMessage<T, E>\n    if (endSerialization && endStreamFlag !== undefined) {\n        return function (iterable) {\n            return __asyncGenerator(this, arguments, function* () {\n                var _a, e_6, _b, _c;\n                try {\n                    for (var _d = true, iterable_6 = __asyncValues(iterable), iterable_6_1; iterable_6_1 = yield __await(iterable_6.next()), _a = iterable_6_1.done, !_a; _d = true) {\n                        _c = iterable_6_1.value;\n                        _d = false;\n                        const { flags, data } = _c;\n                        if ((flags & endStreamFlag) === endStreamFlag) {\n                            yield yield __await({ value: endSerialization.parse(data), end: true });\n                        }\n                        else {\n                            yield yield __await({ value: serialization.parse(data), end: false });\n                        }\n                    }\n                }\n                catch (e_6_1) { e_6 = { error: e_6_1 }; }\n                finally {\n                    try {\n                        if (!_d && !_a && (_b = iterable_6.return)) yield __await(_b.call(iterable_6));\n                    }\n                    finally { if (e_6) throw e_6.error; }\n                }\n            });\n        };\n    }\n    // code path always yields T\n    return function (iterable) {\n        return __asyncGenerator(this, arguments, function* () {\n            var _a, e_7, _b, _c;\n            try {\n                for (var _d = true, iterable_7 = __asyncValues(iterable), iterable_7_1; iterable_7_1 = yield __await(iterable_7.next()), _a = iterable_7_1.done, !_a; _d = true) {\n                    _c = iterable_7_1.value;\n                    _d = false;\n                    const { flags, data } = _c;\n                    if (endStreamFlag !== undefined &&\n                        (flags & endStreamFlag) === endStreamFlag) {\n                        if (endSerialization === null) {\n                            throw new ConnectError(\"unexpected end flag\", Code.InvalidArgument);\n                        }\n                        // skips end-of-stream envelope\n                        continue;\n                    }\n                    yield yield __await(serialization.parse(data));\n                }\n            }\n            catch (e_7_1) { e_7 = { error: e_7_1 }; }\n            finally {\n                try {\n                    if (!_d && !_a && (_b = iterable_7.return)) yield __await(_b.call(iterable_7));\n                }\n                finally { if (e_7) throw e_7.error; }\n            }\n        });\n    };\n}\n/**\n * Creates an AsyncIterableTransform that takes enveloped messages as a source,\n * and compresses them if they are larger than compressMinBytes.\n *\n * @private Internal code, does not follow semantic versioning.\n */\nexport function transformCompressEnvelope(compression, compressMinBytes) {\n    return function (iterable) {\n        return __asyncGenerator(this, arguments, function* () {\n            var _a, e_8, _b, _c;\n            try {\n                for (var _d = true, iterable_8 = __asyncValues(iterable), iterable_8_1; iterable_8_1 = yield __await(iterable_8.next()), _a = iterable_8_1.done, !_a; _d = true) {\n                    _c = iterable_8_1.value;\n                    _d = false;\n                    const env = _c;\n                    yield yield __await(yield __await(envelopeCompress(env, compression, compressMinBytes)));\n                }\n            }\n            catch (e_8_1) { e_8 = { error: e_8_1 }; }\n            finally {\n                try {\n                    if (!_d && !_a && (_b = iterable_8.return)) yield __await(_b.call(iterable_8));\n                }\n                finally { if (e_8) throw e_8.error; }\n            }\n        });\n    };\n}\n/**\n * Creates an AsyncIterableTransform that takes enveloped messages as a source,\n * and decompresses them using the given compression.\n *\n * The iterable raises an error if the decompressed payload of an enveloped\n * message is larger than readMaxBytes, or if no compression is provided.\n *\n * @private Internal code, does not follow semantic versioning.\n */\nexport function transformDecompressEnvelope(compression, readMaxBytes) {\n    return function (iterable) {\n        return __asyncGenerator(this, arguments, function* () {\n            var _a, e_9, _b, _c;\n            try {\n                for (var _d = true, iterable_9 = __asyncValues(iterable), iterable_9_1; iterable_9_1 = yield __await(iterable_9.next()), _a = iterable_9_1.done, !_a; _d = true) {\n                    _c = iterable_9_1.value;\n                    _d = false;\n                    const env = _c;\n                    yield yield __await(yield __await(envelopeDecompress(env, compression, readMaxBytes)));\n                }\n            }\n            catch (e_9_1) { e_9 = { error: e_9_1 }; }\n            finally {\n                try {\n                    if (!_d && !_a && (_b = iterable_9.return)) yield __await(_b.call(iterable_9));\n                }\n                finally { if (e_9) throw e_9.error; }\n            }\n        });\n    };\n}\n/**\n * Create an AsyncIterableTransform that takes enveloped messages as a source,\n * and joins them into a stream of raw bytes.\n *\n * @private Internal code, does not follow semantic versioning.\n */\nexport function transformJoinEnvelopes() {\n    return function (iterable) {\n        return __asyncGenerator(this, arguments, function* () {\n            var _a, e_10, _b, _c;\n            try {\n                for (var _d = true, iterable_10 = __asyncValues(iterable), iterable_10_1; iterable_10_1 = yield __await(iterable_10.next()), _a = iterable_10_1.done, !_a; _d = true) {\n                    _c = iterable_10_1.value;\n                    _d = false;\n                    const { flags, data } = _c;\n                    yield yield __await(encodeEnvelope(flags, data));\n                }\n            }\n            catch (e_10_1) { e_10 = { error: e_10_1 }; }\n            finally {\n                try {\n                    if (!_d && !_a && (_b = iterable_10.return)) yield __await(_b.call(iterable_10));\n                }\n                finally { if (e_10) throw e_10.error; }\n            }\n        });\n    };\n}\n/**\n * Create an AsyncIterableTransform that takes raw bytes as a source, and splits\n * them into enveloped messages.\n *\n * The iterable raises an error\n * - if the payload of an enveloped message is larger than readMaxBytes,\n * - if the stream ended before an enveloped message fully arrived,\n * - or if the stream ended with extraneous data.\n *\n * @private Internal code, does not follow semantic versioning.\n */\nexport function transformSplitEnvelope(readMaxBytes) {\n    // append chunk to buffer, returning updated buffer\n    function append(buffer, chunk) {\n        const n = new Uint8Array(buffer.byteLength + chunk.byteLength);\n        n.set(buffer);\n        n.set(chunk, buffer.length);\n        return n;\n    }\n    // tuple 0: envelope, or undefined if incomplete\n    // tuple 1: remainder of the buffer\n    function shiftEnvelope(buffer, header) {\n        if (buffer.byteLength < 5 + header.length) {\n            return [undefined, buffer];\n        }\n        return [\n            { flags: header.flags, data: buffer.subarray(5, 5 + header.length) },\n            buffer.subarray(5 + header.length),\n        ];\n    }\n    // undefined: header is incomplete\n    function peekHeader(buffer) {\n        if (buffer.byteLength < 5) {\n            return undefined;\n        }\n        const view = new DataView(buffer.buffer, buffer.byteOffset, buffer.byteLength);\n        const length = view.getUint32(1); // 4 bytes message length\n        const flags = view.getUint8(0); // first byte is flags\n        return { length, flags };\n    }\n    return function (iterable) {\n        return __asyncGenerator(this, arguments, function* () {\n            var _a, e_11, _b, _c;\n            let buffer = new Uint8Array(0);\n            try {\n                for (var _d = true, iterable_11 = __asyncValues(iterable), iterable_11_1; iterable_11_1 = yield __await(iterable_11.next()), _a = iterable_11_1.done, !_a; _d = true) {\n                    _c = iterable_11_1.value;\n                    _d = false;\n                    const chunk = _c;\n                    buffer = append(buffer, chunk);\n                    for (;;) {\n                        const header = peekHeader(buffer);\n                        if (!header) {\n                            break;\n                        }\n                        assertReadMaxBytes(readMaxBytes, header.length, true);\n                        let env;\n                        [env, buffer] = shiftEnvelope(buffer, header);\n                        if (!env) {\n                            break;\n                        }\n                        yield yield __await(env);\n                    }\n                }\n            }\n            catch (e_11_1) { e_11 = { error: e_11_1 }; }\n            finally {\n                try {\n                    if (!_d && !_a && (_b = iterable_11.return)) yield __await(_b.call(iterable_11));\n                }\n                finally { if (e_11) throw e_11.error; }\n            }\n            if (buffer.byteLength > 0) {\n                const header = peekHeader(buffer);\n                let message = \"protocol error: incomplete envelope\";\n                if (header) {\n                    message = `protocol error: promised ${header.length} bytes in enveloped message, got ${buffer.byteLength - 5} bytes`;\n                }\n                throw new ConnectError(message, Code.InvalidArgument);\n            }\n        });\n    };\n}\n/**\n * Reads all bytes from the source, and concatenates them to a single Uint8Array.\n *\n * Raises an error if:\n * - more than readMaxBytes are read\n * - lengthHint is a positive integer, but larger than readMaxBytes\n * - lengthHint is a positive integer, and the source contains more or less bytes\n *   than promised\n *\n * @private Internal code, does not follow semantic versioning.\n */\nexport async function readAllBytes(iterable, readMaxBytes, lengthHint) {\n    var _a, e_12, _b, _c, _d, e_13, _e, _f;\n    const [ok, hint] = parseLengthHint(lengthHint);\n    if (ok) {\n        if (hint > readMaxBytes) {\n            assertReadMaxBytes(readMaxBytes, hint, true);\n        }\n        const buffer = new Uint8Array(hint);\n        let offset = 0;\n        try {\n            for (var _g = true, iterable_12 = __asyncValues(iterable), iterable_12_1; iterable_12_1 = await iterable_12.next(), _a = iterable_12_1.done, !_a; _g = true) {\n                _c = iterable_12_1.value;\n                _g = false;\n                const chunk = _c;\n                if (offset + chunk.byteLength > hint) {\n                    throw new ConnectError(`protocol error: promised ${hint} bytes, received ${offset + chunk.byteLength}`, Code.InvalidArgument);\n                }\n                buffer.set(chunk, offset);\n                offset += chunk.byteLength;\n            }\n        }\n        catch (e_12_1) { e_12 = { error: e_12_1 }; }\n        finally {\n            try {\n                if (!_g && !_a && (_b = iterable_12.return)) await _b.call(iterable_12);\n            }\n            finally { if (e_12) throw e_12.error; }\n        }\n        if (offset < hint) {\n            throw new ConnectError(`protocol error: promised ${hint} bytes, received ${offset}`, Code.InvalidArgument);\n        }\n        return buffer;\n    }\n    const chunks = [];\n    let count = 0;\n    try {\n        for (var _h = true, iterable_13 = __asyncValues(iterable), iterable_13_1; iterable_13_1 = await iterable_13.next(), _d = iterable_13_1.done, !_d; _h = true) {\n            _f = iterable_13_1.value;\n            _h = false;\n            const chunk = _f;\n            count += chunk.byteLength;\n            assertReadMaxBytes(readMaxBytes, count);\n            chunks.push(chunk);\n        }\n    }\n    catch (e_13_1) { e_13 = { error: e_13_1 }; }\n    finally {\n        try {\n            if (!_h && !_d && (_e = iterable_13.return)) await _e.call(iterable_13);\n        }\n        finally { if (e_13) throw e_13.error; }\n    }\n    const all = new Uint8Array(count);\n    let offset = 0;\n    for (let chunk = chunks.shift(); chunk; chunk = chunks.shift()) {\n        all.set(chunk, offset);\n        offset += chunk.byteLength;\n    }\n    return all;\n}\n// parse the lengthHint argument of readAllBytes()\nfunction parseLengthHint(lengthHint) {\n    if (lengthHint === undefined || lengthHint === null) {\n        return [false, 0];\n    }\n    const n = typeof lengthHint == \"string\" ? parseInt(lengthHint, 10) : lengthHint;\n    if (!Number.isSafeInteger(n) || n < 0) {\n        return [false, n];\n    }\n    return [true, n];\n}\n/**\n * Wait for the first element of an iterable without modifying the iterable.\n * This consumes the first element, but pushes it back on the stack.\n *\n * @private Internal code, does not follow semantic versioning.\n */\nexport async function untilFirst(iterable) {\n    const it = iterable[Symbol.asyncIterator]();\n    let first = await it.next();\n    return {\n        [Symbol.asyncIterator]() {\n            const w = {\n                async next() {\n                    if (first !== null) {\n                        const n = first;\n                        first = null;\n                        return n;\n                    }\n                    return await it.next();\n                },\n            };\n            if (it.throw !== undefined) {\n                // eslint-disable-next-line @typescript-eslint/no-non-null-assertion -- can't handle mutated object sensibly\n                w.throw = (e) => it.throw(e);\n            }\n            if (it.return !== undefined) {\n                // eslint-disable-next-line @typescript-eslint/no-non-null-assertion,@typescript-eslint/no-explicit-any -- can't handle mutated object sensibly\n                w.return = (value) => it.return(value);\n            }\n            return w;\n        },\n    };\n}\n/**\n * Wrap the given iterable and return an iterable with an abort() method.\n *\n * This function exists purely for convenience. Where one would typically have\n * to access the iterator directly, advance through all elements, and call\n * AsyncIterator.throw() to notify the upstream iterable, this function allows\n * to use convenient for-await loops and still notify the upstream iterable:\n *\n * ```ts\n * const abortable = makeIterableAbortable(iterable);\n * for await (const ele of abortable) {\n *   await abortable.abort(\"ERR\");\n * }\n * ```\n * There are a couple of limitations of this function:\n * - the given async iterable must implement throw\n * - the async iterable cannot be re-use\n * - if source catches errors and yields values for them, they are ignored, and\n *   the source may still dangle\n *\n * There are four possible ways an async function* can handle yield errors:\n * 1. don't catch errors at all - Abortable.abort() will resolve \"rethrown\"\n * 2. catch errors and rethrow - Abortable.abort() will resolve \"rethrown\"\n * 3. catch errors and return - Abortable.abort() will resolve \"completed\"\n * 4. catch errors and yield a value - Abortable.abort() will resolve \"caught\"\n *\n * Note that catching errors and yielding a value is problematic, and it should\n * be documented that this may leave the source in a dangling state.\n *\n * @private Internal code, does not follow semantic versioning.\n */\nexport function makeIterableAbortable(iterable) {\n    const innerCandidate = iterable[Symbol.asyncIterator]();\n    if (innerCandidate.throw === undefined) {\n        throw new Error(\"AsyncIterable does not implement throw\");\n    }\n    const inner = innerCandidate;\n    let aborted;\n    let resultPromise;\n    let it = {\n        next() {\n            resultPromise = inner.next().finally(() => {\n                resultPromise = undefined;\n            });\n            return resultPromise;\n        },\n        throw(e) {\n            return inner.throw(e);\n        },\n    };\n    if (innerCandidate.return !== undefined) {\n        it = Object.assign(Object.assign({}, it), { return(value) {\n                return inner.return(value);\n            } });\n    }\n    let used = false;\n    return {\n        abort(reason) {\n            if (aborted) {\n                return aborted.state;\n            }\n            const f = () => {\n                return inner.throw(reason).then((r) => (r.done === true ? \"completed\" : \"caught\"), () => \"rethrown\");\n            };\n            if (resultPromise) {\n                aborted = { reason, state: resultPromise.then(f, f) };\n                return aborted.state;\n            }\n            aborted = { reason, state: f() };\n            return aborted.state;\n        },\n        [Symbol.asyncIterator]() {\n            if (used) {\n                throw new Error(\"AsyncIterable cannot be re-used\");\n            }\n            used = true;\n            return it;\n        },\n    };\n}\n/**\n * Create a new WritableIterable.\n */\nexport function createWritableIterable() {\n    // We start with two queues to capture the read and write attempts.\n    //\n    // The writes and reads each check of their counterpart is\n    // already available and either interact/add themselves to the queue.\n    const readQueue = [];\n    const writeQueue = [];\n    let err = undefined;\n    let nextResolve;\n    let nextReject;\n    let nextPromise = new Promise((resolve, reject) => {\n        nextResolve = resolve;\n        nextReject = reject;\n    });\n    let closed = false;\n    // drain the readQueue in case of error/writer is closed by sending a\n    // done result.\n    function drain() {\n        for (const next of readQueue.splice(0, readQueue.length)) {\n            next({ done: true, value: undefined });\n        }\n    }\n    return {\n        close() {\n            closed = true;\n            drain();\n        },\n        async write(payload) {\n            if (closed) {\n                throw err !== null && err !== void 0 ? err : new Error(\"cannot write, WritableIterable already closed\");\n            }\n            const read = readQueue.shift();\n            if (read === undefined) {\n                // We didn't find a pending read so we add the payload to the write queue.\n                writeQueue.push(payload);\n            }\n            else {\n                // We found a pending read so we respond with the payload.\n                read({ done: false, value: payload });\n                if (readQueue.length > 0) {\n                    // If there are more in the read queue we can mark the write as complete.\n                    // as the error reporting is not guaranteed to be sequential and therefore cannot\n                    // to linked to a specific write.\n                    return;\n                }\n            }\n            // We await the next call for as many times as there are items in the queue + 1\n            //\n            // If there are no items in the write queue that means write happened and we just have\n            // to wait for one more call likewise if we are the nth write in the queue we\n            // have to wait for n writes to complete and one more.\n            const limit = writeQueue.length + 1;\n            for (let i = 0; i < limit; i++) {\n                await nextPromise;\n            }\n        },\n        [Symbol.asyncIterator]() {\n            return {\n                next() {\n                    // Resolve the nextPromise to indicate\n                    // pending writes that a read attempt has been made\n                    // after their write.\n                    //\n                    // We also need to reset the promise for future writes.\n                    nextResolve();\n                    nextPromise = new Promise((resolve, reject) => {\n                        nextResolve = resolve;\n                        nextReject = reject;\n                    });\n                    const write = writeQueue.shift();\n                    if (write !== undefined) {\n                        // We found a pending write so response with the payload.\n                        return Promise.resolve({ done: false, value: write });\n                    }\n                    if (closed) {\n                        return Promise.resolve({ done: true, value: undefined });\n                    }\n                    // We return a promise immediately that is either resolved/rejected\n                    // as writes happen.\n                    let readResolve;\n                    const readPromise = new Promise((resolve) => (readResolve = resolve));\n                    readQueue.push(readResolve); // eslint-disable-line @typescript-eslint/no-non-null-assertion\n                    return readPromise;\n                },\n                throw(throwErr) {\n                    err = throwErr;\n                    closed = true;\n                    writeQueue.splice(0, writeQueue.length);\n                    nextPromise.catch(() => {\n                        // To make sure that the nextPromise is always resolved.\n                    });\n                    // This will reject all pending writes.\n                    nextReject(err);\n                    drain();\n                    return Promise.resolve({ done: true, value: undefined });\n                },\n                return() {\n                    closed = true;\n                    writeQueue.splice(0, writeQueue.length);\n                    // Resolve once for the write awaiting confirmation.\n                    nextResolve();\n                    // Reject all future writes.\n                    nextPromise = Promise.reject(new Error(\"cannot write, consumer called return\"));\n                    nextPromise.catch(() => {\n                        // To make sure that the nextPromise is always resolved.\n                    });\n                    drain();\n                    return Promise.resolve({ done: true, value: undefined });\n                },\n            };\n        },\n    };\n}\n/**\n * Create an asynchronous iterable from an array.\n *\n * @private Internal code, does not follow semantic versioning.\n */\n// eslint-disable-next-line @typescript-eslint/require-await\nexport function createAsyncIterable(items) {\n    return __asyncGenerator(this, arguments, function* createAsyncIterable_1() {\n        yield __await(yield* __asyncDelegator(__asyncValues(items)));\n    });\n}\n"], "names": [], "mappings": "AAAA,0CAA0C;AAC1C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;;;;;;;;;;;;;;;;;;;;;;AA0BjC;AACA;AACA;AACA;AA5BA,IAAI,gBAAgB,AAAC,IAAI,IAAI,IAAI,CAAC,aAAa,IAAK,SAAU,CAAC;IAC3D,IAAI,CAAC,OAAO,aAAa,EAAE,MAAM,IAAI,UAAU;IAC/C,IAAI,IAAI,CAAC,CAAC,OAAO,aAAa,CAAC,EAAE;IACjC,OAAO,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,OAAO,aAAa,aAAa,SAAS,KAAK,CAAC,CAAC,OAAO,QAAQ,CAAC,IAAI,IAAI,CAAC,GAAG,KAAK,SAAS,KAAK,UAAU,KAAK,WAAW,CAAC,CAAC,OAAO,aAAa,CAAC,GAAG;QAAc,OAAO,IAAI;IAAE,GAAG,CAAC;;IAC/M,SAAS,KAAK,CAAC;QAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,SAAU,CAAC;YAAI,OAAO,IAAI,QAAQ,SAAU,OAAO,EAAE,MAAM;gBAAI,IAAI,CAAC,CAAC,EAAE,CAAC,IAAI,OAAO,SAAS,QAAQ,EAAE,IAAI,EAAE,EAAE,KAAK;YAAG;QAAI;IAAG;IAC/J,SAAS,OAAO,OAAO,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;QAAI,QAAQ,OAAO,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;YAAI,QAAQ;gBAAE,OAAO;gBAAG,MAAM;YAAE;QAAI,GAAG;IAAS;AAC/H;AACA,IAAI,UAAU,AAAC,IAAI,IAAI,IAAI,CAAC,OAAO,IAAK,SAAU,CAAC;IAAI,OAAO,IAAI,YAAY,UAAU,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,IAAI,IAAI,IAAI,QAAQ;AAAI;AAC7H,IAAI,mBAAmB,AAAC,IAAI,IAAI,IAAI,CAAC,gBAAgB,IAAK,SAAU,OAAO,EAAE,UAAU,EAAE,SAAS;IAC9F,IAAI,CAAC,OAAO,aAAa,EAAE,MAAM,IAAI,UAAU;IAC/C,IAAI,IAAI,UAAU,KAAK,CAAC,SAAS,cAAc,EAAE,GAAG,GAAG,IAAI,EAAE;IAC7D,OAAO,IAAI,CAAC,GAAG,KAAK,SAAS,KAAK,UAAU,KAAK,UAAU,cAAc,CAAC,CAAC,OAAO,aAAa,CAAC,GAAG;QAAc,OAAO,IAAI;IAAE,GAAG;;IACjI,SAAS,YAAY,CAAC;QAAI,OAAO,SAAU,CAAC;YAAI,OAAO,QAAQ,OAAO,CAAC,GAAG,IAAI,CAAC,GAAG;QAAS;IAAG;IAC9F,SAAS,KAAK,CAAC,EAAE,CAAC;QAAI,IAAI,CAAC,CAAC,EAAE,EAAE;YAAE,CAAC,CAAC,EAAE,GAAG,SAAU,CAAC;gBAAI,OAAO,IAAI,QAAQ,SAAU,CAAC,EAAE,CAAC;oBAAI,EAAE,IAAI,CAAC;wBAAC;wBAAG;wBAAG;wBAAG;qBAAE,IAAI,KAAK,OAAO,GAAG;gBAAI;YAAI;YAAG,IAAI,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE;QAAG;IAAE;IACvK,SAAS,OAAO,CAAC,EAAE,CAAC;QAAI,IAAI;YAAE,KAAK,CAAC,CAAC,EAAE,CAAC;QAAK,EAAE,OAAO,GAAG;YAAE,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;QAAI;IAAE;IACjF,SAAS,KAAK,CAAC;QAAI,EAAE,KAAK,YAAY,UAAU,QAAQ,OAAO,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,UAAU,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;IAAI;IACvH,SAAS,QAAQ,KAAK;QAAI,OAAO,QAAQ;IAAQ;IACjD,SAAS,OAAO,KAAK;QAAI,OAAO,SAAS;IAAQ;IACjD,SAAS,OAAO,CAAC,EAAE,CAAC;QAAI,IAAI,EAAE,IAAI,EAAE,KAAK,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;IAAG;AACrF;AACA,IAAI,mBAAmB,AAAC,IAAI,IAAI,IAAI,CAAC,gBAAgB,IAAK,SAAU,CAAC;IACjE,IAAI,GAAG;IACP,OAAO,IAAI,CAAC,GAAG,KAAK,SAAS,KAAK,SAAS,SAAU,CAAC;QAAI,MAAM;IAAG,IAAI,KAAK,WAAW,CAAC,CAAC,OAAO,QAAQ,CAAC,GAAG;QAAc,OAAO,IAAI;IAAE,GAAG;;IAC1I,SAAS,KAAK,CAAC,EAAE,CAAC;QAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,SAAU,CAAC;YAAI,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI;gBAAE,OAAO,QAAQ,CAAC,CAAC,EAAE,CAAC;gBAAK,MAAM;YAAM,IAAI,IAAI,EAAE,KAAK;QAAG,IAAI;IAAG;AACzI;;;;;AAKO,SAAS,OAAO,MAAM,EAAE,GAAG,IAAI;IAClC,MAAM,CAAC,YAAY,MAAM,IAAI,GAAG,sBAAsB;IACtD,IAAI,WAAW;IACf,IAAI;IACJ,IAAI,CAAC,QAAQ,QAAQ,QAAQ,KAAK,IAAI,KAAK,IAAI,IAAI,wBAAwB,MAAM,MAAM;QACnF,WAAW,YAAY,sBAAsB;IACjD;IACA,6DAA6D;IAC7D,aAAa;IACb,WAAW,KAAK,aAAa,YAAY;QAAE,0BAA0B;IAAM;IAC3E,OAAO,KAAK,UAAU,KAAK,CAAC,CAAC;QACzB,IAAI,WAAW;YACX,OAAO,UAAU,KAAK,CAAC,QAAQ,IAAI,CAAC,IAAM,QAAQ,MAAM,CAAC;QAC7D;QACA,OAAO,QAAQ,MAAM,CAAC;IAC1B;AACJ;AACA,0EAA0E;AAC1E,SAAS,sBAAsB,IAAI;IAC/B,IAAI;IACJ,IAAI,OAAO,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE,IAAI,YAAY;QAC5C,MAAM,KAAK,GAAG;IAClB;IACA,MAAM,OAAO,KAAK,GAAG;IACrB,OAAO;QAAC;QAAM;QAAM;KAAI;AAC5B;AAMO,SAAS;IACZ,OAAO,eAAgB,QAAQ;QAC3B,IAAI,IAAI,KAAK,IAAI;QACjB,MAAM,MAAM,EAAE;QACd,IAAI;YACA,IAAK,IAAI,KAAK,MAAM,aAAa,cAAc,WAAW,cAAc,eAAe,MAAM,WAAW,IAAI,IAAI,KAAK,aAAa,IAAI,EAAE,CAAC,IAAI,KAAK,KAAM;gBACpJ,KAAK,aAAa,KAAK;gBACvB,KAAK;gBACL,MAAM,QAAQ;gBACd,IAAI,IAAI,CAAC;YACb;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,WAAW,MAAM,GAAG,MAAM,GAAG,IAAI,CAAC;YAC9D,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;QACA,OAAO;IACX;AACJ;AAeO,SAAS,aAAa,YAAY,EAAE,UAAU;IACjD,OAAO,eAAgB,QAAQ;QAC3B,OAAO,MAAM,aAAa,UAAU,cAAc;IACtD;AACJ;AACO,SAAS,KAAK,MAAM,EAAE,GAAG,IAAI;IAChC,OAAO,iBAAiB,IAAI,EAAE,WAAW,UAAU;QAC/C,IAAI;QACJ,MAAM,CAAC,YAAY,IAAI,GAAG,eAAe;QACzC,IAAI;QACJ,MAAM,WAAW,MAAM,CAAC,OAAO,aAAa,CAAC;QAC7C,MAAM,eAAe;YACjB,CAAC,OAAO,aAAa,CAAC;gBAClB,OAAO;YACX;QACJ;QACA,IAAI,WAAW;QACf,IAAI,CAAC,QAAQ,QAAQ,QAAQ,KAAK,IAAI,KAAK,IAAI,IAAI,wBAAwB,MAAM,MAAM;YACnF,WAAW,YAAY,sBAAsB;QACjD;QACA,KAAK,MAAM,KAAK,WAAY;YACxB,WAAW,EAAE;QACjB;QACA,MAAM,KAAK,QAAQ,CAAC,OAAO,aAAa,CAAC;QACzC,IAAI;YACA,OAAS;gBACL,MAAM,IAAI,MAAM,QAAQ,GAAG,IAAI;gBAC/B,IAAI,EAAE,IAAI,KAAK,MAAM;oBACjB;gBACJ;gBACA,IAAI,CAAC,WAAW;oBACZ,MAAM,MAAM,QAAQ,EAAE,KAAK;oBAC3B;gBACJ;gBACA,IAAI;oBACA,MAAM,MAAM,QAAQ,EAAE,KAAK;gBAC/B,EACA,OAAO,GAAG;oBACN,MAAM,QAAQ,UAAU,KAAK,CAAC,KAAK,2CAA2C;oBAC9E,MAAM;gBACV;YACJ;QACJ,SACQ;YACJ,IAAI,CAAC,QAAQ,QAAQ,QAAQ,KAAK,IAAI,KAAK,IAAI,IAAI,wBAAwB,MAAM,MAAM;gBACnF,iDAAiD;gBACjD,kDAAkD;gBAClD,mCAAmC;gBACnC,CAAC,KAAK,SAAS,MAAM,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC,UAAU,KAAK,CAAC;gBAChF,uDAAuD;gBACvD,EAAE;gBACF,wEAAwE;gBACxE,iDAAiD;gBACrD;YACJ;QACJ;IACJ;AACJ;AACA,SAAS,eAAe,IAAI;IACxB,IAAI;IACJ,IAAI,OAAO,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE,IAAI,YAAY;QAC5C,MAAM,KAAK,GAAG;IAClB;IACA,OAAO;QAAC;QAAM;KAAI;AACtB;AASO,SAAS,eAAe,UAAU;IACrC,OAAO,SAAU,QAAQ;QACrB,OAAO,iBAAiB,IAAI,EAAE,WAAW;YACrC,8EAA8E;YAC9E,yCAAyC;YACzC,MAAM,KAAK,QAAQ,CAAC,OAAO,aAAa,CAAC;YACzC,OAAS;gBACL,IAAI;gBACJ,IAAI;oBACA,IAAI,MAAM,QAAQ,GAAG,IAAI;gBAC7B,EACA,OAAO,GAAG;oBACN,MAAM,SAAS,MAAM,QAAQ,WAAW;oBACxC,IAAI,WAAW,WAAW;wBACtB,MAAM,MAAM,QAAQ;oBACxB;oBACA;gBACJ;gBACA,IAAI,EAAE,IAAI,KAAK,MAAM;oBACjB;gBACJ;gBACA,MAAM,MAAM,QAAQ,EAAE,KAAK;YAC/B;QACJ;IACJ;AACJ;AAQO,SAAS,sBAAsB,YAAY;IAC9C,OAAO,SAAU,QAAQ;QACrB,OAAO,iBAAiB,IAAI,EAAE,WAAW;YACrC,8EAA8E;YAC9E,yCAAyC;YACzC,IAAI;YACJ,MAAM,KAAK,QAAQ,CAAC,OAAO,aAAa,CAAC;YACzC,OAAS;gBACL,IAAI;gBACJ,IAAI;oBACA,IAAI,MAAM,QAAQ,GAAG,IAAI;gBAC7B,EACA,OAAO,GAAG;oBACN,MAAM;oBACN;gBACJ;gBACA,IAAI,EAAE,IAAI,KAAK,MAAM;oBACjB;gBACJ;gBACA,MAAM,MAAM,QAAQ,EAAE,KAAK;YAC/B;YACA,MAAM,SAAS,MAAM,QAAQ,aAAa;YAC1C,IAAI,WAAW,WAAW;gBACtB,MAAM,MAAM,QAAQ;YACxB;QACJ;IACJ;AACJ;AASO,SAAS,gBAAgB,OAAO;IACnC,OAAO,SAAU,QAAQ;QACrB,OAAO,iBAAiB,IAAI,EAAE,WAAW;YACrC,IAAI,IAAI,KAAK,IAAI;YACjB,IAAI;gBACA,IAAK,IAAI,KAAK,MAAM,aAAa,cAAc,WAAW,cAAc,eAAe,MAAM,QAAQ,WAAW,IAAI,KAAK,KAAK,aAAa,IAAI,EAAE,CAAC,IAAI,KAAK,KAAM;oBAC7J,KAAK,aAAa,KAAK;oBACvB,KAAK;oBACL,MAAM,QAAQ;oBACd,MAAM,MAAM,QAAQ;gBACxB;YACJ,EACA,OAAO,OAAO;gBAAE,MAAM;oBAAE,OAAO;gBAAM;YAAG,SAChC;gBACJ,IAAI;oBACA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,WAAW,MAAM,GAAG,MAAM,QAAQ,GAAG,IAAI,CAAC;gBACtE,SACQ;oBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;gBAAE;YACxC;YACA,MAAM,SAAS,MAAM,QAAQ;YAC7B,IAAI,WAAW,WAAW;gBACtB,MAAM,MAAM,QAAQ;YACxB;QACJ;IACJ;AACJ;AASO,SAAS,iBAAiB,OAAO;IACpC,OAAO,SAAU,QAAQ;QACrB,OAAO,iBAAiB,IAAI,EAAE,WAAW;YACrC,IAAI,IAAI,KAAK,IAAI;YACjB,MAAM,UAAU,MAAM,QAAQ;YAC9B,IAAI,YAAY,WAAW;gBACvB,MAAM,MAAM,QAAQ;YACxB;YACA,IAAI;gBACA,IAAK,IAAI,KAAK,MAAM,aAAa,cAAc,WAAW,cAAc,eAAe,MAAM,QAAQ,WAAW,IAAI,KAAK,KAAK,aAAa,IAAI,EAAE,CAAC,IAAI,KAAK,KAAM;oBAC7J,KAAK,aAAa,KAAK;oBACvB,KAAK;oBACL,MAAM,QAAQ;oBACd,MAAM,MAAM,QAAQ;gBACxB;YACJ,EACA,OAAO,OAAO;gBAAE,MAAM;oBAAE,OAAO;gBAAM;YAAG,SAChC;gBACJ,IAAI;oBACA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,WAAW,MAAM,GAAG,MAAM,QAAQ,GAAG,IAAI,CAAC;gBACtE,SACQ;oBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;gBAAE;YACxC;QACJ;IACJ;AACJ;AAeO,SAAS,sBAAsB,YAAY,EAAE,UAAU;IAC1D,OAAO,SAAU,QAAQ;QACrB,OAAO,iBAAiB,IAAI,EAAE,WAAW;YACrC,MAAM,MAAM,QAAQ,CAAA,MAAM,QAAQ,aAAa,UAAU,cAAc,YAAW;QACtF;IACJ;AACJ;AACO,SAAS,2BAA2B,aAAa,EAAE,aAAa,EAAE,gBAAgB;IACrF,IAAI,kBAAkB,aAAa,qBAAqB,WAAW;QAC/D,OAAO,SAAU,QAAQ;YACrB,OAAO,iBAAiB,IAAI,EAAE,WAAW;gBACrC,IAAI,IAAI,KAAK,IAAI;gBACjB,IAAI;oBACA,IAAK,IAAI,KAAK,MAAM,aAAa,cAAc,WAAW,cAAc,eAAe,MAAM,QAAQ,WAAW,IAAI,KAAK,KAAK,aAAa,IAAI,EAAE,CAAC,IAAI,KAAK,KAAM;wBAC7J,KAAK,aAAa,KAAK;wBACvB,KAAK;wBACL,MAAM,QAAQ;wBACd,MAAM,OAAO,cAAc,SAAS,CAAC;wBACrC,MAAM,MAAM,QAAQ;4BAAE,OAAO;4BAAG;wBAAK;oBACzC;gBACJ,EACA,OAAO,OAAO;oBAAE,MAAM;wBAAE,OAAO;oBAAM;gBAAG,SAChC;oBACJ,IAAI;wBACA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,WAAW,MAAM,GAAG,MAAM,QAAQ,GAAG,IAAI,CAAC;oBACtE,SACQ;wBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;oBAAE;gBACxC;YACJ;QACJ;IACJ;IACA,OAAO,SAAU,QAAQ;QACrB,OAAO,iBAAiB,IAAI,EAAE,WAAW;YACrC,IAAI,IAAI,KAAK,IAAI;YACjB,IAAI;gBACA,IAAK,IAAI,KAAK,MAAM,aAAa,cAAc,WAAW,cAAc,eAAe,MAAM,QAAQ,WAAW,IAAI,KAAK,KAAK,aAAa,IAAI,EAAE,CAAC,IAAI,KAAK,KAAM;oBAC7J,KAAK,aAAa,KAAK;oBACvB,KAAK;oBACL,MAAM,QAAQ;oBACd,IAAI;oBACJ,IAAI,QAAQ;oBACZ,IAAI,MAAM,GAAG,EAAE;wBACX,QAAQ,QAAQ;wBAChB,OAAO,iBAAiB,SAAS,CAAC,MAAM,KAAK;oBACjD,OACK;wBACD,OAAO,cAAc,SAAS,CAAC,MAAM,KAAK;oBAC9C;oBACA,MAAM,MAAM,QAAQ;wBAAE;wBAAO;oBAAK;gBACtC;YACJ,EACA,OAAO,OAAO;gBAAE,MAAM;oBAAE,OAAO;gBAAM;YAAG,SAChC;gBACJ,IAAI;oBACA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,WAAW,MAAM,GAAG,MAAM,QAAQ,GAAG,IAAI,CAAC;gBACtE,SACQ;oBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;gBAAE;YACxC;QACJ;IACJ;AACJ;AACO,SAAS,uBAAuB,aAAa,EAAE,aAAa,EAAE,gBAAgB;IACjF,uDAAuD;IACvD,IAAI,oBAAoB,kBAAkB,WAAW;QACjD,OAAO,SAAU,QAAQ;YACrB,OAAO,iBAAiB,IAAI,EAAE,WAAW;gBACrC,IAAI,IAAI,KAAK,IAAI;gBACjB,IAAI;oBACA,IAAK,IAAI,KAAK,MAAM,aAAa,cAAc,WAAW,cAAc,eAAe,MAAM,QAAQ,WAAW,IAAI,KAAK,KAAK,aAAa,IAAI,EAAE,CAAC,IAAI,KAAK,KAAM;wBAC7J,KAAK,aAAa,KAAK;wBACvB,KAAK;wBACL,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG;wBACxB,IAAI,CAAC,QAAQ,aAAa,MAAM,eAAe;4BAC3C,MAAM,MAAM,QAAQ;gCAAE,OAAO,iBAAiB,KAAK,CAAC;gCAAO,KAAK;4BAAK;wBACzE,OACK;4BACD,MAAM,MAAM,QAAQ;gCAAE,OAAO,cAAc,KAAK,CAAC;gCAAO,KAAK;4BAAM;wBACvE;oBACJ;gBACJ,EACA,OAAO,OAAO;oBAAE,MAAM;wBAAE,OAAO;oBAAM;gBAAG,SAChC;oBACJ,IAAI;wBACA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,WAAW,MAAM,GAAG,MAAM,QAAQ,GAAG,IAAI,CAAC;oBACtE,SACQ;wBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;oBAAE;gBACxC;YACJ;QACJ;IACJ;IACA,4BAA4B;IAC5B,OAAO,SAAU,QAAQ;QACrB,OAAO,iBAAiB,IAAI,EAAE,WAAW;YACrC,IAAI,IAAI,KAAK,IAAI;YACjB,IAAI;gBACA,IAAK,IAAI,KAAK,MAAM,aAAa,cAAc,WAAW,cAAc,eAAe,MAAM,QAAQ,WAAW,IAAI,KAAK,KAAK,aAAa,IAAI,EAAE,CAAC,IAAI,KAAK,KAAM;oBAC7J,KAAK,aAAa,KAAK;oBACvB,KAAK;oBACL,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG;oBACxB,IAAI,kBAAkB,aAClB,CAAC,QAAQ,aAAa,MAAM,eAAe;wBAC3C,IAAI,qBAAqB,MAAM;4BAC3B,MAAM,IAAI,+RAAA,CAAA,eAAY,CAAC,uBAAuB,mRAAA,CAAA,OAAI,CAAC,eAAe;wBACtE;wBAEA;oBACJ;oBACA,MAAM,MAAM,QAAQ,cAAc,KAAK,CAAC;gBAC5C;YACJ,EACA,OAAO,OAAO;gBAAE,MAAM;oBAAE,OAAO;gBAAM;YAAG,SAChC;gBACJ,IAAI;oBACA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,WAAW,MAAM,GAAG,MAAM,QAAQ,GAAG,IAAI,CAAC;gBACtE,SACQ;oBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;gBAAE;YACxC;QACJ;IACJ;AACJ;AAOO,SAAS,0BAA0B,WAAW,EAAE,gBAAgB;IACnE,OAAO,SAAU,QAAQ;QACrB,OAAO,iBAAiB,IAAI,EAAE,WAAW;YACrC,IAAI,IAAI,KAAK,IAAI;YACjB,IAAI;gBACA,IAAK,IAAI,KAAK,MAAM,aAAa,cAAc,WAAW,cAAc,eAAe,MAAM,QAAQ,WAAW,IAAI,KAAK,KAAK,aAAa,IAAI,EAAE,CAAC,IAAI,KAAK,KAAM;oBAC7J,KAAK,aAAa,KAAK;oBACvB,KAAK;oBACL,MAAM,MAAM;oBACZ,MAAM,MAAM,QAAQ,CAAA,MAAM,QAAQ,CAAA,GAAA,mSAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,aAAa,kBAAiB;gBAC1F;YACJ,EACA,OAAO,OAAO;gBAAE,MAAM;oBAAE,OAAO;gBAAM;YAAG,SAChC;gBACJ,IAAI;oBACA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,WAAW,MAAM,GAAG,MAAM,QAAQ,GAAG,IAAI,CAAC;gBACtE,SACQ;oBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;gBAAE;YACxC;QACJ;IACJ;AACJ;AAUO,SAAS,4BAA4B,WAAW,EAAE,YAAY;IACjE,OAAO,SAAU,QAAQ;QACrB,OAAO,iBAAiB,IAAI,EAAE,WAAW;YACrC,IAAI,IAAI,KAAK,IAAI;YACjB,IAAI;gBACA,IAAK,IAAI,KAAK,MAAM,aAAa,cAAc,WAAW,cAAc,eAAe,MAAM,QAAQ,WAAW,IAAI,KAAK,KAAK,aAAa,IAAI,EAAE,CAAC,IAAI,KAAK,KAAM;oBAC7J,KAAK,aAAa,KAAK;oBACvB,KAAK;oBACL,MAAM,MAAM;oBACZ,MAAM,MAAM,QAAQ,CAAA,MAAM,QAAQ,CAAA,GAAA,mSAAA,CAAA,qBAAkB,AAAD,EAAE,KAAK,aAAa,cAAa;gBACxF;YACJ,EACA,OAAO,OAAO;gBAAE,MAAM;oBAAE,OAAO;gBAAM;YAAG,SAChC;gBACJ,IAAI;oBACA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,WAAW,MAAM,GAAG,MAAM,QAAQ,GAAG,IAAI,CAAC;gBACtE,SACQ;oBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;gBAAE;YACxC;QACJ;IACJ;AACJ;AAOO,SAAS;IACZ,OAAO,SAAU,QAAQ;QACrB,OAAO,iBAAiB,IAAI,EAAE,WAAW;YACrC,IAAI,IAAI,MAAM,IAAI;YAClB,IAAI;gBACA,IAAK,IAAI,KAAK,MAAM,cAAc,cAAc,WAAW,eAAe,gBAAgB,MAAM,QAAQ,YAAY,IAAI,KAAK,KAAK,cAAc,IAAI,EAAE,CAAC,IAAI,KAAK,KAAM;oBAClK,KAAK,cAAc,KAAK;oBACxB,KAAK;oBACL,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG;oBACxB,MAAM,MAAM,QAAQ,CAAA,GAAA,mSAAA,CAAA,iBAAc,AAAD,EAAE,OAAO;gBAC9C;YACJ,EACA,OAAO,QAAQ;gBAAE,OAAO;oBAAE,OAAO;gBAAO;YAAG,SACnC;gBACJ,IAAI;oBACA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,YAAY,MAAM,GAAG,MAAM,QAAQ,GAAG,IAAI,CAAC;gBACvE,SACQ;oBAAE,IAAI,MAAM,MAAM,KAAK,KAAK;gBAAE;YAC1C;QACJ;IACJ;AACJ;AAYO,SAAS,uBAAuB,YAAY;IAC/C,mDAAmD;IACnD,SAAS,OAAO,MAAM,EAAE,KAAK;QACzB,MAAM,IAAI,IAAI,WAAW,OAAO,UAAU,GAAG,MAAM,UAAU;QAC7D,EAAE,GAAG,CAAC;QACN,EAAE,GAAG,CAAC,OAAO,OAAO,MAAM;QAC1B,OAAO;IACX;IACA,gDAAgD;IAChD,mCAAmC;IACnC,SAAS,cAAc,MAAM,EAAE,MAAM;QACjC,IAAI,OAAO,UAAU,GAAG,IAAI,OAAO,MAAM,EAAE;YACvC,OAAO;gBAAC;gBAAW;aAAO;QAC9B;QACA,OAAO;YACH;gBAAE,OAAO,OAAO,KAAK;gBAAE,MAAM,OAAO,QAAQ,CAAC,GAAG,IAAI,OAAO,MAAM;YAAE;YACnE,OAAO,QAAQ,CAAC,IAAI,OAAO,MAAM;SACpC;IACL;IACA,kCAAkC;IAClC,SAAS,WAAW,MAAM;QACtB,IAAI,OAAO,UAAU,GAAG,GAAG;YACvB,OAAO;QACX;QACA,MAAM,OAAO,IAAI,SAAS,OAAO,MAAM,EAAE,OAAO,UAAU,EAAE,OAAO,UAAU;QAC7E,MAAM,SAAS,KAAK,SAAS,CAAC,IAAI,yBAAyB;QAC3D,MAAM,QAAQ,KAAK,QAAQ,CAAC,IAAI,sBAAsB;QACtD,OAAO;YAAE;YAAQ;QAAM;IAC3B;IACA,OAAO,SAAU,QAAQ;QACrB,OAAO,iBAAiB,IAAI,EAAE,WAAW;YACrC,IAAI,IAAI,MAAM,IAAI;YAClB,IAAI,SAAS,IAAI,WAAW;YAC5B,IAAI;gBACA,IAAK,IAAI,KAAK,MAAM,cAAc,cAAc,WAAW,eAAe,gBAAgB,MAAM,QAAQ,YAAY,IAAI,KAAK,KAAK,cAAc,IAAI,EAAE,CAAC,IAAI,KAAK,KAAM;oBAClK,KAAK,cAAc,KAAK;oBACxB,KAAK;oBACL,MAAM,QAAQ;oBACd,SAAS,OAAO,QAAQ;oBACxB,OAAS;wBACL,MAAM,SAAS,WAAW;wBAC1B,IAAI,CAAC,QAAQ;4BACT;wBACJ;wBACA,CAAA,GAAA,sSAAA,CAAA,qBAAkB,AAAD,EAAE,cAAc,OAAO,MAAM,EAAE;wBAChD,IAAI;wBACJ,CAAC,KAAK,OAAO,GAAG,cAAc,QAAQ;wBACtC,IAAI,CAAC,KAAK;4BACN;wBACJ;wBACA,MAAM,MAAM,QAAQ;oBACxB;gBACJ;YACJ,EACA,OAAO,QAAQ;gBAAE,OAAO;oBAAE,OAAO;gBAAO;YAAG,SACnC;gBACJ,IAAI;oBACA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,YAAY,MAAM,GAAG,MAAM,QAAQ,GAAG,IAAI,CAAC;gBACvE,SACQ;oBAAE,IAAI,MAAM,MAAM,KAAK,KAAK;gBAAE;YAC1C;YACA,IAAI,OAAO,UAAU,GAAG,GAAG;gBACvB,MAAM,SAAS,WAAW;gBAC1B,IAAI,UAAU;gBACd,IAAI,QAAQ;oBACR,UAAU,CAAC,yBAAyB,EAAE,OAAO,MAAM,CAAC,iCAAiC,EAAE,OAAO,UAAU,GAAG,EAAE,MAAM,CAAC;gBACxH;gBACA,MAAM,IAAI,+RAAA,CAAA,eAAY,CAAC,SAAS,mRAAA,CAAA,OAAI,CAAC,eAAe;YACxD;QACJ;IACJ;AACJ;AAYO,eAAe,aAAa,QAAQ,EAAE,YAAY,EAAE,UAAU;IACjE,IAAI,IAAI,MAAM,IAAI,IAAI,IAAI,MAAM,IAAI;IACpC,MAAM,CAAC,IAAI,KAAK,GAAG,gBAAgB;IACnC,IAAI,IAAI;QACJ,IAAI,OAAO,cAAc;YACrB,CAAA,GAAA,sSAAA,CAAA,qBAAkB,AAAD,EAAE,cAAc,MAAM;QAC3C;QACA,MAAM,SAAS,IAAI,WAAW;QAC9B,IAAI,SAAS;QACb,IAAI;YACA,IAAK,IAAI,KAAK,MAAM,cAAc,cAAc,WAAW,eAAe,gBAAgB,MAAM,YAAY,IAAI,IAAI,KAAK,cAAc,IAAI,EAAE,CAAC,IAAI,KAAK,KAAM;gBACzJ,KAAK,cAAc,KAAK;gBACxB,KAAK;gBACL,MAAM,QAAQ;gBACd,IAAI,SAAS,MAAM,UAAU,GAAG,MAAM;oBAClC,MAAM,IAAI,+RAAA,CAAA,eAAY,CAAC,CAAC,yBAAyB,EAAE,KAAK,iBAAiB,EAAE,SAAS,MAAM,UAAU,EAAE,EAAE,mRAAA,CAAA,OAAI,CAAC,eAAe;gBAChI;gBACA,OAAO,GAAG,CAAC,OAAO;gBAClB,UAAU,MAAM,UAAU;YAC9B;QACJ,EACA,OAAO,QAAQ;YAAE,OAAO;gBAAE,OAAO;YAAO;QAAG,SACnC;YACJ,IAAI;gBACA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,YAAY,MAAM,GAAG,MAAM,GAAG,IAAI,CAAC;YAC/D,SACQ;gBAAE,IAAI,MAAM,MAAM,KAAK,KAAK;YAAE;QAC1C;QACA,IAAI,SAAS,MAAM;YACf,MAAM,IAAI,+RAAA,CAAA,eAAY,CAAC,CAAC,yBAAyB,EAAE,KAAK,iBAAiB,EAAE,QAAQ,EAAE,mRAAA,CAAA,OAAI,CAAC,eAAe;QAC7G;QACA,OAAO;IACX;IACA,MAAM,SAAS,EAAE;IACjB,IAAI,QAAQ;IACZ,IAAI;QACA,IAAK,IAAI,KAAK,MAAM,cAAc,cAAc,WAAW,eAAe,gBAAgB,MAAM,YAAY,IAAI,IAAI,KAAK,cAAc,IAAI,EAAE,CAAC,IAAI,KAAK,KAAM;YACzJ,KAAK,cAAc,KAAK;YACxB,KAAK;YACL,MAAM,QAAQ;YACd,SAAS,MAAM,UAAU;YACzB,CAAA,GAAA,sSAAA,CAAA,qBAAkB,AAAD,EAAE,cAAc;YACjC,OAAO,IAAI,CAAC;QAChB;IACJ,EACA,OAAO,QAAQ;QAAE,OAAO;YAAE,OAAO;QAAO;IAAG,SACnC;QACJ,IAAI;YACA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,YAAY,MAAM,GAAG,MAAM,GAAG,IAAI,CAAC;QAC/D,SACQ;YAAE,IAAI,MAAM,MAAM,KAAK,KAAK;QAAE;IAC1C;IACA,MAAM,MAAM,IAAI,WAAW;IAC3B,IAAI,SAAS;IACb,IAAK,IAAI,QAAQ,OAAO,KAAK,IAAI,OAAO,QAAQ,OAAO,KAAK,GAAI;QAC5D,IAAI,GAAG,CAAC,OAAO;QACf,UAAU,MAAM,UAAU;IAC9B;IACA,OAAO;AACX;AACA,kDAAkD;AAClD,SAAS,gBAAgB,UAAU;IAC/B,IAAI,eAAe,aAAa,eAAe,MAAM;QACjD,OAAO;YAAC;YAAO;SAAE;IACrB;IACA,MAAM,IAAI,OAAO,cAAc,WAAW,SAAS,YAAY,MAAM;IACrE,IAAI,CAAC,OAAO,aAAa,CAAC,MAAM,IAAI,GAAG;QACnC,OAAO;YAAC;YAAO;SAAE;IACrB;IACA,OAAO;QAAC;QAAM;KAAE;AACpB;AAOO,eAAe,WAAW,QAAQ;IACrC,MAAM,KAAK,QAAQ,CAAC,OAAO,aAAa,CAAC;IACzC,IAAI,QAAQ,MAAM,GAAG,IAAI;IACzB,OAAO;QACH,CAAC,OAAO,aAAa,CAAC;YAClB,MAAM,IAAI;gBACN,MAAM;oBACF,IAAI,UAAU,MAAM;wBAChB,MAAM,IAAI;wBACV,QAAQ;wBACR,OAAO;oBACX;oBACA,OAAO,MAAM,GAAG,IAAI;gBACxB;YACJ;YACA,IAAI,GAAG,KAAK,KAAK,WAAW;gBACxB,4GAA4G;gBAC5G,EAAE,KAAK,GAAG,CAAC,IAAM,GAAG,KAAK,CAAC;YAC9B;YACA,IAAI,GAAG,MAAM,KAAK,WAAW;gBACzB,+IAA+I;gBAC/I,EAAE,MAAM,GAAG,CAAC,QAAU,GAAG,MAAM,CAAC;YACpC;YACA,OAAO;QACX;IACJ;AACJ;AAgCO,SAAS,sBAAsB,QAAQ;IAC1C,MAAM,iBAAiB,QAAQ,CAAC,OAAO,aAAa,CAAC;IACrD,IAAI,eAAe,KAAK,KAAK,WAAW;QACpC,MAAM,IAAI,MAAM;IACpB;IACA,MAAM,QAAQ;IACd,IAAI;IACJ,IAAI;IACJ,IAAI,KAAK;QACL;YACI,gBAAgB,MAAM,IAAI,GAAG,OAAO,CAAC;gBACjC,gBAAgB;YACpB;YACA,OAAO;QACX;QACA,OAAM,CAAC;YACH,OAAO,MAAM,KAAK,CAAC;QACvB;IACJ;IACA,IAAI,eAAe,MAAM,KAAK,WAAW;QACrC,KAAK,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,KAAK;YAAE,QAAO,KAAK;gBAChD,OAAO,MAAM,MAAM,CAAC;YACxB;QAAE;IACV;IACA,IAAI,OAAO;IACX,OAAO;QACH,OAAM,MAAM;YACR,IAAI,SAAS;gBACT,OAAO,QAAQ,KAAK;YACxB;YACA,MAAM,IAAI;gBACN,OAAO,MAAM,KAAK,CAAC,QAAQ,IAAI,CAAC,CAAC,IAAO,EAAE,IAAI,KAAK,OAAO,cAAc,UAAW,IAAM;YAC7F;YACA,IAAI,eAAe;gBACf,UAAU;oBAAE;oBAAQ,OAAO,cAAc,IAAI,CAAC,GAAG;gBAAG;gBACpD,OAAO,QAAQ,KAAK;YACxB;YACA,UAAU;gBAAE;gBAAQ,OAAO;YAAI;YAC/B,OAAO,QAAQ,KAAK;QACxB;QACA,CAAC,OAAO,aAAa,CAAC;YAClB,IAAI,MAAM;gBACN,MAAM,IAAI,MAAM;YACpB;YACA,OAAO;YACP,OAAO;QACX;IACJ;AACJ;AAIO,SAAS;IACZ,mEAAmE;IACnE,EAAE;IACF,0DAA0D;IAC1D,qEAAqE;IACrE,MAAM,YAAY,EAAE;IACpB,MAAM,aAAa,EAAE;IACrB,IAAI,MAAM;IACV,IAAI;IACJ,IAAI;IACJ,IAAI,cAAc,IAAI,QAAQ,CAAC,SAAS;QACpC,cAAc;QACd,aAAa;IACjB;IACA,IAAI,SAAS;IACb,qEAAqE;IACrE,eAAe;IACf,SAAS;QACL,KAAK,MAAM,QAAQ,UAAU,MAAM,CAAC,GAAG,UAAU,MAAM,EAAG;YACtD,KAAK;gBAAE,MAAM;gBAAM,OAAO;YAAU;QACxC;IACJ;IACA,OAAO;QACH;YACI,SAAS;YACT;QACJ;QACA,MAAM,OAAM,OAAO;YACf,IAAI,QAAQ;gBACR,MAAM,QAAQ,QAAQ,QAAQ,KAAK,IAAI,MAAM,IAAI,MAAM;YAC3D;YACA,MAAM,OAAO,UAAU,KAAK;YAC5B,IAAI,SAAS,WAAW;gBACpB,0EAA0E;gBAC1E,WAAW,IAAI,CAAC;YACpB,OACK;gBACD,0DAA0D;gBAC1D,KAAK;oBAAE,MAAM;oBAAO,OAAO;gBAAQ;gBACnC,IAAI,UAAU,MAAM,GAAG,GAAG;oBACtB,yEAAyE;oBACzE,iFAAiF;oBACjF,iCAAiC;oBACjC;gBACJ;YACJ;YACA,+EAA+E;YAC/E,EAAE;YACF,sFAAsF;YACtF,6EAA6E;YAC7E,sDAAsD;YACtD,MAAM,QAAQ,WAAW,MAAM,GAAG;YAClC,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAK;gBAC5B,MAAM;YACV;QACJ;QACA,CAAC,OAAO,aAAa,CAAC;YAClB,OAAO;gBACH;oBACI,sCAAsC;oBACtC,mDAAmD;oBACnD,qBAAqB;oBACrB,EAAE;oBACF,uDAAuD;oBACvD;oBACA,cAAc,IAAI,QAAQ,CAAC,SAAS;wBAChC,cAAc;wBACd,aAAa;oBACjB;oBACA,MAAM,QAAQ,WAAW,KAAK;oBAC9B,IAAI,UAAU,WAAW;wBACrB,yDAAyD;wBACzD,OAAO,QAAQ,OAAO,CAAC;4BAAE,MAAM;4BAAO,OAAO;wBAAM;oBACvD;oBACA,IAAI,QAAQ;wBACR,OAAO,QAAQ,OAAO,CAAC;4BAAE,MAAM;4BAAM,OAAO;wBAAU;oBAC1D;oBACA,mEAAmE;oBACnE,oBAAoB;oBACpB,IAAI;oBACJ,MAAM,cAAc,IAAI,QAAQ,CAAC,UAAa,cAAc;oBAC5D,UAAU,IAAI,CAAC,cAAc,+DAA+D;oBAC5F,OAAO;gBACX;gBACA,OAAM,QAAQ;oBACV,MAAM;oBACN,SAAS;oBACT,WAAW,MAAM,CAAC,GAAG,WAAW,MAAM;oBACtC,YAAY,KAAK,CAAC;oBACd,wDAAwD;oBAC5D;oBACA,uCAAuC;oBACvC,WAAW;oBACX;oBACA,OAAO,QAAQ,OAAO,CAAC;wBAAE,MAAM;wBAAM,OAAO;oBAAU;gBAC1D;gBACA;oBACI,SAAS;oBACT,WAAW,MAAM,CAAC,GAAG,WAAW,MAAM;oBACtC,oDAAoD;oBACpD;oBACA,4BAA4B;oBAC5B,cAAc,QAAQ,MAAM,CAAC,IAAI,MAAM;oBACvC,YAAY,KAAK,CAAC;oBACd,wDAAwD;oBAC5D;oBACA;oBACA,OAAO,QAAQ,OAAO,CAAC;wBAAE,MAAM;wBAAM,OAAO;oBAAU;gBAC1D;YACJ;QACJ;IACJ;AACJ;AAOO,SAAS,oBAAoB,KAAK;IACrC,OAAO,iBAAiB,IAAI,EAAE,WAAW,UAAU;QAC/C,MAAM,QAAQ,CAAA,OAAO,iBAAiB,cAAc,OAAM;IAC9D;AACJ", "ignoreList": [0]}}, {"offset": {"line": 1531, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@connectrpc+connect@1.6.1_@bufbuild+protobuf@1.10.1/node_modules/@connectrpc/connect/dist/esm/promise-client.js"], "sourcesContent": ["// Copyright 2021-2024 The Connect Authors\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nvar __asyncValues = (this && this.__asyncValues) || function (o) {\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n    var m = o[Symbol.asyncIterator], i;\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\n};\nvar __await = (this && this.__await) || function (v) { return this instanceof __await ? (this.v = v, this) : new __await(v); }\nvar __asyncDelegator = (this && this.__asyncDelegator) || function (o) {\n    var i, p;\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\n};\nvar __asyncGenerator = (this && this.__asyncGenerator) || function (thisArg, _arguments, generator) {\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\", awaitReturn), i[Symbol.asyncIterator] = function () { return this; }, i;\n    function awaitReturn(f) { return function (v) { return Promise.resolve(v).then(f, reject); }; }\n    function verb(n, f) { if (g[n]) { i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; if (f) i[n] = f(i[n]); } }\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\n    function fulfill(value) { resume(\"next\", value); }\n    function reject(value) { resume(\"throw\", value); }\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\n};\nimport { Message, MethodKind } from \"@bufbuild/protobuf\";\nimport { makeAnyClient } from \"./any-client.js\";\nimport { ConnectError } from \"./connect-error.js\";\nimport { Code } from \"./code.js\";\nimport { createAsyncIterable } from \"./protocol/async-iterable.js\";\n/**\n * Create a Client for the given service, invoking RPCs through the\n * given transport.\n */\nexport function createClient(service, transport) {\n    return makeAnyClient(service, (method) => {\n        switch (method.kind) {\n            case MethodKind.Unary:\n                return createUnaryFn(transport, service, method);\n            case MethodKind.ServerStreaming:\n                return createServerStreamingFn(transport, service, method);\n            case MethodKind.ClientStreaming:\n                return createClientStreamingFn(transport, service, method);\n            case MethodKind.BiDiStreaming:\n                return createBiDiStreamingFn(transport, service, method);\n            default:\n                return null;\n        }\n    });\n}\n/**\n * @deprecated use createClient.\n */\nexport function createPromiseClient(service, transport) {\n    return createClient(service, transport);\n}\nexport function createUnaryFn(transport, service, method) {\n    return async function (input, options) {\n        var _a, _b;\n        const response = await transport.unary(service, method, options === null || options === void 0 ? void 0 : options.signal, options === null || options === void 0 ? void 0 : options.timeoutMs, options === null || options === void 0 ? void 0 : options.headers, input, options === null || options === void 0 ? void 0 : options.contextValues);\n        (_a = options === null || options === void 0 ? void 0 : options.onHeader) === null || _a === void 0 ? void 0 : _a.call(options, response.header);\n        (_b = options === null || options === void 0 ? void 0 : options.onTrailer) === null || _b === void 0 ? void 0 : _b.call(options, response.trailer);\n        return response.message;\n    };\n}\nexport function createServerStreamingFn(transport, service, method) {\n    return function (input, options) {\n        return handleStreamResponse(transport.stream(service, method, options === null || options === void 0 ? void 0 : options.signal, options === null || options === void 0 ? void 0 : options.timeoutMs, options === null || options === void 0 ? void 0 : options.headers, createAsyncIterable([input]), options === null || options === void 0 ? void 0 : options.contextValues), options);\n    };\n}\nexport function createClientStreamingFn(transport, service, method) {\n    return async function (request, options) {\n        var _a, e_1, _b, _c;\n        var _d, _e;\n        const response = await transport.stream(service, method, options === null || options === void 0 ? void 0 : options.signal, options === null || options === void 0 ? void 0 : options.timeoutMs, options === null || options === void 0 ? void 0 : options.headers, request, options === null || options === void 0 ? void 0 : options.contextValues);\n        (_d = options === null || options === void 0 ? void 0 : options.onHeader) === null || _d === void 0 ? void 0 : _d.call(options, response.header);\n        let singleMessage;\n        let count = 0;\n        try {\n            for (var _f = true, _g = __asyncValues(response.message), _h; _h = await _g.next(), _a = _h.done, !_a; _f = true) {\n                _c = _h.value;\n                _f = false;\n                const message = _c;\n                singleMessage = message;\n                count++;\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (!_f && !_a && (_b = _g.return)) await _b.call(_g);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        if (!singleMessage) {\n            throw new ConnectError(\"protocol error: missing response message\", Code.Unimplemented);\n        }\n        if (count > 1) {\n            throw new ConnectError(\"protocol error: received extra messages for client streaming method\", Code.Unimplemented);\n        }\n        (_e = options === null || options === void 0 ? void 0 : options.onTrailer) === null || _e === void 0 ? void 0 : _e.call(options, response.trailer);\n        return singleMessage;\n    };\n}\nexport function createBiDiStreamingFn(transport, service, method) {\n    return function (request, options) {\n        return handleStreamResponse(transport.stream(service, method, options === null || options === void 0 ? void 0 : options.signal, options === null || options === void 0 ? void 0 : options.timeoutMs, options === null || options === void 0 ? void 0 : options.headers, request, options === null || options === void 0 ? void 0 : options.contextValues), options);\n    };\n}\nfunction handleStreamResponse(stream, options) {\n    const it = (function () {\n        return __asyncGenerator(this, arguments, function* () {\n            var _a, _b;\n            const response = yield __await(stream);\n            (_a = options === null || options === void 0 ? void 0 : options.onHeader) === null || _a === void 0 ? void 0 : _a.call(options, response.header);\n            yield __await(yield* __asyncDelegator(__asyncValues(response.message)));\n            (_b = options === null || options === void 0 ? void 0 : options.onTrailer) === null || _b === void 0 ? void 0 : _b.call(options, response.trailer);\n        });\n    })()[Symbol.asyncIterator]();\n    // Create a new iterable to omit throw/return.\n    return {\n        [Symbol.asyncIterator]: () => ({\n            next: () => it.next(),\n        }),\n    };\n}\n"], "names": [], "mappings": "AAAA,0CAA0C;AAC1C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;;;;;;;;AA0BjC;AACA;AACA;AACA;AACA;AA7BA,IAAI,gBAAgB,AAAC,IAAI,IAAI,IAAI,CAAC,aAAa,IAAK,SAAU,CAAC;IAC3D,IAAI,CAAC,OAAO,aAAa,EAAE,MAAM,IAAI,UAAU;IAC/C,IAAI,IAAI,CAAC,CAAC,OAAO,aAAa,CAAC,EAAE;IACjC,OAAO,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,OAAO,aAAa,aAAa,SAAS,KAAK,CAAC,CAAC,OAAO,QAAQ,CAAC,IAAI,IAAI,CAAC,GAAG,KAAK,SAAS,KAAK,UAAU,KAAK,WAAW,CAAC,CAAC,OAAO,aAAa,CAAC,GAAG;QAAc,OAAO,IAAI;IAAE,GAAG,CAAC;;IAC/M,SAAS,KAAK,CAAC;QAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,SAAU,CAAC;YAAI,OAAO,IAAI,QAAQ,SAAU,OAAO,EAAE,MAAM;gBAAI,IAAI,CAAC,CAAC,EAAE,CAAC,IAAI,OAAO,SAAS,QAAQ,EAAE,IAAI,EAAE,EAAE,KAAK;YAAG;QAAI;IAAG;IAC/J,SAAS,OAAO,OAAO,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;QAAI,QAAQ,OAAO,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;YAAI,QAAQ;gBAAE,OAAO;gBAAG,MAAM;YAAE;QAAI,GAAG;IAAS;AAC/H;AACA,IAAI,UAAU,AAAC,IAAI,IAAI,IAAI,CAAC,OAAO,IAAK,SAAU,CAAC;IAAI,OAAO,IAAI,YAAY,UAAU,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,IAAI,IAAI,IAAI,QAAQ;AAAI;AAC7H,IAAI,mBAAmB,AAAC,IAAI,IAAI,IAAI,CAAC,gBAAgB,IAAK,SAAU,CAAC;IACjE,IAAI,GAAG;IACP,OAAO,IAAI,CAAC,GAAG,KAAK,SAAS,KAAK,SAAS,SAAU,CAAC;QAAI,MAAM;IAAG,IAAI,KAAK,WAAW,CAAC,CAAC,OAAO,QAAQ,CAAC,GAAG;QAAc,OAAO,IAAI;IAAE,GAAG;;IAC1I,SAAS,KAAK,CAAC,EAAE,CAAC;QAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,SAAU,CAAC;YAAI,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI;gBAAE,OAAO,QAAQ,CAAC,CAAC,EAAE,CAAC;gBAAK,MAAM;YAAM,IAAI,IAAI,EAAE,KAAK;QAAG,IAAI;IAAG;AACzI;AACA,IAAI,mBAAmB,AAAC,IAAI,IAAI,IAAI,CAAC,gBAAgB,IAAK,SAAU,OAAO,EAAE,UAAU,EAAE,SAAS;IAC9F,IAAI,CAAC,OAAO,aAAa,EAAE,MAAM,IAAI,UAAU;IAC/C,IAAI,IAAI,UAAU,KAAK,CAAC,SAAS,cAAc,EAAE,GAAG,GAAG,IAAI,EAAE;IAC7D,OAAO,IAAI,CAAC,GAAG,KAAK,SAAS,KAAK,UAAU,KAAK,UAAU,cAAc,CAAC,CAAC,OAAO,aAAa,CAAC,GAAG;QAAc,OAAO,IAAI;IAAE,GAAG;;IACjI,SAAS,YAAY,CAAC;QAAI,OAAO,SAAU,CAAC;YAAI,OAAO,QAAQ,OAAO,CAAC,GAAG,IAAI,CAAC,GAAG;QAAS;IAAG;IAC9F,SAAS,KAAK,CAAC,EAAE,CAAC;QAAI,IAAI,CAAC,CAAC,EAAE,EAAE;YAAE,CAAC,CAAC,EAAE,GAAG,SAAU,CAAC;gBAAI,OAAO,IAAI,QAAQ,SAAU,CAAC,EAAE,CAAC;oBAAI,EAAE,IAAI,CAAC;wBAAC;wBAAG;wBAAG;wBAAG;qBAAE,IAAI,KAAK,OAAO,GAAG;gBAAI;YAAI;YAAG,IAAI,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE;QAAG;IAAE;IACvK,SAAS,OAAO,CAAC,EAAE,CAAC;QAAI,IAAI;YAAE,KAAK,CAAC,CAAC,EAAE,CAAC;QAAK,EAAE,OAAO,GAAG;YAAE,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;QAAI;IAAE;IACjF,SAAS,KAAK,CAAC;QAAI,EAAE,KAAK,YAAY,UAAU,QAAQ,OAAO,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,UAAU,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;IAAI;IACvH,SAAS,QAAQ,KAAK;QAAI,OAAO,QAAQ;IAAQ;IACjD,SAAS,OAAO,KAAK;QAAI,OAAO,SAAS;IAAQ;IACjD,SAAS,OAAO,CAAC,EAAE,CAAC;QAAI,IAAI,EAAE,IAAI,EAAE,KAAK,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;IAAG;AACrF;;;;;;AAUO,SAAS,aAAa,OAAO,EAAE,SAAS;IAC3C,OAAO,CAAA,GAAA,4RAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,CAAC;QAC3B,OAAQ,OAAO,IAAI;YACf,KAAK,oPAAA,CAAA,aAAU,CAAC,KAAK;gBACjB,OAAO,cAAc,WAAW,SAAS;YAC7C,KAAK,oPAAA,CAAA,aAAU,CAAC,eAAe;gBAC3B,OAAO,wBAAwB,WAAW,SAAS;YACvD,KAAK,oPAAA,CAAA,aAAU,CAAC,eAAe;gBAC3B,OAAO,wBAAwB,WAAW,SAAS;YACvD,KAAK,oPAAA,CAAA,aAAU,CAAC,aAAa;gBACzB,OAAO,sBAAsB,WAAW,SAAS;YACrD;gBACI,OAAO;QACf;IACJ;AACJ;AAIO,SAAS,oBAAoB,OAAO,EAAE,SAAS;IAClD,OAAO,aAAa,SAAS;AACjC;AACO,SAAS,cAAc,SAAS,EAAE,OAAO,EAAE,MAAM;IACpD,OAAO,eAAgB,KAAK,EAAE,OAAO;QACjC,IAAI,IAAI;QACR,MAAM,WAAW,MAAM,UAAU,KAAK,CAAC,SAAS,QAAQ,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,MAAM,EAAE,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,SAAS,EAAE,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,OAAO,EAAE,OAAO,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,aAAa;QAChV,CAAC,KAAK,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,QAAQ,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC,SAAS,SAAS,MAAM;QAC/I,CAAC,KAAK,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,SAAS,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC,SAAS,SAAS,OAAO;QACjJ,OAAO,SAAS,OAAO;IAC3B;AACJ;AACO,SAAS,wBAAwB,SAAS,EAAE,OAAO,EAAE,MAAM;IAC9D,OAAO,SAAU,KAAK,EAAE,OAAO;QAC3B,OAAO,qBAAqB,UAAU,MAAM,CAAC,SAAS,QAAQ,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,MAAM,EAAE,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,SAAS,EAAE,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,OAAO,EAAE,CAAA,GAAA,4SAAA,CAAA,sBAAmB,AAAD,EAAE;YAAC;SAAM,GAAG,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,aAAa,GAAG;IACpX;AACJ;AACO,SAAS,wBAAwB,SAAS,EAAE,OAAO,EAAE,MAAM;IAC9D,OAAO,eAAgB,OAAO,EAAE,OAAO;QACnC,IAAI,IAAI,KAAK,IAAI;QACjB,IAAI,IAAI;QACR,MAAM,WAAW,MAAM,UAAU,MAAM,CAAC,SAAS,QAAQ,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,MAAM,EAAE,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,SAAS,EAAE,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,OAAO,EAAE,SAAS,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,aAAa;QACnV,CAAC,KAAK,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,QAAQ,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC,SAAS,SAAS,MAAM;QAC/I,IAAI;QACJ,IAAI,QAAQ;QACZ,IAAI;YACA,IAAK,IAAI,KAAK,MAAM,KAAK,cAAc,SAAS,OAAO,GAAG,IAAI,KAAK,MAAM,GAAG,IAAI,IAAI,KAAK,GAAG,IAAI,EAAE,CAAC,IAAI,KAAK,KAAM;gBAC9G,KAAK,GAAG,KAAK;gBACb,KAAK;gBACL,MAAM,UAAU;gBAChB,gBAAgB;gBAChB;YACJ;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,GAAG,MAAM,GAAG,MAAM,GAAG,IAAI,CAAC;YACtD,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;QACA,IAAI,CAAC,eAAe;YAChB,MAAM,IAAI,+RAAA,CAAA,eAAY,CAAC,4CAA4C,mRAAA,CAAA,OAAI,CAAC,aAAa;QACzF;QACA,IAAI,QAAQ,GAAG;YACX,MAAM,IAAI,+RAAA,CAAA,eAAY,CAAC,uEAAuE,mRAAA,CAAA,OAAI,CAAC,aAAa;QACpH;QACA,CAAC,KAAK,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,SAAS,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC,SAAS,SAAS,OAAO;QACjJ,OAAO;IACX;AACJ;AACO,SAAS,sBAAsB,SAAS,EAAE,OAAO,EAAE,MAAM;IAC5D,OAAO,SAAU,OAAO,EAAE,OAAO;QAC7B,OAAO,qBAAqB,UAAU,MAAM,CAAC,SAAS,QAAQ,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,MAAM,EAAE,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,SAAS,EAAE,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,OAAO,EAAE,SAAS,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,aAAa,GAAG;IAC/V;AACJ;AACA,SAAS,qBAAqB,MAAM,EAAE,OAAO;IACzC,MAAM,KAAK,CAAC;QACR,OAAO,iBAAiB,IAAI,EAAE,WAAW;YACrC,IAAI,IAAI;YACR,MAAM,WAAW,MAAM,QAAQ;YAC/B,CAAC,KAAK,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,QAAQ,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC,SAAS,SAAS,MAAM;YAC/I,MAAM,QAAQ,CAAA,OAAO,iBAAiB,cAAc,SAAS,OAAO,EAAC;YACrE,CAAC,KAAK,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,SAAS,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC,SAAS,SAAS,OAAO;QACrJ;IACJ,CAAC,GAAG,CAAC,OAAO,aAAa,CAAC;IAC1B,8CAA8C;IAC9C,OAAO;QACH,CAAC,OAAO,aAAa,CAAC,EAAE,IAAM,CAAC;gBAC3B,MAAM,IAAM,GAAG,IAAI;YACvB,CAAC;IACL;AACJ", "ignoreList": [0]}}, {"offset": {"line": 1752, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@connectrpc+connect@1.6.1_@bufbuild+protobuf@1.10.1/node_modules/@connectrpc/connect/dist/esm/http-headers.js"], "sourcesContent": ["// Copyright 2021-2024 The Connect Authors\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nimport { protoBase64 } from \"@bufbuild/protobuf\";\nimport { ConnectError } from \"./connect-error.js\";\nimport { Code } from \"./code.js\";\n/**\n * Encode a single binary header value according to the Connect\n * and gRPC specifications.\n *\n * This function accepts raw binary data from a buffer, a string\n * with UTF-8 text, or a protobuf message. It encodes the input\n * with unpadded base64 and returns a string that can be used for\n * a header whose name ends with `-bin`.\n */\nexport function encodeBinaryHeader(value) {\n    let bytes;\n    if (typeof value == \"object\" && \"getType\" in value) {\n        bytes = value.toBinary();\n    }\n    else if (typeof value == \"string\") {\n        bytes = new TextEncoder().encode(value);\n    }\n    else {\n        bytes = value instanceof Uint8Array ? value : new Uint8Array(value);\n    }\n    return protoBase64.enc(bytes).replace(/=+$/, \"\");\n}\nexport function decodeBinaryHeader(value, type, options) {\n    try {\n        const bytes = protoBase64.dec(value);\n        if (type) {\n            return type.fromBinary(bytes, options);\n        }\n        return bytes;\n    }\n    catch (e) {\n        throw ConnectError.from(e, Code.DataLoss);\n    }\n}\n/**\n * Merge two or more Headers objects by appending all fields from\n * all inputs to a new Headers object.\n */\nexport function appendHeaders(...headers) {\n    const h = new Headers();\n    for (const e of headers) {\n        e.forEach((value, key) => {\n            h.append(key, value);\n        });\n    }\n    return h;\n}\n"], "names": [], "mappings": "AAAA,0CAA0C;AAC1C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;;;;;AACjC;AACA;AACA;;;;AAUO,SAAS,mBAAmB,KAAK;IACpC,IAAI;IACJ,IAAI,OAAO,SAAS,YAAY,aAAa,OAAO;QAChD,QAAQ,MAAM,QAAQ;IAC1B,OACK,IAAI,OAAO,SAAS,UAAU;QAC/B,QAAQ,IAAI,cAAc,MAAM,CAAC;IACrC,OACK;QACD,QAAQ,iBAAiB,aAAa,QAAQ,IAAI,WAAW;IACjE;IACA,OAAO,oPAAA,CAAA,cAAW,CAAC,GAAG,CAAC,OAAO,OAAO,CAAC,OAAO;AACjD;AACO,SAAS,mBAAmB,KAAK,EAAE,IAAI,EAAE,OAAO;IACnD,IAAI;QACA,MAAM,QAAQ,oPAAA,CAAA,cAAW,CAAC,GAAG,CAAC;QAC9B,IAAI,MAAM;YACN,OAAO,KAAK,UAAU,CAAC,OAAO;QAClC;QACA,OAAO;IACX,EACA,OAAO,GAAG;QACN,MAAM,+RAAA,CAAA,eAAY,CAAC,IAAI,CAAC,GAAG,mRAAA,CAAA,OAAI,CAAC,QAAQ;IAC5C;AACJ;AAKO,SAAS,cAAc,GAAG,OAAO;IACpC,MAAM,IAAI,IAAI;IACd,KAAK,MAAM,KAAK,QAAS;QACrB,EAAE,OAAO,CAAC,CAAC,OAAO;YACd,EAAE,MAAM,CAAC,KAAK;QAClB;IACJ;IACA,OAAO;AACX", "ignoreList": [0]}}, {"offset": {"line": 1813, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@connectrpc+connect@1.6.1_@bufbuild+protobuf@1.10.1/node_modules/@connectrpc/connect/dist/esm/context-values.js"], "sourcesContent": ["// Copyright 2021-2024 The Connect Authors\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n/**\n * createContextValues creates a new ContextValues.\n */\nexport function createContextValues() {\n    return {\n        get(key) {\n            return key.id in this ? this[key.id] : key.defaultValue;\n        },\n        set(key, value) {\n            this[key.id] = value;\n            return this;\n        },\n        delete(key) {\n            delete this[key.id];\n            return this;\n        },\n    };\n}\n/**\n * createContextKey creates a new ContextKey.\n */\nexport function createContextKey(defaultValue, options) {\n    return { id: Symbol(options === null || options === void 0 ? void 0 : options.description), defaultValue };\n}\n"], "names": [], "mappings": "AAAA,0CAA0C;AAC1C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC;;CAEC;;;;AACM,SAAS;IACZ,OAAO;QACH,KAAI,GAAG;YACH,OAAO,IAAI,EAAE,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,GAAG,IAAI,YAAY;QAC3D;QACA,KAAI,GAAG,EAAE,KAAK;YACV,IAAI,CAAC,IAAI,EAAE,CAAC,GAAG;YACf,OAAO,IAAI;QACf;QACA,QAAO,GAAG;YACN,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC;YACnB,OAAO,IAAI;QACf;IACJ;AACJ;AAIO,SAAS,iBAAiB,YAAY,EAAE,OAAO;IAClD,OAAO;QAAE,IAAI,OAAO,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,WAAW;QAAG;IAAa;AAC7G", "ignoreList": [0]}}, {"offset": {"line": 1859, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@connectrpc+connect@1.6.1_@bufbuild+protobuf@1.10.1/node_modules/@connectrpc/connect/dist/esm/protocol/serialization.js"], "sourcesContent": ["// Copyright 2021-2024 The Connect Authors\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nimport { ConnectError } from \"../connect-error.js\";\nimport { Code } from \"../code.js\";\nimport { assertReadMaxBytes, assertWriteMaxBytes } from \"./limit-io.js\";\n/**\n * Sets default JSON serialization options for connect-es.\n *\n * With standard protobuf JSON serialization, unknown JSON fields are\n * rejected by default. In connect-es, unknown JSON fields are ignored\n * by default.\n */\nexport function getJsonOptions(options) {\n    var _a;\n    const o = Object.assign({}, options);\n    (_a = o.ignoreUnknownFields) !== null && _a !== void 0 ? _a : (o.ignoreUnknownFields = true);\n    return o;\n}\n/**\n * Create an object that provides convenient access to request and response\n * message serialization for a given method.\n *\n * @private Internal code, does not follow semantic versioning.\n */\nexport function createMethodSerializationLookup(method, binaryOptions, jsonOptions, limitOptions) {\n    const inputBinary = limitSerialization(createBinarySerialization(method.I, binaryOptions), limitOptions);\n    const inputJson = limitSerialization(createJsonSerialization(method.I, jsonOptions), limitOptions);\n    const outputBinary = limitSerialization(createBinarySerialization(method.O, binaryOptions), limitOptions);\n    const outputJson = limitSerialization(createJsonSerialization(method.O, jsonOptions), limitOptions);\n    return {\n        getI(useBinaryFormat) {\n            return useBinaryFormat ? inputBinary : inputJson;\n        },\n        getO(useBinaryFormat) {\n            return useBinaryFormat ? outputBinary : outputJson;\n        },\n    };\n}\n/**\n * Returns functions to normalize and serialize the input message\n * of an RPC, and to parse the output message of an RPC.\n *\n * @private Internal code, does not follow semantic versioning.\n */\nexport function createClientMethodSerializers(method, useBinaryFormat, jsonOptions, binaryOptions) {\n    const input = useBinaryFormat\n        ? createBinarySerialization(method.I, binaryOptions)\n        : createJsonSerialization(method.I, jsonOptions);\n    const output = useBinaryFormat\n        ? createBinarySerialization(method.O, binaryOptions)\n        : createJsonSerialization(method.O, jsonOptions);\n    return { parse: output.parse, serialize: input.serialize };\n}\n/**\n * Apply I/O limits to a Serialization object, returning a new object.\n *\n * @private Internal code, does not follow semantic versioning.\n */\nexport function limitSerialization(serialization, limitOptions) {\n    return {\n        serialize(data) {\n            const bytes = serialization.serialize(data);\n            assertWriteMaxBytes(limitOptions.writeMaxBytes, bytes.byteLength);\n            return bytes;\n        },\n        parse(data) {\n            assertReadMaxBytes(limitOptions.readMaxBytes, data.byteLength, true);\n            return serialization.parse(data);\n        },\n    };\n}\n/**\n * Creates a Serialization object for serializing the given protobuf message\n * with the protobuf binary format.\n */\nexport function createBinarySerialization(messageType, options) {\n    return {\n        parse(data) {\n            try {\n                return messageType.fromBinary(data, options);\n            }\n            catch (e) {\n                const m = e instanceof Error ? e.message : String(e);\n                throw new ConnectError(`parse binary: ${m}`, Code.Internal);\n            }\n        },\n        serialize(data) {\n            try {\n                return data.toBinary(options);\n            }\n            catch (e) {\n                const m = e instanceof Error ? e.message : String(e);\n                throw new ConnectError(`serialize binary: ${m}`, Code.Internal);\n            }\n        },\n    };\n}\n/**\n * Creates a Serialization object for serializing the given protobuf message\n * with the protobuf canonical JSON encoding.\n *\n * By default, unknown fields are ignored.\n */\nexport function createJsonSerialization(messageType, options) {\n    var _a, _b;\n    const textEncoder = (_a = options === null || options === void 0 ? void 0 : options.textEncoder) !== null && _a !== void 0 ? _a : new TextEncoder();\n    const textDecoder = (_b = options === null || options === void 0 ? void 0 : options.textDecoder) !== null && _b !== void 0 ? _b : new TextDecoder();\n    const o = getJsonOptions(options);\n    return {\n        parse(data) {\n            try {\n                const json = textDecoder.decode(data);\n                return messageType.fromJsonString(json, o);\n            }\n            catch (e) {\n                throw ConnectError.from(e, Code.InvalidArgument);\n            }\n        },\n        serialize(data) {\n            try {\n                const json = data.toJsonString(o);\n                return textEncoder.encode(json);\n            }\n            catch (e) {\n                throw ConnectError.from(e, Code.Internal);\n            }\n        },\n    };\n}\n"], "names": [], "mappings": "AAAA,0CAA0C;AAC1C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;;;;;;;;AACjC;AACA;AACA;;;;AAQO,SAAS,eAAe,OAAO;IAClC,IAAI;IACJ,MAAM,IAAI,OAAO,MAAM,CAAC,CAAC,GAAG;IAC5B,CAAC,KAAK,EAAE,mBAAmB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAM,EAAE,mBAAmB,GAAG;IACvF,OAAO;AACX;AAOO,SAAS,gCAAgC,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,YAAY;IAC5F,MAAM,cAAc,mBAAmB,0BAA0B,OAAO,CAAC,EAAE,gBAAgB;IAC3F,MAAM,YAAY,mBAAmB,wBAAwB,OAAO,CAAC,EAAE,cAAc;IACrF,MAAM,eAAe,mBAAmB,0BAA0B,OAAO,CAAC,EAAE,gBAAgB;IAC5F,MAAM,aAAa,mBAAmB,wBAAwB,OAAO,CAAC,EAAE,cAAc;IACtF,OAAO;QACH,MAAK,eAAe;YAChB,OAAO,kBAAkB,cAAc;QAC3C;QACA,MAAK,eAAe;YAChB,OAAO,kBAAkB,eAAe;QAC5C;IACJ;AACJ;AAOO,SAAS,8BAA8B,MAAM,EAAE,eAAe,EAAE,WAAW,EAAE,aAAa;IAC7F,MAAM,QAAQ,kBACR,0BAA0B,OAAO,CAAC,EAAE,iBACpC,wBAAwB,OAAO,CAAC,EAAE;IACxC,MAAM,SAAS,kBACT,0BAA0B,OAAO,CAAC,EAAE,iBACpC,wBAAwB,OAAO,CAAC,EAAE;IACxC,OAAO;QAAE,OAAO,OAAO,KAAK;QAAE,WAAW,MAAM,SAAS;IAAC;AAC7D;AAMO,SAAS,mBAAmB,aAAa,EAAE,YAAY;IAC1D,OAAO;QACH,WAAU,IAAI;YACV,MAAM,QAAQ,cAAc,SAAS,CAAC;YACtC,CAAA,GAAA,sSAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa,aAAa,EAAE,MAAM,UAAU;YAChE,OAAO;QACX;QACA,OAAM,IAAI;YACN,CAAA,GAAA,sSAAA,CAAA,qBAAkB,AAAD,EAAE,aAAa,YAAY,EAAE,KAAK,UAAU,EAAE;YAC/D,OAAO,cAAc,KAAK,CAAC;QAC/B;IACJ;AACJ;AAKO,SAAS,0BAA0B,WAAW,EAAE,OAAO;IAC1D,OAAO;QACH,OAAM,IAAI;YACN,IAAI;gBACA,OAAO,YAAY,UAAU,CAAC,MAAM;YACxC,EACA,OAAO,GAAG;gBACN,MAAM,IAAI,aAAa,QAAQ,EAAE,OAAO,GAAG,OAAO;gBAClD,MAAM,IAAI,+RAAA,CAAA,eAAY,CAAC,CAAC,cAAc,EAAE,GAAG,EAAE,mRAAA,CAAA,OAAI,CAAC,QAAQ;YAC9D;QACJ;QACA,WAAU,IAAI;YACV,IAAI;gBACA,OAAO,KAAK,QAAQ,CAAC;YACzB,EACA,OAAO,GAAG;gBACN,MAAM,IAAI,aAAa,QAAQ,EAAE,OAAO,GAAG,OAAO;gBAClD,MAAM,IAAI,+RAAA,CAAA,eAAY,CAAC,CAAC,kBAAkB,EAAE,GAAG,EAAE,mRAAA,CAAA,OAAI,CAAC,QAAQ;YAClE;QACJ;IACJ;AACJ;AAOO,SAAS,wBAAwB,WAAW,EAAE,OAAO;IACxD,IAAI,IAAI;IACR,MAAM,cAAc,CAAC,KAAK,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,WAAW,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI;IACtI,MAAM,cAAc,CAAC,KAAK,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,WAAW,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI;IACtI,MAAM,IAAI,eAAe;IACzB,OAAO;QACH,OAAM,IAAI;YACN,IAAI;gBACA,MAAM,OAAO,YAAY,MAAM,CAAC;gBAChC,OAAO,YAAY,cAAc,CAAC,MAAM;YAC5C,EACA,OAAO,GAAG;gBACN,MAAM,+RAAA,CAAA,eAAY,CAAC,IAAI,CAAC,GAAG,mRAAA,CAAA,OAAI,CAAC,eAAe;YACnD;QACJ;QACA,WAAU,IAAI;YACV,IAAI;gBACA,MAAM,OAAO,KAAK,YAAY,CAAC;gBAC/B,OAAO,YAAY,MAAM,CAAC;YAC9B,EACA,OAAO,GAAG;gBACN,MAAM,+RAAA,CAAA,eAAY,CAAC,IAAI,CAAC,GAAG,mRAAA,CAAA,OAAI,CAAC,QAAQ;YAC5C;QACJ;IACJ;AACJ", "ignoreList": [0]}}, {"offset": {"line": 1977, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@connectrpc+connect@1.6.1_@bufbuild+protobuf@1.10.1/node_modules/@connectrpc/connect/dist/esm/protocol/create-method-url.js"], "sourcesContent": ["// Copyright 2021-2024 The Connect Authors\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n/**\n * Create a URL for the given RPC. This simply adds the qualified\n * service name, a slash, and the method name to the path of the given\n * baseUrl.\n *\n * For example, the baseUri https://example.com and method \"Say\" from\n * the service example.ElizaService results in:\n * https://example.com/example.ElizaService/Say\n *\n * This format is used by the protocols Connect, gRPC and Twirp.\n *\n * Note that this function also accepts a protocol-relative baseUrl.\n * If given an empty string or \"/\" as a baseUrl, it returns just the\n * path.\n */\nexport function createMethodUrl(baseUrl, service, method) {\n    const s = typeof service == \"string\" ? service : service.typeName;\n    const m = typeof method == \"string\" ? method : method.name;\n    return baseUrl.toString().replace(/\\/?$/, `/${s}/${m}`);\n}\n"], "names": [], "mappings": "AAAA,0CAA0C;AAC1C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC;;;;;;;;;;;;;;CAcC;;;AACM,SAAS,gBAAgB,OAAO,EAAE,OAAO,EAAE,MAAM;IACpD,MAAM,IAAI,OAAO,WAAW,WAAW,UAAU,QAAQ,QAAQ;IACjE,MAAM,IAAI,OAAO,UAAU,WAAW,SAAS,OAAO,IAAI;IAC1D,OAAO,QAAQ,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,GAAG;AAC1D", "ignoreList": [0]}}, {"offset": {"line": 2018, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@connectrpc+connect@1.6.1_@bufbuild+protobuf@1.10.1/node_modules/@connectrpc/connect/dist/esm/interceptor.js"], "sourcesContent": ["// Copyright 2021-2024 The Connect Authors\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n/**\n * applyInterceptors takes the given UnaryFn or ServerStreamingFn, and wraps\n * it with each of the given interceptors, returning a new UnaryFn or\n * ServerStreamingFn.\n */\nexport function applyInterceptors(next, interceptors) {\n    var _a;\n    return ((_a = interceptors === null || interceptors === void 0 ? void 0 : interceptors.concat().reverse().reduce(\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-argument\n    (n, i) => i(n), next)) !== null && _a !== void 0 ? _a : next);\n}\n"], "names": [], "mappings": "AAAA,0CAA0C;AAC1C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC;;;;CAIC;;;AACM,SAAS,kBAAkB,IAAI,EAAE,YAAY;IAChD,IAAI;IACJ,OAAQ,CAAC,KAAK,iBAAiB,QAAQ,iBAAiB,KAAK,IAAI,KAAK,IAAI,aAAa,MAAM,GAAG,OAAO,GAAG,MAAM,CAChH,iEAAiE;IACjE,CAAC,GAAG,IAAM,EAAE,IAAI,KAAK,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;AAC5D", "ignoreList": [0]}}, {"offset": {"line": 2049, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@connectrpc+connect@1.6.1_@bufbuild+protobuf@1.10.1/node_modules/@connectrpc/connect/dist/esm/protocol/signals.js"], "sourcesContent": ["// Copyright 2021-2024 The Connect Authors\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nimport { ConnectError } from \"../connect-error.js\";\nimport { Code } from \"../code.js\";\n/**\n * Create an AbortController that is automatically aborted if one of the given\n * signals is aborted.\n *\n * For convenience, the linked AbortSignals can be undefined.\n *\n * If the controller or any of the signals is aborted, all event listeners are\n * removed.\n *\n * @private Internal code, does not follow semantic versioning.\n */\nexport function createLinkedAbortController(...signals) {\n    const controller = new AbortController();\n    const sa = signals.filter((s) => s !== undefined).concat(controller.signal);\n    for (const signal of sa) {\n        if (signal.aborted) {\n            onAbort.apply(signal);\n            break;\n        }\n        signal.addEventListener(\"abort\", onAbort);\n    }\n    function onAbort() {\n        if (!controller.signal.aborted) {\n            controller.abort(getAbortSignalReason(this));\n        }\n        for (const signal of sa) {\n            signal.removeEventListener(\"abort\", onAbort);\n        }\n    }\n    return controller;\n}\n/**\n * Create a deadline signal. The returned object contains an AbortSignal, but\n * also a cleanup function to stop the timer, which must be called once the\n * calling code is no longer interested in the signal.\n *\n * Ideally, we would simply use AbortSignal.timeout(), but it is not widely\n * available yet.\n *\n * @private Internal code, does not follow semantic versioning.\n */\nexport function createDeadlineSignal(timeoutMs) {\n    const controller = new AbortController();\n    const listener = () => {\n        controller.abort(new ConnectError(\"the operation timed out\", Code.DeadlineExceeded));\n    };\n    let timeoutId;\n    if (timeoutMs !== undefined) {\n        if (timeoutMs <= 0)\n            listener();\n        else\n            timeoutId = setTimeout(listener, timeoutMs);\n    }\n    return {\n        signal: controller.signal,\n        cleanup: () => clearTimeout(timeoutId),\n    };\n}\n/**\n * Returns the reason why an AbortSignal was aborted. Returns undefined if the\n * signal has not been aborted.\n *\n * The property AbortSignal.reason is not widely available. This function\n * returns an AbortError if the signal is aborted, but reason is undefined.\n *\n * @private Internal code, does not follow semantic versioning.\n */\nexport function getAbortSignalReason(signal) {\n    if (!signal.aborted) {\n        return undefined;\n    }\n    if (signal.reason !== undefined) {\n        return signal.reason;\n    }\n    // AbortSignal.reason is available in Node.js v16, v18, and later,\n    // and in all browsers since early 2022.\n    const e = new Error(\"This operation was aborted\");\n    e.name = \"AbortError\";\n    return e;\n}\n"], "names": [], "mappings": "AAAA,0CAA0C;AAC1C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;;;;;AACjC;AACA;;;AAYO,SAAS,4BAA4B,GAAG,OAAO;IAClD,MAAM,aAAa,IAAI;IACvB,MAAM,KAAK,QAAQ,MAAM,CAAC,CAAC,IAAM,MAAM,WAAW,MAAM,CAAC,WAAW,MAAM;IAC1E,KAAK,MAAM,UAAU,GAAI;QACrB,IAAI,OAAO,OAAO,EAAE;YAChB,QAAQ,KAAK,CAAC;YACd;QACJ;QACA,OAAO,gBAAgB,CAAC,SAAS;IACrC;IACA,SAAS;QACL,IAAI,CAAC,WAAW,MAAM,CAAC,OAAO,EAAE;YAC5B,WAAW,KAAK,CAAC,qBAAqB,IAAI;QAC9C;QACA,KAAK,MAAM,UAAU,GAAI;YACrB,OAAO,mBAAmB,CAAC,SAAS;QACxC;IACJ;IACA,OAAO;AACX;AAWO,SAAS,qBAAqB,SAAS;IAC1C,MAAM,aAAa,IAAI;IACvB,MAAM,WAAW;QACb,WAAW,KAAK,CAAC,IAAI,+RAAA,CAAA,eAAY,CAAC,2BAA2B,mRAAA,CAAA,OAAI,CAAC,gBAAgB;IACtF;IACA,IAAI;IACJ,IAAI,cAAc,WAAW;QACzB,IAAI,aAAa,GACb;aAEA,YAAY,WAAW,UAAU;IACzC;IACA,OAAO;QACH,QAAQ,WAAW,MAAM;QACzB,SAAS,IAAM,aAAa;IAChC;AACJ;AAUO,SAAS,qBAAqB,MAAM;IACvC,IAAI,CAAC,OAAO,OAAO,EAAE;QACjB,OAAO;IACX;IACA,IAAI,OAAO,MAAM,KAAK,WAAW;QAC7B,OAAO,OAAO,MAAM;IACxB;IACA,kEAAkE;IAClE,wCAAwC;IACxC,MAAM,IAAI,IAAI,MAAM;IACpB,EAAE,IAAI,GAAG;IACT,OAAO;AACX", "ignoreList": [0]}}, {"offset": {"line": 2125, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@connectrpc+connect@1.6.1_@bufbuild+protobuf@1.10.1/node_modules/@connectrpc/connect/dist/esm/protocol/normalize.js"], "sourcesContent": ["// Copyright 2021-2024 The Connect Authors\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nimport { Message } from \"@bufbuild/protobuf\";\n/**\n *  Takes a partial protobuf messages of the\n *  specified message type as input, and returns full instances.\n */\nexport function normalize(type, message) {\n    return message instanceof type\n        ? message\n        : new type(message);\n}\n/**\n * Takes an AsyncIterable of partial protobuf messages of the\n * specified message type as input, and yields full instances.\n */\nexport function normalizeIterable(messageType, input) {\n    function transform(result) {\n        if (result.done === true) {\n            return result;\n        }\n        return {\n            done: result.done,\n            value: normalize(messageType, result.value),\n        };\n    }\n    return {\n        [Symbol.asyncIterator]() {\n            const it = input[Symbol.asyncIterator]();\n            const res = {\n                next: () => it.next().then(transform),\n            };\n            if (it.throw !== undefined) {\n                res.throw = (e) => it.throw(e).then(transform); // eslint-disable-line @typescript-eslint/no-non-null-assertion\n            }\n            if (it.return !== undefined) {\n                res.return = (v) => it.return(v).then(transform); // eslint-disable-line @typescript-eslint/no-non-null-assertion\n            }\n            return res;\n        },\n    };\n}\n"], "names": [], "mappings": "AAAA,0CAA0C;AAC1C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;;;;;AAM1B,SAAS,UAAU,IAAI,EAAE,OAAO;IACnC,OAAO,mBAAmB,OACpB,UACA,IAAI,KAAK;AACnB;AAKO,SAAS,kBAAkB,WAAW,EAAE,KAAK;IAChD,SAAS,UAAU,MAAM;QACrB,IAAI,OAAO,IAAI,KAAK,MAAM;YACtB,OAAO;QACX;QACA,OAAO;YACH,MAAM,OAAO,IAAI;YACjB,OAAO,UAAU,aAAa,OAAO,KAAK;QAC9C;IACJ;IACA,OAAO;QACH,CAAC,OAAO,aAAa,CAAC;YAClB,MAAM,KAAK,KAAK,CAAC,OAAO,aAAa,CAAC;YACtC,MAAM,MAAM;gBACR,MAAM,IAAM,GAAG,IAAI,GAAG,IAAI,CAAC;YAC/B;YACA,IAAI,GAAG,KAAK,KAAK,WAAW;gBACxB,IAAI,KAAK,GAAG,CAAC,IAAM,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,YAAY,+DAA+D;YACnH;YACA,IAAI,GAAG,MAAM,KAAK,WAAW;gBACzB,IAAI,MAAM,GAAG,CAAC,IAAM,GAAG,MAAM,CAAC,GAAG,IAAI,CAAC,YAAY,+DAA+D;YACrH;YACA,OAAO;QACX;IACJ;AACJ", "ignoreList": [0]}}, {"offset": {"line": 2178, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@connectrpc+connect@1.6.1_@bufbuild+protobuf@1.10.1/node_modules/@connectrpc/connect/dist/esm/protocol/run-call.js"], "sourcesContent": ["// Copyright 2021-2024 The Connect Authors\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nimport { applyInterceptors } from \"../interceptor.js\";\nimport { ConnectError } from \"../connect-error.js\";\nimport { createDeadlineSignal, createLinkedAbortController, getAbortSignalReason, } from \"./signals.js\";\nimport { normalize, normalizeIterable } from \"./normalize.js\";\n/**\n * Runs a unary method with the given interceptors. Note that this function\n * is only used when implementing a Transport.\n */\nexport function runUnaryCall(opt) {\n    const next = applyInterceptors(opt.next, opt.interceptors);\n    const [signal, abort, done] = setupSignal(opt);\n    const req = Object.assign(Object.assign({}, opt.req), { message: normalize(opt.req.method.I, opt.req.message), signal });\n    return next(req).then((res) => {\n        done();\n        return res;\n    }, abort);\n}\n/**\n * Runs a server-streaming method with the given interceptors. Note that this\n * function is only used when implementing a Transport.\n */\nexport function runStreamingCall(opt) {\n    const next = applyInterceptors(opt.next, opt.interceptors);\n    const [signal, abort, done] = setupSignal(opt);\n    const req = Object.assign(Object.assign({}, opt.req), { message: normalizeIterable(opt.req.method.I, opt.req.message), signal });\n    let doneCalled = false;\n    // Call return on the request iterable to indicate\n    // that we will no longer consume it and it should\n    // cleanup any allocated resources.\n    signal.addEventListener(\"abort\", function () {\n        var _a, _b;\n        const it = opt.req.message[Symbol.asyncIterator]();\n        // If the signal is aborted due to an error, we want to throw\n        // the error to the request iterator.\n        if (!doneCalled) {\n            (_a = it.throw) === null || _a === void 0 ? void 0 : _a.call(it, this.reason).catch(() => {\n                // throw returns a promise, which we don't care about.\n                //\n                // Uncaught promises are thrown at sometime/somewhere by the event loop,\n                // this is to ensure error is caught and ignored.\n            });\n        }\n        (_b = it.return) === null || _b === void 0 ? void 0 : _b.call(it).catch(() => {\n            // return returns a promise, which we don't care about.\n            //\n            // Uncaught promises are thrown at sometime/somewhere by the event loop,\n            // this is to ensure error is caught and ignored.\n        });\n    });\n    return next(req).then((res) => {\n        return Object.assign(Object.assign({}, res), { message: {\n                [Symbol.asyncIterator]() {\n                    const it = res.message[Symbol.asyncIterator]();\n                    return {\n                        next() {\n                            return it.next().then((r) => {\n                                if (r.done == true) {\n                                    doneCalled = true;\n                                    done();\n                                }\n                                return r;\n                            }, abort);\n                        },\n                        // We deliberately omit throw/return.\n                    };\n                },\n            } });\n    }, abort);\n}\n/**\n * Create an AbortSignal for Transport implementations. The signal is available\n * in UnaryRequest and StreamingRequest, and is triggered when the call is\n * aborted (via a timeout or explicit cancellation), errored (e.g. when reading\n * an error from the server from the wire), or finished successfully.\n *\n * Transport implementations can pass the signal to HTTP clients to ensure that\n * there are no unused connections leak.\n *\n * Returns a tuple:\n * [0]: The signal, which is also aborted if the optional deadline is reached.\n * [1]: Function to call if the Transport encountered an error.\n * [2]: Function to call if the Transport finished without an error.\n */\nfunction setupSignal(opt) {\n    const { signal, cleanup } = createDeadlineSignal(opt.timeoutMs);\n    const controller = createLinkedAbortController(opt.signal, signal);\n    return [\n        controller.signal,\n        function abort(reason) {\n            // We peek at the deadline signal because fetch() will throw an error on\n            // abort that discards the signal reason.\n            const e = ConnectError.from(signal.aborted ? getAbortSignalReason(signal) : reason);\n            controller.abort(e);\n            cleanup();\n            return Promise.reject(e);\n        },\n        function done() {\n            cleanup();\n            controller.abort();\n        },\n    ];\n}\n"], "names": [], "mappings": "AAAA,0CAA0C;AAC1C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;;;;AACjC;AACA;AACA;AACA;;;;;AAKO,SAAS,aAAa,GAAG;IAC5B,MAAM,OAAO,CAAA,GAAA,0RAAA,CAAA,oBAAiB,AAAD,EAAE,IAAI,IAAI,EAAE,IAAI,YAAY;IACzD,MAAM,CAAC,QAAQ,OAAO,KAAK,GAAG,YAAY;IAC1C,MAAM,MAAM,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,IAAI,GAAG,GAAG;QAAE,SAAS,CAAA,GAAA,oSAAA,CAAA,YAAS,AAAD,EAAE,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,GAAG,CAAC,OAAO;QAAG;IAAO;IACtH,OAAO,KAAK,KAAK,IAAI,CAAC,CAAC;QACnB;QACA,OAAO;IACX,GAAG;AACP;AAKO,SAAS,iBAAiB,GAAG;IAChC,MAAM,OAAO,CAAA,GAAA,0RAAA,CAAA,oBAAiB,AAAD,EAAE,IAAI,IAAI,EAAE,IAAI,YAAY;IACzD,MAAM,CAAC,QAAQ,OAAO,KAAK,GAAG,YAAY;IAC1C,MAAM,MAAM,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,IAAI,GAAG,GAAG;QAAE,SAAS,CAAA,GAAA,oSAAA,CAAA,oBAAiB,AAAD,EAAE,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,GAAG,CAAC,OAAO;QAAG;IAAO;IAC9H,IAAI,aAAa;IACjB,kDAAkD;IAClD,kDAAkD;IAClD,mCAAmC;IACnC,OAAO,gBAAgB,CAAC,SAAS;QAC7B,IAAI,IAAI;QACR,MAAM,KAAK,IAAI,GAAG,CAAC,OAAO,CAAC,OAAO,aAAa,CAAC;QAChD,6DAA6D;QAC7D,qCAAqC;QACrC,IAAI,CAAC,YAAY;YACb,CAAC,KAAK,GAAG,KAAK,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC;YAChF,sDAAsD;YACtD,EAAE;YACF,wEAAwE;YACxE,iDAAiD;YACrD;QACJ;QACA,CAAC,KAAK,GAAG,MAAM,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC,IAAI,KAAK,CAAC;QACpE,uDAAuD;QACvD,EAAE;QACF,wEAAwE;QACxE,iDAAiD;QACrD;IACJ;IACA,OAAO,KAAK,KAAK,IAAI,CAAC,CAAC;QACnB,OAAO,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM;YAAE,SAAS;gBAChD,CAAC,OAAO,aAAa,CAAC;oBAClB,MAAM,KAAK,IAAI,OAAO,CAAC,OAAO,aAAa,CAAC;oBAC5C,OAAO;wBACH;4BACI,OAAO,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;gCACnB,IAAI,EAAE,IAAI,IAAI,MAAM;oCAChB,aAAa;oCACb;gCACJ;gCACA,OAAO;4BACX,GAAG;wBACP;oBAEJ;gBACJ;YACJ;QAAE;IACV,GAAG;AACP;AACA;;;;;;;;;;;;;CAaC,GACD,SAAS,YAAY,GAAG;IACpB,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kSAAA,CAAA,uBAAoB,AAAD,EAAE,IAAI,SAAS;IAC9D,MAAM,aAAa,CAAA,GAAA,kSAAA,CAAA,8BAA2B,AAAD,EAAE,IAAI,MAAM,EAAE;IAC3D,OAAO;QACH,WAAW,MAAM;QACjB,SAAS,MAAM,MAAM;YACjB,wEAAwE;YACxE,yCAAyC;YACzC,MAAM,IAAI,+RAAA,CAAA,eAAY,CAAC,IAAI,CAAC,OAAO,OAAO,GAAG,CAAA,GAAA,kSAAA,CAAA,uBAAoB,AAAD,EAAE,UAAU;YAC5E,WAAW,KAAK,CAAC;YACjB;YACA,OAAO,QAAQ,MAAM,CAAC;QAC1B;QACA,SAAS;YACL;YACA,WAAW,KAAK;QACpB;KACH;AACL", "ignoreList": [0]}}, {"offset": {"line": 2305, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@connectrpc+connect@1.6.1_@bufbuild+protobuf@1.10.1/node_modules/@connectrpc/connect/dist/esm/protocol-connect/error-json.js"], "sourcesContent": ["// Copyright 2021-2024 The Connect Authors\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nvar __rest = (this && this.__rest) || function (s, e) {\n    var t = {};\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n        t[p] = s[p];\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n                t[p[i]] = s[p[i]];\n        }\n    return t;\n};\nimport { protoBase64 } from \"@bufbuild/protobuf\";\nimport { Code } from \"../code.js\";\nimport { ConnectError } from \"../connect-error.js\";\nimport { codeFromString, codeToString } from \"./code-string.js\";\n/**\n * Parse a Connect error from a JSON value.\n * Will return a ConnectError, and throw the provided fallback if parsing failed.\n *\n * @private Internal code, does not follow semantic versioning.\n */\nexport function errorFromJson(jsonValue, metadata, fallback) {\n    var _a;\n    if (metadata) {\n        new Headers(metadata).forEach((value, key) => fallback.metadata.append(key, value));\n    }\n    if (typeof jsonValue !== \"object\" ||\n        jsonValue == null ||\n        Array.isArray(jsonValue)) {\n        throw fallback;\n    }\n    let code = fallback.code;\n    if (\"code\" in jsonValue && typeof jsonValue.code === \"string\") {\n        code = (_a = codeFromString(jsonValue.code)) !== null && _a !== void 0 ? _a : code;\n    }\n    const message = jsonValue.message;\n    if (message != null && typeof message !== \"string\") {\n        throw fallback;\n    }\n    const error = new ConnectError(message !== null && message !== void 0 ? message : \"\", code, metadata);\n    if (\"details\" in jsonValue && Array.isArray(jsonValue.details)) {\n        for (const detail of jsonValue.details) {\n            if (detail === null ||\n                typeof detail != \"object\" ||\n                Array.isArray(detail) ||\n                typeof detail.type != \"string\" ||\n                typeof detail.value != \"string\") {\n                throw fallback;\n            }\n            try {\n                error.details.push({\n                    type: detail.type,\n                    value: protoBase64.dec(detail.value),\n                    debug: detail.debug,\n                });\n            }\n            catch (e) {\n                throw fallback;\n            }\n        }\n    }\n    return error;\n}\n/**\n * Parse a Connect error from a serialized JSON value.\n * Will return a ConnectError, and throw the provided fallback if parsing failed.\n *\n * @private Internal code, does not follow semantic versioning.\n */\nexport function errorFromJsonBytes(bytes, metadata, fallback) {\n    let jsonValue;\n    try {\n        jsonValue = JSON.parse(new TextDecoder().decode(bytes));\n    }\n    catch (e) {\n        throw fallback;\n    }\n    return errorFromJson(jsonValue, metadata, fallback);\n}\n/**\n * Serialize the given error to JSON.\n *\n * The JSON serialization options are required to produce the optional\n * human-readable representation in the \"debug\" key if the detail uses\n * google.protobuf.Any. If serialization of the \"debug\" value fails, it\n * is silently disregarded.\n *\n * See https://connectrpc.com/docs/protocol#error-end-stream\n *\n * @private Internal code, does not follow semantic versioning.\n */\nexport function errorToJson(error, jsonWriteOptions) {\n    const o = {\n        code: codeToString(error.code),\n    };\n    if (error.rawMessage.length > 0) {\n        o.message = error.rawMessage;\n    }\n    if (error.details.length > 0) {\n        o.details = error.details\n            .map((value) => {\n            if (\"getType\" in value) {\n                const i = {\n                    type: value.getType().typeName,\n                    value: value.toBinary(),\n                };\n                try {\n                    i.debug = value.toJson(jsonWriteOptions);\n                }\n                catch (e) {\n                    // We deliberately ignore errors that may occur when serializing\n                    // a message to JSON (the message contains an Any).\n                    // The rationale is that we are only trying to provide optional\n                    // debug information.\n                }\n                return i;\n            }\n            return value;\n        })\n            .map((_a) => {\n            var { value } = _a, rest = __rest(_a, [\"value\"]);\n            return (Object.assign(Object.assign({}, rest), { value: protoBase64.enc(value).replace(/=+$/, \"\") }));\n        });\n    }\n    return o;\n}\n/**\n * Serialize the given error to JSON. This calls errorToJson(), but stringifies\n * the result, and converts it into a UInt8Array.\n *\n * @private Internal code, does not follow semantic versioning.\n */\nexport function errorToJsonBytes(error, jsonWriteOptions) {\n    const textEncoder = new TextEncoder();\n    try {\n        const jsonObject = errorToJson(error, jsonWriteOptions);\n        const jsonString = JSON.stringify(jsonObject);\n        return textEncoder.encode(jsonString);\n    }\n    catch (e) {\n        const m = e instanceof Error ? e.message : String(e);\n        throw new ConnectError(`failed to serialize Connect Error: ${m}`, Code.Internal);\n    }\n}\n"], "names": [], "mappings": "AAAA,0CAA0C;AAC1C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;;;;;;AAYjC;AACA;AACA;AACA;AAdA,IAAI,SAAS,AAAC,IAAI,IAAI,IAAI,CAAC,MAAM,IAAK,SAAU,CAAC,EAAE,CAAC;IAChD,IAAI,IAAI,CAAC;IACT,IAAK,IAAI,KAAK,EAAG,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,MAAM,EAAE,OAAO,CAAC,KAAK,GAC9E,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IACf,IAAI,KAAK,QAAQ,OAAO,OAAO,qBAAqB,KAAK,YACrD,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,qBAAqB,CAAC,IAAI,IAAI,EAAE,MAAM,EAAE,IAAK;QACpE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,KAAK,OAAO,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GACzE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACzB;IACJ,OAAO;AACX;;;;;AAWO,SAAS,cAAc,SAAS,EAAE,QAAQ,EAAE,QAAQ;IACvD,IAAI;IACJ,IAAI,UAAU;QACV,IAAI,QAAQ,UAAU,OAAO,CAAC,CAAC,OAAO,MAAQ,SAAS,QAAQ,CAAC,MAAM,CAAC,KAAK;IAChF;IACA,IAAI,OAAO,cAAc,YACrB,aAAa,QACb,MAAM,OAAO,CAAC,YAAY;QAC1B,MAAM;IACV;IACA,IAAI,OAAO,SAAS,IAAI;IACxB,IAAI,UAAU,aAAa,OAAO,UAAU,IAAI,KAAK,UAAU;QAC3D,OAAO,CAAC,KAAK,CAAA,GAAA,oTAAA,CAAA,iBAAc,AAAD,EAAE,UAAU,IAAI,CAAC,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;IAClF;IACA,MAAM,UAAU,UAAU,OAAO;IACjC,IAAI,WAAW,QAAQ,OAAO,YAAY,UAAU;QAChD,MAAM;IACV;IACA,MAAM,QAAQ,IAAI,+RAAA,CAAA,eAAY,CAAC,YAAY,QAAQ,YAAY,KAAK,IAAI,UAAU,IAAI,MAAM;IAC5F,IAAI,aAAa,aAAa,MAAM,OAAO,CAAC,UAAU,OAAO,GAAG;QAC5D,KAAK,MAAM,UAAU,UAAU,OAAO,CAAE;YACpC,IAAI,WAAW,QACX,OAAO,UAAU,YACjB,MAAM,OAAO,CAAC,WACd,OAAO,OAAO,IAAI,IAAI,YACtB,OAAO,OAAO,KAAK,IAAI,UAAU;gBACjC,MAAM;YACV;YACA,IAAI;gBACA,MAAM,OAAO,CAAC,IAAI,CAAC;oBACf,MAAM,OAAO,IAAI;oBACjB,OAAO,oPAAA,CAAA,cAAW,CAAC,GAAG,CAAC,OAAO,KAAK;oBACnC,OAAO,OAAO,KAAK;gBACvB;YACJ,EACA,OAAO,GAAG;gBACN,MAAM;YACV;QACJ;IACJ;IACA,OAAO;AACX;AAOO,SAAS,mBAAmB,KAAK,EAAE,QAAQ,EAAE,QAAQ;IACxD,IAAI;IACJ,IAAI;QACA,YAAY,KAAK,KAAK,CAAC,IAAI,cAAc,MAAM,CAAC;IACpD,EACA,OAAO,GAAG;QACN,MAAM;IACV;IACA,OAAO,cAAc,WAAW,UAAU;AAC9C;AAaO,SAAS,YAAY,KAAK,EAAE,gBAAgB;IAC/C,MAAM,IAAI;QACN,MAAM,CAAA,GAAA,oTAAA,CAAA,eAAY,AAAD,EAAE,MAAM,IAAI;IACjC;IACA,IAAI,MAAM,UAAU,CAAC,MAAM,GAAG,GAAG;QAC7B,EAAE,OAAO,GAAG,MAAM,UAAU;IAChC;IACA,IAAI,MAAM,OAAO,CAAC,MAAM,GAAG,GAAG;QAC1B,EAAE,OAAO,GAAG,MAAM,OAAO,CACpB,GAAG,CAAC,CAAC;YACN,IAAI,aAAa,OAAO;gBACpB,MAAM,IAAI;oBACN,MAAM,MAAM,OAAO,GAAG,QAAQ;oBAC9B,OAAO,MAAM,QAAQ;gBACzB;gBACA,IAAI;oBACA,EAAE,KAAK,GAAG,MAAM,MAAM,CAAC;gBAC3B,EACA,OAAO,GAAG;gBACN,gEAAgE;gBAChE,mDAAmD;gBACnD,+DAA+D;gBAC/D,qBAAqB;gBACzB;gBACA,OAAO;YACX;YACA,OAAO;QACX,GACK,GAAG,CAAC,CAAC;YACN,IAAI,EAAE,KAAK,EAAE,GAAG,IAAI,OAAO,OAAO,IAAI;gBAAC;aAAQ;YAC/C,OAAQ,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,OAAO;gBAAE,OAAO,oPAAA,CAAA,cAAW,CAAC,GAAG,CAAC,OAAO,OAAO,CAAC,OAAO;YAAI;QACtG;IACJ;IACA,OAAO;AACX;AAOO,SAAS,iBAAiB,KAAK,EAAE,gBAAgB;IACpD,MAAM,cAAc,IAAI;IACxB,IAAI;QACA,MAAM,aAAa,YAAY,OAAO;QACtC,MAAM,aAAa,KAAK,SAAS,CAAC;QAClC,OAAO,YAAY,MAAM,CAAC;IAC9B,EACA,OAAO,GAAG;QACN,MAAM,IAAI,aAAa,QAAQ,EAAE,OAAO,GAAG,OAAO;QAClD,MAAM,IAAI,+RAAA,CAAA,eAAY,CAAC,CAAC,mCAAmC,EAAE,GAAG,EAAE,mRAAA,CAAA,OAAI,CAAC,QAAQ;IACnF;AACJ", "ignoreList": [0]}}, {"offset": {"line": 2437, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@connectrpc+connect@1.6.1_@bufbuild+protobuf@1.10.1/node_modules/@connectrpc/connect/dist/esm/protocol-connect/end-stream.js"], "sourcesContent": ["// Copyright 2021-2024 The Connect Authors\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nimport { errorFromJson, errorToJson } from \"./error-json.js\";\nimport { appendHeaders } from \"../http-headers.js\";\nimport { ConnectError } from \"../connect-error.js\";\nimport { Code } from \"../code.js\";\n/**\n * endStreamFlag indicates that the data in a EnvelopedMessage\n * is a EndStreamResponse of the Connect protocol.\n *\n * @private Internal code, does not follow semantic versioning.\n */\nexport const endStreamFlag = 0b00000010;\n/**\n * Parse an EndStreamResponse of the Connect protocol.\n * Throws a ConnectError on malformed input.\n *\n * @private Internal code, does not follow semantic versioning.\n */\nexport function endStreamFromJson(data) {\n    const parseErr = new ConnectError(\"invalid end stream\", Code.Unknown);\n    let jsonValue;\n    try {\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n        jsonValue = JSON.parse(typeof data == \"string\" ? data : new TextDecoder().decode(data));\n    }\n    catch (e) {\n        throw parseErr;\n    }\n    if (typeof jsonValue != \"object\" ||\n        jsonValue == null ||\n        Array.isArray(jsonValue)) {\n        throw parseErr;\n    }\n    const metadata = new Headers();\n    if (\"metadata\" in jsonValue) {\n        if (typeof jsonValue.metadata != \"object\" ||\n            jsonValue.metadata == null ||\n            Array.isArray(jsonValue.metadata)) {\n            throw parseErr;\n        }\n        for (const [key, values] of Object.entries(jsonValue.metadata)) {\n            if (!Array.isArray(values) ||\n                values.some((value) => typeof value != \"string\")) {\n                throw parseErr;\n            }\n            for (const value of values) {\n                metadata.append(key, value);\n            }\n        }\n    }\n    const error = \"error\" in jsonValue && jsonValue.error != null\n        ? errorFromJson(jsonValue.error, metadata, parseErr)\n        : undefined;\n    return { metadata, error };\n}\n/**\n * Serialize the given EndStreamResponse to JSON.\n *\n * The JSON serialization options are required to produce the optional\n * human-readable representation of error details if the detail uses\n * google.protobuf.Any.\n *\n * See https://connectrpc.com/docs/protocol#error-end-stream\n *\n * @private Internal code, does not follow semantic versioning.\n */\nexport function endStreamToJson(metadata, error, jsonWriteOptions) {\n    const es = {};\n    if (error !== undefined) {\n        es.error = errorToJson(error, jsonWriteOptions);\n        metadata = appendHeaders(metadata, error.metadata);\n    }\n    let hasMetadata = false;\n    const md = {};\n    metadata.forEach((value, key) => {\n        hasMetadata = true;\n        md[key] = [value];\n    });\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n    if (hasMetadata) {\n        es.metadata = md;\n    }\n    return es;\n}\n/**\n * Create a Serialization object that serializes a Connect EndStreamResponse.\n *\n * @private Internal code, does not follow semantic versioning.\n */\nexport function createEndStreamSerialization(options) {\n    const textEncoder = new TextEncoder();\n    return {\n        serialize(data) {\n            try {\n                const jsonObject = endStreamToJson(data.metadata, data.error, options);\n                const jsonString = JSON.stringify(jsonObject);\n                return textEncoder.encode(jsonString);\n            }\n            catch (e) {\n                const m = e instanceof Error ? e.message : String(e);\n                throw new ConnectError(`failed to serialize EndStreamResponse: ${m}`, Code.Internal);\n            }\n        },\n        parse(data) {\n            try {\n                return endStreamFromJson(data);\n            }\n            catch (e) {\n                const m = e instanceof Error ? e.message : String(e);\n                throw new ConnectError(`failed to parse EndStreamResponse: ${m}`, Code.InvalidArgument);\n            }\n        },\n    };\n}\n"], "names": [], "mappings": "AAAA,0CAA0C;AAC1C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;;;;;;AACjC;AACA;AACA;AACA;;;;;AAOO,MAAM,gBAAgB;AAOtB,SAAS,kBAAkB,IAAI;IAClC,MAAM,WAAW,IAAI,+RAAA,CAAA,eAAY,CAAC,sBAAsB,mRAAA,CAAA,OAAI,CAAC,OAAO;IACpE,IAAI;IACJ,IAAI;QACA,mEAAmE;QACnE,YAAY,KAAK,KAAK,CAAC,OAAO,QAAQ,WAAW,OAAO,IAAI,cAAc,MAAM,CAAC;IACrF,EACA,OAAO,GAAG;QACN,MAAM;IACV;IACA,IAAI,OAAO,aAAa,YACpB,aAAa,QACb,MAAM,OAAO,CAAC,YAAY;QAC1B,MAAM;IACV;IACA,MAAM,WAAW,IAAI;IACrB,IAAI,cAAc,WAAW;QACzB,IAAI,OAAO,UAAU,QAAQ,IAAI,YAC7B,UAAU,QAAQ,IAAI,QACtB,MAAM,OAAO,CAAC,UAAU,QAAQ,GAAG;YACnC,MAAM;QACV;QACA,KAAK,MAAM,CAAC,KAAK,OAAO,IAAI,OAAO,OAAO,CAAC,UAAU,QAAQ,EAAG;YAC5D,IAAI,CAAC,MAAM,OAAO,CAAC,WACf,OAAO,IAAI,CAAC,CAAC,QAAU,OAAO,SAAS,WAAW;gBAClD,MAAM;YACV;YACA,KAAK,MAAM,SAAS,OAAQ;gBACxB,SAAS,MAAM,CAAC,KAAK;YACzB;QACJ;IACJ;IACA,MAAM,QAAQ,WAAW,aAAa,UAAU,KAAK,IAAI,OACnD,CAAA,GAAA,mTAAA,CAAA,gBAAa,AAAD,EAAE,UAAU,KAAK,EAAE,UAAU,YACzC;IACN,OAAO;QAAE;QAAU;IAAM;AAC7B;AAYO,SAAS,gBAAgB,QAAQ,EAAE,KAAK,EAAE,gBAAgB;IAC7D,MAAM,KAAK,CAAC;IACZ,IAAI,UAAU,WAAW;QACrB,GAAG,KAAK,GAAG,CAAA,GAAA,mTAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAC9B,WAAW,CAAA,GAAA,8RAAA,CAAA,gBAAa,AAAD,EAAE,UAAU,MAAM,QAAQ;IACrD;IACA,IAAI,cAAc;IAClB,MAAM,KAAK,CAAC;IACZ,SAAS,OAAO,CAAC,CAAC,OAAO;QACrB,cAAc;QACd,EAAE,CAAC,IAAI,GAAG;YAAC;SAAM;IACrB;IACA,uEAAuE;IACvE,IAAI,aAAa;QACb,GAAG,QAAQ,GAAG;IAClB;IACA,OAAO;AACX;AAMO,SAAS,6BAA6B,OAAO;IAChD,MAAM,cAAc,IAAI;IACxB,OAAO;QACH,WAAU,IAAI;YACV,IAAI;gBACA,MAAM,aAAa,gBAAgB,KAAK,QAAQ,EAAE,KAAK,KAAK,EAAE;gBAC9D,MAAM,aAAa,KAAK,SAAS,CAAC;gBAClC,OAAO,YAAY,MAAM,CAAC;YAC9B,EACA,OAAO,GAAG;gBACN,MAAM,IAAI,aAAa,QAAQ,EAAE,OAAO,GAAG,OAAO;gBAClD,MAAM,IAAI,+RAAA,CAAA,eAAY,CAAC,CAAC,uCAAuC,EAAE,GAAG,EAAE,mRAAA,CAAA,OAAI,CAAC,QAAQ;YACvF;QACJ;QACA,OAAM,IAAI;YACN,IAAI;gBACA,OAAO,kBAAkB;YAC7B,EACA,OAAO,GAAG;gBACN,MAAM,IAAI,aAAa,QAAQ,EAAE,OAAO,GAAG,OAAO;gBAClD,MAAM,IAAI,+RAAA,CAAA,eAAY,CAAC,CAAC,mCAAmC,EAAE,GAAG,EAAE,mRAAA,CAAA,OAAI,CAAC,eAAe;YAC1F;QACJ;IACJ;AACJ", "ignoreList": [0]}}, {"offset": {"line": 2546, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@connectrpc+connect@1.6.1_@bufbuild+protobuf@1.10.1/node_modules/@connectrpc/connect/dist/esm/protocol-connect/headers.js"], "sourcesContent": ["// Copyright 2021-2024 The Connect Authors\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n/**\n * @private Internal code, does not follow semantic versioning.\n */\nexport const headerContentType = \"Content-Type\";\nexport const headerUnaryContentLength = \"Content-Length\";\nexport const headerUnaryEncoding = \"Content-Encoding\";\nexport const headerStreamEncoding = \"Connect-Content-Encoding\";\nexport const headerUnaryAcceptEncoding = \"Accept-Encoding\";\nexport const headerStreamAcceptEncoding = \"Connect-Accept-Encoding\";\nexport const headerTimeout = \"Connect-Timeout-Ms\";\nexport const headerProtocolVersion = \"Connect-Protocol-Version\";\nexport const headerUserAgent = \"User-Agent\";\n"], "names": [], "mappings": "AAAA,0CAA0C;AAC1C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC;;CAEC;;;;;;;;;;;AACM,MAAM,oBAAoB;AAC1B,MAAM,2BAA2B;AACjC,MAAM,sBAAsB;AAC5B,MAAM,uBAAuB;AAC7B,MAAM,4BAA4B;AAClC,MAAM,6BAA6B;AACnC,MAAM,gBAAgB;AACtB,MAAM,wBAAwB;AAC9B,MAAM,kBAAkB", "ignoreList": [0]}}, {"offset": {"line": 2587, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@connectrpc+connect@1.6.1_@bufbuild+protobuf@1.10.1/node_modules/@connectrpc/connect/dist/esm/protocol-connect/query-params.js"], "sourcesContent": ["// Copyright 2021-2024 The Connect Authors\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n/**\n * @private Internal code, does not follow semantic versioning.\n */\nexport const paramConnectVersion = \"connect\";\nexport const paramEncoding = \"encoding\";\nexport const paramCompression = \"compression\";\nexport const paramBase64 = \"base64\";\nexport const paramMessage = \"message\";\n"], "names": [], "mappings": "AAAA,0CAA0C;AAC1C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC;;CAEC;;;;;;;AACM,MAAM,sBAAsB;AAC5B,MAAM,gBAAgB;AACtB,MAAM,mBAAmB;AACzB,MAAM,cAAc;AACpB,MAAM,eAAe", "ignoreList": [0]}}, {"offset": {"line": 2620, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@connectrpc+connect@1.6.1_@bufbuild+protobuf@1.10.1/node_modules/@connectrpc/connect/dist/esm/protocol-connect/version.js"], "sourcesContent": ["// Copyright 2021-2024 The Connect Authors\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nimport { headerProtocolVersion } from \"./headers.js\";\nimport { paramConnectVersion } from \"./query-params.js\";\nimport { ConnectError } from \"../connect-error.js\";\nimport { Code } from \"../code.js\";\n/**\n * The only know value for the header Connect-Protocol-Version.\n *\n * @private Internal code, does not follow semantic versioning.\n */\nexport const protocolVersion = \"1\";\n/**\n * Requires the Connect-Protocol-Version header to be present with the expected\n * value. Raises a ConnectError with Code.InvalidArgument otherwise.\n *\n * @private Internal code, does not follow semantic versioning.\n */\nexport function requireProtocolVersionHeader(requestHeader) {\n    const v = requestHeader.get(headerProtocolVersion);\n    if (v === null) {\n        throw new ConnectError(`missing required header: set ${headerProtocolVersion} to \"${protocolVersion}\"`, Code.InvalidArgument);\n    }\n    else if (v !== protocolVersion) {\n        throw new ConnectError(`${headerProtocolVersion} must be \"${protocolVersion}\": got \"${v}\"`, Code.InvalidArgument);\n    }\n}\n/**\n * Requires the connect query parameter to be present with the expected value.\n * Raises a ConnectError with Code.InvalidArgument otherwise.\n *\n * @private Internal code, does not follow semantic versioning.\n */\nexport function requireProtocolVersionParam(queryParams) {\n    const v = queryParams.get(paramConnectVersion);\n    if (v === null) {\n        throw new ConnectError(`missing required parameter: set ${paramConnectVersion} to \"v${protocolVersion}\"`, Code.InvalidArgument);\n    }\n    else if (v !== `v${protocolVersion}`) {\n        throw new ConnectError(`${paramConnectVersion} must be \"v${protocolVersion}\": got \"${v}\"`, Code.InvalidArgument);\n    }\n}\n"], "names": [], "mappings": "AAAA,0CAA0C;AAC1C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;;;;;AACjC;AACA;AACA;AACA;;;;;AAMO,MAAM,kBAAkB;AAOxB,SAAS,6BAA6B,aAAa;IACtD,MAAM,IAAI,cAAc,GAAG,CAAC,6SAAA,CAAA,wBAAqB;IACjD,IAAI,MAAM,MAAM;QACZ,MAAM,IAAI,+RAAA,CAAA,eAAY,CAAC,CAAC,6BAA6B,EAAE,6SAAA,CAAA,wBAAqB,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAC,EAAE,mRAAA,CAAA,OAAI,CAAC,eAAe;IAChI,OACK,IAAI,MAAM,iBAAiB;QAC5B,MAAM,IAAI,+RAAA,CAAA,eAAY,CAAC,GAAG,6SAAA,CAAA,wBAAqB,CAAC,UAAU,EAAE,gBAAgB,QAAQ,EAAE,EAAE,CAAC,CAAC,EAAE,mRAAA,CAAA,OAAI,CAAC,eAAe;IACpH;AACJ;AAOO,SAAS,4BAA4B,WAAW;IACnD,MAAM,IAAI,YAAY,GAAG,CAAC,qTAAA,CAAA,sBAAmB;IAC7C,IAAI,MAAM,MAAM;QACZ,MAAM,IAAI,+RAAA,CAAA,eAAY,CAAC,CAAC,gCAAgC,EAAE,qTAAA,CAAA,sBAAmB,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAC,EAAE,mRAAA,CAAA,OAAI,CAAC,eAAe;IAClI,OACK,IAAI,MAAM,CAAC,CAAC,EAAE,iBAAiB,EAAE;QAClC,MAAM,IAAI,+RAAA,CAAA,eAAY,CAAC,GAAG,qTAAA,CAAA,sBAAmB,CAAC,WAAW,EAAE,gBAAgB,QAAQ,EAAE,EAAE,CAAC,CAAC,EAAE,mRAAA,CAAA,OAAI,CAAC,eAAe;IACnH;AACJ", "ignoreList": [0]}}, {"offset": {"line": 2669, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@connectrpc+connect@1.6.1_@bufbuild+protobuf@1.10.1/node_modules/@connectrpc/connect/dist/esm/protocol-connect/content-type.js"], "sourcesContent": ["// Copyright 2021-2024 The Connect Authors\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n/**\n * Regular Expression that matches any valid Connect Content-Type header value.\n *\n * @private Internal code, does not follow semantic versioning.\n */\nexport const contentTypeRegExp = /^application\\/(connect\\+)?(?:(json)(?:; ?charset=utf-?8)?|(proto))$/i;\n/**\n * Regular Expression that matches a Connect unary Content-Type header value.\n *\n * @private Internal code, does not follow semantic versioning.\n */\nexport const contentTypeUnaryRegExp = /^application\\/(?:json(?:; ?charset=utf-?8)?|proto)$/i;\n/**\n * Regular Expression that matches a Connect streaming Content-Type header value.\n *\n * @private Internal code, does not follow semantic versioning.\n */\nexport const contentTypeStreamRegExp = /^application\\/connect\\+?(?:json(?:; ?charset=utf-?8)?|proto)$/i;\nexport const contentTypeUnaryProto = \"application/proto\";\nexport const contentTypeUnaryJson = \"application/json\";\nexport const contentTypeStreamProto = \"application/connect+proto\";\nexport const contentTypeStreamJson = \"application/connect+json\";\nconst encodingProto = \"proto\";\nconst encodingJson = \"json\";\n/**\n * Parse a Connect Content-Type header.\n *\n * @private Internal code, does not follow semantic versioning.\n */\nexport function parseContentType(contentType) {\n    const match = contentType === null || contentType === void 0 ? void 0 : contentType.match(contentTypeRegExp);\n    if (!match) {\n        return undefined;\n    }\n    const stream = !!match[1];\n    const binary = !!match[3];\n    return { stream, binary };\n}\n/**\n * Parse a Connect Get encoding query parameter.\n *\n * @private Internal code, does not follow semantic versioning.\n */\nexport function parseEncodingQuery(encoding) {\n    switch (encoding) {\n        case encodingProto:\n            return { stream: false, binary: true };\n        case encodingJson:\n            return { stream: false, binary: false };\n        default:\n            return undefined;\n    }\n}\n"], "names": [], "mappings": "AAAA,0CAA0C;AAC1C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC;;;;CAIC;;;;;;;;;;;AACM,MAAM,oBAAoB;AAM1B,MAAM,yBAAyB;AAM/B,MAAM,0BAA0B;AAChC,MAAM,wBAAwB;AAC9B,MAAM,uBAAuB;AAC7B,MAAM,yBAAyB;AAC/B,MAAM,wBAAwB;AACrC,MAAM,gBAAgB;AACtB,MAAM,eAAe;AAMd,SAAS,iBAAiB,WAAW;IACxC,MAAM,QAAQ,gBAAgB,QAAQ,gBAAgB,KAAK,IAAI,KAAK,IAAI,YAAY,KAAK,CAAC;IAC1F,IAAI,CAAC,OAAO;QACR,OAAO;IACX;IACA,MAAM,SAAS,CAAC,CAAC,KAAK,CAAC,EAAE;IACzB,MAAM,SAAS,CAAC,CAAC,KAAK,CAAC,EAAE;IACzB,OAAO;QAAE;QAAQ;IAAO;AAC5B;AAMO,SAAS,mBAAmB,QAAQ;IACvC,OAAQ;QACJ,KAAK;YACD,OAAO;gBAAE,QAAQ;gBAAO,QAAQ;YAAK;QACzC,KAAK;YACD,OAAO;gBAAE,QAAQ;gBAAO,QAAQ;YAAM;QAC1C;YACI,OAAO;IACf;AACJ", "ignoreList": [0]}}, {"offset": {"line": 2740, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@connectrpc+connect@1.6.1_@bufbuild+protobuf@1.10.1/node_modules/@connectrpc/connect/dist/esm/protocol-connect/request-header.js"], "sourcesContent": ["// Copyright 2021-2024 The Connect Authors\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nimport { MethodKind } from \"@bufbuild/protobuf\";\nimport { headerContentType, headerStreamAcceptEncoding, headerStreamEncoding, headerUnaryAcceptEncoding, headerUnaryEncoding, headerTimeout, headerProtocolVersion, headerUserAgent, } from \"./headers.js\";\nimport { protocolVersion } from \"./version.js\";\nimport { contentTypeStreamJson, contentTypeStreamProto, contentTypeUnaryJson, contentTypeUnaryProto, } from \"./content-type.js\";\n/**\n * Creates headers for a Connect request.\n *\n * @private Internal code, does not follow semantic versioning.\n */\nexport function requestHeader(methodKind, useBinaryFormat, timeoutMs, userProvidedHeaders, setUserAgent) {\n    const result = new Headers(userProvidedHeaders !== null && userProvidedHeaders !== void 0 ? userProvidedHeaders : {});\n    if (timeoutMs !== undefined) {\n        result.set(headerTimeout, `${timeoutMs}`);\n    }\n    result.set(headerContentType, methodKind == MethodKind.Unary\n        ? useBinaryFormat\n            ? contentTypeUnaryProto\n            : contentTypeUnaryJson\n        : useBinaryFormat\n            ? contentTypeStreamProto\n            : contentTypeStreamJson);\n    result.set(headerProtocolVersion, protocolVersion);\n    if (setUserAgent) {\n        result.set(headerUserAgent, \"connect-es/1.6.1\");\n    }\n    return result;\n}\n/**\n * Creates headers for a Connect request with compression.\n *\n * Note that we always set the Content-Encoding header for unary methods.\n * It is up to the caller to decide whether to apply compression - and remove\n * the header if compression is not used, for example because the payload is\n * too small to make compression effective.\n *\n * @private Internal code, does not follow semantic versioning.\n */\nexport function requestHeaderWithCompression(methodKind, useBinaryFormat, timeoutMs, userProvidedHeaders, acceptCompression, sendCompression, setUserAgent) {\n    const result = requestHeader(methodKind, useBinaryFormat, timeoutMs, userProvidedHeaders, setUserAgent);\n    if (sendCompression != null) {\n        const name = methodKind == MethodKind.Unary\n            ? headerUnaryEncoding\n            : headerStreamEncoding;\n        result.set(name, sendCompression.name);\n    }\n    if (acceptCompression.length > 0) {\n        const name = methodKind == MethodKind.Unary\n            ? headerUnaryAcceptEncoding\n            : headerStreamAcceptEncoding;\n        result.set(name, acceptCompression.map((c) => c.name).join(\",\"));\n    }\n    return result;\n}\n"], "names": [], "mappings": "AAAA,0CAA0C;AAC1C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;;;;AACjC;AACA;AACA;AACA;;;;;AAMO,SAAS,cAAc,UAAU,EAAE,eAAe,EAAE,SAAS,EAAE,mBAAmB,EAAE,YAAY;IACnG,MAAM,SAAS,IAAI,QAAQ,wBAAwB,QAAQ,wBAAwB,KAAK,IAAI,sBAAsB,CAAC;IACnH,IAAI,cAAc,WAAW;QACzB,OAAO,GAAG,CAAC,6SAAA,CAAA,gBAAa,EAAE,GAAG,WAAW;IAC5C;IACA,OAAO,GAAG,CAAC,6SAAA,CAAA,oBAAiB,EAAE,cAAc,oPAAA,CAAA,aAAU,CAAC,KAAK,GACtD,kBACI,qTAAA,CAAA,wBAAqB,GACrB,qTAAA,CAAA,uBAAoB,GACxB,kBACI,qTAAA,CAAA,yBAAsB,GACtB,qTAAA,CAAA,wBAAqB;IAC/B,OAAO,GAAG,CAAC,6SAAA,CAAA,wBAAqB,EAAE,6SAAA,CAAA,kBAAe;IACjD,IAAI,cAAc;QACd,OAAO,GAAG,CAAC,6SAAA,CAAA,kBAAe,EAAE;IAChC;IACA,OAAO;AACX;AAWO,SAAS,6BAA6B,UAAU,EAAE,eAAe,EAAE,SAAS,EAAE,mBAAmB,EAAE,iBAAiB,EAAE,eAAe,EAAE,YAAY;IACtJ,MAAM,SAAS,cAAc,YAAY,iBAAiB,WAAW,qBAAqB;IAC1F,IAAI,mBAAmB,MAAM;QACzB,MAAM,OAAO,cAAc,oPAAA,CAAA,aAAU,CAAC,KAAK,GACrC,6SAAA,CAAA,sBAAmB,GACnB,6SAAA,CAAA,uBAAoB;QAC1B,OAAO,GAAG,CAAC,MAAM,gBAAgB,IAAI;IACzC;IACA,IAAI,kBAAkB,MAAM,GAAG,GAAG;QAC9B,MAAM,OAAO,cAAc,oPAAA,CAAA,aAAU,CAAC,KAAK,GACrC,6SAAA,CAAA,4BAAyB,GACzB,6SAAA,CAAA,6BAA0B;QAChC,OAAO,GAAG,CAAC,MAAM,kBAAkB,GAAG,CAAC,CAAC,IAAM,EAAE,IAAI,EAAE,IAAI,CAAC;IAC/D;IACA,OAAO;AACX", "ignoreList": [0]}}, {"offset": {"line": 2795, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@connectrpc+connect@1.6.1_@bufbuild+protobuf@1.10.1/node_modules/@connectrpc/connect/dist/esm/protocol-connect/trailer-mux.js"], "sourcesContent": ["// Copyright 2021-2024 The Connect Authors\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n/**\n * In unary RPCs, Connect transports trailing metadata as response header\n * fields, prefixed with \"trailer-\".\n *\n * This function demuxes headers and trailers into two separate Headers\n * objects.\n *\n * @private Internal code, does not follow semantic versioning.\n */\nexport function trailerDemux(header) {\n    const h = new Headers(), t = new Headers();\n    header.forEach((value, key) => {\n        if (key.toLowerCase().startsWith(\"trailer-\")) {\n            t.append(key.substring(8), value);\n        }\n        else {\n            h.append(key, value);\n        }\n    });\n    return [h, t];\n}\n/**\n * In unary RPCs, Connect transports trailing metadata as response header\n * fields, prefixed with \"trailer-\".\n *\n * This function muxes a header and a trailer into a single Headers object.\n *\n * @private Internal code, does not follow semantic versioning.\n */\nexport function trailerMux(header, trailer) {\n    const h = new Headers(header);\n    trailer.forEach((value, key) => {\n        h.append(`trailer-${key}`, value);\n    });\n    return h;\n}\n"], "names": [], "mappings": "AAAA,0CAA0C;AAC1C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC;;;;;;;;CAQC;;;;AACM,SAAS,aAAa,MAAM;IAC/B,MAAM,IAAI,IAAI,WAAW,IAAI,IAAI;IACjC,OAAO,OAAO,CAAC,CAAC,OAAO;QACnB,IAAI,IAAI,WAAW,GAAG,UAAU,CAAC,aAAa;YAC1C,EAAE,MAAM,CAAC,IAAI,SAAS,CAAC,IAAI;QAC/B,OACK;YACD,EAAE,MAAM,CAAC,KAAK;QAClB;IACJ;IACA,OAAO;QAAC;QAAG;KAAE;AACjB;AASO,SAAS,WAAW,MAAM,EAAE,OAAO;IACtC,MAAM,IAAI,IAAI,QAAQ;IACtB,QAAQ,OAAO,CAAC,CAAC,OAAO;QACpB,EAAE,MAAM,CAAC,CAAC,QAAQ,EAAE,KAAK,EAAE;IAC/B;IACA,OAAO;AACX", "ignoreList": [0]}}, {"offset": {"line": 2847, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@connectrpc+connect@1.6.1_@bufbuild+protobuf@1.10.1/node_modules/@connectrpc/connect/dist/esm/protocol-connect/get-request.js"], "sourcesContent": ["// Copyright 2021-2024 The Connect Authors\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nimport { Message, protoBase64 } from \"@bufbuild/protobuf\";\nimport { headerContentType, headerProtocolVersion, headerUnaryAcceptEncoding, headerUnaryContentLength, headerUnaryEncoding, } from \"./headers.js\";\nimport { protocolVersion } from \"./version.js\";\nconst contentTypePrefix = \"application/\";\nfunction encodeMessageForUrl(message, useBase64) {\n    if (useBase64) {\n        // TODO(jchadwick-buf): Three regex replaces seems excessive.\n        // Can we make protoBase64.enc more flexible?\n        return protoBase64\n            .enc(message)\n            .replace(/\\+/g, \"-\")\n            .replace(/\\//g, \"_\")\n            .replace(/=+$/, \"\");\n    }\n    else {\n        return encodeURIComponent(new TextDecoder().decode(message));\n    }\n}\n/**\n * @private Internal code, does not follow semantic versioning.\n */\nexport function transformConnectPostToGetRequest(request, message, useBase64) {\n    let query = `?connect=v${protocolVersion}`;\n    const contentType = request.header.get(headerContentType);\n    if ((contentType === null || contentType === void 0 ? void 0 : contentType.indexOf(contentTypePrefix)) === 0) {\n        query +=\n            \"&encoding=\" +\n                encodeURIComponent(contentType.slice(contentTypePrefix.length));\n    }\n    const compression = request.header.get(headerUnaryEncoding);\n    if (compression !== null && compression !== \"identity\") {\n        query += \"&compression=\" + encodeURIComponent(compression);\n        // Force base64 for compressed payloads.\n        useBase64 = true;\n    }\n    if (useBase64) {\n        query += \"&base64=1\";\n    }\n    query += \"&message=\" + encodeMessageForUrl(message, useBase64);\n    const url = request.url + query;\n    // Omit headers that are not used for unary GET requests.\n    const header = new Headers(request.header);\n    [\n        headerProtocolVersion,\n        headerContentType,\n        headerUnaryContentLength,\n        headerUnaryEncoding,\n        headerUnaryAcceptEncoding,\n    ].forEach((h) => header.delete(h));\n    return Object.assign(Object.assign({}, request), { init: Object.assign(Object.assign({}, request.init), { method: \"GET\" }), url,\n        header });\n}\n"], "names": [], "mappings": "AAAA,0CAA0C;AAC1C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;;;AACjC;AACA;AACA;;;;AACA,MAAM,oBAAoB;AAC1B,SAAS,oBAAoB,OAAO,EAAE,SAAS;IAC3C,IAAI,WAAW;QACX,6DAA6D;QAC7D,6CAA6C;QAC7C,OAAO,oPAAA,CAAA,cAAW,CACb,GAAG,CAAC,SACJ,OAAO,CAAC,OAAO,KACf,OAAO,CAAC,OAAO,KACf,OAAO,CAAC,OAAO;IACxB,OACK;QACD,OAAO,mBAAmB,IAAI,cAAc,MAAM,CAAC;IACvD;AACJ;AAIO,SAAS,iCAAiC,OAAO,EAAE,OAAO,EAAE,SAAS;IACxE,IAAI,QAAQ,CAAC,UAAU,EAAE,6SAAA,CAAA,kBAAe,EAAE;IAC1C,MAAM,cAAc,QAAQ,MAAM,CAAC,GAAG,CAAC,6SAAA,CAAA,oBAAiB;IACxD,IAAI,CAAC,gBAAgB,QAAQ,gBAAgB,KAAK,IAAI,KAAK,IAAI,YAAY,OAAO,CAAC,kBAAkB,MAAM,GAAG;QAC1G,SACI,eACI,mBAAmB,YAAY,KAAK,CAAC,kBAAkB,MAAM;IACzE;IACA,MAAM,cAAc,QAAQ,MAAM,CAAC,GAAG,CAAC,6SAAA,CAAA,sBAAmB;IAC1D,IAAI,gBAAgB,QAAQ,gBAAgB,YAAY;QACpD,SAAS,kBAAkB,mBAAmB;QAC9C,wCAAwC;QACxC,YAAY;IAChB;IACA,IAAI,WAAW;QACX,SAAS;IACb;IACA,SAAS,cAAc,oBAAoB,SAAS;IACpD,MAAM,MAAM,QAAQ,GAAG,GAAG;IAC1B,yDAAyD;IACzD,MAAM,SAAS,IAAI,QAAQ,QAAQ,MAAM;IACzC;QACI,6SAAA,CAAA,wBAAqB;QACrB,6SAAA,CAAA,oBAAiB;QACjB,6SAAA,CAAA,2BAAwB;QACxB,6SAAA,CAAA,sBAAmB;QACnB,6SAAA,CAAA,4BAAyB;KAC5B,CAAC,OAAO,CAAC,CAAC,IAAM,OAAO,MAAM,CAAC;IAC/B,OAAO,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,UAAU;QAAE,MAAM,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,QAAQ,IAAI,GAAG;YAAE,QAAQ;QAAM;QAAI;QACxH;IAAO;AACf", "ignoreList": [0]}}, {"offset": {"line": 2919, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@connectrpc+connect@1.6.1_@bufbuild+protobuf@1.10.1/node_modules/@connectrpc/connect/dist/esm/protocol-connect/http-status.js"], "sourcesContent": ["// Copyright 2021-2024 The Connect Authors\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nimport { Code } from \"../code.js\";\n/**\n * Determine the Connect error code for the given HTTP status code.\n * See https://connectrpc.com/docs/protocol/#http-to-error-code\n *\n * @private Internal code, does not follow semantic versioning.\n */\nexport function codeFromHttpStatus(httpStatus) {\n    switch (httpStatus) {\n        case 400: // Bad Request\n            return Code.Internal;\n        case 401: // Unauthorized\n            return Code.Unauthenticated;\n        case 403: // Forbidden\n            return Code.PermissionDenied;\n        case 404: // Not Found\n            return Code.Unimplemented;\n        case 429: // Too Many Requests\n            return Code.Unavailable;\n        case 502: // Bad Gateway\n            return Code.Unavailable;\n        case 503: // Service Unavailable\n            return Code.Unavailable;\n        case 504: // Gateway Timeout\n            return Code.Unavailable;\n        default:\n            return Code.Unknown;\n    }\n}\n/**\n * Returns a HTTP status code for the given Connect code.\n * See https://connectrpc.com/docs/protocol#error-codes\n *\n * @private Internal code, does not follow semantic versioning.\n */\nexport function codeToHttpStatus(code) {\n    switch (code) {\n        case Code.Canceled:\n            return 499; // Client Closed Request\n        case Code.Unknown:\n            return 500; // Internal Server Error\n        case Code.InvalidArgument:\n            return 400; // Bad Request\n        case Code.DeadlineExceeded:\n            return 504; // Gateway Timeout\n        case Code.NotFound:\n            return 404; // Not Found\n        case Code.AlreadyExists:\n            return 409; // Conflict\n        case Code.PermissionDenied:\n            return 403; // Forbidden\n        case Code.ResourceExhausted:\n            return 429; // Too Many Requests\n        case Code.FailedPrecondition:\n            return 400; // Bad Request\n        case Code.Aborted:\n            return 409; // Conflict\n        case Code.OutOfRange:\n            return 400; // Bad Request\n        case Code.Unimplemented:\n            return 501; // Not Implemented\n        case Code.Internal:\n            return 500; // Internal Server Error\n        case Code.Unavailable:\n            return 503; // Service Unavailable\n        case Code.DataLoss:\n            return 500; // Internal Server Error\n        case Code.Unauthenticated:\n            return 401; // Unauthorized\n        default:\n            return 500; // same as CodeUnknown\n    }\n}\n"], "names": [], "mappings": "AAAA,0CAA0C;AAC1C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;;;;AACjC;;AAOO,SAAS,mBAAmB,UAAU;IACzC,OAAQ;QACJ,KAAK;YACD,OAAO,mRAAA,CAAA,OAAI,CAAC,QAAQ;QACxB,KAAK;YACD,OAAO,mRAAA,CAAA,OAAI,CAAC,eAAe;QAC/B,KAAK;YACD,OAAO,mRAAA,CAAA,OAAI,CAAC,gBAAgB;QAChC,KAAK;YACD,OAAO,mRAAA,CAAA,OAAI,CAAC,aAAa;QAC7B,KAAK;YACD,OAAO,mRAAA,CAAA,OAAI,CAAC,WAAW;QAC3B,KAAK;YACD,OAAO,mRAAA,CAAA,OAAI,CAAC,WAAW;QAC3B,KAAK;YACD,OAAO,mRAAA,CAAA,OAAI,CAAC,WAAW;QAC3B,KAAK;YACD,OAAO,mRAAA,CAAA,OAAI,CAAC,WAAW;QAC3B;YACI,OAAO,mRAAA,CAAA,OAAI,CAAC,OAAO;IAC3B;AACJ;AAOO,SAAS,iBAAiB,IAAI;IACjC,OAAQ;QACJ,KAAK,mRAAA,CAAA,OAAI,CAAC,QAAQ;YACd,OAAO,KAAK,wBAAwB;QACxC,KAAK,mRAAA,CAAA,OAAI,CAAC,OAAO;YACb,OAAO,KAAK,wBAAwB;QACxC,KAAK,mRAAA,CAAA,OAAI,CAAC,eAAe;YACrB,OAAO,KAAK,cAAc;QAC9B,KAAK,mRAAA,CAAA,OAAI,CAAC,gBAAgB;YACtB,OAAO,KAAK,kBAAkB;QAClC,KAAK,mRAAA,CAAA,OAAI,CAAC,QAAQ;YACd,OAAO,KAAK,YAAY;QAC5B,KAAK,mRAAA,CAAA,OAAI,CAAC,aAAa;YACnB,OAAO,KAAK,WAAW;QAC3B,KAAK,mRAAA,CAAA,OAAI,CAAC,gBAAgB;YACtB,OAAO,KAAK,YAAY;QAC5B,KAAK,mRAAA,CAAA,OAAI,CAAC,iBAAiB;YACvB,OAAO,KAAK,oBAAoB;QACpC,KAAK,mRAAA,CAAA,OAAI,CAAC,kBAAkB;YACxB,OAAO,KAAK,cAAc;QAC9B,KAAK,mRAAA,CAAA,OAAI,CAAC,OAAO;YACb,OAAO,KAAK,WAAW;QAC3B,KAAK,mRAAA,CAAA,OAAI,CAAC,UAAU;YAChB,OAAO,KAAK,cAAc;QAC9B,KAAK,mRAAA,CAAA,OAAI,CAAC,aAAa;YACnB,OAAO,KAAK,kBAAkB;QAClC,KAAK,mRAAA,CAAA,OAAI,CAAC,QAAQ;YACd,OAAO,KAAK,wBAAwB;QACxC,KAAK,mRAAA,CAAA,OAAI,CAAC,WAAW;YACjB,OAAO,KAAK,sBAAsB;QACtC,KAAK,mRAAA,CAAA,OAAI,CAAC,QAAQ;YACd,OAAO,KAAK,wBAAwB;QACxC,KAAK,mRAAA,CAAA,OAAI,CAAC,eAAe;YACrB,OAAO,KAAK,eAAe;QAC/B;YACI,OAAO,KAAK,sBAAsB;IAC1C;AACJ", "ignoreList": [0]}}, {"offset": {"line": 3004, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@connectrpc+connect@1.6.1_@bufbuild+protobuf@1.10.1/node_modules/@connectrpc/connect/dist/esm/protocol-connect/validate-response.js"], "sourcesContent": ["// Copyright 2021-2024 The Connect Authors\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nimport { MethodKind } from \"@bufbuild/protobuf\";\nimport { Code } from \"../code.js\";\nimport { codeFromHttpStatus } from \"./http-status.js\";\nimport { ConnectError } from \"../connect-error.js\";\nimport { parseContentType } from \"./content-type.js\";\nimport { headerContentType, headerStreamEncoding, headerUnaryEncoding, } from \"./headers.js\";\n/**\n * Validates response status and header for the Connect protocol.\n * Throws a ConnectError if the header indicates an error, or if\n * the content type is unexpected, with the following exception:\n * For unary RPCs with an HTTP error status, this returns an error\n * derived from the HTTP status instead of throwing it, giving an\n * implementation a chance to parse a Connect error from the wire.\n *\n * @private Internal code, does not follow semantic versioning.\n */\nexport function validateResponse(methodKind, useBinaryFormat, status, headers) {\n    const mimeType = headers.get(headerContentType);\n    const parsedType = parseContentType(mimeType);\n    if (status !== 200) {\n        const errorFromStatus = new ConnectError(`HTTP ${status}`, codeFromHttpStatus(status), headers);\n        // If parsedType is defined and it is not binary, then this is a unary JSON response\n        if (methodKind == MethodKind.Unary && parsedType && !parsedType.binary) {\n            return { isUnaryError: true, unaryError: errorFromStatus };\n        }\n        throw errorFromStatus;\n    }\n    const allowedContentType = {\n        binary: useBinaryFormat,\n        stream: methodKind !== MethodKind.Unary,\n    };\n    if ((parsedType === null || parsedType === void 0 ? void 0 : parsedType.binary) !== allowedContentType.binary ||\n        parsedType.stream !== allowedContentType.stream) {\n        throw new ConnectError(`unsupported content type ${mimeType}`, parsedType === undefined ? Code.Unknown : Code.Internal, headers);\n    }\n    return { isUnaryError: false };\n}\n/**\n * Validates response status and header for the Connect protocol.\n * This function is identical to validateResponse(), but also verifies\n * that a given encoding header is acceptable.\n *\n * @private\n */\nexport function validateResponseWithCompression(methodKind, acceptCompression, useBinaryFormat, status, headers) {\n    let compression;\n    const encoding = headers.get(methodKind == MethodKind.Unary ? headerUnaryEncoding : headerStreamEncoding);\n    if (encoding != null && encoding.toLowerCase() !== \"identity\") {\n        compression = acceptCompression.find((c) => c.name === encoding);\n        if (!compression) {\n            throw new ConnectError(`unsupported response encoding \"${encoding}\"`, Code.Internal, headers);\n        }\n    }\n    return Object.assign({ compression }, validateResponse(methodKind, useBinaryFormat, status, headers));\n}\n"], "names": [], "mappings": "AAAA,0CAA0C;AAC1C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;;;;AACjC;AACA;AACA;AACA;AACA;AACA;;;;;;;AAWO,SAAS,iBAAiB,UAAU,EAAE,eAAe,EAAE,MAAM,EAAE,OAAO;IACzE,MAAM,WAAW,QAAQ,GAAG,CAAC,6SAAA,CAAA,oBAAiB;IAC9C,MAAM,aAAa,CAAA,GAAA,qTAAA,CAAA,mBAAgB,AAAD,EAAE;IACpC,IAAI,WAAW,KAAK;QAChB,MAAM,kBAAkB,IAAI,+RAAA,CAAA,eAAY,CAAC,CAAC,KAAK,EAAE,QAAQ,EAAE,CAAA,GAAA,oTAAA,CAAA,qBAAkB,AAAD,EAAE,SAAS;QACvF,oFAAoF;QACpF,IAAI,cAAc,oPAAA,CAAA,aAAU,CAAC,KAAK,IAAI,cAAc,CAAC,WAAW,MAAM,EAAE;YACpE,OAAO;gBAAE,cAAc;gBAAM,YAAY;YAAgB;QAC7D;QACA,MAAM;IACV;IACA,MAAM,qBAAqB;QACvB,QAAQ;QACR,QAAQ,eAAe,oPAAA,CAAA,aAAU,CAAC,KAAK;IAC3C;IACA,IAAI,CAAC,eAAe,QAAQ,eAAe,KAAK,IAAI,KAAK,IAAI,WAAW,MAAM,MAAM,mBAAmB,MAAM,IACzG,WAAW,MAAM,KAAK,mBAAmB,MAAM,EAAE;QACjD,MAAM,IAAI,+RAAA,CAAA,eAAY,CAAC,CAAC,yBAAyB,EAAE,UAAU,EAAE,eAAe,YAAY,mRAAA,CAAA,OAAI,CAAC,OAAO,GAAG,mRAAA,CAAA,OAAI,CAAC,QAAQ,EAAE;IAC5H;IACA,OAAO;QAAE,cAAc;IAAM;AACjC;AAQO,SAAS,gCAAgC,UAAU,EAAE,iBAAiB,EAAE,eAAe,EAAE,MAAM,EAAE,OAAO;IAC3G,IAAI;IACJ,MAAM,WAAW,QAAQ,GAAG,CAAC,cAAc,oPAAA,CAAA,aAAU,CAAC,KAAK,GAAG,6SAAA,CAAA,sBAAmB,GAAG,6SAAA,CAAA,uBAAoB;IACxG,IAAI,YAAY,QAAQ,SAAS,WAAW,OAAO,YAAY;QAC3D,cAAc,kBAAkB,IAAI,CAAC,CAAC,IAAM,EAAE,IAAI,KAAK;QACvD,IAAI,CAAC,aAAa;YACd,MAAM,IAAI,+RAAA,CAAA,eAAY,CAAC,CAAC,+BAA+B,EAAE,SAAS,CAAC,CAAC,EAAE,mRAAA,CAAA,OAAI,CAAC,QAAQ,EAAE;QACzF;IACJ;IACA,OAAO,OAAO,MAAM,CAAC;QAAE;IAAY,GAAG,iBAAiB,YAAY,iBAAiB,QAAQ;AAChG", "ignoreList": [0]}}]}