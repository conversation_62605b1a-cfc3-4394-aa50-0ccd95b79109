"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e";
exports.ids = ["vendor-chunks/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e"];
exports.modules = {

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/enum.js":
/*!*******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/enum.js ***!
  \*******************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.SpanNames = exports.TokenKind = exports.AllowedOperationTypes = void 0;\nvar AllowedOperationTypes;\n(function (AllowedOperationTypes) {\n    AllowedOperationTypes[\"QUERY\"] = \"query\";\n    AllowedOperationTypes[\"MUTATION\"] = \"mutation\";\n    AllowedOperationTypes[\"SUBSCRIPTION\"] = \"subscription\";\n})(AllowedOperationTypes = exports.AllowedOperationTypes || (exports.AllowedOperationTypes = {}));\nvar TokenKind;\n(function (TokenKind) {\n    TokenKind[\"SOF\"] = \"<SOF>\";\n    TokenKind[\"EOF\"] = \"<EOF>\";\n    TokenKind[\"BANG\"] = \"!\";\n    TokenKind[\"DOLLAR\"] = \"$\";\n    TokenKind[\"AMP\"] = \"&\";\n    TokenKind[\"PAREN_L\"] = \"(\";\n    TokenKind[\"PAREN_R\"] = \")\";\n    TokenKind[\"SPREAD\"] = \"...\";\n    TokenKind[\"COLON\"] = \":\";\n    TokenKind[\"EQUALS\"] = \"=\";\n    TokenKind[\"AT\"] = \"@\";\n    TokenKind[\"BRACKET_L\"] = \"[\";\n    TokenKind[\"BRACKET_R\"] = \"]\";\n    TokenKind[\"BRACE_L\"] = \"{\";\n    TokenKind[\"PIPE\"] = \"|\";\n    TokenKind[\"BRACE_R\"] = \"}\";\n    TokenKind[\"NAME\"] = \"Name\";\n    TokenKind[\"INT\"] = \"Int\";\n    TokenKind[\"FLOAT\"] = \"Float\";\n    TokenKind[\"STRING\"] = \"String\";\n    TokenKind[\"BLOCK_STRING\"] = \"BlockString\";\n    TokenKind[\"COMMENT\"] = \"Comment\";\n})(TokenKind = exports.TokenKind || (exports.TokenKind = {}));\nvar SpanNames;\n(function (SpanNames) {\n    SpanNames[\"EXECUTE\"] = \"graphql.execute\";\n    SpanNames[\"PARSE\"] = \"graphql.parse\";\n    SpanNames[\"RESOLVE\"] = \"graphql.resolve\";\n    SpanNames[\"VALIDATE\"] = \"graphql.validate\";\n    SpanNames[\"SCHEMA_VALIDATE\"] = \"graphql.validateSchema\";\n    SpanNames[\"SCHEMA_PARSE\"] = \"graphql.parseSchema\";\n})(SpanNames = exports.SpanNames || (exports.SpanNames = {}));\n//# sourceMappingURL=enum.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/enum.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/enums/AttributeNames.js":
/*!***********************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/enums/AttributeNames.js ***!
  \***********************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.AttributeNames = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar AttributeNames;\n(function (AttributeNames) {\n    AttributeNames[\"SOURCE\"] = \"graphql.source\";\n    AttributeNames[\"FIELD_NAME\"] = \"graphql.field.name\";\n    AttributeNames[\"FIELD_PATH\"] = \"graphql.field.path\";\n    AttributeNames[\"FIELD_TYPE\"] = \"graphql.field.type\";\n    AttributeNames[\"OPERATION_TYPE\"] = \"graphql.operation.type\";\n    AttributeNames[\"OPERATION_NAME\"] = \"graphql.operation.name\";\n    AttributeNames[\"VARIABLES\"] = \"graphql.variables.\";\n    AttributeNames[\"ERROR_VALIDATION_NAME\"] = \"graphql.validation.error\";\n})(AttributeNames = exports.AttributeNames || (exports.AttributeNames = {}));\n//# sourceMappingURL=AttributeNames.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/enums/AttributeNames.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/index.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/index.js ***!
  \********************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/instrumentation.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/types.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/index.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/instrumentation.js":
/*!******************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/instrumentation.js ***!
  \******************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.GraphQLInstrumentation = void 0;\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst enum_1 = __webpack_require__(/*! ./enum */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/enum.js\");\nconst AttributeNames_1 = __webpack_require__(/*! ./enums/AttributeNames */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/enums/AttributeNames.js\");\nconst symbols_1 = __webpack_require__(/*! ./symbols */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/symbols.js\");\nconst internal_types_1 = __webpack_require__(/*! ./internal-types */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/internal-types.js\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/utils.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/version.js\");\nconst DEFAULT_CONFIG = {\n    mergeItems: false,\n    depth: -1,\n    allowValues: false,\n    ignoreResolveSpans: false,\n};\nconst supportedVersions = ['>=14.0.0 <17'];\nclass GraphQLInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, Object.assign(Object.assign({}, DEFAULT_CONFIG), config));\n    }\n    setConfig(config = {}) {\n        super.setConfig(Object.assign(Object.assign({}, DEFAULT_CONFIG), config));\n    }\n    init() {\n        const module = new instrumentation_1.InstrumentationNodeModuleDefinition('graphql', supportedVersions);\n        module.files.push(this._addPatchingExecute());\n        module.files.push(this._addPatchingParser());\n        module.files.push(this._addPatchingValidate());\n        return module;\n    }\n    _addPatchingExecute() {\n        return new instrumentation_1.InstrumentationNodeModuleFile('graphql/execution/execute.js', supportedVersions, \n        // cannot make it work with appropriate type as execute function has 2\n        //types and/cannot import function but only types\n        (moduleExports) => {\n            if ((0, instrumentation_1.isWrapped)(moduleExports.execute)) {\n                this._unwrap(moduleExports, 'execute');\n            }\n            this._wrap(moduleExports, 'execute', this._patchExecute(moduleExports.defaultFieldResolver));\n            return moduleExports;\n        }, moduleExports => {\n            if (moduleExports) {\n                this._unwrap(moduleExports, 'execute');\n            }\n        });\n    }\n    _addPatchingParser() {\n        return new instrumentation_1.InstrumentationNodeModuleFile('graphql/language/parser.js', supportedVersions, (moduleExports) => {\n            if ((0, instrumentation_1.isWrapped)(moduleExports.parse)) {\n                this._unwrap(moduleExports, 'parse');\n            }\n            this._wrap(moduleExports, 'parse', this._patchParse());\n            return moduleExports;\n        }, (moduleExports) => {\n            if (moduleExports) {\n                this._unwrap(moduleExports, 'parse');\n            }\n        });\n    }\n    _addPatchingValidate() {\n        return new instrumentation_1.InstrumentationNodeModuleFile('graphql/validation/validate.js', supportedVersions, moduleExports => {\n            if ((0, instrumentation_1.isWrapped)(moduleExports.validate)) {\n                this._unwrap(moduleExports, 'validate');\n            }\n            this._wrap(moduleExports, 'validate', this._patchValidate());\n            return moduleExports;\n        }, moduleExports => {\n            if (moduleExports) {\n                this._unwrap(moduleExports, 'validate');\n            }\n        });\n    }\n    _patchExecute(defaultFieldResolved) {\n        const instrumentation = this;\n        return function execute(original) {\n            return function patchExecute() {\n                let processedArgs;\n                // case when apollo server is used for example\n                if (arguments.length >= 2) {\n                    const args = arguments;\n                    processedArgs = instrumentation._wrapExecuteArgs(args[0], args[1], args[2], args[3], args[4], args[5], args[6], args[7], defaultFieldResolved);\n                }\n                else {\n                    const args = arguments[0];\n                    processedArgs = instrumentation._wrapExecuteArgs(args.schema, args.document, args.rootValue, args.contextValue, args.variableValues, args.operationName, args.fieldResolver, args.typeResolver, defaultFieldResolved);\n                }\n                const operation = (0, utils_1.getOperation)(processedArgs.document, processedArgs.operationName);\n                const span = instrumentation._createExecuteSpan(operation, processedArgs);\n                processedArgs.contextValue[symbols_1.OTEL_GRAPHQL_DATA_SYMBOL] = {\n                    source: processedArgs.document\n                        ? processedArgs.document ||\n                            processedArgs.document[symbols_1.OTEL_GRAPHQL_DATA_SYMBOL]\n                        : undefined,\n                    span,\n                    fields: {},\n                };\n                return api_1.context.with(api_1.trace.setSpan(api_1.context.active(), span), () => {\n                    return (0, instrumentation_1.safeExecuteInTheMiddle)(() => {\n                        return original.apply(this, [\n                            processedArgs,\n                        ]);\n                    }, (err, result) => {\n                        instrumentation._handleExecutionResult(span, err, result);\n                    });\n                });\n            };\n        };\n    }\n    _handleExecutionResult(span, err, result) {\n        const config = this.getConfig();\n        if (result === undefined || err) {\n            (0, utils_1.endSpan)(span, err);\n            return;\n        }\n        if ((0, utils_1.isPromise)(result)) {\n            result.then(resultData => {\n                if (typeof config.responseHook !== 'function') {\n                    (0, utils_1.endSpan)(span);\n                    return;\n                }\n                this._executeResponseHook(span, resultData);\n            }, error => {\n                (0, utils_1.endSpan)(span, error);\n            });\n        }\n        else {\n            if (typeof config.responseHook !== 'function') {\n                (0, utils_1.endSpan)(span);\n                return;\n            }\n            this._executeResponseHook(span, result);\n        }\n    }\n    _executeResponseHook(span, result) {\n        const { responseHook } = this.getConfig();\n        if (!responseHook) {\n            return;\n        }\n        (0, instrumentation_1.safeExecuteInTheMiddle)(() => {\n            responseHook(span, result);\n        }, err => {\n            if (err) {\n                this._diag.error('Error running response hook', err);\n            }\n            (0, utils_1.endSpan)(span, undefined);\n        }, true);\n    }\n    _patchParse() {\n        const instrumentation = this;\n        return function parse(original) {\n            return function patchParse(source, options) {\n                return instrumentation._parse(this, original, source, options);\n            };\n        };\n    }\n    _patchValidate() {\n        const instrumentation = this;\n        return function validate(original) {\n            return function patchValidate(schema, documentAST, rules, options, typeInfo) {\n                return instrumentation._validate(this, original, schema, documentAST, rules, typeInfo, options);\n            };\n        };\n    }\n    _parse(obj, original, source, options) {\n        const config = this.getConfig();\n        const span = this.tracer.startSpan(enum_1.SpanNames.PARSE);\n        return api_1.context.with(api_1.trace.setSpan(api_1.context.active(), span), () => {\n            return (0, instrumentation_1.safeExecuteInTheMiddle)(() => {\n                return original.call(obj, source, options);\n            }, (err, result) => {\n                if (result) {\n                    const operation = (0, utils_1.getOperation)(result);\n                    if (!operation) {\n                        span.updateName(enum_1.SpanNames.SCHEMA_PARSE);\n                    }\n                    else if (result.loc) {\n                        (0, utils_1.addSpanSource)(span, result.loc, config.allowValues);\n                    }\n                }\n                (0, utils_1.endSpan)(span, err);\n            });\n        });\n    }\n    _validate(obj, original, schema, documentAST, rules, typeInfo, options) {\n        const span = this.tracer.startSpan(enum_1.SpanNames.VALIDATE, {});\n        return api_1.context.with(api_1.trace.setSpan(api_1.context.active(), span), () => {\n            return (0, instrumentation_1.safeExecuteInTheMiddle)(() => {\n                return original.call(obj, schema, documentAST, rules, options, typeInfo);\n            }, (err, errors) => {\n                if (!documentAST.loc) {\n                    span.updateName(enum_1.SpanNames.SCHEMA_VALIDATE);\n                }\n                if (errors && errors.length) {\n                    span.recordException({\n                        name: AttributeNames_1.AttributeNames.ERROR_VALIDATION_NAME,\n                        message: JSON.stringify(errors),\n                    });\n                }\n                (0, utils_1.endSpan)(span, err);\n            });\n        });\n    }\n    _createExecuteSpan(operation, processedArgs) {\n        var _a;\n        const config = this.getConfig();\n        const span = this.tracer.startSpan(enum_1.SpanNames.EXECUTE, {});\n        if (operation) {\n            const { operation: operationType, name: nameNode } = operation;\n            span.setAttribute(AttributeNames_1.AttributeNames.OPERATION_TYPE, operationType);\n            const operationName = nameNode === null || nameNode === void 0 ? void 0 : nameNode.value;\n            // https://opentelemetry.io/docs/reference/specification/trace/semantic_conventions/instrumentation/graphql/\n            // > The span name MUST be of the format <graphql.operation.type> <graphql.operation.name> provided that graphql.operation.type and graphql.operation.name are available.\n            // > If graphql.operation.name is not available, the span SHOULD be named <graphql.operation.type>.\n            if (operationName) {\n                span.setAttribute(AttributeNames_1.AttributeNames.OPERATION_NAME, operationName);\n                span.updateName(`${operationType} ${operationName}`);\n            }\n            else {\n                span.updateName(operationType);\n            }\n        }\n        else {\n            let operationName = ' ';\n            if (processedArgs.operationName) {\n                operationName = ` \"${processedArgs.operationName}\" `;\n            }\n            operationName = internal_types_1.OPERATION_NOT_SUPPORTED.replace('$operationName$', operationName);\n            span.setAttribute(AttributeNames_1.AttributeNames.OPERATION_NAME, operationName);\n        }\n        if ((_a = processedArgs.document) === null || _a === void 0 ? void 0 : _a.loc) {\n            (0, utils_1.addSpanSource)(span, processedArgs.document.loc, config.allowValues);\n        }\n        if (processedArgs.variableValues && config.allowValues) {\n            (0, utils_1.addInputVariableAttributes)(span, processedArgs.variableValues);\n        }\n        return span;\n    }\n    _wrapExecuteArgs(schema, document, rootValue, contextValue, variableValues, operationName, fieldResolver, typeResolver, defaultFieldResolved) {\n        if (!contextValue) {\n            contextValue = {};\n        }\n        if (contextValue[symbols_1.OTEL_GRAPHQL_DATA_SYMBOL] ||\n            this.getConfig().ignoreResolveSpans) {\n            return {\n                schema,\n                document,\n                rootValue,\n                contextValue,\n                variableValues,\n                operationName,\n                fieldResolver,\n                typeResolver,\n            };\n        }\n        const isUsingDefaultResolver = fieldResolver == null;\n        // follows graphql implementation here:\n        // https://github.com/graphql/graphql-js/blob/0b7daed9811731362c71900e12e5ea0d1ecc7f1f/src/execution/execute.ts#L494\n        const fieldResolverForExecute = fieldResolver !== null && fieldResolver !== void 0 ? fieldResolver : defaultFieldResolved;\n        fieldResolver = (0, utils_1.wrapFieldResolver)(this.tracer, () => this.getConfig(), fieldResolverForExecute, isUsingDefaultResolver);\n        if (schema) {\n            (0, utils_1.wrapFields)(schema.getQueryType(), this.tracer, () => this.getConfig());\n            (0, utils_1.wrapFields)(schema.getMutationType(), this.tracer, () => this.getConfig());\n        }\n        return {\n            schema,\n            document,\n            rootValue,\n            contextValue,\n            variableValues,\n            operationName,\n            fieldResolver,\n            typeResolver,\n        };\n    }\n}\nexports.GraphQLInstrumentation = GraphQLInstrumentation;\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/instrumentation.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/internal-types.js":
/*!*****************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/internal-types.js ***!
  \*****************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.OPERATION_NOT_SUPPORTED = void 0;\nconst symbols_1 = __webpack_require__(/*! ./symbols */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/symbols.js\");\nexports.OPERATION_NOT_SUPPORTED = 'Operation$operationName$not' + ' supported';\n//# sourceMappingURL=internal-types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/internal-types.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/symbols.js":
/*!**********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/symbols.js ***!
  \**********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.OTEL_GRAPHQL_DATA_SYMBOL = exports.OTEL_PATCHED_SYMBOL = void 0;\nexports.OTEL_PATCHED_SYMBOL = Symbol.for('opentelemetry.patched');\nexports.OTEL_GRAPHQL_DATA_SYMBOL = Symbol.for('opentelemetry.graphql_data');\n//# sourceMappingURL=symbols.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/symbols.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/types.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/types.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9Ab3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfNzhlYWU2NTYwZDQ3NTY2NzJiMDVkMWZhNmUwZmU3NWUvbm9kZV9tb2R1bGVzL0BvcGVudGVsZW1ldHJ5L2luc3RydW1lbnRhdGlvbi1ncmFwaHFsL2J1aWxkL3NyYy90eXBlcy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbndhclxcRG9jdW1lbnRzXFxDdWJlbnR3ZWJcXGN1YmVudFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQG9wZW50ZWxlbWV0cnkraW5zdHJ1bWVudGF0Xzc4ZWFlNjU2MGQ0NzU2NjcyYjA1ZDFmYTZlMGZlNzVlXFxub2RlX21vZHVsZXNcXEBvcGVudGVsZW1ldHJ5XFxpbnN0cnVtZW50YXRpb24tZ3JhcGhxbFxcYnVpbGRcXHNyY1xcdHlwZXMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG4vKlxuICogQ29weXJpZ2h0IFRoZSBPcGVuVGVsZW1ldHJ5IEF1dGhvcnNcbiAqXG4gKiBMaWNlbnNlZCB1bmRlciB0aGUgQXBhY2hlIExpY2Vuc2UsIFZlcnNpb24gMi4wICh0aGUgXCJMaWNlbnNlXCIpO1xuICogeW91IG1heSBub3QgdXNlIHRoaXMgZmlsZSBleGNlcHQgaW4gY29tcGxpYW5jZSB3aXRoIHRoZSBMaWNlbnNlLlxuICogWW91IG1heSBvYnRhaW4gYSBjb3B5IG9mIHRoZSBMaWNlbnNlIGF0XG4gKlxuICogICAgICBodHRwczovL3d3dy5hcGFjaGUub3JnL2xpY2Vuc2VzL0xJQ0VOU0UtMi4wXG4gKlxuICogVW5sZXNzIHJlcXVpcmVkIGJ5IGFwcGxpY2FibGUgbGF3IG9yIGFncmVlZCB0byBpbiB3cml0aW5nLCBzb2Z0d2FyZVxuICogZGlzdHJpYnV0ZWQgdW5kZXIgdGhlIExpY2Vuc2UgaXMgZGlzdHJpYnV0ZWQgb24gYW4gXCJBUyBJU1wiIEJBU0lTLFxuICogV0lUSE9VVCBXQVJSQU5USUVTIE9SIENPTkRJVElPTlMgT0YgQU5ZIEtJTkQsIGVpdGhlciBleHByZXNzIG9yIGltcGxpZWQuXG4gKiBTZWUgdGhlIExpY2Vuc2UgZm9yIHRoZSBzcGVjaWZpYyBsYW5ndWFnZSBnb3Zlcm5pbmcgcGVybWlzc2lvbnMgYW5kXG4gKiBsaW1pdGF0aW9ucyB1bmRlciB0aGUgTGljZW5zZS5cbiAqL1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dHlwZXMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/types.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/utils.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/utils.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.wrapFieldResolver = exports.wrapFields = exports.getSourceFromLocation = exports.getOperation = exports.endSpan = exports.addSpanSource = exports.addInputVariableAttributes = exports.isPromise = void 0;\nconst api = __webpack_require__(/*! @opentelemetry/api */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst enum_1 = __webpack_require__(/*! ./enum */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/enum.js\");\nconst AttributeNames_1 = __webpack_require__(/*! ./enums/AttributeNames */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/enums/AttributeNames.js\");\nconst symbols_1 = __webpack_require__(/*! ./symbols */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/symbols.js\");\nconst OPERATION_VALUES = Object.values(enum_1.AllowedOperationTypes);\n// https://github.com/graphql/graphql-js/blob/main/src/jsutils/isPromise.ts\nconst isPromise = (value) => {\n    return typeof (value === null || value === void 0 ? void 0 : value.then) === 'function';\n};\nexports.isPromise = isPromise;\n// https://github.com/graphql/graphql-js/blob/main/src/jsutils/isObjectLike.ts\nconst isObjectLike = (value) => {\n    return typeof value == 'object' && value !== null;\n};\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction addInputVariableAttribute(span, key, variable) {\n    if (Array.isArray(variable)) {\n        variable.forEach((value, idx) => {\n            addInputVariableAttribute(span, `${key}.${idx}`, value);\n        });\n    }\n    else if (variable instanceof Object) {\n        Object.entries(variable).forEach(([nestedKey, value]) => {\n            addInputVariableAttribute(span, `${key}.${nestedKey}`, value);\n        });\n    }\n    else {\n        span.setAttribute(`${AttributeNames_1.AttributeNames.VARIABLES}${String(key)}`, variable);\n    }\n}\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction addInputVariableAttributes(span, variableValues) {\n    Object.entries(variableValues).forEach(([key, value]) => {\n        addInputVariableAttribute(span, key, value);\n    });\n}\nexports.addInputVariableAttributes = addInputVariableAttributes;\nfunction addSpanSource(span, loc, allowValues, start, end) {\n    const source = getSourceFromLocation(loc, allowValues, start, end);\n    span.setAttribute(AttributeNames_1.AttributeNames.SOURCE, source);\n}\nexports.addSpanSource = addSpanSource;\nfunction createFieldIfNotExists(tracer, getConfig, contextValue, info, path) {\n    let field = getField(contextValue, path);\n    let spanAdded = false;\n    if (!field) {\n        spanAdded = true;\n        const parent = getParentField(contextValue, path);\n        field = {\n            parent,\n            span: createResolverSpan(tracer, getConfig, contextValue, info, path, parent.span),\n            error: null,\n        };\n        addField(contextValue, path, field);\n    }\n    return { spanAdded, field };\n}\nfunction createResolverSpan(tracer, getConfig, contextValue, info, path, parentSpan) {\n    var _a, _b;\n    const attributes = {\n        [AttributeNames_1.AttributeNames.FIELD_NAME]: info.fieldName,\n        [AttributeNames_1.AttributeNames.FIELD_PATH]: path.join('.'),\n        [AttributeNames_1.AttributeNames.FIELD_TYPE]: info.returnType.toString(),\n    };\n    const span = tracer.startSpan(`${enum_1.SpanNames.RESOLVE} ${attributes[AttributeNames_1.AttributeNames.FIELD_PATH]}`, {\n        attributes,\n    }, parentSpan ? api.trace.setSpan(api.context.active(), parentSpan) : undefined);\n    const document = contextValue[symbols_1.OTEL_GRAPHQL_DATA_SYMBOL].source;\n    const fieldNode = info.fieldNodes.find(fieldNode => fieldNode.kind === 'Field');\n    if (fieldNode) {\n        addSpanSource(span, document.loc, getConfig().allowValues, (_a = fieldNode.loc) === null || _a === void 0 ? void 0 : _a.start, (_b = fieldNode.loc) === null || _b === void 0 ? void 0 : _b.end);\n    }\n    return span;\n}\nfunction endSpan(span, error) {\n    if (error) {\n        span.recordException(error);\n    }\n    span.end();\n}\nexports.endSpan = endSpan;\nfunction getOperation(document, operationName) {\n    if (!document || !Array.isArray(document.definitions)) {\n        return undefined;\n    }\n    if (operationName) {\n        return document.definitions\n            .filter(definition => { var _a; return OPERATION_VALUES.indexOf((_a = definition) === null || _a === void 0 ? void 0 : _a.operation) !== -1; })\n            .find(definition => { var _a, _b; return operationName === ((_b = (_a = definition) === null || _a === void 0 ? void 0 : _a.name) === null || _b === void 0 ? void 0 : _b.value); });\n    }\n    else {\n        return document.definitions.find(definition => { var _a; return OPERATION_VALUES.indexOf((_a = definition) === null || _a === void 0 ? void 0 : _a.operation) !== -1; });\n    }\n}\nexports.getOperation = getOperation;\nfunction addField(contextValue, path, field) {\n    return (contextValue[symbols_1.OTEL_GRAPHQL_DATA_SYMBOL].fields[path.join('.')] =\n        field);\n}\nfunction getField(contextValue, path) {\n    return contextValue[symbols_1.OTEL_GRAPHQL_DATA_SYMBOL].fields[path.join('.')];\n}\nfunction getParentField(contextValue, path) {\n    for (let i = path.length - 1; i > 0; i--) {\n        const field = getField(contextValue, path.slice(0, i));\n        if (field) {\n            return field;\n        }\n    }\n    return {\n        span: contextValue[symbols_1.OTEL_GRAPHQL_DATA_SYMBOL].span,\n    };\n}\nfunction pathToArray(mergeItems, path) {\n    const flattened = [];\n    let curr = path;\n    while (curr) {\n        let key = curr.key;\n        if (mergeItems && typeof key === 'number') {\n            key = '*';\n        }\n        flattened.push(String(key));\n        curr = curr.prev;\n    }\n    return flattened.reverse();\n}\nfunction repeatBreak(i) {\n    return repeatChar('\\n', i);\n}\nfunction repeatSpace(i) {\n    return repeatChar(' ', i);\n}\nfunction repeatChar(char, to) {\n    let text = '';\n    for (let i = 0; i < to; i++) {\n        text += char;\n    }\n    return text;\n}\nconst KindsToBeRemoved = [\n    enum_1.TokenKind.FLOAT,\n    enum_1.TokenKind.STRING,\n    enum_1.TokenKind.INT,\n    enum_1.TokenKind.BLOCK_STRING,\n];\nfunction getSourceFromLocation(loc, allowValues = false, inputStart, inputEnd) {\n    var _a, _b;\n    let source = '';\n    if (loc === null || loc === void 0 ? void 0 : loc.startToken) {\n        const start = typeof inputStart === 'number' ? inputStart : loc.start;\n        const end = typeof inputEnd === 'number' ? inputEnd : loc.end;\n        let next = loc.startToken.next;\n        let previousLine = 1;\n        while (next) {\n            if (next.start < start) {\n                next = next.next;\n                previousLine = next === null || next === void 0 ? void 0 : next.line;\n                continue;\n            }\n            if (next.end > end) {\n                next = next.next;\n                previousLine = next === null || next === void 0 ? void 0 : next.line;\n                continue;\n            }\n            let value = next.value || next.kind;\n            let space = '';\n            if (!allowValues && KindsToBeRemoved.indexOf(next.kind) >= 0) {\n                // value = repeatChar('*', value.length);\n                value = '*';\n            }\n            if (next.kind === enum_1.TokenKind.STRING) {\n                value = `\"${value}\"`;\n            }\n            if (next.kind === enum_1.TokenKind.EOF) {\n                value = '';\n            }\n            if (next.line > previousLine) {\n                source += repeatBreak(next.line - previousLine);\n                previousLine = next.line;\n                space = repeatSpace(next.column - 1);\n            }\n            else {\n                if (next.line === ((_a = next.prev) === null || _a === void 0 ? void 0 : _a.line)) {\n                    space = repeatSpace(next.start - (((_b = next.prev) === null || _b === void 0 ? void 0 : _b.end) || 0));\n                }\n            }\n            source += space + value;\n            if (next) {\n                next = next.next;\n            }\n        }\n    }\n    return source;\n}\nexports.getSourceFromLocation = getSourceFromLocation;\nfunction wrapFields(type, tracer, getConfig) {\n    if (!type ||\n        typeof type.getFields !== 'function' ||\n        type[symbols_1.OTEL_PATCHED_SYMBOL]) {\n        return;\n    }\n    const fields = type.getFields();\n    type[symbols_1.OTEL_PATCHED_SYMBOL] = true;\n    Object.keys(fields).forEach(key => {\n        const field = fields[key];\n        if (!field) {\n            return;\n        }\n        if (field.resolve) {\n            field.resolve = wrapFieldResolver(tracer, getConfig, field.resolve);\n        }\n        if (field.type) {\n            let unwrappedType = field.type;\n            while (unwrappedType.ofType) {\n                unwrappedType = unwrappedType.ofType;\n            }\n            wrapFields(unwrappedType, tracer, getConfig);\n        }\n    });\n}\nexports.wrapFields = wrapFields;\nconst handleResolveSpanError = (resolveSpan, err, shouldEndSpan) => {\n    if (!shouldEndSpan) {\n        return;\n    }\n    resolveSpan.recordException(err);\n    resolveSpan.setStatus({\n        code: api.SpanStatusCode.ERROR,\n        message: err.message,\n    });\n    resolveSpan.end();\n};\nconst handleResolveSpanSuccess = (resolveSpan, shouldEndSpan) => {\n    if (!shouldEndSpan) {\n        return;\n    }\n    resolveSpan.end();\n};\nfunction wrapFieldResolver(tracer, getConfig, fieldResolver, isDefaultResolver = false) {\n    if (wrappedFieldResolver[symbols_1.OTEL_PATCHED_SYMBOL] ||\n        typeof fieldResolver !== 'function') {\n        return fieldResolver;\n    }\n    function wrappedFieldResolver(source, args, contextValue, info) {\n        if (!fieldResolver) {\n            return undefined;\n        }\n        const config = getConfig();\n        // follows what graphql is doing to decide if this is a trivial resolver\n        // for which we don't need to create a resolve span\n        if (config.ignoreTrivialResolveSpans &&\n            isDefaultResolver &&\n            (isObjectLike(source) || typeof source === 'function')) {\n            const property = source[info.fieldName];\n            // a function execution is not trivial and should be recorder.\n            // property which is not a function is just a value and we don't want a \"resolve\" span for it\n            if (typeof property !== 'function') {\n                return fieldResolver.call(this, source, args, contextValue, info);\n            }\n        }\n        if (!contextValue[symbols_1.OTEL_GRAPHQL_DATA_SYMBOL]) {\n            return fieldResolver.call(this, source, args, contextValue, info);\n        }\n        const path = pathToArray(config.mergeItems, info && info.path);\n        const depth = path.filter((item) => typeof item === 'string').length;\n        let field;\n        let shouldEndSpan = false;\n        if (config.depth >= 0 && config.depth < depth) {\n            field = getParentField(contextValue, path);\n        }\n        else {\n            const newField = createFieldIfNotExists(tracer, getConfig, contextValue, info, path);\n            field = newField.field;\n            shouldEndSpan = newField.spanAdded;\n        }\n        return api.context.with(api.trace.setSpan(api.context.active(), field.span), () => {\n            try {\n                const res = fieldResolver.call(this, source, args, contextValue, info);\n                if ((0, exports.isPromise)(res)) {\n                    return res.then((r) => {\n                        handleResolveSpanSuccess(field.span, shouldEndSpan);\n                        return r;\n                    }, (err) => {\n                        handleResolveSpanError(field.span, err, shouldEndSpan);\n                        throw err;\n                    });\n                }\n                else {\n                    handleResolveSpanSuccess(field.span, shouldEndSpan);\n                    return res;\n                }\n            }\n            catch (err) {\n                handleResolveSpanError(field.span, err, shouldEndSpan);\n                throw err;\n            }\n        });\n    }\n    wrappedFieldResolver[symbols_1.OTEL_PATCHED_SYMBOL] = true;\n    return wrappedFieldResolver;\n}\nexports.wrapFieldResolver = wrapFieldResolver;\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/utils.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/version.js":
/*!**********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/version.js ***!
  \**********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.47.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-graphql';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/version.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/enum.js":
/*!*******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/enum.js ***!
  \*******************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.SpanNames = exports.TokenKind = exports.AllowedOperationTypes = void 0;\nvar AllowedOperationTypes;\n(function (AllowedOperationTypes) {\n    AllowedOperationTypes[\"QUERY\"] = \"query\";\n    AllowedOperationTypes[\"MUTATION\"] = \"mutation\";\n    AllowedOperationTypes[\"SUBSCRIPTION\"] = \"subscription\";\n})(AllowedOperationTypes = exports.AllowedOperationTypes || (exports.AllowedOperationTypes = {}));\nvar TokenKind;\n(function (TokenKind) {\n    TokenKind[\"SOF\"] = \"<SOF>\";\n    TokenKind[\"EOF\"] = \"<EOF>\";\n    TokenKind[\"BANG\"] = \"!\";\n    TokenKind[\"DOLLAR\"] = \"$\";\n    TokenKind[\"AMP\"] = \"&\";\n    TokenKind[\"PAREN_L\"] = \"(\";\n    TokenKind[\"PAREN_R\"] = \")\";\n    TokenKind[\"SPREAD\"] = \"...\";\n    TokenKind[\"COLON\"] = \":\";\n    TokenKind[\"EQUALS\"] = \"=\";\n    TokenKind[\"AT\"] = \"@\";\n    TokenKind[\"BRACKET_L\"] = \"[\";\n    TokenKind[\"BRACKET_R\"] = \"]\";\n    TokenKind[\"BRACE_L\"] = \"{\";\n    TokenKind[\"PIPE\"] = \"|\";\n    TokenKind[\"BRACE_R\"] = \"}\";\n    TokenKind[\"NAME\"] = \"Name\";\n    TokenKind[\"INT\"] = \"Int\";\n    TokenKind[\"FLOAT\"] = \"Float\";\n    TokenKind[\"STRING\"] = \"String\";\n    TokenKind[\"BLOCK_STRING\"] = \"BlockString\";\n    TokenKind[\"COMMENT\"] = \"Comment\";\n})(TokenKind = exports.TokenKind || (exports.TokenKind = {}));\nvar SpanNames;\n(function (SpanNames) {\n    SpanNames[\"EXECUTE\"] = \"graphql.execute\";\n    SpanNames[\"PARSE\"] = \"graphql.parse\";\n    SpanNames[\"RESOLVE\"] = \"graphql.resolve\";\n    SpanNames[\"VALIDATE\"] = \"graphql.validate\";\n    SpanNames[\"SCHEMA_VALIDATE\"] = \"graphql.validateSchema\";\n    SpanNames[\"SCHEMA_PARSE\"] = \"graphql.parseSchema\";\n})(SpanNames = exports.SpanNames || (exports.SpanNames = {}));\n//# sourceMappingURL=enum.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF83OGVhZTY1NjBkNDc1NjY3MmIwNWQxZmE2ZTBmZTc1ZS9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLWdyYXBocWwvYnVpbGQvc3JjL2VudW0uanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsaUJBQWlCLEdBQUcsaUJBQWlCLEdBQUcsNkJBQTZCO0FBQ3JFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLDREQUE0RCw2QkFBNkIsS0FBSztBQUMvRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2QkFBNkI7QUFDN0I7QUFDQSw2QkFBNkI7QUFDN0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxvQ0FBb0MsaUJBQWlCLEtBQUs7QUFDM0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsb0NBQW9DLGlCQUFpQixLQUFLO0FBQzNEIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFud2FyXFxEb2N1bWVudHNcXEN1YmVudHdlYlxcY3ViZW50XFxub2RlX21vZHVsZXNcXC5wbnBtXFxAb3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfNzhlYWU2NTYwZDQ3NTY2NzJiMDVkMWZhNmUwZmU3NWVcXG5vZGVfbW9kdWxlc1xcQG9wZW50ZWxlbWV0cnlcXGluc3RydW1lbnRhdGlvbi1ncmFwaHFsXFxidWlsZFxcc3JjXFxlbnVtLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuLypcbiAqIENvcHlyaWdodCBUaGUgT3BlblRlbGVtZXRyeSBBdXRob3JzXG4gKlxuICogTGljZW5zZWQgdW5kZXIgdGhlIEFwYWNoZSBMaWNlbnNlLCBWZXJzaW9uIDIuMCAodGhlIFwiTGljZW5zZVwiKTtcbiAqIHlvdSBtYXkgbm90IHVzZSB0aGlzIGZpbGUgZXhjZXB0IGluIGNvbXBsaWFuY2Ugd2l0aCB0aGUgTGljZW5zZS5cbiAqIFlvdSBtYXkgb2J0YWluIGEgY29weSBvZiB0aGUgTGljZW5zZSBhdFxuICpcbiAqICAgICAgaHR0cHM6Ly93d3cuYXBhY2hlLm9yZy9saWNlbnNlcy9MSUNFTlNFLTIuMFxuICpcbiAqIFVubGVzcyByZXF1aXJlZCBieSBhcHBsaWNhYmxlIGxhdyBvciBhZ3JlZWQgdG8gaW4gd3JpdGluZywgc29mdHdhcmVcbiAqIGRpc3RyaWJ1dGVkIHVuZGVyIHRoZSBMaWNlbnNlIGlzIGRpc3RyaWJ1dGVkIG9uIGFuIFwiQVMgSVNcIiBCQVNJUyxcbiAqIFdJVEhPVVQgV0FSUkFOVElFUyBPUiBDT05ESVRJT05TIE9GIEFOWSBLSU5ELCBlaXRoZXIgZXhwcmVzcyBvciBpbXBsaWVkLlxuICogU2VlIHRoZSBMaWNlbnNlIGZvciB0aGUgc3BlY2lmaWMgbGFuZ3VhZ2UgZ292ZXJuaW5nIHBlcm1pc3Npb25zIGFuZFxuICogbGltaXRhdGlvbnMgdW5kZXIgdGhlIExpY2Vuc2UuXG4gKi9cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuU3Bhbk5hbWVzID0gZXhwb3J0cy5Ub2tlbktpbmQgPSBleHBvcnRzLkFsbG93ZWRPcGVyYXRpb25UeXBlcyA9IHZvaWQgMDtcbnZhciBBbGxvd2VkT3BlcmF0aW9uVHlwZXM7XG4oZnVuY3Rpb24gKEFsbG93ZWRPcGVyYXRpb25UeXBlcykge1xuICAgIEFsbG93ZWRPcGVyYXRpb25UeXBlc1tcIlFVRVJZXCJdID0gXCJxdWVyeVwiO1xuICAgIEFsbG93ZWRPcGVyYXRpb25UeXBlc1tcIk1VVEFUSU9OXCJdID0gXCJtdXRhdGlvblwiO1xuICAgIEFsbG93ZWRPcGVyYXRpb25UeXBlc1tcIlNVQlNDUklQVElPTlwiXSA9IFwic3Vic2NyaXB0aW9uXCI7XG59KShBbGxvd2VkT3BlcmF0aW9uVHlwZXMgPSBleHBvcnRzLkFsbG93ZWRPcGVyYXRpb25UeXBlcyB8fCAoZXhwb3J0cy5BbGxvd2VkT3BlcmF0aW9uVHlwZXMgPSB7fSkpO1xudmFyIFRva2VuS2luZDtcbihmdW5jdGlvbiAoVG9rZW5LaW5kKSB7XG4gICAgVG9rZW5LaW5kW1wiU09GXCJdID0gXCI8U09GPlwiO1xuICAgIFRva2VuS2luZFtcIkVPRlwiXSA9IFwiPEVPRj5cIjtcbiAgICBUb2tlbktpbmRbXCJCQU5HXCJdID0gXCIhXCI7XG4gICAgVG9rZW5LaW5kW1wiRE9MTEFSXCJdID0gXCIkXCI7XG4gICAgVG9rZW5LaW5kW1wiQU1QXCJdID0gXCImXCI7XG4gICAgVG9rZW5LaW5kW1wiUEFSRU5fTFwiXSA9IFwiKFwiO1xuICAgIFRva2VuS2luZFtcIlBBUkVOX1JcIl0gPSBcIilcIjtcbiAgICBUb2tlbktpbmRbXCJTUFJFQURcIl0gPSBcIi4uLlwiO1xuICAgIFRva2VuS2luZFtcIkNPTE9OXCJdID0gXCI6XCI7XG4gICAgVG9rZW5LaW5kW1wiRVFVQUxTXCJdID0gXCI9XCI7XG4gICAgVG9rZW5LaW5kW1wiQVRcIl0gPSBcIkBcIjtcbiAgICBUb2tlbktpbmRbXCJCUkFDS0VUX0xcIl0gPSBcIltcIjtcbiAgICBUb2tlbktpbmRbXCJCUkFDS0VUX1JcIl0gPSBcIl1cIjtcbiAgICBUb2tlbktpbmRbXCJCUkFDRV9MXCJdID0gXCJ7XCI7XG4gICAgVG9rZW5LaW5kW1wiUElQRVwiXSA9IFwifFwiO1xuICAgIFRva2VuS2luZFtcIkJSQUNFX1JcIl0gPSBcIn1cIjtcbiAgICBUb2tlbktpbmRbXCJOQU1FXCJdID0gXCJOYW1lXCI7XG4gICAgVG9rZW5LaW5kW1wiSU5UXCJdID0gXCJJbnRcIjtcbiAgICBUb2tlbktpbmRbXCJGTE9BVFwiXSA9IFwiRmxvYXRcIjtcbiAgICBUb2tlbktpbmRbXCJTVFJJTkdcIl0gPSBcIlN0cmluZ1wiO1xuICAgIFRva2VuS2luZFtcIkJMT0NLX1NUUklOR1wiXSA9IFwiQmxvY2tTdHJpbmdcIjtcbiAgICBUb2tlbktpbmRbXCJDT01NRU5UXCJdID0gXCJDb21tZW50XCI7XG59KShUb2tlbktpbmQgPSBleHBvcnRzLlRva2VuS2luZCB8fCAoZXhwb3J0cy5Ub2tlbktpbmQgPSB7fSkpO1xudmFyIFNwYW5OYW1lcztcbihmdW5jdGlvbiAoU3Bhbk5hbWVzKSB7XG4gICAgU3Bhbk5hbWVzW1wiRVhFQ1VURVwiXSA9IFwiZ3JhcGhxbC5leGVjdXRlXCI7XG4gICAgU3Bhbk5hbWVzW1wiUEFSU0VcIl0gPSBcImdyYXBocWwucGFyc2VcIjtcbiAgICBTcGFuTmFtZXNbXCJSRVNPTFZFXCJdID0gXCJncmFwaHFsLnJlc29sdmVcIjtcbiAgICBTcGFuTmFtZXNbXCJWQUxJREFURVwiXSA9IFwiZ3JhcGhxbC52YWxpZGF0ZVwiO1xuICAgIFNwYW5OYW1lc1tcIlNDSEVNQV9WQUxJREFURVwiXSA9IFwiZ3JhcGhxbC52YWxpZGF0ZVNjaGVtYVwiO1xuICAgIFNwYW5OYW1lc1tcIlNDSEVNQV9QQVJTRVwiXSA9IFwiZ3JhcGhxbC5wYXJzZVNjaGVtYVwiO1xufSkoU3Bhbk5hbWVzID0gZXhwb3J0cy5TcGFuTmFtZXMgfHwgKGV4cG9ydHMuU3Bhbk5hbWVzID0ge30pKTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWVudW0uanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/enum.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/enums/AttributeNames.js":
/*!***********************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/enums/AttributeNames.js ***!
  \***********************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.AttributeNames = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar AttributeNames;\n(function (AttributeNames) {\n    AttributeNames[\"SOURCE\"] = \"graphql.source\";\n    AttributeNames[\"FIELD_NAME\"] = \"graphql.field.name\";\n    AttributeNames[\"FIELD_PATH\"] = \"graphql.field.path\";\n    AttributeNames[\"FIELD_TYPE\"] = \"graphql.field.type\";\n    AttributeNames[\"OPERATION_TYPE\"] = \"graphql.operation.type\";\n    AttributeNames[\"OPERATION_NAME\"] = \"graphql.operation.name\";\n    AttributeNames[\"VARIABLES\"] = \"graphql.variables.\";\n    AttributeNames[\"ERROR_VALIDATION_NAME\"] = \"graphql.validation.error\";\n})(AttributeNames = exports.AttributeNames || (exports.AttributeNames = {}));\n//# sourceMappingURL=AttributeNames.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/enums/AttributeNames.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/index.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/index.js ***!
  \********************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/instrumentation.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/types.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/index.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/instrumentation.js":
/*!******************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/instrumentation.js ***!
  \******************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.GraphQLInstrumentation = void 0;\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst enum_1 = __webpack_require__(/*! ./enum */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/enum.js\");\nconst AttributeNames_1 = __webpack_require__(/*! ./enums/AttributeNames */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/enums/AttributeNames.js\");\nconst symbols_1 = __webpack_require__(/*! ./symbols */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/symbols.js\");\nconst internal_types_1 = __webpack_require__(/*! ./internal-types */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/internal-types.js\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/utils.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/version.js\");\nconst DEFAULT_CONFIG = {\n    mergeItems: false,\n    depth: -1,\n    allowValues: false,\n    ignoreResolveSpans: false,\n};\nconst supportedVersions = ['>=14.0.0 <17'];\nclass GraphQLInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, Object.assign(Object.assign({}, DEFAULT_CONFIG), config));\n    }\n    setConfig(config = {}) {\n        super.setConfig(Object.assign(Object.assign({}, DEFAULT_CONFIG), config));\n    }\n    init() {\n        const module = new instrumentation_1.InstrumentationNodeModuleDefinition('graphql', supportedVersions);\n        module.files.push(this._addPatchingExecute());\n        module.files.push(this._addPatchingParser());\n        module.files.push(this._addPatchingValidate());\n        return module;\n    }\n    _addPatchingExecute() {\n        return new instrumentation_1.InstrumentationNodeModuleFile('graphql/execution/execute.js', supportedVersions, \n        // cannot make it work with appropriate type as execute function has 2\n        //types and/cannot import function but only types\n        (moduleExports) => {\n            if ((0, instrumentation_1.isWrapped)(moduleExports.execute)) {\n                this._unwrap(moduleExports, 'execute');\n            }\n            this._wrap(moduleExports, 'execute', this._patchExecute(moduleExports.defaultFieldResolver));\n            return moduleExports;\n        }, moduleExports => {\n            if (moduleExports) {\n                this._unwrap(moduleExports, 'execute');\n            }\n        });\n    }\n    _addPatchingParser() {\n        return new instrumentation_1.InstrumentationNodeModuleFile('graphql/language/parser.js', supportedVersions, (moduleExports) => {\n            if ((0, instrumentation_1.isWrapped)(moduleExports.parse)) {\n                this._unwrap(moduleExports, 'parse');\n            }\n            this._wrap(moduleExports, 'parse', this._patchParse());\n            return moduleExports;\n        }, (moduleExports) => {\n            if (moduleExports) {\n                this._unwrap(moduleExports, 'parse');\n            }\n        });\n    }\n    _addPatchingValidate() {\n        return new instrumentation_1.InstrumentationNodeModuleFile('graphql/validation/validate.js', supportedVersions, moduleExports => {\n            if ((0, instrumentation_1.isWrapped)(moduleExports.validate)) {\n                this._unwrap(moduleExports, 'validate');\n            }\n            this._wrap(moduleExports, 'validate', this._patchValidate());\n            return moduleExports;\n        }, moduleExports => {\n            if (moduleExports) {\n                this._unwrap(moduleExports, 'validate');\n            }\n        });\n    }\n    _patchExecute(defaultFieldResolved) {\n        const instrumentation = this;\n        return function execute(original) {\n            return function patchExecute() {\n                let processedArgs;\n                // case when apollo server is used for example\n                if (arguments.length >= 2) {\n                    const args = arguments;\n                    processedArgs = instrumentation._wrapExecuteArgs(args[0], args[1], args[2], args[3], args[4], args[5], args[6], args[7], defaultFieldResolved);\n                }\n                else {\n                    const args = arguments[0];\n                    processedArgs = instrumentation._wrapExecuteArgs(args.schema, args.document, args.rootValue, args.contextValue, args.variableValues, args.operationName, args.fieldResolver, args.typeResolver, defaultFieldResolved);\n                }\n                const operation = (0, utils_1.getOperation)(processedArgs.document, processedArgs.operationName);\n                const span = instrumentation._createExecuteSpan(operation, processedArgs);\n                processedArgs.contextValue[symbols_1.OTEL_GRAPHQL_DATA_SYMBOL] = {\n                    source: processedArgs.document\n                        ? processedArgs.document ||\n                            processedArgs.document[symbols_1.OTEL_GRAPHQL_DATA_SYMBOL]\n                        : undefined,\n                    span,\n                    fields: {},\n                };\n                return api_1.context.with(api_1.trace.setSpan(api_1.context.active(), span), () => {\n                    return (0, instrumentation_1.safeExecuteInTheMiddle)(() => {\n                        return original.apply(this, [\n                            processedArgs,\n                        ]);\n                    }, (err, result) => {\n                        instrumentation._handleExecutionResult(span, err, result);\n                    });\n                });\n            };\n        };\n    }\n    _handleExecutionResult(span, err, result) {\n        const config = this.getConfig();\n        if (result === undefined || err) {\n            (0, utils_1.endSpan)(span, err);\n            return;\n        }\n        if ((0, utils_1.isPromise)(result)) {\n            result.then(resultData => {\n                if (typeof config.responseHook !== 'function') {\n                    (0, utils_1.endSpan)(span);\n                    return;\n                }\n                this._executeResponseHook(span, resultData);\n            }, error => {\n                (0, utils_1.endSpan)(span, error);\n            });\n        }\n        else {\n            if (typeof config.responseHook !== 'function') {\n                (0, utils_1.endSpan)(span);\n                return;\n            }\n            this._executeResponseHook(span, result);\n        }\n    }\n    _executeResponseHook(span, result) {\n        const { responseHook } = this.getConfig();\n        if (!responseHook) {\n            return;\n        }\n        (0, instrumentation_1.safeExecuteInTheMiddle)(() => {\n            responseHook(span, result);\n        }, err => {\n            if (err) {\n                this._diag.error('Error running response hook', err);\n            }\n            (0, utils_1.endSpan)(span, undefined);\n        }, true);\n    }\n    _patchParse() {\n        const instrumentation = this;\n        return function parse(original) {\n            return function patchParse(source, options) {\n                return instrumentation._parse(this, original, source, options);\n            };\n        };\n    }\n    _patchValidate() {\n        const instrumentation = this;\n        return function validate(original) {\n            return function patchValidate(schema, documentAST, rules, options, typeInfo) {\n                return instrumentation._validate(this, original, schema, documentAST, rules, typeInfo, options);\n            };\n        };\n    }\n    _parse(obj, original, source, options) {\n        const config = this.getConfig();\n        const span = this.tracer.startSpan(enum_1.SpanNames.PARSE);\n        return api_1.context.with(api_1.trace.setSpan(api_1.context.active(), span), () => {\n            return (0, instrumentation_1.safeExecuteInTheMiddle)(() => {\n                return original.call(obj, source, options);\n            }, (err, result) => {\n                if (result) {\n                    const operation = (0, utils_1.getOperation)(result);\n                    if (!operation) {\n                        span.updateName(enum_1.SpanNames.SCHEMA_PARSE);\n                    }\n                    else if (result.loc) {\n                        (0, utils_1.addSpanSource)(span, result.loc, config.allowValues);\n                    }\n                }\n                (0, utils_1.endSpan)(span, err);\n            });\n        });\n    }\n    _validate(obj, original, schema, documentAST, rules, typeInfo, options) {\n        const span = this.tracer.startSpan(enum_1.SpanNames.VALIDATE, {});\n        return api_1.context.with(api_1.trace.setSpan(api_1.context.active(), span), () => {\n            return (0, instrumentation_1.safeExecuteInTheMiddle)(() => {\n                return original.call(obj, schema, documentAST, rules, options, typeInfo);\n            }, (err, errors) => {\n                if (!documentAST.loc) {\n                    span.updateName(enum_1.SpanNames.SCHEMA_VALIDATE);\n                }\n                if (errors && errors.length) {\n                    span.recordException({\n                        name: AttributeNames_1.AttributeNames.ERROR_VALIDATION_NAME,\n                        message: JSON.stringify(errors),\n                    });\n                }\n                (0, utils_1.endSpan)(span, err);\n            });\n        });\n    }\n    _createExecuteSpan(operation, processedArgs) {\n        var _a;\n        const config = this.getConfig();\n        const span = this.tracer.startSpan(enum_1.SpanNames.EXECUTE, {});\n        if (operation) {\n            const { operation: operationType, name: nameNode } = operation;\n            span.setAttribute(AttributeNames_1.AttributeNames.OPERATION_TYPE, operationType);\n            const operationName = nameNode === null || nameNode === void 0 ? void 0 : nameNode.value;\n            // https://opentelemetry.io/docs/reference/specification/trace/semantic_conventions/instrumentation/graphql/\n            // > The span name MUST be of the format <graphql.operation.type> <graphql.operation.name> provided that graphql.operation.type and graphql.operation.name are available.\n            // > If graphql.operation.name is not available, the span SHOULD be named <graphql.operation.type>.\n            if (operationName) {\n                span.setAttribute(AttributeNames_1.AttributeNames.OPERATION_NAME, operationName);\n                span.updateName(`${operationType} ${operationName}`);\n            }\n            else {\n                span.updateName(operationType);\n            }\n        }\n        else {\n            let operationName = ' ';\n            if (processedArgs.operationName) {\n                operationName = ` \"${processedArgs.operationName}\" `;\n            }\n            operationName = internal_types_1.OPERATION_NOT_SUPPORTED.replace('$operationName$', operationName);\n            span.setAttribute(AttributeNames_1.AttributeNames.OPERATION_NAME, operationName);\n        }\n        if ((_a = processedArgs.document) === null || _a === void 0 ? void 0 : _a.loc) {\n            (0, utils_1.addSpanSource)(span, processedArgs.document.loc, config.allowValues);\n        }\n        if (processedArgs.variableValues && config.allowValues) {\n            (0, utils_1.addInputVariableAttributes)(span, processedArgs.variableValues);\n        }\n        return span;\n    }\n    _wrapExecuteArgs(schema, document, rootValue, contextValue, variableValues, operationName, fieldResolver, typeResolver, defaultFieldResolved) {\n        if (!contextValue) {\n            contextValue = {};\n        }\n        if (contextValue[symbols_1.OTEL_GRAPHQL_DATA_SYMBOL] ||\n            this.getConfig().ignoreResolveSpans) {\n            return {\n                schema,\n                document,\n                rootValue,\n                contextValue,\n                variableValues,\n                operationName,\n                fieldResolver,\n                typeResolver,\n            };\n        }\n        const isUsingDefaultResolver = fieldResolver == null;\n        // follows graphql implementation here:\n        // https://github.com/graphql/graphql-js/blob/0b7daed9811731362c71900e12e5ea0d1ecc7f1f/src/execution/execute.ts#L494\n        const fieldResolverForExecute = fieldResolver !== null && fieldResolver !== void 0 ? fieldResolver : defaultFieldResolved;\n        fieldResolver = (0, utils_1.wrapFieldResolver)(this.tracer, () => this.getConfig(), fieldResolverForExecute, isUsingDefaultResolver);\n        if (schema) {\n            (0, utils_1.wrapFields)(schema.getQueryType(), this.tracer, () => this.getConfig());\n            (0, utils_1.wrapFields)(schema.getMutationType(), this.tracer, () => this.getConfig());\n        }\n        return {\n            schema,\n            document,\n            rootValue,\n            contextValue,\n            variableValues,\n            operationName,\n            fieldResolver,\n            typeResolver,\n        };\n    }\n}\nexports.GraphQLInstrumentation = GraphQLInstrumentation;\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF83OGVhZTY1NjBkNDc1NjY3MmIwNWQxZmE2ZTBmZTc1ZS9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLWdyYXBocWwvYnVpbGQvc3JjL2luc3RydW1lbnRhdGlvbi5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCw4QkFBOEI7QUFDOUIsY0FBYyxtQkFBTyxDQUFDLHNJQUFvQjtBQUMxQywwQkFBMEIsbUJBQU8sQ0FBQyxrTUFBZ0M7QUFDbEUsZUFBZSxtQkFBTyxDQUFDLGlMQUFRO0FBQy9CLHlCQUF5QixtQkFBTyxDQUFDLGlOQUF3QjtBQUN6RCxrQkFBa0IsbUJBQU8sQ0FBQyx1TEFBVztBQUNyQyx5QkFBeUIsbUJBQU8sQ0FBQyxxTUFBa0I7QUFDbkQsZ0JBQWdCLG1CQUFPLENBQUMsbUxBQVM7QUFDakM7QUFDQSxrQkFBa0IsbUJBQU8sQ0FBQyx1TEFBVztBQUNyQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkJBQTJCO0FBQzNCLCtGQUErRjtBQUMvRjtBQUNBLHlCQUF5QjtBQUN6QixzREFBc0Q7QUFDdEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOEJBQThCO0FBQzlCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQjtBQUNyQjtBQUNBLHFCQUFxQjtBQUNyQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IsZUFBZTtBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2IsU0FBUztBQUNUO0FBQ0E7QUFDQSx3RUFBd0U7QUFDeEU7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQSxhQUFhO0FBQ2IsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUVBQXVFO0FBQ3ZFO0FBQ0Esb0JBQW9CLDJDQUEyQztBQUMvRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1DQUFtQyxlQUFlLEVBQUUsY0FBYztBQUNsRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUNBQXFDLDRCQUE0QjtBQUNqRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOEJBQThCO0FBQzlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFud2FyXFxEb2N1bWVudHNcXEN1YmVudHdlYlxcY3ViZW50XFxub2RlX21vZHVsZXNcXC5wbnBtXFxAb3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfNzhlYWU2NTYwZDQ3NTY2NzJiMDVkMWZhNmUwZmU3NWVcXG5vZGVfbW9kdWxlc1xcQG9wZW50ZWxlbWV0cnlcXGluc3RydW1lbnRhdGlvbi1ncmFwaHFsXFxidWlsZFxcc3JjXFxpbnN0cnVtZW50YXRpb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG4vKlxuICogQ29weXJpZ2h0IFRoZSBPcGVuVGVsZW1ldHJ5IEF1dGhvcnNcbiAqXG4gKiBMaWNlbnNlZCB1bmRlciB0aGUgQXBhY2hlIExpY2Vuc2UsIFZlcnNpb24gMi4wICh0aGUgXCJMaWNlbnNlXCIpO1xuICogeW91IG1heSBub3QgdXNlIHRoaXMgZmlsZSBleGNlcHQgaW4gY29tcGxpYW5jZSB3aXRoIHRoZSBMaWNlbnNlLlxuICogWW91IG1heSBvYnRhaW4gYSBjb3B5IG9mIHRoZSBMaWNlbnNlIGF0XG4gKlxuICogICAgICBodHRwczovL3d3dy5hcGFjaGUub3JnL2xpY2Vuc2VzL0xJQ0VOU0UtMi4wXG4gKlxuICogVW5sZXNzIHJlcXVpcmVkIGJ5IGFwcGxpY2FibGUgbGF3IG9yIGFncmVlZCB0byBpbiB3cml0aW5nLCBzb2Z0d2FyZVxuICogZGlzdHJpYnV0ZWQgdW5kZXIgdGhlIExpY2Vuc2UgaXMgZGlzdHJpYnV0ZWQgb24gYW4gXCJBUyBJU1wiIEJBU0lTLFxuICogV0lUSE9VVCBXQVJSQU5USUVTIE9SIENPTkRJVElPTlMgT0YgQU5ZIEtJTkQsIGVpdGhlciBleHByZXNzIG9yIGltcGxpZWQuXG4gKiBTZWUgdGhlIExpY2Vuc2UgZm9yIHRoZSBzcGVjaWZpYyBsYW5ndWFnZSBnb3Zlcm5pbmcgcGVybWlzc2lvbnMgYW5kXG4gKiBsaW1pdGF0aW9ucyB1bmRlciB0aGUgTGljZW5zZS5cbiAqL1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5HcmFwaFFMSW5zdHJ1bWVudGF0aW9uID0gdm9pZCAwO1xuY29uc3QgYXBpXzEgPSByZXF1aXJlKFwiQG9wZW50ZWxlbWV0cnkvYXBpXCIpO1xuY29uc3QgaW5zdHJ1bWVudGF0aW9uXzEgPSByZXF1aXJlKFwiQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uXCIpO1xuY29uc3QgZW51bV8xID0gcmVxdWlyZShcIi4vZW51bVwiKTtcbmNvbnN0IEF0dHJpYnV0ZU5hbWVzXzEgPSByZXF1aXJlKFwiLi9lbnVtcy9BdHRyaWJ1dGVOYW1lc1wiKTtcbmNvbnN0IHN5bWJvbHNfMSA9IHJlcXVpcmUoXCIuL3N5bWJvbHNcIik7XG5jb25zdCBpbnRlcm5hbF90eXBlc18xID0gcmVxdWlyZShcIi4vaW50ZXJuYWwtdHlwZXNcIik7XG5jb25zdCB1dGlsc18xID0gcmVxdWlyZShcIi4vdXRpbHNcIik7XG4vKiogQGtuaXBpZ25vcmUgKi9cbmNvbnN0IHZlcnNpb25fMSA9IHJlcXVpcmUoXCIuL3ZlcnNpb25cIik7XG5jb25zdCBERUZBVUxUX0NPTkZJRyA9IHtcbiAgICBtZXJnZUl0ZW1zOiBmYWxzZSxcbiAgICBkZXB0aDogLTEsXG4gICAgYWxsb3dWYWx1ZXM6IGZhbHNlLFxuICAgIGlnbm9yZVJlc29sdmVTcGFuczogZmFsc2UsXG59O1xuY29uc3Qgc3VwcG9ydGVkVmVyc2lvbnMgPSBbJz49MTQuMC4wIDwxNyddO1xuY2xhc3MgR3JhcGhRTEluc3RydW1lbnRhdGlvbiBleHRlbmRzIGluc3RydW1lbnRhdGlvbl8xLkluc3RydW1lbnRhdGlvbkJhc2Uge1xuICAgIGNvbnN0cnVjdG9yKGNvbmZpZyA9IHt9KSB7XG4gICAgICAgIHN1cGVyKHZlcnNpb25fMS5QQUNLQUdFX05BTUUsIHZlcnNpb25fMS5QQUNLQUdFX1ZFUlNJT04sIE9iamVjdC5hc3NpZ24oT2JqZWN0LmFzc2lnbih7fSwgREVGQVVMVF9DT05GSUcpLCBjb25maWcpKTtcbiAgICB9XG4gICAgc2V0Q29uZmlnKGNvbmZpZyA9IHt9KSB7XG4gICAgICAgIHN1cGVyLnNldENvbmZpZyhPYmplY3QuYXNzaWduKE9iamVjdC5hc3NpZ24oe30sIERFRkFVTFRfQ09ORklHKSwgY29uZmlnKSk7XG4gICAgfVxuICAgIGluaXQoKSB7XG4gICAgICAgIGNvbnN0IG1vZHVsZSA9IG5ldyBpbnN0cnVtZW50YXRpb25fMS5JbnN0cnVtZW50YXRpb25Ob2RlTW9kdWxlRGVmaW5pdGlvbignZ3JhcGhxbCcsIHN1cHBvcnRlZFZlcnNpb25zKTtcbiAgICAgICAgbW9kdWxlLmZpbGVzLnB1c2godGhpcy5fYWRkUGF0Y2hpbmdFeGVjdXRlKCkpO1xuICAgICAgICBtb2R1bGUuZmlsZXMucHVzaCh0aGlzLl9hZGRQYXRjaGluZ1BhcnNlcigpKTtcbiAgICAgICAgbW9kdWxlLmZpbGVzLnB1c2godGhpcy5fYWRkUGF0Y2hpbmdWYWxpZGF0ZSgpKTtcbiAgICAgICAgcmV0dXJuIG1vZHVsZTtcbiAgICB9XG4gICAgX2FkZFBhdGNoaW5nRXhlY3V0ZSgpIHtcbiAgICAgICAgcmV0dXJuIG5ldyBpbnN0cnVtZW50YXRpb25fMS5JbnN0cnVtZW50YXRpb25Ob2RlTW9kdWxlRmlsZSgnZ3JhcGhxbC9leGVjdXRpb24vZXhlY3V0ZS5qcycsIHN1cHBvcnRlZFZlcnNpb25zLCBcbiAgICAgICAgLy8gY2Fubm90IG1ha2UgaXQgd29yayB3aXRoIGFwcHJvcHJpYXRlIHR5cGUgYXMgZXhlY3V0ZSBmdW5jdGlvbiBoYXMgMlxuICAgICAgICAvL3R5cGVzIGFuZC9jYW5ub3QgaW1wb3J0IGZ1bmN0aW9uIGJ1dCBvbmx5IHR5cGVzXG4gICAgICAgIChtb2R1bGVFeHBvcnRzKSA9PiB7XG4gICAgICAgICAgICBpZiAoKDAsIGluc3RydW1lbnRhdGlvbl8xLmlzV3JhcHBlZCkobW9kdWxlRXhwb3J0cy5leGVjdXRlKSkge1xuICAgICAgICAgICAgICAgIHRoaXMuX3Vud3JhcChtb2R1bGVFeHBvcnRzLCAnZXhlY3V0ZScpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgdGhpcy5fd3JhcChtb2R1bGVFeHBvcnRzLCAnZXhlY3V0ZScsIHRoaXMuX3BhdGNoRXhlY3V0ZShtb2R1bGVFeHBvcnRzLmRlZmF1bHRGaWVsZFJlc29sdmVyKSk7XG4gICAgICAgICAgICByZXR1cm4gbW9kdWxlRXhwb3J0cztcbiAgICAgICAgfSwgbW9kdWxlRXhwb3J0cyA9PiB7XG4gICAgICAgICAgICBpZiAobW9kdWxlRXhwb3J0cykge1xuICAgICAgICAgICAgICAgIHRoaXMuX3Vud3JhcChtb2R1bGVFeHBvcnRzLCAnZXhlY3V0ZScpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9KTtcbiAgICB9XG4gICAgX2FkZFBhdGNoaW5nUGFyc2VyKCkge1xuICAgICAgICByZXR1cm4gbmV3IGluc3RydW1lbnRhdGlvbl8xLkluc3RydW1lbnRhdGlvbk5vZGVNb2R1bGVGaWxlKCdncmFwaHFsL2xhbmd1YWdlL3BhcnNlci5qcycsIHN1cHBvcnRlZFZlcnNpb25zLCAobW9kdWxlRXhwb3J0cykgPT4ge1xuICAgICAgICAgICAgaWYgKCgwLCBpbnN0cnVtZW50YXRpb25fMS5pc1dyYXBwZWQpKG1vZHVsZUV4cG9ydHMucGFyc2UpKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5fdW53cmFwKG1vZHVsZUV4cG9ydHMsICdwYXJzZScpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgdGhpcy5fd3JhcChtb2R1bGVFeHBvcnRzLCAncGFyc2UnLCB0aGlzLl9wYXRjaFBhcnNlKCkpO1xuICAgICAgICAgICAgcmV0dXJuIG1vZHVsZUV4cG9ydHM7XG4gICAgICAgIH0sIChtb2R1bGVFeHBvcnRzKSA9PiB7XG4gICAgICAgICAgICBpZiAobW9kdWxlRXhwb3J0cykge1xuICAgICAgICAgICAgICAgIHRoaXMuX3Vud3JhcChtb2R1bGVFeHBvcnRzLCAncGFyc2UnKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSk7XG4gICAgfVxuICAgIF9hZGRQYXRjaGluZ1ZhbGlkYXRlKCkge1xuICAgICAgICByZXR1cm4gbmV3IGluc3RydW1lbnRhdGlvbl8xLkluc3RydW1lbnRhdGlvbk5vZGVNb2R1bGVGaWxlKCdncmFwaHFsL3ZhbGlkYXRpb24vdmFsaWRhdGUuanMnLCBzdXBwb3J0ZWRWZXJzaW9ucywgbW9kdWxlRXhwb3J0cyA9PiB7XG4gICAgICAgICAgICBpZiAoKDAsIGluc3RydW1lbnRhdGlvbl8xLmlzV3JhcHBlZCkobW9kdWxlRXhwb3J0cy52YWxpZGF0ZSkpIHtcbiAgICAgICAgICAgICAgICB0aGlzLl91bndyYXAobW9kdWxlRXhwb3J0cywgJ3ZhbGlkYXRlJyk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICB0aGlzLl93cmFwKG1vZHVsZUV4cG9ydHMsICd2YWxpZGF0ZScsIHRoaXMuX3BhdGNoVmFsaWRhdGUoKSk7XG4gICAgICAgICAgICByZXR1cm4gbW9kdWxlRXhwb3J0cztcbiAgICAgICAgfSwgbW9kdWxlRXhwb3J0cyA9PiB7XG4gICAgICAgICAgICBpZiAobW9kdWxlRXhwb3J0cykge1xuICAgICAgICAgICAgICAgIHRoaXMuX3Vud3JhcChtb2R1bGVFeHBvcnRzLCAndmFsaWRhdGUnKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSk7XG4gICAgfVxuICAgIF9wYXRjaEV4ZWN1dGUoZGVmYXVsdEZpZWxkUmVzb2x2ZWQpIHtcbiAgICAgICAgY29uc3QgaW5zdHJ1bWVudGF0aW9uID0gdGhpcztcbiAgICAgICAgcmV0dXJuIGZ1bmN0aW9uIGV4ZWN1dGUob3JpZ2luYWwpIHtcbiAgICAgICAgICAgIHJldHVybiBmdW5jdGlvbiBwYXRjaEV4ZWN1dGUoKSB7XG4gICAgICAgICAgICAgICAgbGV0IHByb2Nlc3NlZEFyZ3M7XG4gICAgICAgICAgICAgICAgLy8gY2FzZSB3aGVuIGFwb2xsbyBzZXJ2ZXIgaXMgdXNlZCBmb3IgZXhhbXBsZVxuICAgICAgICAgICAgICAgIGlmIChhcmd1bWVudHMubGVuZ3RoID49IDIpIHtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgYXJncyA9IGFyZ3VtZW50cztcbiAgICAgICAgICAgICAgICAgICAgcHJvY2Vzc2VkQXJncyA9IGluc3RydW1lbnRhdGlvbi5fd3JhcEV4ZWN1dGVBcmdzKGFyZ3NbMF0sIGFyZ3NbMV0sIGFyZ3NbMl0sIGFyZ3NbM10sIGFyZ3NbNF0sIGFyZ3NbNV0sIGFyZ3NbNl0sIGFyZ3NbN10sIGRlZmF1bHRGaWVsZFJlc29sdmVkKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGFyZ3MgPSBhcmd1bWVudHNbMF07XG4gICAgICAgICAgICAgICAgICAgIHByb2Nlc3NlZEFyZ3MgPSBpbnN0cnVtZW50YXRpb24uX3dyYXBFeGVjdXRlQXJncyhhcmdzLnNjaGVtYSwgYXJncy5kb2N1bWVudCwgYXJncy5yb290VmFsdWUsIGFyZ3MuY29udGV4dFZhbHVlLCBhcmdzLnZhcmlhYmxlVmFsdWVzLCBhcmdzLm9wZXJhdGlvbk5hbWUsIGFyZ3MuZmllbGRSZXNvbHZlciwgYXJncy50eXBlUmVzb2x2ZXIsIGRlZmF1bHRGaWVsZFJlc29sdmVkKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY29uc3Qgb3BlcmF0aW9uID0gKDAsIHV0aWxzXzEuZ2V0T3BlcmF0aW9uKShwcm9jZXNzZWRBcmdzLmRvY3VtZW50LCBwcm9jZXNzZWRBcmdzLm9wZXJhdGlvbk5hbWUpO1xuICAgICAgICAgICAgICAgIGNvbnN0IHNwYW4gPSBpbnN0cnVtZW50YXRpb24uX2NyZWF0ZUV4ZWN1dGVTcGFuKG9wZXJhdGlvbiwgcHJvY2Vzc2VkQXJncyk7XG4gICAgICAgICAgICAgICAgcHJvY2Vzc2VkQXJncy5jb250ZXh0VmFsdWVbc3ltYm9sc18xLk9URUxfR1JBUEhRTF9EQVRBX1NZTUJPTF0gPSB7XG4gICAgICAgICAgICAgICAgICAgIHNvdXJjZTogcHJvY2Vzc2VkQXJncy5kb2N1bWVudFxuICAgICAgICAgICAgICAgICAgICAgICAgPyBwcm9jZXNzZWRBcmdzLmRvY3VtZW50IHx8XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcHJvY2Vzc2VkQXJncy5kb2N1bWVudFtzeW1ib2xzXzEuT1RFTF9HUkFQSFFMX0RBVEFfU1lNQk9MXVxuICAgICAgICAgICAgICAgICAgICAgICAgOiB1bmRlZmluZWQsXG4gICAgICAgICAgICAgICAgICAgIHNwYW4sXG4gICAgICAgICAgICAgICAgICAgIGZpZWxkczoge30sXG4gICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgICAgICByZXR1cm4gYXBpXzEuY29udGV4dC53aXRoKGFwaV8xLnRyYWNlLnNldFNwYW4oYXBpXzEuY29udGV4dC5hY3RpdmUoKSwgc3BhbiksICgpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuICgwLCBpbnN0cnVtZW50YXRpb25fMS5zYWZlRXhlY3V0ZUluVGhlTWlkZGxlKSgoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gb3JpZ2luYWwuYXBwbHkodGhpcywgW1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHByb2Nlc3NlZEFyZ3MsXG4gICAgICAgICAgICAgICAgICAgICAgICBdKTtcbiAgICAgICAgICAgICAgICAgICAgfSwgKGVyciwgcmVzdWx0KSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICBpbnN0cnVtZW50YXRpb24uX2hhbmRsZUV4ZWN1dGlvblJlc3VsdChzcGFuLCBlcnIsIHJlc3VsdCk7XG4gICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgfTtcbiAgICAgICAgfTtcbiAgICB9XG4gICAgX2hhbmRsZUV4ZWN1dGlvblJlc3VsdChzcGFuLCBlcnIsIHJlc3VsdCkge1xuICAgICAgICBjb25zdCBjb25maWcgPSB0aGlzLmdldENvbmZpZygpO1xuICAgICAgICBpZiAocmVzdWx0ID09PSB1bmRlZmluZWQgfHwgZXJyKSB7XG4gICAgICAgICAgICAoMCwgdXRpbHNfMS5lbmRTcGFuKShzcGFuLCBlcnIpO1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIGlmICgoMCwgdXRpbHNfMS5pc1Byb21pc2UpKHJlc3VsdCkpIHtcbiAgICAgICAgICAgIHJlc3VsdC50aGVuKHJlc3VsdERhdGEgPT4ge1xuICAgICAgICAgICAgICAgIGlmICh0eXBlb2YgY29uZmlnLnJlc3BvbnNlSG9vayAhPT0gJ2Z1bmN0aW9uJykge1xuICAgICAgICAgICAgICAgICAgICAoMCwgdXRpbHNfMS5lbmRTcGFuKShzcGFuKTtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB0aGlzLl9leGVjdXRlUmVzcG9uc2VIb29rKHNwYW4sIHJlc3VsdERhdGEpO1xuICAgICAgICAgICAgfSwgZXJyb3IgPT4ge1xuICAgICAgICAgICAgICAgICgwLCB1dGlsc18xLmVuZFNwYW4pKHNwYW4sIGVycm9yKTtcbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgaWYgKHR5cGVvZiBjb25maWcucmVzcG9uc2VIb29rICE9PSAnZnVuY3Rpb24nKSB7XG4gICAgICAgICAgICAgICAgKDAsIHV0aWxzXzEuZW5kU3Bhbikoc3Bhbik7XG4gICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgdGhpcy5fZXhlY3V0ZVJlc3BvbnNlSG9vayhzcGFuLCByZXN1bHQpO1xuICAgICAgICB9XG4gICAgfVxuICAgIF9leGVjdXRlUmVzcG9uc2VIb29rKHNwYW4sIHJlc3VsdCkge1xuICAgICAgICBjb25zdCB7IHJlc3BvbnNlSG9vayB9ID0gdGhpcy5nZXRDb25maWcoKTtcbiAgICAgICAgaWYgKCFyZXNwb25zZUhvb2spIHtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICAoMCwgaW5zdHJ1bWVudGF0aW9uXzEuc2FmZUV4ZWN1dGVJblRoZU1pZGRsZSkoKCkgPT4ge1xuICAgICAgICAgICAgcmVzcG9uc2VIb29rKHNwYW4sIHJlc3VsdCk7XG4gICAgICAgIH0sIGVyciA9PiB7XG4gICAgICAgICAgICBpZiAoZXJyKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5fZGlhZy5lcnJvcignRXJyb3IgcnVubmluZyByZXNwb25zZSBob29rJywgZXJyKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgICgwLCB1dGlsc18xLmVuZFNwYW4pKHNwYW4sIHVuZGVmaW5lZCk7XG4gICAgICAgIH0sIHRydWUpO1xuICAgIH1cbiAgICBfcGF0Y2hQYXJzZSgpIHtcbiAgICAgICAgY29uc3QgaW5zdHJ1bWVudGF0aW9uID0gdGhpcztcbiAgICAgICAgcmV0dXJuIGZ1bmN0aW9uIHBhcnNlKG9yaWdpbmFsKSB7XG4gICAgICAgICAgICByZXR1cm4gZnVuY3Rpb24gcGF0Y2hQYXJzZShzb3VyY2UsIG9wdGlvbnMpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gaW5zdHJ1bWVudGF0aW9uLl9wYXJzZSh0aGlzLCBvcmlnaW5hbCwgc291cmNlLCBvcHRpb25zKTtcbiAgICAgICAgICAgIH07XG4gICAgICAgIH07XG4gICAgfVxuICAgIF9wYXRjaFZhbGlkYXRlKCkge1xuICAgICAgICBjb25zdCBpbnN0cnVtZW50YXRpb24gPSB0aGlzO1xuICAgICAgICByZXR1cm4gZnVuY3Rpb24gdmFsaWRhdGUob3JpZ2luYWwpIHtcbiAgICAgICAgICAgIHJldHVybiBmdW5jdGlvbiBwYXRjaFZhbGlkYXRlKHNjaGVtYSwgZG9jdW1lbnRBU1QsIHJ1bGVzLCBvcHRpb25zLCB0eXBlSW5mbykge1xuICAgICAgICAgICAgICAgIHJldHVybiBpbnN0cnVtZW50YXRpb24uX3ZhbGlkYXRlKHRoaXMsIG9yaWdpbmFsLCBzY2hlbWEsIGRvY3VtZW50QVNULCBydWxlcywgdHlwZUluZm8sIG9wdGlvbnMpO1xuICAgICAgICAgICAgfTtcbiAgICAgICAgfTtcbiAgICB9XG4gICAgX3BhcnNlKG9iaiwgb3JpZ2luYWwsIHNvdXJjZSwgb3B0aW9ucykge1xuICAgICAgICBjb25zdCBjb25maWcgPSB0aGlzLmdldENvbmZpZygpO1xuICAgICAgICBjb25zdCBzcGFuID0gdGhpcy50cmFjZXIuc3RhcnRTcGFuKGVudW1fMS5TcGFuTmFtZXMuUEFSU0UpO1xuICAgICAgICByZXR1cm4gYXBpXzEuY29udGV4dC53aXRoKGFwaV8xLnRyYWNlLnNldFNwYW4oYXBpXzEuY29udGV4dC5hY3RpdmUoKSwgc3BhbiksICgpID0+IHtcbiAgICAgICAgICAgIHJldHVybiAoMCwgaW5zdHJ1bWVudGF0aW9uXzEuc2FmZUV4ZWN1dGVJblRoZU1pZGRsZSkoKCkgPT4ge1xuICAgICAgICAgICAgICAgIHJldHVybiBvcmlnaW5hbC5jYWxsKG9iaiwgc291cmNlLCBvcHRpb25zKTtcbiAgICAgICAgICAgIH0sIChlcnIsIHJlc3VsdCkgPT4ge1xuICAgICAgICAgICAgICAgIGlmIChyZXN1bHQpIHtcbiAgICAgICAgICAgICAgICAgICAgY29uc3Qgb3BlcmF0aW9uID0gKDAsIHV0aWxzXzEuZ2V0T3BlcmF0aW9uKShyZXN1bHQpO1xuICAgICAgICAgICAgICAgICAgICBpZiAoIW9wZXJhdGlvbikge1xuICAgICAgICAgICAgICAgICAgICAgICAgc3Bhbi51cGRhdGVOYW1lKGVudW1fMS5TcGFuTmFtZXMuU0NIRU1BX1BBUlNFKTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBlbHNlIGlmIChyZXN1bHQubG9jKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAoMCwgdXRpbHNfMS5hZGRTcGFuU291cmNlKShzcGFuLCByZXN1bHQubG9jLCBjb25maWcuYWxsb3dWYWx1ZXMpO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICgwLCB1dGlsc18xLmVuZFNwYW4pKHNwYW4sIGVycik7XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfSk7XG4gICAgfVxuICAgIF92YWxpZGF0ZShvYmosIG9yaWdpbmFsLCBzY2hlbWEsIGRvY3VtZW50QVNULCBydWxlcywgdHlwZUluZm8sIG9wdGlvbnMpIHtcbiAgICAgICAgY29uc3Qgc3BhbiA9IHRoaXMudHJhY2VyLnN0YXJ0U3BhbihlbnVtXzEuU3Bhbk5hbWVzLlZBTElEQVRFLCB7fSk7XG4gICAgICAgIHJldHVybiBhcGlfMS5jb250ZXh0LndpdGgoYXBpXzEudHJhY2Uuc2V0U3BhbihhcGlfMS5jb250ZXh0LmFjdGl2ZSgpLCBzcGFuKSwgKCkgPT4ge1xuICAgICAgICAgICAgcmV0dXJuICgwLCBpbnN0cnVtZW50YXRpb25fMS5zYWZlRXhlY3V0ZUluVGhlTWlkZGxlKSgoKSA9PiB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIG9yaWdpbmFsLmNhbGwob2JqLCBzY2hlbWEsIGRvY3VtZW50QVNULCBydWxlcywgb3B0aW9ucywgdHlwZUluZm8pO1xuICAgICAgICAgICAgfSwgKGVyciwgZXJyb3JzKSA9PiB7XG4gICAgICAgICAgICAgICAgaWYgKCFkb2N1bWVudEFTVC5sb2MpIHtcbiAgICAgICAgICAgICAgICAgICAgc3Bhbi51cGRhdGVOYW1lKGVudW1fMS5TcGFuTmFtZXMuU0NIRU1BX1ZBTElEQVRFKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgaWYgKGVycm9ycyAmJiBlcnJvcnMubGVuZ3RoKSB7XG4gICAgICAgICAgICAgICAgICAgIHNwYW4ucmVjb3JkRXhjZXB0aW9uKHtcbiAgICAgICAgICAgICAgICAgICAgICAgIG5hbWU6IEF0dHJpYnV0ZU5hbWVzXzEuQXR0cmlidXRlTmFtZXMuRVJST1JfVkFMSURBVElPTl9OQU1FLFxuICAgICAgICAgICAgICAgICAgICAgICAgbWVzc2FnZTogSlNPTi5zdHJpbmdpZnkoZXJyb3JzKSxcbiAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICgwLCB1dGlsc18xLmVuZFNwYW4pKHNwYW4sIGVycik7XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfSk7XG4gICAgfVxuICAgIF9jcmVhdGVFeGVjdXRlU3BhbihvcGVyYXRpb24sIHByb2Nlc3NlZEFyZ3MpIHtcbiAgICAgICAgdmFyIF9hO1xuICAgICAgICBjb25zdCBjb25maWcgPSB0aGlzLmdldENvbmZpZygpO1xuICAgICAgICBjb25zdCBzcGFuID0gdGhpcy50cmFjZXIuc3RhcnRTcGFuKGVudW1fMS5TcGFuTmFtZXMuRVhFQ1VURSwge30pO1xuICAgICAgICBpZiAob3BlcmF0aW9uKSB7XG4gICAgICAgICAgICBjb25zdCB7IG9wZXJhdGlvbjogb3BlcmF0aW9uVHlwZSwgbmFtZTogbmFtZU5vZGUgfSA9IG9wZXJhdGlvbjtcbiAgICAgICAgICAgIHNwYW4uc2V0QXR0cmlidXRlKEF0dHJpYnV0ZU5hbWVzXzEuQXR0cmlidXRlTmFtZXMuT1BFUkFUSU9OX1RZUEUsIG9wZXJhdGlvblR5cGUpO1xuICAgICAgICAgICAgY29uc3Qgb3BlcmF0aW9uTmFtZSA9IG5hbWVOb2RlID09PSBudWxsIHx8IG5hbWVOb2RlID09PSB2b2lkIDAgPyB2b2lkIDAgOiBuYW1lTm9kZS52YWx1ZTtcbiAgICAgICAgICAgIC8vIGh0dHBzOi8vb3BlbnRlbGVtZXRyeS5pby9kb2NzL3JlZmVyZW5jZS9zcGVjaWZpY2F0aW9uL3RyYWNlL3NlbWFudGljX2NvbnZlbnRpb25zL2luc3RydW1lbnRhdGlvbi9ncmFwaHFsL1xuICAgICAgICAgICAgLy8gPiBUaGUgc3BhbiBuYW1lIE1VU1QgYmUgb2YgdGhlIGZvcm1hdCA8Z3JhcGhxbC5vcGVyYXRpb24udHlwZT4gPGdyYXBocWwub3BlcmF0aW9uLm5hbWU+IHByb3ZpZGVkIHRoYXQgZ3JhcGhxbC5vcGVyYXRpb24udHlwZSBhbmQgZ3JhcGhxbC5vcGVyYXRpb24ubmFtZSBhcmUgYXZhaWxhYmxlLlxuICAgICAgICAgICAgLy8gPiBJZiBncmFwaHFsLm9wZXJhdGlvbi5uYW1lIGlzIG5vdCBhdmFpbGFibGUsIHRoZSBzcGFuIFNIT1VMRCBiZSBuYW1lZCA8Z3JhcGhxbC5vcGVyYXRpb24udHlwZT4uXG4gICAgICAgICAgICBpZiAob3BlcmF0aW9uTmFtZSkge1xuICAgICAgICAgICAgICAgIHNwYW4uc2V0QXR0cmlidXRlKEF0dHJpYnV0ZU5hbWVzXzEuQXR0cmlidXRlTmFtZXMuT1BFUkFUSU9OX05BTUUsIG9wZXJhdGlvbk5hbWUpO1xuICAgICAgICAgICAgICAgIHNwYW4udXBkYXRlTmFtZShgJHtvcGVyYXRpb25UeXBlfSAke29wZXJhdGlvbk5hbWV9YCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICBzcGFuLnVwZGF0ZU5hbWUob3BlcmF0aW9uVHlwZSk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICBsZXQgb3BlcmF0aW9uTmFtZSA9ICcgJztcbiAgICAgICAgICAgIGlmIChwcm9jZXNzZWRBcmdzLm9wZXJhdGlvbk5hbWUpIHtcbiAgICAgICAgICAgICAgICBvcGVyYXRpb25OYW1lID0gYCBcIiR7cHJvY2Vzc2VkQXJncy5vcGVyYXRpb25OYW1lfVwiIGA7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBvcGVyYXRpb25OYW1lID0gaW50ZXJuYWxfdHlwZXNfMS5PUEVSQVRJT05fTk9UX1NVUFBPUlRFRC5yZXBsYWNlKCckb3BlcmF0aW9uTmFtZSQnLCBvcGVyYXRpb25OYW1lKTtcbiAgICAgICAgICAgIHNwYW4uc2V0QXR0cmlidXRlKEF0dHJpYnV0ZU5hbWVzXzEuQXR0cmlidXRlTmFtZXMuT1BFUkFUSU9OX05BTUUsIG9wZXJhdGlvbk5hbWUpO1xuICAgICAgICB9XG4gICAgICAgIGlmICgoX2EgPSBwcm9jZXNzZWRBcmdzLmRvY3VtZW50KSA9PT0gbnVsbCB8fCBfYSA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2EubG9jKSB7XG4gICAgICAgICAgICAoMCwgdXRpbHNfMS5hZGRTcGFuU291cmNlKShzcGFuLCBwcm9jZXNzZWRBcmdzLmRvY3VtZW50LmxvYywgY29uZmlnLmFsbG93VmFsdWVzKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAocHJvY2Vzc2VkQXJncy52YXJpYWJsZVZhbHVlcyAmJiBjb25maWcuYWxsb3dWYWx1ZXMpIHtcbiAgICAgICAgICAgICgwLCB1dGlsc18xLmFkZElucHV0VmFyaWFibGVBdHRyaWJ1dGVzKShzcGFuLCBwcm9jZXNzZWRBcmdzLnZhcmlhYmxlVmFsdWVzKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gc3BhbjtcbiAgICB9XG4gICAgX3dyYXBFeGVjdXRlQXJncyhzY2hlbWEsIGRvY3VtZW50LCByb290VmFsdWUsIGNvbnRleHRWYWx1ZSwgdmFyaWFibGVWYWx1ZXMsIG9wZXJhdGlvbk5hbWUsIGZpZWxkUmVzb2x2ZXIsIHR5cGVSZXNvbHZlciwgZGVmYXVsdEZpZWxkUmVzb2x2ZWQpIHtcbiAgICAgICAgaWYgKCFjb250ZXh0VmFsdWUpIHtcbiAgICAgICAgICAgIGNvbnRleHRWYWx1ZSA9IHt9O1xuICAgICAgICB9XG4gICAgICAgIGlmIChjb250ZXh0VmFsdWVbc3ltYm9sc18xLk9URUxfR1JBUEhRTF9EQVRBX1NZTUJPTF0gfHxcbiAgICAgICAgICAgIHRoaXMuZ2V0Q29uZmlnKCkuaWdub3JlUmVzb2x2ZVNwYW5zKSB7XG4gICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgIHNjaGVtYSxcbiAgICAgICAgICAgICAgICBkb2N1bWVudCxcbiAgICAgICAgICAgICAgICByb290VmFsdWUsXG4gICAgICAgICAgICAgICAgY29udGV4dFZhbHVlLFxuICAgICAgICAgICAgICAgIHZhcmlhYmxlVmFsdWVzLFxuICAgICAgICAgICAgICAgIG9wZXJhdGlvbk5hbWUsXG4gICAgICAgICAgICAgICAgZmllbGRSZXNvbHZlcixcbiAgICAgICAgICAgICAgICB0eXBlUmVzb2x2ZXIsXG4gICAgICAgICAgICB9O1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGlzVXNpbmdEZWZhdWx0UmVzb2x2ZXIgPSBmaWVsZFJlc29sdmVyID09IG51bGw7XG4gICAgICAgIC8vIGZvbGxvd3MgZ3JhcGhxbCBpbXBsZW1lbnRhdGlvbiBoZXJlOlxuICAgICAgICAvLyBodHRwczovL2dpdGh1Yi5jb20vZ3JhcGhxbC9ncmFwaHFsLWpzL2Jsb2IvMGI3ZGFlZDk4MTE3MzEzNjJjNzE5MDBlMTJlNWVhMGQxZWNjN2YxZi9zcmMvZXhlY3V0aW9uL2V4ZWN1dGUudHMjTDQ5NFxuICAgICAgICBjb25zdCBmaWVsZFJlc29sdmVyRm9yRXhlY3V0ZSA9IGZpZWxkUmVzb2x2ZXIgIT09IG51bGwgJiYgZmllbGRSZXNvbHZlciAhPT0gdm9pZCAwID8gZmllbGRSZXNvbHZlciA6IGRlZmF1bHRGaWVsZFJlc29sdmVkO1xuICAgICAgICBmaWVsZFJlc29sdmVyID0gKDAsIHV0aWxzXzEud3JhcEZpZWxkUmVzb2x2ZXIpKHRoaXMudHJhY2VyLCAoKSA9PiB0aGlzLmdldENvbmZpZygpLCBmaWVsZFJlc29sdmVyRm9yRXhlY3V0ZSwgaXNVc2luZ0RlZmF1bHRSZXNvbHZlcik7XG4gICAgICAgIGlmIChzY2hlbWEpIHtcbiAgICAgICAgICAgICgwLCB1dGlsc18xLndyYXBGaWVsZHMpKHNjaGVtYS5nZXRRdWVyeVR5cGUoKSwgdGhpcy50cmFjZXIsICgpID0+IHRoaXMuZ2V0Q29uZmlnKCkpO1xuICAgICAgICAgICAgKDAsIHV0aWxzXzEud3JhcEZpZWxkcykoc2NoZW1hLmdldE11dGF0aW9uVHlwZSgpLCB0aGlzLnRyYWNlciwgKCkgPT4gdGhpcy5nZXRDb25maWcoKSk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIHNjaGVtYSxcbiAgICAgICAgICAgIGRvY3VtZW50LFxuICAgICAgICAgICAgcm9vdFZhbHVlLFxuICAgICAgICAgICAgY29udGV4dFZhbHVlLFxuICAgICAgICAgICAgdmFyaWFibGVWYWx1ZXMsXG4gICAgICAgICAgICBvcGVyYXRpb25OYW1lLFxuICAgICAgICAgICAgZmllbGRSZXNvbHZlcixcbiAgICAgICAgICAgIHR5cGVSZXNvbHZlcixcbiAgICAgICAgfTtcbiAgICB9XG59XG5leHBvcnRzLkdyYXBoUUxJbnN0cnVtZW50YXRpb24gPSBHcmFwaFFMSW5zdHJ1bWVudGF0aW9uO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5zdHJ1bWVudGF0aW9uLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/instrumentation.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/internal-types.js":
/*!*****************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/internal-types.js ***!
  \*****************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.OPERATION_NOT_SUPPORTED = void 0;\nconst symbols_1 = __webpack_require__(/*! ./symbols */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/symbols.js\");\nexports.OPERATION_NOT_SUPPORTED = 'Operation$operationName$not' + ' supported';\n//# sourceMappingURL=internal-types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/internal-types.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/symbols.js":
/*!**********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/symbols.js ***!
  \**********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.OTEL_GRAPHQL_DATA_SYMBOL = exports.OTEL_PATCHED_SYMBOL = void 0;\nexports.OTEL_PATCHED_SYMBOL = Symbol.for('opentelemetry.patched');\nexports.OTEL_GRAPHQL_DATA_SYMBOL = Symbol.for('opentelemetry.graphql_data');\n//# sourceMappingURL=symbols.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/symbols.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/types.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/types.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF83OGVhZTY1NjBkNDc1NjY3MmIwNWQxZmE2ZTBmZTc1ZS9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLWdyYXBocWwvYnVpbGQvc3JjL3R5cGVzLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdEIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFud2FyXFxEb2N1bWVudHNcXEN1YmVudHdlYlxcY3ViZW50XFxub2RlX21vZHVsZXNcXC5wbnBtXFxAb3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfNzhlYWU2NTYwZDQ3NTY2NzJiMDVkMWZhNmUwZmU3NWVcXG5vZGVfbW9kdWxlc1xcQG9wZW50ZWxlbWV0cnlcXGluc3RydW1lbnRhdGlvbi1ncmFwaHFsXFxidWlsZFxcc3JjXFx0eXBlcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbi8qXG4gKiBDb3B5cmlnaHQgVGhlIE9wZW5UZWxlbWV0cnkgQXV0aG9yc1xuICpcbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4gKiB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuXG4gKiBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbiAqXG4gKiAgICAgIGh0dHBzOi8vd3d3LmFwYWNoZS5vcmcvbGljZW5zZXMvTElDRU5TRS0yLjBcbiAqXG4gKiBVbmxlc3MgcmVxdWlyZWQgYnkgYXBwbGljYWJsZSBsYXcgb3IgYWdyZWVkIHRvIGluIHdyaXRpbmcsIHNvZnR3YXJlXG4gKiBkaXN0cmlidXRlZCB1bmRlciB0aGUgTGljZW5zZSBpcyBkaXN0cmlidXRlZCBvbiBhbiBcIkFTIElTXCIgQkFTSVMsXG4gKiBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgQ09ORElUSU9OUyBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC5cbiAqIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmRcbiAqIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuICovXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD10eXBlcy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/types.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/utils.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/utils.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.wrapFieldResolver = exports.wrapFields = exports.getSourceFromLocation = exports.getOperation = exports.endSpan = exports.addSpanSource = exports.addInputVariableAttributes = exports.isPromise = void 0;\nconst api = __webpack_require__(/*! @opentelemetry/api */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst enum_1 = __webpack_require__(/*! ./enum */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/enum.js\");\nconst AttributeNames_1 = __webpack_require__(/*! ./enums/AttributeNames */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/enums/AttributeNames.js\");\nconst symbols_1 = __webpack_require__(/*! ./symbols */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/symbols.js\");\nconst OPERATION_VALUES = Object.values(enum_1.AllowedOperationTypes);\n// https://github.com/graphql/graphql-js/blob/main/src/jsutils/isPromise.ts\nconst isPromise = (value) => {\n    return typeof (value === null || value === void 0 ? void 0 : value.then) === 'function';\n};\nexports.isPromise = isPromise;\n// https://github.com/graphql/graphql-js/blob/main/src/jsutils/isObjectLike.ts\nconst isObjectLike = (value) => {\n    return typeof value == 'object' && value !== null;\n};\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction addInputVariableAttribute(span, key, variable) {\n    if (Array.isArray(variable)) {\n        variable.forEach((value, idx) => {\n            addInputVariableAttribute(span, `${key}.${idx}`, value);\n        });\n    }\n    else if (variable instanceof Object) {\n        Object.entries(variable).forEach(([nestedKey, value]) => {\n            addInputVariableAttribute(span, `${key}.${nestedKey}`, value);\n        });\n    }\n    else {\n        span.setAttribute(`${AttributeNames_1.AttributeNames.VARIABLES}${String(key)}`, variable);\n    }\n}\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction addInputVariableAttributes(span, variableValues) {\n    Object.entries(variableValues).forEach(([key, value]) => {\n        addInputVariableAttribute(span, key, value);\n    });\n}\nexports.addInputVariableAttributes = addInputVariableAttributes;\nfunction addSpanSource(span, loc, allowValues, start, end) {\n    const source = getSourceFromLocation(loc, allowValues, start, end);\n    span.setAttribute(AttributeNames_1.AttributeNames.SOURCE, source);\n}\nexports.addSpanSource = addSpanSource;\nfunction createFieldIfNotExists(tracer, getConfig, contextValue, info, path) {\n    let field = getField(contextValue, path);\n    let spanAdded = false;\n    if (!field) {\n        spanAdded = true;\n        const parent = getParentField(contextValue, path);\n        field = {\n            parent,\n            span: createResolverSpan(tracer, getConfig, contextValue, info, path, parent.span),\n            error: null,\n        };\n        addField(contextValue, path, field);\n    }\n    return { spanAdded, field };\n}\nfunction createResolverSpan(tracer, getConfig, contextValue, info, path, parentSpan) {\n    var _a, _b;\n    const attributes = {\n        [AttributeNames_1.AttributeNames.FIELD_NAME]: info.fieldName,\n        [AttributeNames_1.AttributeNames.FIELD_PATH]: path.join('.'),\n        [AttributeNames_1.AttributeNames.FIELD_TYPE]: info.returnType.toString(),\n    };\n    const span = tracer.startSpan(`${enum_1.SpanNames.RESOLVE} ${attributes[AttributeNames_1.AttributeNames.FIELD_PATH]}`, {\n        attributes,\n    }, parentSpan ? api.trace.setSpan(api.context.active(), parentSpan) : undefined);\n    const document = contextValue[symbols_1.OTEL_GRAPHQL_DATA_SYMBOL].source;\n    const fieldNode = info.fieldNodes.find(fieldNode => fieldNode.kind === 'Field');\n    if (fieldNode) {\n        addSpanSource(span, document.loc, getConfig().allowValues, (_a = fieldNode.loc) === null || _a === void 0 ? void 0 : _a.start, (_b = fieldNode.loc) === null || _b === void 0 ? void 0 : _b.end);\n    }\n    return span;\n}\nfunction endSpan(span, error) {\n    if (error) {\n        span.recordException(error);\n    }\n    span.end();\n}\nexports.endSpan = endSpan;\nfunction getOperation(document, operationName) {\n    if (!document || !Array.isArray(document.definitions)) {\n        return undefined;\n    }\n    if (operationName) {\n        return document.definitions\n            .filter(definition => { var _a; return OPERATION_VALUES.indexOf((_a = definition) === null || _a === void 0 ? void 0 : _a.operation) !== -1; })\n            .find(definition => { var _a, _b; return operationName === ((_b = (_a = definition) === null || _a === void 0 ? void 0 : _a.name) === null || _b === void 0 ? void 0 : _b.value); });\n    }\n    else {\n        return document.definitions.find(definition => { var _a; return OPERATION_VALUES.indexOf((_a = definition) === null || _a === void 0 ? void 0 : _a.operation) !== -1; });\n    }\n}\nexports.getOperation = getOperation;\nfunction addField(contextValue, path, field) {\n    return (contextValue[symbols_1.OTEL_GRAPHQL_DATA_SYMBOL].fields[path.join('.')] =\n        field);\n}\nfunction getField(contextValue, path) {\n    return contextValue[symbols_1.OTEL_GRAPHQL_DATA_SYMBOL].fields[path.join('.')];\n}\nfunction getParentField(contextValue, path) {\n    for (let i = path.length - 1; i > 0; i--) {\n        const field = getField(contextValue, path.slice(0, i));\n        if (field) {\n            return field;\n        }\n    }\n    return {\n        span: contextValue[symbols_1.OTEL_GRAPHQL_DATA_SYMBOL].span,\n    };\n}\nfunction pathToArray(mergeItems, path) {\n    const flattened = [];\n    let curr = path;\n    while (curr) {\n        let key = curr.key;\n        if (mergeItems && typeof key === 'number') {\n            key = '*';\n        }\n        flattened.push(String(key));\n        curr = curr.prev;\n    }\n    return flattened.reverse();\n}\nfunction repeatBreak(i) {\n    return repeatChar('\\n', i);\n}\nfunction repeatSpace(i) {\n    return repeatChar(' ', i);\n}\nfunction repeatChar(char, to) {\n    let text = '';\n    for (let i = 0; i < to; i++) {\n        text += char;\n    }\n    return text;\n}\nconst KindsToBeRemoved = [\n    enum_1.TokenKind.FLOAT,\n    enum_1.TokenKind.STRING,\n    enum_1.TokenKind.INT,\n    enum_1.TokenKind.BLOCK_STRING,\n];\nfunction getSourceFromLocation(loc, allowValues = false, inputStart, inputEnd) {\n    var _a, _b;\n    let source = '';\n    if (loc === null || loc === void 0 ? void 0 : loc.startToken) {\n        const start = typeof inputStart === 'number' ? inputStart : loc.start;\n        const end = typeof inputEnd === 'number' ? inputEnd : loc.end;\n        let next = loc.startToken.next;\n        let previousLine = 1;\n        while (next) {\n            if (next.start < start) {\n                next = next.next;\n                previousLine = next === null || next === void 0 ? void 0 : next.line;\n                continue;\n            }\n            if (next.end > end) {\n                next = next.next;\n                previousLine = next === null || next === void 0 ? void 0 : next.line;\n                continue;\n            }\n            let value = next.value || next.kind;\n            let space = '';\n            if (!allowValues && KindsToBeRemoved.indexOf(next.kind) >= 0) {\n                // value = repeatChar('*', value.length);\n                value = '*';\n            }\n            if (next.kind === enum_1.TokenKind.STRING) {\n                value = `\"${value}\"`;\n            }\n            if (next.kind === enum_1.TokenKind.EOF) {\n                value = '';\n            }\n            if (next.line > previousLine) {\n                source += repeatBreak(next.line - previousLine);\n                previousLine = next.line;\n                space = repeatSpace(next.column - 1);\n            }\n            else {\n                if (next.line === ((_a = next.prev) === null || _a === void 0 ? void 0 : _a.line)) {\n                    space = repeatSpace(next.start - (((_b = next.prev) === null || _b === void 0 ? void 0 : _b.end) || 0));\n                }\n            }\n            source += space + value;\n            if (next) {\n                next = next.next;\n            }\n        }\n    }\n    return source;\n}\nexports.getSourceFromLocation = getSourceFromLocation;\nfunction wrapFields(type, tracer, getConfig) {\n    if (!type ||\n        typeof type.getFields !== 'function' ||\n        type[symbols_1.OTEL_PATCHED_SYMBOL]) {\n        return;\n    }\n    const fields = type.getFields();\n    type[symbols_1.OTEL_PATCHED_SYMBOL] = true;\n    Object.keys(fields).forEach(key => {\n        const field = fields[key];\n        if (!field) {\n            return;\n        }\n        if (field.resolve) {\n            field.resolve = wrapFieldResolver(tracer, getConfig, field.resolve);\n        }\n        if (field.type) {\n            let unwrappedType = field.type;\n            while (unwrappedType.ofType) {\n                unwrappedType = unwrappedType.ofType;\n            }\n            wrapFields(unwrappedType, tracer, getConfig);\n        }\n    });\n}\nexports.wrapFields = wrapFields;\nconst handleResolveSpanError = (resolveSpan, err, shouldEndSpan) => {\n    if (!shouldEndSpan) {\n        return;\n    }\n    resolveSpan.recordException(err);\n    resolveSpan.setStatus({\n        code: api.SpanStatusCode.ERROR,\n        message: err.message,\n    });\n    resolveSpan.end();\n};\nconst handleResolveSpanSuccess = (resolveSpan, shouldEndSpan) => {\n    if (!shouldEndSpan) {\n        return;\n    }\n    resolveSpan.end();\n};\nfunction wrapFieldResolver(tracer, getConfig, fieldResolver, isDefaultResolver = false) {\n    if (wrappedFieldResolver[symbols_1.OTEL_PATCHED_SYMBOL] ||\n        typeof fieldResolver !== 'function') {\n        return fieldResolver;\n    }\n    function wrappedFieldResolver(source, args, contextValue, info) {\n        if (!fieldResolver) {\n            return undefined;\n        }\n        const config = getConfig();\n        // follows what graphql is doing to decide if this is a trivial resolver\n        // for which we don't need to create a resolve span\n        if (config.ignoreTrivialResolveSpans &&\n            isDefaultResolver &&\n            (isObjectLike(source) || typeof source === 'function')) {\n            const property = source[info.fieldName];\n            // a function execution is not trivial and should be recorder.\n            // property which is not a function is just a value and we don't want a \"resolve\" span for it\n            if (typeof property !== 'function') {\n                return fieldResolver.call(this, source, args, contextValue, info);\n            }\n        }\n        if (!contextValue[symbols_1.OTEL_GRAPHQL_DATA_SYMBOL]) {\n            return fieldResolver.call(this, source, args, contextValue, info);\n        }\n        const path = pathToArray(config.mergeItems, info && info.path);\n        const depth = path.filter((item) => typeof item === 'string').length;\n        let field;\n        let shouldEndSpan = false;\n        if (config.depth >= 0 && config.depth < depth) {\n            field = getParentField(contextValue, path);\n        }\n        else {\n            const newField = createFieldIfNotExists(tracer, getConfig, contextValue, info, path);\n            field = newField.field;\n            shouldEndSpan = newField.spanAdded;\n        }\n        return api.context.with(api.trace.setSpan(api.context.active(), field.span), () => {\n            try {\n                const res = fieldResolver.call(this, source, args, contextValue, info);\n                if ((0, exports.isPromise)(res)) {\n                    return res.then((r) => {\n                        handleResolveSpanSuccess(field.span, shouldEndSpan);\n                        return r;\n                    }, (err) => {\n                        handleResolveSpanError(field.span, err, shouldEndSpan);\n                        throw err;\n                    });\n                }\n                else {\n                    handleResolveSpanSuccess(field.span, shouldEndSpan);\n                    return res;\n                }\n            }\n            catch (err) {\n                handleResolveSpanError(field.span, err, shouldEndSpan);\n                throw err;\n            }\n        });\n    }\n    wrappedFieldResolver[symbols_1.OTEL_PATCHED_SYMBOL] = true;\n    return wrappedFieldResolver;\n}\nexports.wrapFieldResolver = wrapFieldResolver;\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/utils.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/version.js":
/*!**********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/version.js ***!
  \**********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.47.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-graphql';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/version.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/enum.js":
/*!*******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/enum.js ***!
  \*******************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.SpanNames = exports.TokenKind = exports.AllowedOperationTypes = void 0;\nvar AllowedOperationTypes;\n(function (AllowedOperationTypes) {\n    AllowedOperationTypes[\"QUERY\"] = \"query\";\n    AllowedOperationTypes[\"MUTATION\"] = \"mutation\";\n    AllowedOperationTypes[\"SUBSCRIPTION\"] = \"subscription\";\n})(AllowedOperationTypes = exports.AllowedOperationTypes || (exports.AllowedOperationTypes = {}));\nvar TokenKind;\n(function (TokenKind) {\n    TokenKind[\"SOF\"] = \"<SOF>\";\n    TokenKind[\"EOF\"] = \"<EOF>\";\n    TokenKind[\"BANG\"] = \"!\";\n    TokenKind[\"DOLLAR\"] = \"$\";\n    TokenKind[\"AMP\"] = \"&\";\n    TokenKind[\"PAREN_L\"] = \"(\";\n    TokenKind[\"PAREN_R\"] = \")\";\n    TokenKind[\"SPREAD\"] = \"...\";\n    TokenKind[\"COLON\"] = \":\";\n    TokenKind[\"EQUALS\"] = \"=\";\n    TokenKind[\"AT\"] = \"@\";\n    TokenKind[\"BRACKET_L\"] = \"[\";\n    TokenKind[\"BRACKET_R\"] = \"]\";\n    TokenKind[\"BRACE_L\"] = \"{\";\n    TokenKind[\"PIPE\"] = \"|\";\n    TokenKind[\"BRACE_R\"] = \"}\";\n    TokenKind[\"NAME\"] = \"Name\";\n    TokenKind[\"INT\"] = \"Int\";\n    TokenKind[\"FLOAT\"] = \"Float\";\n    TokenKind[\"STRING\"] = \"String\";\n    TokenKind[\"BLOCK_STRING\"] = \"BlockString\";\n    TokenKind[\"COMMENT\"] = \"Comment\";\n})(TokenKind = exports.TokenKind || (exports.TokenKind = {}));\nvar SpanNames;\n(function (SpanNames) {\n    SpanNames[\"EXECUTE\"] = \"graphql.execute\";\n    SpanNames[\"PARSE\"] = \"graphql.parse\";\n    SpanNames[\"RESOLVE\"] = \"graphql.resolve\";\n    SpanNames[\"VALIDATE\"] = \"graphql.validate\";\n    SpanNames[\"SCHEMA_VALIDATE\"] = \"graphql.validateSchema\";\n    SpanNames[\"SCHEMA_PARSE\"] = \"graphql.parseSchema\";\n})(SpanNames = exports.SpanNames || (exports.SpanNames = {}));\n//# sourceMappingURL=enum.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/enum.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/enums/AttributeNames.js":
/*!***********************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/enums/AttributeNames.js ***!
  \***********************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.AttributeNames = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar AttributeNames;\n(function (AttributeNames) {\n    AttributeNames[\"SOURCE\"] = \"graphql.source\";\n    AttributeNames[\"FIELD_NAME\"] = \"graphql.field.name\";\n    AttributeNames[\"FIELD_PATH\"] = \"graphql.field.path\";\n    AttributeNames[\"FIELD_TYPE\"] = \"graphql.field.type\";\n    AttributeNames[\"OPERATION_TYPE\"] = \"graphql.operation.type\";\n    AttributeNames[\"OPERATION_NAME\"] = \"graphql.operation.name\";\n    AttributeNames[\"VARIABLES\"] = \"graphql.variables.\";\n    AttributeNames[\"ERROR_VALIDATION_NAME\"] = \"graphql.validation.error\";\n})(AttributeNames = exports.AttributeNames || (exports.AttributeNames = {}));\n//# sourceMappingURL=AttributeNames.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/enums/AttributeNames.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/index.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/index.js ***!
  \********************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/instrumentation.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/types.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/instrumentation.js":
/*!******************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/instrumentation.js ***!
  \******************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.GraphQLInstrumentation = void 0;\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst enum_1 = __webpack_require__(/*! ./enum */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/enum.js\");\nconst AttributeNames_1 = __webpack_require__(/*! ./enums/AttributeNames */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/enums/AttributeNames.js\");\nconst symbols_1 = __webpack_require__(/*! ./symbols */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/symbols.js\");\nconst internal_types_1 = __webpack_require__(/*! ./internal-types */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/internal-types.js\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/utils.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/version.js\");\nconst DEFAULT_CONFIG = {\n    mergeItems: false,\n    depth: -1,\n    allowValues: false,\n    ignoreResolveSpans: false,\n};\nconst supportedVersions = ['>=14.0.0 <17'];\nclass GraphQLInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, Object.assign(Object.assign({}, DEFAULT_CONFIG), config));\n    }\n    setConfig(config = {}) {\n        super.setConfig(Object.assign(Object.assign({}, DEFAULT_CONFIG), config));\n    }\n    init() {\n        const module = new instrumentation_1.InstrumentationNodeModuleDefinition('graphql', supportedVersions);\n        module.files.push(this._addPatchingExecute());\n        module.files.push(this._addPatchingParser());\n        module.files.push(this._addPatchingValidate());\n        return module;\n    }\n    _addPatchingExecute() {\n        return new instrumentation_1.InstrumentationNodeModuleFile('graphql/execution/execute.js', supportedVersions, \n        // cannot make it work with appropriate type as execute function has 2\n        //types and/cannot import function but only types\n        (moduleExports) => {\n            if ((0, instrumentation_1.isWrapped)(moduleExports.execute)) {\n                this._unwrap(moduleExports, 'execute');\n            }\n            this._wrap(moduleExports, 'execute', this._patchExecute(moduleExports.defaultFieldResolver));\n            return moduleExports;\n        }, moduleExports => {\n            if (moduleExports) {\n                this._unwrap(moduleExports, 'execute');\n            }\n        });\n    }\n    _addPatchingParser() {\n        return new instrumentation_1.InstrumentationNodeModuleFile('graphql/language/parser.js', supportedVersions, (moduleExports) => {\n            if ((0, instrumentation_1.isWrapped)(moduleExports.parse)) {\n                this._unwrap(moduleExports, 'parse');\n            }\n            this._wrap(moduleExports, 'parse', this._patchParse());\n            return moduleExports;\n        }, (moduleExports) => {\n            if (moduleExports) {\n                this._unwrap(moduleExports, 'parse');\n            }\n        });\n    }\n    _addPatchingValidate() {\n        return new instrumentation_1.InstrumentationNodeModuleFile('graphql/validation/validate.js', supportedVersions, moduleExports => {\n            if ((0, instrumentation_1.isWrapped)(moduleExports.validate)) {\n                this._unwrap(moduleExports, 'validate');\n            }\n            this._wrap(moduleExports, 'validate', this._patchValidate());\n            return moduleExports;\n        }, moduleExports => {\n            if (moduleExports) {\n                this._unwrap(moduleExports, 'validate');\n            }\n        });\n    }\n    _patchExecute(defaultFieldResolved) {\n        const instrumentation = this;\n        return function execute(original) {\n            return function patchExecute() {\n                let processedArgs;\n                // case when apollo server is used for example\n                if (arguments.length >= 2) {\n                    const args = arguments;\n                    processedArgs = instrumentation._wrapExecuteArgs(args[0], args[1], args[2], args[3], args[4], args[5], args[6], args[7], defaultFieldResolved);\n                }\n                else {\n                    const args = arguments[0];\n                    processedArgs = instrumentation._wrapExecuteArgs(args.schema, args.document, args.rootValue, args.contextValue, args.variableValues, args.operationName, args.fieldResolver, args.typeResolver, defaultFieldResolved);\n                }\n                const operation = (0, utils_1.getOperation)(processedArgs.document, processedArgs.operationName);\n                const span = instrumentation._createExecuteSpan(operation, processedArgs);\n                processedArgs.contextValue[symbols_1.OTEL_GRAPHQL_DATA_SYMBOL] = {\n                    source: processedArgs.document\n                        ? processedArgs.document ||\n                            processedArgs.document[symbols_1.OTEL_GRAPHQL_DATA_SYMBOL]\n                        : undefined,\n                    span,\n                    fields: {},\n                };\n                return api_1.context.with(api_1.trace.setSpan(api_1.context.active(), span), () => {\n                    return (0, instrumentation_1.safeExecuteInTheMiddle)(() => {\n                        return original.apply(this, [\n                            processedArgs,\n                        ]);\n                    }, (err, result) => {\n                        instrumentation._handleExecutionResult(span, err, result);\n                    });\n                });\n            };\n        };\n    }\n    _handleExecutionResult(span, err, result) {\n        const config = this.getConfig();\n        if (result === undefined || err) {\n            (0, utils_1.endSpan)(span, err);\n            return;\n        }\n        if ((0, utils_1.isPromise)(result)) {\n            result.then(resultData => {\n                if (typeof config.responseHook !== 'function') {\n                    (0, utils_1.endSpan)(span);\n                    return;\n                }\n                this._executeResponseHook(span, resultData);\n            }, error => {\n                (0, utils_1.endSpan)(span, error);\n            });\n        }\n        else {\n            if (typeof config.responseHook !== 'function') {\n                (0, utils_1.endSpan)(span);\n                return;\n            }\n            this._executeResponseHook(span, result);\n        }\n    }\n    _executeResponseHook(span, result) {\n        const { responseHook } = this.getConfig();\n        if (!responseHook) {\n            return;\n        }\n        (0, instrumentation_1.safeExecuteInTheMiddle)(() => {\n            responseHook(span, result);\n        }, err => {\n            if (err) {\n                this._diag.error('Error running response hook', err);\n            }\n            (0, utils_1.endSpan)(span, undefined);\n        }, true);\n    }\n    _patchParse() {\n        const instrumentation = this;\n        return function parse(original) {\n            return function patchParse(source, options) {\n                return instrumentation._parse(this, original, source, options);\n            };\n        };\n    }\n    _patchValidate() {\n        const instrumentation = this;\n        return function validate(original) {\n            return function patchValidate(schema, documentAST, rules, options, typeInfo) {\n                return instrumentation._validate(this, original, schema, documentAST, rules, typeInfo, options);\n            };\n        };\n    }\n    _parse(obj, original, source, options) {\n        const config = this.getConfig();\n        const span = this.tracer.startSpan(enum_1.SpanNames.PARSE);\n        return api_1.context.with(api_1.trace.setSpan(api_1.context.active(), span), () => {\n            return (0, instrumentation_1.safeExecuteInTheMiddle)(() => {\n                return original.call(obj, source, options);\n            }, (err, result) => {\n                if (result) {\n                    const operation = (0, utils_1.getOperation)(result);\n                    if (!operation) {\n                        span.updateName(enum_1.SpanNames.SCHEMA_PARSE);\n                    }\n                    else if (result.loc) {\n                        (0, utils_1.addSpanSource)(span, result.loc, config.allowValues);\n                    }\n                }\n                (0, utils_1.endSpan)(span, err);\n            });\n        });\n    }\n    _validate(obj, original, schema, documentAST, rules, typeInfo, options) {\n        const span = this.tracer.startSpan(enum_1.SpanNames.VALIDATE, {});\n        return api_1.context.with(api_1.trace.setSpan(api_1.context.active(), span), () => {\n            return (0, instrumentation_1.safeExecuteInTheMiddle)(() => {\n                return original.call(obj, schema, documentAST, rules, options, typeInfo);\n            }, (err, errors) => {\n                if (!documentAST.loc) {\n                    span.updateName(enum_1.SpanNames.SCHEMA_VALIDATE);\n                }\n                if (errors && errors.length) {\n                    span.recordException({\n                        name: AttributeNames_1.AttributeNames.ERROR_VALIDATION_NAME,\n                        message: JSON.stringify(errors),\n                    });\n                }\n                (0, utils_1.endSpan)(span, err);\n            });\n        });\n    }\n    _createExecuteSpan(operation, processedArgs) {\n        var _a;\n        const config = this.getConfig();\n        const span = this.tracer.startSpan(enum_1.SpanNames.EXECUTE, {});\n        if (operation) {\n            const { operation: operationType, name: nameNode } = operation;\n            span.setAttribute(AttributeNames_1.AttributeNames.OPERATION_TYPE, operationType);\n            const operationName = nameNode === null || nameNode === void 0 ? void 0 : nameNode.value;\n            // https://opentelemetry.io/docs/reference/specification/trace/semantic_conventions/instrumentation/graphql/\n            // > The span name MUST be of the format <graphql.operation.type> <graphql.operation.name> provided that graphql.operation.type and graphql.operation.name are available.\n            // > If graphql.operation.name is not available, the span SHOULD be named <graphql.operation.type>.\n            if (operationName) {\n                span.setAttribute(AttributeNames_1.AttributeNames.OPERATION_NAME, operationName);\n                span.updateName(`${operationType} ${operationName}`);\n            }\n            else {\n                span.updateName(operationType);\n            }\n        }\n        else {\n            let operationName = ' ';\n            if (processedArgs.operationName) {\n                operationName = ` \"${processedArgs.operationName}\" `;\n            }\n            operationName = internal_types_1.OPERATION_NOT_SUPPORTED.replace('$operationName$', operationName);\n            span.setAttribute(AttributeNames_1.AttributeNames.OPERATION_NAME, operationName);\n        }\n        if ((_a = processedArgs.document) === null || _a === void 0 ? void 0 : _a.loc) {\n            (0, utils_1.addSpanSource)(span, processedArgs.document.loc, config.allowValues);\n        }\n        if (processedArgs.variableValues && config.allowValues) {\n            (0, utils_1.addInputVariableAttributes)(span, processedArgs.variableValues);\n        }\n        return span;\n    }\n    _wrapExecuteArgs(schema, document, rootValue, contextValue, variableValues, operationName, fieldResolver, typeResolver, defaultFieldResolved) {\n        if (!contextValue) {\n            contextValue = {};\n        }\n        if (contextValue[symbols_1.OTEL_GRAPHQL_DATA_SYMBOL] ||\n            this.getConfig().ignoreResolveSpans) {\n            return {\n                schema,\n                document,\n                rootValue,\n                contextValue,\n                variableValues,\n                operationName,\n                fieldResolver,\n                typeResolver,\n            };\n        }\n        const isUsingDefaultResolver = fieldResolver == null;\n        // follows graphql implementation here:\n        // https://github.com/graphql/graphql-js/blob/0b7daed9811731362c71900e12e5ea0d1ecc7f1f/src/execution/execute.ts#L494\n        const fieldResolverForExecute = fieldResolver !== null && fieldResolver !== void 0 ? fieldResolver : defaultFieldResolved;\n        fieldResolver = (0, utils_1.wrapFieldResolver)(this.tracer, () => this.getConfig(), fieldResolverForExecute, isUsingDefaultResolver);\n        if (schema) {\n            (0, utils_1.wrapFields)(schema.getQueryType(), this.tracer, () => this.getConfig());\n            (0, utils_1.wrapFields)(schema.getMutationType(), this.tracer, () => this.getConfig());\n        }\n        return {\n            schema,\n            document,\n            rootValue,\n            contextValue,\n            variableValues,\n            operationName,\n            fieldResolver,\n            typeResolver,\n        };\n    }\n}\nexports.GraphQLInstrumentation = GraphQLInstrumentation;\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/instrumentation.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/internal-types.js":
/*!*****************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/internal-types.js ***!
  \*****************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.OPERATION_NOT_SUPPORTED = void 0;\nconst symbols_1 = __webpack_require__(/*! ./symbols */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/symbols.js\");\nexports.OPERATION_NOT_SUPPORTED = 'Operation$operationName$not' + ' supported';\n//# sourceMappingURL=internal-types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/internal-types.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/symbols.js":
/*!**********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/symbols.js ***!
  \**********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.OTEL_GRAPHQL_DATA_SYMBOL = exports.OTEL_PATCHED_SYMBOL = void 0;\nexports.OTEL_PATCHED_SYMBOL = Symbol.for('opentelemetry.patched');\nexports.OTEL_GRAPHQL_DATA_SYMBOL = Symbol.for('opentelemetry.graphql_data');\n//# sourceMappingURL=symbols.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/symbols.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/types.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/types.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF83OGVhZTY1NjBkNDc1NjY3MmIwNWQxZmE2ZTBmZTc1ZS9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLWdyYXBocWwvYnVpbGQvc3JjL3R5cGVzLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdEIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFud2FyXFxEb2N1bWVudHNcXEN1YmVudHdlYlxcY3ViZW50XFxub2RlX21vZHVsZXNcXC5wbnBtXFxAb3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfNzhlYWU2NTYwZDQ3NTY2NzJiMDVkMWZhNmUwZmU3NWVcXG5vZGVfbW9kdWxlc1xcQG9wZW50ZWxlbWV0cnlcXGluc3RydW1lbnRhdGlvbi1ncmFwaHFsXFxidWlsZFxcc3JjXFx0eXBlcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbi8qXG4gKiBDb3B5cmlnaHQgVGhlIE9wZW5UZWxlbWV0cnkgQXV0aG9yc1xuICpcbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4gKiB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuXG4gKiBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbiAqXG4gKiAgICAgIGh0dHBzOi8vd3d3LmFwYWNoZS5vcmcvbGljZW5zZXMvTElDRU5TRS0yLjBcbiAqXG4gKiBVbmxlc3MgcmVxdWlyZWQgYnkgYXBwbGljYWJsZSBsYXcgb3IgYWdyZWVkIHRvIGluIHdyaXRpbmcsIHNvZnR3YXJlXG4gKiBkaXN0cmlidXRlZCB1bmRlciB0aGUgTGljZW5zZSBpcyBkaXN0cmlidXRlZCBvbiBhbiBcIkFTIElTXCIgQkFTSVMsXG4gKiBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgQ09ORElUSU9OUyBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC5cbiAqIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmRcbiAqIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuICovXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD10eXBlcy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/types.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/utils.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/utils.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.wrapFieldResolver = exports.wrapFields = exports.getSourceFromLocation = exports.getOperation = exports.endSpan = exports.addSpanSource = exports.addInputVariableAttributes = exports.isPromise = void 0;\nconst api = __webpack_require__(/*! @opentelemetry/api */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst enum_1 = __webpack_require__(/*! ./enum */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/enum.js\");\nconst AttributeNames_1 = __webpack_require__(/*! ./enums/AttributeNames */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/enums/AttributeNames.js\");\nconst symbols_1 = __webpack_require__(/*! ./symbols */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/symbols.js\");\nconst OPERATION_VALUES = Object.values(enum_1.AllowedOperationTypes);\n// https://github.com/graphql/graphql-js/blob/main/src/jsutils/isPromise.ts\nconst isPromise = (value) => {\n    return typeof (value === null || value === void 0 ? void 0 : value.then) === 'function';\n};\nexports.isPromise = isPromise;\n// https://github.com/graphql/graphql-js/blob/main/src/jsutils/isObjectLike.ts\nconst isObjectLike = (value) => {\n    return typeof value == 'object' && value !== null;\n};\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction addInputVariableAttribute(span, key, variable) {\n    if (Array.isArray(variable)) {\n        variable.forEach((value, idx) => {\n            addInputVariableAttribute(span, `${key}.${idx}`, value);\n        });\n    }\n    else if (variable instanceof Object) {\n        Object.entries(variable).forEach(([nestedKey, value]) => {\n            addInputVariableAttribute(span, `${key}.${nestedKey}`, value);\n        });\n    }\n    else {\n        span.setAttribute(`${AttributeNames_1.AttributeNames.VARIABLES}${String(key)}`, variable);\n    }\n}\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction addInputVariableAttributes(span, variableValues) {\n    Object.entries(variableValues).forEach(([key, value]) => {\n        addInputVariableAttribute(span, key, value);\n    });\n}\nexports.addInputVariableAttributes = addInputVariableAttributes;\nfunction addSpanSource(span, loc, allowValues, start, end) {\n    const source = getSourceFromLocation(loc, allowValues, start, end);\n    span.setAttribute(AttributeNames_1.AttributeNames.SOURCE, source);\n}\nexports.addSpanSource = addSpanSource;\nfunction createFieldIfNotExists(tracer, getConfig, contextValue, info, path) {\n    let field = getField(contextValue, path);\n    let spanAdded = false;\n    if (!field) {\n        spanAdded = true;\n        const parent = getParentField(contextValue, path);\n        field = {\n            parent,\n            span: createResolverSpan(tracer, getConfig, contextValue, info, path, parent.span),\n            error: null,\n        };\n        addField(contextValue, path, field);\n    }\n    return { spanAdded, field };\n}\nfunction createResolverSpan(tracer, getConfig, contextValue, info, path, parentSpan) {\n    var _a, _b;\n    const attributes = {\n        [AttributeNames_1.AttributeNames.FIELD_NAME]: info.fieldName,\n        [AttributeNames_1.AttributeNames.FIELD_PATH]: path.join('.'),\n        [AttributeNames_1.AttributeNames.FIELD_TYPE]: info.returnType.toString(),\n    };\n    const span = tracer.startSpan(`${enum_1.SpanNames.RESOLVE} ${attributes[AttributeNames_1.AttributeNames.FIELD_PATH]}`, {\n        attributes,\n    }, parentSpan ? api.trace.setSpan(api.context.active(), parentSpan) : undefined);\n    const document = contextValue[symbols_1.OTEL_GRAPHQL_DATA_SYMBOL].source;\n    const fieldNode = info.fieldNodes.find(fieldNode => fieldNode.kind === 'Field');\n    if (fieldNode) {\n        addSpanSource(span, document.loc, getConfig().allowValues, (_a = fieldNode.loc) === null || _a === void 0 ? void 0 : _a.start, (_b = fieldNode.loc) === null || _b === void 0 ? void 0 : _b.end);\n    }\n    return span;\n}\nfunction endSpan(span, error) {\n    if (error) {\n        span.recordException(error);\n    }\n    span.end();\n}\nexports.endSpan = endSpan;\nfunction getOperation(document, operationName) {\n    if (!document || !Array.isArray(document.definitions)) {\n        return undefined;\n    }\n    if (operationName) {\n        return document.definitions\n            .filter(definition => { var _a; return OPERATION_VALUES.indexOf((_a = definition) === null || _a === void 0 ? void 0 : _a.operation) !== -1; })\n            .find(definition => { var _a, _b; return operationName === ((_b = (_a = definition) === null || _a === void 0 ? void 0 : _a.name) === null || _b === void 0 ? void 0 : _b.value); });\n    }\n    else {\n        return document.definitions.find(definition => { var _a; return OPERATION_VALUES.indexOf((_a = definition) === null || _a === void 0 ? void 0 : _a.operation) !== -1; });\n    }\n}\nexports.getOperation = getOperation;\nfunction addField(contextValue, path, field) {\n    return (contextValue[symbols_1.OTEL_GRAPHQL_DATA_SYMBOL].fields[path.join('.')] =\n        field);\n}\nfunction getField(contextValue, path) {\n    return contextValue[symbols_1.OTEL_GRAPHQL_DATA_SYMBOL].fields[path.join('.')];\n}\nfunction getParentField(contextValue, path) {\n    for (let i = path.length - 1; i > 0; i--) {\n        const field = getField(contextValue, path.slice(0, i));\n        if (field) {\n            return field;\n        }\n    }\n    return {\n        span: contextValue[symbols_1.OTEL_GRAPHQL_DATA_SYMBOL].span,\n    };\n}\nfunction pathToArray(mergeItems, path) {\n    const flattened = [];\n    let curr = path;\n    while (curr) {\n        let key = curr.key;\n        if (mergeItems && typeof key === 'number') {\n            key = '*';\n        }\n        flattened.push(String(key));\n        curr = curr.prev;\n    }\n    return flattened.reverse();\n}\nfunction repeatBreak(i) {\n    return repeatChar('\\n', i);\n}\nfunction repeatSpace(i) {\n    return repeatChar(' ', i);\n}\nfunction repeatChar(char, to) {\n    let text = '';\n    for (let i = 0; i < to; i++) {\n        text += char;\n    }\n    return text;\n}\nconst KindsToBeRemoved = [\n    enum_1.TokenKind.FLOAT,\n    enum_1.TokenKind.STRING,\n    enum_1.TokenKind.INT,\n    enum_1.TokenKind.BLOCK_STRING,\n];\nfunction getSourceFromLocation(loc, allowValues = false, inputStart, inputEnd) {\n    var _a, _b;\n    let source = '';\n    if (loc === null || loc === void 0 ? void 0 : loc.startToken) {\n        const start = typeof inputStart === 'number' ? inputStart : loc.start;\n        const end = typeof inputEnd === 'number' ? inputEnd : loc.end;\n        let next = loc.startToken.next;\n        let previousLine = 1;\n        while (next) {\n            if (next.start < start) {\n                next = next.next;\n                previousLine = next === null || next === void 0 ? void 0 : next.line;\n                continue;\n            }\n            if (next.end > end) {\n                next = next.next;\n                previousLine = next === null || next === void 0 ? void 0 : next.line;\n                continue;\n            }\n            let value = next.value || next.kind;\n            let space = '';\n            if (!allowValues && KindsToBeRemoved.indexOf(next.kind) >= 0) {\n                // value = repeatChar('*', value.length);\n                value = '*';\n            }\n            if (next.kind === enum_1.TokenKind.STRING) {\n                value = `\"${value}\"`;\n            }\n            if (next.kind === enum_1.TokenKind.EOF) {\n                value = '';\n            }\n            if (next.line > previousLine) {\n                source += repeatBreak(next.line - previousLine);\n                previousLine = next.line;\n                space = repeatSpace(next.column - 1);\n            }\n            else {\n                if (next.line === ((_a = next.prev) === null || _a === void 0 ? void 0 : _a.line)) {\n                    space = repeatSpace(next.start - (((_b = next.prev) === null || _b === void 0 ? void 0 : _b.end) || 0));\n                }\n            }\n            source += space + value;\n            if (next) {\n                next = next.next;\n            }\n        }\n    }\n    return source;\n}\nexports.getSourceFromLocation = getSourceFromLocation;\nfunction wrapFields(type, tracer, getConfig) {\n    if (!type ||\n        typeof type.getFields !== 'function' ||\n        type[symbols_1.OTEL_PATCHED_SYMBOL]) {\n        return;\n    }\n    const fields = type.getFields();\n    type[symbols_1.OTEL_PATCHED_SYMBOL] = true;\n    Object.keys(fields).forEach(key => {\n        const field = fields[key];\n        if (!field) {\n            return;\n        }\n        if (field.resolve) {\n            field.resolve = wrapFieldResolver(tracer, getConfig, field.resolve);\n        }\n        if (field.type) {\n            let unwrappedType = field.type;\n            while (unwrappedType.ofType) {\n                unwrappedType = unwrappedType.ofType;\n            }\n            wrapFields(unwrappedType, tracer, getConfig);\n        }\n    });\n}\nexports.wrapFields = wrapFields;\nconst handleResolveSpanError = (resolveSpan, err, shouldEndSpan) => {\n    if (!shouldEndSpan) {\n        return;\n    }\n    resolveSpan.recordException(err);\n    resolveSpan.setStatus({\n        code: api.SpanStatusCode.ERROR,\n        message: err.message,\n    });\n    resolveSpan.end();\n};\nconst handleResolveSpanSuccess = (resolveSpan, shouldEndSpan) => {\n    if (!shouldEndSpan) {\n        return;\n    }\n    resolveSpan.end();\n};\nfunction wrapFieldResolver(tracer, getConfig, fieldResolver, isDefaultResolver = false) {\n    if (wrappedFieldResolver[symbols_1.OTEL_PATCHED_SYMBOL] ||\n        typeof fieldResolver !== 'function') {\n        return fieldResolver;\n    }\n    function wrappedFieldResolver(source, args, contextValue, info) {\n        if (!fieldResolver) {\n            return undefined;\n        }\n        const config = getConfig();\n        // follows what graphql is doing to decide if this is a trivial resolver\n        // for which we don't need to create a resolve span\n        if (config.ignoreTrivialResolveSpans &&\n            isDefaultResolver &&\n            (isObjectLike(source) || typeof source === 'function')) {\n            const property = source[info.fieldName];\n            // a function execution is not trivial and should be recorder.\n            // property which is not a function is just a value and we don't want a \"resolve\" span for it\n            if (typeof property !== 'function') {\n                return fieldResolver.call(this, source, args, contextValue, info);\n            }\n        }\n        if (!contextValue[symbols_1.OTEL_GRAPHQL_DATA_SYMBOL]) {\n            return fieldResolver.call(this, source, args, contextValue, info);\n        }\n        const path = pathToArray(config.mergeItems, info && info.path);\n        const depth = path.filter((item) => typeof item === 'string').length;\n        let field;\n        let shouldEndSpan = false;\n        if (config.depth >= 0 && config.depth < depth) {\n            field = getParentField(contextValue, path);\n        }\n        else {\n            const newField = createFieldIfNotExists(tracer, getConfig, contextValue, info, path);\n            field = newField.field;\n            shouldEndSpan = newField.spanAdded;\n        }\n        return api.context.with(api.trace.setSpan(api.context.active(), field.span), () => {\n            try {\n                const res = fieldResolver.call(this, source, args, contextValue, info);\n                if ((0, exports.isPromise)(res)) {\n                    return res.then((r) => {\n                        handleResolveSpanSuccess(field.span, shouldEndSpan);\n                        return r;\n                    }, (err) => {\n                        handleResolveSpanError(field.span, err, shouldEndSpan);\n                        throw err;\n                    });\n                }\n                else {\n                    handleResolveSpanSuccess(field.span, shouldEndSpan);\n                    return res;\n                }\n            }\n            catch (err) {\n                handleResolveSpanError(field.span, err, shouldEndSpan);\n                throw err;\n            }\n        });\n    }\n    wrappedFieldResolver[symbols_1.OTEL_PATCHED_SYMBOL] = true;\n    return wrappedFieldResolver;\n}\nexports.wrapFieldResolver = wrapFieldResolver;\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/utils.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/version.js":
/*!**********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/version.js ***!
  \**********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.47.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-graphql';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_78eae6560d4756672b05d1fa6e0fe75e/node_modules/@opentelemetry/instrumentation-graphql/build/src/version.js\n");

/***/ })

};
;