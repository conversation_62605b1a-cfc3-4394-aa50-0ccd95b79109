// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["driverAdapters"]
  output          = "../generated/client"
}

datasource db {
  provider     = "postgresql"
  url          = env("DATABASE_URL")
  relationMode = "prisma"
}

model User {
  id          String   @id @default(cuid())
  clerkId     String   @unique
  email       String   @unique
  name        String?
  picture     String?

  // Extension connection
  extensionApiKey String?
  lastExtensionSync DateTime?
  lastSettingsSync DateTime?
  extensionEnabled Boolean @default(true)
  lastActiveAt DateTime?
  termsAccepted Boolean @default(false)
  termsAcceptedAt DateTime?

  // Subscription (sync with extension)
  subscriptionTier    String @default("FREE")
  subscriptionStatus  String @default("ACTIVE")

  // Settings sync
  extensionSettings   Json?
  preferences         Json?

  // Relations
  extensionSessions ExtensionSession[]
  usageMetrics     UsageMetrics[]
  apiKeys          ApiKey[]
  usageAnalytics   UsageAnalytics[]

  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model ExtensionSession {
  id        String   @id @default(cuid())
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  sessionId String
  isActive  Boolean  @default(true)
  lastActiveAt DateTime @default(now())

  // Extension details
  extensionVersion String?
  vscodeVersion    String?
  platform         String?
  metadata         Json?

  // Usage tracking
  tokensUsed    Int @default(0)
  requestsMade  Int @default(0)

  createdAt DateTime @default(now())

  @@unique([userId, sessionId])
  @@index([userId])
  @@index([isActive])
}

model UsageMetrics {
  id     String @id @default(cuid())
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  tokensUsed    Int @default(0)
  requestsMade  Int @default(0)
  costAccrued   Float @default(0)

  date DateTime @default(now())

  @@index([userId])
  @@index([date])
}

model UsageAnalytics {
  id     String @id @default(cuid())
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  modelId      String
  tokensUsed   Int @default(0)
  requestsMade Int @default(0)
  costAccrued  Float @default(0)
  sessionId    String?
  metadata     Json?

  createdAt DateTime @default(now())

  @@index([userId])
  @@index([modelId])
  @@index([createdAt])
}

model ApiKey {
  id     String @id @default(cuid())
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  name        String
  description String?
  keyHash     String   @unique
  permissions Json     @default("[]")
  isActive    Boolean  @default(true)
  expiresAt   DateTime?
  lastUsedAt  DateTime?
  usageCount  Int      @default(0)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([userId])
  @@index([isActive])
}

model UserProfile {
  id     String @id @default(cuid())
  userId String @unique

  email String
  name  String?

  // Extension settings
  subscriptionTier    String @default("FREE")
  subscriptionStatus  String @default("ACTIVE")
  termsAccepted       Boolean @default(false)
  extensionEnabled    Boolean @default(true)
  settings            Json?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([userId])
}

model PendingLogin {
  id        String   @id @default(cuid())
  deviceId  String
  state     String
  token     String
  createdAt DateTime @default(now())
  expiresAt DateTime

  @@index([deviceId])
  @@index([state])
  @@index([expiresAt])
}

// Keep the existing Page model for now
model Page {
  id   Int    @id @default(autoincrement())
  name String
}
