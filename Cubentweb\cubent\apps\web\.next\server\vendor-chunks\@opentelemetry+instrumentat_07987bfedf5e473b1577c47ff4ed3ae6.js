"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@opentelemetry+instrumentat_07987bfedf5e473b1577c47ff4ed3ae6";
exports.ids = ["vendor-chunks/@opentelemetry+instrumentat_07987bfedf5e473b1577c47ff4ed3ae6"];
exports.modules = {

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_07987bfedf5e473b1577c47ff4ed3ae6/node_modules/@opentelemetry/instrumentation-kafkajs/build/src/index.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_07987bfedf5e473b1577c47ff4ed3ae6/node_modules/@opentelemetry/instrumentation-kafkajs/build/src/index.js ***!
  \********************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors, Aspecto\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_07987bfedf5e473b1577c47ff4ed3ae6/node_modules/@opentelemetry/instrumentation-kafkajs/build/src/instrumentation.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_07987bfedf5e473b1577c47ff4ed3ae6/node_modules/@opentelemetry/instrumentation-kafkajs/build/src/types.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_07987bfedf5e473b1577c47ff4ed3ae6/node_modules/@opentelemetry/instrumentation-kafkajs/build/src/index.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_07987bfedf5e473b1577c47ff4ed3ae6/node_modules/@opentelemetry/instrumentation-kafkajs/build/src/instrumentation.js":
/*!******************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_07987bfedf5e473b1577c47ff4ed3ae6/node_modules/@opentelemetry/instrumentation-kafkajs/build/src/instrumentation.js ***!
  \******************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors, Aspecto\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KafkaJsInstrumentation = void 0;\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_07987bfedf5e473b1577c47ff4ed3ae6/node_modules/@opentelemetry/instrumentation-kafkajs/build/src/version.js\");\nconst propagator_1 = __webpack_require__(/*! ./propagator */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_07987bfedf5e473b1577c47ff4ed3ae6/node_modules/@opentelemetry/instrumentation-kafkajs/build/src/propagator.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nclass KafkaJsInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, config);\n    }\n    init() {\n        const unpatch = (moduleExports) => {\n            var _a, _b;\n            if ((0, instrumentation_1.isWrapped)((_a = moduleExports === null || moduleExports === void 0 ? void 0 : moduleExports.Kafka) === null || _a === void 0 ? void 0 : _a.prototype.producer)) {\n                this._unwrap(moduleExports.Kafka.prototype, 'producer');\n            }\n            if ((0, instrumentation_1.isWrapped)((_b = moduleExports === null || moduleExports === void 0 ? void 0 : moduleExports.Kafka) === null || _b === void 0 ? void 0 : _b.prototype.consumer)) {\n                this._unwrap(moduleExports.Kafka.prototype, 'consumer');\n            }\n        };\n        const module = new instrumentation_1.InstrumentationNodeModuleDefinition('kafkajs', ['>=0.1.0 <3'], (moduleExports) => {\n            var _a, _b;\n            unpatch(moduleExports);\n            this._wrap((_a = moduleExports === null || moduleExports === void 0 ? void 0 : moduleExports.Kafka) === null || _a === void 0 ? void 0 : _a.prototype, 'producer', this._getProducerPatch());\n            this._wrap((_b = moduleExports === null || moduleExports === void 0 ? void 0 : moduleExports.Kafka) === null || _b === void 0 ? void 0 : _b.prototype, 'consumer', this._getConsumerPatch());\n            return moduleExports;\n        }, unpatch);\n        return module;\n    }\n    _getConsumerPatch() {\n        const instrumentation = this;\n        return (original) => {\n            return function consumer(...args) {\n                const newConsumer = original.apply(this, args);\n                if ((0, instrumentation_1.isWrapped)(newConsumer.run)) {\n                    instrumentation._unwrap(newConsumer, 'run');\n                }\n                instrumentation._wrap(newConsumer, 'run', instrumentation._getConsumerRunPatch());\n                return newConsumer;\n            };\n        };\n    }\n    _getProducerPatch() {\n        const instrumentation = this;\n        return (original) => {\n            return function consumer(...args) {\n                const newProducer = original.apply(this, args);\n                if ((0, instrumentation_1.isWrapped)(newProducer.sendBatch)) {\n                    instrumentation._unwrap(newProducer, 'sendBatch');\n                }\n                instrumentation._wrap(newProducer, 'sendBatch', instrumentation._getProducerSendBatchPatch());\n                if ((0, instrumentation_1.isWrapped)(newProducer.send)) {\n                    instrumentation._unwrap(newProducer, 'send');\n                }\n                instrumentation._wrap(newProducer, 'send', instrumentation._getProducerSendPatch());\n                return newProducer;\n            };\n        };\n    }\n    _getConsumerRunPatch() {\n        const instrumentation = this;\n        return (original) => {\n            return function run(...args) {\n                const config = args[0];\n                if (config === null || config === void 0 ? void 0 : config.eachMessage) {\n                    if ((0, instrumentation_1.isWrapped)(config.eachMessage)) {\n                        instrumentation._unwrap(config, 'eachMessage');\n                    }\n                    instrumentation._wrap(config, 'eachMessage', instrumentation._getConsumerEachMessagePatch());\n                }\n                if (config === null || config === void 0 ? void 0 : config.eachBatch) {\n                    if ((0, instrumentation_1.isWrapped)(config.eachBatch)) {\n                        instrumentation._unwrap(config, 'eachBatch');\n                    }\n                    instrumentation._wrap(config, 'eachBatch', instrumentation._getConsumerEachBatchPatch());\n                }\n                return original.call(this, config);\n            };\n        };\n    }\n    _getConsumerEachMessagePatch() {\n        const instrumentation = this;\n        return (original) => {\n            return function eachMessage(...args) {\n                const payload = args[0];\n                const propagatedContext = api_1.propagation.extract(api_1.ROOT_CONTEXT, payload.message.headers, propagator_1.bufferTextMapGetter);\n                const span = instrumentation._startConsumerSpan(payload.topic, payload.message, semantic_conventions_1.MESSAGINGOPERATIONVALUES_PROCESS, propagatedContext);\n                const eachMessagePromise = api_1.context.with(api_1.trace.setSpan(propagatedContext, span), () => {\n                    return original.apply(this, args);\n                });\n                return instrumentation._endSpansOnPromise([span], eachMessagePromise);\n            };\n        };\n    }\n    _getConsumerEachBatchPatch() {\n        return (original) => {\n            const instrumentation = this;\n            return function eachBatch(...args) {\n                const payload = args[0];\n                // https://github.com/open-telemetry/opentelemetry-specification/blob/master/specification/trace/semantic_conventions/messaging.md#topic-with-multiple-consumers\n                const receivingSpan = instrumentation._startConsumerSpan(payload.batch.topic, undefined, semantic_conventions_1.MESSAGINGOPERATIONVALUES_RECEIVE, api_1.ROOT_CONTEXT);\n                return api_1.context.with(api_1.trace.setSpan(api_1.context.active(), receivingSpan), () => {\n                    const spans = payload.batch.messages.map((message) => {\n                        var _a;\n                        const propagatedContext = api_1.propagation.extract(api_1.ROOT_CONTEXT, message.headers, propagator_1.bufferTextMapGetter);\n                        const spanContext = (_a = api_1.trace\n                            .getSpan(propagatedContext)) === null || _a === void 0 ? void 0 : _a.spanContext();\n                        let origSpanLink;\n                        if (spanContext) {\n                            origSpanLink = {\n                                context: spanContext,\n                            };\n                        }\n                        return instrumentation._startConsumerSpan(payload.batch.topic, message, semantic_conventions_1.MESSAGINGOPERATIONVALUES_PROCESS, undefined, origSpanLink);\n                    });\n                    const batchMessagePromise = original.apply(this, args);\n                    spans.unshift(receivingSpan);\n                    return instrumentation._endSpansOnPromise(spans, batchMessagePromise);\n                });\n            };\n        };\n    }\n    _getProducerSendBatchPatch() {\n        const instrumentation = this;\n        return (original) => {\n            return function sendBatch(...args) {\n                const batch = args[0];\n                const messages = batch.topicMessages || [];\n                const spans = messages\n                    .map(topicMessage => topicMessage.messages.map(message => instrumentation._startProducerSpan(topicMessage.topic, message)))\n                    .reduce((acc, val) => acc.concat(val), []);\n                const origSendResult = original.apply(this, args);\n                return instrumentation._endSpansOnPromise(spans, origSendResult);\n            };\n        };\n    }\n    _getProducerSendPatch() {\n        const instrumentation = this;\n        return (original) => {\n            return function send(...args) {\n                const record = args[0];\n                const spans = record.messages.map(message => {\n                    return instrumentation._startProducerSpan(record.topic, message);\n                });\n                const origSendResult = original.apply(this, args);\n                return instrumentation._endSpansOnPromise(spans, origSendResult);\n            };\n        };\n    }\n    _endSpansOnPromise(spans, sendPromise) {\n        return Promise.resolve(sendPromise)\n            .catch(reason => {\n            let errorMessage;\n            if (typeof reason === 'string')\n                errorMessage = reason;\n            else if (typeof reason === 'object' &&\n                Object.prototype.hasOwnProperty.call(reason, 'message'))\n                errorMessage = reason.message;\n            spans.forEach(span => span.setStatus({\n                code: api_1.SpanStatusCode.ERROR,\n                message: errorMessage,\n            }));\n            throw reason;\n        })\n            .finally(() => {\n            spans.forEach(span => span.end());\n        });\n    }\n    _startConsumerSpan(topic, message, operation, context, link) {\n        const span = this.tracer.startSpan(topic, {\n            kind: api_1.SpanKind.CONSUMER,\n            attributes: {\n                [semantic_conventions_1.SEMATTRS_MESSAGING_SYSTEM]: 'kafka',\n                [semantic_conventions_1.SEMATTRS_MESSAGING_DESTINATION]: topic,\n                [semantic_conventions_1.SEMATTRS_MESSAGING_OPERATION]: operation,\n            },\n            links: link ? [link] : [],\n        }, context);\n        const { consumerHook } = this.getConfig();\n        if (consumerHook && message) {\n            (0, instrumentation_1.safeExecuteInTheMiddle)(() => consumerHook(span, { topic, message }), e => {\n                if (e)\n                    this._diag.error('consumerHook error', e);\n            }, true);\n        }\n        return span;\n    }\n    _startProducerSpan(topic, message) {\n        var _a;\n        const span = this.tracer.startSpan(topic, {\n            kind: api_1.SpanKind.PRODUCER,\n            attributes: {\n                [semantic_conventions_1.SEMATTRS_MESSAGING_SYSTEM]: 'kafka',\n                [semantic_conventions_1.SEMATTRS_MESSAGING_DESTINATION]: topic,\n            },\n        });\n        message.headers = (_a = message.headers) !== null && _a !== void 0 ? _a : {};\n        api_1.propagation.inject(api_1.trace.setSpan(api_1.context.active(), span), message.headers);\n        const { producerHook } = this.getConfig();\n        if (producerHook) {\n            (0, instrumentation_1.safeExecuteInTheMiddle)(() => producerHook(span, { topic, message }), e => {\n                if (e)\n                    this._diag.error('producerHook error', e);\n            }, true);\n        }\n        return span;\n    }\n}\nexports.KafkaJsInstrumentation = KafkaJsInstrumentation;\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_07987bfedf5e473b1577c47ff4ed3ae6/node_modules/@opentelemetry/instrumentation-kafkajs/build/src/instrumentation.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_07987bfedf5e473b1577c47ff4ed3ae6/node_modules/@opentelemetry/instrumentation-kafkajs/build/src/propagator.js":
/*!*************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_07987bfedf5e473b1577c47ff4ed3ae6/node_modules/@opentelemetry/instrumentation-kafkajs/build/src/propagator.js ***!
  \*************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.bufferTextMapGetter = void 0;\n/*\nsame as open telemetry's `defaultTextMapGetter`,\nbut also handle case where header is buffer,\nadding toString() to make sure string is returned\n*/\nexports.bufferTextMapGetter = {\n    get(carrier, key) {\n        var _a;\n        if (!carrier) {\n            return undefined;\n        }\n        const keys = Object.keys(carrier);\n        for (const carrierKey of keys) {\n            if (carrierKey === key || carrierKey.toLowerCase() === key) {\n                return (_a = carrier[carrierKey]) === null || _a === void 0 ? void 0 : _a.toString();\n            }\n        }\n        return undefined;\n    },\n    keys(carrier) {\n        return carrier ? Object.keys(carrier) : [];\n    },\n};\n//# sourceMappingURL=propagator.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_07987bfedf5e473b1577c47ff4ed3ae6/node_modules/@opentelemetry/instrumentation-kafkajs/build/src/propagator.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_07987bfedf5e473b1577c47ff4ed3ae6/node_modules/@opentelemetry/instrumentation-kafkajs/build/src/types.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_07987bfedf5e473b1577c47ff4ed3ae6/node_modules/@opentelemetry/instrumentation-kafkajs/build/src/types.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9Ab3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfMDc5ODdiZmVkZjVlNDczYjE1NzdjNDdmZjRlZDNhZTYvbm9kZV9tb2R1bGVzL0BvcGVudGVsZW1ldHJ5L2luc3RydW1lbnRhdGlvbi1rYWZrYWpzL2J1aWxkL3NyYy90eXBlcy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbndhclxcRG9jdW1lbnRzXFxDdWJlbnR3ZWJcXGN1YmVudFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQG9wZW50ZWxlbWV0cnkraW5zdHJ1bWVudGF0XzA3OTg3YmZlZGY1ZTQ3M2IxNTc3YzQ3ZmY0ZWQzYWU2XFxub2RlX21vZHVsZXNcXEBvcGVudGVsZW1ldHJ5XFxpbnN0cnVtZW50YXRpb24ta2Fma2Fqc1xcYnVpbGRcXHNyY1xcdHlwZXMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD10eXBlcy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_07987bfedf5e473b1577c47ff4ed3ae6/node_modules/@opentelemetry/instrumentation-kafkajs/build/src/types.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_07987bfedf5e473b1577c47ff4ed3ae6/node_modules/@opentelemetry/instrumentation-kafkajs/build/src/version.js":
/*!**********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_07987bfedf5e473b1577c47ff4ed3ae6/node_modules/@opentelemetry/instrumentation-kafkajs/build/src/version.js ***!
  \**********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.7.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-kafkajs';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_07987bfedf5e473b1577c47ff4ed3ae6/node_modules/@opentelemetry/instrumentation-kafkajs/build/src/version.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_07987bfedf5e473b1577c47ff4ed3ae6/node_modules/@opentelemetry/instrumentation-kafkajs/build/src/index.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_07987bfedf5e473b1577c47ff4ed3ae6/node_modules/@opentelemetry/instrumentation-kafkajs/build/src/index.js ***!
  \********************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors, Aspecto\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_07987bfedf5e473b1577c47ff4ed3ae6/node_modules/@opentelemetry/instrumentation-kafkajs/build/src/instrumentation.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_07987bfedf5e473b1577c47ff4ed3ae6/node_modules/@opentelemetry/instrumentation-kafkajs/build/src/types.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_07987bfedf5e473b1577c47ff4ed3ae6/node_modules/@opentelemetry/instrumentation-kafkajs/build/src/index.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_07987bfedf5e473b1577c47ff4ed3ae6/node_modules/@opentelemetry/instrumentation-kafkajs/build/src/instrumentation.js":
/*!******************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_07987bfedf5e473b1577c47ff4ed3ae6/node_modules/@opentelemetry/instrumentation-kafkajs/build/src/instrumentation.js ***!
  \******************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors, Aspecto\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KafkaJsInstrumentation = void 0;\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_07987bfedf5e473b1577c47ff4ed3ae6/node_modules/@opentelemetry/instrumentation-kafkajs/build/src/version.js\");\nconst propagator_1 = __webpack_require__(/*! ./propagator */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_07987bfedf5e473b1577c47ff4ed3ae6/node_modules/@opentelemetry/instrumentation-kafkajs/build/src/propagator.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nclass KafkaJsInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, config);\n    }\n    init() {\n        const unpatch = (moduleExports) => {\n            var _a, _b;\n            if ((0, instrumentation_1.isWrapped)((_a = moduleExports === null || moduleExports === void 0 ? void 0 : moduleExports.Kafka) === null || _a === void 0 ? void 0 : _a.prototype.producer)) {\n                this._unwrap(moduleExports.Kafka.prototype, 'producer');\n            }\n            if ((0, instrumentation_1.isWrapped)((_b = moduleExports === null || moduleExports === void 0 ? void 0 : moduleExports.Kafka) === null || _b === void 0 ? void 0 : _b.prototype.consumer)) {\n                this._unwrap(moduleExports.Kafka.prototype, 'consumer');\n            }\n        };\n        const module = new instrumentation_1.InstrumentationNodeModuleDefinition('kafkajs', ['>=0.1.0 <3'], (moduleExports) => {\n            var _a, _b;\n            unpatch(moduleExports);\n            this._wrap((_a = moduleExports === null || moduleExports === void 0 ? void 0 : moduleExports.Kafka) === null || _a === void 0 ? void 0 : _a.prototype, 'producer', this._getProducerPatch());\n            this._wrap((_b = moduleExports === null || moduleExports === void 0 ? void 0 : moduleExports.Kafka) === null || _b === void 0 ? void 0 : _b.prototype, 'consumer', this._getConsumerPatch());\n            return moduleExports;\n        }, unpatch);\n        return module;\n    }\n    _getConsumerPatch() {\n        const instrumentation = this;\n        return (original) => {\n            return function consumer(...args) {\n                const newConsumer = original.apply(this, args);\n                if ((0, instrumentation_1.isWrapped)(newConsumer.run)) {\n                    instrumentation._unwrap(newConsumer, 'run');\n                }\n                instrumentation._wrap(newConsumer, 'run', instrumentation._getConsumerRunPatch());\n                return newConsumer;\n            };\n        };\n    }\n    _getProducerPatch() {\n        const instrumentation = this;\n        return (original) => {\n            return function consumer(...args) {\n                const newProducer = original.apply(this, args);\n                if ((0, instrumentation_1.isWrapped)(newProducer.sendBatch)) {\n                    instrumentation._unwrap(newProducer, 'sendBatch');\n                }\n                instrumentation._wrap(newProducer, 'sendBatch', instrumentation._getProducerSendBatchPatch());\n                if ((0, instrumentation_1.isWrapped)(newProducer.send)) {\n                    instrumentation._unwrap(newProducer, 'send');\n                }\n                instrumentation._wrap(newProducer, 'send', instrumentation._getProducerSendPatch());\n                return newProducer;\n            };\n        };\n    }\n    _getConsumerRunPatch() {\n        const instrumentation = this;\n        return (original) => {\n            return function run(...args) {\n                const config = args[0];\n                if (config === null || config === void 0 ? void 0 : config.eachMessage) {\n                    if ((0, instrumentation_1.isWrapped)(config.eachMessage)) {\n                        instrumentation._unwrap(config, 'eachMessage');\n                    }\n                    instrumentation._wrap(config, 'eachMessage', instrumentation._getConsumerEachMessagePatch());\n                }\n                if (config === null || config === void 0 ? void 0 : config.eachBatch) {\n                    if ((0, instrumentation_1.isWrapped)(config.eachBatch)) {\n                        instrumentation._unwrap(config, 'eachBatch');\n                    }\n                    instrumentation._wrap(config, 'eachBatch', instrumentation._getConsumerEachBatchPatch());\n                }\n                return original.call(this, config);\n            };\n        };\n    }\n    _getConsumerEachMessagePatch() {\n        const instrumentation = this;\n        return (original) => {\n            return function eachMessage(...args) {\n                const payload = args[0];\n                const propagatedContext = api_1.propagation.extract(api_1.ROOT_CONTEXT, payload.message.headers, propagator_1.bufferTextMapGetter);\n                const span = instrumentation._startConsumerSpan(payload.topic, payload.message, semantic_conventions_1.MESSAGINGOPERATIONVALUES_PROCESS, propagatedContext);\n                const eachMessagePromise = api_1.context.with(api_1.trace.setSpan(propagatedContext, span), () => {\n                    return original.apply(this, args);\n                });\n                return instrumentation._endSpansOnPromise([span], eachMessagePromise);\n            };\n        };\n    }\n    _getConsumerEachBatchPatch() {\n        return (original) => {\n            const instrumentation = this;\n            return function eachBatch(...args) {\n                const payload = args[0];\n                // https://github.com/open-telemetry/opentelemetry-specification/blob/master/specification/trace/semantic_conventions/messaging.md#topic-with-multiple-consumers\n                const receivingSpan = instrumentation._startConsumerSpan(payload.batch.topic, undefined, semantic_conventions_1.MESSAGINGOPERATIONVALUES_RECEIVE, api_1.ROOT_CONTEXT);\n                return api_1.context.with(api_1.trace.setSpan(api_1.context.active(), receivingSpan), () => {\n                    const spans = payload.batch.messages.map((message) => {\n                        var _a;\n                        const propagatedContext = api_1.propagation.extract(api_1.ROOT_CONTEXT, message.headers, propagator_1.bufferTextMapGetter);\n                        const spanContext = (_a = api_1.trace\n                            .getSpan(propagatedContext)) === null || _a === void 0 ? void 0 : _a.spanContext();\n                        let origSpanLink;\n                        if (spanContext) {\n                            origSpanLink = {\n                                context: spanContext,\n                            };\n                        }\n                        return instrumentation._startConsumerSpan(payload.batch.topic, message, semantic_conventions_1.MESSAGINGOPERATIONVALUES_PROCESS, undefined, origSpanLink);\n                    });\n                    const batchMessagePromise = original.apply(this, args);\n                    spans.unshift(receivingSpan);\n                    return instrumentation._endSpansOnPromise(spans, batchMessagePromise);\n                });\n            };\n        };\n    }\n    _getProducerSendBatchPatch() {\n        const instrumentation = this;\n        return (original) => {\n            return function sendBatch(...args) {\n                const batch = args[0];\n                const messages = batch.topicMessages || [];\n                const spans = messages\n                    .map(topicMessage => topicMessage.messages.map(message => instrumentation._startProducerSpan(topicMessage.topic, message)))\n                    .reduce((acc, val) => acc.concat(val), []);\n                const origSendResult = original.apply(this, args);\n                return instrumentation._endSpansOnPromise(spans, origSendResult);\n            };\n        };\n    }\n    _getProducerSendPatch() {\n        const instrumentation = this;\n        return (original) => {\n            return function send(...args) {\n                const record = args[0];\n                const spans = record.messages.map(message => {\n                    return instrumentation._startProducerSpan(record.topic, message);\n                });\n                const origSendResult = original.apply(this, args);\n                return instrumentation._endSpansOnPromise(spans, origSendResult);\n            };\n        };\n    }\n    _endSpansOnPromise(spans, sendPromise) {\n        return Promise.resolve(sendPromise)\n            .catch(reason => {\n            let errorMessage;\n            if (typeof reason === 'string')\n                errorMessage = reason;\n            else if (typeof reason === 'object' &&\n                Object.prototype.hasOwnProperty.call(reason, 'message'))\n                errorMessage = reason.message;\n            spans.forEach(span => span.setStatus({\n                code: api_1.SpanStatusCode.ERROR,\n                message: errorMessage,\n            }));\n            throw reason;\n        })\n            .finally(() => {\n            spans.forEach(span => span.end());\n        });\n    }\n    _startConsumerSpan(topic, message, operation, context, link) {\n        const span = this.tracer.startSpan(topic, {\n            kind: api_1.SpanKind.CONSUMER,\n            attributes: {\n                [semantic_conventions_1.SEMATTRS_MESSAGING_SYSTEM]: 'kafka',\n                [semantic_conventions_1.SEMATTRS_MESSAGING_DESTINATION]: topic,\n                [semantic_conventions_1.SEMATTRS_MESSAGING_OPERATION]: operation,\n            },\n            links: link ? [link] : [],\n        }, context);\n        const { consumerHook } = this.getConfig();\n        if (consumerHook && message) {\n            (0, instrumentation_1.safeExecuteInTheMiddle)(() => consumerHook(span, { topic, message }), e => {\n                if (e)\n                    this._diag.error('consumerHook error', e);\n            }, true);\n        }\n        return span;\n    }\n    _startProducerSpan(topic, message) {\n        var _a;\n        const span = this.tracer.startSpan(topic, {\n            kind: api_1.SpanKind.PRODUCER,\n            attributes: {\n                [semantic_conventions_1.SEMATTRS_MESSAGING_SYSTEM]: 'kafka',\n                [semantic_conventions_1.SEMATTRS_MESSAGING_DESTINATION]: topic,\n            },\n        });\n        message.headers = (_a = message.headers) !== null && _a !== void 0 ? _a : {};\n        api_1.propagation.inject(api_1.trace.setSpan(api_1.context.active(), span), message.headers);\n        const { producerHook } = this.getConfig();\n        if (producerHook) {\n            (0, instrumentation_1.safeExecuteInTheMiddle)(() => producerHook(span, { topic, message }), e => {\n                if (e)\n                    this._diag.error('producerHook error', e);\n            }, true);\n        }\n        return span;\n    }\n}\nexports.KafkaJsInstrumentation = KafkaJsInstrumentation;\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_07987bfedf5e473b1577c47ff4ed3ae6/node_modules/@opentelemetry/instrumentation-kafkajs/build/src/instrumentation.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_07987bfedf5e473b1577c47ff4ed3ae6/node_modules/@opentelemetry/instrumentation-kafkajs/build/src/propagator.js":
/*!*************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_07987bfedf5e473b1577c47ff4ed3ae6/node_modules/@opentelemetry/instrumentation-kafkajs/build/src/propagator.js ***!
  \*************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.bufferTextMapGetter = void 0;\n/*\nsame as open telemetry's `defaultTextMapGetter`,\nbut also handle case where header is buffer,\nadding toString() to make sure string is returned\n*/\nexports.bufferTextMapGetter = {\n    get(carrier, key) {\n        var _a;\n        if (!carrier) {\n            return undefined;\n        }\n        const keys = Object.keys(carrier);\n        for (const carrierKey of keys) {\n            if (carrierKey === key || carrierKey.toLowerCase() === key) {\n                return (_a = carrier[carrierKey]) === null || _a === void 0 ? void 0 : _a.toString();\n            }\n        }\n        return undefined;\n    },\n    keys(carrier) {\n        return carrier ? Object.keys(carrier) : [];\n    },\n};\n//# sourceMappingURL=propagator.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_07987bfedf5e473b1577c47ff4ed3ae6/node_modules/@opentelemetry/instrumentation-kafkajs/build/src/propagator.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_07987bfedf5e473b1577c47ff4ed3ae6/node_modules/@opentelemetry/instrumentation-kafkajs/build/src/types.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_07987bfedf5e473b1577c47ff4ed3ae6/node_modules/@opentelemetry/instrumentation-kafkajs/build/src/types.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF8wNzk4N2JmZWRmNWU0NzNiMTU3N2M0N2ZmNGVkM2FlNi9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLWthZmthanMvYnVpbGQvc3JjL3R5cGVzLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdEIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFud2FyXFxEb2N1bWVudHNcXEN1YmVudHdlYlxcY3ViZW50XFxub2RlX21vZHVsZXNcXC5wbnBtXFxAb3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfMDc5ODdiZmVkZjVlNDczYjE1NzdjNDdmZjRlZDNhZTZcXG5vZGVfbW9kdWxlc1xcQG9wZW50ZWxlbWV0cnlcXGluc3RydW1lbnRhdGlvbi1rYWZrYWpzXFxidWlsZFxcc3JjXFx0eXBlcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXR5cGVzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_07987bfedf5e473b1577c47ff4ed3ae6/node_modules/@opentelemetry/instrumentation-kafkajs/build/src/types.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_07987bfedf5e473b1577c47ff4ed3ae6/node_modules/@opentelemetry/instrumentation-kafkajs/build/src/version.js":
/*!**********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_07987bfedf5e473b1577c47ff4ed3ae6/node_modules/@opentelemetry/instrumentation-kafkajs/build/src/version.js ***!
  \**********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.7.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-kafkajs';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_07987bfedf5e473b1577c47ff4ed3ae6/node_modules/@opentelemetry/instrumentation-kafkajs/build/src/version.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_07987bfedf5e473b1577c47ff4ed3ae6/node_modules/@opentelemetry/instrumentation-kafkajs/build/src/index.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_07987bfedf5e473b1577c47ff4ed3ae6/node_modules/@opentelemetry/instrumentation-kafkajs/build/src/index.js ***!
  \********************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors, Aspecto\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_07987bfedf5e473b1577c47ff4ed3ae6/node_modules/@opentelemetry/instrumentation-kafkajs/build/src/instrumentation.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_07987bfedf5e473b1577c47ff4ed3ae6/node_modules/@opentelemetry/instrumentation-kafkajs/build/src/types.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_07987bfedf5e473b1577c47ff4ed3ae6/node_modules/@opentelemetry/instrumentation-kafkajs/build/src/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_07987bfedf5e473b1577c47ff4ed3ae6/node_modules/@opentelemetry/instrumentation-kafkajs/build/src/instrumentation.js":
/*!******************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_07987bfedf5e473b1577c47ff4ed3ae6/node_modules/@opentelemetry/instrumentation-kafkajs/build/src/instrumentation.js ***!
  \******************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors, Aspecto\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KafkaJsInstrumentation = void 0;\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_07987bfedf5e473b1577c47ff4ed3ae6/node_modules/@opentelemetry/instrumentation-kafkajs/build/src/version.js\");\nconst propagator_1 = __webpack_require__(/*! ./propagator */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_07987bfedf5e473b1577c47ff4ed3ae6/node_modules/@opentelemetry/instrumentation-kafkajs/build/src/propagator.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nclass KafkaJsInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, config);\n    }\n    init() {\n        const unpatch = (moduleExports) => {\n            var _a, _b;\n            if ((0, instrumentation_1.isWrapped)((_a = moduleExports === null || moduleExports === void 0 ? void 0 : moduleExports.Kafka) === null || _a === void 0 ? void 0 : _a.prototype.producer)) {\n                this._unwrap(moduleExports.Kafka.prototype, 'producer');\n            }\n            if ((0, instrumentation_1.isWrapped)((_b = moduleExports === null || moduleExports === void 0 ? void 0 : moduleExports.Kafka) === null || _b === void 0 ? void 0 : _b.prototype.consumer)) {\n                this._unwrap(moduleExports.Kafka.prototype, 'consumer');\n            }\n        };\n        const module = new instrumentation_1.InstrumentationNodeModuleDefinition('kafkajs', ['>=0.1.0 <3'], (moduleExports) => {\n            var _a, _b;\n            unpatch(moduleExports);\n            this._wrap((_a = moduleExports === null || moduleExports === void 0 ? void 0 : moduleExports.Kafka) === null || _a === void 0 ? void 0 : _a.prototype, 'producer', this._getProducerPatch());\n            this._wrap((_b = moduleExports === null || moduleExports === void 0 ? void 0 : moduleExports.Kafka) === null || _b === void 0 ? void 0 : _b.prototype, 'consumer', this._getConsumerPatch());\n            return moduleExports;\n        }, unpatch);\n        return module;\n    }\n    _getConsumerPatch() {\n        const instrumentation = this;\n        return (original) => {\n            return function consumer(...args) {\n                const newConsumer = original.apply(this, args);\n                if ((0, instrumentation_1.isWrapped)(newConsumer.run)) {\n                    instrumentation._unwrap(newConsumer, 'run');\n                }\n                instrumentation._wrap(newConsumer, 'run', instrumentation._getConsumerRunPatch());\n                return newConsumer;\n            };\n        };\n    }\n    _getProducerPatch() {\n        const instrumentation = this;\n        return (original) => {\n            return function consumer(...args) {\n                const newProducer = original.apply(this, args);\n                if ((0, instrumentation_1.isWrapped)(newProducer.sendBatch)) {\n                    instrumentation._unwrap(newProducer, 'sendBatch');\n                }\n                instrumentation._wrap(newProducer, 'sendBatch', instrumentation._getProducerSendBatchPatch());\n                if ((0, instrumentation_1.isWrapped)(newProducer.send)) {\n                    instrumentation._unwrap(newProducer, 'send');\n                }\n                instrumentation._wrap(newProducer, 'send', instrumentation._getProducerSendPatch());\n                return newProducer;\n            };\n        };\n    }\n    _getConsumerRunPatch() {\n        const instrumentation = this;\n        return (original) => {\n            return function run(...args) {\n                const config = args[0];\n                if (config === null || config === void 0 ? void 0 : config.eachMessage) {\n                    if ((0, instrumentation_1.isWrapped)(config.eachMessage)) {\n                        instrumentation._unwrap(config, 'eachMessage');\n                    }\n                    instrumentation._wrap(config, 'eachMessage', instrumentation._getConsumerEachMessagePatch());\n                }\n                if (config === null || config === void 0 ? void 0 : config.eachBatch) {\n                    if ((0, instrumentation_1.isWrapped)(config.eachBatch)) {\n                        instrumentation._unwrap(config, 'eachBatch');\n                    }\n                    instrumentation._wrap(config, 'eachBatch', instrumentation._getConsumerEachBatchPatch());\n                }\n                return original.call(this, config);\n            };\n        };\n    }\n    _getConsumerEachMessagePatch() {\n        const instrumentation = this;\n        return (original) => {\n            return function eachMessage(...args) {\n                const payload = args[0];\n                const propagatedContext = api_1.propagation.extract(api_1.ROOT_CONTEXT, payload.message.headers, propagator_1.bufferTextMapGetter);\n                const span = instrumentation._startConsumerSpan(payload.topic, payload.message, semantic_conventions_1.MESSAGINGOPERATIONVALUES_PROCESS, propagatedContext);\n                const eachMessagePromise = api_1.context.with(api_1.trace.setSpan(propagatedContext, span), () => {\n                    return original.apply(this, args);\n                });\n                return instrumentation._endSpansOnPromise([span], eachMessagePromise);\n            };\n        };\n    }\n    _getConsumerEachBatchPatch() {\n        return (original) => {\n            const instrumentation = this;\n            return function eachBatch(...args) {\n                const payload = args[0];\n                // https://github.com/open-telemetry/opentelemetry-specification/blob/master/specification/trace/semantic_conventions/messaging.md#topic-with-multiple-consumers\n                const receivingSpan = instrumentation._startConsumerSpan(payload.batch.topic, undefined, semantic_conventions_1.MESSAGINGOPERATIONVALUES_RECEIVE, api_1.ROOT_CONTEXT);\n                return api_1.context.with(api_1.trace.setSpan(api_1.context.active(), receivingSpan), () => {\n                    const spans = payload.batch.messages.map((message) => {\n                        var _a;\n                        const propagatedContext = api_1.propagation.extract(api_1.ROOT_CONTEXT, message.headers, propagator_1.bufferTextMapGetter);\n                        const spanContext = (_a = api_1.trace\n                            .getSpan(propagatedContext)) === null || _a === void 0 ? void 0 : _a.spanContext();\n                        let origSpanLink;\n                        if (spanContext) {\n                            origSpanLink = {\n                                context: spanContext,\n                            };\n                        }\n                        return instrumentation._startConsumerSpan(payload.batch.topic, message, semantic_conventions_1.MESSAGINGOPERATIONVALUES_PROCESS, undefined, origSpanLink);\n                    });\n                    const batchMessagePromise = original.apply(this, args);\n                    spans.unshift(receivingSpan);\n                    return instrumentation._endSpansOnPromise(spans, batchMessagePromise);\n                });\n            };\n        };\n    }\n    _getProducerSendBatchPatch() {\n        const instrumentation = this;\n        return (original) => {\n            return function sendBatch(...args) {\n                const batch = args[0];\n                const messages = batch.topicMessages || [];\n                const spans = messages\n                    .map(topicMessage => topicMessage.messages.map(message => instrumentation._startProducerSpan(topicMessage.topic, message)))\n                    .reduce((acc, val) => acc.concat(val), []);\n                const origSendResult = original.apply(this, args);\n                return instrumentation._endSpansOnPromise(spans, origSendResult);\n            };\n        };\n    }\n    _getProducerSendPatch() {\n        const instrumentation = this;\n        return (original) => {\n            return function send(...args) {\n                const record = args[0];\n                const spans = record.messages.map(message => {\n                    return instrumentation._startProducerSpan(record.topic, message);\n                });\n                const origSendResult = original.apply(this, args);\n                return instrumentation._endSpansOnPromise(spans, origSendResult);\n            };\n        };\n    }\n    _endSpansOnPromise(spans, sendPromise) {\n        return Promise.resolve(sendPromise)\n            .catch(reason => {\n            let errorMessage;\n            if (typeof reason === 'string')\n                errorMessage = reason;\n            else if (typeof reason === 'object' &&\n                Object.prototype.hasOwnProperty.call(reason, 'message'))\n                errorMessage = reason.message;\n            spans.forEach(span => span.setStatus({\n                code: api_1.SpanStatusCode.ERROR,\n                message: errorMessage,\n            }));\n            throw reason;\n        })\n            .finally(() => {\n            spans.forEach(span => span.end());\n        });\n    }\n    _startConsumerSpan(topic, message, operation, context, link) {\n        const span = this.tracer.startSpan(topic, {\n            kind: api_1.SpanKind.CONSUMER,\n            attributes: {\n                [semantic_conventions_1.SEMATTRS_MESSAGING_SYSTEM]: 'kafka',\n                [semantic_conventions_1.SEMATTRS_MESSAGING_DESTINATION]: topic,\n                [semantic_conventions_1.SEMATTRS_MESSAGING_OPERATION]: operation,\n            },\n            links: link ? [link] : [],\n        }, context);\n        const { consumerHook } = this.getConfig();\n        if (consumerHook && message) {\n            (0, instrumentation_1.safeExecuteInTheMiddle)(() => consumerHook(span, { topic, message }), e => {\n                if (e)\n                    this._diag.error('consumerHook error', e);\n            }, true);\n        }\n        return span;\n    }\n    _startProducerSpan(topic, message) {\n        var _a;\n        const span = this.tracer.startSpan(topic, {\n            kind: api_1.SpanKind.PRODUCER,\n            attributes: {\n                [semantic_conventions_1.SEMATTRS_MESSAGING_SYSTEM]: 'kafka',\n                [semantic_conventions_1.SEMATTRS_MESSAGING_DESTINATION]: topic,\n            },\n        });\n        message.headers = (_a = message.headers) !== null && _a !== void 0 ? _a : {};\n        api_1.propagation.inject(api_1.trace.setSpan(api_1.context.active(), span), message.headers);\n        const { producerHook } = this.getConfig();\n        if (producerHook) {\n            (0, instrumentation_1.safeExecuteInTheMiddle)(() => producerHook(span, { topic, message }), e => {\n                if (e)\n                    this._diag.error('producerHook error', e);\n            }, true);\n        }\n        return span;\n    }\n}\nexports.KafkaJsInstrumentation = KafkaJsInstrumentation;\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_07987bfedf5e473b1577c47ff4ed3ae6/node_modules/@opentelemetry/instrumentation-kafkajs/build/src/instrumentation.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_07987bfedf5e473b1577c47ff4ed3ae6/node_modules/@opentelemetry/instrumentation-kafkajs/build/src/propagator.js":
/*!*************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_07987bfedf5e473b1577c47ff4ed3ae6/node_modules/@opentelemetry/instrumentation-kafkajs/build/src/propagator.js ***!
  \*************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.bufferTextMapGetter = void 0;\n/*\nsame as open telemetry's `defaultTextMapGetter`,\nbut also handle case where header is buffer,\nadding toString() to make sure string is returned\n*/\nexports.bufferTextMapGetter = {\n    get(carrier, key) {\n        var _a;\n        if (!carrier) {\n            return undefined;\n        }\n        const keys = Object.keys(carrier);\n        for (const carrierKey of keys) {\n            if (carrierKey === key || carrierKey.toLowerCase() === key) {\n                return (_a = carrier[carrierKey]) === null || _a === void 0 ? void 0 : _a.toString();\n            }\n        }\n        return undefined;\n    },\n    keys(carrier) {\n        return carrier ? Object.keys(carrier) : [];\n    },\n};\n//# sourceMappingURL=propagator.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_07987bfedf5e473b1577c47ff4ed3ae6/node_modules/@opentelemetry/instrumentation-kafkajs/build/src/propagator.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_07987bfedf5e473b1577c47ff4ed3ae6/node_modules/@opentelemetry/instrumentation-kafkajs/build/src/types.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_07987bfedf5e473b1577c47ff4ed3ae6/node_modules/@opentelemetry/instrumentation-kafkajs/build/src/types.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF8wNzk4N2JmZWRmNWU0NzNiMTU3N2M0N2ZmNGVkM2FlNi9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLWthZmthanMvYnVpbGQvc3JjL3R5cGVzLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdEIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFud2FyXFxEb2N1bWVudHNcXEN1YmVudHdlYlxcY3ViZW50XFxub2RlX21vZHVsZXNcXC5wbnBtXFxAb3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfMDc5ODdiZmVkZjVlNDczYjE1NzdjNDdmZjRlZDNhZTZcXG5vZGVfbW9kdWxlc1xcQG9wZW50ZWxlbWV0cnlcXGluc3RydW1lbnRhdGlvbi1rYWZrYWpzXFxidWlsZFxcc3JjXFx0eXBlcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXR5cGVzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_07987bfedf5e473b1577c47ff4ed3ae6/node_modules/@opentelemetry/instrumentation-kafkajs/build/src/types.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_07987bfedf5e473b1577c47ff4ed3ae6/node_modules/@opentelemetry/instrumentation-kafkajs/build/src/version.js":
/*!**********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_07987bfedf5e473b1577c47ff4ed3ae6/node_modules/@opentelemetry/instrumentation-kafkajs/build/src/version.js ***!
  \**********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.7.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-kafkajs';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_07987bfedf5e473b1577c47ff4ed3ae6/node_modules/@opentelemetry/instrumentation-kafkajs/build/src/version.js\n");

/***/ })

};
;