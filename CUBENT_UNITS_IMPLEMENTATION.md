# Cubent Units Implementation

This document outlines the implementation of Cubent Units tracking system for both the extension and webapp.

## Overview

The Cubent Units system provides unified usage tracking across all AI models based on the pricing model from https://cubentdev.mintlify.app/models-and-pricing.

## Database Changes

### Schema Updates (Neon PostgreSQL)

Added the following fields to track Cubent Units:

**User table:**
- `currentMonthCubentUnits` (Float) - Current month usage
- `cubentUnitsLimit` (Float) - Monthly limit (default: 50)
- `lastUsageReset` (DateTime) - Last reset timestamp

**UsageMetrics table:**
- `cubentUnits` (Float) - Cubent Units used per day

**UsageAnalytics table:**
- `cubentUnits` (Float) - Cubent Units used per request

## Implementation Components

### 1. Cubent Units Calculator (`Cubentweb/cubent/packages/database/cubent-units.ts`)

- **MODEL_PRICING**: Complete mapping of all models to their Cubent Unit costs
- **calculateCubentUnits()**: Calculate units for a given model and usage
- **canMakeRequest()**: Check if user has enough units
- **getUsagePercentage()**: Calculate usage percentage

### 2. Webapp API Endpoints

**Updated `/api/extension/usage` route:**
- **POST**: Track usage with Cubent Units calculation
- **GET**: Return usage stats including Cubent Units

### 3. Extension Integration

**UsageSettings Component (`Cubent.dev/webview-ui/src/components/settings/UsageSettings.tsx`):**
- Simple usage display in extension settings
- Shows current month Cubent Units usage
- Progress bar with color-coded warnings
- Authentication required message for non-authenticated users

**Message Handler Updates:**
- `getUserUsageStats` now fetches from webapp API
- Real-time usage data from database

**Usage Tracking Utility (`Cubent.dev/src/core/user/trackApiUsage.ts`):**
- `trackApiUsage()`: Send usage data to webapp
- `canMakeRequest()`: Check usage limits before API calls

### 4. Webapp Usage Analytics

**Enhanced Profile Page:**
- Current month Cubent Units progress bar
- Usage warnings when approaching limits

**Detailed Usage Page (`/profile/usage`):**
- Cubent Units tracking in summary cards
- Current month usage overview with progress bar
- All-time usage statistics

## Model Pricing

Based on the official Cubent documentation, the system supports:

### Anthropic Claude Models
- Claude 3.7 Sonnet: 1.1 units
- Claude 3.7 Sonnet (Thinking): 1.35 units
- Claude 3.5 Sonnet: 0.95 units
- Claude 3.5 Haiku: 0.55 units
- Claude 3 Haiku: 0.45 units

### OpenAI Models
- GPT-4o: 1.1 units
- GPT-4.5 Preview: 1.2 units
- GPT-4o Mini: 0.65 units
- O3 Mini: 1.0 units
- O3 Mini (High Reasoning): 1.1 units
- O3 Mini (Low Reasoning): 0.75 units

### DeepSeek Models
- DeepSeek Chat: 0.35 units
- DeepSeek Reasoner: 0.7 units

### Google Gemini Models
- Gemini 2.5 Flash: 0.3 units
- Gemini 2.5 Flash (Thinking): 0.4 units
- Gemini 2.5 Pro: 0.85 units
- Gemini 2.0 Flash: 0.45 units
- Gemini 2.0 Pro: 0.70 units
- Gemini 1.5 Flash: 0.40 units
- Gemini 1.5 Pro: 0.65 units

### xAI Grok Models
- Grok 3: 1.1 units
- Grok 3 Mini: 0.30 units
- Grok 2 Vision: 0.70 units

## Usage Flow

1. **User makes API request** in extension
2. **Extension checks** if user can make request (`canMakeRequest()`)
3. **API request is made** if allowed
4. **Usage is tracked** via `trackApiUsage()` function
5. **Webapp receives usage data** and calculates Cubent Units
6. **Database is updated** with usage metrics
7. **User can view usage** in extension settings and webapp

## Current Limits

- **Default limit**: 50 Cubent Units per month per user
- **Warning threshold**: 90% usage
- **Color coding**: 
  - Green: < 75%
  - Yellow: 75-90%
  - Red: > 90%

## Authentication

- Users must be authenticated through the webapp to track usage
- Extension shows "Authentication Required" message for non-authenticated users
- Usage tracking is skipped for non-authenticated users

## Next Steps

To complete the implementation:

1. **Integrate tracking calls** in the extension's API request handlers
2. **Test the usage tracking** with real API calls
3. **Add monthly reset logic** for Cubent Units
4. **Implement subscription tier limits** (currently using fixed 50 units)
5. **Add usage alerts and notifications**
6. **Build and deploy** both extension and webapp changes

## Files Modified

### Webapp (Cubentweb)
- `packages/database/prisma/schema.prisma` - Database schema
- `packages/database/cubent-units.ts` - Cubent Units calculator
- `apps/app/app/api/extension/usage/route.ts` - API endpoints
- `apps/app/app/(authenticated)/profile/usage/page.tsx` - Usage analytics page
- `apps/app/components/usage/UsageOverview.tsx` - Usage overview component

### Extension (Cubent.dev)
- `webview-ui/src/components/settings/UsageSettings.tsx` - Usage settings component
- `webview-ui/src/components/settings/SettingsView.tsx` - Added usage tab
- `src/core/webview/webviewMessageHandler.ts` - Updated message handler
- `src/core/user/trackApiUsage.ts` - Usage tracking utility

### Database
- Migration: `20250620185600_add_cubent_units_tracking/migration.sql`
