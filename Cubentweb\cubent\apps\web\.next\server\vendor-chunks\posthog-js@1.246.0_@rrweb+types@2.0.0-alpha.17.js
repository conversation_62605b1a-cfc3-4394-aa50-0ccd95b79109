"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/posthog-js@1.246.0_@rrweb+types@2.0.0-alpha.17";
exports.ids = ["vendor-chunks/posthog-js@1.246.0_@rrweb+types@2.0.0-alpha.17"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/posthog-js@1.246.0_@rrweb+types@2.0.0-alpha.17/node_modules/posthog-js/dist/module.js":
/*!**********************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/posthog-js@1.246.0_@rrweb+types@2.0.0-alpha.17/node_modules/posthog-js/dist/module.js ***!
  \**********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   COPY_AUTOCAPTURE_EVENT: () => (/* binding */ f),\n/* harmony export */   Compression: () => (/* binding */ g),\n/* harmony export */   PostHog: () => (/* binding */ Yo),\n/* harmony export */   SurveyPosition: () => (/* binding */ Zo),\n/* harmony export */   SurveyQuestionBranchingType: () => (/* binding */ ea),\n/* harmony export */   SurveyQuestionType: () => (/* binding */ ia),\n/* harmony export */   SurveySchedule: () => (/* binding */ ra),\n/* harmony export */   SurveyType: () => (/* binding */ ta),\n/* harmony export */   SurveyWidgetType: () => (/* binding */ Qo),\n/* harmony export */   \"default\": () => (/* binding */ sa),\n/* harmony export */   knownUnsafeEditableEvent: () => (/* binding */ p),\n/* harmony export */   posthog: () => (/* binding */ sa),\n/* harmony export */   severityLevels: () => (/* binding */ _)\n/* harmony export */ });\nvar t=\"undefined\"!=typeof window?window:void 0,i=\"undefined\"!=typeof globalThis?globalThis:t,e=Array.prototype,r=e.forEach,s=e.indexOf,n=null==i?void 0:i.navigator,o=null==i?void 0:i.document,a=null==i?void 0:i.location,l=null==i?void 0:i.fetch,u=null!=i&&i.XMLHttpRequest&&\"withCredentials\"in new i.XMLHttpRequest?i.XMLHttpRequest:void 0,h=null==i?void 0:i.AbortController,d=null==n?void 0:n.userAgent,v=null!=t?t:{},c={DEBUG:!1,LIB_VERSION:\"1.246.0\"},f=\"$copy_autocapture\",p=[\"$snapshot\",\"$pageview\",\"$pageleave\",\"$set\",\"survey dismissed\",\"survey sent\",\"survey shown\",\"$identify\",\"$groupidentify\",\"$create_alias\",\"$$client_ingestion_warning\",\"$web_experiment_applied\",\"$feature_enrollment_update\",\"$feature_flag_called\"],g=function(t){return t.GZipJS=\"gzip-js\",t.Base64=\"base64\",t}({}),_=[\"fatal\",\"error\",\"warning\",\"log\",\"info\",\"debug\"];function m(t,i){return-1!==t.indexOf(i)}var b=function(t){return t.trim()},w=function(t){return t.replace(/^\\$/,\"\")};var y=Array.isArray,S=Object.prototype,$=S.hasOwnProperty,k=S.toString,x=y||function(t){return\"[object Array]\"===k.call(t)},E=t=>\"function\"==typeof t,I=t=>t===Object(t)&&!x(t),P=t=>{if(I(t)){for(var i in t)if($.call(t,i))return!1;return!0}return!1},R=t=>void 0===t,T=t=>\"[object String]\"==k.call(t),M=t=>T(t)&&0===t.trim().length,C=t=>null===t,O=t=>R(t)||C(t),F=t=>\"[object Number]\"==k.call(t),A=t=>\"[object Boolean]\"===k.call(t),D=t=>t instanceof FormData,L=t=>m(p,t),N=i=>{var e={t:function(e){if(t&&(c.DEBUG||v.POSTHOG_DEBUG)&&!R(t.console)&&t.console){for(var r=(\"__rrweb_original__\"in t.console[e]?t.console[e].__rrweb_original__:t.console[e]),s=arguments.length,n=new Array(s>1?s-1:0),o=1;o<s;o++)n[o-1]=arguments[o];r(i,...n)}},info:function(){for(var t=arguments.length,i=new Array(t),r=0;r<t;r++)i[r]=arguments[r];e.t(\"log\",...i)},warn:function(){for(var t=arguments.length,i=new Array(t),r=0;r<t;r++)i[r]=arguments[r];e.t(\"warn\",...i)},error:function(){for(var t=arguments.length,i=new Array(t),r=0;r<t;r++)i[r]=arguments[r];e.t(\"error\",...i)},critical:function(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];console.error(i,...e)},uninitializedWarning:t=>{e.error(\"You must initialize PostHog before calling \"+t)},createLogger:t=>N(i+\" \"+t)};return e},j=N(\"[PostHog.js]\"),z=j.createLogger,U=z(\"[ExternalScriptsLoader]\"),q=(t,i,e)=>{if(t.config.disable_external_dependency_loading)return U.warn(i+\" was requested but loading of external scripts is disabled.\"),e(\"Loading of external scripts is disabled\");var r=null==o?void 0:o.querySelectorAll(\"script\");if(r)for(var s=0;s<r.length;s++)if(r[s].src===i)return e();var n=()=>{if(!o)return e(\"document not found\");var r=o.createElement(\"script\");if(r.type=\"text/javascript\",r.crossOrigin=\"anonymous\",r.src=i,r.onload=t=>e(void 0,t),r.onerror=t=>e(t),t.config.prepare_external_dependency_script&&(r=t.config.prepare_external_dependency_script(r)),!r)return e(\"prepare_external_dependency_script returned null\");var s,n=o.querySelectorAll(\"body > script\");n.length>0?null==(s=n[0].parentNode)||s.insertBefore(r,n[0]):o.body.appendChild(r)};null!=o&&o.body?n():null==o||o.addEventListener(\"DOMContentLoaded\",n)};function B(){return B=Object.assign?Object.assign.bind():function(t){for(var i=1;i<arguments.length;i++){var e=arguments[i];for(var r in e)({}).hasOwnProperty.call(e,r)&&(t[r]=e[r])}return t},B.apply(null,arguments)}function H(t,i){if(null==t)return{};var e={};for(var r in t)if({}.hasOwnProperty.call(t,r)){if(-1!==i.indexOf(r))continue;e[r]=t[r]}return e}v.__PosthogExtensions__=v.__PosthogExtensions__||{},v.__PosthogExtensions__.loadExternalDependency=(t,i,e)=>{var r=\"/static/\"+i+\".js?v=\"+t.version;if(\"remote-config\"===i&&(r=\"/array/\"+t.config.token+\"/config.js\"),\"toolbar\"===i){var s=3e5;r=r+\"&t=\"+Math.floor(Date.now()/s)*s}var n=t.requestRouter.endpointFor(\"assets\",r);q(t,n,e)},v.__PosthogExtensions__.loadSiteApp=(t,i,e)=>{var r=t.requestRouter.endpointFor(\"api\",i);q(t,r,e)};var W={};function G(t,i,e){if(x(t))if(r&&t.forEach===r)t.forEach(i,e);else if(\"length\"in t&&t.length===+t.length)for(var s=0,n=t.length;s<n;s++)if(s in t&&i.call(e,t[s],s)===W)return}function J(t,i,e){if(!O(t)){if(x(t))return G(t,i,e);if(D(t)){for(var r of t.entries())if(i.call(e,r[1],r[0])===W)return}else for(var s in t)if($.call(t,s)&&i.call(e,t[s],s)===W)return}}var V=function(t){for(var i=arguments.length,e=new Array(i>1?i-1:0),r=1;r<i;r++)e[r-1]=arguments[r];return G(e,(function(i){for(var e in i)void 0!==i[e]&&(t[e]=i[e])})),t},K=function(t){for(var i=arguments.length,e=new Array(i>1?i-1:0),r=1;r<i;r++)e[r-1]=arguments[r];return G(e,(function(i){G(i,(function(i){t.push(i)}))})),t};function Y(t){for(var i=Object.keys(t),e=i.length,r=new Array(e);e--;)r[e]=[i[e],t[i[e]]];return r}var X=function(t){try{return t()}catch(t){return}},Q=function(t){return function(){try{for(var i=arguments.length,e=new Array(i),r=0;r<i;r++)e[r]=arguments[r];return t.apply(this,e)}catch(t){j.critical(\"Implementation error. Please turn on debug mode and open a ticket on https://app.posthog.com/home#panel=support%3Asupport%3A.\"),j.critical(t)}}},Z=function(t){var i={};return J(t,(function(t,e){(T(t)&&t.length>0||F(t))&&(i[e]=t)})),i};function tt(t,i){return e=t,r=t=>T(t)&&!C(i)?t.slice(0,i):t,s=new Set,function t(i,e){return i!==Object(i)?r?r(i,e):i:s.has(i)?void 0:(s.add(i),x(i)?(n=[],G(i,(i=>{n.push(t(i))}))):(n={},J(i,((i,e)=>{s.has(i)||(n[e]=t(i,e))}))),n);var n}(e);var e,r,s}var it=[\"herokuapp.com\",\"vercel.app\",\"netlify.app\"];function et(t){var i=null==t?void 0:t.hostname;if(!T(i))return!1;var e=i.split(\".\").slice(-2).join(\".\");for(var r of it)if(e===r)return!1;return!0}function rt(t,i){for(var e=0;e<t.length;e++)if(i(t[e]))return t[e]}function st(t,i,e,r){var{capture:s=!1,passive:n=!0}=null!=r?r:{};null==t||t.addEventListener(i,e,{capture:s,passive:n})}var nt=\"$people_distinct_id\",ot=\"__alias\",at=\"__timers\",lt=\"$autocapture_disabled_server_side\",ut=\"$heatmaps_enabled_server_side\",ht=\"$exception_capture_enabled_server_side\",dt=\"$error_tracking_suppression_rules\",vt=\"$web_vitals_enabled_server_side\",ct=\"$dead_clicks_enabled_server_side\",ft=\"$web_vitals_allowed_metrics\",pt=\"$session_recording_enabled_server_side\",gt=\"$console_log_recording_enabled_server_side\",_t=\"$session_recording_network_payload_capture\",mt=\"$session_recording_masking\",bt=\"$session_recording_canvas_recording\",wt=\"$replay_sample_rate\",yt=\"$replay_minimum_duration\",St=\"$replay_script_config\",$t=\"$sesid\",kt=\"$session_is_sampled\",xt=\"$session_recording_url_trigger_activated_session\",Et=\"$session_recording_event_trigger_activated_session\",It=\"$enabled_feature_flags\",Pt=\"$early_access_features\",Rt=\"$feature_flag_details\",Tt=\"$stored_person_properties\",Mt=\"$stored_group_properties\",Ct=\"$surveys\",Ot=\"$surveys_activated\",Ft=\"$flag_call_reported\",At=\"$user_state\",Dt=\"$client_session_props\",Lt=\"$capture_rate_limit\",Nt=\"$initial_campaign_params\",jt=\"$initial_referrer_info\",zt=\"$initial_person_info\",Ut=\"$epp\",qt=\"__POSTHOG_TOOLBAR__\",Bt=\"$posthog_cookieless\",Ht=[nt,ot,\"__cmpns\",at,pt,ut,$t,It,dt,At,Pt,Rt,Mt,Tt,Ct,Ft,Dt,Lt,Nt,jt,Ut,zt];function Wt(t){return t instanceof Element&&(t.id===qt||!(null==t.closest||!t.closest(\".toolbar-global-fade-container\")))}function Gt(t){return!!t&&1===t.nodeType}function Jt(t,i){return!!t&&!!t.tagName&&t.tagName.toLowerCase()===i.toLowerCase()}function Vt(t){return!!t&&3===t.nodeType}function Kt(t){return!!t&&11===t.nodeType}function Yt(t){return t?b(t).split(/\\s+/):[]}function Xt(i){var e=null==t?void 0:t.location.href;return!!(e&&i&&i.some((t=>e.match(t))))}function Qt(t){var i=\"\";switch(typeof t.className){case\"string\":i=t.className;break;case\"object\":i=(t.className&&\"baseVal\"in t.className?t.className.baseVal:null)||t.getAttribute(\"class\")||\"\";break;default:i=\"\"}return Yt(i)}function Zt(t){return O(t)?null:b(t).split(/(\\s+)/).filter((t=>ci(t))).join(\"\").replace(/[\\r\\n]/g,\" \").replace(/[ ]+/g,\" \").substring(0,255)}function ti(t){var i=\"\";return ni(t)&&!oi(t)&&t.childNodes&&t.childNodes.length&&J(t.childNodes,(function(t){var e;Vt(t)&&t.textContent&&(i+=null!==(e=Zt(t.textContent))&&void 0!==e?e:\"\")})),b(i)}function ii(t){return R(t.target)?t.srcElement||null:null!=(i=t.target)&&i.shadowRoot?t.composedPath()[0]||null:t.target||null;var i}var ei=[\"a\",\"button\",\"form\",\"input\",\"select\",\"textarea\",\"label\"];function ri(t){var i=t.parentNode;return!(!i||!Gt(i))&&i}function si(i,e,r,s,n){var o,a,l;if(void 0===r&&(r=void 0),!t||!i||Jt(i,\"html\")||!Gt(i))return!1;if(null!=(o=r)&&o.url_allowlist&&!Xt(r.url_allowlist))return!1;if(null!=(a=r)&&a.url_ignorelist&&Xt(r.url_ignorelist))return!1;if(null!=(l=r)&&l.dom_event_allowlist){var u=r.dom_event_allowlist;if(u&&!u.some((t=>e.type===t)))return!1}for(var h=!1,d=[i],v=!0,c=i;c.parentNode&&!Jt(c,\"body\");)if(Kt(c.parentNode))d.push(c.parentNode.host),c=c.parentNode.host;else{if(!(v=ri(c)))break;if(s||ei.indexOf(v.tagName.toLowerCase())>-1)h=!0;else{var f=t.getComputedStyle(v);f&&\"pointer\"===f.getPropertyValue(\"cursor\")&&(h=!0)}d.push(v),c=v}if(!function(t,i){var e=null==i?void 0:i.element_allowlist;if(R(e))return!0;var r,s=function(t){if(e.some((i=>t.tagName.toLowerCase()===i)))return{v:!0}};for(var n of t)if(r=s(n))return r.v;return!1}(d,r))return!1;if(!function(t,i){var e=null==i?void 0:i.css_selector_allowlist;if(R(e))return!0;var r,s=function(t){if(e.some((i=>t.matches(i))))return{v:!0}};for(var n of t)if(r=s(n))return r.v;return!1}(d,r))return!1;var p=t.getComputedStyle(i);if(p&&\"pointer\"===p.getPropertyValue(\"cursor\")&&\"click\"===e.type)return!0;var g=i.tagName.toLowerCase();switch(g){case\"html\":return!1;case\"form\":return(n||[\"submit\"]).indexOf(e.type)>=0;case\"input\":case\"select\":case\"textarea\":return(n||[\"change\",\"click\"]).indexOf(e.type)>=0;default:return h?(n||[\"click\"]).indexOf(e.type)>=0:(n||[\"click\"]).indexOf(e.type)>=0&&(ei.indexOf(g)>-1||\"true\"===i.getAttribute(\"contenteditable\"))}}function ni(t){for(var i=t;i.parentNode&&!Jt(i,\"body\");i=i.parentNode){var e=Qt(i);if(m(e,\"ph-sensitive\")||m(e,\"ph-no-capture\"))return!1}if(m(Qt(t),\"ph-include\"))return!0;var r=t.type||\"\";if(T(r))switch(r.toLowerCase()){case\"hidden\":case\"password\":return!1}var s=t.name||t.id||\"\";if(T(s)){if(/^cc|cardnum|ccnum|creditcard|csc|cvc|cvv|exp|pass|pwd|routing|seccode|securitycode|securitynum|socialsec|socsec|ssn/i.test(s.replace(/[^a-zA-Z0-9]/g,\"\")))return!1}return!0}function oi(t){return!!(Jt(t,\"input\")&&![\"button\",\"checkbox\",\"submit\",\"reset\"].includes(t.type)||Jt(t,\"select\")||Jt(t,\"textarea\")||\"true\"===t.getAttribute(\"contenteditable\"))}var ai=\"(4[0-9]{12}(?:[0-9]{3})?)|(5[1-5][0-9]{14})|(6(?:011|5[0-9]{2})[0-9]{12})|(3[47][0-9]{13})|(3(?:0[0-5]|[68][0-9])[0-9]{11})|((?:2131|1800|35[0-9]{3})[0-9]{11})\",li=new RegExp(\"^(?:\"+ai+\")$\"),ui=new RegExp(ai),hi=\"\\\\d{3}-?\\\\d{2}-?\\\\d{4}\",di=new RegExp(\"^(\"+hi+\")$\"),vi=new RegExp(\"(\"+hi+\")\");function ci(t,i){if(void 0===i&&(i=!0),O(t))return!1;if(T(t)){if(t=b(t),(i?li:ui).test((t||\"\").replace(/[- ]/g,\"\")))return!1;if((i?di:vi).test(t))return!1}return!0}function fi(t){var i=ti(t);return ci(i=(i+\" \"+pi(t)).trim())?i:\"\"}function pi(t){var i=\"\";return t&&t.childNodes&&t.childNodes.length&&J(t.childNodes,(function(t){var e;if(t&&\"span\"===(null==(e=t.tagName)?void 0:e.toLowerCase()))try{var r=ti(t);i=(i+\" \"+r).trim(),t.childNodes&&t.childNodes.length&&(i=(i+\" \"+pi(t)).trim())}catch(t){j.error(\"[AutoCapture]\",t)}})),i}function gi(t){return function(t){var i=t.map((t=>{var i,e,r=\"\";if(t.tag_name&&(r+=t.tag_name),t.attr_class)for(var s of(t.attr_class.sort(),t.attr_class))r+=\".\"+s.replace(/\"/g,\"\");var n=B({},t.text?{text:t.text}:{},{\"nth-child\":null!==(i=t.nth_child)&&void 0!==i?i:0,\"nth-of-type\":null!==(e=t.nth_of_type)&&void 0!==e?e:0},t.href?{href:t.href}:{},t.attr_id?{attr_id:t.attr_id}:{},t.attributes),o={};return Y(n).sort(((t,i)=>{var[e]=t,[r]=i;return e.localeCompare(r)})).forEach((t=>{var[i,e]=t;return o[_i(i.toString())]=_i(e.toString())})),r+=\":\",r+=Y(o).map((t=>{var[i,e]=t;return i+'=\"'+e+'\"'})).join(\"\")}));return i.join(\";\")}(function(t){return t.map((t=>{var i,e,r={text:null==(i=t.$el_text)?void 0:i.slice(0,400),tag_name:t.tag_name,href:null==(e=t.attr__href)?void 0:e.slice(0,2048),attr_class:mi(t),attr_id:t.attr__id,nth_child:t.nth_child,nth_of_type:t.nth_of_type,attributes:{}};return Y(t).filter((t=>{var[i]=t;return 0===i.indexOf(\"attr__\")})).forEach((t=>{var[i,e]=t;return r.attributes[i]=e})),r}))}(t))}function _i(t){return t.replace(/\"|\\\\\"/g,'\\\\\"')}function mi(t){var i=t.attr__class;return i?x(i)?i:Yt(i):void 0}class bi{constructor(){this.clicks=[]}isRageClick(t,i,e){var r=this.clicks[this.clicks.length-1];if(r&&Math.abs(t-r.x)+Math.abs(i-r.y)<30&&e-r.timestamp<1e3){if(this.clicks.push({x:t,y:i,timestamp:e}),3===this.clicks.length)return!0}else this.clicks=[{x:t,y:i,timestamp:e}];return!1}}var wi=[\"localhost\",\"127.0.0.1\"],yi=t=>{var i=null==o?void 0:o.createElement(\"a\");return R(i)?null:(i.href=t,i)},Si=function(t,i){var e,r;void 0===i&&(i=\"&\");var s=[];return J(t,(function(t,i){R(t)||R(i)||\"undefined\"===i||(e=encodeURIComponent((t=>t instanceof File)(t)?t.name:t.toString()),r=encodeURIComponent(i),s[s.length]=r+\"=\"+e)})),s.join(i)},$i=function(t,i){for(var e,r=((t.split(\"#\")[0]||\"\").split(/\\?(.*)/)[1]||\"\").replace(/^\\?+/g,\"\").split(\"&\"),s=0;s<r.length;s++){var n=r[s].split(\"=\");if(n[0]===i){e=n;break}}if(!x(e)||e.length<2)return\"\";var o=e[1];try{o=decodeURIComponent(o)}catch(t){j.error(\"Skipping decoding for malformed query param: \"+o)}return o.replace(/\\+/g,\" \")},ki=function(t,i,e){if(!t||!i||!i.length)return t;for(var r=t.split(\"#\"),s=r[0]||\"\",n=r[1],o=s.split(\"?\"),a=o[1],l=o[0],u=(a||\"\").split(\"&\"),h=[],d=0;d<u.length;d++){var v=u[d].split(\"=\");x(v)&&(i.includes(v[0])?h.push(v[0]+\"=\"+e):h.push(u[d]))}var c=l;return null!=a&&(c+=\"?\"+h.join(\"&\")),null!=n&&(c+=\"#\"+n),c},xi=function(t,i){var e=t.match(new RegExp(i+\"=([^&]*)\"));return e?e[1]:null},Ei=z(\"[AutoCapture]\");function Ii(t,i){return i.length>t?i.slice(0,t)+\"...\":i}function Pi(t){if(t.previousElementSibling)return t.previousElementSibling;var i=t;do{i=i.previousSibling}while(i&&!Gt(i));return i}function Ri(t,i,e,r){var s=t.tagName.toLowerCase(),n={tag_name:s};ei.indexOf(s)>-1&&!e&&(\"a\"===s.toLowerCase()||\"button\"===s.toLowerCase()?n.$el_text=Ii(1024,fi(t)):n.$el_text=Ii(1024,ti(t)));var o=Qt(t);o.length>0&&(n.classes=o.filter((function(t){return\"\"!==t}))),J(t.attributes,(function(e){var s;if((!oi(t)||-1!==[\"name\",\"id\",\"class\",\"aria-label\"].indexOf(e.name))&&((null==r||!r.includes(e.name))&&!i&&ci(e.value)&&(s=e.name,!T(s)||\"_ngcontent\"!==s.substring(0,10)&&\"_nghost\"!==s.substring(0,7)))){var o=e.value;\"class\"===e.name&&(o=Yt(o).join(\" \")),n[\"attr__\"+e.name]=Ii(1024,o)}}));for(var a=1,l=1,u=t;u=Pi(u);)a++,u.tagName===t.tagName&&l++;return n.nth_child=a,n.nth_of_type=l,n}function Ti(i,e){for(var r,s,{e:n,maskAllElementAttributes:o,maskAllText:a,elementAttributeIgnoreList:l,elementsChainAsString:u}=e,h=[i],d=i;d.parentNode&&!Jt(d,\"body\");)Kt(d.parentNode)?(h.push(d.parentNode.host),d=d.parentNode.host):(h.push(d.parentNode),d=d.parentNode);var v,c=[],f={},p=!1,g=!1;if(J(h,(t=>{var i=ni(t);\"a\"===t.tagName.toLowerCase()&&(p=t.getAttribute(\"href\"),p=i&&p&&ci(p)&&p),m(Qt(t),\"ph-no-capture\")&&(g=!0),c.push(Ri(t,o,a,l));var e=function(t){if(!ni(t))return{};var i={};return J(t.attributes,(function(t){if(t.name&&0===t.name.indexOf(\"data-ph-capture-attribute\")){var e=t.name.replace(\"data-ph-capture-attribute-\",\"\"),r=t.value;e&&r&&ci(r)&&(i[e]=r)}})),i}(t);V(f,e)})),g)return{props:{},explicitNoCapture:g};if(a||(\"a\"===i.tagName.toLowerCase()||\"button\"===i.tagName.toLowerCase()?c[0].$el_text=fi(i):c[0].$el_text=ti(i)),p){var _,b;c[0].attr__href=p;var w=null==(_=yi(p))?void 0:_.host,y=null==t||null==(b=t.location)?void 0:b.host;w&&y&&w!==y&&(v=p)}return{props:V({$event_type:n.type,$ce_version:1},u?{}:{$elements:c},{$elements_chain:gi(c)},null!=(r=c[0])&&r.$el_text?{$el_text:null==(s=c[0])?void 0:s.$el_text}:{},v&&\"click\"===n.type?{$external_click_url:v}:{},f)}}class Mi{constructor(t){this.i=!1,this.o=null,this.rageclicks=new bi,this.h=!1,this.instance=t,this.m=null}get S(){var t,i,e=I(this.instance.config.autocapture)?this.instance.config.autocapture:{};return e.url_allowlist=null==(t=e.url_allowlist)?void 0:t.map((t=>new RegExp(t))),e.url_ignorelist=null==(i=e.url_ignorelist)?void 0:i.map((t=>new RegExp(t))),e}$(){if(this.isBrowserSupported()){if(t&&o){var i=i=>{i=i||(null==t?void 0:t.event);try{this.k(i)}catch(t){Ei.error(\"Failed to capture event\",t)}};if(st(o,\"submit\",i,{capture:!0}),st(o,\"change\",i,{capture:!0}),st(o,\"click\",i,{capture:!0}),this.S.capture_copied_text){var e=i=>{i=i||(null==t?void 0:t.event),this.k(i,f)};st(o,\"copy\",e,{capture:!0}),st(o,\"cut\",e,{capture:!0})}}}else Ei.info(\"Disabling Automatic Event Collection because this browser is not supported\")}startIfEnabled(){this.isEnabled&&!this.i&&(this.$(),this.i=!0)}onRemoteConfig(t){t.elementsChainAsString&&(this.h=t.elementsChainAsString),this.instance.persistence&&this.instance.persistence.register({[lt]:!!t.autocapture_opt_out}),this.o=!!t.autocapture_opt_out,this.startIfEnabled()}setElementSelectors(t){this.m=t}getElementSelectors(t){var i,e=[];return null==(i=this.m)||i.forEach((i=>{var r=null==o?void 0:o.querySelectorAll(i);null==r||r.forEach((r=>{t===r&&e.push(i)}))})),e}get isEnabled(){var t,i,e=null==(t=this.instance.persistence)?void 0:t.props[lt],r=this.o;if(C(r)&&!A(e)&&!this.instance.config.advanced_disable_decide)return!1;var s=null!==(i=this.o)&&void 0!==i?i:!!e;return!!this.instance.config.autocapture&&!s}k(i,e){if(void 0===e&&(e=\"$autocapture\"),this.isEnabled){var r,s=ii(i);if(Vt(s)&&(s=s.parentNode||null),\"$autocapture\"===e&&\"click\"===i.type&&i instanceof MouseEvent)this.instance.config.rageclick&&null!=(r=this.rageclicks)&&r.isRageClick(i.clientX,i.clientY,(new Date).getTime())&&this.k(i,\"$rageclick\");var n=e===f;if(s&&si(s,i,this.S,n,n?[\"copy\",\"cut\"]:void 0)){var{props:o,explicitNoCapture:a}=Ti(s,{e:i,maskAllElementAttributes:this.instance.config.mask_all_element_attributes,maskAllText:this.instance.config.mask_all_text,elementAttributeIgnoreList:this.S.element_attribute_ignorelist,elementsChainAsString:this.h});if(a)return!1;var l=this.getElementSelectors(s);if(l&&l.length>0&&(o.$element_selectors=l),e===f){var u,h=Zt(null==t||null==(u=t.getSelection())?void 0:u.toString()),d=i.type||\"clipboard\";if(!h)return!1;o.$selected_content=h,o.$copy_type=d}return this.instance.capture(e,o),!0}}}isBrowserSupported(){return E(null==o?void 0:o.querySelectorAll)}}Math.trunc||(Math.trunc=function(t){return t<0?Math.ceil(t):Math.floor(t)}),Number.isInteger||(Number.isInteger=function(t){return F(t)&&isFinite(t)&&Math.floor(t)===t});var Ci=\"0123456789abcdef\";class Oi{constructor(t){if(this.bytes=t,16!==t.length)throw new TypeError(\"not 128-bit length\")}static fromFieldsV7(t,i,e,r){if(!Number.isInteger(t)||!Number.isInteger(i)||!Number.isInteger(e)||!Number.isInteger(r)||t<0||i<0||e<0||r<0||t>0xffffffffffff||i>4095||e>1073741823||r>4294967295)throw new RangeError(\"invalid field value\");var s=new Uint8Array(16);return s[0]=t/Math.pow(2,40),s[1]=t/Math.pow(2,32),s[2]=t/Math.pow(2,24),s[3]=t/Math.pow(2,16),s[4]=t/Math.pow(2,8),s[5]=t,s[6]=112|i>>>8,s[7]=i,s[8]=128|e>>>24,s[9]=e>>>16,s[10]=e>>>8,s[11]=e,s[12]=r>>>24,s[13]=r>>>16,s[14]=r>>>8,s[15]=r,new Oi(s)}toString(){for(var t=\"\",i=0;i<this.bytes.length;i++)t=t+Ci.charAt(this.bytes[i]>>>4)+Ci.charAt(15&this.bytes[i]),3!==i&&5!==i&&7!==i&&9!==i||(t+=\"-\");if(36!==t.length)throw new Error(\"Invalid UUIDv7 was generated\");return t}clone(){return new Oi(this.bytes.slice(0))}equals(t){return 0===this.compareTo(t)}compareTo(t){for(var i=0;i<16;i++){var e=this.bytes[i]-t.bytes[i];if(0!==e)return Math.sign(e)}return 0}}class Fi{constructor(){this.I=0,this.P=0,this.R=new Li}generate(){var t=this.generateOrAbort();if(R(t)){this.I=0;var i=this.generateOrAbort();if(R(i))throw new Error(\"Could not generate UUID after timestamp reset\");return i}return t}generateOrAbort(){var t=Date.now();if(t>this.I)this.I=t,this.T();else{if(!(t+1e4>this.I))return;this.P++,this.P>4398046511103&&(this.I++,this.T())}return Oi.fromFieldsV7(this.I,Math.trunc(this.P/Math.pow(2,30)),this.P&Math.pow(2,30)-1,this.R.nextUint32())}T(){this.P=1024*this.R.nextUint32()+(1023&this.R.nextUint32())}}var Ai,Di=t=>{if(\"undefined\"!=typeof UUIDV7_DENY_WEAK_RNG&&UUIDV7_DENY_WEAK_RNG)throw new Error(\"no cryptographically strong RNG available\");for(var i=0;i<t.length;i++)t[i]=65536*Math.trunc(65536*Math.random())+Math.trunc(65536*Math.random());return t};t&&!R(t.crypto)&&crypto.getRandomValues&&(Di=t=>crypto.getRandomValues(t));class Li{constructor(){this.M=new Uint32Array(8),this.C=1/0}nextUint32(){return this.C>=this.M.length&&(Di(this.M),this.C=0),this.M[this.C++]}}var Ni=()=>ji().toString(),ji=()=>(Ai||(Ai=new Fi)).generate(),zi=\"\";var Ui=/[a-z0-9][a-z0-9-]+\\.[a-z]{2,}$/i;function qi(t,i){if(i){var e=function(t,i){if(void 0===i&&(i=o),zi)return zi;if(!i)return\"\";if([\"localhost\",\"127.0.0.1\"].includes(t))return\"\";for(var e=t.split(\".\"),r=Math.min(e.length,8),s=\"dmn_chk_\"+Ni();!zi&&r--;){var n=e.slice(r).join(\".\"),a=s+\"=1;domain=.\"+n+\";path=/\";i.cookie=a+\";max-age=3\",i.cookie.includes(s)&&(i.cookie=a+\";max-age=0\",zi=n)}return zi}(t);if(!e){var r=(t=>{var i=t.match(Ui);return i?i[0]:\"\"})(t);r!==e&&j.info(\"Warning: cookie subdomain discovery mismatch\",r,e),e=r}return e?\"; domain=.\"+e:\"\"}return\"\"}var Bi={O:()=>!!o,F:function(t){j.error(\"cookieStore error: \"+t)},A:function(t){if(o){try{for(var i=t+\"=\",e=o.cookie.split(\";\").filter((t=>t.length)),r=0;r<e.length;r++){for(var s=e[r];\" \"==s.charAt(0);)s=s.substring(1,s.length);if(0===s.indexOf(i))return decodeURIComponent(s.substring(i.length,s.length))}}catch(t){}return null}},D:function(t){var i;try{i=JSON.parse(Bi.A(t))||{}}catch(t){}return i},L:function(t,i,e,r,s){if(o)try{var n=\"\",a=\"\",l=qi(o.location.hostname,r);if(e){var u=new Date;u.setTime(u.getTime()+24*e*60*60*1e3),n=\"; expires=\"+u.toUTCString()}s&&(a=\"; secure\");var h=t+\"=\"+encodeURIComponent(JSON.stringify(i))+n+\"; SameSite=Lax; path=/\"+l+a;return h.length>3686.4&&j.warn(\"cookieStore warning: large cookie, len=\"+h.length),o.cookie=h,h}catch(t){return}},N:function(t,i){try{Bi.L(t,\"\",-1,i)}catch(t){return}}},Hi=null,Wi={O:function(){if(!C(Hi))return Hi;var i=!0;if(R(t))i=!1;else try{var e=\"__mplssupport__\";Wi.L(e,\"xyz\"),'\"xyz\"'!==Wi.A(e)&&(i=!1),Wi.N(e)}catch(t){i=!1}return i||j.error(\"localStorage unsupported; falling back to cookie store\"),Hi=i,i},F:function(t){j.error(\"localStorage error: \"+t)},A:function(i){try{return null==t?void 0:t.localStorage.getItem(i)}catch(t){Wi.F(t)}return null},D:function(t){try{return JSON.parse(Wi.A(t))||{}}catch(t){}return null},L:function(i,e){try{null==t||t.localStorage.setItem(i,JSON.stringify(e))}catch(t){Wi.F(t)}},N:function(i){try{null==t||t.localStorage.removeItem(i)}catch(t){Wi.F(t)}}},Gi=[\"distinct_id\",$t,kt,Ut,zt],Ji=B({},Wi,{D:function(t){try{var i={};try{i=Bi.D(t)||{}}catch(t){}var e=V(i,JSON.parse(Wi.A(t)||\"{}\"));return Wi.L(t,e),e}catch(t){}return null},L:function(t,i,e,r,s,n){try{Wi.L(t,i,void 0,void 0,n);var o={};Gi.forEach((t=>{i[t]&&(o[t]=i[t])})),Object.keys(o).length&&Bi.L(t,o,e,r,s,n)}catch(t){Wi.F(t)}},N:function(i,e){try{null==t||t.localStorage.removeItem(i),Bi.N(i,e)}catch(t){Wi.F(t)}}}),Vi={},Ki={O:function(){return!0},F:function(t){j.error(\"memoryStorage error: \"+t)},A:function(t){return Vi[t]||null},D:function(t){return Vi[t]||null},L:function(t,i){Vi[t]=i},N:function(t){delete Vi[t]}},Yi=null,Xi={O:function(){if(!C(Yi))return Yi;if(Yi=!0,R(t))Yi=!1;else try{var i=\"__support__\";Xi.L(i,\"xyz\"),'\"xyz\"'!==Xi.A(i)&&(Yi=!1),Xi.N(i)}catch(t){Yi=!1}return Yi},F:function(t){j.error(\"sessionStorage error: \",t)},A:function(i){try{return null==t?void 0:t.sessionStorage.getItem(i)}catch(t){Xi.F(t)}return null},D:function(t){try{return JSON.parse(Xi.A(t))||null}catch(t){}return null},L:function(i,e){try{null==t||t.sessionStorage.setItem(i,JSON.stringify(e))}catch(t){Xi.F(t)}},N:function(i){try{null==t||t.sessionStorage.removeItem(i)}catch(t){Xi.F(t)}}},Qi=function(t){return t[t.PENDING=-1]=\"PENDING\",t[t.DENIED=0]=\"DENIED\",t[t.GRANTED=1]=\"GRANTED\",t}({});class Zi{constructor(t){this._instance=t}get S(){return this._instance.config}get consent(){return this.j()?Qi.DENIED:this.U}isOptedOut(){return this.consent===Qi.DENIED||this.consent===Qi.PENDING&&this.S.opt_out_capturing_by_default}isOptedIn(){return!this.isOptedOut()}optInOut(t){this.q.L(this.B,t?1:0,this.S.cookie_expiration,this.S.cross_subdomain_cookie,this.S.secure_cookie)}reset(){this.q.N(this.B,this.S.cross_subdomain_cookie)}get B(){var{token:t,opt_out_capturing_cookie_prefix:i}=this._instance.config;return(i||\"__ph_opt_in_out_\")+t}get U(){var t=this.q.A(this.B);return\"1\"===t?Qi.GRANTED:\"0\"===t?Qi.DENIED:Qi.PENDING}get q(){if(!this.H){var t=this.S.opt_out_capturing_persistence_type;this.H=\"localStorage\"===t?Wi:Bi;var i=\"localStorage\"===t?Bi:Wi;i.A(this.B)&&(this.H.A(this.B)||this.optInOut(\"1\"===i.A(this.B)),i.N(this.B,this.S.cross_subdomain_cookie))}return this.H}j(){return!!this.S.respect_dnt&&!!rt([null==n?void 0:n.doNotTrack,null==n?void 0:n.msDoNotTrack,v.doNotTrack],(t=>m([!0,1,\"1\",\"yes\"],t)))}}var te=z(\"[Dead Clicks]\"),ie=()=>!0,ee=t=>{var i,e=!(null==(i=t.instance.persistence)||!i.get_property(ct)),r=t.instance.config.capture_dead_clicks;return A(r)?r:e};class re{get lazyLoadedDeadClicksAutocapture(){return this.W}constructor(t,i,e){this.instance=t,this.isEnabled=i,this.onCapture=e,this.startIfEnabled()}onRemoteConfig(t){this.instance.persistence&&this.instance.persistence.register({[ct]:null==t?void 0:t.captureDeadClicks}),this.startIfEnabled()}startIfEnabled(){this.isEnabled(this)&&this.G((()=>{this.J()}))}G(t){var i,e;null!=(i=v.__PosthogExtensions__)&&i.initDeadClicksAutocapture&&t(),null==(e=v.__PosthogExtensions__)||null==e.loadExternalDependency||e.loadExternalDependency(this.instance,\"dead-clicks-autocapture\",(i=>{i?te.error(\"failed to load script\",i):t()}))}J(){var t;if(o){if(!this.W&&null!=(t=v.__PosthogExtensions__)&&t.initDeadClicksAutocapture){var i=I(this.instance.config.capture_dead_clicks)?this.instance.config.capture_dead_clicks:{};i.__onCapture=this.onCapture,this.W=v.__PosthogExtensions__.initDeadClicksAutocapture(this.instance,i),this.W.start(o),te.info(\"starting...\")}}else te.error(\"`document` not found. Cannot start.\")}stop(){this.W&&(this.W.stop(),this.W=void 0,te.info(\"stopping...\"))}}var se=z(\"[ExceptionAutocapture]\");class ne{constructor(i){var e;this.V=()=>{var i;if(t&&this.isEnabled&&null!=(i=v.__PosthogExtensions__)&&i.errorWrappingFunctions){var e=v.__PosthogExtensions__.errorWrappingFunctions.wrapOnError,r=v.__PosthogExtensions__.errorWrappingFunctions.wrapUnhandledRejection,s=v.__PosthogExtensions__.errorWrappingFunctions.wrapConsoleError;try{!this.K&&this.S.capture_unhandled_errors&&(this.K=e(this.captureException.bind(this))),!this.Y&&this.S.capture_unhandled_rejections&&(this.Y=r(this.captureException.bind(this))),!this.X&&this.S.capture_console_errors&&(this.X=s(this.captureException.bind(this)))}catch(t){se.error(\"failed to start\",t),this.Z()}}},this._instance=i,this.tt=!(null==(e=this._instance.persistence)||!e.props[ht]),this.S=this.it(),this.startIfEnabled()}it(){var t=this._instance.config.capture_exceptions,i={capture_unhandled_errors:!1,capture_unhandled_rejections:!1,capture_console_errors:!1};return I(t)?i=B({},i,t):(R(t)?this.tt:t)&&(i=B({},i,{capture_unhandled_errors:!0,capture_unhandled_rejections:!0})),i}get isEnabled(){return this.S.capture_console_errors||this.S.capture_unhandled_errors||this.S.capture_unhandled_rejections}startIfEnabled(){this.isEnabled&&(se.info(\"enabled\"),this.G(this.V))}G(t){var i,e;null!=(i=v.__PosthogExtensions__)&&i.errorWrappingFunctions&&t(),null==(e=v.__PosthogExtensions__)||null==e.loadExternalDependency||e.loadExternalDependency(this._instance,\"exception-autocapture\",(i=>{if(i)return se.error(\"failed to load script\",i);t()}))}Z(){var t,i,e;null==(t=this.K)||t.call(this),this.K=void 0,null==(i=this.Y)||i.call(this),this.Y=void 0,null==(e=this.X)||e.call(this),this.X=void 0}onRemoteConfig(t){var i=t.autocaptureExceptions;this.tt=!!i||!1,this.S=this.it(),this._instance.persistence&&this._instance.persistence.register({[ht]:this.tt}),this.startIfEnabled()}captureException(t){var i=this._instance.requestRouter.endpointFor(\"ui\");t.$exception_personURL=i+\"/project/\"+this._instance.config.token+\"/person/\"+this._instance.get_distinct_id(),this._instance.exceptions.sendExceptionEvent(t)}}function oe(t){return!R(Event)&&ae(t,Event)}function ae(t,i){try{return t instanceof i}catch(t){return!1}}function le(t){switch(Object.prototype.toString.call(t)){case\"[object Error]\":case\"[object Exception]\":case\"[object DOMException]\":case\"[object DOMError]\":return!0;default:return ae(t,Error)}}function ue(t,i){return Object.prototype.toString.call(t)===\"[object \"+i+\"]\"}function he(t){return ue(t,\"DOMError\")}var de=/\\(error: (.*)\\)/,ve=50,ce=\"?\";function fe(t,i,e,r){var s={platform:\"web:javascript\",filename:t,function:\"<anonymous>\"===i?ce:i,in_app:!0};return R(e)||(s.lineno=e),R(r)||(s.colno=r),s}var pe=/^\\s*at (\\S+?)(?::(\\d+))(?::(\\d+))\\s*$/i,ge=/^\\s*at (?:(.+?\\)(?: \\[.+\\])?|.*?) ?\\((?:address at )?)?(?:async )?((?:<anonymous>|[-a-z]+:|.*bundle|\\/)?.*?)(?::(\\d+))?(?::(\\d+))?\\)?\\s*$/i,_e=/\\((\\S*)(?::(\\d+))(?::(\\d+))\\)/,me=/^\\s*(.*?)(?:\\((.*?)\\))?(?:^|@)?((?:[-a-z]+)?:\\/.*?|\\[native code\\]|[^@]*(?:bundle|\\d+\\.js)|\\/[\\w\\-. /=]+)(?::(\\d+))?(?::(\\d+))?\\s*$/i,be=/(\\S+) line (\\d+)(?: > eval line \\d+)* > eval/i,we=function(){for(var t=arguments.length,i=new Array(t),e=0;e<t;e++)i[e]=arguments[e];var r=i.sort(((t,i)=>t[0]-i[0])).map((t=>t[1]));return function(t,i){void 0===i&&(i=0);for(var e=[],s=t.split(\"\\n\"),n=i;n<s.length;n++){var o=s[n];if(!(o.length>1024)){var a=de.test(o)?o.replace(de,\"$1\"):o;if(!a.match(/\\S*Error: /)){for(var l of r){var u=l(a);if(u){e.push(u);break}}if(e.length>=ve)break}}}return function(t){if(!t.length)return[];var i=Array.from(t);return i.reverse(),i.slice(0,ve).map((t=>B({},t,{filename:t.filename||ye(i).filename,function:t.function||ce})))}(e)}}(...[[30,t=>{var i=pe.exec(t);if(i){var[,e,r,s]=i;return fe(e,ce,+r,+s)}var n=ge.exec(t);if(n){if(n[2]&&0===n[2].indexOf(\"eval\")){var o=_e.exec(n[2]);o&&(n[2]=o[1],n[3]=o[2],n[4]=o[3])}var[a,l]=xe(n[1]||ce,n[2]);return fe(l,a,n[3]?+n[3]:void 0,n[4]?+n[4]:void 0)}}],[50,t=>{var i=me.exec(t);if(i){if(i[3]&&i[3].indexOf(\" > eval\")>-1){var e=be.exec(i[3]);e&&(i[1]=i[1]||\"eval\",i[3]=e[1],i[4]=e[2],i[5]=\"\")}var r=i[3],s=i[1]||ce;return[s,r]=xe(s,r),fe(r,s,i[4]?+i[4]:void 0,i[5]?+i[5]:void 0)}}]]);function ye(t){return t[t.length-1]||{}}var Se,$e,ke,xe=(t,i)=>{var e=-1!==t.indexOf(\"safari-extension\"),r=-1!==t.indexOf(\"safari-web-extension\");return e||r?[-1!==t.indexOf(\"@\")?t.split(\"@\")[0]:ce,e?\"safari-extension:\"+i:\"safari-web-extension:\"+i]:[t,i]};var Ee=/^(?:[Uu]ncaught (?:exception: )?)?(?:((?:Eval|Internal|Range|Reference|Syntax|Type|URI|)Error): )?(.*)$/i;function Ie(t,i){void 0===i&&(i=0);var e=t.stacktrace||t.stack||\"\",r=function(t){if(t&&Pe.test(t.message))return 1;return 0}(t);try{var s=we,n=function(t,i){var e=function(t){var i=globalThis._posthogChunkIds;if(!i)return{};var e=Object.keys(i);return ke&&e.length===$e||($e=e.length,ke=e.reduce(((e,r)=>{Se||(Se={});var s=Se[r];if(s)e[s[0]]=s[1];else for(var n=t(r),o=n.length-1;o>=0;o--){var a=n[o],l=null==a?void 0:a.filename,u=i[r];if(l&&u){e[l]=u,Se[r]=[l,u];break}}return e}),{})),ke}(i);return t.forEach((t=>{t.filename&&(t.chunk_id=e[t.filename])})),t}(s(e,r),s);return n.slice(0,n.length-i)}catch(t){}return[]}var Pe=/Minified React error #\\d+;/i;function Re(t,i){var e,r,s=Ie(t),n=null===(e=null==i?void 0:i.handled)||void 0===e||e,o=null!==(r=null==i?void 0:i.synthetic)&&void 0!==r&&r;return{type:null!=i&&i.overrideExceptionType?i.overrideExceptionType:t.name,value:function(t){var i=t.message;if(i.error&&\"string\"==typeof i.error.message)return String(i.error.message);return String(i)}(t),stacktrace:{frames:s,type:\"raw\"},mechanism:{handled:n,synthetic:o}}}function Te(t,i){var e=Re(t,i);return t.cause&&le(t.cause)&&t.cause!==t?[e,...Te(t.cause,{handled:null==i?void 0:i.handled,synthetic:null==i?void 0:i.synthetic})]:[e]}function Me(t,i){return{$exception_list:Te(t,i),$exception_level:\"error\"}}function Ce(t,i){var e,r,s,n=null===(e=null==i?void 0:i.handled)||void 0===e||e,o=null===(r=null==i?void 0:i.synthetic)||void 0===r||r,a={type:null!=i&&i.overrideExceptionType?i.overrideExceptionType:null!==(s=null==i?void 0:i.defaultExceptionType)&&void 0!==s?s:\"Error\",value:t||(null==i?void 0:i.defaultExceptionMessage),mechanism:{handled:n,synthetic:o}};if(null!=i&&i.syntheticException){var l=Ie(i.syntheticException,1);l.length&&(a.stacktrace={frames:l,type:\"raw\"})}return{$exception_list:[a],$exception_level:\"error\"}}function Oe(t){return T(t)&&!M(t)&&_.indexOf(t)>=0}function Fe(t,i){var e,r,s=null===(e=null==i?void 0:i.handled)||void 0===e||e,n=null===(r=null==i?void 0:i.synthetic)||void 0===r||r,o=null!=i&&i.overrideExceptionType?i.overrideExceptionType:oe(t)?t.constructor.name:\"Error\",a=\"Non-Error 'exception' captured with keys: \"+function(t,i){void 0===i&&(i=40);var e=Object.keys(t);if(e.sort(),!e.length)return\"[object has no keys]\";for(var r=e.length;r>0;r--){var s=e.slice(0,r).join(\", \");if(!(s.length>i))return r===e.length||s.length<=i?s:s.slice(0,i)+\"...\"}return\"\"}(t),l={type:o,value:a,mechanism:{handled:s,synthetic:n}};if(null!=i&&i.syntheticException){var u=Ie(null==i?void 0:i.syntheticException,1);u.length&&(l.stacktrace={frames:u,type:\"raw\"})}return{$exception_list:[l],$exception_level:Oe(t.level)?t.level:\"error\"}}function Ae(t,i){var{error:e,event:r}=t,s={$exception_list:[]},n=e||r;if(he(n)||function(t){return ue(t,\"DOMException\")}(n)){var o=n;if(function(t){return\"stack\"in t}(n))s=Me(n,i);else{var a=o.name||(he(o)?\"DOMError\":\"DOMException\"),l=o.message?a+\": \"+o.message:a;s=Ce(l,B({},i,{overrideExceptionType:he(o)?\"DOMError\":\"DOMException\",defaultExceptionMessage:l}))}return\"code\"in o&&(s.$exception_DOMException_code=\"\"+o.code),s}if(function(t){return ue(t,\"ErrorEvent\")}(n)&&n.error)return Me(n.error,i);if(le(n))return Me(n,i);if(function(t){return ue(t,\"Object\")}(n)||oe(n))return Fe(n,i);if(R(e)&&T(r)){var u=\"Error\",h=r,d=r.match(Ee);return d&&(u=d[1],h=d[2]),Ce(h,B({},i,{overrideExceptionType:u,defaultExceptionMessage:h}))}return Ce(n,i)}function De(t,i,e){try{if(!(i in t))return()=>{};var r=t[i],s=e(r);return E(s)&&(s.prototype=s.prototype||{},Object.defineProperties(s,{__posthog_wrapped__:{enumerable:!1,value:!0}})),t[i]=s,()=>{t[i]=r}}catch(t){return()=>{}}}class Le{constructor(i){var e;this._instance=i,this.et=(null==t||null==(e=t.location)?void 0:e.pathname)||\"\"}get isEnabled(){return\"history_change\"===this._instance.config.capture_pageview}startIfEnabled(){this.isEnabled&&(j.info(\"History API monitoring enabled, starting...\"),this.monitorHistoryChanges())}stop(){this.rt&&this.rt(),this.rt=void 0,j.info(\"History API monitoring stopped\")}monitorHistoryChanges(){var i,e;if(t&&t.history){var r=this;null!=(i=t.history.pushState)&&i.__posthog_wrapped__||De(t.history,\"pushState\",(t=>function(i,e,s){t.call(this,i,e,s),r.st(\"pushState\")})),null!=(e=t.history.replaceState)&&e.__posthog_wrapped__||De(t.history,\"replaceState\",(t=>function(i,e,s){t.call(this,i,e,s),r.st(\"replaceState\")})),this.nt()}}st(i){try{var e,r=null==t||null==(e=t.location)?void 0:e.pathname;if(!r)return;r!==this.et&&this.isEnabled&&this._instance.capture(\"$pageview\",{navigation_type:i}),this.et=r}catch(t){j.error(\"Error capturing \"+i+\" pageview\",t)}}nt(){if(!this.rt){var i=()=>{this.st(\"popstate\")};st(t,\"popstate\",i),this.rt=()=>{t&&t.removeEventListener(\"popstate\",i)}}}}function Ne(t){var i,e;return(null==(i=JSON.stringify(t,(e=[],function(t,i){if(I(i)){for(;e.length>0&&e[e.length-1]!==this;)e.pop();return e.includes(i)?\"[Circular]\":(e.push(i),i)}return i})))?void 0:i.length)||0}function je(t,i){if(void 0===i&&(i=6606028.8),t.size>=i&&t.data.length>1){var e=Math.floor(t.data.length/2),r=t.data.slice(0,e),s=t.data.slice(e);return[je({size:Ne(r),data:r,sessionId:t.sessionId,windowId:t.windowId}),je({size:Ne(s),data:s,sessionId:t.sessionId,windowId:t.windowId})].flatMap((t=>t))}return[t]}var ze=(t=>(t[t.DomContentLoaded=0]=\"DomContentLoaded\",t[t.Load=1]=\"Load\",t[t.FullSnapshot=2]=\"FullSnapshot\",t[t.IncrementalSnapshot=3]=\"IncrementalSnapshot\",t[t.Meta=4]=\"Meta\",t[t.Custom=5]=\"Custom\",t[t.Plugin=6]=\"Plugin\",t))(ze||{}),Ue=(t=>(t[t.Mutation=0]=\"Mutation\",t[t.MouseMove=1]=\"MouseMove\",t[t.MouseInteraction=2]=\"MouseInteraction\",t[t.Scroll=3]=\"Scroll\",t[t.ViewportResize=4]=\"ViewportResize\",t[t.Input=5]=\"Input\",t[t.TouchMove=6]=\"TouchMove\",t[t.MediaInteraction=7]=\"MediaInteraction\",t[t.StyleSheetRule=8]=\"StyleSheetRule\",t[t.CanvasMutation=9]=\"CanvasMutation\",t[t.Font=10]=\"Font\",t[t.Log=11]=\"Log\",t[t.Drag=12]=\"Drag\",t[t.StyleDeclaration=13]=\"StyleDeclaration\",t[t.Selection=14]=\"Selection\",t[t.AdoptedStyleSheet=15]=\"AdoptedStyleSheet\",t[t.CustomElement=16]=\"CustomElement\",t))(Ue||{}),qe=\"[SessionRecording]\",Be=\"redacted\",He={initiatorTypes:[\"audio\",\"beacon\",\"body\",\"css\",\"early-hint\",\"embed\",\"fetch\",\"frame\",\"iframe\",\"icon\",\"image\",\"img\",\"input\",\"link\",\"navigation\",\"object\",\"ping\",\"script\",\"track\",\"video\",\"xmlhttprequest\"],maskRequestFn:t=>t,recordHeaders:!1,recordBody:!1,recordInitialRequests:!1,recordPerformance:!1,performanceEntryTypeToObserve:[\"first-input\",\"navigation\",\"paint\",\"resource\"],payloadSizeLimitBytes:1e6,payloadHostDenyList:[\".lr-ingest.io\",\".ingest.sentry.io\",\".clarity.ms\",\"analytics.google.com\",\"bam.nr-data.net\"]},We=[\"authorization\",\"x-forwarded-for\",\"authorization\",\"cookie\",\"set-cookie\",\"x-api-key\",\"x-real-ip\",\"remote-addr\",\"forwarded\",\"proxy-authorization\",\"x-csrf-token\",\"x-csrftoken\",\"x-xsrf-token\"],Ge=[\"password\",\"secret\",\"passwd\",\"api_key\",\"apikey\",\"auth\",\"credentials\",\"mysql_pwd\",\"privatekey\",\"private_key\",\"token\"],Je=[\"/s/\",\"/e/\",\"/i/\"];function Ve(t,i,e,r){if(O(t))return t;var s=(null==i?void 0:i[\"content-length\"])||function(t){return new Blob([t]).size}(t);return T(s)&&(s=parseInt(s)),s>e?qe+\" \"+r+\" body too large to record (\"+s+\" bytes)\":t}function Ke(t,i){if(O(t))return t;var e=t;return ci(e,!1)||(e=qe+\" \"+i+\" body \"+Be),J(Ge,(t=>{var r,s;null!=(r=e)&&r.length&&-1!==(null==(s=e)?void 0:s.indexOf(t))&&(e=qe+\" \"+i+\" body \"+Be+\" as might contain: \"+t)})),e}var Ye=(t,i)=>{var e,r,s,n={payloadSizeLimitBytes:He.payloadSizeLimitBytes,performanceEntryTypeToObserve:[...He.performanceEntryTypeToObserve],payloadHostDenyList:[...i.payloadHostDenyList||[],...He.payloadHostDenyList]},o=!1!==t.session_recording.recordHeaders&&i.recordHeaders,a=!1!==t.session_recording.recordBody&&i.recordBody,l=!1!==t.capture_performance&&i.recordPerformance,u=(e=n,s=Math.min(1e6,null!==(r=e.payloadSizeLimitBytes)&&void 0!==r?r:1e6),t=>(null!=t&&t.requestBody&&(t.requestBody=Ve(t.requestBody,t.requestHeaders,s,\"Request\")),null!=t&&t.responseBody&&(t.responseBody=Ve(t.responseBody,t.responseHeaders,s,\"Response\")),t)),h=i=>{return u(((t,i)=>{var e,r=yi(t.name),s=0===i.indexOf(\"http\")?null==(e=yi(i))?void 0:e.pathname:i;\"/\"===s&&(s=\"\");var n=null==r?void 0:r.pathname.replace(s||\"\",\"\");if(!(r&&n&&Je.some((t=>0===n.indexOf(t)))))return t})((r=(e=i).requestHeaders,O(r)||J(Object.keys(null!=r?r:{}),(t=>{We.includes(t.toLowerCase())&&(r[t]=Be)})),e),t.api_host));var e,r},d=E(t.session_recording.maskNetworkRequestFn);return d&&E(t.session_recording.maskCapturedNetworkRequestFn)&&j.warn(\"Both `maskNetworkRequestFn` and `maskCapturedNetworkRequestFn` are defined. `maskNetworkRequestFn` will be ignored.\"),d&&(t.session_recording.maskCapturedNetworkRequestFn=i=>{var e=t.session_recording.maskNetworkRequestFn({url:i.name});return B({},i,{name:null==e?void 0:e.url})}),n.maskRequestFn=E(t.session_recording.maskCapturedNetworkRequestFn)?i=>{var e,r=h(i);return r&&null!==(e=null==t.session_recording.maskCapturedNetworkRequestFn?void 0:t.session_recording.maskCapturedNetworkRequestFn(r))&&void 0!==e?e:void 0}:t=>function(t){if(!R(t))return t.requestBody=Ke(t.requestBody,\"Request\"),t.responseBody=Ke(t.responseBody,\"Response\"),t}(h(t)),B({},He,n,{recordHeaders:o,recordBody:a,recordPerformance:l,recordInitialRequests:l})};function Xe(t,i,e,r,s){return i>e&&(j.warn(\"min cannot be greater than max.\"),i=e),F(t)?t>e?(r&&j.warn(r+\" cannot be  greater than max: \"+e+\". Using max value instead.\"),e):t<i?(r&&j.warn(r+\" cannot be less than min: \"+i+\". Using min value instead.\"),i):t:(r&&j.warn(r+\" must be a number. using max or fallback. max: \"+e+\", fallback: \"+s),Xe(s||e,i,e,r))}class Qe{constructor(t,i){var e,r;void 0===i&&(i={}),this.ot=100,this.lt=10,this.ut={},this.ht={},this.dt=()=>{Object.keys(this.ut).forEach((t=>{this.ut[t]=this.ut[t]+this.lt,this.ut[t]>=this.ot&&delete this.ut[t]}))},this.vt=t=>{var i=this._rrweb.mirror.getNode(t);if(\"svg\"!==(null==i?void 0:i.nodeName)&&i instanceof Element){var e=i.closest(\"svg\");if(e)return[this._rrweb.mirror.getId(e),e]}return[t,i]},this.ct=t=>{var i,e,r,s,n,o,a,l;return(null!==(i=null==(e=t.removes)?void 0:e.length)&&void 0!==i?i:0)+(null!==(r=null==(s=t.attributes)?void 0:s.length)&&void 0!==r?r:0)+(null!==(n=null==(o=t.texts)?void 0:o.length)&&void 0!==n?n:0)+(null!==(a=null==(l=t.adds)?void 0:l.length)&&void 0!==a?a:0)},this.throttleMutations=t=>{if(3!==t.type||0!==t.data.source)return t;var i=t.data,e=this.ct(i);i.attributes&&(i.attributes=i.attributes.filter((t=>{var i,e,r,[s,n]=this.vt(t.id);if(0===this.ut[s])return!1;(this.ut[s]=null!==(i=this.ut[s])&&void 0!==i?i:this.ot,this.ut[s]=Math.max(this.ut[s]-1,0),0===this.ut[s])&&(this.ht[s]||(this.ht[s]=!0,null==(e=(r=this.ft).onBlockedNode)||e.call(r,s,n)));return t})));var r=this.ct(i);return 0!==r||e===r?t:void 0},this._rrweb=t,this.ft=i,this.lt=Xe(null!==(e=this.ft.refillRate)&&void 0!==e?e:this.lt,0,100,\"mutation throttling refill rate\"),this.ot=Xe(null!==(r=this.ft.bucketSize)&&void 0!==r?r:this.ot,0,100,\"mutation throttling bucket size\"),setInterval((()=>{this.dt()}),1e3)}}var Ze=Uint8Array,tr=Uint16Array,ir=Uint32Array,er=new Ze([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),rr=new Ze([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),sr=new Ze([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),nr=function(t,i){for(var e=new tr(31),r=0;r<31;++r)e[r]=i+=1<<t[r-1];var s=new ir(e[30]);for(r=1;r<30;++r)for(var n=e[r];n<e[r+1];++n)s[n]=n-e[r]<<5|r;return[e,s]},or=nr(er,2),ar=or[0],lr=or[1];ar[28]=258,lr[258]=28;for(var ur=nr(rr,0)[1],hr=new tr(32768),dr=0;dr<32768;++dr){var vr=(43690&dr)>>>1|(21845&dr)<<1;vr=(61680&(vr=(52428&vr)>>>2|(13107&vr)<<2))>>>4|(3855&vr)<<4,hr[dr]=((65280&vr)>>>8|(255&vr)<<8)>>>1}var cr=function(t,i,e){for(var r=t.length,s=0,n=new tr(i);s<r;++s)++n[t[s]-1];var o,a=new tr(i);for(s=0;s<i;++s)a[s]=a[s-1]+n[s-1]<<1;if(e){o=new tr(1<<i);var l=15-i;for(s=0;s<r;++s)if(t[s])for(var u=s<<4|t[s],h=i-t[s],d=a[t[s]-1]++<<h,v=d|(1<<h)-1;d<=v;++d)o[hr[d]>>>l]=u}else for(o=new tr(r),s=0;s<r;++s)o[s]=hr[a[t[s]-1]++]>>>15-t[s];return o},fr=new Ze(288);for(dr=0;dr<144;++dr)fr[dr]=8;for(dr=144;dr<256;++dr)fr[dr]=9;for(dr=256;dr<280;++dr)fr[dr]=7;for(dr=280;dr<288;++dr)fr[dr]=8;var pr=new Ze(32);for(dr=0;dr<32;++dr)pr[dr]=5;var gr=cr(fr,9,0),_r=cr(pr,5,0),mr=function(t){return(t/8>>0)+(7&t&&1)},br=function(t,i,e){(null==e||e>t.length)&&(e=t.length);var r=new(t instanceof tr?tr:t instanceof ir?ir:Ze)(e-i);return r.set(t.subarray(i,e)),r},wr=function(t,i,e){e<<=7&i;var r=i/8>>0;t[r]|=e,t[r+1]|=e>>>8},yr=function(t,i,e){e<<=7&i;var r=i/8>>0;t[r]|=e,t[r+1]|=e>>>8,t[r+2]|=e>>>16},Sr=function(t,i){for(var e=[],r=0;r<t.length;++r)t[r]&&e.push({s:r,f:t[r]});var s=e.length,n=e.slice();if(!s)return[new Ze(0),0];if(1==s){var o=new Ze(e[0].s+1);return o[e[0].s]=1,[o,1]}e.sort((function(t,i){return t.f-i.f})),e.push({s:-1,f:25001});var a=e[0],l=e[1],u=0,h=1,d=2;for(e[0]={s:-1,f:a.f+l.f,l:a,r:l};h!=s-1;)a=e[e[u].f<e[d].f?u++:d++],l=e[u!=h&&e[u].f<e[d].f?u++:d++],e[h++]={s:-1,f:a.f+l.f,l:a,r:l};var v=n[0].s;for(r=1;r<s;++r)n[r].s>v&&(v=n[r].s);var c=new tr(v+1),f=$r(e[h-1],c,0);if(f>i){r=0;var p=0,g=f-i,_=1<<g;for(n.sort((function(t,i){return c[i.s]-c[t.s]||t.f-i.f}));r<s;++r){var m=n[r].s;if(!(c[m]>i))break;p+=_-(1<<f-c[m]),c[m]=i}for(p>>>=g;p>0;){var b=n[r].s;c[b]<i?p-=1<<i-c[b]++-1:++r}for(;r>=0&&p;--r){var w=n[r].s;c[w]==i&&(--c[w],++p)}f=i}return[new Ze(c),f]},$r=function(t,i,e){return-1==t.s?Math.max($r(t.l,i,e+1),$r(t.r,i,e+1)):i[t.s]=e},kr=function(t){for(var i=t.length;i&&!t[--i];);for(var e=new tr(++i),r=0,s=t[0],n=1,o=function(t){e[r++]=t},a=1;a<=i;++a)if(t[a]==s&&a!=i)++n;else{if(!s&&n>2){for(;n>138;n-=138)o(32754);n>2&&(o(n>10?n-11<<5|28690:n-3<<5|12305),n=0)}else if(n>3){for(o(s),--n;n>6;n-=6)o(8304);n>2&&(o(n-3<<5|8208),n=0)}for(;n--;)o(s);n=1,s=t[a]}return[e.subarray(0,r),i]},xr=function(t,i){for(var e=0,r=0;r<i.length;++r)e+=t[r]*i[r];return e},Er=function(t,i,e){var r=e.length,s=mr(i+2);t[s]=255&r,t[s+1]=r>>>8,t[s+2]=255^t[s],t[s+3]=255^t[s+1];for(var n=0;n<r;++n)t[s+n+4]=e[n];return 8*(s+4+r)},Ir=function(t,i,e,r,s,n,o,a,l,u,h){wr(i,h++,e),++s[256];for(var d=Sr(s,15),v=d[0],c=d[1],f=Sr(n,15),p=f[0],g=f[1],_=kr(v),m=_[0],b=_[1],w=kr(p),y=w[0],S=w[1],$=new tr(19),k=0;k<m.length;++k)$[31&m[k]]++;for(k=0;k<y.length;++k)$[31&y[k]]++;for(var x=Sr($,7),E=x[0],I=x[1],P=19;P>4&&!E[sr[P-1]];--P);var R,T,M,C,O=u+5<<3,F=xr(s,fr)+xr(n,pr)+o,A=xr(s,v)+xr(n,p)+o+14+3*P+xr($,E)+(2*$[16]+3*$[17]+7*$[18]);if(O<=F&&O<=A)return Er(i,h,t.subarray(l,l+u));if(wr(i,h,1+(A<F)),h+=2,A<F){R=cr(v,c,0),T=v,M=cr(p,g,0),C=p;var D=cr(E,I,0);wr(i,h,b-257),wr(i,h+5,S-1),wr(i,h+10,P-4),h+=14;for(k=0;k<P;++k)wr(i,h+3*k,E[sr[k]]);h+=3*P;for(var L=[m,y],N=0;N<2;++N){var j=L[N];for(k=0;k<j.length;++k){var z=31&j[k];wr(i,h,D[z]),h+=E[z],z>15&&(wr(i,h,j[k]>>>5&127),h+=j[k]>>>12)}}}else R=gr,T=fr,M=_r,C=pr;for(k=0;k<a;++k)if(r[k]>255){z=r[k]>>>18&31;yr(i,h,R[z+257]),h+=T[z+257],z>7&&(wr(i,h,r[k]>>>23&31),h+=er[z]);var U=31&r[k];yr(i,h,M[U]),h+=C[U],U>3&&(yr(i,h,r[k]>>>5&8191),h+=rr[U])}else yr(i,h,R[r[k]]),h+=T[r[k]];return yr(i,h,R[256]),h+T[256]},Pr=new ir([65540,131080,131088,131104,262176,1048704,1048832,2114560,2117632]),Rr=function(){for(var t=new ir(256),i=0;i<256;++i){for(var e=i,r=9;--r;)e=(1&e&&3988292384)^e>>>1;t[i]=e}return t}(),Tr=function(){var t=4294967295;return{p:function(i){for(var e=t,r=0;r<i.length;++r)e=Rr[255&e^i[r]]^e>>>8;t=e},d:function(){return 4294967295^t}}},Mr=function(t,i,e,r,s){return function(t,i,e,r,s,n){var o=t.length,a=new Ze(r+o+5*(1+Math.floor(o/7e3))+s),l=a.subarray(r,a.length-s),u=0;if(!i||o<8)for(var h=0;h<=o;h+=65535){var d=h+65535;d<o?u=Er(l,u,t.subarray(h,d)):(l[h]=n,u=Er(l,u,t.subarray(h,o)))}else{for(var v=Pr[i-1],c=v>>>13,f=8191&v,p=(1<<e)-1,g=new tr(32768),_=new tr(p+1),m=Math.ceil(e/3),b=2*m,w=function(i){return(t[i]^t[i+1]<<m^t[i+2]<<b)&p},y=new ir(25e3),S=new tr(288),$=new tr(32),k=0,x=0,E=(h=0,0),I=0,P=0;h<o;++h){var R=w(h),T=32767&h,M=_[R];if(g[T]=M,_[R]=T,I<=h){var C=o-h;if((k>7e3||E>24576)&&C>423){u=Ir(t,l,0,y,S,$,x,E,P,h-P,u),E=k=x=0,P=h;for(var O=0;O<286;++O)S[O]=0;for(O=0;O<30;++O)$[O]=0}var F=2,A=0,D=f,L=T-M&32767;if(C>2&&R==w(h-L))for(var N=Math.min(c,C)-1,j=Math.min(32767,h),z=Math.min(258,C);L<=j&&--D&&T!=M;){if(t[h+F]==t[h+F-L]){for(var U=0;U<z&&t[h+U]==t[h+U-L];++U);if(U>F){if(F=U,A=L,U>N)break;var q=Math.min(L,U-2),B=0;for(O=0;O<q;++O){var H=h-L+O+32768&32767,W=H-g[H]+32768&32767;W>B&&(B=W,M=H)}}}L+=(T=M)-(M=g[T])+32768&32767}if(A){y[E++]=268435456|lr[F]<<18|ur[A];var G=31&lr[F],J=31&ur[A];x+=er[G]+rr[J],++S[257+G],++$[J],I=h+F,++k}else y[E++]=t[h],++S[t[h]]}}u=Ir(t,l,n,y,S,$,x,E,P,h-P,u)}return br(a,0,r+mr(u)+s)}(t,null==i.level?6:i.level,null==i.mem?Math.ceil(1.5*Math.max(8,Math.min(13,Math.log(t.length)))):12+i.mem,e,r,!s)},Cr=function(t,i,e){for(;e;++i)t[i]=e,e>>>=8},Or=function(t,i){var e=i.filename;if(t[0]=31,t[1]=139,t[2]=8,t[8]=i.level<2?4:9==i.level?2:0,t[9]=3,0!=i.mtime&&Cr(t,4,Math.floor(new Date(i.mtime||Date.now())/1e3)),e){t[3]=8;for(var r=0;r<=e.length;++r)t[r+10]=e.charCodeAt(r)}},Fr=function(t){return 10+(t.filename&&t.filename.length+1||0)};function Ar(t,i){void 0===i&&(i={});var e=Tr(),r=t.length;e.p(t);var s=Mr(t,i,Fr(i),8),n=s.length;return Or(s,i),Cr(s,n-8,e.d()),Cr(s,n-4,r),s}function Dr(t,i){var e=t.length;if(\"undefined\"!=typeof TextEncoder)return(new TextEncoder).encode(t);for(var r=new Ze(t.length+(t.length>>>1)),s=0,n=function(t){r[s++]=t},o=0;o<e;++o){if(s+5>r.length){var a=new Ze(s+8+(e-o<<1));a.set(r),r=a}var l=t.charCodeAt(o);l<128||i?n(l):l<2048?(n(192|l>>>6),n(128|63&l)):l>55295&&l<57344?(n(240|(l=65536+(1047552&l)|1023&t.charCodeAt(++o))>>>18),n(128|l>>>12&63),n(128|l>>>6&63),n(128|63&l)):(n(224|l>>>12),n(128|l>>>6&63),n(128|63&l))}return br(r,0,s)}function Lr(t,i){return function(t){for(var i=0,e=0;e<t.length;e++)i=(i<<5)-i+t.charCodeAt(e),i|=0;return Math.abs(i)}(t)%100<Xe(100*i,0,100)}var Nr=\"disabled\",jr=\"sampled\",zr=\"active\",Ur=\"buffering\",qr=\"paused\",Br=\"trigger\",Hr=Br+\"_activated\",Wr=Br+\"_pending\",Gr=Br+\"_\"+Nr;function Jr(t,i){return i.some((i=>\"regex\"===i.matching&&new RegExp(i.url).test(t)))}class Vr{constructor(t){this.gt=t}triggerStatus(t){var i=this.gt.map((i=>i.triggerStatus(t)));return i.includes(Hr)?Hr:i.includes(Wr)?Wr:Gr}stop(){this.gt.forEach((t=>t.stop()))}}class Kr{constructor(t){this.gt=t}triggerStatus(t){var i=new Set;for(var e of this.gt)i.add(e.triggerStatus(t));switch(i.delete(Gr),i.size){case 0:return Gr;case 1:return Array.from(i)[0];default:return Wr}}stop(){this.gt.forEach((t=>t.stop()))}}class Yr{triggerStatus(){return Wr}stop(){}}class Xr{constructor(t){this._t=[],this.bt=[],this.urlBlocked=!1,this._instance=t}onRemoteConfig(t){var i,e;this._t=(null==(i=t.sessionRecording)?void 0:i.urlTriggers)||[],this.bt=(null==(e=t.sessionRecording)?void 0:e.urlBlocklist)||[]}wt(t){var i;return 0===this._t.length?Gr:(null==(i=this._instance)?void 0:i.get_property(xt))===t?Hr:Wr}triggerStatus(t){var i=this.wt(t),e=i===Hr?Hr:i===Wr?Wr:Gr;return this._instance.register_for_session({$sdk_debug_replay_url_trigger_status:e}),e}checkUrlTriggerConditions(i,e,r){if(void 0!==t&&t.location.href){var s=t.location.href,n=this.urlBlocked,o=Jr(s,this.bt);n&&o||(o&&!n?i():!o&&n&&e(),Jr(s,this._t)&&r(\"url\"))}}stop(){}}class Qr{constructor(t){this.linkedFlag=null,this.linkedFlagSeen=!1,this.yt=()=>{},this._instance=t}triggerStatus(){var t=Wr;return O(this.linkedFlag)&&(t=Gr),this.linkedFlagSeen&&(t=Hr),this._instance.register_for_session({$sdk_debug_replay_linked_flag_trigger_status:t}),t}onRemoteConfig(t,i){var e;if(this.linkedFlag=(null==(e=t.sessionRecording)?void 0:e.linkedFlag)||null,!O(this.linkedFlag)&&!this.linkedFlagSeen){var r=T(this.linkedFlag)?this.linkedFlag:this.linkedFlag.flag,s=T(this.linkedFlag)?null:this.linkedFlag.variant;this.yt=this._instance.onFeatureFlags(((t,e)=>{var n=!1;if(I(e)&&r in e){var o=e[r];n=A(o)?!0===o:s?o===s:!!o}this.linkedFlagSeen=n,n&&i(r,s)}))}}stop(){this.yt()}}class Zr{constructor(t){this.St=[],this._instance=t}onRemoteConfig(t){var i;this.St=(null==(i=t.sessionRecording)?void 0:i.eventTriggers)||[]}$t(t){var i;return 0===this.St.length?Gr:(null==(i=this._instance)?void 0:i.get_property(Et))===t?Hr:Wr}triggerStatus(t){var i=this.$t(t),e=i===Hr?Hr:i===Wr?Wr:Gr;return this._instance.register_for_session({$sdk_debug_replay_event_trigger_status:e}),e}stop(){}}function ts(t){return t.isRecordingEnabled?Ur:Nr}function is(t){if(!t.receivedDecide)return Ur;if(!t.isRecordingEnabled)return Nr;if(t.urlTriggerMatching.urlBlocked)return qr;var i=!0===t.isSampled,e=new Vr([t.eventTriggerMatching,t.urlTriggerMatching,t.linkedFlagMatching]).triggerStatus(t.sessionId);return i?jr:e===Hr?zr:e===Wr?Ur:!1===t.isSampled?Nr:zr}function es(t){if(!t.receivedDecide)return Ur;if(!t.isRecordingEnabled)return Nr;if(t.urlTriggerMatching.urlBlocked)return qr;var i=new Kr([t.eventTriggerMatching,t.urlTriggerMatching,t.linkedFlagMatching]).triggerStatus(t.sessionId),e=i!==Gr,r=A(t.isSampled);return e&&i===Wr?Ur:e&&i===Gr||r&&!t.isSampled?Nr:!0===t.isSampled?jr:zr}var rs=\"[SessionRecording]\",ss=z(rs);function ns(){var t;return null==v||null==(t=v.__PosthogExtensions__)||null==(t=t.rrweb)?void 0:t.record}var os=3e5,as=[Ue.MouseMove,Ue.MouseInteraction,Ue.Scroll,Ue.ViewportResize,Ue.Input,Ue.TouchMove,Ue.MediaInteraction,Ue.Drag],ls=t=>({rrwebMethod:t,enqueuedAt:Date.now(),attempt:1});function us(t){return function(t,i){for(var e=\"\",r=0;r<t.length;){var s=t[r++];s<128||i?e+=String.fromCharCode(s):s<224?e+=String.fromCharCode((31&s)<<6|63&t[r++]):s<240?e+=String.fromCharCode((15&s)<<12|(63&t[r++])<<6|63&t[r++]):(s=((15&s)<<18|(63&t[r++])<<12|(63&t[r++])<<6|63&t[r++])-65536,e+=String.fromCharCode(55296|s>>10,56320|1023&s))}return e}(Ar(Dr(JSON.stringify(t))),!0)}function hs(t){return t.type===ze.Custom&&\"sessionIdle\"===t.data.tag}class ds{get sessionId(){return this.kt}get xt(){return this._instance.config.session_recording.session_idle_threshold_ms||3e5}get started(){return this.Et}get It(){if(!this._instance.sessionManager)throw new Error(rs+\" must be started with a valid sessionManager.\");return this._instance.sessionManager}get Pt(){var t,i;return this.Rt.triggerStatus(this.sessionId)===Wr?6e4:null!==(t=null==(i=this._instance.config.session_recording)?void 0:i.full_snapshot_interval_millis)&&void 0!==t?t:os}get Tt(){var t=this._instance.get_property(kt);return A(t)?t:null}get Mt(){var t,i,e=null==(t=this.M)?void 0:t.data[(null==(i=this.M)?void 0:i.data.length)-1],{sessionStartTimestamp:r}=this.It.checkAndGetSessionAndWindowId(!0);return e?e.timestamp-r:null}get Ct(){var i=!!this._instance.get_property(pt),e=!this._instance.config.disable_session_recording;return t&&i&&e}get Ot(){var t=!!this._instance.get_property(gt),i=this._instance.config.enable_recording_console_log;return null!=i?i:t}get Ft(){var t,i,e,r,s,n,o=this._instance.config.session_recording.captureCanvas,a=this._instance.get_property(bt),l=null!==(t=null!==(i=null==o?void 0:o.recordCanvas)&&void 0!==i?i:null==a?void 0:a.enabled)&&void 0!==t&&t,u=null!==(e=null!==(r=null==o?void 0:o.canvasFps)&&void 0!==r?r:null==a?void 0:a.fps)&&void 0!==e?e:4,h=null!==(s=null!==(n=null==o?void 0:o.canvasQuality)&&void 0!==n?n:null==a?void 0:a.quality)&&void 0!==s?s:.4;if(\"string\"==typeof h){var d=parseFloat(h);h=isNaN(d)?.4:d}return{enabled:l,fps:Xe(u,0,12,\"canvas recording fps\",4),quality:Xe(h,0,1,\"canvas recording quality\",.4)}}get At(){var t,i,e=this._instance.get_property(_t),r={recordHeaders:null==(t=this._instance.config.session_recording)?void 0:t.recordHeaders,recordBody:null==(i=this._instance.config.session_recording)?void 0:i.recordBody},s=(null==r?void 0:r.recordHeaders)||(null==e?void 0:e.recordHeaders),n=(null==r?void 0:r.recordBody)||(null==e?void 0:e.recordBody),o=I(this._instance.config.capture_performance)?this._instance.config.capture_performance.network_timing:this._instance.config.capture_performance,a=!!(A(o)?o:null==e?void 0:e.capturePerformance);return s||n||a?{recordHeaders:s,recordBody:n,recordPerformance:a}:void 0}get Dt(){var t,i,e,r,s,n,o=this._instance.get_property(mt),a={maskAllInputs:null==(t=this._instance.config.session_recording)?void 0:t.maskAllInputs,maskTextSelector:null==(i=this._instance.config.session_recording)?void 0:i.maskTextSelector,blockSelector:null==(e=this._instance.config.session_recording)?void 0:e.blockSelector},l=null!==(r=null==a?void 0:a.maskAllInputs)&&void 0!==r?r:null==o?void 0:o.maskAllInputs,u=null!==(s=null==a?void 0:a.maskTextSelector)&&void 0!==s?s:null==o?void 0:o.maskTextSelector,h=null!==(n=null==a?void 0:a.blockSelector)&&void 0!==n?n:null==o?void 0:o.blockSelector;return R(l)&&R(u)&&R(h)?void 0:{maskAllInputs:null==l||l,maskTextSelector:u,blockSelector:h}}get Lt(){var t=this._instance.get_property(wt);return F(t)?t:null}get Nt(){var t=this._instance.get_property(yt);return F(t)?t:null}get status(){return this.jt?this.zt({receivedDecide:this.jt,isRecordingEnabled:this.Ct,isSampled:this.Tt,urlTriggerMatching:this.Ut,eventTriggerMatching:this.qt,linkedFlagMatching:this.Bt,sessionId:this.sessionId}):Ur}constructor(t){if(this.zt=ts,this.jt=!1,this.Ht=[],this.Wt=\"unknown\",this.Gt=Date.now(),this.Rt=new Yr,this.Jt=void 0,this.Vt=void 0,this.Kt=void 0,this.Yt=void 0,this.Xt=void 0,this._forceAllowLocalhostNetworkCapture=!1,this.Qt=()=>{this.Zt()},this.ti=()=>{this.ii(\"browser offline\",{})},this.ei=()=>{this.ii(\"browser online\",{})},this.ri=()=>{if(null!=o&&o.visibilityState){var t=\"window \"+o.visibilityState;this.ii(t,{})}},this._instance=t,this.Et=!1,this.si=\"/s/\",this.ni=void 0,this.jt=!1,!this._instance.sessionManager)throw ss.error(\"started without valid sessionManager\"),new Error(rs+\" started without valid sessionManager. This is a bug.\");if(this._instance.config.__preview_experimental_cookieless_mode)throw new Error(rs+\" cannot be used with __preview_experimental_cookieless_mode.\");this.Bt=new Qr(this._instance),this.Ut=new Xr(this._instance),this.qt=new Zr(this._instance);var{sessionId:i,windowId:e}=this.It.checkAndGetSessionAndWindowId();this.kt=i,this.oi=e,this.M=this.ai(),this.xt>=this.It.sessionTimeoutMs&&ss.warn(\"session_idle_threshold_ms (\"+this.xt+\") is greater than the session timeout (\"+this.It.sessionTimeoutMs+\"). Session will never be detected as idle\")}startIfEnabledOrStop(i){this.Ct?(this.li(i),st(t,\"beforeunload\",this.Qt),st(t,\"offline\",this.ti),st(t,\"online\",this.ei),st(t,\"visibilitychange\",this.ri),this.ui(),this.hi(),O(this.Jt)&&(this.Jt=this._instance.on(\"eventCaptured\",(t=>{try{if(\"$pageview\"===t.event){var i=null!=t&&t.properties.$current_url?this.di(null==t?void 0:t.properties.$current_url):\"\";if(!i)return;this.ii(\"$pageview\",{href:i})}}catch(t){ss.error(\"Could not add $pageview to rrweb session\",t)}}))),this.Vt||(this.Vt=this.It.onSessionId(((t,i,e)=>{var r,s;e&&(this.ii(\"$session_id_change\",{sessionId:t,windowId:i,changeReason:e}),null==(r=this._instance)||null==(r=r.persistence)||r.unregister(Et),null==(s=this._instance)||null==(s=s.persistence)||s.unregister(xt))})))):this.stopRecording()}stopRecording(){var i,e,r,s;this.Et&&this.ni&&(this.ni(),this.ni=void 0,this.Et=!1,null==t||t.removeEventListener(\"beforeunload\",this.Qt),null==t||t.removeEventListener(\"offline\",this.ti),null==t||t.removeEventListener(\"online\",this.ei),null==t||t.removeEventListener(\"visibilitychange\",this.ri),this.ai(),clearInterval(this.vi),null==(i=this.Jt)||i.call(this),this.Jt=void 0,null==(e=this.Xt)||e.call(this),this.Xt=void 0,null==(r=this.Vt)||r.call(this),this.Vt=void 0,null==(s=this.Yt)||s.call(this),this.Yt=void 0,this.qt.stop(),this.Ut.stop(),this.Bt.stop(),ss.info(\"stopped\"))}ci(){var t;null==(t=this._instance.persistence)||t.unregister(kt)}fi(t){var i,e=this.kt!==t,r=this.Lt;if(F(r)){var s=this.Tt,n=e||!A(s),o=n?Lr(t,r):s;n&&(o?this.pi(jr):ss.warn(\"Sample rate (\"+r+\") has determined that this sessionId (\"+t+\") will not be sent to the server.\"),this.ii(\"samplingDecisionMade\",{sampleRate:r,isSampled:o})),null==(i=this._instance.persistence)||i.register({[kt]:o})}else this.ci()}onRemoteConfig(t){var i,e,r,s;(this.ii(\"$remote_config_received\",t),this.gi(t),null!=(i=t.sessionRecording)&&i.endpoint)&&(this.si=null==(s=t.sessionRecording)?void 0:s.endpoint);this.ui(),\"any\"===(null==(e=t.sessionRecording)?void 0:e.triggerMatchType)?(this.zt=is,this.Rt=new Vr([this.qt,this.Ut])):(this.zt=es,this.Rt=new Kr([this.qt,this.Ut])),this._instance.register_for_session({$sdk_debug_replay_remote_trigger_matching_config:null==(r=t.sessionRecording)?void 0:r.triggerMatchType}),this.Ut.onRemoteConfig(t),this.qt.onRemoteConfig(t),this.Bt.onRemoteConfig(t,((t,i)=>{this.pi(\"linked_flag_matched\",{flag:t,variant:i})})),this.jt=!0,this.startIfEnabledOrStop()}ui(){F(this.Lt)&&O(this.Yt)&&(this.Yt=this.It.onSessionId((t=>{this.fi(t)})))}gi(t){if(this._instance.persistence){var i,e=this._instance.persistence,r=()=>{var i,r,s,n,o,a,l,u,h,d=null==(i=t.sessionRecording)?void 0:i.sampleRate,v=O(d)?null:parseFloat(d);O(v)&&this.ci();var c=null==(r=t.sessionRecording)?void 0:r.minimumDurationMilliseconds;e.register({[pt]:!!t.sessionRecording,[gt]:null==(s=t.sessionRecording)?void 0:s.consoleLogRecordingEnabled,[_t]:B({capturePerformance:t.capturePerformance},null==(n=t.sessionRecording)?void 0:n.networkPayloadCapture),[mt]:null==(o=t.sessionRecording)?void 0:o.masking,[bt]:{enabled:null==(a=t.sessionRecording)?void 0:a.recordCanvas,fps:null==(l=t.sessionRecording)?void 0:l.canvasFps,quality:null==(u=t.sessionRecording)?void 0:u.canvasQuality},[wt]:v,[yt]:R(c)?null:c,[St]:null==(h=t.sessionRecording)?void 0:h.scriptConfig})};r(),null==(i=this.Kt)||i.call(this),this.Kt=this.It.onSessionId(r)}}log(t,i){var e;void 0===i&&(i=\"log\"),null==(e=this._instance.sessionRecording)||e.onRRwebEmit({type:6,data:{plugin:\"rrweb/console@1\",payload:{level:i,trace:[],payload:[JSON.stringify(t)]}},timestamp:Date.now()})}li(t){if(!R(Object.assign)&&!R(Array.from)&&!(this.Et||this._instance.config.disable_session_recording||this._instance.consent.isOptedOut())){var i;if(this.Et=!0,this.It.checkAndGetSessionAndWindowId(),ns())this.mi();else null==(i=v.__PosthogExtensions__)||null==i.loadExternalDependency||i.loadExternalDependency(this._instance,this.bi,(t=>{if(t)return ss.error(\"could not load recorder\",t);this.mi()}));ss.info(\"starting\"),this.status===zr&&this.pi(t||\"recording_initialized\")}}get bi(){var t;return(null==(t=this._instance)||null==(t=t.persistence)||null==(t=t.get_property(St))?void 0:t.script)||\"recorder\"}wi(t){var i;return 3===t.type&&-1!==as.indexOf(null==(i=t.data)?void 0:i.source)}yi(t){var i=this.wi(t);i||this.Wt||t.timestamp-this.Gt>this.xt&&(this.Wt=!0,clearInterval(this.vi),this.ii(\"sessionIdle\",{eventTimestamp:t.timestamp,lastActivityTimestamp:this.Gt,threshold:this.xt,bufferLength:this.M.data.length,bufferSize:this.M.size}),this.Zt());var e=!1;if(i&&(this.Gt=t.timestamp,this.Wt)){var r=\"unknown\"===this.Wt;this.Wt=!1,r||(this.ii(\"sessionNoLongerIdle\",{reason:\"user activity\",type:t.type}),e=!0)}if(!this.Wt){var{windowId:s,sessionId:n}=this.It.checkAndGetSessionAndWindowId(!i,t.timestamp),o=this.kt!==n,a=this.oi!==s;this.oi=s,this.kt=n,o||a?(this.stopRecording(),this.startIfEnabledOrStop(\"session_id_changed\")):e&&this.Si()}}$i(t){try{return t.rrwebMethod(),!0}catch(i){return this.Ht.length<10?this.Ht.push({enqueuedAt:t.enqueuedAt||Date.now(),attempt:t.attempt++,rrwebMethod:t.rrwebMethod}):ss.warn(\"could not emit queued rrweb event.\",i,t),!1}}ii(t,i){return this.$i(ls((()=>ns().addCustomEvent(t,i))))}ki(){return this.$i(ls((()=>ns().takeFullSnapshot())))}mi(){var t,i,e,r,s={blockClass:\"ph-no-capture\",blockSelector:void 0,ignoreClass:\"ph-ignore-input\",maskTextClass:\"ph-mask\",maskTextSelector:void 0,maskTextFn:void 0,maskAllInputs:!0,maskInputOptions:{password:!0},maskInputFn:void 0,slimDOMOptions:{},collectFonts:!1,inlineStylesheet:!0,recordCrossOriginIframes:!1},n=this._instance.config.session_recording;for(var[o,a]of Object.entries(n||{}))o in s&&(\"maskInputOptions\"===o?s.maskInputOptions=B({password:!0},a):s[o]=a);(this.Ft&&this.Ft.enabled&&(s.recordCanvas=!0,s.sampling={canvas:this.Ft.fps},s.dataURLOptions={type:\"image/webp\",quality:this.Ft.quality}),this.Dt)&&(s.maskAllInputs=null===(i=this.Dt.maskAllInputs)||void 0===i||i,s.maskTextSelector=null!==(e=this.Dt.maskTextSelector)&&void 0!==e?e:void 0,s.blockSelector=null!==(r=this.Dt.blockSelector)&&void 0!==r?r:void 0);var l=ns();if(l){this.xi=null!==(t=this.xi)&&void 0!==t?t:new Qe(l,{refillRate:this._instance.config.session_recording.__mutationRateLimiterRefillRate,bucketSize:this._instance.config.session_recording.__mutationRateLimiterBucketSize,onBlockedNode:(t,i)=>{var e=\"Too many mutations on node '\"+t+\"'. Rate limiting. This could be due to SVG animations or something similar\";ss.info(e,{node:i}),this.log(rs+\" \"+e,\"warn\")}});var u=this.Ei();this.ni=l(B({emit:t=>{this.onRRwebEmit(t)},plugins:u},s)),this.Gt=Date.now(),this.Wt=A(this.Wt)?this.Wt:\"unknown\",this.ii(\"$session_options\",{sessionRecordingOptions:s,activePlugins:u.map((t=>null==t?void 0:t.name))}),this.ii(\"$posthog_config\",{config:this._instance.config})}else ss.error(\"onScriptLoaded was called but rrwebRecord is not available. This indicates something has gone wrong.\")}Si(){if(this.vi&&clearInterval(this.vi),!0!==this.Wt){var t=this.Pt;t&&(this.vi=setInterval((()=>{this.ki()}),t))}}Ei(){var t,i,e=[],r=null==(t=v.__PosthogExtensions__)||null==(t=t.rrwebPlugins)?void 0:t.getRecordConsolePlugin;r&&this.Ot&&e.push(r());var s=null==(i=v.__PosthogExtensions__)||null==(i=i.rrwebPlugins)?void 0:i.getRecordNetworkPlugin;this.At&&E(s)&&(!wi.includes(location.hostname)||this._forceAllowLocalhostNetworkCapture?e.push(s(Ye(this._instance.config,this.At))):ss.info(\"NetworkCapture not started because we are on localhost.\"));return e}onRRwebEmit(t){var i;if(this.Ii(),t&&I(t)){if(t.type===ze.Meta){var e=this.di(t.data.href);if(this.Pi=e,!e)return;t.data.href=e}else this.Ri();if(this.Ut.checkUrlTriggerConditions((()=>this.Ti()),(()=>this.Mi()),(t=>this.Ci(t))),!this.Ut.urlBlocked||(r=t).type===ze.Custom&&\"recording paused\"===r.data.tag){var r;t.type===ze.FullSnapshot&&this.Si(),t.type===ze.FullSnapshot&&this.jt&&this.Rt.triggerStatus(this.sessionId)===Wr&&this.ai();var s=this.xi?this.xi.throttleMutations(t):t;if(s){var n=function(t){var i=t;if(i&&I(i)&&6===i.type&&I(i.data)&&\"rrweb/console@1\"===i.data.plugin){i.data.payload.payload.length>10&&(i.data.payload.payload=i.data.payload.payload.slice(0,10),i.data.payload.payload.push(\"...[truncated]\"));for(var e=[],r=0;r<i.data.payload.payload.length;r++)i.data.payload.payload[r]&&i.data.payload.payload[r].length>2e3?e.push(i.data.payload.payload[r].slice(0,2e3)+\"...[truncated]\"):e.push(i.data.payload.payload[r]);return i.data.payload.payload=e,t}return t}(s);if(this.yi(n),!0!==this.Wt||hs(n)){if(hs(n)){var o=n.data.payload;if(o){var a=o.lastActivityTimestamp,l=o.threshold;n.timestamp=a+l}}var u=null===(i=this._instance.config.session_recording.compress_events)||void 0===i||i?function(t){if(Ne(t)<1024)return t;try{if(t.type===ze.FullSnapshot)return B({},t,{data:us(t.data),cv:\"2024-10\"});if(t.type===ze.IncrementalSnapshot&&t.data.source===Ue.Mutation)return B({},t,{cv:\"2024-10\",data:B({},t.data,{texts:us(t.data.texts),attributes:us(t.data.attributes),removes:us(t.data.removes),adds:us(t.data.adds)})});if(t.type===ze.IncrementalSnapshot&&t.data.source===Ue.StyleSheetRule)return B({},t,{cv:\"2024-10\",data:B({},t.data,{adds:t.data.adds?us(t.data.adds):void 0,removes:t.data.removes?us(t.data.removes):void 0})})}catch(t){ss.error(\"could not compress event - will use uncompressed event\",t)}return t}(n):n,h={$snapshot_bytes:Ne(u),$snapshot_data:u,$session_id:this.kt,$window_id:this.oi};this.status!==Nr?this.Oi(h):this.ai()}}}}}Ri(){if(!this._instance.config.capture_pageview&&t){var i=this.di(t.location.href);this.Pi!==i&&(this.ii(\"$url_changed\",{href:i}),this.Pi=i)}}Ii(){if(this.Ht.length){var t=[...this.Ht];this.Ht=[],t.forEach((t=>{Date.now()-t.enqueuedAt<=2e3&&this.$i(t)}))}}di(t){var i=this._instance.config.session_recording;if(i.maskNetworkRequestFn){var e,r={url:t};return null==(e=r=i.maskNetworkRequestFn(r))?void 0:e.url}return t}ai(){return this.M={size:0,data:[],sessionId:this.kt,windowId:this.oi},this.M}Zt(){this.Fi&&(clearTimeout(this.Fi),this.Fi=void 0);var t=this.Nt,i=this.Mt,e=F(i)&&i>=0,r=F(t)&&e&&i<t;if(this.status===Ur||this.status===qr||this.status===Nr||r)return this.Fi=setTimeout((()=>{this.Zt()}),2e3),this.M;this.M.data.length>0&&je(this.M).forEach((t=>{this.Ai({$snapshot_bytes:t.size,$snapshot_data:t.data,$session_id:t.sessionId,$window_id:t.windowId,$lib:\"web\",$lib_version:c.LIB_VERSION})}));return this.ai()}Oi(t){var i,e=2+((null==(i=this.M)?void 0:i.data.length)||0);!this.Wt&&(this.M.size+t.$snapshot_bytes+e>943718.4||this.M.sessionId!==this.kt)&&(this.M=this.Zt()),this.M.size+=t.$snapshot_bytes,this.M.data.push(t.$snapshot_data),this.Fi||this.Wt||(this.Fi=setTimeout((()=>{this.Zt()}),2e3))}Ai(t){this._instance.capture(\"$snapshot\",t,{_url:this._instance.requestRouter.endpointFor(\"api\",this.si),_noTruncate:!0,_batchKey:\"recordings\",skip_client_rate_limiting:!0})}Ci(t){var i;this.Rt.triggerStatus(this.sessionId)===Wr&&(null==(i=this._instance)||null==(i=i.persistence)||i.register({[\"url\"===t?xt:Et]:this.kt}),this.Zt(),this.pi(t+\"_trigger_matched\"))}Ti(){this.Ut.urlBlocked||(this.Ut.urlBlocked=!0,clearInterval(this.vi),ss.info(\"recording paused due to URL blocker\"),this.ii(\"recording paused\",{reason:\"url blocker\"}))}Mi(){this.Ut.urlBlocked&&(this.Ut.urlBlocked=!1,this.ki(),this.Si(),this.ii(\"recording resumed\",{reason:\"left blocked url\"}),ss.info(\"recording resumed\"))}hi(){0!==this.qt.St.length&&O(this.Xt)&&(this.Xt=this._instance.on(\"eventCaptured\",(t=>{try{this.qt.St.includes(t.event)&&this.Ci(\"event\")}catch(t){ss.error(\"Could not activate event trigger\",t)}})))}overrideLinkedFlag(){this.Bt.linkedFlagSeen=!0,this.ki(),this.pi(\"linked_flag_overridden\")}overrideSampling(){var t;null==(t=this._instance.persistence)||t.register({[kt]:!0}),this.ki(),this.pi(\"sampling_overridden\")}overrideTrigger(t){this.Ci(t)}pi(t,i){this._instance.register_for_session({$session_recording_start_reason:t}),ss.info(t.replace(\"_\",\" \"),i),m([\"recording_initialized\",\"session_id_changed\"],t)||this.ii(t,i)}get sdkDebugProperties(){var{sessionStartTimestamp:t}=this.It.checkAndGetSessionAndWindowId(!0);return{$recording_status:this.status,$sdk_debug_replay_internal_buffer_length:this.M.data.length,$sdk_debug_replay_internal_buffer_size:this.M.size,$sdk_debug_current_session_duration:this.Mt,$sdk_debug_session_start:t}}}var vs=z(\"[SegmentIntegration]\");function cs(t,i){var e=t.config.segment;if(!e)return i();!function(t,i){var e=t.config.segment;if(!e)return i();var r=e=>{var r=()=>e.anonymousId()||Ni();t.config.get_device_id=r,e.id()&&(t.register({distinct_id:e.id(),$device_id:r()}),t.persistence.set_property(At,\"identified\")),i()},s=e.user();\"then\"in s&&E(s.then)?s.then((t=>r(t))):r(s)}(t,(()=>{e.register((t=>{Promise&&Promise.resolve||vs.warn(\"This browser does not have Promise support, and can not use the segment integration\");var i=(i,e)=>{if(!e)return i;i.event.userId||i.event.anonymousId===t.get_distinct_id()||(vs.info(\"No userId set, resetting PostHog\"),t.reset()),i.event.userId&&i.event.userId!==t.get_distinct_id()&&(vs.info(\"UserId set, identifying with PostHog\"),t.identify(i.event.userId));var r=t.calculateEventProperties(e,i.event.properties);return i.event.properties=Object.assign({},r,i.event.properties),i};return{name:\"PostHog JS\",type:\"enrichment\",version:\"1.0.0\",isLoaded:()=>!0,load:()=>Promise.resolve(),track:t=>i(t,t.event.event),page:t=>i(t,\"$pageview\"),identify:t=>i(t,\"$identify\"),screen:t=>i(t,\"$screen\")}})(t)).then((()=>{i()}))}))}var fs=\"posthog-js\";function ps(t,i){var{organization:e,projectId:r,prefix:s,severityAllowList:n=[\"error\"]}=void 0===i?{}:i;return i=>{var o,a,l,u,h;if(!(\"*\"===n||n.includes(i.level))||!t.__loaded)return i;i.tags||(i.tags={});var d=t.requestRouter.endpointFor(\"ui\",\"/project/\"+t.config.token+\"/person/\"+t.get_distinct_id());i.tags[\"PostHog Person URL\"]=d,t.sessionRecordingStarted()&&(i.tags[\"PostHog Recording URL\"]=t.get_session_replay_url({withTimestamp:!0}));var v=(null==(o=i.exception)?void 0:o.values)||[],c=v.map((t=>B({},t,{stacktrace:t.stacktrace?B({},t.stacktrace,{type:\"raw\",frames:(t.stacktrace.frames||[]).map((t=>B({},t,{platform:\"web:javascript\"})))}):void 0}))),f={$exception_message:(null==(a=v[0])?void 0:a.value)||i.message,$exception_type:null==(l=v[0])?void 0:l.type,$exception_personURL:d,$exception_level:i.level,$exception_list:c,$sentry_event_id:i.event_id,$sentry_exception:i.exception,$sentry_exception_message:(null==(u=v[0])?void 0:u.value)||i.message,$sentry_exception_type:null==(h=v[0])?void 0:h.type,$sentry_tags:i.tags};return e&&r&&(f.$sentry_url=(s||\"https://sentry.io/organizations/\")+e+\"/issues/?project=\"+r+\"&query=\"+i.event_id),t.exceptions.sendExceptionEvent(f),i}}class gs{constructor(t,i,e,r,s){this.name=fs,this.setupOnce=function(n){n(ps(t,{organization:i,projectId:e,prefix:r,severityAllowList:s}))}}}var _s=null!=t&&t.location?xi(t.location.hash,\"__posthog\")||xi(location.hash,\"state\"):null,ms=\"_postHogToolbarParams\",bs=z(\"[Toolbar]\"),ws=function(t){return t[t.UNINITIALIZED=0]=\"UNINITIALIZED\",t[t.LOADING=1]=\"LOADING\",t[t.LOADED=2]=\"LOADED\",t}(ws||{});class ys{constructor(t){this.instance=t}Di(t){v.ph_toolbar_state=t}Li(){var t;return null!==(t=v.ph_toolbar_state)&&void 0!==t?t:ws.UNINITIALIZED}maybeLoadToolbar(i,e,r){if(void 0===i&&(i=void 0),void 0===e&&(e=void 0),void 0===r&&(r=void 0),!t||!o)return!1;i=null!=i?i:t.location,r=null!=r?r:t.history;try{if(!e){try{t.localStorage.setItem(\"test\",\"test\"),t.localStorage.removeItem(\"test\")}catch(t){return!1}e=null==t?void 0:t.localStorage}var s,n=_s||xi(i.hash,\"__posthog\")||xi(i.hash,\"state\"),a=n?X((()=>JSON.parse(atob(decodeURIComponent(n)))))||X((()=>JSON.parse(decodeURIComponent(n)))):null;return a&&\"ph_authorize\"===a.action?((s=a).source=\"url\",s&&Object.keys(s).length>0&&(a.desiredHash?i.hash=a.desiredHash:r?r.replaceState(r.state,\"\",i.pathname+i.search):i.hash=\"\")):((s=JSON.parse(e.getItem(ms)||\"{}\")).source=\"localstorage\",delete s.userIntent),!(!s.token||this.instance.config.token!==s.token)&&(this.loadToolbar(s),!0)}catch(t){return!1}}Ni(t){var i=v.ph_load_toolbar||v.ph_load_editor;!O(i)&&E(i)?i(t,this.instance):bs.warn(\"No toolbar load function found\")}loadToolbar(i){var e=!(null==o||!o.getElementById(qt));if(!t||e)return!1;var r=\"custom\"===this.instance.requestRouter.region&&this.instance.config.advanced_disable_toolbar_metrics,s=B({token:this.instance.config.token},i,{apiURL:this.instance.requestRouter.endpointFor(\"ui\")},r?{instrument:!1}:{});if(t.localStorage.setItem(ms,JSON.stringify(B({},s,{source:void 0}))),this.Li()===ws.LOADED)this.Ni(s);else if(this.Li()===ws.UNINITIALIZED){var n;this.Di(ws.LOADING),null==(n=v.__PosthogExtensions__)||null==n.loadExternalDependency||n.loadExternalDependency(this.instance,\"toolbar\",(t=>{if(t)return bs.error(\"[Toolbar] Failed to load\",t),void this.Di(ws.UNINITIALIZED);this.Di(ws.LOADED),this.Ni(s)})),st(t,\"turbolinks:load\",(()=>{this.Di(ws.UNINITIALIZED),this.loadToolbar(s)}))}return!0}ji(t){return this.loadToolbar(t)}maybeLoadEditor(t,i,e){return void 0===t&&(t=void 0),void 0===i&&(i=void 0),void 0===e&&(e=void 0),this.maybeLoadToolbar(t,i,e)}}var Ss=z(\"[TracingHeaders]\");class $s{constructor(t){this.zi=void 0,this.Ui=void 0,this.V=()=>{var t,i;R(this.zi)&&(null==(t=v.__PosthogExtensions__)||null==(t=t.tracingHeadersPatchFns)||t._patchXHR(this._instance.sessionManager));R(this.Ui)&&(null==(i=v.__PosthogExtensions__)||null==(i=i.tracingHeadersPatchFns)||i._patchFetch(this._instance.sessionManager))},this._instance=t}G(t){var i,e;null!=(i=v.__PosthogExtensions__)&&i.tracingHeadersPatchFns&&t(),null==(e=v.__PosthogExtensions__)||null==e.loadExternalDependency||e.loadExternalDependency(this._instance,\"tracing-headers\",(i=>{if(i)return Ss.error(\"failed to load script\",i);t()}))}startIfEnabledOrStop(){var t,i;this._instance.config.__add_tracing_headers?this.G(this.V):(null==(t=this.zi)||t.call(this),null==(i=this.Ui)||i.call(this),this.zi=void 0,this.Ui=void 0)}}var ks=z(\"[Web Vitals]\"),xs=9e5;class Es{constructor(t){var i;this.qi=!1,this.i=!1,this.M={url:void 0,metrics:[],firstMetricTimestamp:void 0},this.Bi=()=>{clearTimeout(this.Hi),0!==this.M.metrics.length&&(this._instance.capture(\"$web_vitals\",this.M.metrics.reduce(((t,i)=>B({},t,{[\"$web_vitals_\"+i.name+\"_event\"]:B({},i),[\"$web_vitals_\"+i.name+\"_value\"]:i.value})),{})),this.M={url:void 0,metrics:[],firstMetricTimestamp:void 0})},this.Wi=t=>{var i,e=null==(i=this._instance.sessionManager)?void 0:i.checkAndGetSessionAndWindowId(!0);if(R(e))ks.error(\"Could not read session ID. Dropping metrics!\");else{this.M=this.M||{url:void 0,metrics:[],firstMetricTimestamp:void 0};var r=this.Gi();if(!R(r))if(O(null==t?void 0:t.name)||O(null==t?void 0:t.value))ks.error(\"Invalid metric received\",t);else if(this.Ji&&t.value>=this.Ji)ks.error(\"Ignoring metric with value >= \"+this.Ji,t);else this.M.url!==r&&(this.Bi(),this.Hi=setTimeout(this.Bi,this.flushToCaptureTimeoutMs)),R(this.M.url)&&(this.M.url=r),this.M.firstMetricTimestamp=R(this.M.firstMetricTimestamp)?Date.now():this.M.firstMetricTimestamp,t.attribution&&t.attribution.interactionTargetElement&&(t.attribution.interactionTargetElement=void 0),this.M.metrics.push(B({},t,{$current_url:r,$session_id:e.sessionId,$window_id:e.windowId,timestamp:Date.now()})),this.M.metrics.length===this.allowedMetrics.length&&this.Bi()}},this.V=()=>{var t,i,e,r,s=v.__PosthogExtensions__;R(s)||R(s.postHogWebVitalsCallbacks)||({onLCP:t,onCLS:i,onFCP:e,onINP:r}=s.postHogWebVitalsCallbacks),t&&i&&e&&r?(this.allowedMetrics.indexOf(\"LCP\")>-1&&t(this.Wi.bind(this)),this.allowedMetrics.indexOf(\"CLS\")>-1&&i(this.Wi.bind(this)),this.allowedMetrics.indexOf(\"FCP\")>-1&&e(this.Wi.bind(this)),this.allowedMetrics.indexOf(\"INP\")>-1&&r(this.Wi.bind(this)),this.i=!0):ks.error(\"web vitals callbacks not loaded - not starting\")},this._instance=t,this.qi=!(null==(i=this._instance.persistence)||!i.props[vt]),this.startIfEnabled()}get allowedMetrics(){var t,i,e=I(this._instance.config.capture_performance)?null==(t=this._instance.config.capture_performance)?void 0:t.web_vitals_allowed_metrics:void 0;return R(e)?(null==(i=this._instance.persistence)?void 0:i.props[ft])||[\"CLS\",\"FCP\",\"INP\",\"LCP\"]:e}get flushToCaptureTimeoutMs(){return(I(this._instance.config.capture_performance)?this._instance.config.capture_performance.web_vitals_delayed_flush_ms:void 0)||5e3}get Ji(){var t=I(this._instance.config.capture_performance)&&F(this._instance.config.capture_performance.__web_vitals_max_value)?this._instance.config.capture_performance.__web_vitals_max_value:xs;return 0<t&&t<=6e4?xs:t}get isEnabled(){var t=null==a?void 0:a.protocol;if(\"http:\"!==t&&\"https:\"!==t)return ks.info(\"Web Vitals are disabled on non-http/https protocols\"),!1;var i=I(this._instance.config.capture_performance)?this._instance.config.capture_performance.web_vitals:A(this._instance.config.capture_performance)?this._instance.config.capture_performance:void 0;return A(i)?i:this.qi}startIfEnabled(){this.isEnabled&&!this.i&&(ks.info(\"enabled, starting...\"),this.G(this.V))}onRemoteConfig(t){var i=I(t.capturePerformance)&&!!t.capturePerformance.web_vitals,e=I(t.capturePerformance)?t.capturePerformance.web_vitals_allowed_metrics:void 0;this._instance.persistence&&(this._instance.persistence.register({[vt]:i}),this._instance.persistence.register({[ft]:e})),this.qi=i,this.startIfEnabled()}G(t){var i,e;null!=(i=v.__PosthogExtensions__)&&i.postHogWebVitalsCallbacks&&t(),null==(e=v.__PosthogExtensions__)||null==e.loadExternalDependency||e.loadExternalDependency(this._instance,\"web-vitals\",(i=>{i?ks.error(\"failed to load script\",i):t()}))}Gi(){var i=t?t.location.href:void 0;return i||ks.error(\"Could not determine current URL\"),i}}var Is=z(\"[Heatmaps]\");function Ps(t){return I(t)&&\"clientX\"in t&&\"clientY\"in t&&F(t.clientX)&&F(t.clientY)}class Rs{constructor(t){var i;this.rageclicks=new bi,this.qi=!1,this.i=!1,this.Vi=null,this.instance=t,this.qi=!(null==(i=this.instance.persistence)||!i.props[ut])}get flushIntervalMilliseconds(){var t=5e3;return I(this.instance.config.capture_heatmaps)&&this.instance.config.capture_heatmaps.flush_interval_milliseconds&&(t=this.instance.config.capture_heatmaps.flush_interval_milliseconds),t}get isEnabled(){return R(this.instance.config.capture_heatmaps)?R(this.instance.config.enable_heatmaps)?this.qi:this.instance.config.enable_heatmaps:!1!==this.instance.config.capture_heatmaps}startIfEnabled(){if(this.isEnabled){if(this.i)return;Is.info(\"starting...\"),this.Ki(),this.Vi=setInterval(this.Yi.bind(this),this.flushIntervalMilliseconds)}else{var t,i;clearInterval(null!==(t=this.Vi)&&void 0!==t?t:void 0),null==(i=this.Xi)||i.stop(),this.getAndClearBuffer()}}onRemoteConfig(t){var i=!!t.heatmaps;this.instance.persistence&&this.instance.persistence.register({[ut]:i}),this.qi=i,this.startIfEnabled()}getAndClearBuffer(){var t=this.M;return this.M=void 0,t}Qi(t){this.Zi(t.originalEvent,\"deadclick\")}Ki(){t&&o&&(st(t,\"beforeunload\",this.Yi.bind(this)),st(o,\"click\",(i=>this.Zi(i||(null==t?void 0:t.event))),{capture:!0}),st(o,\"mousemove\",(i=>this.te(i||(null==t?void 0:t.event))),{capture:!0}),this.Xi=new re(this.instance,ie,this.Qi.bind(this)),this.Xi.startIfEnabled(),this.i=!0)}ie(i,e){var r=this.instance.scrollManager.scrollY(),s=this.instance.scrollManager.scrollX(),n=this.instance.scrollManager.scrollElement(),o=function(i,e,r){for(var s=i;s&&Gt(s)&&!Jt(s,\"body\");){if(s===r)return!1;if(m(e,null==t?void 0:t.getComputedStyle(s).position))return!0;s=ri(s)}return!1}(ii(i),[\"fixed\",\"sticky\"],n);return{x:i.clientX+(o?0:s),y:i.clientY+(o?0:r),target_fixed:o,type:e}}Zi(t,i){var e;if(void 0===i&&(i=\"click\"),!Wt(t.target)&&Ps(t)){var r=this.ie(t,i);null!=(e=this.rageclicks)&&e.isRageClick(t.clientX,t.clientY,(new Date).getTime())&&this.ee(B({},r,{type:\"rageclick\"})),this.ee(r)}}te(t){!Wt(t.target)&&Ps(t)&&(clearTimeout(this.re),this.re=setTimeout((()=>{this.ee(this.ie(t,\"mousemove\"))}),500))}ee(i){if(t){var e=t.location.href;this.M=this.M||{},this.M[e]||(this.M[e]=[]),this.M[e].push(i)}}Yi(){this.M&&!P(this.M)&&this.instance.capture(\"$$heatmap\",{$heatmap_data:this.getAndClearBuffer()})}}class Ts{constructor(t){this._instance=t}doPageView(i,e){var r,s=this.se(i,e);return this.ne={pathname:null!==(r=null==t?void 0:t.location.pathname)&&void 0!==r?r:\"\",pageViewId:e,timestamp:i},this._instance.scrollManager.resetContext(),s}doPageLeave(t){var i;return this.se(t,null==(i=this.ne)?void 0:i.pageViewId)}doEvent(){var t;return{$pageview_id:null==(t=this.ne)?void 0:t.pageViewId}}se(t,i){var e=this.ne;if(!e)return{$pageview_id:i};var r={$pageview_id:i,$prev_pageview_id:e.pageViewId},s=this._instance.scrollManager.getContext();if(s&&!this._instance.config.disable_scroll_properties){var{maxScrollHeight:n,lastScrollY:o,maxScrollY:a,maxContentHeight:l,lastContentY:u,maxContentY:h}=s;if(!(R(n)||R(o)||R(a)||R(l)||R(u)||R(h))){n=Math.ceil(n),o=Math.ceil(o),a=Math.ceil(a),l=Math.ceil(l),u=Math.ceil(u),h=Math.ceil(h);var d=n<=1?1:Xe(o/n,0,1),v=n<=1?1:Xe(a/n,0,1),c=l<=1?1:Xe(u/l,0,1),f=l<=1?1:Xe(h/l,0,1);r=V(r,{$prev_pageview_last_scroll:o,$prev_pageview_last_scroll_percentage:d,$prev_pageview_max_scroll:a,$prev_pageview_max_scroll_percentage:v,$prev_pageview_last_content:u,$prev_pageview_last_content_percentage:c,$prev_pageview_max_content:h,$prev_pageview_max_content_percentage:f})}}return e.pathname&&(r.$prev_pageview_pathname=e.pathname),e.timestamp&&(r.$prev_pageview_duration=(t.getTime()-e.timestamp.getTime())/1e3),r}}var Ms=function(t){var i,e,r,s,n=\"\";for(i=e=0,r=(t=(t+\"\").replace(/\\r\\n/g,\"\\n\").replace(/\\r/g,\"\\n\")).length,s=0;s<r;s++){var o=t.charCodeAt(s),a=null;o<128?e++:a=o>127&&o<2048?String.fromCharCode(o>>6|192,63&o|128):String.fromCharCode(o>>12|224,o>>6&63|128,63&o|128),C(a)||(e>i&&(n+=t.substring(i,e)),n+=a,i=e=s+1)}return e>i&&(n+=t.substring(i,t.length)),n},Cs=!!u||!!l,Os=\"text/plain\",Fs=(t,i)=>{var[e,r]=t.split(\"?\"),s=B({},i);null==r||r.split(\"&\").forEach((t=>{var[i]=t.split(\"=\");delete s[i]}));var n=Si(s);return e+\"?\"+(n=n?(r?r+\"&\":\"\")+n:r)},As=(t,i)=>JSON.stringify(t,((t,i)=>\"bigint\"==typeof i?i.toString():i),i),Ds=t=>{var{data:i,compression:e}=t;if(i){if(e===g.GZipJS){var r=Ar(Dr(As(i)),{mtime:0}),s=new Blob([r],{type:Os});return{contentType:Os,body:s,estimatedSize:s.size}}if(e===g.Base64){var n=function(t){var i,e,r,s,n,o=\"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=\",a=0,l=0,u=\"\",h=[];if(!t)return t;t=Ms(t);do{i=(n=t.charCodeAt(a++)<<16|t.charCodeAt(a++)<<8|t.charCodeAt(a++))>>18&63,e=n>>12&63,r=n>>6&63,s=63&n,h[l++]=o.charAt(i)+o.charAt(e)+o.charAt(r)+o.charAt(s)}while(a<t.length);switch(u=h.join(\"\"),t.length%3){case 1:u=u.slice(0,-2)+\"==\";break;case 2:u=u.slice(0,-1)+\"=\"}return u}(As(i)),o=(t=>\"data=\"+encodeURIComponent(\"string\"==typeof t?t:As(t)))(n);return{contentType:\"application/x-www-form-urlencoded\",body:o,estimatedSize:new Blob([o]).size}}var a=As(i);return{contentType:\"application/json\",body:a,estimatedSize:new Blob([a]).size}}},Ls=[];l&&Ls.push({transport:\"fetch\",method:t=>{var i,e,{contentType:r,body:s,estimatedSize:n}=null!==(i=Ds(t))&&void 0!==i?i:{},o=new Headers;J(t.headers,(function(t,i){o.append(i,t)})),r&&o.append(\"Content-Type\",r);var a=t.url,u=null;if(h){var d=new h;u={signal:d.signal,timeout:setTimeout((()=>d.abort()),t.timeout)}}l(a,B({method:(null==t?void 0:t.method)||\"GET\",headers:o,keepalive:\"POST\"===t.method&&(n||0)<52428.8,body:s,signal:null==(e=u)?void 0:e.signal},t.fetchOptions)).then((i=>i.text().then((e=>{var r={statusCode:i.status,text:e};if(200===i.status)try{r.json=JSON.parse(e)}catch(t){j.error(t)}null==t.callback||t.callback(r)})))).catch((i=>{j.error(i),null==t.callback||t.callback({statusCode:0,text:i})})).finally((()=>u?clearTimeout(u.timeout):null))}}),u&&Ls.push({transport:\"XHR\",method:t=>{var i,e=new u;e.open(t.method||\"GET\",t.url,!0);var{contentType:r,body:s}=null!==(i=Ds(t))&&void 0!==i?i:{};J(t.headers,(function(t,i){e.setRequestHeader(i,t)})),r&&e.setRequestHeader(\"Content-Type\",r),t.timeout&&(e.timeout=t.timeout),e.withCredentials=!0,e.onreadystatechange=()=>{if(4===e.readyState){var i={statusCode:e.status,text:e.responseText};if(200===e.status)try{i.json=JSON.parse(e.responseText)}catch(t){}null==t.callback||t.callback(i)}},e.send(s)}}),null!=n&&n.sendBeacon&&Ls.push({transport:\"sendBeacon\",method:t=>{var i=Fs(t.url,{beacon:\"1\"});try{var e,{contentType:r,body:s}=null!==(e=Ds(t))&&void 0!==e?e:{},o=\"string\"==typeof s?new Blob([s],{type:r}):s;n.sendBeacon(i,o)}catch(t){}}});var Ns=function(t,i){if(!function(t){try{new RegExp(t)}catch(t){return!1}return!0}(i))return!1;try{return new RegExp(i).test(t)}catch(t){return!1}};function js(t,i,e){return As({distinct_id:t,userPropertiesToSet:i,userPropertiesToSetOnce:e})}var zs={exact:(t,i)=>i.some((i=>t.some((t=>i===t)))),is_not:(t,i)=>i.every((i=>t.every((t=>i!==t)))),regex:(t,i)=>i.some((i=>t.some((t=>Ns(i,t))))),not_regex:(t,i)=>i.every((i=>t.every((t=>!Ns(i,t))))),icontains:(t,i)=>i.map(Us).some((i=>t.map(Us).some((t=>i.includes(t))))),not_icontains:(t,i)=>i.map(Us).every((i=>t.map(Us).every((t=>!i.includes(t)))))},Us=t=>t.toLowerCase(),qs=z(\"[Error tracking]\");class Bs{constructor(t){var i,e;this.oe=[],this._instance=t,this.oe=null!==(i=null==(e=this._instance.persistence)?void 0:e.get_property(dt))&&void 0!==i?i:[]}onRemoteConfig(t){var i,e,r=null!==(i=null==(e=t.errorTracking)?void 0:e.suppressionRules)&&void 0!==i?i:[];this.oe=r,this._instance.persistence&&this._instance.persistence.register({[dt]:this.oe})}sendExceptionEvent(t){this.ae(t)?qs.info(\"Skipping exception capture because a suppression rule matched\"):this._instance.capture(\"$exception\",t,{_noTruncate:!0,_batchKey:\"exceptionEvent\"})}ae(t){var i=t.$exception_list;if(!i||!x(i)||0===i.length)return!1;var e=i.reduce(((t,i)=>{var{type:e,value:r}=i;return T(e)&&e.length>0&&t.$exception_types.push(e),T(r)&&r.length>0&&t.$exception_messages.push(r),t}),{$exception_types:[],$exception_messages:[]});return this.oe.some((t=>{var i=t.values.map((t=>{var i=zs[t.operator],r=x(t.value)?t.value:[t.value],s=e[t.key];return r.length>0&&i(r,s)}));return\"OR\"===t.type?i.some(Boolean):i.every(Boolean)}))}}var Hs=\"Mobile\",Ws=\"iOS\",Gs=\"Android\",Js=\"Tablet\",Vs=Gs+\" \"+Js,Ks=\"iPad\",Ys=\"Apple\",Xs=Ys+\" Watch\",Qs=\"Safari\",Zs=\"BlackBerry\",tn=\"Samsung\",en=tn+\"Browser\",rn=tn+\" Internet\",sn=\"Chrome\",nn=sn+\" OS\",on=sn+\" \"+Ws,an=\"Internet Explorer\",ln=an+\" \"+Hs,un=\"Opera\",hn=un+\" Mini\",dn=\"Edge\",vn=\"Microsoft \"+dn,cn=\"Firefox\",fn=cn+\" \"+Ws,pn=\"Nintendo\",gn=\"PlayStation\",_n=\"Xbox\",mn=Gs+\" \"+Hs,bn=Hs+\" \"+Qs,wn=\"Windows\",yn=wn+\" Phone\",Sn=\"Nokia\",$n=\"Ouya\",kn=\"Generic\",xn=kn+\" \"+Hs.toLowerCase(),En=kn+\" \"+Js.toLowerCase(),In=\"Konqueror\",Pn=\"(\\\\d+(\\\\.\\\\d+)?)\",Rn=new RegExp(\"Version/\"+Pn),Tn=new RegExp(_n,\"i\"),Mn=new RegExp(gn+\" \\\\w+\",\"i\"),Cn=new RegExp(pn+\" \\\\w+\",\"i\"),On=new RegExp(Zs+\"|PlayBook|BB10\",\"i\"),Fn={\"NT3.51\":\"NT 3.11\",\"NT4.0\":\"NT 4.0\",\"5.0\":\"2000\",5.1:\"XP\",5.2:\"XP\",\"6.0\":\"Vista\",6.1:\"7\",6.2:\"8\",6.3:\"8.1\",6.4:\"10\",\"10.0\":\"10\"};var An=(t,i)=>i&&m(i,Ys)||function(t){return m(t,Qs)&&!m(t,sn)&&!m(t,Gs)}(t),Dn=function(t,i){return i=i||\"\",m(t,\" OPR/\")&&m(t,\"Mini\")?hn:m(t,\" OPR/\")?un:On.test(t)?Zs:m(t,\"IE\"+Hs)||m(t,\"WPDesktop\")?ln:m(t,en)?rn:m(t,dn)||m(t,\"Edg/\")?vn:m(t,\"FBIOS\")?\"Facebook \"+Hs:m(t,\"UCWEB\")||m(t,\"UCBrowser\")?\"UC Browser\":m(t,\"CriOS\")?on:m(t,\"CrMo\")||m(t,sn)?sn:m(t,Gs)&&m(t,Qs)?mn:m(t,\"FxiOS\")?fn:m(t.toLowerCase(),In.toLowerCase())?In:An(t,i)?m(t,Hs)?bn:Qs:m(t,cn)?cn:m(t,\"MSIE\")||m(t,\"Trident/\")?an:m(t,\"Gecko\")?cn:\"\"},Ln={[ln]:[new RegExp(\"rv:\"+Pn)],[vn]:[new RegExp(dn+\"?\\\\/\"+Pn)],[sn]:[new RegExp(\"(\"+sn+\"|CrMo)\\\\/\"+Pn)],[on]:[new RegExp(\"CriOS\\\\/\"+Pn)],\"UC Browser\":[new RegExp(\"(UCBrowser|UCWEB)\\\\/\"+Pn)],[Qs]:[Rn],[bn]:[Rn],[un]:[new RegExp(\"(Opera|OPR)\\\\/\"+Pn)],[cn]:[new RegExp(cn+\"\\\\/\"+Pn)],[fn]:[new RegExp(\"FxiOS\\\\/\"+Pn)],[In]:[new RegExp(\"Konqueror[:/]?\"+Pn,\"i\")],[Zs]:[new RegExp(Zs+\" \"+Pn),Rn],[mn]:[new RegExp(\"android\\\\s\"+Pn,\"i\")],[rn]:[new RegExp(en+\"\\\\/\"+Pn)],[an]:[new RegExp(\"(rv:|MSIE )\"+Pn)],Mozilla:[new RegExp(\"rv:\"+Pn)]},Nn=function(t,i){var e=Dn(t,i),r=Ln[e];if(R(r))return null;for(var s=0;s<r.length;s++){var n=r[s],o=t.match(n);if(o)return parseFloat(o[o.length-2])}return null},jn=[[new RegExp(_n+\"; \"+_n+\" (.*?)[);]\",\"i\"),t=>[_n,t&&t[1]||\"\"]],[new RegExp(pn,\"i\"),[pn,\"\"]],[new RegExp(gn,\"i\"),[gn,\"\"]],[On,[Zs,\"\"]],[new RegExp(wn,\"i\"),(t,i)=>{if(/Phone/.test(i)||/WPDesktop/.test(i))return[yn,\"\"];if(new RegExp(Hs).test(i)&&!/IEMobile\\b/.test(i))return[wn+\" \"+Hs,\"\"];var e=/Windows NT ([0-9.]+)/i.exec(i);if(e&&e[1]){var r=e[1],s=Fn[r]||\"\";return/arm/i.test(i)&&(s=\"RT\"),[wn,s]}return[wn,\"\"]}],[/((iPhone|iPad|iPod).*?OS (\\d+)_(\\d+)_?(\\d+)?|iPhone)/,t=>{if(t&&t[3]){var i=[t[3],t[4],t[5]||\"0\"];return[Ws,i.join(\".\")]}return[Ws,\"\"]}],[/(watch.*\\/(\\d+\\.\\d+\\.\\d+)|watch os,(\\d+\\.\\d+),)/i,t=>{var i=\"\";return t&&t.length>=3&&(i=R(t[2])?t[3]:t[2]),[\"watchOS\",i]}],[new RegExp(\"(\"+Gs+\" (\\\\d+)\\\\.(\\\\d+)\\\\.?(\\\\d+)?|\"+Gs+\")\",\"i\"),t=>{if(t&&t[2]){var i=[t[2],t[3],t[4]||\"0\"];return[Gs,i.join(\".\")]}return[Gs,\"\"]}],[/Mac OS X (\\d+)[_.](\\d+)[_.]?(\\d+)?/i,t=>{var i=[\"Mac OS X\",\"\"];if(t&&t[1]){var e=[t[1],t[2],t[3]||\"0\"];i[1]=e.join(\".\")}return i}],[/Mac/i,[\"Mac OS X\",\"\"]],[/CrOS/,[nn,\"\"]],[/Linux|debian/i,[\"Linux\",\"\"]]],zn=function(t){return Cn.test(t)?pn:Mn.test(t)?gn:Tn.test(t)?_n:new RegExp($n,\"i\").test(t)?$n:new RegExp(\"(\"+yn+\"|WPDesktop)\",\"i\").test(t)?yn:/iPad/.test(t)?Ks:/iPod/.test(t)?\"iPod Touch\":/iPhone/.test(t)?\"iPhone\":/(watch)(?: ?os[,/]|\\d,\\d\\/)[\\d.]+/i.test(t)?Xs:On.test(t)?Zs:/(kobo)\\s(ereader|touch)/i.test(t)?\"Kobo\":new RegExp(Sn,\"i\").test(t)?Sn:/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\\))/i.test(t)||/(kf[a-z]+)( bui|\\)).+silk\\//i.test(t)?\"Kindle Fire\":/(Android|ZTE)/i.test(t)?!new RegExp(Hs).test(t)||/(9138B|TB782B|Nexus [97]|pixel c|HUAWEISHT|BTV|noble nook|smart ultra 6)/i.test(t)?/pixel[\\daxl ]{1,6}/i.test(t)&&!/pixel c/i.test(t)||/(huaweimed-al00|tah-|APA|SM-G92|i980|zte|U304AA)/i.test(t)||/lmy47v/i.test(t)&&!/QTAQZ3/i.test(t)?Gs:Vs:Gs:new RegExp(\"(pda|\"+Hs+\")\",\"i\").test(t)?xn:new RegExp(Js,\"i\").test(t)&&!new RegExp(Js+\" pc\",\"i\").test(t)?En:\"\"},Un=\"https?://(.*)\",qn=[\"gclid\",\"gclsrc\",\"dclid\",\"gbraid\",\"wbraid\",\"fbclid\",\"msclkid\",\"twclid\",\"li_fat_id\",\"igshid\",\"ttclid\",\"rdt_cid\",\"epik\",\"qclid\",\"sccid\",\"irclid\",\"_kx\"],Bn=K([\"utm_source\",\"utm_medium\",\"utm_campaign\",\"utm_content\",\"utm_term\",\"gad_source\",\"mc_cid\"],qn),Hn=\"<masked>\";function Wn(t,i,e){if(!o)return{};var r=i?K([],qn,e||[]):[];return Gn(ki(o.URL,r,Hn),t)}function Gn(t,i){var e=Bn.concat(i||[]),r={};return J(e,(function(i){var e=$i(t,i);r[i]=e||null})),r}function Jn(t){var i=function(t){return t?0===t.search(Un+\"google.([^/?]*)\")?\"google\":0===t.search(Un+\"bing.com\")?\"bing\":0===t.search(Un+\"yahoo.com\")?\"yahoo\":0===t.search(Un+\"duckduckgo.com\")?\"duckduckgo\":null:null}(t),e=\"yahoo\"!=i?\"q\":\"p\",r={};if(!C(i)){r.$search_engine=i;var s=o?$i(o.referrer,e):\"\";s.length&&(r.ph_keyword=s)}return r}function Vn(){return navigator.language||navigator.userLanguage}function Kn(){return(null==o?void 0:o.referrer)||\"$direct\"}function Yn(t,i){var e=t?K([],qn,i||[]):[],r=null==a?void 0:a.href.substring(0,1e3);return{r:Kn().substring(0,1e3),u:r?ki(r,e,Hn):void 0}}function Xn(t){var i,{r:e,u:r}=t,s={$referrer:e,$referring_domain:null==e?void 0:\"$direct\"==e?\"$direct\":null==(i=yi(e))?void 0:i.host};if(r){s.$current_url=r;var n=yi(r);s.$host=null==n?void 0:n.host,s.$pathname=null==n?void 0:n.pathname;var o=Gn(r);V(s,o)}if(e){var a=Jn(e);V(s,a)}return s}function Qn(){try{return Intl.DateTimeFormat().resolvedOptions().timeZone}catch(t){return}}function Zn(){try{return(new Date).getTimezoneOffset()}catch(t){return}}function to(i,e){if(!d)return{};var r,s,n,o=i?K([],qn,e||[]):[],[l,u]=function(t){for(var i=0;i<jn.length;i++){var[e,r]=jn[i],s=e.exec(t),n=s&&(E(r)?r(s,t):r);if(n)return n}return[\"\",\"\"]}(d);return V(Z({$os:l,$os_version:u,$browser:Dn(d,navigator.vendor),$device:zn(d),$device_type:(s=d,n=zn(s),n===Ks||n===Vs||\"Kobo\"===n||\"Kindle Fire\"===n||n===En?Js:n===pn||n===_n||n===gn||n===$n?\"Console\":n===Xs?\"Wearable\":n?Hs:\"Desktop\"),$timezone:Qn(),$timezone_offset:Zn()}),{$current_url:ki(null==a?void 0:a.href,o,Hn),$host:null==a?void 0:a.host,$pathname:null==a?void 0:a.pathname,$raw_user_agent:d.length>1e3?d.substring(0,997)+\"...\":d,$browser_version:Nn(d,navigator.vendor),$browser_language:Vn(),$browser_language_prefix:(r=Vn(),\"string\"==typeof r?r.split(\"-\")[0]:void 0),$screen_height:null==t?void 0:t.screen.height,$screen_width:null==t?void 0:t.screen.width,$viewport_height:null==t?void 0:t.innerHeight,$viewport_width:null==t?void 0:t.innerWidth,$lib:\"web\",$lib_version:c.LIB_VERSION,$insert_id:Math.random().toString(36).substring(2,10)+Math.random().toString(36).substring(2,10),$time:Date.now()/1e3})}var io=z(\"[FeatureFlags]\"),eo=\"$active_feature_flags\",ro=\"$override_feature_flags\",so=\"$feature_flag_payloads\",no=\"$override_feature_flag_payloads\",oo=\"$feature_flag_request_id\",ao=t=>{var i={};for(var[e,r]of Y(t||{}))r&&(i[e]=r);return i},lo=t=>{var i=t.flags;return i?(t.featureFlags=Object.fromEntries(Object.keys(i).map((t=>{var e;return[t,null!==(e=i[t].variant)&&void 0!==e?e:i[t].enabled]}))),t.featureFlagPayloads=Object.fromEntries(Object.keys(i).filter((t=>i[t].enabled)).filter((t=>{var e;return null==(e=i[t].metadata)?void 0:e.payload})).map((t=>{var e;return[t,null==(e=i[t].metadata)?void 0:e.payload]})))):io.warn(\"Using an older version of the feature flags endpoint. Please upgrade your PostHog server to the latest version\"),t},uo=function(t){return t.FeatureFlags=\"feature_flags\",t.Recordings=\"recordings\",t}({});class ho{constructor(t){this.le=!1,this.ue=!1,this.he=!1,this.de=!1,this.ve=!1,this.ce=!1,this.fe=!1,this._instance=t,this.featureFlagEventHandlers=[]}decide(){if(this._instance.config.__preview_remote_config)this.ce=!0;else{var t=!this.pe&&(this._instance.config.advanced_disable_feature_flags||this._instance.config.advanced_disable_feature_flags_on_first_load);this.ge({disableFlags:t})}}get hasLoadedFlags(){return this.ue}getFlags(){return Object.keys(this.getFlagVariants())}getFlagsWithDetails(){var t=this._instance.get_property(Rt),i=this._instance.get_property(ro),e=this._instance.get_property(no);if(!e&&!i)return t||{};var r=V({},t||{}),s=[...new Set([...Object.keys(e||{}),...Object.keys(i||{})])];for(var n of s){var o,a,l=r[n],u=null==i?void 0:i[n],h=R(u)?null!==(o=null==l?void 0:l.enabled)&&void 0!==o&&o:!!u,d=R(u)?l.variant:\"string\"==typeof u?u:void 0,v=null==e?void 0:e[n],c=B({},l,{enabled:h,variant:h?null!=d?d:null==l?void 0:l.variant:void 0});if(h!==(null==l?void 0:l.enabled)&&(c.original_enabled=null==l?void 0:l.enabled),d!==(null==l?void 0:l.variant)&&(c.original_variant=null==l?void 0:l.variant),v)c.metadata=B({},null==l?void 0:l.metadata,{payload:v,original_payload:null==l||null==(a=l.metadata)?void 0:a.payload});r[n]=c}return this.le||(io.warn(\" Overriding feature flag details!\",{flagDetails:t,overriddenPayloads:e,finalDetails:r}),this.le=!0),r}getFlagVariants(){var t=this._instance.get_property(It),i=this._instance.get_property(ro);if(!i)return t||{};for(var e=V({},t),r=Object.keys(i),s=0;s<r.length;s++)e[r[s]]=i[r[s]];return this.le||(io.warn(\" Overriding feature flags!\",{enabledFlags:t,overriddenFlags:i,finalFlags:e}),this.le=!0),e}getFlagPayloads(){var t=this._instance.get_property(so),i=this._instance.get_property(no);if(!i)return t||{};for(var e=V({},t||{}),r=Object.keys(i),s=0;s<r.length;s++)e[r[s]]=i[r[s]];return this.le||(io.warn(\" Overriding feature flag payloads!\",{flagPayloads:t,overriddenPayloads:i,finalPayloads:e}),this.le=!0),e}reloadFeatureFlags(){this.de||this._instance.config.advanced_disable_feature_flags||this.pe||(this.pe=setTimeout((()=>{this.ge()}),5))}_e(){clearTimeout(this.pe),this.pe=void 0}ensureFlagsLoaded(){this.ue||this.he||this.pe||this.reloadFeatureFlags()}setAnonymousDistinctId(t){this.$anon_distinct_id=t}setReloadingPaused(t){this.de=t}ge(t){var i;if(this._e(),!this._instance.config.advanced_disable_decide)if(this.he)this.ve=!0;else{var e={token:this._instance.config.token,distinct_id:this._instance.get_distinct_id(),groups:this._instance.getGroups(),$anon_distinct_id:this.$anon_distinct_id,person_properties:B({},(null==(i=this._instance.persistence)?void 0:i.get_initial_props())||{},this._instance.get_property(Tt)||{}),group_properties:this._instance.get_property(Mt)};(null!=t&&t.disableFlags||this._instance.config.advanced_disable_feature_flags)&&(e.disable_flags=!0);var r=this._instance.config.__preview_flags_v2&&this._instance.config.__preview_remote_config;r&&(e.timezone=Qn()),this.he=!0,this._instance.me({method:\"POST\",url:this._instance.requestRouter.endpointFor(\"api\",r?\"/flags/?v=2\":\"/decide/?v=4\"),data:e,compression:this._instance.config.disable_compression?void 0:g.Base64,timeout:this._instance.config.feature_flag_request_timeout_ms,callback:t=>{var i,r,s=!0;(200===t.statusCode&&(this.ve||(this.$anon_distinct_id=void 0),s=!1),this.he=!1,this.ce)||(this.ce=!0,this._instance.be(null!==(r=t.json)&&void 0!==r?r:{}));if(!e.disable_flags||this.ve)if(this.fe=!s,t.json&&null!=(i=t.json.quotaLimited)&&i.includes(uo.FeatureFlags))io.warn(\"You have hit your feature flags quota limit, and will not be able to load feature flags until the quota is reset.  Please visit https://posthog.com/docs/billing/limits-alerts to learn more.\");else{var n;if(!e.disable_flags)this.receivedFeatureFlags(null!==(n=t.json)&&void 0!==n?n:{},s);this.ve&&(this.ve=!1,this.ge())}}})}}getFeatureFlag(t,i){if(void 0===i&&(i={}),this.ue||this.getFlags()&&this.getFlags().length>0){var e=this.getFlagVariants()[t],r=\"\"+e,s=this._instance.get_property(oo)||void 0,n=this._instance.get_property(Ft)||{};if((i.send_event||!(\"send_event\"in i))&&(!(t in n)||!n[t].includes(r))){var o,a,l,u,h,d,v,c,f;x(n[t])?n[t].push(r):n[t]=[r],null==(o=this._instance.persistence)||o.register({[Ft]:n});var p=this.getFeatureFlagDetails(t),g={$feature_flag:t,$feature_flag_response:e,$feature_flag_payload:this.getFeatureFlagPayload(t)||null,$feature_flag_request_id:s,$feature_flag_bootstrapped_response:(null==(a=this._instance.config.bootstrap)||null==(a=a.featureFlags)?void 0:a[t])||null,$feature_flag_bootstrapped_payload:(null==(l=this._instance.config.bootstrap)||null==(l=l.featureFlagPayloads)?void 0:l[t])||null,$used_bootstrap_value:!this.fe};R(null==p||null==(u=p.metadata)?void 0:u.version)||(g.$feature_flag_version=p.metadata.version);var _,m=null!==(h=null==p||null==(d=p.reason)?void 0:d.description)&&void 0!==h?h:null==p||null==(v=p.reason)?void 0:v.code;if(m&&(g.$feature_flag_reason=m),null!=p&&null!=(c=p.metadata)&&c.id&&(g.$feature_flag_id=p.metadata.id),R(null==p?void 0:p.original_variant)&&R(null==p?void 0:p.original_enabled)||(g.$feature_flag_original_response=R(p.original_variant)?p.original_enabled:p.original_variant),null!=p&&null!=(f=p.metadata)&&f.original_payload)g.$feature_flag_original_payload=null==p||null==(_=p.metadata)?void 0:_.original_payload;this._instance.capture(\"$feature_flag_called\",g)}return e}io.warn('getFeatureFlag for key \"'+t+\"\\\" failed. Feature flags didn't load in time.\")}getFeatureFlagDetails(t){return this.getFlagsWithDetails()[t]}getFeatureFlagPayload(t){return this.getFlagPayloads()[t]}getRemoteConfigPayload(t,i){var e=this._instance.config.token;this._instance.me({method:\"POST\",url:this._instance.requestRouter.endpointFor(\"api\",\"/decide/?v=4\"),data:{distinct_id:this._instance.get_distinct_id(),token:e},compression:this._instance.config.disable_compression?void 0:g.Base64,timeout:this._instance.config.feature_flag_request_timeout_ms,callback:e=>{var r,s=null==(r=e.json)?void 0:r.featureFlagPayloads;i((null==s?void 0:s[t])||void 0)}})}isFeatureEnabled(t,i){if(void 0===i&&(i={}),this.ue||this.getFlags()&&this.getFlags().length>0)return!!this.getFeatureFlag(t,i);io.warn('isFeatureEnabled for key \"'+t+\"\\\" failed. Feature flags didn't load in time.\")}addFeatureFlagsHandler(t){this.featureFlagEventHandlers.push(t)}removeFeatureFlagsHandler(t){this.featureFlagEventHandlers=this.featureFlagEventHandlers.filter((i=>i!==t))}receivedFeatureFlags(t,i){if(this._instance.persistence){this.ue=!0;var e=this.getFlagVariants(),r=this.getFlagPayloads(),s=this.getFlagsWithDetails();!function(t,i,e,r,s){void 0===e&&(e={}),void 0===r&&(r={}),void 0===s&&(s={});var n=lo(t),o=n.flags,a=n.featureFlags,l=n.featureFlagPayloads;if(a){var u=t.requestId;if(x(a)){io.warn(\"v1 of the feature flags endpoint is deprecated. Please use the latest version.\");var h={};if(a)for(var d=0;d<a.length;d++)h[a[d]]=!0;i&&i.register({[eo]:a,[It]:h})}else{var v=a,c=l,f=o;t.errorsWhileComputingFlags&&(v=B({},e,v),c=B({},r,c),f=B({},s,f)),i&&i.register(B({[eo]:Object.keys(ao(v)),[It]:v||{},[so]:c||{},[Rt]:f||{}},u?{[oo]:u}:{}))}}}(t,this._instance.persistence,e,r,s),this.we(i)}}override(t,i){void 0===i&&(i=!1),io.warn(\"override is deprecated. Please use overrideFeatureFlags instead.\"),this.overrideFeatureFlags({flags:t,suppressWarning:i})}overrideFeatureFlags(t){if(!this._instance.__loaded||!this._instance.persistence)return io.uninitializedWarning(\"posthog.featureFlags.overrideFeatureFlags\");if(!1===t)return this._instance.persistence.unregister(ro),this._instance.persistence.unregister(no),void this.we();if(t&&\"object\"==typeof t&&(\"flags\"in t||\"payloads\"in t)){var i,e=t;if(this.le=Boolean(null!==(i=e.suppressWarning)&&void 0!==i&&i),\"flags\"in e)if(!1===e.flags)this._instance.persistence.unregister(ro);else if(e.flags)if(x(e.flags)){for(var r={},s=0;s<e.flags.length;s++)r[e.flags[s]]=!0;this._instance.persistence.register({[ro]:r})}else this._instance.persistence.register({[ro]:e.flags});return\"payloads\"in e&&(!1===e.payloads?this._instance.persistence.unregister(no):e.payloads&&this._instance.persistence.register({[no]:e.payloads})),void this.we()}this.we()}onFeatureFlags(t){if(this.addFeatureFlagsHandler(t),this.ue){var{flags:i,flagVariants:e}=this.ye();t(i,e)}return()=>this.removeFeatureFlagsHandler(t)}updateEarlyAccessFeatureEnrollment(t,i){var e,r=(this._instance.get_property(Pt)||[]).find((i=>i.flagKey===t)),s={[\"$feature_enrollment/\"+t]:i},n={$feature_flag:t,$feature_enrollment:i,$set:s};r&&(n.$early_access_feature_name=r.name),this._instance.capture(\"$feature_enrollment_update\",n),this.setPersonPropertiesForFlags(s,!1);var o=B({},this.getFlagVariants(),{[t]:i});null==(e=this._instance.persistence)||e.register({[eo]:Object.keys(ao(o)),[It]:o}),this.we()}getEarlyAccessFeatures(t,i,e){void 0===i&&(i=!1);var r=this._instance.get_property(Pt),s=e?\"&\"+e.map((t=>\"stage=\"+t)).join(\"&\"):\"\";if(r&&!i)return t(r);this._instance.me({url:this._instance.requestRouter.endpointFor(\"api\",\"/api/early_access_features/?token=\"+this._instance.config.token+s),method:\"GET\",callback:i=>{var e;if(i.json){var r=i.json.earlyAccessFeatures;return null==(e=this._instance.persistence)||e.register({[Pt]:r}),t(r)}}})}ye(){var t=this.getFlags(),i=this.getFlagVariants();return{flags:t.filter((t=>i[t])),flagVariants:Object.keys(i).filter((t=>i[t])).reduce(((t,e)=>(t[e]=i[e],t)),{})}}we(t){var{flags:i,flagVariants:e}=this.ye();this.featureFlagEventHandlers.forEach((r=>r(i,e,{errorsLoading:t})))}setPersonPropertiesForFlags(t,i){void 0===i&&(i=!0);var e=this._instance.get_property(Tt)||{};this._instance.register({[Tt]:B({},e,t)}),i&&this._instance.reloadFeatureFlags()}resetPersonPropertiesForFlags(){this._instance.unregister(Tt)}setGroupPropertiesForFlags(t,i){void 0===i&&(i=!0);var e=this._instance.get_property(Mt)||{};0!==Object.keys(e).length&&Object.keys(e).forEach((i=>{e[i]=B({},e[i],t[i]),delete t[i]})),this._instance.register({[Mt]:B({},e,t)}),i&&this._instance.reloadFeatureFlags()}resetGroupPropertiesForFlags(t){if(t){var i=this._instance.get_property(Mt)||{};this._instance.register({[Mt]:B({},i,{[t]:{}})})}else this._instance.unregister(Mt)}}var vo=[\"cookie\",\"localstorage\",\"localstorage+cookie\",\"sessionstorage\",\"memory\"];class co{constructor(t){this.S=t,this.props={},this.Se=!1,this.$e=(t=>{var i=\"\";return t.token&&(i=t.token.replace(/\\+/g,\"PL\").replace(/\\//g,\"SL\").replace(/=/g,\"EQ\")),t.persistence_name?\"ph_\"+t.persistence_name:\"ph_\"+i+\"_posthog\"})(t),this.q=this.ke(t),this.load(),t.debug&&j.info(\"Persistence loaded\",t.persistence,B({},this.props)),this.update_config(t,t),this.save()}ke(t){-1===vo.indexOf(t.persistence.toLowerCase())&&(j.critical(\"Unknown persistence type \"+t.persistence+\"; falling back to localStorage+cookie\"),t.persistence=\"localStorage+cookie\");var i=t.persistence.toLowerCase();return\"localstorage\"===i&&Wi.O()?Wi:\"localstorage+cookie\"===i&&Ji.O()?Ji:\"sessionstorage\"===i&&Xi.O()?Xi:\"memory\"===i?Ki:\"cookie\"===i?Bi:Ji.O()?Ji:Bi}properties(){var t={};return J(this.props,(function(i,e){if(e===It&&I(i))for(var r=Object.keys(i),n=0;n<r.length;n++)t[\"$feature/\"+r[n]]=i[r[n]];else a=e,l=!1,(C(o=Ht)?l:s&&o.indexOf===s?-1!=o.indexOf(a):(J(o,(function(t){if(l||(l=t===a))return W})),l))||(t[e]=i);var o,a,l})),t}load(){if(!this.xe){var t=this.q.D(this.$e);t&&(this.props=V({},t))}}save(){this.xe||this.q.L(this.$e,this.props,this.Ee,this.Ie,this.Pe,this.S.debug)}remove(){this.q.N(this.$e,!1),this.q.N(this.$e,!0)}clear(){this.remove(),this.props={}}register_once(t,i,e){if(I(t)){R(i)&&(i=\"None\"),this.Ee=R(e)?this.Re:e;var r=!1;if(J(t,((t,e)=>{this.props.hasOwnProperty(e)&&this.props[e]!==i||(this.props[e]=t,r=!0)})),r)return this.save(),!0}return!1}register(t,i){if(I(t)){this.Ee=R(i)?this.Re:i;var e=!1;if(J(t,((i,r)=>{t.hasOwnProperty(r)&&this.props[r]!==i&&(this.props[r]=i,e=!0)})),e)return this.save(),!0}return!1}unregister(t){t in this.props&&(delete this.props[t],this.save())}update_campaign_params(){if(!this.Se){var t=Wn(this.S.custom_campaign_params,this.S.mask_personal_data_properties,this.S.custom_personal_data_properties);P(Z(t))||this.register(t),this.Se=!0}}update_search_keyword(){var t;this.register((t=null==o?void 0:o.referrer)?Jn(t):{})}update_referrer_info(){var t;this.register_once({$referrer:Kn(),$referring_domain:null!=o&&o.referrer&&(null==(t=yi(o.referrer))?void 0:t.host)||\"$direct\"},void 0)}set_initial_person_info(){this.props[Nt]||this.props[jt]||this.register_once({[zt]:Yn(this.S.mask_personal_data_properties,this.S.custom_personal_data_properties)},void 0)}get_initial_props(){var t={};J([jt,Nt],(i=>{var e=this.props[i];e&&J(e,(function(i,e){t[\"$initial_\"+w(e)]=i}))}));var i,e,r=this.props[zt];if(r){var s=(i=Xn(r),e={},J(i,(function(t,i){e[\"$initial_\"+w(i)]=t})),e);V(t,s)}return t}safe_merge(t){return J(this.props,(function(i,e){e in t||(t[e]=i)})),t}update_config(t,i){if(this.Re=this.Ee=t.cookie_expiration,this.set_disabled(t.disable_persistence),this.set_cross_subdomain(t.cross_subdomain_cookie),this.set_secure(t.secure_cookie),t.persistence!==i.persistence){var e=this.ke(t),r=this.props;this.clear(),this.q=e,this.props=r,this.save()}}set_disabled(t){this.xe=t,this.xe?this.remove():this.save()}set_cross_subdomain(t){t!==this.Ie&&(this.Ie=t,this.remove(),this.save())}set_secure(t){t!==this.Pe&&(this.Pe=t,this.remove(),this.save())}set_event_timer(t,i){var e=this.props[at]||{};e[t]=i,this.props[at]=e,this.save()}remove_event_timer(t){var i=(this.props[at]||{})[t];return R(i)||(delete this.props[at][t],this.save()),i}get_property(t){return this.props[t]}set_property(t,i){this.props[t]=i,this.save()}}class fo{constructor(){this.Te={},this.Te={}}on(t,i){return this.Te[t]||(this.Te[t]=[]),this.Te[t].push(i),()=>{this.Te[t]=this.Te[t].filter((t=>t!==i))}}emit(t,i){for(var e of this.Te[t]||[])e(i);for(var r of this.Te[\"*\"]||[])r(t,i)}}class po{constructor(t){this.Me=new fo,this.Ce=(t,i)=>this.Oe(t,i)&&this.Fe(t,i)&&this.Ae(t,i),this.Oe=(t,i)=>null==i||!i.event||(null==t?void 0:t.event)===(null==i?void 0:i.event),this._instance=t,this.De=new Set,this.Le=new Set}init(){var t;if(!R(null==(t=this._instance)?void 0:t.Ne)){var i;null==(i=this._instance)||i.Ne(((t,i)=>{this.on(t,i)}))}}register(t){var i,e;if(!R(null==(i=this._instance)?void 0:i.Ne)&&(t.forEach((t=>{var i,e;null==(i=this.Le)||i.add(t),null==(e=t.steps)||e.forEach((t=>{var i;null==(i=this.De)||i.add((null==t?void 0:t.event)||\"\")}))})),null!=(e=this._instance)&&e.autocapture)){var r,s=new Set;t.forEach((t=>{var i;null==(i=t.steps)||i.forEach((t=>{null!=t&&t.selector&&s.add(null==t?void 0:t.selector)}))})),null==(r=this._instance)||r.autocapture.setElementSelectors(s)}}on(t,i){var e;null!=i&&0!=t.length&&(this.De.has(t)||this.De.has(null==i?void 0:i.event))&&this.Le&&(null==(e=this.Le)?void 0:e.size)>0&&this.Le.forEach((t=>{this.je(i,t)&&this.Me.emit(\"actionCaptured\",t.name)}))}ze(t){this.onAction(\"actionCaptured\",(i=>t(i)))}je(t,i){if(null==(null==i?void 0:i.steps))return!1;for(var e of i.steps)if(this.Ce(t,e))return!0;return!1}onAction(t,i){return this.Me.on(t,i)}Fe(t,i){if(null!=i&&i.url){var e,r=null==t||null==(e=t.properties)?void 0:e.$current_url;if(!r||\"string\"!=typeof r)return!1;if(!po.Ue(r,null==i?void 0:i.url,(null==i?void 0:i.url_matching)||\"contains\"))return!1}return!0}static Ue(i,e,r){switch(r){case\"regex\":return!!t&&Ns(i,e);case\"exact\":return e===i;case\"contains\":var s=po.qe(e).replace(/_/g,\".\").replace(/%/g,\".*\");return Ns(i,s);default:return!1}}static qe(t){return t.replace(/[|\\\\{}()[\\]^$+*?.]/g,\"\\\\$&\").replace(/-/g,\"\\\\x2d\")}Ae(t,i){if((null!=i&&i.href||null!=i&&i.tag_name||null!=i&&i.text)&&!this.Be(t).some((t=>!(null!=i&&i.href&&!po.Ue(t.href||\"\",null==i?void 0:i.href,(null==i?void 0:i.href_matching)||\"exact\"))&&((null==i||!i.tag_name||t.tag_name===(null==i?void 0:i.tag_name))&&!(null!=i&&i.text&&!po.Ue(t.text||\"\",null==i?void 0:i.text,(null==i?void 0:i.text_matching)||\"exact\")&&!po.Ue(t.$el_text||\"\",null==i?void 0:i.text,(null==i?void 0:i.text_matching)||\"exact\"))))))return!1;if(null!=i&&i.selector){var e,r=null==t||null==(e=t.properties)?void 0:e.$element_selectors;if(!r)return!1;if(!r.includes(null==i?void 0:i.selector))return!1}return!0}Be(t){return null==(null==t?void 0:t.properties.$elements)?[]:null==t?void 0:t.properties.$elements}}var go=z(\"[Surveys]\");class _o{constructor(t){this._instance=t,this.He=new Map,this.We=new Map}register(t){var i;R(null==(i=this._instance)?void 0:i.Ne)||(this.Ge(t),this.Je(t))}Je(t){var i=t.filter((t=>{var i,e;return(null==(i=t.conditions)?void 0:i.actions)&&(null==(e=t.conditions)||null==(e=e.actions)||null==(e=e.values)?void 0:e.length)>0}));if(0!==i.length){if(null==this.Ve){this.Ve=new po(this._instance),this.Ve.init();this.Ve.ze((t=>{this.onAction(t)}))}i.forEach((t=>{var i,e,r,s,n;t.conditions&&null!=(i=t.conditions)&&i.actions&&null!=(e=t.conditions)&&null!=(e=e.actions)&&e.values&&(null==(r=t.conditions)||null==(r=r.actions)||null==(r=r.values)?void 0:r.length)>0&&(null==(s=this.Ve)||s.register(t.conditions.actions.values),null==(n=t.conditions)||null==(n=n.actions)||null==(n=n.values)||n.forEach((i=>{if(i&&i.name){var e=this.We.get(i.name);e&&e.push(t.id),this.We.set(i.name,e||[t.id])}})))}))}}Ge(t){var i;if(0!==t.filter((t=>{var i,e;return(null==(i=t.conditions)?void 0:i.events)&&(null==(e=t.conditions)||null==(e=e.events)||null==(e=e.values)?void 0:e.length)>0})).length){null==(i=this._instance)||i.Ne(((t,i)=>{this.onEvent(t,i)})),t.forEach((t=>{var i;null==(i=t.conditions)||null==(i=i.events)||null==(i=i.values)||i.forEach((i=>{if(i&&i.name){var e=this.He.get(i.name);e&&e.push(t.id),this.He.set(i.name,e||[t.id])}}))}))}}onEvent(t,i){var e,r=(null==(e=this._instance)||null==(e=e.persistence)?void 0:e.props[Ot])||[];if(\"survey shown\"===t&&i&&r.length>0){var s;go.info(\"survey event matched, removing survey from activated surveys\",{event:t,eventPayload:i,existingActivatedSurveys:r});var n=null==i||null==(s=i.properties)?void 0:s.$survey_id;if(n){var o=r.indexOf(n);o>=0&&(r.splice(o,1),this.Ke(r))}}else this.He.has(t)&&(go.info(\"survey event matched, updating activated surveys\",{event:t,surveys:this.He.get(t)}),this.Ke(r.concat(this.He.get(t)||[])))}onAction(t){var i,e=(null==(i=this._instance)||null==(i=i.persistence)?void 0:i.props[Ot])||[];this.We.has(t)&&this.Ke(e.concat(this.We.get(t)||[]))}Ke(t){var i;null==(i=this._instance)||null==(i=i.persistence)||i.register({[Ot]:[...new Set(t)]})}getSurveys(){var t,i=null==(t=this._instance)||null==(t=t.persistence)?void 0:t.props[Ot];return i||[]}getEventToSurveys(){return this.He}Ye(){return this.Ve}}class mo{constructor(t){this.Xe=null,this.Qe=!1,this.Ze=!1,this.tr=[],this._instance=t,this._surveyEventReceiver=null}onRemoteConfig(t){var i=t.surveys;if(O(i))return go.warn(\"Decide not loaded yet. Not loading surveys.\");var e=x(i);this.ir=e?i.length>0:i,go.info(\"decide response received, hasSurveys: \"+this.ir),this.ir&&this.loadIfEnabled()}reset(){localStorage.removeItem(\"lastSeenSurveyDate\");for(var t=[],i=0;i<localStorage.length;i++){var e=localStorage.key(i);(null!=e&&e.startsWith(\"seenSurvey_\")||null!=e&&e.startsWith(\"inProgressSurvey_\"))&&t.push(e)}t.forEach((t=>localStorage.removeItem(t)))}loadIfEnabled(){if(!this.Xe)if(this.Ze)go.info(\"Already initializing surveys, skipping...\");else if(this._instance.config.disable_surveys)go.info(\"Disabled. Not loading surveys.\");else if(this.ir){var t=null==v?void 0:v.__PosthogExtensions__;if(t){this.Ze=!0;try{var i=t.generateSurveys;if(i)return void this.er(i);var e=t.loadExternalDependency;if(!e)return void this.rr(\"PostHog loadExternalDependency extension not found.\");e(this._instance,\"surveys\",(i=>{i||!t.generateSurveys?this.rr(\"Could not load surveys script\",i):this.er(t.generateSurveys)}))}catch(t){throw this.rr(\"Error initializing surveys\",t),t}finally{this.Ze=!1}}else go.error(\"PostHog Extensions not found.\")}else go.info(\"No surveys to load.\")}er(t){this.Xe=t(this._instance),this._surveyEventReceiver=new _o(this._instance),go.info(\"Surveys loaded successfully\"),this.sr({isLoaded:!0})}rr(t,i){go.error(t,i),this.sr({isLoaded:!1,error:t})}onSurveysLoaded(t){return this.tr.push(t),this.Xe&&this.sr({isLoaded:!0}),()=>{this.tr=this.tr.filter((i=>i!==t))}}getSurveys(t,i){if(void 0===i&&(i=!1),this._instance.config.disable_surveys)return go.info(\"Disabled. Not loading surveys.\"),t([]);var e=this._instance.get_property(Ct);if(e&&!i)return t(e,{isLoaded:!0});if(this.Qe)return t([],{isLoaded:!1,error:\"Surveys are already being loaded\"});try{this.Qe=!0,this._instance.me({url:this._instance.requestRouter.endpointFor(\"api\",\"/api/surveys/?token=\"+this._instance.config.token),method:\"GET\",timeout:this._instance.config.surveys_request_timeout_ms,callback:i=>{var e;this.Qe=!1;var r=i.statusCode;if(200!==r||!i.json){var s=\"Surveys API could not be loaded, status: \"+r;return go.error(s),t([],{isLoaded:!1,error:s})}var n,o=i.json.surveys||[],a=o.filter((t=>function(t){return!(!t.start_date||t.end_date)}(t)&&(function(t){var i;return!(null==(i=t.conditions)||null==(i=i.events)||null==(i=i.values)||!i.length)}(t)||function(t){var i;return!(null==(i=t.conditions)||null==(i=i.actions)||null==(i=i.values)||!i.length)}(t))));a.length>0&&(null==(n=this._surveyEventReceiver)||n.register(a));return null==(e=this._instance.persistence)||e.register({[Ct]:o}),t(o,{isLoaded:!0})}})}catch(t){throw this.Qe=!1,t}}sr(t){for(var i of this.tr)try{t.isLoaded?this.getSurveys(i):i([],t)}catch(t){go.error(\"Error in survey callback\",t)}}getActiveMatchingSurveys(t,i){if(void 0===i&&(i=!1),!O(this.Xe))return this.Xe.getActiveMatchingSurveys(t,i);go.warn(\"init was not called\")}nr(t){var i=null;return this.getSurveys((e=>{var r;i=null!==(r=e.find((i=>i.id===t)))&&void 0!==r?r:null})),i}ar(t){if(O(this.Xe))return{eligible:!1,reason:\"SDK is not enabled or survey functionality is not yet loaded\"};var i=\"string\"==typeof t?this.nr(t):t;return i?this.Xe.checkSurveyEligibility(i):{eligible:!1,reason:\"Survey not found\"}}canRenderSurvey(t){if(O(this.Xe))return go.warn(\"init was not called\"),{visible:!1,disabledReason:\"SDK is not enabled or survey functionality is not yet loaded\"};var i=this.ar(t);return{visible:i.eligible,disabledReason:i.reason}}canRenderSurveyAsync(t,i){return O(this.Xe)?(go.warn(\"init was not called\"),Promise.resolve({visible:!1,disabledReason:\"SDK is not enabled or survey functionality is not yet loaded\"})):new Promise((e=>{this.getSurveys((i=>{var r,s=null!==(r=i.find((i=>i.id===t)))&&void 0!==r?r:null;if(s){var n=this.ar(s);e({visible:n.eligible,disabledReason:n.reason})}else e({visible:!1,disabledReason:\"Survey not found\"})}),i)}))}renderSurvey(t,i){if(O(this.Xe))go.warn(\"init was not called\");else{var e=this.nr(t),r=null==o?void 0:o.querySelector(i);e?r?this.Xe.renderSurvey(e,r):go.warn(\"Survey element not found\"):go.warn(\"Survey not found\")}}}var bo=z(\"[RateLimiter]\");class wo{constructor(t){var i,e;this.serverLimits={},this.lastEventRateLimited=!1,this.checkForLimiting=t=>{var i=t.text;if(i&&i.length)try{(JSON.parse(i).quota_limited||[]).forEach((t=>{bo.info((t||\"events\")+\" is quota limited.\"),this.serverLimits[t]=(new Date).getTime()+6e4}))}catch(t){return void bo.warn('could not rate limit - continuing. Error: \"'+(null==t?void 0:t.message)+'\"',{text:i})}},this.instance=t,this.captureEventsPerSecond=(null==(i=t.config.rate_limiting)?void 0:i.events_per_second)||10,this.captureEventsBurstLimit=Math.max((null==(e=t.config.rate_limiting)?void 0:e.events_burst_limit)||10*this.captureEventsPerSecond,this.captureEventsPerSecond),this.lastEventRateLimited=this.clientRateLimitContext(!0).isRateLimited}clientRateLimitContext(t){var i,e,r;void 0===t&&(t=!1);var s=(new Date).getTime(),n=null!==(i=null==(e=this.instance.persistence)?void 0:e.get_property(Lt))&&void 0!==i?i:{tokens:this.captureEventsBurstLimit,last:s};n.tokens+=(s-n.last)/1e3*this.captureEventsPerSecond,n.last=s,n.tokens>this.captureEventsBurstLimit&&(n.tokens=this.captureEventsBurstLimit);var o=n.tokens<1;return o||t||(n.tokens=Math.max(0,n.tokens-1)),!o||this.lastEventRateLimited||t||this.instance.capture(\"$$client_ingestion_warning\",{$$client_ingestion_warning_message:\"posthog-js client rate limited. Config is set to \"+this.captureEventsPerSecond+\" events per second and \"+this.captureEventsBurstLimit+\" events burst limit.\"},{skip_client_rate_limiting:!0}),this.lastEventRateLimited=o,null==(r=this.instance.persistence)||r.set_property(Lt,n),{isRateLimited:o,remainingTokens:n.tokens}}isServerRateLimited(t){var i=this.serverLimits[t||\"events\"]||!1;return!1!==i&&(new Date).getTime()<i}}var yo=z(\"[RemoteConfig]\");class So{constructor(t){this._instance=t}get remoteConfig(){var t;return null==(t=v._POSTHOG_REMOTE_CONFIG)||null==(t=t[this._instance.config.token])?void 0:t.config}lr(t){var i,e;null!=(i=v.__PosthogExtensions__)&&i.loadExternalDependency?null==(e=v.__PosthogExtensions__)||null==e.loadExternalDependency||e.loadExternalDependency(this._instance,\"remote-config\",(()=>t(this.remoteConfig))):(yo.error(\"PostHog Extensions not found. Cannot load remote config.\"),t())}ur(t){this._instance.me({method:\"GET\",url:this._instance.requestRouter.endpointFor(\"assets\",\"/array/\"+this._instance.config.token+\"/config\"),callback:i=>{t(i.json)}})}load(){try{if(this.remoteConfig)return yo.info(\"Using preloaded remote config\",this.remoteConfig),void this.be(this.remoteConfig);if(this._instance.config.advanced_disable_decide)return void yo.warn(\"Remote config is disabled. Falling back to local config.\");this.lr((t=>{if(!t)return yo.info(\"No config found after loading remote JS config. Falling back to JSON.\"),void this.ur((t=>{this.be(t)}));this.be(t)}))}catch(t){yo.error(\"Error loading remote config\",t)}}be(t){t?this._instance.config.__preview_remote_config?(this._instance.be(t),!1!==t.hasFeatureFlags&&this._instance.featureFlags.ensureFlagsLoaded()):yo.info(\"__preview_remote_config is disabled. Logging config instead\",t):yo.error(\"Failed to fetch remote config from PostHog.\")}}var $o=3e3;class ko{constructor(t,i){this.hr=!0,this.dr=[],this.vr=Xe((null==i?void 0:i.flush_interval_ms)||$o,250,5e3,\"flush interval\",$o),this.cr=t}enqueue(t){this.dr.push(t),this.pr||this.gr()}unload(){this._r();var t=this.dr.length>0?this.mr():{},i=Object.values(t);[...i.filter((t=>0===t.url.indexOf(\"/e\"))),...i.filter((t=>0!==t.url.indexOf(\"/e\")))].map((t=>{this.cr(B({},t,{transport:\"sendBeacon\"}))}))}enable(){this.hr=!1,this.gr()}gr(){var t=this;this.hr||(this.pr=setTimeout((()=>{if(this._r(),this.dr.length>0){var i=this.mr(),e=function(){var e=i[r],s=(new Date).getTime();e.data&&x(e.data)&&J(e.data,(t=>{t.offset=Math.abs(t.timestamp-s),delete t.timestamp})),t.cr(e)};for(var r in i)e()}}),this.vr))}_r(){clearTimeout(this.pr),this.pr=void 0}mr(){var t={};return J(this.dr,(i=>{var e,r=i,s=(r?r.batchKey:null)||r.url;R(t[s])&&(t[s]=B({},r,{data:[]})),null==(e=t[s].data)||e.push(r.data)})),this.dr=[],t}}var xo=[\"retriesPerformedSoFar\"];class Eo{constructor(i){this.br=!1,this.wr=3e3,this.dr=[],this._instance=i,this.dr=[],this.yr=!0,!R(t)&&\"onLine\"in t.navigator&&(this.yr=t.navigator.onLine,st(t,\"online\",(()=>{this.yr=!0,this.Yi()})),st(t,\"offline\",(()=>{this.yr=!1})))}get length(){return this.dr.length}retriableRequest(t){var{retriesPerformedSoFar:i}=t,e=H(t,xo);F(i)&&i>0&&(e.url=Fs(e.url,{retry_count:i})),this._instance.me(B({},e,{callback:t=>{200!==t.statusCode&&(t.statusCode<400||t.statusCode>=500)&&(null!=i?i:0)<10?this.Sr(B({retriesPerformedSoFar:i},e)):null==e.callback||e.callback(t)}}))}Sr(t){var i=t.retriesPerformedSoFar||0;t.retriesPerformedSoFar=i+1;var e=function(t){var i=3e3*Math.pow(2,t),e=i/2,r=Math.min(18e5,i),s=(Math.random()-.5)*(r-e);return Math.ceil(r+s)}(i),r=Date.now()+e;this.dr.push({retryAt:r,requestOptions:t});var s=\"Enqueued failed request for retry in \"+e;navigator.onLine||(s+=\" (Browser is offline)\"),j.warn(s),this.br||(this.br=!0,this.$r())}$r(){this.kr&&clearTimeout(this.kr),this.kr=setTimeout((()=>{this.yr&&this.dr.length>0&&this.Yi(),this.$r()}),this.wr)}Yi(){var t=Date.now(),i=[],e=this.dr.filter((e=>e.retryAt<t||(i.push(e),!1)));if(this.dr=i,e.length>0)for(var{requestOptions:r}of e)this.retriableRequest(r)}unload(){for(var{requestOptions:t}of(this.kr&&(clearTimeout(this.kr),this.kr=void 0),this.dr))try{this._instance.me(B({},t,{transport:\"sendBeacon\"}))}catch(t){j.error(t)}this.dr=[]}}class Io{constructor(t){this.Er=()=>{var t,i,e,r;this.Ir||(this.Ir={});var s=this.scrollElement(),n=this.scrollY(),o=s?Math.max(0,s.scrollHeight-s.clientHeight):0,a=n+((null==s?void 0:s.clientHeight)||0),l=(null==s?void 0:s.scrollHeight)||0;this.Ir.lastScrollY=Math.ceil(n),this.Ir.maxScrollY=Math.max(n,null!==(t=this.Ir.maxScrollY)&&void 0!==t?t:0),this.Ir.maxScrollHeight=Math.max(o,null!==(i=this.Ir.maxScrollHeight)&&void 0!==i?i:0),this.Ir.lastContentY=a,this.Ir.maxContentY=Math.max(a,null!==(e=this.Ir.maxContentY)&&void 0!==e?e:0),this.Ir.maxContentHeight=Math.max(l,null!==(r=this.Ir.maxContentHeight)&&void 0!==r?r:0)},this._instance=t}getContext(){return this.Ir}resetContext(){var t=this.Ir;return setTimeout(this.Er,0),t}startMeasuringScrollPosition(){st(t,\"scroll\",this.Er,{capture:!0}),st(t,\"scrollend\",this.Er,{capture:!0}),st(t,\"resize\",this.Er)}scrollElement(){if(!this._instance.config.scroll_root_selector)return null==t?void 0:t.document.documentElement;var i=x(this._instance.config.scroll_root_selector)?this._instance.config.scroll_root_selector:[this._instance.config.scroll_root_selector];for(var e of i){var r=null==t?void 0:t.document.querySelector(e);if(r)return r}}scrollY(){if(this._instance.config.scroll_root_selector){var i=this.scrollElement();return i&&i.scrollTop||0}return t&&(t.scrollY||t.pageYOffset||t.document.documentElement.scrollTop)||0}scrollX(){if(this._instance.config.scroll_root_selector){var i=this.scrollElement();return i&&i.scrollLeft||0}return t&&(t.scrollX||t.pageXOffset||t.document.documentElement.scrollLeft)||0}}var Po=t=>Yn(null==t?void 0:t.config.mask_personal_data_properties,null==t?void 0:t.config.custom_personal_data_properties);class Ro{constructor(t,i,e,r){this.Pr=t=>{var i=this.Rr();if(!i||i.sessionId!==t){var e={sessionId:t,props:this.Tr(this._instance)};this.Mr.register({[Dt]:e})}},this._instance=t,this.Cr=i,this.Mr=e,this.Tr=r||Po,this.Cr.onSessionId(this.Pr)}Rr(){return this.Mr.props[Dt]}getSetOnceProps(){var t,i=null==(t=this.Rr())?void 0:t.props;return i?\"r\"in i?Xn(i):{$referring_domain:i.referringDomain,$pathname:i.initialPathName,utm_source:i.utm_source,utm_campaign:i.utm_campaign,utm_medium:i.utm_medium,utm_content:i.utm_content,utm_term:i.utm_term}:{}}getSessionProps(){var t={};return J(Z(this.getSetOnceProps()),((i,e)=>{\"$current_url\"===e&&(e=\"url\"),t[\"$session_entry_\"+w(e)]=i})),t}}var To=z(\"[SessionId]\");class Mo{constructor(t,i,e){var r;if(this.Or=[],!t.persistence)throw new Error(\"SessionIdManager requires a PostHogPersistence instance\");if(t.config.__preview_experimental_cookieless_mode)throw new Error(\"SessionIdManager cannot be used with __preview_experimental_cookieless_mode\");this.S=t.config,this.Mr=t.persistence,this.oi=void 0,this.kt=void 0,this._sessionStartTimestamp=null,this._sessionActivityTimestamp=null,this.Fr=i||Ni,this.Ar=e||Ni;var s=this.S.persistence_name||this.S.token,n=this.S.session_idle_timeout_seconds||1800;if(this._sessionTimeoutMs=1e3*Xe(n,60,36e3,\"session_idle_timeout_seconds\",1800),t.register({$configured_session_timeout_ms:this._sessionTimeoutMs}),this.Dr(),this.Lr=\"ph_\"+s+\"_window_id\",this.Nr=\"ph_\"+s+\"_primary_window_exists\",this.jr()){var o=Xi.D(this.Lr),a=Xi.D(this.Nr);o&&!a?this.oi=o:Xi.N(this.Lr),Xi.L(this.Nr,!0)}if(null!=(r=this.S.bootstrap)&&r.sessionID)try{var l=(t=>{var i=t.replace(/-/g,\"\");if(32!==i.length)throw new Error(\"Not a valid UUID\");if(\"7\"!==i[12])throw new Error(\"Not a UUIDv7\");return parseInt(i.substring(0,12),16)})(this.S.bootstrap.sessionID);this.zr(this.S.bootstrap.sessionID,(new Date).getTime(),l)}catch(t){To.error(\"Invalid sessionID in bootstrap\",t)}this.Ur()}get sessionTimeoutMs(){return this._sessionTimeoutMs}onSessionId(t){return R(this.Or)&&(this.Or=[]),this.Or.push(t),this.kt&&t(this.kt,this.oi),()=>{this.Or=this.Or.filter((i=>i!==t))}}jr(){return\"memory\"!==this.S.persistence&&!this.Mr.xe&&Xi.O()}qr(t){t!==this.oi&&(this.oi=t,this.jr()&&Xi.L(this.Lr,t))}Br(){return this.oi?this.oi:this.jr()?Xi.D(this.Lr):null}zr(t,i,e){t===this.kt&&i===this._sessionActivityTimestamp&&e===this._sessionStartTimestamp||(this._sessionStartTimestamp=e,this._sessionActivityTimestamp=i,this.kt=t,this.Mr.register({[$t]:[i,t,e]}))}Hr(){if(this.kt&&this._sessionActivityTimestamp&&this._sessionStartTimestamp)return[this._sessionActivityTimestamp,this.kt,this._sessionStartTimestamp];var t=this.Mr.props[$t];return x(t)&&2===t.length&&t.push(t[0]),t||[0,null,0]}resetSessionId(){this.zr(null,null,null)}Ur(){st(t,\"beforeunload\",(()=>{this.jr()&&Xi.N(this.Nr)}),{capture:!1})}checkAndGetSessionAndWindowId(t,i){if(void 0===t&&(t=!1),void 0===i&&(i=null),this.S.__preview_experimental_cookieless_mode)throw new Error(\"checkAndGetSessionAndWindowId should not be called in __preview_experimental_cookieless_mode\");var e=i||(new Date).getTime(),[r,s,n]=this.Hr(),o=this.Br(),a=F(n)&&n>0&&Math.abs(e-n)>864e5,l=!1,u=!s,h=!t&&Math.abs(e-r)>this.sessionTimeoutMs;u||h||a?(s=this.Fr(),o=this.Ar(),To.info(\"new session ID generated\",{sessionId:s,windowId:o,changeReason:{noSessionId:u,activityTimeout:h,sessionPastMaximumLength:a}}),n=e,l=!0):o||(o=this.Ar(),l=!0);var d=0===r||!t||a?e:r,v=0===n?(new Date).getTime():n;return this.qr(o),this.zr(s,d,v),t||this.Dr(),l&&this.Or.forEach((t=>t(s,o,l?{noSessionId:u,activityTimeout:h,sessionPastMaximumLength:a}:void 0))),{sessionId:s,windowId:o,sessionStartTimestamp:v,changeReason:l?{noSessionId:u,activityTimeout:h,sessionPastMaximumLength:a}:void 0,lastActivityTimestamp:r}}Dr(){clearTimeout(this.Wr),this.Wr=setTimeout((()=>{this.resetSessionId()}),1.1*this.sessionTimeoutMs)}}var Co=[\"$set_once\",\"$set\"],Oo=z(\"[SiteApps]\");class Fo{constructor(t){this._instance=t,this.Gr=[],this.apps={}}get isEnabled(){return!!this._instance.config.opt_in_site_apps}Jr(t,i){if(i){var e=this.globalsForEvent(i);this.Gr.push(e),this.Gr.length>1e3&&(this.Gr=this.Gr.slice(10))}}get siteAppLoaders(){var t;return null==(t=v._POSTHOG_REMOTE_CONFIG)||null==(t=t[this._instance.config.token])?void 0:t.siteApps}init(){if(this.isEnabled){var t=this._instance.Ne(this.Jr.bind(this));this.Vr=()=>{t(),this.Gr=[],this.Vr=void 0}}}globalsForEvent(t){var i,e,r,s,n,o,a;if(!t)throw new Error(\"Event payload is required\");var l={},u=this._instance.get_property(\"$groups\")||[],h=this._instance.get_property(\"$stored_group_properties\")||{};for(var[d,v]of Object.entries(h))l[d]={id:u[d],type:d,properties:v};var{$set_once:c,$set:f}=t;return{event:B({},H(t,Co),{properties:B({},t.properties,f?{$set:B({},null!==(i=null==(e=t.properties)?void 0:e.$set)&&void 0!==i?i:{},f)}:{},c?{$set_once:B({},null!==(r=null==(s=t.properties)?void 0:s.$set_once)&&void 0!==r?r:{},c)}:{}),elements_chain:null!==(n=null==(o=t.properties)?void 0:o.$elements_chain)&&void 0!==n?n:\"\",distinct_id:null==(a=t.properties)?void 0:a.distinct_id}),person:{properties:this._instance.get_property(\"$stored_person_properties\")},groups:l}}setupSiteApp(t){var i=this.apps[t.id],e=()=>{var e;(!i.errored&&this.Gr.length&&(Oo.info(\"Processing \"+this.Gr.length+\" events for site app with id \"+t.id),this.Gr.forEach((t=>null==i.processEvent?void 0:i.processEvent(t))),i.processedBuffer=!0),Object.values(this.apps).every((t=>t.processedBuffer||t.errored)))&&(null==(e=this.Vr)||e.call(this))},r=!1,s=s=>{i.errored=!s,i.loaded=!0,Oo.info(\"Site app with id \"+t.id+\" \"+(s?\"loaded\":\"errored\")),r&&e()};try{var{processEvent:n}=t.init({posthog:this._instance,callback:t=>{s(t)}});n&&(i.processEvent=n),r=!0}catch(i){Oo.error(\"Error while initializing PostHog app with config id \"+t.id,i),s(!1)}if(r&&i.loaded)try{e()}catch(e){Oo.error(\"Error while processing buffered events PostHog app with config id \"+t.id,e),i.errored=!0}}Kr(){var t=this.siteAppLoaders||[];for(var i of t)this.apps[i.id]={id:i.id,loaded:!1,errored:!1,processedBuffer:!1};for(var e of t)this.setupSiteApp(e)}Yr(t){if(0!==Object.keys(this.apps).length){var i=this.globalsForEvent(t);for(var e of Object.values(this.apps))try{null==e.processEvent||e.processEvent(i)}catch(i){Oo.error(\"Error while processing event \"+t.event+\" for site app \"+e.id,i)}}}onRemoteConfig(t){var i,e,r,s=this;if(null!=(i=this.siteAppLoaders)&&i.length)return this.isEnabled?(this.Kr(),void this._instance.on(\"eventCaptured\",(t=>this.Yr(t)))):void Oo.error('PostHog site apps are disabled. Enable the \"opt_in_site_apps\" config to proceed.');if(null==(e=this.Vr)||e.call(this),null!=(r=t.siteApps)&&r.length)if(this.isEnabled){var n=function(t){var i;v[\"__$$ph_site_app_\"+t]=s._instance,null==(i=v.__PosthogExtensions__)||null==i.loadSiteApp||i.loadSiteApp(s._instance,a,(i=>{if(i)return Oo.error(\"Error while initializing PostHog app with config id \"+t,i)}))};for(var{id:o,url:a}of t.siteApps)n(o)}else Oo.error('PostHog site apps are disabled. Enable the \"opt_in_site_apps\" config to proceed.')}}var Ao=[\"amazonbot\",\"amazonproductbot\",\"app.hypefactors.com\",\"applebot\",\"archive.org_bot\",\"awariobot\",\"backlinksextendedbot\",\"baiduspider\",\"bingbot\",\"bingpreview\",\"chrome-lighthouse\",\"dataforseobot\",\"deepscan\",\"duckduckbot\",\"facebookexternal\",\"facebookcatalog\",\"http://yandex.com/bots\",\"hubspot\",\"ia_archiver\",\"linkedinbot\",\"meta-externalagent\",\"mj12bot\",\"msnbot\",\"nessus\",\"petalbot\",\"pinterest\",\"prerender\",\"rogerbot\",\"screaming frog\",\"sebot-wa\",\"sitebulb\",\"slackbot\",\"slurp\",\"trendictionbot\",\"turnitin\",\"twitterbot\",\"vercelbot\",\"yahoo! slurp\",\"yandexbot\",\"zoombot\",\"bot.htm\",\"bot.php\",\"(bot;\",\"bot/\",\"crawler\",\"ahrefsbot\",\"ahrefssiteaudit\",\"semrushbot\",\"siteauditbot\",\"splitsignalbot\",\"gptbot\",\"oai-searchbot\",\"chatgpt-user\",\"perplexitybot\",\"better uptime bot\",\"sentryuptimebot\",\"uptimerobot\",\"headlesschrome\",\"cypress\",\"google-hoteladsverifier\",\"adsbot-google\",\"apis-google\",\"duplexweb-google\",\"feedfetcher-google\",\"google favicon\",\"google web preview\",\"google-read-aloud\",\"googlebot\",\"googleother\",\"google-cloudvertexbot\",\"googleweblight\",\"mediapartners-google\",\"storebot-google\",\"google-inspectiontool\",\"bytespider\"],Do=function(t,i){if(!t)return!1;var e=t.toLowerCase();return Ao.concat(i||[]).some((t=>{var i=t.toLowerCase();return-1!==e.indexOf(i)}))},Lo=function(t,i){if(!t)return!1;var e=t.userAgent;if(e&&Do(e,i))return!0;try{var r=null==t?void 0:t.userAgentData;if(null!=r&&r.brands&&r.brands.some((t=>Do(null==t?void 0:t.brand,i))))return!0}catch(t){}return!!t.webdriver},No=function(t){return t.US=\"us\",t.EU=\"eu\",t.CUSTOM=\"custom\",t}({}),jo=\"i.posthog.com\";class zo{constructor(t){this.Xr={},this.instance=t}get apiHost(){var t=this.instance.config.api_host.trim().replace(/\\/$/,\"\");return\"https://app.posthog.com\"===t?\"https://us.i.posthog.com\":t}get uiHost(){var t,i=null==(t=this.instance.config.ui_host)?void 0:t.replace(/\\/$/,\"\");return i||(i=this.apiHost.replace(\".\"+jo,\".posthog.com\")),\"https://app.posthog.com\"===i?\"https://us.posthog.com\":i}get region(){return this.Xr[this.apiHost]||(/https:\\/\\/(app|us|us-assets)(\\.i)?\\.posthog\\.com/i.test(this.apiHost)?this.Xr[this.apiHost]=No.US:/https:\\/\\/(eu|eu-assets)(\\.i)?\\.posthog\\.com/i.test(this.apiHost)?this.Xr[this.apiHost]=No.EU:this.Xr[this.apiHost]=No.CUSTOM),this.Xr[this.apiHost]}endpointFor(t,i){if(void 0===i&&(i=\"\"),i&&(i=\"/\"===i[0]?i:\"/\"+i),\"ui\"===t)return this.uiHost+i;if(this.region===No.CUSTOM)return this.apiHost+i;var e=jo+i;switch(t){case\"assets\":return\"https://\"+this.region+\"-assets.\"+e;case\"api\":return\"https://\"+this.region+\".\"+e}}}var Uo={icontains:(i,e)=>!!t&&e.href.toLowerCase().indexOf(i.toLowerCase())>-1,not_icontains:(i,e)=>!!t&&-1===e.href.toLowerCase().indexOf(i.toLowerCase()),regex:(i,e)=>!!t&&Ns(e.href,i),not_regex:(i,e)=>!!t&&!Ns(e.href,i),exact:(t,i)=>i.href===t,is_not:(t,i)=>i.href!==t};class qo{constructor(t){var i=this;this.getWebExperimentsAndEvaluateDisplayLogic=function(t){void 0===t&&(t=!1),i.getWebExperiments((t=>{qo.Qr(\"retrieved web experiments from the server\"),i.Zr=new Map,t.forEach((t=>{if(t.feature_flag_key){var e;if(i.Zr)qo.Qr(\"setting flag key \",t.feature_flag_key,\" to web experiment \",t),null==(e=i.Zr)||e.set(t.feature_flag_key,t);var r=i._instance.getFeatureFlag(t.feature_flag_key);T(r)&&t.variants[r]&&i.ts(t.name,r,t.variants[r].transforms)}else if(t.variants)for(var s in t.variants){var n=t.variants[s];qo.es(n)&&i.ts(t.name,s,n.transforms)}}))}),t)},this._instance=t,this._instance.onFeatureFlags((t=>{this.onFeatureFlags(t)}))}onFeatureFlags(t){if(this._is_bot())qo.Qr(\"Refusing to render web experiment since the viewer is a likely bot\");else if(!this._instance.config.disable_web_experiments){if(O(this.Zr))return this.Zr=new Map,this.loadIfEnabled(),void this.previewWebExperiment();qo.Qr(\"applying feature flags\",t),t.forEach((t=>{var i;if(this.Zr&&null!=(i=this.Zr)&&i.has(t)){var e,r=this._instance.getFeatureFlag(t),s=null==(e=this.Zr)?void 0:e.get(t);r&&null!=s&&s.variants[r]&&this.ts(s.name,r,s.variants[r].transforms)}}))}}previewWebExperiment(){var t=qo.getWindowLocation();if(null!=t&&t.search){var i=$i(null==t?void 0:t.search,\"__experiment_id\"),e=$i(null==t?void 0:t.search,\"__experiment_variant\");i&&e&&(qo.Qr(\"previewing web experiments \"+i+\" && \"+e),this.getWebExperiments((t=>{this.rs(parseInt(i),e,t)}),!1,!0))}}loadIfEnabled(){this._instance.config.disable_web_experiments||this.getWebExperimentsAndEvaluateDisplayLogic()}getWebExperiments(t,i,e){if(this._instance.config.disable_web_experiments&&!e)return t([]);var r=this._instance.get_property(\"$web_experiments\");if(r&&!i)return t(r);this._instance.me({url:this._instance.requestRouter.endpointFor(\"api\",\"/api/web_experiments/?token=\"+this._instance.config.token),method:\"GET\",callback:i=>{if(200!==i.statusCode||!i.json)return t([]);var e=i.json.experiments||[];return t(e)}})}rs(t,i,e){var r=e.filter((i=>i.id===t));r&&r.length>0&&(qo.Qr(\"Previewing web experiment [\"+r[0].name+\"] with variant [\"+i+\"]\"),this.ts(r[0].name,i,r[0].variants[i].transforms))}static es(t){return!O(t.conditions)&&(qo.ss(t)&&qo.ns(t))}static ss(t){var i;if(O(t.conditions)||O(null==(i=t.conditions)?void 0:i.url))return!0;var e,r,s,n=qo.getWindowLocation();return!!n&&(null==(e=t.conditions)||!e.url||Uo[null!==(r=null==(s=t.conditions)?void 0:s.urlMatchType)&&void 0!==r?r:\"icontains\"](t.conditions.url,n))}static getWindowLocation(){return null==t?void 0:t.location}static ns(t){var i;if(O(t.conditions)||O(null==(i=t.conditions)?void 0:i.utm))return!0;var e=Wn();if(e.utm_source){var r,s,n,o,a,l,u,h,d=null==(r=t.conditions)||null==(r=r.utm)||!r.utm_campaign||(null==(s=t.conditions)||null==(s=s.utm)?void 0:s.utm_campaign)==e.utm_campaign,v=null==(n=t.conditions)||null==(n=n.utm)||!n.utm_source||(null==(o=t.conditions)||null==(o=o.utm)?void 0:o.utm_source)==e.utm_source,c=null==(a=t.conditions)||null==(a=a.utm)||!a.utm_medium||(null==(l=t.conditions)||null==(l=l.utm)?void 0:l.utm_medium)==e.utm_medium,f=null==(u=t.conditions)||null==(u=u.utm)||!u.utm_term||(null==(h=t.conditions)||null==(h=h.utm)?void 0:h.utm_term)==e.utm_term;return d&&c&&f&&v}return!1}static Qr(t){for(var i=arguments.length,e=new Array(i>1?i-1:0),r=1;r<i;r++)e[r-1]=arguments[r];j.info(\"[WebExperiments] \"+t,e)}ts(t,i,e){this._is_bot()?qo.Qr(\"Refusing to render web experiment since the viewer is a likely bot\"):\"control\"!==i?e.forEach((e=>{if(e.selector){var r;qo.Qr(\"applying transform of variant \"+i+\" for experiment \"+t+\" \",e);var s=null==(r=document)?void 0:r.querySelectorAll(e.selector);null==s||s.forEach((t=>{var i=t;e.html&&(i.innerHTML=e.html),e.css&&i.setAttribute(\"style\",e.css)}))}})):qo.Qr(\"Control variants leave the page unmodified.\")}_is_bot(){return n&&this._instance?Lo(n,this._instance.config.custom_blocked_useragents):void 0}}var Bo={},Ho=()=>{},Wo=\"posthog\",Go=!Cs&&-1===(null==d?void 0:d.indexOf(\"MSIE\"))&&-1===(null==d?void 0:d.indexOf(\"Mozilla\")),Jo=()=>{var i;return{api_host:\"https://us.i.posthog.com\",ui_host:null,token:\"\",autocapture:!0,rageclick:!0,cross_subdomain_cookie:et(null==o?void 0:o.location),persistence:\"localStorage+cookie\",persistence_name:\"\",loaded:Ho,save_campaign_params:!0,custom_campaign_params:[],custom_blocked_useragents:[],save_referrer:!0,capture_pageview:!0,capture_pageleave:\"if_capture_pageview\",debug:a&&T(null==a?void 0:a.search)&&-1!==a.search.indexOf(\"__posthog_debug=true\")||!1,cookie_expiration:365,upgrade:!1,disable_session_recording:!1,disable_persistence:!1,disable_web_experiments:!0,disable_surveys:!1,disable_external_dependency_loading:!1,enable_recording_console_log:void 0,secure_cookie:\"https:\"===(null==t||null==(i=t.location)?void 0:i.protocol),ip:!0,opt_out_capturing_by_default:!1,opt_out_persistence_by_default:!1,opt_out_useragent_filter:!1,opt_out_capturing_persistence_type:\"localStorage\",opt_out_capturing_cookie_prefix:null,opt_in_site_apps:!1,property_denylist:[],respect_dnt:!1,sanitize_properties:null,request_headers:{},request_batching:!0,properties_string_max_length:65535,session_recording:{},mask_all_element_attributes:!1,mask_all_text:!1,mask_personal_data_properties:!1,custom_personal_data_properties:[],advanced_disable_decide:!1,advanced_disable_feature_flags:!1,advanced_disable_feature_flags_on_first_load:!1,advanced_disable_toolbar_metrics:!1,feature_flag_request_timeout_ms:3e3,surveys_request_timeout_ms:1e4,on_request_error:t=>{var i=\"Bad HTTP status: \"+t.statusCode+\" \"+t.text;j.error(i)},get_device_id:t=>t,capture_performance:void 0,name:\"posthog\",bootstrap:{},disable_compression:!1,session_idle_timeout_seconds:1800,person_profiles:\"identified_only\",before_send:void 0,request_queue_config:{flush_interval_ms:$o},_onCapture:Ho}},Vo=t=>{var i={};R(t.process_person)||(i.person_profiles=t.process_person),R(t.xhr_headers)||(i.request_headers=t.xhr_headers),R(t.cookie_name)||(i.persistence_name=t.cookie_name),R(t.disable_cookie)||(i.disable_persistence=t.disable_cookie),R(t.store_google)||(i.save_campaign_params=t.store_google),R(t.verbose)||(i.debug=t.verbose);var e=V({},i,t);return x(t.property_blacklist)&&(R(t.property_denylist)?e.property_denylist=t.property_blacklist:x(t.property_denylist)?e.property_denylist=[...t.property_blacklist,...t.property_denylist]:j.error(\"Invalid value for property_denylist config: \"+t.property_denylist)),e};class Ko{constructor(){this.__forceAllowLocalhost=!1}get os(){return this.__forceAllowLocalhost}set os(t){j.error(\"WebPerformanceObserver is deprecated and has no impact on network capture. Use `_forceAllowLocalhostNetworkCapture` on `posthog.sessionRecording`\"),this.__forceAllowLocalhost=t}}class Yo{get decideEndpointWasHit(){var t,i;return null!==(t=null==(i=this.featureFlags)?void 0:i.hasLoadedFlags)&&void 0!==t&&t}constructor(){this.webPerformance=new Ko,this.ls=!1,this.version=c.LIB_VERSION,this.us=new fo,this._calculate_event_properties=this.calculateEventProperties.bind(this),this.config=Jo(),this.SentryIntegration=gs,this.sentryIntegration=t=>function(t,i){var e=ps(t,i);return{name:fs,processEvent:t=>e(t)}}(this,t),this.__request_queue=[],this.__loaded=!1,this.analyticsDefaultEndpoint=\"/e/\",this.hs=!1,this.ds=null,this.vs=null,this.cs=null,this.featureFlags=new ho(this),this.toolbar=new ys(this),this.scrollManager=new Io(this),this.pageViewManager=new Ts(this),this.surveys=new mo(this),this.experiments=new qo(this),this.exceptions=new Bs(this),this.rateLimiter=new wo(this),this.requestRouter=new zo(this),this.consent=new Zi(this),this.people={set:(t,i,e)=>{var r=T(t)?{[t]:i}:t;this.setPersonProperties(r),null==e||e({})},set_once:(t,i,e)=>{var r=T(t)?{[t]:i}:t;this.setPersonProperties(void 0,r),null==e||e({})}},this.on(\"eventCaptured\",(t=>j.info('send \"'+(null==t?void 0:t.event)+'\"',t)))}init(t,i,e){if(e&&e!==Wo){var r,s=null!==(r=Bo[e])&&void 0!==r?r:new Yo;return s._init(t,i,e),Bo[e]=s,Bo[Wo][e]=s,s}return this._init(t,i,e)}_init(i,e,r){var s,n;if(void 0===e&&(e={}),R(i)||M(i))return j.critical(\"PostHog was initialized without a token. This likely indicates a misconfiguration. Please check the first argument passed to posthog.init()\"),this;if(this.__loaded)return j.warn(\"You have already initialized PostHog! Re-initializing is a no-op\"),this;this.__loaded=!0,this.config={},this.fs=[],e.person_profiles&&(this.vs=e.person_profiles),this.set_config(V({},Jo(),Vo(e),{name:r,token:i})),this.config.on_xhr_error&&j.error(\"on_xhr_error is deprecated. Use on_request_error instead\"),this.compression=e.disable_compression?void 0:g.GZipJS,this.persistence=new co(this.config),this.sessionPersistence=\"sessionStorage\"===this.config.persistence||\"memory\"===this.config.persistence?this.persistence:new co(B({},this.config,{persistence:\"sessionStorage\"}));var o=B({},this.persistence.props),a=B({},this.sessionPersistence.props);if(this.ps=new ko((t=>this.gs(t)),this.config.request_queue_config),this._s=new Eo(this),this.__request_queue=[],this.config.__preview_experimental_cookieless_mode||(this.sessionManager=new Mo(this),this.sessionPropsManager=new Ro(this,this.sessionManager,this.persistence)),new $s(this).startIfEnabledOrStop(),this.siteApps=new Fo(this),null==(s=this.siteApps)||s.init(),this.config.__preview_experimental_cookieless_mode||(this.sessionRecording=new ds(this),this.sessionRecording.startIfEnabledOrStop()),this.config.disable_scroll_properties||this.scrollManager.startMeasuringScrollPosition(),this.autocapture=new Mi(this),this.autocapture.startIfEnabled(),this.surveys.loadIfEnabled(),this.heatmaps=new Rs(this),this.heatmaps.startIfEnabled(),this.webVitalsAutocapture=new Es(this),this.exceptionObserver=new ne(this),this.exceptionObserver.startIfEnabled(),this.deadClicksAutocapture=new re(this,ee),this.deadClicksAutocapture.startIfEnabled(),this.historyAutocapture=new Le(this),this.historyAutocapture.startIfEnabled(),c.DEBUG=c.DEBUG||this.config.debug,c.DEBUG&&j.info(\"Starting in debug mode\",{this:this,config:e,thisC:B({},this.config),p:o,s:a}),this.bs(),void 0!==(null==(n=e.bootstrap)?void 0:n.distinctID)){var l,u,h=this.config.get_device_id(Ni()),d=null!=(l=e.bootstrap)&&l.isIdentifiedID?h:e.bootstrap.distinctID;this.persistence.set_property(At,null!=(u=e.bootstrap)&&u.isIdentifiedID?\"identified\":\"anonymous\"),this.register({distinct_id:e.bootstrap.distinctID,$device_id:d})}if(this.ws()){var v,f,p=Object.keys((null==(v=e.bootstrap)?void 0:v.featureFlags)||{}).filter((t=>{var i;return!(null==(i=e.bootstrap)||null==(i=i.featureFlags)||!i[t])})).reduce(((t,i)=>{var r;return t[i]=(null==(r=e.bootstrap)||null==(r=r.featureFlags)?void 0:r[i])||!1,t}),{}),_=Object.keys((null==(f=e.bootstrap)?void 0:f.featureFlagPayloads)||{}).filter((t=>p[t])).reduce(((t,i)=>{var r,s;null!=(r=e.bootstrap)&&null!=(r=r.featureFlagPayloads)&&r[i]&&(t[i]=null==(s=e.bootstrap)||null==(s=s.featureFlagPayloads)?void 0:s[i]);return t}),{});this.featureFlags.receivedFeatureFlags({featureFlags:p,featureFlagPayloads:_})}if(this.config.__preview_experimental_cookieless_mode)this.register_once({distinct_id:Bt,$device_id:null},\"\");else if(!this.get_distinct_id()){var m=this.config.get_device_id(Ni());this.register_once({distinct_id:m,$device_id:m},\"\"),this.persistence.set_property(At,\"anonymous\")}return st(t,\"onpagehide\"in self?\"pagehide\":\"unload\",this._handle_unload.bind(this),{passive:!1}),this.toolbar.maybeLoadToolbar(),e.segment?cs(this,(()=>this.ys())):this.ys(),E(this.config._onCapture)&&this.config._onCapture!==Ho&&(j.warn(\"onCapture is deprecated. Please use `before_send` instead\"),this.on(\"eventCaptured\",(t=>this.config._onCapture(t.event,t)))),this}be(t){var i,e,r,s,n,a,l,u;if(!o||!o.body)return j.info(\"document not ready yet, trying again in 500 milliseconds...\"),void setTimeout((()=>{this.be(t)}),500);this.compression=void 0,t.supportedCompression&&!this.config.disable_compression&&(this.compression=m(t.supportedCompression,g.GZipJS)?g.GZipJS:m(t.supportedCompression,g.Base64)?g.Base64:void 0),null!=(i=t.analytics)&&i.endpoint&&(this.analyticsDefaultEndpoint=t.analytics.endpoint),this.set_config({person_profiles:this.vs?this.vs:\"identified_only\"}),null==(e=this.siteApps)||e.onRemoteConfig(t),null==(r=this.sessionRecording)||r.onRemoteConfig(t),null==(s=this.autocapture)||s.onRemoteConfig(t),null==(n=this.heatmaps)||n.onRemoteConfig(t),this.surveys.onRemoteConfig(t),null==(a=this.webVitalsAutocapture)||a.onRemoteConfig(t),null==(l=this.exceptionObserver)||l.onRemoteConfig(t),this.exceptions.onRemoteConfig(t),null==(u=this.deadClicksAutocapture)||u.onRemoteConfig(t)}ys(){try{this.config.loaded(this)}catch(t){j.critical(\"`loaded` function failed\",t)}this.Ss(),this.config.capture_pageview&&setTimeout((()=>{this.consent.isOptedIn()&&this.$s()}),1),new So(this).load(),this.featureFlags.decide()}Ss(){var t;this.has_opted_out_capturing()||this.config.request_batching&&(null==(t=this.ps)||t.enable())}_dom_loaded(){this.has_opted_out_capturing()||G(this.__request_queue,(t=>this.gs(t))),this.__request_queue=[],this.Ss()}_handle_unload(){var t,i;this.config.request_batching?(this.ks()&&this.capture(\"$pageleave\"),null==(t=this.ps)||t.unload(),null==(i=this._s)||i.unload()):this.ks()&&this.capture(\"$pageleave\",null,{transport:\"sendBeacon\"})}me(t){this.__loaded&&(Go?this.__request_queue.push(t):this.rateLimiter.isServerRateLimited(t.batchKey)||(t.transport=t.transport||this.config.api_transport,t.url=Fs(t.url,{ip:this.config.ip?1:0}),t.headers=B({},this.config.request_headers),t.compression=\"best-available\"===t.compression?this.compression:t.compression,t.fetchOptions=t.fetchOptions||this.config.fetch_options,(t=>{var i,e,r,s=B({},t);s.timeout=s.timeout||6e4,s.url=Fs(s.url,{_:(new Date).getTime().toString(),ver:c.LIB_VERSION,compression:s.compression});var n=null!==(i=s.transport)&&void 0!==i?i:\"fetch\",o=null!==(e=null==(r=rt(Ls,(t=>t.transport===n)))?void 0:r.method)&&void 0!==e?e:Ls[0].method;if(!o)throw new Error(\"No available transport method\");o(s)})(B({},t,{callback:i=>{var e,r;(this.rateLimiter.checkForLimiting(i),i.statusCode>=400)&&(null==(e=(r=this.config).on_request_error)||e.call(r,i));null==t.callback||t.callback(i)}}))))}gs(t){this._s?this._s.retriableRequest(t):this.me(t)}_execute_array(t){var i,e=[],r=[],s=[];G(t,(t=>{t&&(i=t[0],x(i)?s.push(t):E(t)?t.call(this):x(t)&&\"alias\"===i?e.push(t):x(t)&&-1!==i.indexOf(\"capture\")&&E(this[i])?s.push(t):r.push(t))}));var n=function(t,i){G(t,(function(t){if(x(t[0])){var e=i;J(t,(function(t){e=e[t[0]].apply(e,t.slice(1))}))}else this[t[0]].apply(this,t.slice(1))}),i)};n(e,this),n(r,this),n(s,this)}ws(){var t,i;return(null==(t=this.config.bootstrap)?void 0:t.featureFlags)&&Object.keys(null==(i=this.config.bootstrap)?void 0:i.featureFlags).length>0||!1}push(t){this._execute_array([t])}capture(t,i,e){var r;if(this.__loaded&&this.persistence&&this.sessionPersistence&&this.ps){if(!this.consent.isOptedOut())if(!R(t)&&T(t)){if(this.config.opt_out_useragent_filter||!this._is_bot()){var s=null!=e&&e.skip_client_rate_limiting?void 0:this.rateLimiter.clientRateLimitContext();if(null==s||!s.isRateLimited){null!=i&&i.$current_url&&!T(null==i?void 0:i.$current_url)&&(j.error(\"Invalid `$current_url` property provided to `posthog.capture`. Input must be a string. Ignoring provided value.\"),null==i||delete i.$current_url),this.sessionPersistence.update_search_keyword(),this.config.save_campaign_params&&this.sessionPersistence.update_campaign_params(),this.config.save_referrer&&this.sessionPersistence.update_referrer_info(),(this.config.save_campaign_params||this.config.save_referrer)&&this.persistence.set_initial_person_info();var n=new Date,o=(null==e?void 0:e.timestamp)||n,a=Ni(),l={uuid:a,event:t,properties:this.calculateEventProperties(t,i||{},o,a)};s&&(l.properties.$lib_rate_limit_remaining_tokens=s.remainingTokens),(null==e?void 0:e.$set)&&(l.$set=null==e?void 0:e.$set);var u=this.xs(null==e?void 0:e.$set_once);u&&(l.$set_once=u),(l=tt(l,null!=e&&e._noTruncate?null:this.config.properties_string_max_length)).timestamp=o,R(null==e?void 0:e.timestamp)||(l.properties.$event_time_override_provided=!0,l.properties.$event_time_override_system_time=n);var h=B({},l.properties.$set,l.$set);if(P(h)||this.setPersonPropertiesForFlags(h),!O(this.config.before_send)){var d=this.Es(l);if(!d)return;l=d}this.us.emit(\"eventCaptured\",l);var v={method:\"POST\",url:null!==(r=null==e?void 0:e._url)&&void 0!==r?r:this.requestRouter.endpointFor(\"api\",this.analyticsDefaultEndpoint),data:l,compression:\"best-available\",batchKey:null==e?void 0:e._batchKey};return!this.config.request_batching||e&&(null==e||!e._batchKey)||null!=e&&e.send_instantly?this.gs(v):this.ps.enqueue(v),l}j.critical(\"This capture call is ignored due to client rate limiting.\")}}else j.error(\"No event name provided to posthog.capture\")}else j.uninitializedWarning(\"posthog.capture\")}Ne(t){return this.on(\"eventCaptured\",(i=>t(i.event,i)))}calculateEventProperties(t,i,e,r,s){if(e=e||new Date,!this.persistence||!this.sessionPersistence)return i;var n=s?void 0:this.persistence.remove_event_timer(t),a=B({},i);if(a.token=this.config.token,this.config.__preview_experimental_cookieless_mode&&(a.$cookieless_mode=!0),\"$snapshot\"===t){var l=B({},this.persistence.properties(),this.sessionPersistence.properties());return a.distinct_id=l.distinct_id,(!T(a.distinct_id)&&!F(a.distinct_id)||M(a.distinct_id))&&j.error(\"Invalid distinct_id for replay event. This indicates a bug in your implementation\"),a}var u,h=to(this.config.mask_personal_data_properties,this.config.custom_personal_data_properties);if(this.sessionManager){var{sessionId:v,windowId:c}=this.sessionManager.checkAndGetSessionAndWindowId(s,e.getTime());a.$session_id=v,a.$window_id=c}this.sessionPropsManager&&V(a,this.sessionPropsManager.getSessionProps());try{var f;this.sessionRecording&&V(a,this.sessionRecording.sdkDebugProperties),a.$sdk_debug_retry_queue_size=null==(f=this._s)?void 0:f.length}catch(t){a.$sdk_debug_error_capturing_properties=String(t)}if(this.requestRouter.region===No.CUSTOM&&(a.$lib_custom_api_host=this.config.api_host),u=\"$pageview\"!==t||s?\"$pageleave\"!==t||s?this.pageViewManager.doEvent():this.pageViewManager.doPageLeave(e):this.pageViewManager.doPageView(e,r),a=V(a,u),\"$pageview\"===t&&o&&(a.title=o.title),!R(n)){var p=e.getTime()-n;a.$duration=parseFloat((p/1e3).toFixed(3))}d&&this.config.opt_out_useragent_filter&&(a.$browser_type=this._is_bot()?\"bot\":\"browser\"),(a=V({},h,this.persistence.properties(),this.sessionPersistence.properties(),a)).$is_identified=this._isIdentified(),x(this.config.property_denylist)?J(this.config.property_denylist,(function(t){delete a[t]})):j.error(\"Invalid value for property_denylist config: \"+this.config.property_denylist+\" or property_blacklist config: \"+this.config.property_blacklist);var g=this.config.sanitize_properties;g&&(j.error(\"sanitize_properties is deprecated. Use before_send instead\"),a=g(a,t));var _=this.Is();return a.$process_person_profile=_,_&&!s&&this.Ps(\"_calculate_event_properties\"),a}xs(t){var i;if(!this.persistence||!this.Is())return t;if(this.ls)return t;var e=this.persistence.get_initial_props(),r=null==(i=this.sessionPropsManager)?void 0:i.getSetOnceProps(),s=V({},e,r||{},t||{}),n=this.config.sanitize_properties;return n&&(j.error(\"sanitize_properties is deprecated. Use before_send instead\"),s=n(s,\"$set_once\")),this.ls=!0,P(s)?void 0:s}register(t,i){var e;null==(e=this.persistence)||e.register(t,i)}register_once(t,i,e){var r;null==(r=this.persistence)||r.register_once(t,i,e)}register_for_session(t){var i;null==(i=this.sessionPersistence)||i.register(t)}unregister(t){var i;null==(i=this.persistence)||i.unregister(t)}unregister_for_session(t){var i;null==(i=this.sessionPersistence)||i.unregister(t)}Rs(t,i){this.register({[t]:i})}getFeatureFlag(t,i){return this.featureFlags.getFeatureFlag(t,i)}getFeatureFlagPayload(t){var i=this.featureFlags.getFeatureFlagPayload(t);try{return JSON.parse(i)}catch(t){return i}}isFeatureEnabled(t,i){return this.featureFlags.isFeatureEnabled(t,i)}reloadFeatureFlags(){this.featureFlags.reloadFeatureFlags()}updateEarlyAccessFeatureEnrollment(t,i){this.featureFlags.updateEarlyAccessFeatureEnrollment(t,i)}getEarlyAccessFeatures(t,i,e){return void 0===i&&(i=!1),this.featureFlags.getEarlyAccessFeatures(t,i,e)}on(t,i){return this.us.on(t,i)}onFeatureFlags(t){return this.featureFlags.onFeatureFlags(t)}onSurveysLoaded(t){return this.surveys.onSurveysLoaded(t)}onSessionId(t){var i,e;return null!==(i=null==(e=this.sessionManager)?void 0:e.onSessionId(t))&&void 0!==i?i:()=>{}}getSurveys(t,i){void 0===i&&(i=!1),this.surveys.getSurveys(t,i)}getActiveMatchingSurveys(t,i){void 0===i&&(i=!1),this.surveys.getActiveMatchingSurveys(t,i)}renderSurvey(t,i){this.surveys.renderSurvey(t,i)}canRenderSurvey(t){return this.surveys.canRenderSurvey(t)}canRenderSurveyAsync(t,i){return void 0===i&&(i=!1),this.surveys.canRenderSurveyAsync(t,i)}identify(t,i,e){if(!this.__loaded||!this.persistence)return j.uninitializedWarning(\"posthog.identify\");if(F(t)&&(t=t.toString(),j.warn(\"The first argument to posthog.identify was a number, but it should be a string. It has been converted to a string.\")),t){if([\"distinct_id\",\"distinctid\"].includes(t.toLowerCase()))j.critical('The string \"'+t+'\" was set in posthog.identify which indicates an error. This ID should be unique to the user and not a hardcoded string.');else if(this.Ps(\"posthog.identify\")){var r=this.get_distinct_id();if(this.register({$user_id:t}),!this.get_property(\"$device_id\")){var s=r;this.register_once({$had_persisted_distinct_id:!0,$device_id:s},\"\")}t!==r&&t!==this.get_property(ot)&&(this.unregister(ot),this.register({distinct_id:t}));var n=\"anonymous\"===(this.persistence.get_property(At)||\"anonymous\");t!==r&&n?(this.persistence.set_property(At,\"identified\"),this.setPersonPropertiesForFlags(B({},e||{},i||{}),!1),this.capture(\"$identify\",{distinct_id:t,$anon_distinct_id:r},{$set:i||{},$set_once:e||{}}),this.cs=js(t,i,e),this.featureFlags.setAnonymousDistinctId(r)):(i||e)&&this.setPersonProperties(i,e),t!==r&&(this.reloadFeatureFlags(),this.unregister(Ft))}}else j.error(\"Unique user id has not been set in posthog.identify\")}setPersonProperties(t,i){if((t||i)&&this.Ps(\"posthog.setPersonProperties\")){var e=js(this.get_distinct_id(),t,i);this.cs!==e?(this.setPersonPropertiesForFlags(B({},i||{},t||{})),this.capture(\"$set\",{$set:t||{},$set_once:i||{}}),this.cs=e):j.info(\"A duplicate setPersonProperties call was made with the same properties. It has been ignored.\")}}group(t,i,e){if(t&&i){if(this.Ps(\"posthog.group\")){var r=this.getGroups();r[t]!==i&&this.resetGroupPropertiesForFlags(t),this.register({$groups:B({},r,{[t]:i})}),e&&(this.capture(\"$groupidentify\",{$group_type:t,$group_key:i,$group_set:e}),this.setGroupPropertiesForFlags({[t]:e})),r[t]===i||e||this.reloadFeatureFlags()}}else j.error(\"posthog.group requires a group type and group key\")}resetGroups(){this.register({$groups:{}}),this.resetGroupPropertiesForFlags(),this.reloadFeatureFlags()}setPersonPropertiesForFlags(t,i){void 0===i&&(i=!0),this.featureFlags.setPersonPropertiesForFlags(t,i)}resetPersonPropertiesForFlags(){this.featureFlags.resetPersonPropertiesForFlags()}setGroupPropertiesForFlags(t,i){void 0===i&&(i=!0),this.Ps(\"posthog.setGroupPropertiesForFlags\")&&this.featureFlags.setGroupPropertiesForFlags(t,i)}resetGroupPropertiesForFlags(t){this.featureFlags.resetGroupPropertiesForFlags(t)}reset(t){var i,e,r,s;if(j.info(\"reset\"),!this.__loaded)return j.uninitializedWarning(\"posthog.reset\");var n=this.get_property(\"$device_id\");if(this.consent.reset(),null==(i=this.persistence)||i.clear(),null==(e=this.sessionPersistence)||e.clear(),this.surveys.reset(),null==(r=this.persistence)||r.set_property(At,\"anonymous\"),null==(s=this.sessionManager)||s.resetSessionId(),this.cs=null,this.config.__preview_experimental_cookieless_mode)this.register_once({distinct_id:Bt,$device_id:null},\"\");else{var o=this.config.get_device_id(Ni());this.register_once({distinct_id:o,$device_id:t?o:n},\"\")}this.register({$last_posthog_reset:(new Date).toISOString()},1)}get_distinct_id(){return this.get_property(\"distinct_id\")}getGroups(){return this.get_property(\"$groups\")||{}}get_session_id(){var t,i;return null!==(t=null==(i=this.sessionManager)?void 0:i.checkAndGetSessionAndWindowId(!0).sessionId)&&void 0!==t?t:\"\"}get_session_replay_url(t){if(!this.sessionManager)return\"\";var{sessionId:i,sessionStartTimestamp:e}=this.sessionManager.checkAndGetSessionAndWindowId(!0),r=this.requestRouter.endpointFor(\"ui\",\"/project/\"+this.config.token+\"/replay/\"+i);if(null!=t&&t.withTimestamp&&e){var s,n=null!==(s=t.timestampLookBack)&&void 0!==s?s:10;if(!e)return r;r+=\"?t=\"+Math.max(Math.floor(((new Date).getTime()-e)/1e3)-n,0)}return r}alias(t,i){return t===this.get_property(nt)?(j.critical(\"Attempting to create alias for existing People user - aborting.\"),-2):this.Ps(\"posthog.alias\")?(R(i)&&(i=this.get_distinct_id()),t!==i?(this.Rs(ot,t),this.capture(\"$create_alias\",{alias:t,distinct_id:i})):(j.warn(\"alias matches current distinct_id - skipping api call.\"),this.identify(t),-1)):void 0}set_config(t){var i,e,r,s,n=B({},this.config);I(t)&&(V(this.config,Vo(t)),null==(i=this.persistence)||i.update_config(this.config,n),this.sessionPersistence=\"sessionStorage\"===this.config.persistence||\"memory\"===this.config.persistence?this.persistence:new co(B({},this.config,{persistence:\"sessionStorage\"})),Wi.O()&&\"true\"===Wi.A(\"ph_debug\")&&(this.config.debug=!0),this.config.debug&&(c.DEBUG=!0,j.info(\"set_config\",JSON.stringify({config:t,oldConfig:n,newConfig:B({},this.config)},null,2))),null==(e=this.sessionRecording)||e.startIfEnabledOrStop(),null==(r=this.autocapture)||r.startIfEnabled(),null==(s=this.heatmaps)||s.startIfEnabled(),this.surveys.loadIfEnabled(),this.bs())}startSessionRecording(t){var i=!0===t,e={sampling:i||!(null==t||!t.sampling),linked_flag:i||!(null==t||!t.linked_flag),url_trigger:i||!(null==t||!t.url_trigger),event_trigger:i||!(null==t||!t.event_trigger)};if(Object.values(e).some(Boolean)){var r,s,n,o,a;if(null==(r=this.sessionManager)||r.checkAndGetSessionAndWindowId(),e.sampling)null==(s=this.sessionRecording)||s.overrideSampling();if(e.linked_flag)null==(n=this.sessionRecording)||n.overrideLinkedFlag();if(e.url_trigger)null==(o=this.sessionRecording)||o.overrideTrigger(\"url\");if(e.event_trigger)null==(a=this.sessionRecording)||a.overrideTrigger(\"event\")}this.set_config({disable_session_recording:!1})}stopSessionRecording(){this.set_config({disable_session_recording:!0})}sessionRecordingStarted(){var t;return!(null==(t=this.sessionRecording)||!t.started)}captureException(t,i){var e=new Error(\"PostHog syntheticException\");this.exceptions.sendExceptionEvent(B({},Ae((t=>t instanceof Error)(t)?{error:t,event:t.message}:{event:t},{syntheticException:e}),i))}loadToolbar(t){return this.toolbar.loadToolbar(t)}get_property(t){var i;return null==(i=this.persistence)?void 0:i.props[t]}getSessionProperty(t){var i;return null==(i=this.sessionPersistence)?void 0:i.props[t]}toString(){var t,i=null!==(t=this.config.name)&&void 0!==t?t:Wo;return i!==Wo&&(i=Wo+\".\"+i),i}_isIdentified(){var t,i;return\"identified\"===(null==(t=this.persistence)?void 0:t.get_property(At))||\"identified\"===(null==(i=this.sessionPersistence)?void 0:i.get_property(At))}Is(){var t,i;return!(\"never\"===this.config.person_profiles||\"identified_only\"===this.config.person_profiles&&!this._isIdentified()&&P(this.getGroups())&&(null==(t=this.persistence)||null==(t=t.props)||!t[ot])&&(null==(i=this.persistence)||null==(i=i.props)||!i[Ut]))}ks(){return!0===this.config.capture_pageleave||\"if_capture_pageview\"===this.config.capture_pageleave&&(!0===this.config.capture_pageview||\"history_change\"===this.config.capture_pageview)}createPersonProfile(){this.Is()||this.Ps(\"posthog.createPersonProfile\")&&this.setPersonProperties({},{})}Ps(t){return\"never\"===this.config.person_profiles?(j.error(t+' was called, but process_person is set to \"never\". This call will be ignored.'),!1):(this.Rs(Ut,!0),!0)}bs(){var t,i,e,r,s=this.consent.isOptedOut(),n=this.config.opt_out_persistence_by_default,o=this.config.disable_persistence||s&&!!n;(null==(t=this.persistence)?void 0:t.xe)!==o&&(null==(e=this.persistence)||e.set_disabled(o));(null==(i=this.sessionPersistence)?void 0:i.xe)!==o&&(null==(r=this.sessionPersistence)||r.set_disabled(o))}opt_in_capturing(t){var i;(this.consent.optInOut(!0),this.bs(),R(null==t?void 0:t.captureEventName)||null!=t&&t.captureEventName)&&this.capture(null!==(i=null==t?void 0:t.captureEventName)&&void 0!==i?i:\"$opt_in\",null==t?void 0:t.captureProperties,{send_instantly:!0});this.config.capture_pageview&&this.$s()}opt_out_capturing(){this.consent.optInOut(!1),this.bs()}has_opted_in_capturing(){return this.consent.isOptedIn()}has_opted_out_capturing(){return this.consent.isOptedOut()}clear_opt_in_out_capturing(){this.consent.reset(),this.bs()}_is_bot(){return n?Lo(n,this.config.custom_blocked_useragents):void 0}$s(){o&&(\"visible\"===o.visibilityState?this.hs||(this.hs=!0,this.capture(\"$pageview\",{title:o.title},{send_instantly:!0}),this.ds&&(o.removeEventListener(\"visibilitychange\",this.ds),this.ds=null)):this.ds||(this.ds=this.$s.bind(this),st(o,\"visibilitychange\",this.ds)))}debug(i){!1===i?(null==t||t.console.log(\"You've disabled debug mode.\"),localStorage&&localStorage.removeItem(\"ph_debug\"),this.set_config({debug:!1})):(null==t||t.console.log(\"You're now in debug mode. All calls to PostHog will be logged in your console.\\nYou can disable this with `posthog.debug(false)`.\"),localStorage&&localStorage.setItem(\"ph_debug\",\"true\"),this.set_config({debug:!0}))}Es(t){if(O(this.config.before_send))return t;var i=x(this.config.before_send)?this.config.before_send:[this.config.before_send],e=t;for(var r of i){if(e=r(e),O(e)){var s=\"Event '\"+t.event+\"' was rejected in beforeSend function\";return L(t.event)?j.warn(s+\". This can cause unexpected behavior.\"):j.info(s),null}e.properties&&!P(e.properties)||j.warn(\"Event '\"+t.event+\"' has no properties after beforeSend function, this is likely an error.\")}return e}getPageViewId(){var t;return null==(t=this.pageViewManager.ne)?void 0:t.pageViewId}captureTraceFeedback(t,i){this.capture(\"$ai_feedback\",{$ai_trace_id:String(t),$ai_feedback_text:i})}captureTraceMetric(t,i,e){this.capture(\"$ai_metric\",{$ai_trace_id:String(t),$ai_metric_name:i,$ai_metric_value:String(e)})}}!function(t,i){for(var e=0;e<i.length;e++)t.prototype[i[e]]=Q(t.prototype[i[e]])}(Yo,[\"identify\"]);var Xo,Qo=function(t){return t.Button=\"button\",t.Tab=\"tab\",t.Selector=\"selector\",t}({}),Zo=function(t){return t.TopLeft=\"top_left\",t.TopRight=\"top_right\",t.TopCenter=\"top_center\",t.MiddleLeft=\"middle_left\",t.MiddleRight=\"middle_right\",t.MiddleCenter=\"middle_center\",t.Left=\"left\",t.Center=\"center\",t.Right=\"right\",t.NextToTrigger=\"next_to_trigger\",t}({}),ta=function(t){return t.Popover=\"popover\",t.API=\"api\",t.Widget=\"widget\",t}({}),ia=function(t){return t.Open=\"open\",t.MultipleChoice=\"multiple_choice\",t.SingleChoice=\"single_choice\",t.Rating=\"rating\",t.Link=\"link\",t}({}),ea=function(t){return t.NextQuestion=\"next_question\",t.End=\"end\",t.ResponseBased=\"response_based\",t.SpecificQuestion=\"specific_question\",t}({}),ra=function(t){return t.Once=\"once\",t.Recurring=\"recurring\",t.Always=\"always\",t}({}),sa=(Xo=Bo[Wo]=new Yo,function(){function i(){i.done||(i.done=!0,Go=!1,J(Bo,(function(t){t._dom_loaded()})))}null!=o&&o.addEventListener?\"complete\"===o.readyState?i():st(o,\"DOMContentLoaded\",i,{capture:!1}):t&&j.error(\"Browser doesn't support `document.addEventListener` so PostHog couldn't be initialized\")}(),Xo);\n//# sourceMappingURL=module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/posthog-js@1.246.0_@rrweb+types@2.0.0-alpha.17/node_modules/posthog-js/dist/module.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/posthog-js@1.246.0_@rrweb+types@2.0.0-alpha.17/node_modules/posthog-js/react/dist/esm/index.js":
/*!*******************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/posthog-js@1.246.0_@rrweb+types@2.0.0-alpha.17/node_modules/posthog-js/react/dist/esm/index.js ***!
  \*******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PostHogContext: () => (/* binding */ PostHogContext),\n/* harmony export */   PostHogErrorBoundary: () => (/* binding */ PostHogErrorBoundary),\n/* harmony export */   PostHogFeature: () => (/* binding */ PostHogFeature),\n/* harmony export */   PostHogProvider: () => (/* binding */ PostHogProvider),\n/* harmony export */   useActiveFeatureFlags: () => (/* binding */ useActiveFeatureFlags),\n/* harmony export */   useFeatureFlagEnabled: () => (/* binding */ useFeatureFlagEnabled),\n/* harmony export */   useFeatureFlagPayload: () => (/* binding */ useFeatureFlagPayload),\n/* harmony export */   useFeatureFlagVariantKey: () => (/* binding */ useFeatureFlagVariantKey),\n/* harmony export */   usePostHog: () => (/* binding */ usePostHog)\n/* harmony export */ });\n/* harmony import */ var posthog_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! posthog-js */ \"(ssr)/../../node_modules/.pnpm/posthog-js@1.246.0_@rrweb+types@2.0.0-alpha.17/node_modules/posthog-js/dist/module.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\n\nvar PostHogContext = (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({ client: posthog_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"] });\n\nfunction isDeepEqual(obj1, obj2, visited) {\r\n    if (visited === void 0) { visited = new WeakMap(); }\r\n    if (obj1 === obj2) {\r\n        return true;\r\n    }\r\n    if (typeof obj1 !== 'object' || obj1 === null || typeof obj2 !== 'object' || obj2 === null) {\r\n        return false;\r\n    }\r\n    if (visited.has(obj1) && visited.get(obj1) === obj2) {\r\n        return true;\r\n    }\r\n    visited.set(obj1, obj2);\r\n    var keys1 = Object.keys(obj1);\r\n    var keys2 = Object.keys(obj2);\r\n    if (keys1.length !== keys2.length) {\r\n        return false;\r\n    }\r\n    for (var _i = 0, keys1_1 = keys1; _i < keys1_1.length; _i++) {\r\n        var key = keys1_1[_i];\r\n        if (!keys2.includes(key)) {\r\n            return false;\r\n        }\r\n        if (!isDeepEqual(obj1[key], obj2[key], visited)) {\r\n            return false;\r\n        }\r\n    }\r\n    return true;\r\n}\n\nfunction PostHogProvider(_a) {\r\n    var children = _a.children, client = _a.client, apiKey = _a.apiKey, options = _a.options;\r\n    var previousInitializationRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\r\n    var posthog = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(function () {\r\n        if (client) {\r\n            if (apiKey) {\r\n                console.warn('[PostHog.js] You have provided both `client` and `apiKey` to `PostHogProvider`. `apiKey` will be ignored in favour of `client`.');\r\n            }\r\n            if (options) {\r\n                console.warn('[PostHog.js] You have provided both `client` and `options` to `PostHogProvider`. `options` will be ignored in favour of `client`.');\r\n            }\r\n            return client;\r\n        }\r\n        if (apiKey) {\r\n            return posthog_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"];\r\n        }\r\n        console.warn('[PostHog.js] No `apiKey` or `client` were provided to `PostHogProvider`. Using default global `window.posthog` instance. You must initialize it manually. This is not recommended behavior.');\r\n        return posthog_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"];\r\n    }, [client, apiKey, JSON.stringify(options)]);\r\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\r\n        if (client) {\r\n            return;\r\n        }\r\n        var previousInitialization = previousInitializationRef.current;\r\n        if (!previousInitialization) {\r\n            if (posthog_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].__loaded) {\r\n                console.warn('[PostHog.js] `posthog` was already loaded elsewhere. This may cause issues.');\r\n            }\r\n            posthog_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].init(apiKey, options);\r\n            previousInitializationRef.current = {\r\n                apiKey: apiKey,\r\n                options: options !== null && options !== void 0 ? options : {},\r\n            };\r\n        }\r\n        else {\r\n            if (apiKey !== previousInitialization.apiKey) {\r\n                console.warn(\"[PostHog.js] You have provided a different `apiKey` to `PostHogProvider` than the one that was already initialized. This is not supported by our provider and we'll keep using the previous key. If you need to toggle between API Keys you need to control the `client` yourself and pass it in as a prop rather than an `apiKey` prop.\");\r\n            }\r\n            if (options && !isDeepEqual(options, previousInitialization.options)) {\r\n                posthog_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].set_config(options);\r\n            }\r\n            previousInitializationRef.current = {\r\n                apiKey: apiKey,\r\n                options: options !== null && options !== void 0 ? options : {},\r\n            };\r\n        }\r\n    }, [client, apiKey, JSON.stringify(options)]);\r\n    return react__WEBPACK_IMPORTED_MODULE_1___default().createElement(PostHogContext.Provider, { value: { client: posthog } }, children);\r\n}\n\nvar usePostHog = function () {\r\n    var client = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(PostHogContext).client;\r\n    return client;\r\n};\n\nfunction useFeatureFlagEnabled(flag) {\r\n    var client = usePostHog();\r\n    var _a = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(function () { return client.isFeatureEnabled(flag); }), featureEnabled = _a[0], setFeatureEnabled = _a[1];\r\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\r\n        return client.onFeatureFlags(function () {\r\n            setFeatureEnabled(client.isFeatureEnabled(flag));\r\n        });\r\n    }, [client, flag]);\r\n    return featureEnabled;\r\n}\n\nfunction useFeatureFlagPayload(flag) {\r\n    var client = usePostHog();\r\n    var _a = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(function () { return client.getFeatureFlagPayload(flag); }), featureFlagPayload = _a[0], setFeatureFlagPayload = _a[1];\r\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\r\n        return client.onFeatureFlags(function () {\r\n            setFeatureFlagPayload(client.getFeatureFlagPayload(flag));\r\n        });\r\n    }, [client, flag]);\r\n    return featureFlagPayload;\r\n}\n\nfunction useActiveFeatureFlags() {\r\n    var client = usePostHog();\r\n    var _a = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(function () { return client.featureFlags.getFlags(); }), featureFlags = _a[0], setFeatureFlags = _a[1];\r\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\r\n        return client.onFeatureFlags(function (flags) {\r\n            setFeatureFlags(flags);\r\n        });\r\n    }, [client]);\r\n    return featureFlags;\r\n}\n\nfunction useFeatureFlagVariantKey(flag) {\r\n    var client = usePostHog();\r\n    var _a = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(function () {\r\n        return client.getFeatureFlag(flag);\r\n    }), featureFlagVariantKey = _a[0], setFeatureFlagVariantKey = _a[1];\r\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\r\n        return client.onFeatureFlags(function () {\r\n            setFeatureFlagVariantKey(client.getFeatureFlag(flag));\r\n        });\r\n    }, [client, flag]);\r\n    return featureFlagVariantKey;\r\n}\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nfunction __extends(d, b) {\r\n    if (typeof b !== \"function\" && b !== null)\r\n        throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nvar __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n    return __assign.apply(this, arguments);\r\n};\r\n\r\nfunction __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\n\nvar isFunction = function (f) {\r\n    return typeof f === 'function';\r\n};\r\nvar isUndefined = function (x) {\r\n    return x === void 0;\r\n};\r\nvar isNull = function (x) {\r\n    return x === null;\r\n};\n\nfunction PostHogFeature(_a) {\r\n    var flag = _a.flag, match = _a.match, children = _a.children, fallback = _a.fallback, visibilityObserverOptions = _a.visibilityObserverOptions, trackInteraction = _a.trackInteraction, trackView = _a.trackView, props = __rest(_a, [\"flag\", \"match\", \"children\", \"fallback\", \"visibilityObserverOptions\", \"trackInteraction\", \"trackView\"]);\r\n    var payload = useFeatureFlagPayload(flag);\r\n    var variant = useFeatureFlagVariantKey(flag);\r\n    var shouldTrackInteraction = trackInteraction !== null && trackInteraction !== void 0 ? trackInteraction : true;\r\n    var shouldTrackView = trackView !== null && trackView !== void 0 ? trackView : true;\r\n    if (isUndefined(match) || variant === match) {\r\n        var childNode = isFunction(children) ? children(payload) : children;\r\n        return (react__WEBPACK_IMPORTED_MODULE_1___default().createElement(VisibilityAndClickTrackers, __assign({ flag: flag, options: visibilityObserverOptions, trackInteraction: shouldTrackInteraction, trackView: shouldTrackView }, props), childNode));\r\n    }\r\n    return react__WEBPACK_IMPORTED_MODULE_1___default().createElement((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), null, fallback);\r\n}\r\nfunction captureFeatureInteraction(_a) {\r\n    var _b;\r\n    var flag = _a.flag, posthog = _a.posthog, flagVariant = _a.flagVariant;\r\n    var properties = {\r\n        feature_flag: flag,\r\n        $set: (_b = {}, _b[\"$feature_interaction/\".concat(flag)] = flagVariant !== null && flagVariant !== void 0 ? flagVariant : true, _b),\r\n    };\r\n    if (typeof flagVariant === 'string') {\r\n        properties.feature_flag_variant = flagVariant;\r\n    }\r\n    posthog.capture('$feature_interaction', properties);\r\n}\r\nfunction captureFeatureView(_a) {\r\n    var _b;\r\n    var flag = _a.flag, posthog = _a.posthog, flagVariant = _a.flagVariant;\r\n    var properties = {\r\n        feature_flag: flag,\r\n        $set: (_b = {}, _b[\"$feature_view/\".concat(flag)] = flagVariant !== null && flagVariant !== void 0 ? flagVariant : true, _b),\r\n    };\r\n    if (typeof flagVariant === 'string') {\r\n        properties.feature_flag_variant = flagVariant;\r\n    }\r\n    posthog.capture('$feature_view', properties);\r\n}\r\nfunction VisibilityAndClickTracker(_a) {\r\n    var flag = _a.flag, children = _a.children, onIntersect = _a.onIntersect, onClick = _a.onClick, trackView = _a.trackView, options = _a.options, props = __rest(_a, [\"flag\", \"children\", \"onIntersect\", \"onClick\", \"trackView\", \"options\"]);\r\n    var ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\r\n    var posthog = usePostHog();\r\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\r\n        if (isNull(ref.current) || !trackView)\r\n            return;\r\n        var observer = new IntersectionObserver(function (_a) {\r\n            var entry = _a[0];\r\n            return onIntersect(entry);\r\n        }, __assign({ threshold: 0.1 }, options));\r\n        observer.observe(ref.current);\r\n        return function () { return observer.disconnect(); };\r\n    }, [flag, options, posthog, ref, trackView, onIntersect]);\r\n    return (react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"div\", __assign({ ref: ref }, props, { onClick: onClick }), children));\r\n}\r\nfunction VisibilityAndClickTrackers(_a) {\r\n    var flag = _a.flag, children = _a.children, trackInteraction = _a.trackInteraction, trackView = _a.trackView, options = _a.options, props = __rest(_a, [\"flag\", \"children\", \"trackInteraction\", \"trackView\", \"options\"]);\r\n    var clickTrackedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\r\n    var visibilityTrackedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\r\n    var posthog = usePostHog();\r\n    var variant = useFeatureFlagVariantKey(flag);\r\n    var cachedOnClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function () {\r\n        if (!clickTrackedRef.current && trackInteraction) {\r\n            captureFeatureInteraction({ flag: flag, posthog: posthog, flagVariant: variant });\r\n            clickTrackedRef.current = true;\r\n        }\r\n    }, [flag, posthog, trackInteraction, variant]);\r\n    var onIntersect = function (entry) {\r\n        if (!visibilityTrackedRef.current && entry.isIntersecting) {\r\n            captureFeatureView({ flag: flag, posthog: posthog, flagVariant: variant });\r\n            visibilityTrackedRef.current = true;\r\n        }\r\n    };\r\n    var trackedChildren = react__WEBPACK_IMPORTED_MODULE_1__.Children.map(children, function (child) {\r\n        return (react__WEBPACK_IMPORTED_MODULE_1___default().createElement(VisibilityAndClickTracker, __assign({ flag: flag, onClick: cachedOnClick, onIntersect: onIntersect, trackView: trackView, options: options }, props), child));\r\n    });\r\n    return react__WEBPACK_IMPORTED_MODULE_1___default().createElement((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), null, trackedChildren);\r\n}\n\nvar INITIAL_STATE = {\r\n    componentStack: null,\r\n    error: null,\r\n};\r\nvar __POSTHOG_ERROR_MESSAGES = {\r\n    INVALID_FALLBACK: '[PostHog.js][PostHogErrorBoundary] Invalid fallback prop, provide a valid React element or a function that returns a valid React element.',\r\n};\r\nvar PostHogErrorBoundary = (function (_super) {\r\n    __extends(PostHogErrorBoundary, _super);\r\n    function PostHogErrorBoundary(props) {\r\n        var _this = _super.call(this, props) || this;\r\n        _this.state = INITIAL_STATE;\r\n        return _this;\r\n    }\r\n    PostHogErrorBoundary.prototype.componentDidCatch = function (error, errorInfo) {\r\n        var componentStack = errorInfo.componentStack;\r\n        var additionalProperties = this.props.additionalProperties;\r\n        this.setState({\r\n            error: error,\r\n            componentStack: componentStack,\r\n        });\r\n        var currentProperties;\r\n        if (isFunction(additionalProperties)) {\r\n            currentProperties = additionalProperties(error);\r\n        }\r\n        else if (typeof additionalProperties === 'object') {\r\n            currentProperties = additionalProperties;\r\n        }\r\n        var client = this.context.client;\r\n        client.captureException(error, currentProperties);\r\n    };\r\n    PostHogErrorBoundary.prototype.render = function () {\r\n        var _a = this.props, children = _a.children, fallback = _a.fallback;\r\n        var state = this.state;\r\n        if (state.componentStack == null) {\r\n            return isFunction(children) ? children() : children;\r\n        }\r\n        var element = isFunction(fallback)\r\n            ? react__WEBPACK_IMPORTED_MODULE_1___default().createElement(fallback, {\r\n                error: state.error,\r\n                componentStack: state.componentStack,\r\n            })\r\n            : fallback;\r\n        if (react__WEBPACK_IMPORTED_MODULE_1___default().isValidElement(element)) {\r\n            return element;\r\n        }\r\n        console.warn(__POSTHOG_ERROR_MESSAGES.INVALID_FALLBACK);\r\n        return react__WEBPACK_IMPORTED_MODULE_1___default().createElement((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), null);\r\n    };\r\n    PostHogErrorBoundary.contextType = PostHogContext;\r\n    return PostHogErrorBoundary;\r\n}((react__WEBPACK_IMPORTED_MODULE_1___default().Component)));\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL3Bvc3Rob2ctanNAMS4yNDYuMF9AcnJ3ZWIrdHlwZXNAMi4wLjAtYWxwaGEuMTcvbm9kZV9tb2R1bGVzL3Bvc3Rob2ctanMvcmVhY3QvZGlzdC9lc20vaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBQW1DO0FBQ21GOztBQUV0SCxxQkFBcUIsb0RBQWEsR0FBRyxRQUFRLGtEQUFTLEVBQUU7O0FBRXhEO0FBQ0EsOEJBQThCO0FBQzlCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNDQUFzQyxxQkFBcUI7QUFDM0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLG9DQUFvQyw2Q0FBTTtBQUMxQyxrQkFBa0IsOENBQU87QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsa0RBQVM7QUFDNUI7QUFDQTtBQUNBLGVBQWUsa0RBQVM7QUFDeEIsS0FBSztBQUNMLElBQUksZ0RBQVM7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLGtEQUFTO0FBQ3pCO0FBQ0E7QUFDQSxZQUFZLGtEQUFTO0FBQ3JCO0FBQ0E7QUFDQSw4RUFBOEU7QUFDOUU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0Isa0RBQVM7QUFDekI7QUFDQTtBQUNBO0FBQ0EsOEVBQThFO0FBQzlFO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsV0FBVywwREFBbUIsNEJBQTRCLFNBQVMsbUJBQW1CO0FBQ3RGOztBQUVBO0FBQ0EsaUJBQWlCLGlEQUFVO0FBQzNCO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLGFBQWEsK0NBQVEsZUFBZSx1Q0FBdUM7QUFDM0UsSUFBSSxnREFBUztBQUNiO0FBQ0E7QUFDQSxTQUFTO0FBQ1QsS0FBSztBQUNMO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLGFBQWEsK0NBQVEsZUFBZSw0Q0FBNEM7QUFDaEYsSUFBSSxnREFBUztBQUNiO0FBQ0E7QUFDQSxTQUFTO0FBQ1QsS0FBSztBQUNMO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLGFBQWEsK0NBQVEsZUFBZSx3Q0FBd0M7QUFDNUUsSUFBSSxnREFBUztBQUNiO0FBQ0E7QUFDQSxTQUFTO0FBQ1QsS0FBSztBQUNMO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLGFBQWEsK0NBQVE7QUFDckI7QUFDQSxLQUFLO0FBQ0wsSUFBSSxnREFBUztBQUNiO0FBQ0E7QUFDQSxTQUFTO0FBQ1QsS0FBSztBQUNMO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxnQkFBZ0Isc0NBQXNDLGtCQUFrQjtBQUNuRiwwQkFBMEI7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0I7QUFDcEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlEQUFpRCxPQUFPO0FBQ3hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkRBQTZELGNBQWM7QUFDM0U7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQiwwREFBbUIsd0NBQXdDLHNIQUFzSDtBQUNqTTtBQUNBLFdBQVcsMERBQW1CLENBQUMsdURBQWM7QUFDN0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCO0FBQ3RCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0I7QUFDdEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWMsNkNBQU07QUFDcEI7QUFDQSxJQUFJLGdEQUFTO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVMsYUFBYSxnQkFBZ0I7QUFDdEM7QUFDQSw2QkFBNkI7QUFDN0IsS0FBSztBQUNMLFlBQVksMERBQW1CLG1CQUFtQixVQUFVLFdBQVcsa0JBQWtCO0FBQ3pGO0FBQ0E7QUFDQTtBQUNBLDBCQUEwQiw2Q0FBTTtBQUNoQywrQkFBK0IsNkNBQU07QUFDckM7QUFDQTtBQUNBLHdCQUF3QixrREFBVztBQUNuQztBQUNBLHdDQUF3QyxvREFBb0Q7QUFDNUY7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsaUNBQWlDLG9EQUFvRDtBQUNyRjtBQUNBO0FBQ0E7QUFDQSwwQkFBMEIsMkNBQVE7QUFDbEMsZ0JBQWdCLDBEQUFtQix1Q0FBdUMsc0dBQXNHO0FBQ2hMLEtBQUs7QUFDTCxXQUFXLDBEQUFtQixDQUFDLHVEQUFjO0FBQzdDOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWMsMERBQW1CO0FBQ2pDO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQSxZQUFZLDJEQUFvQjtBQUNoQztBQUNBO0FBQ0E7QUFDQSxlQUFlLDBEQUFtQixDQUFDLHVEQUFjO0FBQ2pEO0FBQ0E7QUFDQTtBQUNBLENBQUMsQ0FBQyx3REFBZTs7QUFFMksiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYW53YXJcXERvY3VtZW50c1xcQ3ViZW50d2ViXFxjdWJlbnRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXHBvc3Rob2ctanNAMS4yNDYuMF9AcnJ3ZWIrdHlwZXNAMi4wLjAtYWxwaGEuMTdcXG5vZGVfbW9kdWxlc1xccG9zdGhvZy1qc1xccmVhY3RcXGRpc3RcXGVzbVxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHBvc3Rob2dKcyBmcm9tICdwb3N0aG9nLWpzJztcbmltcG9ydCBSZWFjdCwgeyBjcmVhdGVDb250ZXh0LCB1c2VSZWYsIHVzZU1lbW8sIHVzZUVmZmVjdCwgdXNlQ29udGV4dCwgdXNlU3RhdGUsIHVzZUNhbGxiYWNrLCBDaGlsZHJlbiB9IGZyb20gJ3JlYWN0JztcblxudmFyIFBvc3RIb2dDb250ZXh0ID0gY3JlYXRlQ29udGV4dCh7IGNsaWVudDogcG9zdGhvZ0pzIH0pO1xuXG5mdW5jdGlvbiBpc0RlZXBFcXVhbChvYmoxLCBvYmoyLCB2aXNpdGVkKSB7XHJcbiAgICBpZiAodmlzaXRlZCA9PT0gdm9pZCAwKSB7IHZpc2l0ZWQgPSBuZXcgV2Vha01hcCgpOyB9XHJcbiAgICBpZiAob2JqMSA9PT0gb2JqMikge1xyXG4gICAgICAgIHJldHVybiB0cnVlO1xyXG4gICAgfVxyXG4gICAgaWYgKHR5cGVvZiBvYmoxICE9PSAnb2JqZWN0JyB8fCBvYmoxID09PSBudWxsIHx8IHR5cGVvZiBvYmoyICE9PSAnb2JqZWN0JyB8fCBvYmoyID09PSBudWxsKSB7XHJcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xyXG4gICAgfVxyXG4gICAgaWYgKHZpc2l0ZWQuaGFzKG9iajEpICYmIHZpc2l0ZWQuZ2V0KG9iajEpID09PSBvYmoyKSB7XHJcbiAgICAgICAgcmV0dXJuIHRydWU7XHJcbiAgICB9XHJcbiAgICB2aXNpdGVkLnNldChvYmoxLCBvYmoyKTtcclxuICAgIHZhciBrZXlzMSA9IE9iamVjdC5rZXlzKG9iajEpO1xyXG4gICAgdmFyIGtleXMyID0gT2JqZWN0LmtleXMob2JqMik7XHJcbiAgICBpZiAoa2V5czEubGVuZ3RoICE9PSBrZXlzMi5sZW5ndGgpIHtcclxuICAgICAgICByZXR1cm4gZmFsc2U7XHJcbiAgICB9XHJcbiAgICBmb3IgKHZhciBfaSA9IDAsIGtleXMxXzEgPSBrZXlzMTsgX2kgPCBrZXlzMV8xLmxlbmd0aDsgX2krKykge1xyXG4gICAgICAgIHZhciBrZXkgPSBrZXlzMV8xW19pXTtcclxuICAgICAgICBpZiAoIWtleXMyLmluY2x1ZGVzKGtleSkpIHtcclxuICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xyXG4gICAgICAgIH1cclxuICAgICAgICBpZiAoIWlzRGVlcEVxdWFsKG9iajFba2V5XSwgb2JqMltrZXldLCB2aXNpdGVkKSkge1xyXG4gICAgICAgICAgICByZXR1cm4gZmFsc2U7XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG4gICAgcmV0dXJuIHRydWU7XHJcbn1cblxuZnVuY3Rpb24gUG9zdEhvZ1Byb3ZpZGVyKF9hKSB7XHJcbiAgICB2YXIgY2hpbGRyZW4gPSBfYS5jaGlsZHJlbiwgY2xpZW50ID0gX2EuY2xpZW50LCBhcGlLZXkgPSBfYS5hcGlLZXksIG9wdGlvbnMgPSBfYS5vcHRpb25zO1xyXG4gICAgdmFyIHByZXZpb3VzSW5pdGlhbGl6YXRpb25SZWYgPSB1c2VSZWYobnVsbCk7XHJcbiAgICB2YXIgcG9zdGhvZyA9IHVzZU1lbW8oZnVuY3Rpb24gKCkge1xyXG4gICAgICAgIGlmIChjbGllbnQpIHtcclxuICAgICAgICAgICAgaWYgKGFwaUtleSkge1xyXG4gICAgICAgICAgICAgICAgY29uc29sZS53YXJuKCdbUG9zdEhvZy5qc10gWW91IGhhdmUgcHJvdmlkZWQgYm90aCBgY2xpZW50YCBhbmQgYGFwaUtleWAgdG8gYFBvc3RIb2dQcm92aWRlcmAuIGBhcGlLZXlgIHdpbGwgYmUgaWdub3JlZCBpbiBmYXZvdXIgb2YgYGNsaWVudGAuJyk7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgaWYgKG9wdGlvbnMpIHtcclxuICAgICAgICAgICAgICAgIGNvbnNvbGUud2FybignW1Bvc3RIb2cuanNdIFlvdSBoYXZlIHByb3ZpZGVkIGJvdGggYGNsaWVudGAgYW5kIGBvcHRpb25zYCB0byBgUG9zdEhvZ1Byb3ZpZGVyYC4gYG9wdGlvbnNgIHdpbGwgYmUgaWdub3JlZCBpbiBmYXZvdXIgb2YgYGNsaWVudGAuJyk7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgcmV0dXJuIGNsaWVudDtcclxuICAgICAgICB9XHJcbiAgICAgICAgaWYgKGFwaUtleSkge1xyXG4gICAgICAgICAgICByZXR1cm4gcG9zdGhvZ0pzO1xyXG4gICAgICAgIH1cclxuICAgICAgICBjb25zb2xlLndhcm4oJ1tQb3N0SG9nLmpzXSBObyBgYXBpS2V5YCBvciBgY2xpZW50YCB3ZXJlIHByb3ZpZGVkIHRvIGBQb3N0SG9nUHJvdmlkZXJgLiBVc2luZyBkZWZhdWx0IGdsb2JhbCBgd2luZG93LnBvc3Rob2dgIGluc3RhbmNlLiBZb3UgbXVzdCBpbml0aWFsaXplIGl0IG1hbnVhbGx5LiBUaGlzIGlzIG5vdCByZWNvbW1lbmRlZCBiZWhhdmlvci4nKTtcclxuICAgICAgICByZXR1cm4gcG9zdGhvZ0pzO1xyXG4gICAgfSwgW2NsaWVudCwgYXBpS2V5LCBKU09OLnN0cmluZ2lmeShvcHRpb25zKV0pO1xyXG4gICAgdXNlRWZmZWN0KGZ1bmN0aW9uICgpIHtcclxuICAgICAgICBpZiAoY2xpZW50KSB7XHJcbiAgICAgICAgICAgIHJldHVybjtcclxuICAgICAgICB9XHJcbiAgICAgICAgdmFyIHByZXZpb3VzSW5pdGlhbGl6YXRpb24gPSBwcmV2aW91c0luaXRpYWxpemF0aW9uUmVmLmN1cnJlbnQ7XHJcbiAgICAgICAgaWYgKCFwcmV2aW91c0luaXRpYWxpemF0aW9uKSB7XHJcbiAgICAgICAgICAgIGlmIChwb3N0aG9nSnMuX19sb2FkZWQpIHtcclxuICAgICAgICAgICAgICAgIGNvbnNvbGUud2FybignW1Bvc3RIb2cuanNdIGBwb3N0aG9nYCB3YXMgYWxyZWFkeSBsb2FkZWQgZWxzZXdoZXJlLiBUaGlzIG1heSBjYXVzZSBpc3N1ZXMuJyk7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgcG9zdGhvZ0pzLmluaXQoYXBpS2V5LCBvcHRpb25zKTtcclxuICAgICAgICAgICAgcHJldmlvdXNJbml0aWFsaXphdGlvblJlZi5jdXJyZW50ID0ge1xyXG4gICAgICAgICAgICAgICAgYXBpS2V5OiBhcGlLZXksXHJcbiAgICAgICAgICAgICAgICBvcHRpb25zOiBvcHRpb25zICE9PSBudWxsICYmIG9wdGlvbnMgIT09IHZvaWQgMCA/IG9wdGlvbnMgOiB7fSxcclxuICAgICAgICAgICAgfTtcclxuICAgICAgICB9XHJcbiAgICAgICAgZWxzZSB7XHJcbiAgICAgICAgICAgIGlmIChhcGlLZXkgIT09IHByZXZpb3VzSW5pdGlhbGl6YXRpb24uYXBpS2V5KSB7XHJcbiAgICAgICAgICAgICAgICBjb25zb2xlLndhcm4oXCJbUG9zdEhvZy5qc10gWW91IGhhdmUgcHJvdmlkZWQgYSBkaWZmZXJlbnQgYGFwaUtleWAgdG8gYFBvc3RIb2dQcm92aWRlcmAgdGhhbiB0aGUgb25lIHRoYXQgd2FzIGFscmVhZHkgaW5pdGlhbGl6ZWQuIFRoaXMgaXMgbm90IHN1cHBvcnRlZCBieSBvdXIgcHJvdmlkZXIgYW5kIHdlJ2xsIGtlZXAgdXNpbmcgdGhlIHByZXZpb3VzIGtleS4gSWYgeW91IG5lZWQgdG8gdG9nZ2xlIGJldHdlZW4gQVBJIEtleXMgeW91IG5lZWQgdG8gY29udHJvbCB0aGUgYGNsaWVudGAgeW91cnNlbGYgYW5kIHBhc3MgaXQgaW4gYXMgYSBwcm9wIHJhdGhlciB0aGFuIGFuIGBhcGlLZXlgIHByb3AuXCIpO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIGlmIChvcHRpb25zICYmICFpc0RlZXBFcXVhbChvcHRpb25zLCBwcmV2aW91c0luaXRpYWxpemF0aW9uLm9wdGlvbnMpKSB7XHJcbiAgICAgICAgICAgICAgICBwb3N0aG9nSnMuc2V0X2NvbmZpZyhvcHRpb25zKTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICBwcmV2aW91c0luaXRpYWxpemF0aW9uUmVmLmN1cnJlbnQgPSB7XHJcbiAgICAgICAgICAgICAgICBhcGlLZXk6IGFwaUtleSxcclxuICAgICAgICAgICAgICAgIG9wdGlvbnM6IG9wdGlvbnMgIT09IG51bGwgJiYgb3B0aW9ucyAhPT0gdm9pZCAwID8gb3B0aW9ucyA6IHt9LFxyXG4gICAgICAgICAgICB9O1xyXG4gICAgICAgIH1cclxuICAgIH0sIFtjbGllbnQsIGFwaUtleSwgSlNPTi5zdHJpbmdpZnkob3B0aW9ucyldKTtcclxuICAgIHJldHVybiBSZWFjdC5jcmVhdGVFbGVtZW50KFBvc3RIb2dDb250ZXh0LlByb3ZpZGVyLCB7IHZhbHVlOiB7IGNsaWVudDogcG9zdGhvZyB9IH0sIGNoaWxkcmVuKTtcclxufVxuXG52YXIgdXNlUG9zdEhvZyA9IGZ1bmN0aW9uICgpIHtcclxuICAgIHZhciBjbGllbnQgPSB1c2VDb250ZXh0KFBvc3RIb2dDb250ZXh0KS5jbGllbnQ7XHJcbiAgICByZXR1cm4gY2xpZW50O1xyXG59O1xuXG5mdW5jdGlvbiB1c2VGZWF0dXJlRmxhZ0VuYWJsZWQoZmxhZykge1xyXG4gICAgdmFyIGNsaWVudCA9IHVzZVBvc3RIb2coKTtcclxuICAgIHZhciBfYSA9IHVzZVN0YXRlKGZ1bmN0aW9uICgpIHsgcmV0dXJuIGNsaWVudC5pc0ZlYXR1cmVFbmFibGVkKGZsYWcpOyB9KSwgZmVhdHVyZUVuYWJsZWQgPSBfYVswXSwgc2V0RmVhdHVyZUVuYWJsZWQgPSBfYVsxXTtcclxuICAgIHVzZUVmZmVjdChmdW5jdGlvbiAoKSB7XHJcbiAgICAgICAgcmV0dXJuIGNsaWVudC5vbkZlYXR1cmVGbGFncyhmdW5jdGlvbiAoKSB7XHJcbiAgICAgICAgICAgIHNldEZlYXR1cmVFbmFibGVkKGNsaWVudC5pc0ZlYXR1cmVFbmFibGVkKGZsYWcpKTtcclxuICAgICAgICB9KTtcclxuICAgIH0sIFtjbGllbnQsIGZsYWddKTtcclxuICAgIHJldHVybiBmZWF0dXJlRW5hYmxlZDtcclxufVxuXG5mdW5jdGlvbiB1c2VGZWF0dXJlRmxhZ1BheWxvYWQoZmxhZykge1xyXG4gICAgdmFyIGNsaWVudCA9IHVzZVBvc3RIb2coKTtcclxuICAgIHZhciBfYSA9IHVzZVN0YXRlKGZ1bmN0aW9uICgpIHsgcmV0dXJuIGNsaWVudC5nZXRGZWF0dXJlRmxhZ1BheWxvYWQoZmxhZyk7IH0pLCBmZWF0dXJlRmxhZ1BheWxvYWQgPSBfYVswXSwgc2V0RmVhdHVyZUZsYWdQYXlsb2FkID0gX2FbMV07XHJcbiAgICB1c2VFZmZlY3QoZnVuY3Rpb24gKCkge1xyXG4gICAgICAgIHJldHVybiBjbGllbnQub25GZWF0dXJlRmxhZ3MoZnVuY3Rpb24gKCkge1xyXG4gICAgICAgICAgICBzZXRGZWF0dXJlRmxhZ1BheWxvYWQoY2xpZW50LmdldEZlYXR1cmVGbGFnUGF5bG9hZChmbGFnKSk7XHJcbiAgICAgICAgfSk7XHJcbiAgICB9LCBbY2xpZW50LCBmbGFnXSk7XHJcbiAgICByZXR1cm4gZmVhdHVyZUZsYWdQYXlsb2FkO1xyXG59XG5cbmZ1bmN0aW9uIHVzZUFjdGl2ZUZlYXR1cmVGbGFncygpIHtcclxuICAgIHZhciBjbGllbnQgPSB1c2VQb3N0SG9nKCk7XHJcbiAgICB2YXIgX2EgPSB1c2VTdGF0ZShmdW5jdGlvbiAoKSB7IHJldHVybiBjbGllbnQuZmVhdHVyZUZsYWdzLmdldEZsYWdzKCk7IH0pLCBmZWF0dXJlRmxhZ3MgPSBfYVswXSwgc2V0RmVhdHVyZUZsYWdzID0gX2FbMV07XHJcbiAgICB1c2VFZmZlY3QoZnVuY3Rpb24gKCkge1xyXG4gICAgICAgIHJldHVybiBjbGllbnQub25GZWF0dXJlRmxhZ3MoZnVuY3Rpb24gKGZsYWdzKSB7XHJcbiAgICAgICAgICAgIHNldEZlYXR1cmVGbGFncyhmbGFncyk7XHJcbiAgICAgICAgfSk7XHJcbiAgICB9LCBbY2xpZW50XSk7XHJcbiAgICByZXR1cm4gZmVhdHVyZUZsYWdzO1xyXG59XG5cbmZ1bmN0aW9uIHVzZUZlYXR1cmVGbGFnVmFyaWFudEtleShmbGFnKSB7XHJcbiAgICB2YXIgY2xpZW50ID0gdXNlUG9zdEhvZygpO1xyXG4gICAgdmFyIF9hID0gdXNlU3RhdGUoZnVuY3Rpb24gKCkge1xyXG4gICAgICAgIHJldHVybiBjbGllbnQuZ2V0RmVhdHVyZUZsYWcoZmxhZyk7XHJcbiAgICB9KSwgZmVhdHVyZUZsYWdWYXJpYW50S2V5ID0gX2FbMF0sIHNldEZlYXR1cmVGbGFnVmFyaWFudEtleSA9IF9hWzFdO1xyXG4gICAgdXNlRWZmZWN0KGZ1bmN0aW9uICgpIHtcclxuICAgICAgICByZXR1cm4gY2xpZW50Lm9uRmVhdHVyZUZsYWdzKGZ1bmN0aW9uICgpIHtcclxuICAgICAgICAgICAgc2V0RmVhdHVyZUZsYWdWYXJpYW50S2V5KGNsaWVudC5nZXRGZWF0dXJlRmxhZyhmbGFnKSk7XHJcbiAgICAgICAgfSk7XHJcbiAgICB9LCBbY2xpZW50LCBmbGFnXSk7XHJcbiAgICByZXR1cm4gZmVhdHVyZUZsYWdWYXJpYW50S2V5O1xyXG59XG5cbi8qKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKipcclxuQ29weXJpZ2h0IChjKSBNaWNyb3NvZnQgQ29ycG9yYXRpb24uXHJcblxyXG5QZXJtaXNzaW9uIHRvIHVzZSwgY29weSwgbW9kaWZ5LCBhbmQvb3IgZGlzdHJpYnV0ZSB0aGlzIHNvZnR3YXJlIGZvciBhbnlcclxucHVycG9zZSB3aXRoIG9yIHdpdGhvdXQgZmVlIGlzIGhlcmVieSBncmFudGVkLlxyXG5cclxuVEhFIFNPRlRXQVJFIElTIFBST1ZJREVEIFwiQVMgSVNcIiBBTkQgVEhFIEFVVEhPUiBESVNDTEFJTVMgQUxMIFdBUlJBTlRJRVMgV0lUSFxyXG5SRUdBUkQgVE8gVEhJUyBTT0ZUV0FSRSBJTkNMVURJTkcgQUxMIElNUExJRUQgV0FSUkFOVElFUyBPRiBNRVJDSEFOVEFCSUxJVFlcclxuQU5EIEZJVE5FU1MuIElOIE5PIEVWRU5UIFNIQUxMIFRIRSBBVVRIT1IgQkUgTElBQkxFIEZPUiBBTlkgU1BFQ0lBTCwgRElSRUNULFxyXG5JTkRJUkVDVCwgT1IgQ09OU0VRVUVOVElBTCBEQU1BR0VTIE9SIEFOWSBEQU1BR0VTIFdIQVRTT0VWRVIgUkVTVUxUSU5HIEZST01cclxuTE9TUyBPRiBVU0UsIERBVEEgT1IgUFJPRklUUywgV0hFVEhFUiBJTiBBTiBBQ1RJT04gT0YgQ09OVFJBQ1QsIE5FR0xJR0VOQ0UgT1JcclxuT1RIRVIgVE9SVElPVVMgQUNUSU9OLCBBUklTSU5HIE9VVCBPRiBPUiBJTiBDT05ORUNUSU9OIFdJVEggVEhFIFVTRSBPUlxyXG5QRVJGT1JNQU5DRSBPRiBUSElTIFNPRlRXQVJFLlxyXG4qKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKiAqL1xyXG4vKiBnbG9iYWwgUmVmbGVjdCwgUHJvbWlzZSAqL1xyXG5cclxudmFyIGV4dGVuZFN0YXRpY3MgPSBmdW5jdGlvbihkLCBiKSB7XHJcbiAgICBleHRlbmRTdGF0aWNzID0gT2JqZWN0LnNldFByb3RvdHlwZU9mIHx8XHJcbiAgICAgICAgKHsgX19wcm90b19fOiBbXSB9IGluc3RhbmNlb2YgQXJyYXkgJiYgZnVuY3Rpb24gKGQsIGIpIHsgZC5fX3Byb3RvX18gPSBiOyB9KSB8fFxyXG4gICAgICAgIGZ1bmN0aW9uIChkLCBiKSB7IGZvciAodmFyIHAgaW4gYikgaWYgKE9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHkuY2FsbChiLCBwKSkgZFtwXSA9IGJbcF07IH07XHJcbiAgICByZXR1cm4gZXh0ZW5kU3RhdGljcyhkLCBiKTtcclxufTtcclxuXHJcbmZ1bmN0aW9uIF9fZXh0ZW5kcyhkLCBiKSB7XHJcbiAgICBpZiAodHlwZW9mIGIgIT09IFwiZnVuY3Rpb25cIiAmJiBiICE9PSBudWxsKVxyXG4gICAgICAgIHRocm93IG5ldyBUeXBlRXJyb3IoXCJDbGFzcyBleHRlbmRzIHZhbHVlIFwiICsgU3RyaW5nKGIpICsgXCIgaXMgbm90IGEgY29uc3RydWN0b3Igb3IgbnVsbFwiKTtcclxuICAgIGV4dGVuZFN0YXRpY3MoZCwgYik7XHJcbiAgICBmdW5jdGlvbiBfXygpIHsgdGhpcy5jb25zdHJ1Y3RvciA9IGQ7IH1cclxuICAgIGQucHJvdG90eXBlID0gYiA9PT0gbnVsbCA/IE9iamVjdC5jcmVhdGUoYikgOiAoX18ucHJvdG90eXBlID0gYi5wcm90b3R5cGUsIG5ldyBfXygpKTtcclxufVxyXG5cclxudmFyIF9fYXNzaWduID0gZnVuY3Rpb24oKSB7XHJcbiAgICBfX2Fzc2lnbiA9IE9iamVjdC5hc3NpZ24gfHwgZnVuY3Rpb24gX19hc3NpZ24odCkge1xyXG4gICAgICAgIGZvciAodmFyIHMsIGkgPSAxLCBuID0gYXJndW1lbnRzLmxlbmd0aDsgaSA8IG47IGkrKykge1xyXG4gICAgICAgICAgICBzID0gYXJndW1lbnRzW2ldO1xyXG4gICAgICAgICAgICBmb3IgKHZhciBwIGluIHMpIGlmIChPYmplY3QucHJvdG90eXBlLmhhc093blByb3BlcnR5LmNhbGwocywgcCkpIHRbcF0gPSBzW3BdO1xyXG4gICAgICAgIH1cclxuICAgICAgICByZXR1cm4gdDtcclxuICAgIH07XHJcbiAgICByZXR1cm4gX19hc3NpZ24uYXBwbHkodGhpcywgYXJndW1lbnRzKTtcclxufTtcclxuXHJcbmZ1bmN0aW9uIF9fcmVzdChzLCBlKSB7XHJcbiAgICB2YXIgdCA9IHt9O1xyXG4gICAgZm9yICh2YXIgcCBpbiBzKSBpZiAoT2JqZWN0LnByb3RvdHlwZS5oYXNPd25Qcm9wZXJ0eS5jYWxsKHMsIHApICYmIGUuaW5kZXhPZihwKSA8IDApXHJcbiAgICAgICAgdFtwXSA9IHNbcF07XHJcbiAgICBpZiAocyAhPSBudWxsICYmIHR5cGVvZiBPYmplY3QuZ2V0T3duUHJvcGVydHlTeW1ib2xzID09PSBcImZ1bmN0aW9uXCIpXHJcbiAgICAgICAgZm9yICh2YXIgaSA9IDAsIHAgPSBPYmplY3QuZ2V0T3duUHJvcGVydHlTeW1ib2xzKHMpOyBpIDwgcC5sZW5ndGg7IGkrKykge1xyXG4gICAgICAgICAgICBpZiAoZS5pbmRleE9mKHBbaV0pIDwgMCAmJiBPYmplY3QucHJvdG90eXBlLnByb3BlcnR5SXNFbnVtZXJhYmxlLmNhbGwocywgcFtpXSkpXHJcbiAgICAgICAgICAgICAgICB0W3BbaV1dID0gc1twW2ldXTtcclxuICAgICAgICB9XHJcbiAgICByZXR1cm4gdDtcclxufVxuXG52YXIgaXNGdW5jdGlvbiA9IGZ1bmN0aW9uIChmKSB7XHJcbiAgICByZXR1cm4gdHlwZW9mIGYgPT09ICdmdW5jdGlvbic7XHJcbn07XHJcbnZhciBpc1VuZGVmaW5lZCA9IGZ1bmN0aW9uICh4KSB7XHJcbiAgICByZXR1cm4geCA9PT0gdm9pZCAwO1xyXG59O1xyXG52YXIgaXNOdWxsID0gZnVuY3Rpb24gKHgpIHtcclxuICAgIHJldHVybiB4ID09PSBudWxsO1xyXG59O1xuXG5mdW5jdGlvbiBQb3N0SG9nRmVhdHVyZShfYSkge1xyXG4gICAgdmFyIGZsYWcgPSBfYS5mbGFnLCBtYXRjaCA9IF9hLm1hdGNoLCBjaGlsZHJlbiA9IF9hLmNoaWxkcmVuLCBmYWxsYmFjayA9IF9hLmZhbGxiYWNrLCB2aXNpYmlsaXR5T2JzZXJ2ZXJPcHRpb25zID0gX2EudmlzaWJpbGl0eU9ic2VydmVyT3B0aW9ucywgdHJhY2tJbnRlcmFjdGlvbiA9IF9hLnRyYWNrSW50ZXJhY3Rpb24sIHRyYWNrVmlldyA9IF9hLnRyYWNrVmlldywgcHJvcHMgPSBfX3Jlc3QoX2EsIFtcImZsYWdcIiwgXCJtYXRjaFwiLCBcImNoaWxkcmVuXCIsIFwiZmFsbGJhY2tcIiwgXCJ2aXNpYmlsaXR5T2JzZXJ2ZXJPcHRpb25zXCIsIFwidHJhY2tJbnRlcmFjdGlvblwiLCBcInRyYWNrVmlld1wiXSk7XHJcbiAgICB2YXIgcGF5bG9hZCA9IHVzZUZlYXR1cmVGbGFnUGF5bG9hZChmbGFnKTtcclxuICAgIHZhciB2YXJpYW50ID0gdXNlRmVhdHVyZUZsYWdWYXJpYW50S2V5KGZsYWcpO1xyXG4gICAgdmFyIHNob3VsZFRyYWNrSW50ZXJhY3Rpb24gPSB0cmFja0ludGVyYWN0aW9uICE9PSBudWxsICYmIHRyYWNrSW50ZXJhY3Rpb24gIT09IHZvaWQgMCA/IHRyYWNrSW50ZXJhY3Rpb24gOiB0cnVlO1xyXG4gICAgdmFyIHNob3VsZFRyYWNrVmlldyA9IHRyYWNrVmlldyAhPT0gbnVsbCAmJiB0cmFja1ZpZXcgIT09IHZvaWQgMCA/IHRyYWNrVmlldyA6IHRydWU7XHJcbiAgICBpZiAoaXNVbmRlZmluZWQobWF0Y2gpIHx8IHZhcmlhbnQgPT09IG1hdGNoKSB7XHJcbiAgICAgICAgdmFyIGNoaWxkTm9kZSA9IGlzRnVuY3Rpb24oY2hpbGRyZW4pID8gY2hpbGRyZW4ocGF5bG9hZCkgOiBjaGlsZHJlbjtcclxuICAgICAgICByZXR1cm4gKFJlYWN0LmNyZWF0ZUVsZW1lbnQoVmlzaWJpbGl0eUFuZENsaWNrVHJhY2tlcnMsIF9fYXNzaWduKHsgZmxhZzogZmxhZywgb3B0aW9uczogdmlzaWJpbGl0eU9ic2VydmVyT3B0aW9ucywgdHJhY2tJbnRlcmFjdGlvbjogc2hvdWxkVHJhY2tJbnRlcmFjdGlvbiwgdHJhY2tWaWV3OiBzaG91bGRUcmFja1ZpZXcgfSwgcHJvcHMpLCBjaGlsZE5vZGUpKTtcclxuICAgIH1cclxuICAgIHJldHVybiBSZWFjdC5jcmVhdGVFbGVtZW50KFJlYWN0LkZyYWdtZW50LCBudWxsLCBmYWxsYmFjayk7XHJcbn1cclxuZnVuY3Rpb24gY2FwdHVyZUZlYXR1cmVJbnRlcmFjdGlvbihfYSkge1xyXG4gICAgdmFyIF9iO1xyXG4gICAgdmFyIGZsYWcgPSBfYS5mbGFnLCBwb3N0aG9nID0gX2EucG9zdGhvZywgZmxhZ1ZhcmlhbnQgPSBfYS5mbGFnVmFyaWFudDtcclxuICAgIHZhciBwcm9wZXJ0aWVzID0ge1xyXG4gICAgICAgIGZlYXR1cmVfZmxhZzogZmxhZyxcclxuICAgICAgICAkc2V0OiAoX2IgPSB7fSwgX2JbXCIkZmVhdHVyZV9pbnRlcmFjdGlvbi9cIi5jb25jYXQoZmxhZyldID0gZmxhZ1ZhcmlhbnQgIT09IG51bGwgJiYgZmxhZ1ZhcmlhbnQgIT09IHZvaWQgMCA/IGZsYWdWYXJpYW50IDogdHJ1ZSwgX2IpLFxyXG4gICAgfTtcclxuICAgIGlmICh0eXBlb2YgZmxhZ1ZhcmlhbnQgPT09ICdzdHJpbmcnKSB7XHJcbiAgICAgICAgcHJvcGVydGllcy5mZWF0dXJlX2ZsYWdfdmFyaWFudCA9IGZsYWdWYXJpYW50O1xyXG4gICAgfVxyXG4gICAgcG9zdGhvZy5jYXB0dXJlKCckZmVhdHVyZV9pbnRlcmFjdGlvbicsIHByb3BlcnRpZXMpO1xyXG59XHJcbmZ1bmN0aW9uIGNhcHR1cmVGZWF0dXJlVmlldyhfYSkge1xyXG4gICAgdmFyIF9iO1xyXG4gICAgdmFyIGZsYWcgPSBfYS5mbGFnLCBwb3N0aG9nID0gX2EucG9zdGhvZywgZmxhZ1ZhcmlhbnQgPSBfYS5mbGFnVmFyaWFudDtcclxuICAgIHZhciBwcm9wZXJ0aWVzID0ge1xyXG4gICAgICAgIGZlYXR1cmVfZmxhZzogZmxhZyxcclxuICAgICAgICAkc2V0OiAoX2IgPSB7fSwgX2JbXCIkZmVhdHVyZV92aWV3L1wiLmNvbmNhdChmbGFnKV0gPSBmbGFnVmFyaWFudCAhPT0gbnVsbCAmJiBmbGFnVmFyaWFudCAhPT0gdm9pZCAwID8gZmxhZ1ZhcmlhbnQgOiB0cnVlLCBfYiksXHJcbiAgICB9O1xyXG4gICAgaWYgKHR5cGVvZiBmbGFnVmFyaWFudCA9PT0gJ3N0cmluZycpIHtcclxuICAgICAgICBwcm9wZXJ0aWVzLmZlYXR1cmVfZmxhZ192YXJpYW50ID0gZmxhZ1ZhcmlhbnQ7XHJcbiAgICB9XHJcbiAgICBwb3N0aG9nLmNhcHR1cmUoJyRmZWF0dXJlX3ZpZXcnLCBwcm9wZXJ0aWVzKTtcclxufVxyXG5mdW5jdGlvbiBWaXNpYmlsaXR5QW5kQ2xpY2tUcmFja2VyKF9hKSB7XHJcbiAgICB2YXIgZmxhZyA9IF9hLmZsYWcsIGNoaWxkcmVuID0gX2EuY2hpbGRyZW4sIG9uSW50ZXJzZWN0ID0gX2Eub25JbnRlcnNlY3QsIG9uQ2xpY2sgPSBfYS5vbkNsaWNrLCB0cmFja1ZpZXcgPSBfYS50cmFja1ZpZXcsIG9wdGlvbnMgPSBfYS5vcHRpb25zLCBwcm9wcyA9IF9fcmVzdChfYSwgW1wiZmxhZ1wiLCBcImNoaWxkcmVuXCIsIFwib25JbnRlcnNlY3RcIiwgXCJvbkNsaWNrXCIsIFwidHJhY2tWaWV3XCIsIFwib3B0aW9uc1wiXSk7XHJcbiAgICB2YXIgcmVmID0gdXNlUmVmKG51bGwpO1xyXG4gICAgdmFyIHBvc3Rob2cgPSB1c2VQb3N0SG9nKCk7XHJcbiAgICB1c2VFZmZlY3QoZnVuY3Rpb24gKCkge1xyXG4gICAgICAgIGlmIChpc051bGwocmVmLmN1cnJlbnQpIHx8ICF0cmFja1ZpZXcpXHJcbiAgICAgICAgICAgIHJldHVybjtcclxuICAgICAgICB2YXIgb2JzZXJ2ZXIgPSBuZXcgSW50ZXJzZWN0aW9uT2JzZXJ2ZXIoZnVuY3Rpb24gKF9hKSB7XHJcbiAgICAgICAgICAgIHZhciBlbnRyeSA9IF9hWzBdO1xyXG4gICAgICAgICAgICByZXR1cm4gb25JbnRlcnNlY3QoZW50cnkpO1xyXG4gICAgICAgIH0sIF9fYXNzaWduKHsgdGhyZXNob2xkOiAwLjEgfSwgb3B0aW9ucykpO1xyXG4gICAgICAgIG9ic2VydmVyLm9ic2VydmUocmVmLmN1cnJlbnQpO1xyXG4gICAgICAgIHJldHVybiBmdW5jdGlvbiAoKSB7IHJldHVybiBvYnNlcnZlci5kaXNjb25uZWN0KCk7IH07XHJcbiAgICB9LCBbZmxhZywgb3B0aW9ucywgcG9zdGhvZywgcmVmLCB0cmFja1ZpZXcsIG9uSW50ZXJzZWN0XSk7XHJcbiAgICByZXR1cm4gKFJlYWN0LmNyZWF0ZUVsZW1lbnQoXCJkaXZcIiwgX19hc3NpZ24oeyByZWY6IHJlZiB9LCBwcm9wcywgeyBvbkNsaWNrOiBvbkNsaWNrIH0pLCBjaGlsZHJlbikpO1xyXG59XHJcbmZ1bmN0aW9uIFZpc2liaWxpdHlBbmRDbGlja1RyYWNrZXJzKF9hKSB7XHJcbiAgICB2YXIgZmxhZyA9IF9hLmZsYWcsIGNoaWxkcmVuID0gX2EuY2hpbGRyZW4sIHRyYWNrSW50ZXJhY3Rpb24gPSBfYS50cmFja0ludGVyYWN0aW9uLCB0cmFja1ZpZXcgPSBfYS50cmFja1ZpZXcsIG9wdGlvbnMgPSBfYS5vcHRpb25zLCBwcm9wcyA9IF9fcmVzdChfYSwgW1wiZmxhZ1wiLCBcImNoaWxkcmVuXCIsIFwidHJhY2tJbnRlcmFjdGlvblwiLCBcInRyYWNrVmlld1wiLCBcIm9wdGlvbnNcIl0pO1xyXG4gICAgdmFyIGNsaWNrVHJhY2tlZFJlZiA9IHVzZVJlZihmYWxzZSk7XHJcbiAgICB2YXIgdmlzaWJpbGl0eVRyYWNrZWRSZWYgPSB1c2VSZWYoZmFsc2UpO1xyXG4gICAgdmFyIHBvc3Rob2cgPSB1c2VQb3N0SG9nKCk7XHJcbiAgICB2YXIgdmFyaWFudCA9IHVzZUZlYXR1cmVGbGFnVmFyaWFudEtleShmbGFnKTtcclxuICAgIHZhciBjYWNoZWRPbkNsaWNrID0gdXNlQ2FsbGJhY2soZnVuY3Rpb24gKCkge1xyXG4gICAgICAgIGlmICghY2xpY2tUcmFja2VkUmVmLmN1cnJlbnQgJiYgdHJhY2tJbnRlcmFjdGlvbikge1xyXG4gICAgICAgICAgICBjYXB0dXJlRmVhdHVyZUludGVyYWN0aW9uKHsgZmxhZzogZmxhZywgcG9zdGhvZzogcG9zdGhvZywgZmxhZ1ZhcmlhbnQ6IHZhcmlhbnQgfSk7XHJcbiAgICAgICAgICAgIGNsaWNrVHJhY2tlZFJlZi5jdXJyZW50ID0gdHJ1ZTtcclxuICAgICAgICB9XHJcbiAgICB9LCBbZmxhZywgcG9zdGhvZywgdHJhY2tJbnRlcmFjdGlvbiwgdmFyaWFudF0pO1xyXG4gICAgdmFyIG9uSW50ZXJzZWN0ID0gZnVuY3Rpb24gKGVudHJ5KSB7XHJcbiAgICAgICAgaWYgKCF2aXNpYmlsaXR5VHJhY2tlZFJlZi5jdXJyZW50ICYmIGVudHJ5LmlzSW50ZXJzZWN0aW5nKSB7XHJcbiAgICAgICAgICAgIGNhcHR1cmVGZWF0dXJlVmlldyh7IGZsYWc6IGZsYWcsIHBvc3Rob2c6IHBvc3Rob2csIGZsYWdWYXJpYW50OiB2YXJpYW50IH0pO1xyXG4gICAgICAgICAgICB2aXNpYmlsaXR5VHJhY2tlZFJlZi5jdXJyZW50ID0gdHJ1ZTtcclxuICAgICAgICB9XHJcbiAgICB9O1xyXG4gICAgdmFyIHRyYWNrZWRDaGlsZHJlbiA9IENoaWxkcmVuLm1hcChjaGlsZHJlbiwgZnVuY3Rpb24gKGNoaWxkKSB7XHJcbiAgICAgICAgcmV0dXJuIChSZWFjdC5jcmVhdGVFbGVtZW50KFZpc2liaWxpdHlBbmRDbGlja1RyYWNrZXIsIF9fYXNzaWduKHsgZmxhZzogZmxhZywgb25DbGljazogY2FjaGVkT25DbGljaywgb25JbnRlcnNlY3Q6IG9uSW50ZXJzZWN0LCB0cmFja1ZpZXc6IHRyYWNrVmlldywgb3B0aW9uczogb3B0aW9ucyB9LCBwcm9wcyksIGNoaWxkKSk7XHJcbiAgICB9KTtcclxuICAgIHJldHVybiBSZWFjdC5jcmVhdGVFbGVtZW50KFJlYWN0LkZyYWdtZW50LCBudWxsLCB0cmFja2VkQ2hpbGRyZW4pO1xyXG59XG5cbnZhciBJTklUSUFMX1NUQVRFID0ge1xyXG4gICAgY29tcG9uZW50U3RhY2s6IG51bGwsXHJcbiAgICBlcnJvcjogbnVsbCxcclxufTtcclxudmFyIF9fUE9TVEhPR19FUlJPUl9NRVNTQUdFUyA9IHtcclxuICAgIElOVkFMSURfRkFMTEJBQ0s6ICdbUG9zdEhvZy5qc11bUG9zdEhvZ0Vycm9yQm91bmRhcnldIEludmFsaWQgZmFsbGJhY2sgcHJvcCwgcHJvdmlkZSBhIHZhbGlkIFJlYWN0IGVsZW1lbnQgb3IgYSBmdW5jdGlvbiB0aGF0IHJldHVybnMgYSB2YWxpZCBSZWFjdCBlbGVtZW50LicsXHJcbn07XHJcbnZhciBQb3N0SG9nRXJyb3JCb3VuZGFyeSA9IChmdW5jdGlvbiAoX3N1cGVyKSB7XHJcbiAgICBfX2V4dGVuZHMoUG9zdEhvZ0Vycm9yQm91bmRhcnksIF9zdXBlcik7XHJcbiAgICBmdW5jdGlvbiBQb3N0SG9nRXJyb3JCb3VuZGFyeShwcm9wcykge1xyXG4gICAgICAgIHZhciBfdGhpcyA9IF9zdXBlci5jYWxsKHRoaXMsIHByb3BzKSB8fCB0aGlzO1xyXG4gICAgICAgIF90aGlzLnN0YXRlID0gSU5JVElBTF9TVEFURTtcclxuICAgICAgICByZXR1cm4gX3RoaXM7XHJcbiAgICB9XHJcbiAgICBQb3N0SG9nRXJyb3JCb3VuZGFyeS5wcm90b3R5cGUuY29tcG9uZW50RGlkQ2F0Y2ggPSBmdW5jdGlvbiAoZXJyb3IsIGVycm9ySW5mbykge1xyXG4gICAgICAgIHZhciBjb21wb25lbnRTdGFjayA9IGVycm9ySW5mby5jb21wb25lbnRTdGFjaztcclxuICAgICAgICB2YXIgYWRkaXRpb25hbFByb3BlcnRpZXMgPSB0aGlzLnByb3BzLmFkZGl0aW9uYWxQcm9wZXJ0aWVzO1xyXG4gICAgICAgIHRoaXMuc2V0U3RhdGUoe1xyXG4gICAgICAgICAgICBlcnJvcjogZXJyb3IsXHJcbiAgICAgICAgICAgIGNvbXBvbmVudFN0YWNrOiBjb21wb25lbnRTdGFjayxcclxuICAgICAgICB9KTtcclxuICAgICAgICB2YXIgY3VycmVudFByb3BlcnRpZXM7XHJcbiAgICAgICAgaWYgKGlzRnVuY3Rpb24oYWRkaXRpb25hbFByb3BlcnRpZXMpKSB7XHJcbiAgICAgICAgICAgIGN1cnJlbnRQcm9wZXJ0aWVzID0gYWRkaXRpb25hbFByb3BlcnRpZXMoZXJyb3IpO1xyXG4gICAgICAgIH1cclxuICAgICAgICBlbHNlIGlmICh0eXBlb2YgYWRkaXRpb25hbFByb3BlcnRpZXMgPT09ICdvYmplY3QnKSB7XHJcbiAgICAgICAgICAgIGN1cnJlbnRQcm9wZXJ0aWVzID0gYWRkaXRpb25hbFByb3BlcnRpZXM7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIHZhciBjbGllbnQgPSB0aGlzLmNvbnRleHQuY2xpZW50O1xyXG4gICAgICAgIGNsaWVudC5jYXB0dXJlRXhjZXB0aW9uKGVycm9yLCBjdXJyZW50UHJvcGVydGllcyk7XHJcbiAgICB9O1xyXG4gICAgUG9zdEhvZ0Vycm9yQm91bmRhcnkucHJvdG90eXBlLnJlbmRlciA9IGZ1bmN0aW9uICgpIHtcclxuICAgICAgICB2YXIgX2EgPSB0aGlzLnByb3BzLCBjaGlsZHJlbiA9IF9hLmNoaWxkcmVuLCBmYWxsYmFjayA9IF9hLmZhbGxiYWNrO1xyXG4gICAgICAgIHZhciBzdGF0ZSA9IHRoaXMuc3RhdGU7XHJcbiAgICAgICAgaWYgKHN0YXRlLmNvbXBvbmVudFN0YWNrID09IG51bGwpIHtcclxuICAgICAgICAgICAgcmV0dXJuIGlzRnVuY3Rpb24oY2hpbGRyZW4pID8gY2hpbGRyZW4oKSA6IGNoaWxkcmVuO1xyXG4gICAgICAgIH1cclxuICAgICAgICB2YXIgZWxlbWVudCA9IGlzRnVuY3Rpb24oZmFsbGJhY2spXHJcbiAgICAgICAgICAgID8gUmVhY3QuY3JlYXRlRWxlbWVudChmYWxsYmFjaywge1xyXG4gICAgICAgICAgICAgICAgZXJyb3I6IHN0YXRlLmVycm9yLFxyXG4gICAgICAgICAgICAgICAgY29tcG9uZW50U3RhY2s6IHN0YXRlLmNvbXBvbmVudFN0YWNrLFxyXG4gICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICA6IGZhbGxiYWNrO1xyXG4gICAgICAgIGlmIChSZWFjdC5pc1ZhbGlkRWxlbWVudChlbGVtZW50KSkge1xyXG4gICAgICAgICAgICByZXR1cm4gZWxlbWVudDtcclxuICAgICAgICB9XHJcbiAgICAgICAgY29uc29sZS53YXJuKF9fUE9TVEhPR19FUlJPUl9NRVNTQUdFUy5JTlZBTElEX0ZBTExCQUNLKTtcclxuICAgICAgICByZXR1cm4gUmVhY3QuY3JlYXRlRWxlbWVudChSZWFjdC5GcmFnbWVudCwgbnVsbCk7XHJcbiAgICB9O1xyXG4gICAgUG9zdEhvZ0Vycm9yQm91bmRhcnkuY29udGV4dFR5cGUgPSBQb3N0SG9nQ29udGV4dDtcclxuICAgIHJldHVybiBQb3N0SG9nRXJyb3JCb3VuZGFyeTtcclxufShSZWFjdC5Db21wb25lbnQpKTtcblxuZXhwb3J0IHsgUG9zdEhvZ0NvbnRleHQsIFBvc3RIb2dFcnJvckJvdW5kYXJ5LCBQb3N0SG9nRmVhdHVyZSwgUG9zdEhvZ1Byb3ZpZGVyLCB1c2VBY3RpdmVGZWF0dXJlRmxhZ3MsIHVzZUZlYXR1cmVGbGFnRW5hYmxlZCwgdXNlRmVhdHVyZUZsYWdQYXlsb2FkLCB1c2VGZWF0dXJlRmxhZ1ZhcmlhbnRLZXksIHVzZVBvc3RIb2cgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/posthog-js@1.246.0_@rrweb+types@2.0.0-alpha.17/node_modules/posthog-js/react/dist/esm/index.js\n");

/***/ })

};
;