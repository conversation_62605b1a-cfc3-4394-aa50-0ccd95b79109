"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90";
exports.ids = ["vendor-chunks/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90"];
exports.modules = {

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/enums/AttributeNames.js":
/*!********************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/enums/AttributeNames.js ***!
  \********************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.AttributeNames = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar AttributeNames;\n(function (AttributeNames) {\n    AttributeNames[\"HAPI_TYPE\"] = \"hapi.type\";\n    AttributeNames[\"PLUGIN_NAME\"] = \"hapi.plugin.name\";\n    AttributeNames[\"EXT_TYPE\"] = \"server.ext.type\";\n})(AttributeNames = exports.AttributeNames || (exports.AttributeNames = {}));\n//# sourceMappingURL=AttributeNames.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/enums/AttributeNames.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/index.js":
/*!*****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/index.js ***!
  \*****************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/instrumentation.js\"), exports);\n__exportStar(__webpack_require__(/*! ./enums/AttributeNames */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/enums/AttributeNames.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/index.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/instrumentation.js":
/*!***************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/instrumentation.js ***!
  \***************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.HapiInstrumentation = void 0;\nconst api = __webpack_require__(/*! @opentelemetry/api */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst core_1 = __webpack_require__(/*! @opentelemetry/core */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+core@1.30.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/core/build/esm/index.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/version.js\");\nconst internal_types_1 = __webpack_require__(/*! ./internal-types */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/internal-types.js\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/utils.js\");\n/** Hapi instrumentation for OpenTelemetry */\nclass HapiInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, config);\n    }\n    init() {\n        return new instrumentation_1.InstrumentationNodeModuleDefinition(internal_types_1.HapiComponentName, ['>=17.0.0 <22'], (module) => {\n            const moduleExports = module[Symbol.toStringTag] === 'Module' ? module.default : module;\n            if (!(0, instrumentation_1.isWrapped)(moduleExports.server)) {\n                this._wrap(moduleExports, 'server', this._getServerPatch.bind(this));\n            }\n            if (!(0, instrumentation_1.isWrapped)(moduleExports.Server)) {\n                this._wrap(moduleExports, 'Server', this._getServerPatch.bind(this));\n            }\n            return moduleExports;\n        }, (module) => {\n            const moduleExports = module[Symbol.toStringTag] === 'Module' ? module.default : module;\n            this._massUnwrap([moduleExports], ['server', 'Server']);\n        });\n    }\n    /**\n     * Patches the Hapi.server and Hapi.Server functions in order to instrument\n     * the server.route, server.ext, and server.register functions via calls to the\n     * @function _getServerRoutePatch, @function _getServerExtPatch, and\n     * @function _getServerRegisterPatch functions\n     * @param original - the original Hapi Server creation function\n     */\n    _getServerPatch(original) {\n        const instrumentation = this;\n        const self = this;\n        return function server(opts) {\n            const newServer = original.apply(this, [opts]);\n            self._wrap(newServer, 'route', originalRouter => {\n                return instrumentation._getServerRoutePatch.bind(instrumentation)(originalRouter);\n            });\n            // Casting as any is necessary here due to multiple overloads on the Hapi.ext\n            // function, which requires supporting a variety of different parameters\n            // as extension inputs\n            self._wrap(newServer, 'ext', originalExtHandler => {\n                return instrumentation._getServerExtPatch.bind(instrumentation)(\n                // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                originalExtHandler);\n            });\n            // Casting as any is necessary here due to multiple overloads on the Hapi.Server.register\n            // function, which requires supporting a variety of different types of Plugin inputs\n            self._wrap(newServer, 'register', \n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            instrumentation._getServerRegisterPatch.bind(instrumentation));\n            return newServer;\n        };\n    }\n    /**\n     * Patches the plugin register function used by the Hapi Server. This function\n     * goes through each plugin that is being registered and adds instrumentation\n     * via a call to the @function _wrapRegisterHandler function.\n     * @param {RegisterFunction<T>} original - the original register function which\n     * registers each plugin on the server\n     */\n    _getServerRegisterPatch(original) {\n        const instrumentation = this;\n        return function register(pluginInput, options) {\n            if (Array.isArray(pluginInput)) {\n                for (const pluginObj of pluginInput) {\n                    const plugin = (0, utils_1.getPluginFromInput)(pluginObj);\n                    instrumentation._wrapRegisterHandler(plugin);\n                }\n            }\n            else {\n                const plugin = (0, utils_1.getPluginFromInput)(pluginInput);\n                instrumentation._wrapRegisterHandler(plugin);\n            }\n            return original.apply(this, [pluginInput, options]);\n        };\n    }\n    /**\n     * Patches the Server.ext function which adds extension methods to the specified\n     * point along the request lifecycle. This function accepts the full range of\n     * accepted input into the standard Hapi `server.ext` function. For each extension,\n     * it adds instrumentation to the handler via a call to the @function _wrapExtMethods\n     * function.\n     * @param original - the original ext function which adds the extension method to the server\n     * @param {string} [pluginName] - if present, represents the name of the plugin responsible\n     * for adding this server extension. Else, signifies that the extension was added directly\n     */\n    _getServerExtPatch(original, pluginName) {\n        const instrumentation = this;\n        return function ext(...args) {\n            if (Array.isArray(args[0])) {\n                const eventsList = args[0];\n                for (let i = 0; i < eventsList.length; i++) {\n                    const eventObj = eventsList[i];\n                    if ((0, utils_1.isLifecycleExtType)(eventObj.type)) {\n                        const lifecycleEventObj = eventObj;\n                        const handler = instrumentation._wrapExtMethods(lifecycleEventObj.method, eventObj.type, pluginName);\n                        lifecycleEventObj.method = handler;\n                        eventsList[i] = lifecycleEventObj;\n                    }\n                }\n                return original.apply(this, args);\n            }\n            else if ((0, utils_1.isDirectExtInput)(args)) {\n                const extInput = args;\n                const method = extInput[1];\n                const handler = instrumentation._wrapExtMethods(method, extInput[0], pluginName);\n                return original.apply(this, [extInput[0], handler, extInput[2]]);\n            }\n            else if ((0, utils_1.isLifecycleExtEventObj)(args[0])) {\n                const lifecycleEventObj = args[0];\n                const handler = instrumentation._wrapExtMethods(lifecycleEventObj.method, lifecycleEventObj.type, pluginName);\n                lifecycleEventObj.method = handler;\n                return original.call(this, lifecycleEventObj);\n            }\n            return original.apply(this, args);\n        };\n    }\n    /**\n     * Patches the Server.route function. This function accepts either one or an array\n     * of Hapi.ServerRoute objects and adds instrumentation on each route via a call to\n     * the @function _wrapRouteHandler function.\n     * @param {HapiServerRouteInputMethod} original - the original route function which adds\n     * the route to the server\n     * @param {string} [pluginName] - if present, represents the name of the plugin responsible\n     * for adding this server route. Else, signifies that the route was added directly\n     */\n    _getServerRoutePatch(original, pluginName) {\n        const instrumentation = this;\n        return function route(route) {\n            if (Array.isArray(route)) {\n                for (let i = 0; i < route.length; i++) {\n                    const newRoute = instrumentation._wrapRouteHandler.call(instrumentation, route[i], pluginName);\n                    route[i] = newRoute;\n                }\n            }\n            else {\n                route = instrumentation._wrapRouteHandler.call(instrumentation, route, pluginName);\n            }\n            return original.apply(this, [route]);\n        };\n    }\n    /**\n     * Wraps newly registered plugins to add instrumentation to the plugin's clone of\n     * the original server. Specifically, wraps the server.route and server.ext functions\n     * via calls to @function _getServerRoutePatch and @function _getServerExtPatch\n     * @param {Hapi.Plugin<T>} plugin - the new plugin which is being instrumented\n     */\n    _wrapRegisterHandler(plugin) {\n        const instrumentation = this;\n        const pluginName = (0, utils_1.getPluginName)(plugin);\n        const oldRegister = plugin.register;\n        const self = this;\n        const newRegisterHandler = function (server, options) {\n            self._wrap(server, 'route', original => {\n                return instrumentation._getServerRoutePatch.bind(instrumentation)(original, pluginName);\n            });\n            // Casting as any is necessary here due to multiple overloads on the Hapi.ext\n            // function, which requires supporting a variety of different parameters\n            // as extension inputs\n            self._wrap(server, 'ext', originalExtHandler => {\n                return instrumentation._getServerExtPatch.bind(instrumentation)(\n                // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                originalExtHandler, pluginName);\n            });\n            return oldRegister.call(this, server, options);\n        };\n        plugin.register = newRegisterHandler;\n    }\n    /**\n     * Wraps request extension methods to add instrumentation to each new extension handler.\n     * Patches each individual extension in order to create the\n     * span and propagate context. It does not create spans when there is no parent span.\n     * @param {PatchableExtMethod | PatchableExtMethod[]} method - the request extension\n     * handler which is being instrumented\n     * @param {Hapi.ServerRequestExtType} extPoint - the point in the Hapi request lifecycle\n     * which this extension targets\n     * @param {string} [pluginName] - if present, represents the name of the plugin responsible\n     * for adding this server route. Else, signifies that the route was added directly\n     */\n    _wrapExtMethods(method, extPoint, pluginName) {\n        const instrumentation = this;\n        if (method instanceof Array) {\n            for (let i = 0; i < method.length; i++) {\n                method[i] = instrumentation._wrapExtMethods(method[i], extPoint);\n            }\n            return method;\n        }\n        else if ((0, utils_1.isPatchableExtMethod)(method)) {\n            if (method[internal_types_1.handlerPatched] === true)\n                return method;\n            method[internal_types_1.handlerPatched] = true;\n            const newHandler = async function (...params) {\n                if (api.trace.getSpan(api.context.active()) === undefined) {\n                    return await method.apply(this, params);\n                }\n                const metadata = (0, utils_1.getExtMetadata)(extPoint, pluginName);\n                const span = instrumentation.tracer.startSpan(metadata.name, {\n                    attributes: metadata.attributes,\n                });\n                try {\n                    return await api.context.with(api.trace.setSpan(api.context.active(), span), method, undefined, ...params);\n                }\n                catch (err) {\n                    span.recordException(err);\n                    span.setStatus({\n                        code: api.SpanStatusCode.ERROR,\n                        message: err.message,\n                    });\n                    throw err;\n                }\n                finally {\n                    span.end();\n                }\n            };\n            return newHandler;\n        }\n        return method;\n    }\n    /**\n     * Patches each individual route handler method in order to create the\n     * span and propagate context. It does not create spans when there is no parent span.\n     * @param {PatchableServerRoute} route - the route handler which is being instrumented\n     * @param {string} [pluginName] - if present, represents the name of the plugin responsible\n     * for adding this server route. Else, signifies that the route was added directly\n     */\n    _wrapRouteHandler(route, pluginName) {\n        var _a;\n        const instrumentation = this;\n        if (route[internal_types_1.handlerPatched] === true)\n            return route;\n        route[internal_types_1.handlerPatched] = true;\n        const wrapHandler = oldHandler => {\n            return async function (...params) {\n                if (api.trace.getSpan(api.context.active()) === undefined) {\n                    return await oldHandler.call(this, ...params);\n                }\n                const rpcMetadata = (0, core_1.getRPCMetadata)(api.context.active());\n                if ((rpcMetadata === null || rpcMetadata === void 0 ? void 0 : rpcMetadata.type) === core_1.RPCType.HTTP) {\n                    rpcMetadata.route = route.path;\n                }\n                const metadata = (0, utils_1.getRouteMetadata)(route, pluginName);\n                const span = instrumentation.tracer.startSpan(metadata.name, {\n                    attributes: metadata.attributes,\n                });\n                try {\n                    return await api.context.with(api.trace.setSpan(api.context.active(), span), () => oldHandler.call(this, ...params));\n                }\n                catch (err) {\n                    span.recordException(err);\n                    span.setStatus({\n                        code: api.SpanStatusCode.ERROR,\n                        message: err.message,\n                    });\n                    throw err;\n                }\n                finally {\n                    span.end();\n                }\n            };\n        };\n        if (typeof route.handler === 'function') {\n            route.handler = wrapHandler(route.handler);\n        }\n        else if (typeof route.options === 'function') {\n            const oldOptions = route.options;\n            route.options = function (server) {\n                const options = oldOptions(server);\n                if (typeof options.handler === 'function') {\n                    options.handler = wrapHandler(options.handler);\n                }\n                return options;\n            };\n        }\n        else if (typeof ((_a = route.options) === null || _a === void 0 ? void 0 : _a.handler) === 'function') {\n            route.options.handler = wrapHandler(route.options.handler);\n        }\n        return route;\n    }\n}\nexports.HapiInstrumentation = HapiInstrumentation;\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/instrumentation.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/internal-types.js":
/*!**************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/internal-types.js ***!
  \**************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.HapiLifecycleMethodNames = exports.HapiLayerType = exports.handlerPatched = exports.HapiComponentName = void 0;\nexports.HapiComponentName = '@hapi/hapi';\n/**\n * This symbol is used to mark a Hapi route handler or server extension handler as\n * already patched, since its possible to use these handlers multiple times\n * i.e. when allowing multiple versions of one plugin, or when registering a plugin\n * multiple times on different servers.\n */\nexports.handlerPatched = Symbol('hapi-handler-patched');\nexports.HapiLayerType = {\n    ROUTER: 'router',\n    PLUGIN: 'plugin',\n    EXT: 'server.ext',\n};\nexports.HapiLifecycleMethodNames = new Set([\n    'onPreAuth',\n    'onCredentials',\n    'onPostAuth',\n    'onPreHandler',\n    'onPostHandler',\n    'onPreResponse',\n    'onRequest',\n]);\n//# sourceMappingURL=internal-types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/internal-types.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/utils.js":
/*!*****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/utils.js ***!
  \*****************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getPluginFromInput = exports.getExtMetadata = exports.getRouteMetadata = exports.isPatchableExtMethod = exports.isDirectExtInput = exports.isLifecycleExtEventObj = exports.isLifecycleExtType = exports.getPluginName = void 0;\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst internal_types_1 = __webpack_require__(/*! ./internal-types */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/internal-types.js\");\nconst AttributeNames_1 = __webpack_require__(/*! ./enums/AttributeNames */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/enums/AttributeNames.js\");\nfunction getPluginName(plugin) {\n    if (plugin.name) {\n        return plugin.name;\n    }\n    else {\n        return plugin.pkg.name;\n    }\n}\nexports.getPluginName = getPluginName;\nconst isLifecycleExtType = (variableToCheck) => {\n    return (typeof variableToCheck === 'string' &&\n        internal_types_1.HapiLifecycleMethodNames.has(variableToCheck));\n};\nexports.isLifecycleExtType = isLifecycleExtType;\nconst isLifecycleExtEventObj = (variableToCheck) => {\n    var _a;\n    const event = (_a = variableToCheck) === null || _a === void 0 ? void 0 : _a.type;\n    return event !== undefined && (0, exports.isLifecycleExtType)(event);\n};\nexports.isLifecycleExtEventObj = isLifecycleExtEventObj;\nconst isDirectExtInput = (variableToCheck) => {\n    return (Array.isArray(variableToCheck) &&\n        variableToCheck.length <= 3 &&\n        (0, exports.isLifecycleExtType)(variableToCheck[0]) &&\n        typeof variableToCheck[1] === 'function');\n};\nexports.isDirectExtInput = isDirectExtInput;\nconst isPatchableExtMethod = (variableToCheck) => {\n    return !Array.isArray(variableToCheck);\n};\nexports.isPatchableExtMethod = isPatchableExtMethod;\nconst getRouteMetadata = (route, pluginName) => {\n    if (pluginName) {\n        return {\n            attributes: {\n                [semantic_conventions_1.SEMATTRS_HTTP_ROUTE]: route.path,\n                [semantic_conventions_1.SEMATTRS_HTTP_METHOD]: route.method,\n                [AttributeNames_1.AttributeNames.HAPI_TYPE]: internal_types_1.HapiLayerType.PLUGIN,\n                [AttributeNames_1.AttributeNames.PLUGIN_NAME]: pluginName,\n            },\n            name: `${pluginName}: route - ${route.path}`,\n        };\n    }\n    return {\n        attributes: {\n            [semantic_conventions_1.SEMATTRS_HTTP_ROUTE]: route.path,\n            [semantic_conventions_1.SEMATTRS_HTTP_METHOD]: route.method,\n            [AttributeNames_1.AttributeNames.HAPI_TYPE]: internal_types_1.HapiLayerType.ROUTER,\n        },\n        name: `route - ${route.path}`,\n    };\n};\nexports.getRouteMetadata = getRouteMetadata;\nconst getExtMetadata = (extPoint, pluginName) => {\n    if (pluginName) {\n        return {\n            attributes: {\n                [AttributeNames_1.AttributeNames.EXT_TYPE]: extPoint,\n                [AttributeNames_1.AttributeNames.HAPI_TYPE]: internal_types_1.HapiLayerType.EXT,\n                [AttributeNames_1.AttributeNames.PLUGIN_NAME]: pluginName,\n            },\n            name: `${pluginName}: ext - ${extPoint}`,\n        };\n    }\n    return {\n        attributes: {\n            [AttributeNames_1.AttributeNames.EXT_TYPE]: extPoint,\n            [AttributeNames_1.AttributeNames.HAPI_TYPE]: internal_types_1.HapiLayerType.EXT,\n        },\n        name: `ext - ${extPoint}`,\n    };\n};\nexports.getExtMetadata = getExtMetadata;\nconst getPluginFromInput = (pluginObj) => {\n    if ('plugin' in pluginObj) {\n        if ('plugin' in pluginObj.plugin) {\n            return pluginObj.plugin.plugin;\n        }\n        return pluginObj.plugin;\n    }\n    return pluginObj;\n};\nexports.getPluginFromInput = getPluginFromInput;\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/utils.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/version.js":
/*!*******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/version.js ***!
  \*******************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.45.2';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-hapi';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/version.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/enums/AttributeNames.js":
/*!********************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/enums/AttributeNames.js ***!
  \********************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.AttributeNames = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar AttributeNames;\n(function (AttributeNames) {\n    AttributeNames[\"HAPI_TYPE\"] = \"hapi.type\";\n    AttributeNames[\"PLUGIN_NAME\"] = \"hapi.plugin.name\";\n    AttributeNames[\"EXT_TYPE\"] = \"server.ext.type\";\n})(AttributeNames = exports.AttributeNames || (exports.AttributeNames = {}));\n//# sourceMappingURL=AttributeNames.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/enums/AttributeNames.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/index.js":
/*!*****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/index.js ***!
  \*****************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/instrumentation.js\"), exports);\n__exportStar(__webpack_require__(/*! ./enums/AttributeNames */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/enums/AttributeNames.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/index.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/instrumentation.js":
/*!***************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/instrumentation.js ***!
  \***************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.HapiInstrumentation = void 0;\nconst api = __webpack_require__(/*! @opentelemetry/api */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst core_1 = __webpack_require__(/*! @opentelemetry/core */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+core@1.30.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/core/build/esm/index.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/version.js\");\nconst internal_types_1 = __webpack_require__(/*! ./internal-types */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/internal-types.js\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/utils.js\");\n/** Hapi instrumentation for OpenTelemetry */\nclass HapiInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, config);\n    }\n    init() {\n        return new instrumentation_1.InstrumentationNodeModuleDefinition(internal_types_1.HapiComponentName, ['>=17.0.0 <22'], (module) => {\n            const moduleExports = module[Symbol.toStringTag] === 'Module' ? module.default : module;\n            if (!(0, instrumentation_1.isWrapped)(moduleExports.server)) {\n                this._wrap(moduleExports, 'server', this._getServerPatch.bind(this));\n            }\n            if (!(0, instrumentation_1.isWrapped)(moduleExports.Server)) {\n                this._wrap(moduleExports, 'Server', this._getServerPatch.bind(this));\n            }\n            return moduleExports;\n        }, (module) => {\n            const moduleExports = module[Symbol.toStringTag] === 'Module' ? module.default : module;\n            this._massUnwrap([moduleExports], ['server', 'Server']);\n        });\n    }\n    /**\n     * Patches the Hapi.server and Hapi.Server functions in order to instrument\n     * the server.route, server.ext, and server.register functions via calls to the\n     * @function _getServerRoutePatch, @function _getServerExtPatch, and\n     * @function _getServerRegisterPatch functions\n     * @param original - the original Hapi Server creation function\n     */\n    _getServerPatch(original) {\n        const instrumentation = this;\n        const self = this;\n        return function server(opts) {\n            const newServer = original.apply(this, [opts]);\n            self._wrap(newServer, 'route', originalRouter => {\n                return instrumentation._getServerRoutePatch.bind(instrumentation)(originalRouter);\n            });\n            // Casting as any is necessary here due to multiple overloads on the Hapi.ext\n            // function, which requires supporting a variety of different parameters\n            // as extension inputs\n            self._wrap(newServer, 'ext', originalExtHandler => {\n                return instrumentation._getServerExtPatch.bind(instrumentation)(\n                // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                originalExtHandler);\n            });\n            // Casting as any is necessary here due to multiple overloads on the Hapi.Server.register\n            // function, which requires supporting a variety of different types of Plugin inputs\n            self._wrap(newServer, 'register', \n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            instrumentation._getServerRegisterPatch.bind(instrumentation));\n            return newServer;\n        };\n    }\n    /**\n     * Patches the plugin register function used by the Hapi Server. This function\n     * goes through each plugin that is being registered and adds instrumentation\n     * via a call to the @function _wrapRegisterHandler function.\n     * @param {RegisterFunction<T>} original - the original register function which\n     * registers each plugin on the server\n     */\n    _getServerRegisterPatch(original) {\n        const instrumentation = this;\n        return function register(pluginInput, options) {\n            if (Array.isArray(pluginInput)) {\n                for (const pluginObj of pluginInput) {\n                    const plugin = (0, utils_1.getPluginFromInput)(pluginObj);\n                    instrumentation._wrapRegisterHandler(plugin);\n                }\n            }\n            else {\n                const plugin = (0, utils_1.getPluginFromInput)(pluginInput);\n                instrumentation._wrapRegisterHandler(plugin);\n            }\n            return original.apply(this, [pluginInput, options]);\n        };\n    }\n    /**\n     * Patches the Server.ext function which adds extension methods to the specified\n     * point along the request lifecycle. This function accepts the full range of\n     * accepted input into the standard Hapi `server.ext` function. For each extension,\n     * it adds instrumentation to the handler via a call to the @function _wrapExtMethods\n     * function.\n     * @param original - the original ext function which adds the extension method to the server\n     * @param {string} [pluginName] - if present, represents the name of the plugin responsible\n     * for adding this server extension. Else, signifies that the extension was added directly\n     */\n    _getServerExtPatch(original, pluginName) {\n        const instrumentation = this;\n        return function ext(...args) {\n            if (Array.isArray(args[0])) {\n                const eventsList = args[0];\n                for (let i = 0; i < eventsList.length; i++) {\n                    const eventObj = eventsList[i];\n                    if ((0, utils_1.isLifecycleExtType)(eventObj.type)) {\n                        const lifecycleEventObj = eventObj;\n                        const handler = instrumentation._wrapExtMethods(lifecycleEventObj.method, eventObj.type, pluginName);\n                        lifecycleEventObj.method = handler;\n                        eventsList[i] = lifecycleEventObj;\n                    }\n                }\n                return original.apply(this, args);\n            }\n            else if ((0, utils_1.isDirectExtInput)(args)) {\n                const extInput = args;\n                const method = extInput[1];\n                const handler = instrumentation._wrapExtMethods(method, extInput[0], pluginName);\n                return original.apply(this, [extInput[0], handler, extInput[2]]);\n            }\n            else if ((0, utils_1.isLifecycleExtEventObj)(args[0])) {\n                const lifecycleEventObj = args[0];\n                const handler = instrumentation._wrapExtMethods(lifecycleEventObj.method, lifecycleEventObj.type, pluginName);\n                lifecycleEventObj.method = handler;\n                return original.call(this, lifecycleEventObj);\n            }\n            return original.apply(this, args);\n        };\n    }\n    /**\n     * Patches the Server.route function. This function accepts either one or an array\n     * of Hapi.ServerRoute objects and adds instrumentation on each route via a call to\n     * the @function _wrapRouteHandler function.\n     * @param {HapiServerRouteInputMethod} original - the original route function which adds\n     * the route to the server\n     * @param {string} [pluginName] - if present, represents the name of the plugin responsible\n     * for adding this server route. Else, signifies that the route was added directly\n     */\n    _getServerRoutePatch(original, pluginName) {\n        const instrumentation = this;\n        return function route(route) {\n            if (Array.isArray(route)) {\n                for (let i = 0; i < route.length; i++) {\n                    const newRoute = instrumentation._wrapRouteHandler.call(instrumentation, route[i], pluginName);\n                    route[i] = newRoute;\n                }\n            }\n            else {\n                route = instrumentation._wrapRouteHandler.call(instrumentation, route, pluginName);\n            }\n            return original.apply(this, [route]);\n        };\n    }\n    /**\n     * Wraps newly registered plugins to add instrumentation to the plugin's clone of\n     * the original server. Specifically, wraps the server.route and server.ext functions\n     * via calls to @function _getServerRoutePatch and @function _getServerExtPatch\n     * @param {Hapi.Plugin<T>} plugin - the new plugin which is being instrumented\n     */\n    _wrapRegisterHandler(plugin) {\n        const instrumentation = this;\n        const pluginName = (0, utils_1.getPluginName)(plugin);\n        const oldRegister = plugin.register;\n        const self = this;\n        const newRegisterHandler = function (server, options) {\n            self._wrap(server, 'route', original => {\n                return instrumentation._getServerRoutePatch.bind(instrumentation)(original, pluginName);\n            });\n            // Casting as any is necessary here due to multiple overloads on the Hapi.ext\n            // function, which requires supporting a variety of different parameters\n            // as extension inputs\n            self._wrap(server, 'ext', originalExtHandler => {\n                return instrumentation._getServerExtPatch.bind(instrumentation)(\n                // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                originalExtHandler, pluginName);\n            });\n            return oldRegister.call(this, server, options);\n        };\n        plugin.register = newRegisterHandler;\n    }\n    /**\n     * Wraps request extension methods to add instrumentation to each new extension handler.\n     * Patches each individual extension in order to create the\n     * span and propagate context. It does not create spans when there is no parent span.\n     * @param {PatchableExtMethod | PatchableExtMethod[]} method - the request extension\n     * handler which is being instrumented\n     * @param {Hapi.ServerRequestExtType} extPoint - the point in the Hapi request lifecycle\n     * which this extension targets\n     * @param {string} [pluginName] - if present, represents the name of the plugin responsible\n     * for adding this server route. Else, signifies that the route was added directly\n     */\n    _wrapExtMethods(method, extPoint, pluginName) {\n        const instrumentation = this;\n        if (method instanceof Array) {\n            for (let i = 0; i < method.length; i++) {\n                method[i] = instrumentation._wrapExtMethods(method[i], extPoint);\n            }\n            return method;\n        }\n        else if ((0, utils_1.isPatchableExtMethod)(method)) {\n            if (method[internal_types_1.handlerPatched] === true)\n                return method;\n            method[internal_types_1.handlerPatched] = true;\n            const newHandler = async function (...params) {\n                if (api.trace.getSpan(api.context.active()) === undefined) {\n                    return await method.apply(this, params);\n                }\n                const metadata = (0, utils_1.getExtMetadata)(extPoint, pluginName);\n                const span = instrumentation.tracer.startSpan(metadata.name, {\n                    attributes: metadata.attributes,\n                });\n                try {\n                    return await api.context.with(api.trace.setSpan(api.context.active(), span), method, undefined, ...params);\n                }\n                catch (err) {\n                    span.recordException(err);\n                    span.setStatus({\n                        code: api.SpanStatusCode.ERROR,\n                        message: err.message,\n                    });\n                    throw err;\n                }\n                finally {\n                    span.end();\n                }\n            };\n            return newHandler;\n        }\n        return method;\n    }\n    /**\n     * Patches each individual route handler method in order to create the\n     * span and propagate context. It does not create spans when there is no parent span.\n     * @param {PatchableServerRoute} route - the route handler which is being instrumented\n     * @param {string} [pluginName] - if present, represents the name of the plugin responsible\n     * for adding this server route. Else, signifies that the route was added directly\n     */\n    _wrapRouteHandler(route, pluginName) {\n        var _a;\n        const instrumentation = this;\n        if (route[internal_types_1.handlerPatched] === true)\n            return route;\n        route[internal_types_1.handlerPatched] = true;\n        const wrapHandler = oldHandler => {\n            return async function (...params) {\n                if (api.trace.getSpan(api.context.active()) === undefined) {\n                    return await oldHandler.call(this, ...params);\n                }\n                const rpcMetadata = (0, core_1.getRPCMetadata)(api.context.active());\n                if ((rpcMetadata === null || rpcMetadata === void 0 ? void 0 : rpcMetadata.type) === core_1.RPCType.HTTP) {\n                    rpcMetadata.route = route.path;\n                }\n                const metadata = (0, utils_1.getRouteMetadata)(route, pluginName);\n                const span = instrumentation.tracer.startSpan(metadata.name, {\n                    attributes: metadata.attributes,\n                });\n                try {\n                    return await api.context.with(api.trace.setSpan(api.context.active(), span), () => oldHandler.call(this, ...params));\n                }\n                catch (err) {\n                    span.recordException(err);\n                    span.setStatus({\n                        code: api.SpanStatusCode.ERROR,\n                        message: err.message,\n                    });\n                    throw err;\n                }\n                finally {\n                    span.end();\n                }\n            };\n        };\n        if (typeof route.handler === 'function') {\n            route.handler = wrapHandler(route.handler);\n        }\n        else if (typeof route.options === 'function') {\n            const oldOptions = route.options;\n            route.options = function (server) {\n                const options = oldOptions(server);\n                if (typeof options.handler === 'function') {\n                    options.handler = wrapHandler(options.handler);\n                }\n                return options;\n            };\n        }\n        else if (typeof ((_a = route.options) === null || _a === void 0 ? void 0 : _a.handler) === 'function') {\n            route.options.handler = wrapHandler(route.options.handler);\n        }\n        return route;\n    }\n}\nexports.HapiInstrumentation = HapiInstrumentation;\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/instrumentation.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/internal-types.js":
/*!**************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/internal-types.js ***!
  \**************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.HapiLifecycleMethodNames = exports.HapiLayerType = exports.handlerPatched = exports.HapiComponentName = void 0;\nexports.HapiComponentName = '@hapi/hapi';\n/**\n * This symbol is used to mark a Hapi route handler or server extension handler as\n * already patched, since its possible to use these handlers multiple times\n * i.e. when allowing multiple versions of one plugin, or when registering a plugin\n * multiple times on different servers.\n */\nexports.handlerPatched = Symbol('hapi-handler-patched');\nexports.HapiLayerType = {\n    ROUTER: 'router',\n    PLUGIN: 'plugin',\n    EXT: 'server.ext',\n};\nexports.HapiLifecycleMethodNames = new Set([\n    'onPreAuth',\n    'onCredentials',\n    'onPostAuth',\n    'onPreHandler',\n    'onPostHandler',\n    'onPreResponse',\n    'onRequest',\n]);\n//# sourceMappingURL=internal-types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/internal-types.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/utils.js":
/*!*****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/utils.js ***!
  \*****************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getPluginFromInput = exports.getExtMetadata = exports.getRouteMetadata = exports.isPatchableExtMethod = exports.isDirectExtInput = exports.isLifecycleExtEventObj = exports.isLifecycleExtType = exports.getPluginName = void 0;\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst internal_types_1 = __webpack_require__(/*! ./internal-types */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/internal-types.js\");\nconst AttributeNames_1 = __webpack_require__(/*! ./enums/AttributeNames */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/enums/AttributeNames.js\");\nfunction getPluginName(plugin) {\n    if (plugin.name) {\n        return plugin.name;\n    }\n    else {\n        return plugin.pkg.name;\n    }\n}\nexports.getPluginName = getPluginName;\nconst isLifecycleExtType = (variableToCheck) => {\n    return (typeof variableToCheck === 'string' &&\n        internal_types_1.HapiLifecycleMethodNames.has(variableToCheck));\n};\nexports.isLifecycleExtType = isLifecycleExtType;\nconst isLifecycleExtEventObj = (variableToCheck) => {\n    var _a;\n    const event = (_a = variableToCheck) === null || _a === void 0 ? void 0 : _a.type;\n    return event !== undefined && (0, exports.isLifecycleExtType)(event);\n};\nexports.isLifecycleExtEventObj = isLifecycleExtEventObj;\nconst isDirectExtInput = (variableToCheck) => {\n    return (Array.isArray(variableToCheck) &&\n        variableToCheck.length <= 3 &&\n        (0, exports.isLifecycleExtType)(variableToCheck[0]) &&\n        typeof variableToCheck[1] === 'function');\n};\nexports.isDirectExtInput = isDirectExtInput;\nconst isPatchableExtMethod = (variableToCheck) => {\n    return !Array.isArray(variableToCheck);\n};\nexports.isPatchableExtMethod = isPatchableExtMethod;\nconst getRouteMetadata = (route, pluginName) => {\n    if (pluginName) {\n        return {\n            attributes: {\n                [semantic_conventions_1.SEMATTRS_HTTP_ROUTE]: route.path,\n                [semantic_conventions_1.SEMATTRS_HTTP_METHOD]: route.method,\n                [AttributeNames_1.AttributeNames.HAPI_TYPE]: internal_types_1.HapiLayerType.PLUGIN,\n                [AttributeNames_1.AttributeNames.PLUGIN_NAME]: pluginName,\n            },\n            name: `${pluginName}: route - ${route.path}`,\n        };\n    }\n    return {\n        attributes: {\n            [semantic_conventions_1.SEMATTRS_HTTP_ROUTE]: route.path,\n            [semantic_conventions_1.SEMATTRS_HTTP_METHOD]: route.method,\n            [AttributeNames_1.AttributeNames.HAPI_TYPE]: internal_types_1.HapiLayerType.ROUTER,\n        },\n        name: `route - ${route.path}`,\n    };\n};\nexports.getRouteMetadata = getRouteMetadata;\nconst getExtMetadata = (extPoint, pluginName) => {\n    if (pluginName) {\n        return {\n            attributes: {\n                [AttributeNames_1.AttributeNames.EXT_TYPE]: extPoint,\n                [AttributeNames_1.AttributeNames.HAPI_TYPE]: internal_types_1.HapiLayerType.EXT,\n                [AttributeNames_1.AttributeNames.PLUGIN_NAME]: pluginName,\n            },\n            name: `${pluginName}: ext - ${extPoint}`,\n        };\n    }\n    return {\n        attributes: {\n            [AttributeNames_1.AttributeNames.EXT_TYPE]: extPoint,\n            [AttributeNames_1.AttributeNames.HAPI_TYPE]: internal_types_1.HapiLayerType.EXT,\n        },\n        name: `ext - ${extPoint}`,\n    };\n};\nexports.getExtMetadata = getExtMetadata;\nconst getPluginFromInput = (pluginObj) => {\n    if ('plugin' in pluginObj) {\n        if ('plugin' in pluginObj.plugin) {\n            return pluginObj.plugin.plugin;\n        }\n        return pluginObj.plugin;\n    }\n    return pluginObj;\n};\nexports.getPluginFromInput = getPluginFromInput;\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/utils.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/version.js":
/*!*******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/version.js ***!
  \*******************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.45.2';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-hapi';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/version.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/enums/AttributeNames.js":
/*!********************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/enums/AttributeNames.js ***!
  \********************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.AttributeNames = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar AttributeNames;\n(function (AttributeNames) {\n    AttributeNames[\"HAPI_TYPE\"] = \"hapi.type\";\n    AttributeNames[\"PLUGIN_NAME\"] = \"hapi.plugin.name\";\n    AttributeNames[\"EXT_TYPE\"] = \"server.ext.type\";\n})(AttributeNames = exports.AttributeNames || (exports.AttributeNames = {}));\n//# sourceMappingURL=AttributeNames.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/enums/AttributeNames.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/index.js":
/*!*****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/index.js ***!
  \*****************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/instrumentation.js\"), exports);\n__exportStar(__webpack_require__(/*! ./enums/AttributeNames */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/enums/AttributeNames.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/instrumentation.js":
/*!***************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/instrumentation.js ***!
  \***************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.HapiInstrumentation = void 0;\nconst api = __webpack_require__(/*! @opentelemetry/api */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst core_1 = __webpack_require__(/*! @opentelemetry/core */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+core@1.30.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/core/build/esm/index.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/version.js\");\nconst internal_types_1 = __webpack_require__(/*! ./internal-types */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/internal-types.js\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/utils.js\");\n/** Hapi instrumentation for OpenTelemetry */\nclass HapiInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, config);\n    }\n    init() {\n        return new instrumentation_1.InstrumentationNodeModuleDefinition(internal_types_1.HapiComponentName, ['>=17.0.0 <22'], (module) => {\n            const moduleExports = module[Symbol.toStringTag] === 'Module' ? module.default : module;\n            if (!(0, instrumentation_1.isWrapped)(moduleExports.server)) {\n                this._wrap(moduleExports, 'server', this._getServerPatch.bind(this));\n            }\n            if (!(0, instrumentation_1.isWrapped)(moduleExports.Server)) {\n                this._wrap(moduleExports, 'Server', this._getServerPatch.bind(this));\n            }\n            return moduleExports;\n        }, (module) => {\n            const moduleExports = module[Symbol.toStringTag] === 'Module' ? module.default : module;\n            this._massUnwrap([moduleExports], ['server', 'Server']);\n        });\n    }\n    /**\n     * Patches the Hapi.server and Hapi.Server functions in order to instrument\n     * the server.route, server.ext, and server.register functions via calls to the\n     * @function _getServerRoutePatch, @function _getServerExtPatch, and\n     * @function _getServerRegisterPatch functions\n     * @param original - the original Hapi Server creation function\n     */\n    _getServerPatch(original) {\n        const instrumentation = this;\n        const self = this;\n        return function server(opts) {\n            const newServer = original.apply(this, [opts]);\n            self._wrap(newServer, 'route', originalRouter => {\n                return instrumentation._getServerRoutePatch.bind(instrumentation)(originalRouter);\n            });\n            // Casting as any is necessary here due to multiple overloads on the Hapi.ext\n            // function, which requires supporting a variety of different parameters\n            // as extension inputs\n            self._wrap(newServer, 'ext', originalExtHandler => {\n                return instrumentation._getServerExtPatch.bind(instrumentation)(\n                // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                originalExtHandler);\n            });\n            // Casting as any is necessary here due to multiple overloads on the Hapi.Server.register\n            // function, which requires supporting a variety of different types of Plugin inputs\n            self._wrap(newServer, 'register', \n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            instrumentation._getServerRegisterPatch.bind(instrumentation));\n            return newServer;\n        };\n    }\n    /**\n     * Patches the plugin register function used by the Hapi Server. This function\n     * goes through each plugin that is being registered and adds instrumentation\n     * via a call to the @function _wrapRegisterHandler function.\n     * @param {RegisterFunction<T>} original - the original register function which\n     * registers each plugin on the server\n     */\n    _getServerRegisterPatch(original) {\n        const instrumentation = this;\n        return function register(pluginInput, options) {\n            if (Array.isArray(pluginInput)) {\n                for (const pluginObj of pluginInput) {\n                    const plugin = (0, utils_1.getPluginFromInput)(pluginObj);\n                    instrumentation._wrapRegisterHandler(plugin);\n                }\n            }\n            else {\n                const plugin = (0, utils_1.getPluginFromInput)(pluginInput);\n                instrumentation._wrapRegisterHandler(plugin);\n            }\n            return original.apply(this, [pluginInput, options]);\n        };\n    }\n    /**\n     * Patches the Server.ext function which adds extension methods to the specified\n     * point along the request lifecycle. This function accepts the full range of\n     * accepted input into the standard Hapi `server.ext` function. For each extension,\n     * it adds instrumentation to the handler via a call to the @function _wrapExtMethods\n     * function.\n     * @param original - the original ext function which adds the extension method to the server\n     * @param {string} [pluginName] - if present, represents the name of the plugin responsible\n     * for adding this server extension. Else, signifies that the extension was added directly\n     */\n    _getServerExtPatch(original, pluginName) {\n        const instrumentation = this;\n        return function ext(...args) {\n            if (Array.isArray(args[0])) {\n                const eventsList = args[0];\n                for (let i = 0; i < eventsList.length; i++) {\n                    const eventObj = eventsList[i];\n                    if ((0, utils_1.isLifecycleExtType)(eventObj.type)) {\n                        const lifecycleEventObj = eventObj;\n                        const handler = instrumentation._wrapExtMethods(lifecycleEventObj.method, eventObj.type, pluginName);\n                        lifecycleEventObj.method = handler;\n                        eventsList[i] = lifecycleEventObj;\n                    }\n                }\n                return original.apply(this, args);\n            }\n            else if ((0, utils_1.isDirectExtInput)(args)) {\n                const extInput = args;\n                const method = extInput[1];\n                const handler = instrumentation._wrapExtMethods(method, extInput[0], pluginName);\n                return original.apply(this, [extInput[0], handler, extInput[2]]);\n            }\n            else if ((0, utils_1.isLifecycleExtEventObj)(args[0])) {\n                const lifecycleEventObj = args[0];\n                const handler = instrumentation._wrapExtMethods(lifecycleEventObj.method, lifecycleEventObj.type, pluginName);\n                lifecycleEventObj.method = handler;\n                return original.call(this, lifecycleEventObj);\n            }\n            return original.apply(this, args);\n        };\n    }\n    /**\n     * Patches the Server.route function. This function accepts either one or an array\n     * of Hapi.ServerRoute objects and adds instrumentation on each route via a call to\n     * the @function _wrapRouteHandler function.\n     * @param {HapiServerRouteInputMethod} original - the original route function which adds\n     * the route to the server\n     * @param {string} [pluginName] - if present, represents the name of the plugin responsible\n     * for adding this server route. Else, signifies that the route was added directly\n     */\n    _getServerRoutePatch(original, pluginName) {\n        const instrumentation = this;\n        return function route(route) {\n            if (Array.isArray(route)) {\n                for (let i = 0; i < route.length; i++) {\n                    const newRoute = instrumentation._wrapRouteHandler.call(instrumentation, route[i], pluginName);\n                    route[i] = newRoute;\n                }\n            }\n            else {\n                route = instrumentation._wrapRouteHandler.call(instrumentation, route, pluginName);\n            }\n            return original.apply(this, [route]);\n        };\n    }\n    /**\n     * Wraps newly registered plugins to add instrumentation to the plugin's clone of\n     * the original server. Specifically, wraps the server.route and server.ext functions\n     * via calls to @function _getServerRoutePatch and @function _getServerExtPatch\n     * @param {Hapi.Plugin<T>} plugin - the new plugin which is being instrumented\n     */\n    _wrapRegisterHandler(plugin) {\n        const instrumentation = this;\n        const pluginName = (0, utils_1.getPluginName)(plugin);\n        const oldRegister = plugin.register;\n        const self = this;\n        const newRegisterHandler = function (server, options) {\n            self._wrap(server, 'route', original => {\n                return instrumentation._getServerRoutePatch.bind(instrumentation)(original, pluginName);\n            });\n            // Casting as any is necessary here due to multiple overloads on the Hapi.ext\n            // function, which requires supporting a variety of different parameters\n            // as extension inputs\n            self._wrap(server, 'ext', originalExtHandler => {\n                return instrumentation._getServerExtPatch.bind(instrumentation)(\n                // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                originalExtHandler, pluginName);\n            });\n            return oldRegister.call(this, server, options);\n        };\n        plugin.register = newRegisterHandler;\n    }\n    /**\n     * Wraps request extension methods to add instrumentation to each new extension handler.\n     * Patches each individual extension in order to create the\n     * span and propagate context. It does not create spans when there is no parent span.\n     * @param {PatchableExtMethod | PatchableExtMethod[]} method - the request extension\n     * handler which is being instrumented\n     * @param {Hapi.ServerRequestExtType} extPoint - the point in the Hapi request lifecycle\n     * which this extension targets\n     * @param {string} [pluginName] - if present, represents the name of the plugin responsible\n     * for adding this server route. Else, signifies that the route was added directly\n     */\n    _wrapExtMethods(method, extPoint, pluginName) {\n        const instrumentation = this;\n        if (method instanceof Array) {\n            for (let i = 0; i < method.length; i++) {\n                method[i] = instrumentation._wrapExtMethods(method[i], extPoint);\n            }\n            return method;\n        }\n        else if ((0, utils_1.isPatchableExtMethod)(method)) {\n            if (method[internal_types_1.handlerPatched] === true)\n                return method;\n            method[internal_types_1.handlerPatched] = true;\n            const newHandler = async function (...params) {\n                if (api.trace.getSpan(api.context.active()) === undefined) {\n                    return await method.apply(this, params);\n                }\n                const metadata = (0, utils_1.getExtMetadata)(extPoint, pluginName);\n                const span = instrumentation.tracer.startSpan(metadata.name, {\n                    attributes: metadata.attributes,\n                });\n                try {\n                    return await api.context.with(api.trace.setSpan(api.context.active(), span), method, undefined, ...params);\n                }\n                catch (err) {\n                    span.recordException(err);\n                    span.setStatus({\n                        code: api.SpanStatusCode.ERROR,\n                        message: err.message,\n                    });\n                    throw err;\n                }\n                finally {\n                    span.end();\n                }\n            };\n            return newHandler;\n        }\n        return method;\n    }\n    /**\n     * Patches each individual route handler method in order to create the\n     * span and propagate context. It does not create spans when there is no parent span.\n     * @param {PatchableServerRoute} route - the route handler which is being instrumented\n     * @param {string} [pluginName] - if present, represents the name of the plugin responsible\n     * for adding this server route. Else, signifies that the route was added directly\n     */\n    _wrapRouteHandler(route, pluginName) {\n        var _a;\n        const instrumentation = this;\n        if (route[internal_types_1.handlerPatched] === true)\n            return route;\n        route[internal_types_1.handlerPatched] = true;\n        const wrapHandler = oldHandler => {\n            return async function (...params) {\n                if (api.trace.getSpan(api.context.active()) === undefined) {\n                    return await oldHandler.call(this, ...params);\n                }\n                const rpcMetadata = (0, core_1.getRPCMetadata)(api.context.active());\n                if ((rpcMetadata === null || rpcMetadata === void 0 ? void 0 : rpcMetadata.type) === core_1.RPCType.HTTP) {\n                    rpcMetadata.route = route.path;\n                }\n                const metadata = (0, utils_1.getRouteMetadata)(route, pluginName);\n                const span = instrumentation.tracer.startSpan(metadata.name, {\n                    attributes: metadata.attributes,\n                });\n                try {\n                    return await api.context.with(api.trace.setSpan(api.context.active(), span), () => oldHandler.call(this, ...params));\n                }\n                catch (err) {\n                    span.recordException(err);\n                    span.setStatus({\n                        code: api.SpanStatusCode.ERROR,\n                        message: err.message,\n                    });\n                    throw err;\n                }\n                finally {\n                    span.end();\n                }\n            };\n        };\n        if (typeof route.handler === 'function') {\n            route.handler = wrapHandler(route.handler);\n        }\n        else if (typeof route.options === 'function') {\n            const oldOptions = route.options;\n            route.options = function (server) {\n                const options = oldOptions(server);\n                if (typeof options.handler === 'function') {\n                    options.handler = wrapHandler(options.handler);\n                }\n                return options;\n            };\n        }\n        else if (typeof ((_a = route.options) === null || _a === void 0 ? void 0 : _a.handler) === 'function') {\n            route.options.handler = wrapHandler(route.options.handler);\n        }\n        return route;\n    }\n}\nexports.HapiInstrumentation = HapiInstrumentation;\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/instrumentation.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/internal-types.js":
/*!**************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/internal-types.js ***!
  \**************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.HapiLifecycleMethodNames = exports.HapiLayerType = exports.handlerPatched = exports.HapiComponentName = void 0;\nexports.HapiComponentName = '@hapi/hapi';\n/**\n * This symbol is used to mark a Hapi route handler or server extension handler as\n * already patched, since its possible to use these handlers multiple times\n * i.e. when allowing multiple versions of one plugin, or when registering a plugin\n * multiple times on different servers.\n */\nexports.handlerPatched = Symbol('hapi-handler-patched');\nexports.HapiLayerType = {\n    ROUTER: 'router',\n    PLUGIN: 'plugin',\n    EXT: 'server.ext',\n};\nexports.HapiLifecycleMethodNames = new Set([\n    'onPreAuth',\n    'onCredentials',\n    'onPostAuth',\n    'onPreHandler',\n    'onPostHandler',\n    'onPreResponse',\n    'onRequest',\n]);\n//# sourceMappingURL=internal-types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF82NGQxNmJiZjU2OTE0MDQ0OTE4ZmUyYzhiMDAxNWI5MC9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLWhhcGkvYnVpbGQvc3JjL2ludGVybmFsLXR5cGVzLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELGdDQUFnQyxHQUFHLHFCQUFxQixHQUFHLHNCQUFzQixHQUFHLHlCQUF5QjtBQUM3Ryx5QkFBeUI7QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCO0FBQ3RCLHFCQUFxQjtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdDQUFnQztBQUNoQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYW53YXJcXERvY3VtZW50c1xcQ3ViZW50d2ViXFxjdWJlbnRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF82NGQxNmJiZjU2OTE0MDQ0OTE4ZmUyYzhiMDAxNWI5MFxcbm9kZV9tb2R1bGVzXFxAb3BlbnRlbGVtZXRyeVxcaW5zdHJ1bWVudGF0aW9uLWhhcGlcXGJ1aWxkXFxzcmNcXGludGVybmFsLXR5cGVzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuLypcbiAqIENvcHlyaWdodCBUaGUgT3BlblRlbGVtZXRyeSBBdXRob3JzXG4gKlxuICogTGljZW5zZWQgdW5kZXIgdGhlIEFwYWNoZSBMaWNlbnNlLCBWZXJzaW9uIDIuMCAodGhlIFwiTGljZW5zZVwiKTtcbiAqIHlvdSBtYXkgbm90IHVzZSB0aGlzIGZpbGUgZXhjZXB0IGluIGNvbXBsaWFuY2Ugd2l0aCB0aGUgTGljZW5zZS5cbiAqIFlvdSBtYXkgb2J0YWluIGEgY29weSBvZiB0aGUgTGljZW5zZSBhdFxuICpcbiAqICAgICAgaHR0cHM6Ly93d3cuYXBhY2hlLm9yZy9saWNlbnNlcy9MSUNFTlNFLTIuMFxuICpcbiAqIFVubGVzcyByZXF1aXJlZCBieSBhcHBsaWNhYmxlIGxhdyBvciBhZ3JlZWQgdG8gaW4gd3JpdGluZywgc29mdHdhcmVcbiAqIGRpc3RyaWJ1dGVkIHVuZGVyIHRoZSBMaWNlbnNlIGlzIGRpc3RyaWJ1dGVkIG9uIGFuIFwiQVMgSVNcIiBCQVNJUyxcbiAqIFdJVEhPVVQgV0FSUkFOVElFUyBPUiBDT05ESVRJT05TIE9GIEFOWSBLSU5ELCBlaXRoZXIgZXhwcmVzcyBvciBpbXBsaWVkLlxuICogU2VlIHRoZSBMaWNlbnNlIGZvciB0aGUgc3BlY2lmaWMgbGFuZ3VhZ2UgZ292ZXJuaW5nIHBlcm1pc3Npb25zIGFuZFxuICogbGltaXRhdGlvbnMgdW5kZXIgdGhlIExpY2Vuc2UuXG4gKi9cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuSGFwaUxpZmVjeWNsZU1ldGhvZE5hbWVzID0gZXhwb3J0cy5IYXBpTGF5ZXJUeXBlID0gZXhwb3J0cy5oYW5kbGVyUGF0Y2hlZCA9IGV4cG9ydHMuSGFwaUNvbXBvbmVudE5hbWUgPSB2b2lkIDA7XG5leHBvcnRzLkhhcGlDb21wb25lbnROYW1lID0gJ0BoYXBpL2hhcGknO1xuLyoqXG4gKiBUaGlzIHN5bWJvbCBpcyB1c2VkIHRvIG1hcmsgYSBIYXBpIHJvdXRlIGhhbmRsZXIgb3Igc2VydmVyIGV4dGVuc2lvbiBoYW5kbGVyIGFzXG4gKiBhbHJlYWR5IHBhdGNoZWQsIHNpbmNlIGl0cyBwb3NzaWJsZSB0byB1c2UgdGhlc2UgaGFuZGxlcnMgbXVsdGlwbGUgdGltZXNcbiAqIGkuZS4gd2hlbiBhbGxvd2luZyBtdWx0aXBsZSB2ZXJzaW9ucyBvZiBvbmUgcGx1Z2luLCBvciB3aGVuIHJlZ2lzdGVyaW5nIGEgcGx1Z2luXG4gKiBtdWx0aXBsZSB0aW1lcyBvbiBkaWZmZXJlbnQgc2VydmVycy5cbiAqL1xuZXhwb3J0cy5oYW5kbGVyUGF0Y2hlZCA9IFN5bWJvbCgnaGFwaS1oYW5kbGVyLXBhdGNoZWQnKTtcbmV4cG9ydHMuSGFwaUxheWVyVHlwZSA9IHtcbiAgICBST1VURVI6ICdyb3V0ZXInLFxuICAgIFBMVUdJTjogJ3BsdWdpbicsXG4gICAgRVhUOiAnc2VydmVyLmV4dCcsXG59O1xuZXhwb3J0cy5IYXBpTGlmZWN5Y2xlTWV0aG9kTmFtZXMgPSBuZXcgU2V0KFtcbiAgICAnb25QcmVBdXRoJyxcbiAgICAnb25DcmVkZW50aWFscycsXG4gICAgJ29uUG9zdEF1dGgnLFxuICAgICdvblByZUhhbmRsZXInLFxuICAgICdvblBvc3RIYW5kbGVyJyxcbiAgICAnb25QcmVSZXNwb25zZScsXG4gICAgJ29uUmVxdWVzdCcsXG5dKTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWludGVybmFsLXR5cGVzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/internal-types.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/utils.js":
/*!*****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/utils.js ***!
  \*****************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getPluginFromInput = exports.getExtMetadata = exports.getRouteMetadata = exports.isPatchableExtMethod = exports.isDirectExtInput = exports.isLifecycleExtEventObj = exports.isLifecycleExtType = exports.getPluginName = void 0;\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst internal_types_1 = __webpack_require__(/*! ./internal-types */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/internal-types.js\");\nconst AttributeNames_1 = __webpack_require__(/*! ./enums/AttributeNames */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/enums/AttributeNames.js\");\nfunction getPluginName(plugin) {\n    if (plugin.name) {\n        return plugin.name;\n    }\n    else {\n        return plugin.pkg.name;\n    }\n}\nexports.getPluginName = getPluginName;\nconst isLifecycleExtType = (variableToCheck) => {\n    return (typeof variableToCheck === 'string' &&\n        internal_types_1.HapiLifecycleMethodNames.has(variableToCheck));\n};\nexports.isLifecycleExtType = isLifecycleExtType;\nconst isLifecycleExtEventObj = (variableToCheck) => {\n    var _a;\n    const event = (_a = variableToCheck) === null || _a === void 0 ? void 0 : _a.type;\n    return event !== undefined && (0, exports.isLifecycleExtType)(event);\n};\nexports.isLifecycleExtEventObj = isLifecycleExtEventObj;\nconst isDirectExtInput = (variableToCheck) => {\n    return (Array.isArray(variableToCheck) &&\n        variableToCheck.length <= 3 &&\n        (0, exports.isLifecycleExtType)(variableToCheck[0]) &&\n        typeof variableToCheck[1] === 'function');\n};\nexports.isDirectExtInput = isDirectExtInput;\nconst isPatchableExtMethod = (variableToCheck) => {\n    return !Array.isArray(variableToCheck);\n};\nexports.isPatchableExtMethod = isPatchableExtMethod;\nconst getRouteMetadata = (route, pluginName) => {\n    if (pluginName) {\n        return {\n            attributes: {\n                [semantic_conventions_1.SEMATTRS_HTTP_ROUTE]: route.path,\n                [semantic_conventions_1.SEMATTRS_HTTP_METHOD]: route.method,\n                [AttributeNames_1.AttributeNames.HAPI_TYPE]: internal_types_1.HapiLayerType.PLUGIN,\n                [AttributeNames_1.AttributeNames.PLUGIN_NAME]: pluginName,\n            },\n            name: `${pluginName}: route - ${route.path}`,\n        };\n    }\n    return {\n        attributes: {\n            [semantic_conventions_1.SEMATTRS_HTTP_ROUTE]: route.path,\n            [semantic_conventions_1.SEMATTRS_HTTP_METHOD]: route.method,\n            [AttributeNames_1.AttributeNames.HAPI_TYPE]: internal_types_1.HapiLayerType.ROUTER,\n        },\n        name: `route - ${route.path}`,\n    };\n};\nexports.getRouteMetadata = getRouteMetadata;\nconst getExtMetadata = (extPoint, pluginName) => {\n    if (pluginName) {\n        return {\n            attributes: {\n                [AttributeNames_1.AttributeNames.EXT_TYPE]: extPoint,\n                [AttributeNames_1.AttributeNames.HAPI_TYPE]: internal_types_1.HapiLayerType.EXT,\n                [AttributeNames_1.AttributeNames.PLUGIN_NAME]: pluginName,\n            },\n            name: `${pluginName}: ext - ${extPoint}`,\n        };\n    }\n    return {\n        attributes: {\n            [AttributeNames_1.AttributeNames.EXT_TYPE]: extPoint,\n            [AttributeNames_1.AttributeNames.HAPI_TYPE]: internal_types_1.HapiLayerType.EXT,\n        },\n        name: `ext - ${extPoint}`,\n    };\n};\nexports.getExtMetadata = getExtMetadata;\nconst getPluginFromInput = (pluginObj) => {\n    if ('plugin' in pluginObj) {\n        if ('plugin' in pluginObj.plugin) {\n            return pluginObj.plugin.plugin;\n        }\n        return pluginObj.plugin;\n    }\n    return pluginObj;\n};\nexports.getPluginFromInput = getPluginFromInput;\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/utils.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/version.js":
/*!*******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/version.js ***!
  \*******************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.45.2';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-hapi';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_64d16bbf56914044918fe2c8b0015b90/node_modules/@opentelemetry/instrumentation-hapi/build/src/version.js\n");

/***/ })

};
;