"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90";
exports.ids = ["vendor-chunks/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90"];
exports.modules = {

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/AttributeNames.js":
/*!***************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/AttributeNames.js ***!
  \***************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.AttributeNames = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// Mysql specific attributes not covered by semantic conventions\nvar AttributeNames;\n(function (AttributeNames) {\n    AttributeNames[\"MYSQL_VALUES\"] = \"db.mysql.values\";\n})(AttributeNames = exports.AttributeNames || (exports.AttributeNames = {}));\n//# sourceMappingURL=AttributeNames.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/AttributeNames.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/index.js":
/*!******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/index.js ***!
  \******************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/instrumentation.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/types.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/index.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/instrumentation.js":
/*!****************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/instrumentation.js ***!
  \****************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.MySQLInstrumentation = void 0;\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst AttributeNames_1 = __webpack_require__(/*! ./AttributeNames */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/AttributeNames.js\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/utils.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/version.js\");\nclass MySQLInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, config);\n        this._setMetricInstruments();\n    }\n    setMeterProvider(meterProvider) {\n        super.setMeterProvider(meterProvider);\n        this._setMetricInstruments();\n    }\n    _setMetricInstruments() {\n        this._connectionsUsage = this.meter.createUpDownCounter('db.client.connections.usage', //TODO:: use semantic convention\n        {\n            description: 'The number of connections that are currently in state described by the state attribute.',\n            unit: '{connection}',\n        });\n    }\n    init() {\n        return [\n            new instrumentation_1.InstrumentationNodeModuleDefinition('mysql', ['>=2.0.0 <3'], (moduleExports) => {\n                if ((0, instrumentation_1.isWrapped)(moduleExports.createConnection)) {\n                    this._unwrap(moduleExports, 'createConnection');\n                }\n                this._wrap(moduleExports, 'createConnection', this._patchCreateConnection());\n                if ((0, instrumentation_1.isWrapped)(moduleExports.createPool)) {\n                    this._unwrap(moduleExports, 'createPool');\n                }\n                this._wrap(moduleExports, 'createPool', this._patchCreatePool());\n                if ((0, instrumentation_1.isWrapped)(moduleExports.createPoolCluster)) {\n                    this._unwrap(moduleExports, 'createPoolCluster');\n                }\n                this._wrap(moduleExports, 'createPoolCluster', this._patchCreatePoolCluster());\n                return moduleExports;\n            }, (moduleExports) => {\n                if (moduleExports === undefined)\n                    return;\n                this._unwrap(moduleExports, 'createConnection');\n                this._unwrap(moduleExports, 'createPool');\n                this._unwrap(moduleExports, 'createPoolCluster');\n            }),\n        ];\n    }\n    // global export function\n    _patchCreateConnection() {\n        return (originalCreateConnection) => {\n            const thisPlugin = this;\n            return function createConnection(_connectionUri) {\n                const originalResult = originalCreateConnection(...arguments);\n                // This is unwrapped on next call after unpatch\n                thisPlugin._wrap(originalResult, 'query', thisPlugin._patchQuery(originalResult));\n                return originalResult;\n            };\n        };\n    }\n    // global export function\n    _patchCreatePool() {\n        return (originalCreatePool) => {\n            const thisPlugin = this;\n            return function createPool(_config) {\n                const pool = originalCreatePool(...arguments);\n                thisPlugin._wrap(pool, 'query', thisPlugin._patchQuery(pool));\n                thisPlugin._wrap(pool, 'getConnection', thisPlugin._patchGetConnection(pool));\n                thisPlugin._wrap(pool, 'end', thisPlugin._patchPoolEnd(pool));\n                thisPlugin._setPoolcallbacks(pool, thisPlugin, '');\n                return pool;\n            };\n        };\n    }\n    _patchPoolEnd(pool) {\n        return (originalPoolEnd) => {\n            const thisPlugin = this;\n            return function end(callback) {\n                const nAll = pool._allConnections.length;\n                const nFree = pool._freeConnections.length;\n                const nUsed = nAll - nFree;\n                const poolName = (0, utils_1.getPoolName)(pool);\n                thisPlugin._connectionsUsage.add(-nUsed, {\n                    state: 'used',\n                    name: poolName,\n                });\n                thisPlugin._connectionsUsage.add(-nFree, {\n                    state: 'idle',\n                    name: poolName,\n                });\n                originalPoolEnd.apply(pool, arguments);\n            };\n        };\n    }\n    // global export function\n    _patchCreatePoolCluster() {\n        return (originalCreatePoolCluster) => {\n            const thisPlugin = this;\n            return function createPool(_config) {\n                const cluster = originalCreatePoolCluster(...arguments);\n                // This is unwrapped on next call after unpatch\n                thisPlugin._wrap(cluster, 'getConnection', thisPlugin._patchGetConnection(cluster));\n                thisPlugin._wrap(cluster, 'add', thisPlugin._patchAdd(cluster));\n                return cluster;\n            };\n        };\n    }\n    _patchAdd(cluster) {\n        return (originalAdd) => {\n            const thisPlugin = this;\n            return function add(id, config) {\n                // Unwrap if unpatch has been called\n                if (!thisPlugin['_enabled']) {\n                    thisPlugin._unwrap(cluster, 'add');\n                    return originalAdd.apply(cluster, arguments);\n                }\n                originalAdd.apply(cluster, arguments);\n                const nodes = cluster['_nodes'];\n                if (nodes) {\n                    const nodeId = typeof id === 'object'\n                        ? 'CLUSTER::' + cluster._lastId\n                        : String(id);\n                    const pool = nodes[nodeId].pool;\n                    thisPlugin._setPoolcallbacks(pool, thisPlugin, id);\n                }\n            };\n        };\n    }\n    // method on cluster or pool\n    _patchGetConnection(pool) {\n        return (originalGetConnection) => {\n            const thisPlugin = this;\n            return function getConnection(arg1, arg2, arg3) {\n                // Unwrap if unpatch has been called\n                if (!thisPlugin['_enabled']) {\n                    thisPlugin._unwrap(pool, 'getConnection');\n                    return originalGetConnection.apply(pool, arguments);\n                }\n                if (arguments.length === 1 && typeof arg1 === 'function') {\n                    const patchFn = thisPlugin._getConnectionCallbackPatchFn(arg1);\n                    return originalGetConnection.call(pool, patchFn);\n                }\n                if (arguments.length === 2 && typeof arg2 === 'function') {\n                    const patchFn = thisPlugin._getConnectionCallbackPatchFn(arg2);\n                    return originalGetConnection.call(pool, arg1, patchFn);\n                }\n                if (arguments.length === 3 && typeof arg3 === 'function') {\n                    const patchFn = thisPlugin._getConnectionCallbackPatchFn(arg3);\n                    return originalGetConnection.call(pool, arg1, arg2, patchFn);\n                }\n                return originalGetConnection.apply(pool, arguments);\n            };\n        };\n    }\n    _getConnectionCallbackPatchFn(cb) {\n        const thisPlugin = this;\n        const activeContext = api_1.context.active();\n        return function (err, connection) {\n            if (connection) {\n                // this is the callback passed into a query\n                // no need to unwrap\n                if (!(0, instrumentation_1.isWrapped)(connection.query)) {\n                    thisPlugin._wrap(connection, 'query', thisPlugin._patchQuery(connection));\n                }\n            }\n            if (typeof cb === 'function') {\n                api_1.context.with(activeContext, cb, this, err, connection);\n            }\n        };\n    }\n    _patchQuery(connection) {\n        return (originalQuery) => {\n            const thisPlugin = this;\n            return function query(query, _valuesOrCallback, _callback) {\n                if (!thisPlugin['_enabled']) {\n                    thisPlugin._unwrap(connection, 'query');\n                    return originalQuery.apply(connection, arguments);\n                }\n                const span = thisPlugin.tracer.startSpan((0, utils_1.getSpanName)(query), {\n                    kind: api_1.SpanKind.CLIENT,\n                    attributes: Object.assign(Object.assign({}, MySQLInstrumentation.COMMON_ATTRIBUTES), (0, utils_1.getConnectionAttributes)(connection.config)),\n                });\n                span.setAttribute(semantic_conventions_1.SEMATTRS_DB_STATEMENT, (0, utils_1.getDbStatement)(query));\n                if (thisPlugin.getConfig().enhancedDatabaseReporting) {\n                    let values;\n                    if (Array.isArray(_valuesOrCallback)) {\n                        values = _valuesOrCallback;\n                    }\n                    else if (arguments[2]) {\n                        values = [_valuesOrCallback];\n                    }\n                    span.setAttribute(AttributeNames_1.AttributeNames.MYSQL_VALUES, (0, utils_1.getDbValues)(query, values));\n                }\n                const cbIndex = Array.from(arguments).findIndex(arg => typeof arg === 'function');\n                const parentContext = api_1.context.active();\n                if (cbIndex === -1) {\n                    const streamableQuery = api_1.context.with(api_1.trace.setSpan(api_1.context.active(), span), () => {\n                        return originalQuery.apply(connection, arguments);\n                    });\n                    api_1.context.bind(parentContext, streamableQuery);\n                    return streamableQuery\n                        .on('error', err => span.setStatus({\n                        code: api_1.SpanStatusCode.ERROR,\n                        message: err.message,\n                    }))\n                        .on('end', () => {\n                        span.end();\n                    });\n                }\n                else {\n                    thisPlugin._wrap(arguments, cbIndex, thisPlugin._patchCallbackQuery(span, parentContext));\n                    return api_1.context.with(api_1.trace.setSpan(api_1.context.active(), span), () => {\n                        return originalQuery.apply(connection, arguments);\n                    });\n                }\n            };\n        };\n    }\n    _patchCallbackQuery(span, parentContext) {\n        return (originalCallback) => {\n            return function (err, results, fields) {\n                if (err) {\n                    span.setStatus({\n                        code: api_1.SpanStatusCode.ERROR,\n                        message: err.message,\n                    });\n                }\n                span.end();\n                return api_1.context.with(parentContext, () => originalCallback(...arguments));\n            };\n        };\n    }\n    _setPoolcallbacks(pool, thisPlugin, id) {\n        //TODO:: use semantic convention\n        const poolName = id || (0, utils_1.getPoolName)(pool);\n        pool.on('connection', connection => {\n            thisPlugin._connectionsUsage.add(1, {\n                state: 'idle',\n                name: poolName,\n            });\n        });\n        pool.on('acquire', connection => {\n            thisPlugin._connectionsUsage.add(-1, {\n                state: 'idle',\n                name: poolName,\n            });\n            thisPlugin._connectionsUsage.add(1, {\n                state: 'used',\n                name: poolName,\n            });\n        });\n        pool.on('release', connection => {\n            thisPlugin._connectionsUsage.add(-1, {\n                state: 'used',\n                name: poolName,\n            });\n            thisPlugin._connectionsUsage.add(1, {\n                state: 'idle',\n                name: poolName,\n            });\n        });\n    }\n}\nexports.MySQLInstrumentation = MySQLInstrumentation;\nMySQLInstrumentation.COMMON_ATTRIBUTES = {\n    [semantic_conventions_1.SEMATTRS_DB_SYSTEM]: semantic_conventions_1.DBSYSTEMVALUES_MYSQL,\n};\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/instrumentation.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/types.js":
/*!******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/types.js ***!
  \******************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9Ab3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfYWI2OWU0YzU1NmRmZjM0OTQyMDY1Njk1ZjA5YjlhOTAvbm9kZV9tb2R1bGVzL0BvcGVudGVsZW1ldHJ5L2luc3RydW1lbnRhdGlvbi1teXNxbC9idWlsZC9zcmMvdHlwZXMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYW53YXJcXERvY3VtZW50c1xcQ3ViZW50d2ViXFxjdWJlbnRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF9hYjY5ZTRjNTU2ZGZmMzQ5NDIwNjU2OTVmMDliOWE5MFxcbm9kZV9tb2R1bGVzXFxAb3BlbnRlbGVtZXRyeVxcaW5zdHJ1bWVudGF0aW9uLW15c3FsXFxidWlsZFxcc3JjXFx0eXBlcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbi8qXG4gKiBDb3B5cmlnaHQgVGhlIE9wZW5UZWxlbWV0cnkgQXV0aG9yc1xuICpcbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4gKiB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuXG4gKiBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbiAqXG4gKiAgICAgIGh0dHBzOi8vd3d3LmFwYWNoZS5vcmcvbGljZW5zZXMvTElDRU5TRS0yLjBcbiAqXG4gKiBVbmxlc3MgcmVxdWlyZWQgYnkgYXBwbGljYWJsZSBsYXcgb3IgYWdyZWVkIHRvIGluIHdyaXRpbmcsIHNvZnR3YXJlXG4gKiBkaXN0cmlidXRlZCB1bmRlciB0aGUgTGljZW5zZSBpcyBkaXN0cmlidXRlZCBvbiBhbiBcIkFTIElTXCIgQkFTSVMsXG4gKiBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgQ09ORElUSU9OUyBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC5cbiAqIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmRcbiAqIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuICovXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD10eXBlcy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/types.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/utils.js":
/*!******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/utils.js ***!
  \******************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getPoolName = exports.arrayStringifyHelper = exports.getSpanName = exports.getDbValues = exports.getDbStatement = exports.getConnectionAttributes = void 0;\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\n/**\n * Get an Attributes map from a mysql connection config object\n *\n * @param config ConnectionConfig\n */\nfunction getConnectionAttributes(config) {\n    const { host, port, database, user } = getConfig(config);\n    const portNumber = parseInt(port, 10);\n    if (!isNaN(portNumber)) {\n        return {\n            [semantic_conventions_1.SEMATTRS_NET_PEER_NAME]: host,\n            [semantic_conventions_1.SEMATTRS_NET_PEER_PORT]: portNumber,\n            [semantic_conventions_1.SEMATTRS_DB_CONNECTION_STRING]: getJDBCString(host, port, database),\n            [semantic_conventions_1.SEMATTRS_DB_NAME]: database,\n            [semantic_conventions_1.SEMATTRS_DB_USER]: user,\n        };\n    }\n    return {\n        [semantic_conventions_1.SEMATTRS_NET_PEER_NAME]: host,\n        [semantic_conventions_1.SEMATTRS_DB_CONNECTION_STRING]: getJDBCString(host, port, database),\n        [semantic_conventions_1.SEMATTRS_DB_NAME]: database,\n        [semantic_conventions_1.SEMATTRS_DB_USER]: user,\n    };\n}\nexports.getConnectionAttributes = getConnectionAttributes;\nfunction getConfig(config) {\n    const { host, port, database, user } = (config && config.connectionConfig) || config || {};\n    return { host, port, database, user };\n}\nfunction getJDBCString(host, port, database) {\n    let jdbcString = `jdbc:mysql://${host || 'localhost'}`;\n    if (typeof port === 'number') {\n        jdbcString += `:${port}`;\n    }\n    if (typeof database === 'string') {\n        jdbcString += `/${database}`;\n    }\n    return jdbcString;\n}\n/**\n * @returns the database statement being executed.\n */\nfunction getDbStatement(query) {\n    if (typeof query === 'string') {\n        return query;\n    }\n    else {\n        return query.sql;\n    }\n}\nexports.getDbStatement = getDbStatement;\nfunction getDbValues(query, values) {\n    if (typeof query === 'string') {\n        return arrayStringifyHelper(values);\n    }\n    else {\n        // According to https://github.com/mysqljs/mysql#performing-queries\n        // The values argument will override the values in the option object.\n        return arrayStringifyHelper(values || query.values);\n    }\n}\nexports.getDbValues = getDbValues;\n/**\n * The span name SHOULD be set to a low cardinality value\n * representing the statement executed on the database.\n *\n * @returns SQL statement without variable arguments or SQL verb\n */\nfunction getSpanName(query) {\n    const rawQuery = typeof query === 'object' ? query.sql : query;\n    // Extract the SQL verb\n    const firstSpace = rawQuery === null || rawQuery === void 0 ? void 0 : rawQuery.indexOf(' ');\n    if (typeof firstSpace === 'number' && firstSpace !== -1) {\n        return rawQuery === null || rawQuery === void 0 ? void 0 : rawQuery.substring(0, firstSpace);\n    }\n    return rawQuery;\n}\nexports.getSpanName = getSpanName;\nfunction arrayStringifyHelper(arr) {\n    if (arr)\n        return `[${arr.toString()}]`;\n    return '';\n}\nexports.arrayStringifyHelper = arrayStringifyHelper;\nfunction getPoolName(pool) {\n    const c = pool.config.connectionConfig;\n    let poolName = '';\n    poolName += c.host ? `host: '${c.host}', ` : '';\n    poolName += c.port ? `port: ${c.port}, ` : '';\n    poolName += c.database ? `database: '${c.database}', ` : '';\n    poolName += c.user ? `user: '${c.user}'` : '';\n    if (!c.user) {\n        poolName = poolName.substring(0, poolName.length - 2); //omit last comma\n    }\n    return poolName.trim();\n}\nexports.getPoolName = getPoolName;\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/utils.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/version.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/version.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.45.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-mysql';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/version.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/AttributeNames.js":
/*!***************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/AttributeNames.js ***!
  \***************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.AttributeNames = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// Mysql specific attributes not covered by semantic conventions\nvar AttributeNames;\n(function (AttributeNames) {\n    AttributeNames[\"MYSQL_VALUES\"] = \"db.mysql.values\";\n})(AttributeNames = exports.AttributeNames || (exports.AttributeNames = {}));\n//# sourceMappingURL=AttributeNames.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF9hYjY5ZTRjNTU2ZGZmMzQ5NDIwNjU2OTVmMDliOWE5MC9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLW15c3FsL2J1aWxkL3NyYy9BdHRyaWJ1dGVOYW1lcy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxzQkFBc0I7QUFDdEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLDhDQUE4QyxzQkFBc0IsS0FBSztBQUMxRSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbndhclxcRG9jdW1lbnRzXFxDdWJlbnR3ZWJcXGN1YmVudFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQG9wZW50ZWxlbWV0cnkraW5zdHJ1bWVudGF0X2FiNjllNGM1NTZkZmYzNDk0MjA2NTY5NWYwOWI5YTkwXFxub2RlX21vZHVsZXNcXEBvcGVudGVsZW1ldHJ5XFxpbnN0cnVtZW50YXRpb24tbXlzcWxcXGJ1aWxkXFxzcmNcXEF0dHJpYnV0ZU5hbWVzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5BdHRyaWJ1dGVOYW1lcyA9IHZvaWQgMDtcbi8qXG4gKiBDb3B5cmlnaHQgVGhlIE9wZW5UZWxlbWV0cnkgQXV0aG9yc1xuICpcbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4gKiB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuXG4gKiBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbiAqXG4gKiAgICAgIGh0dHBzOi8vd3d3LmFwYWNoZS5vcmcvbGljZW5zZXMvTElDRU5TRS0yLjBcbiAqXG4gKiBVbmxlc3MgcmVxdWlyZWQgYnkgYXBwbGljYWJsZSBsYXcgb3IgYWdyZWVkIHRvIGluIHdyaXRpbmcsIHNvZnR3YXJlXG4gKiBkaXN0cmlidXRlZCB1bmRlciB0aGUgTGljZW5zZSBpcyBkaXN0cmlidXRlZCBvbiBhbiBcIkFTIElTXCIgQkFTSVMsXG4gKiBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgQ09ORElUSU9OUyBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC5cbiAqIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmRcbiAqIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuICovXG4vLyBNeXNxbCBzcGVjaWZpYyBhdHRyaWJ1dGVzIG5vdCBjb3ZlcmVkIGJ5IHNlbWFudGljIGNvbnZlbnRpb25zXG52YXIgQXR0cmlidXRlTmFtZXM7XG4oZnVuY3Rpb24gKEF0dHJpYnV0ZU5hbWVzKSB7XG4gICAgQXR0cmlidXRlTmFtZXNbXCJNWVNRTF9WQUxVRVNcIl0gPSBcImRiLm15c3FsLnZhbHVlc1wiO1xufSkoQXR0cmlidXRlTmFtZXMgPSBleHBvcnRzLkF0dHJpYnV0ZU5hbWVzIHx8IChleHBvcnRzLkF0dHJpYnV0ZU5hbWVzID0ge30pKTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPUF0dHJpYnV0ZU5hbWVzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/AttributeNames.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/index.js":
/*!******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/index.js ***!
  \******************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/instrumentation.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/types.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/index.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/instrumentation.js":
/*!****************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/instrumentation.js ***!
  \****************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.MySQLInstrumentation = void 0;\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst AttributeNames_1 = __webpack_require__(/*! ./AttributeNames */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/AttributeNames.js\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/utils.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/version.js\");\nclass MySQLInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, config);\n        this._setMetricInstruments();\n    }\n    setMeterProvider(meterProvider) {\n        super.setMeterProvider(meterProvider);\n        this._setMetricInstruments();\n    }\n    _setMetricInstruments() {\n        this._connectionsUsage = this.meter.createUpDownCounter('db.client.connections.usage', //TODO:: use semantic convention\n        {\n            description: 'The number of connections that are currently in state described by the state attribute.',\n            unit: '{connection}',\n        });\n    }\n    init() {\n        return [\n            new instrumentation_1.InstrumentationNodeModuleDefinition('mysql', ['>=2.0.0 <3'], (moduleExports) => {\n                if ((0, instrumentation_1.isWrapped)(moduleExports.createConnection)) {\n                    this._unwrap(moduleExports, 'createConnection');\n                }\n                this._wrap(moduleExports, 'createConnection', this._patchCreateConnection());\n                if ((0, instrumentation_1.isWrapped)(moduleExports.createPool)) {\n                    this._unwrap(moduleExports, 'createPool');\n                }\n                this._wrap(moduleExports, 'createPool', this._patchCreatePool());\n                if ((0, instrumentation_1.isWrapped)(moduleExports.createPoolCluster)) {\n                    this._unwrap(moduleExports, 'createPoolCluster');\n                }\n                this._wrap(moduleExports, 'createPoolCluster', this._patchCreatePoolCluster());\n                return moduleExports;\n            }, (moduleExports) => {\n                if (moduleExports === undefined)\n                    return;\n                this._unwrap(moduleExports, 'createConnection');\n                this._unwrap(moduleExports, 'createPool');\n                this._unwrap(moduleExports, 'createPoolCluster');\n            }),\n        ];\n    }\n    // global export function\n    _patchCreateConnection() {\n        return (originalCreateConnection) => {\n            const thisPlugin = this;\n            return function createConnection(_connectionUri) {\n                const originalResult = originalCreateConnection(...arguments);\n                // This is unwrapped on next call after unpatch\n                thisPlugin._wrap(originalResult, 'query', thisPlugin._patchQuery(originalResult));\n                return originalResult;\n            };\n        };\n    }\n    // global export function\n    _patchCreatePool() {\n        return (originalCreatePool) => {\n            const thisPlugin = this;\n            return function createPool(_config) {\n                const pool = originalCreatePool(...arguments);\n                thisPlugin._wrap(pool, 'query', thisPlugin._patchQuery(pool));\n                thisPlugin._wrap(pool, 'getConnection', thisPlugin._patchGetConnection(pool));\n                thisPlugin._wrap(pool, 'end', thisPlugin._patchPoolEnd(pool));\n                thisPlugin._setPoolcallbacks(pool, thisPlugin, '');\n                return pool;\n            };\n        };\n    }\n    _patchPoolEnd(pool) {\n        return (originalPoolEnd) => {\n            const thisPlugin = this;\n            return function end(callback) {\n                const nAll = pool._allConnections.length;\n                const nFree = pool._freeConnections.length;\n                const nUsed = nAll - nFree;\n                const poolName = (0, utils_1.getPoolName)(pool);\n                thisPlugin._connectionsUsage.add(-nUsed, {\n                    state: 'used',\n                    name: poolName,\n                });\n                thisPlugin._connectionsUsage.add(-nFree, {\n                    state: 'idle',\n                    name: poolName,\n                });\n                originalPoolEnd.apply(pool, arguments);\n            };\n        };\n    }\n    // global export function\n    _patchCreatePoolCluster() {\n        return (originalCreatePoolCluster) => {\n            const thisPlugin = this;\n            return function createPool(_config) {\n                const cluster = originalCreatePoolCluster(...arguments);\n                // This is unwrapped on next call after unpatch\n                thisPlugin._wrap(cluster, 'getConnection', thisPlugin._patchGetConnection(cluster));\n                thisPlugin._wrap(cluster, 'add', thisPlugin._patchAdd(cluster));\n                return cluster;\n            };\n        };\n    }\n    _patchAdd(cluster) {\n        return (originalAdd) => {\n            const thisPlugin = this;\n            return function add(id, config) {\n                // Unwrap if unpatch has been called\n                if (!thisPlugin['_enabled']) {\n                    thisPlugin._unwrap(cluster, 'add');\n                    return originalAdd.apply(cluster, arguments);\n                }\n                originalAdd.apply(cluster, arguments);\n                const nodes = cluster['_nodes'];\n                if (nodes) {\n                    const nodeId = typeof id === 'object'\n                        ? 'CLUSTER::' + cluster._lastId\n                        : String(id);\n                    const pool = nodes[nodeId].pool;\n                    thisPlugin._setPoolcallbacks(pool, thisPlugin, id);\n                }\n            };\n        };\n    }\n    // method on cluster or pool\n    _patchGetConnection(pool) {\n        return (originalGetConnection) => {\n            const thisPlugin = this;\n            return function getConnection(arg1, arg2, arg3) {\n                // Unwrap if unpatch has been called\n                if (!thisPlugin['_enabled']) {\n                    thisPlugin._unwrap(pool, 'getConnection');\n                    return originalGetConnection.apply(pool, arguments);\n                }\n                if (arguments.length === 1 && typeof arg1 === 'function') {\n                    const patchFn = thisPlugin._getConnectionCallbackPatchFn(arg1);\n                    return originalGetConnection.call(pool, patchFn);\n                }\n                if (arguments.length === 2 && typeof arg2 === 'function') {\n                    const patchFn = thisPlugin._getConnectionCallbackPatchFn(arg2);\n                    return originalGetConnection.call(pool, arg1, patchFn);\n                }\n                if (arguments.length === 3 && typeof arg3 === 'function') {\n                    const patchFn = thisPlugin._getConnectionCallbackPatchFn(arg3);\n                    return originalGetConnection.call(pool, arg1, arg2, patchFn);\n                }\n                return originalGetConnection.apply(pool, arguments);\n            };\n        };\n    }\n    _getConnectionCallbackPatchFn(cb) {\n        const thisPlugin = this;\n        const activeContext = api_1.context.active();\n        return function (err, connection) {\n            if (connection) {\n                // this is the callback passed into a query\n                // no need to unwrap\n                if (!(0, instrumentation_1.isWrapped)(connection.query)) {\n                    thisPlugin._wrap(connection, 'query', thisPlugin._patchQuery(connection));\n                }\n            }\n            if (typeof cb === 'function') {\n                api_1.context.with(activeContext, cb, this, err, connection);\n            }\n        };\n    }\n    _patchQuery(connection) {\n        return (originalQuery) => {\n            const thisPlugin = this;\n            return function query(query, _valuesOrCallback, _callback) {\n                if (!thisPlugin['_enabled']) {\n                    thisPlugin._unwrap(connection, 'query');\n                    return originalQuery.apply(connection, arguments);\n                }\n                const span = thisPlugin.tracer.startSpan((0, utils_1.getSpanName)(query), {\n                    kind: api_1.SpanKind.CLIENT,\n                    attributes: Object.assign(Object.assign({}, MySQLInstrumentation.COMMON_ATTRIBUTES), (0, utils_1.getConnectionAttributes)(connection.config)),\n                });\n                span.setAttribute(semantic_conventions_1.SEMATTRS_DB_STATEMENT, (0, utils_1.getDbStatement)(query));\n                if (thisPlugin.getConfig().enhancedDatabaseReporting) {\n                    let values;\n                    if (Array.isArray(_valuesOrCallback)) {\n                        values = _valuesOrCallback;\n                    }\n                    else if (arguments[2]) {\n                        values = [_valuesOrCallback];\n                    }\n                    span.setAttribute(AttributeNames_1.AttributeNames.MYSQL_VALUES, (0, utils_1.getDbValues)(query, values));\n                }\n                const cbIndex = Array.from(arguments).findIndex(arg => typeof arg === 'function');\n                const parentContext = api_1.context.active();\n                if (cbIndex === -1) {\n                    const streamableQuery = api_1.context.with(api_1.trace.setSpan(api_1.context.active(), span), () => {\n                        return originalQuery.apply(connection, arguments);\n                    });\n                    api_1.context.bind(parentContext, streamableQuery);\n                    return streamableQuery\n                        .on('error', err => span.setStatus({\n                        code: api_1.SpanStatusCode.ERROR,\n                        message: err.message,\n                    }))\n                        .on('end', () => {\n                        span.end();\n                    });\n                }\n                else {\n                    thisPlugin._wrap(arguments, cbIndex, thisPlugin._patchCallbackQuery(span, parentContext));\n                    return api_1.context.with(api_1.trace.setSpan(api_1.context.active(), span), () => {\n                        return originalQuery.apply(connection, arguments);\n                    });\n                }\n            };\n        };\n    }\n    _patchCallbackQuery(span, parentContext) {\n        return (originalCallback) => {\n            return function (err, results, fields) {\n                if (err) {\n                    span.setStatus({\n                        code: api_1.SpanStatusCode.ERROR,\n                        message: err.message,\n                    });\n                }\n                span.end();\n                return api_1.context.with(parentContext, () => originalCallback(...arguments));\n            };\n        };\n    }\n    _setPoolcallbacks(pool, thisPlugin, id) {\n        //TODO:: use semantic convention\n        const poolName = id || (0, utils_1.getPoolName)(pool);\n        pool.on('connection', connection => {\n            thisPlugin._connectionsUsage.add(1, {\n                state: 'idle',\n                name: poolName,\n            });\n        });\n        pool.on('acquire', connection => {\n            thisPlugin._connectionsUsage.add(-1, {\n                state: 'idle',\n                name: poolName,\n            });\n            thisPlugin._connectionsUsage.add(1, {\n                state: 'used',\n                name: poolName,\n            });\n        });\n        pool.on('release', connection => {\n            thisPlugin._connectionsUsage.add(-1, {\n                state: 'used',\n                name: poolName,\n            });\n            thisPlugin._connectionsUsage.add(1, {\n                state: 'idle',\n                name: poolName,\n            });\n        });\n    }\n}\nexports.MySQLInstrumentation = MySQLInstrumentation;\nMySQLInstrumentation.COMMON_ATTRIBUTES = {\n    [semantic_conventions_1.SEMATTRS_DB_SYSTEM]: semantic_conventions_1.DBSYSTEMVALUES_MYSQL,\n};\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/instrumentation.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/types.js":
/*!******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/types.js ***!
  \******************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF9hYjY5ZTRjNTU2ZGZmMzQ5NDIwNjU2OTVmMDliOWE5MC9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLW15c3FsL2J1aWxkL3NyYy90eXBlcy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbndhclxcRG9jdW1lbnRzXFxDdWJlbnR3ZWJcXGN1YmVudFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQG9wZW50ZWxlbWV0cnkraW5zdHJ1bWVudGF0X2FiNjllNGM1NTZkZmYzNDk0MjA2NTY5NWYwOWI5YTkwXFxub2RlX21vZHVsZXNcXEBvcGVudGVsZW1ldHJ5XFxpbnN0cnVtZW50YXRpb24tbXlzcWxcXGJ1aWxkXFxzcmNcXHR5cGVzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuLypcbiAqIENvcHlyaWdodCBUaGUgT3BlblRlbGVtZXRyeSBBdXRob3JzXG4gKlxuICogTGljZW5zZWQgdW5kZXIgdGhlIEFwYWNoZSBMaWNlbnNlLCBWZXJzaW9uIDIuMCAodGhlIFwiTGljZW5zZVwiKTtcbiAqIHlvdSBtYXkgbm90IHVzZSB0aGlzIGZpbGUgZXhjZXB0IGluIGNvbXBsaWFuY2Ugd2l0aCB0aGUgTGljZW5zZS5cbiAqIFlvdSBtYXkgb2J0YWluIGEgY29weSBvZiB0aGUgTGljZW5zZSBhdFxuICpcbiAqICAgICAgaHR0cHM6Ly93d3cuYXBhY2hlLm9yZy9saWNlbnNlcy9MSUNFTlNFLTIuMFxuICpcbiAqIFVubGVzcyByZXF1aXJlZCBieSBhcHBsaWNhYmxlIGxhdyBvciBhZ3JlZWQgdG8gaW4gd3JpdGluZywgc29mdHdhcmVcbiAqIGRpc3RyaWJ1dGVkIHVuZGVyIHRoZSBMaWNlbnNlIGlzIGRpc3RyaWJ1dGVkIG9uIGFuIFwiQVMgSVNcIiBCQVNJUyxcbiAqIFdJVEhPVVQgV0FSUkFOVElFUyBPUiBDT05ESVRJT05TIE9GIEFOWSBLSU5ELCBlaXRoZXIgZXhwcmVzcyBvciBpbXBsaWVkLlxuICogU2VlIHRoZSBMaWNlbnNlIGZvciB0aGUgc3BlY2lmaWMgbGFuZ3VhZ2UgZ292ZXJuaW5nIHBlcm1pc3Npb25zIGFuZFxuICogbGltaXRhdGlvbnMgdW5kZXIgdGhlIExpY2Vuc2UuXG4gKi9cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXR5cGVzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/types.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/utils.js":
/*!******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/utils.js ***!
  \******************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getPoolName = exports.arrayStringifyHelper = exports.getSpanName = exports.getDbValues = exports.getDbStatement = exports.getConnectionAttributes = void 0;\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\n/**\n * Get an Attributes map from a mysql connection config object\n *\n * @param config ConnectionConfig\n */\nfunction getConnectionAttributes(config) {\n    const { host, port, database, user } = getConfig(config);\n    const portNumber = parseInt(port, 10);\n    if (!isNaN(portNumber)) {\n        return {\n            [semantic_conventions_1.SEMATTRS_NET_PEER_NAME]: host,\n            [semantic_conventions_1.SEMATTRS_NET_PEER_PORT]: portNumber,\n            [semantic_conventions_1.SEMATTRS_DB_CONNECTION_STRING]: getJDBCString(host, port, database),\n            [semantic_conventions_1.SEMATTRS_DB_NAME]: database,\n            [semantic_conventions_1.SEMATTRS_DB_USER]: user,\n        };\n    }\n    return {\n        [semantic_conventions_1.SEMATTRS_NET_PEER_NAME]: host,\n        [semantic_conventions_1.SEMATTRS_DB_CONNECTION_STRING]: getJDBCString(host, port, database),\n        [semantic_conventions_1.SEMATTRS_DB_NAME]: database,\n        [semantic_conventions_1.SEMATTRS_DB_USER]: user,\n    };\n}\nexports.getConnectionAttributes = getConnectionAttributes;\nfunction getConfig(config) {\n    const { host, port, database, user } = (config && config.connectionConfig) || config || {};\n    return { host, port, database, user };\n}\nfunction getJDBCString(host, port, database) {\n    let jdbcString = `jdbc:mysql://${host || 'localhost'}`;\n    if (typeof port === 'number') {\n        jdbcString += `:${port}`;\n    }\n    if (typeof database === 'string') {\n        jdbcString += `/${database}`;\n    }\n    return jdbcString;\n}\n/**\n * @returns the database statement being executed.\n */\nfunction getDbStatement(query) {\n    if (typeof query === 'string') {\n        return query;\n    }\n    else {\n        return query.sql;\n    }\n}\nexports.getDbStatement = getDbStatement;\nfunction getDbValues(query, values) {\n    if (typeof query === 'string') {\n        return arrayStringifyHelper(values);\n    }\n    else {\n        // According to https://github.com/mysqljs/mysql#performing-queries\n        // The values argument will override the values in the option object.\n        return arrayStringifyHelper(values || query.values);\n    }\n}\nexports.getDbValues = getDbValues;\n/**\n * The span name SHOULD be set to a low cardinality value\n * representing the statement executed on the database.\n *\n * @returns SQL statement without variable arguments or SQL verb\n */\nfunction getSpanName(query) {\n    const rawQuery = typeof query === 'object' ? query.sql : query;\n    // Extract the SQL verb\n    const firstSpace = rawQuery === null || rawQuery === void 0 ? void 0 : rawQuery.indexOf(' ');\n    if (typeof firstSpace === 'number' && firstSpace !== -1) {\n        return rawQuery === null || rawQuery === void 0 ? void 0 : rawQuery.substring(0, firstSpace);\n    }\n    return rawQuery;\n}\nexports.getSpanName = getSpanName;\nfunction arrayStringifyHelper(arr) {\n    if (arr)\n        return `[${arr.toString()}]`;\n    return '';\n}\nexports.arrayStringifyHelper = arrayStringifyHelper;\nfunction getPoolName(pool) {\n    const c = pool.config.connectionConfig;\n    let poolName = '';\n    poolName += c.host ? `host: '${c.host}', ` : '';\n    poolName += c.port ? `port: ${c.port}, ` : '';\n    poolName += c.database ? `database: '${c.database}', ` : '';\n    poolName += c.user ? `user: '${c.user}'` : '';\n    if (!c.user) {\n        poolName = poolName.substring(0, poolName.length - 2); //omit last comma\n    }\n    return poolName.trim();\n}\nexports.getPoolName = getPoolName;\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/utils.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/version.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/version.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.45.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-mysql';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/version.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/AttributeNames.js":
/*!***************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/AttributeNames.js ***!
  \***************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.AttributeNames = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// Mysql specific attributes not covered by semantic conventions\nvar AttributeNames;\n(function (AttributeNames) {\n    AttributeNames[\"MYSQL_VALUES\"] = \"db.mysql.values\";\n})(AttributeNames = exports.AttributeNames || (exports.AttributeNames = {}));\n//# sourceMappingURL=AttributeNames.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF9hYjY5ZTRjNTU2ZGZmMzQ5NDIwNjU2OTVmMDliOWE5MC9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLW15c3FsL2J1aWxkL3NyYy9BdHRyaWJ1dGVOYW1lcy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxzQkFBc0I7QUFDdEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLDhDQUE4QyxzQkFBc0IsS0FBSztBQUMxRSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbndhclxcRG9jdW1lbnRzXFxDdWJlbnR3ZWJcXGN1YmVudFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQG9wZW50ZWxlbWV0cnkraW5zdHJ1bWVudGF0X2FiNjllNGM1NTZkZmYzNDk0MjA2NTY5NWYwOWI5YTkwXFxub2RlX21vZHVsZXNcXEBvcGVudGVsZW1ldHJ5XFxpbnN0cnVtZW50YXRpb24tbXlzcWxcXGJ1aWxkXFxzcmNcXEF0dHJpYnV0ZU5hbWVzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5BdHRyaWJ1dGVOYW1lcyA9IHZvaWQgMDtcbi8qXG4gKiBDb3B5cmlnaHQgVGhlIE9wZW5UZWxlbWV0cnkgQXV0aG9yc1xuICpcbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4gKiB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuXG4gKiBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbiAqXG4gKiAgICAgIGh0dHBzOi8vd3d3LmFwYWNoZS5vcmcvbGljZW5zZXMvTElDRU5TRS0yLjBcbiAqXG4gKiBVbmxlc3MgcmVxdWlyZWQgYnkgYXBwbGljYWJsZSBsYXcgb3IgYWdyZWVkIHRvIGluIHdyaXRpbmcsIHNvZnR3YXJlXG4gKiBkaXN0cmlidXRlZCB1bmRlciB0aGUgTGljZW5zZSBpcyBkaXN0cmlidXRlZCBvbiBhbiBcIkFTIElTXCIgQkFTSVMsXG4gKiBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgQ09ORElUSU9OUyBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC5cbiAqIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmRcbiAqIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuICovXG4vLyBNeXNxbCBzcGVjaWZpYyBhdHRyaWJ1dGVzIG5vdCBjb3ZlcmVkIGJ5IHNlbWFudGljIGNvbnZlbnRpb25zXG52YXIgQXR0cmlidXRlTmFtZXM7XG4oZnVuY3Rpb24gKEF0dHJpYnV0ZU5hbWVzKSB7XG4gICAgQXR0cmlidXRlTmFtZXNbXCJNWVNRTF9WQUxVRVNcIl0gPSBcImRiLm15c3FsLnZhbHVlc1wiO1xufSkoQXR0cmlidXRlTmFtZXMgPSBleHBvcnRzLkF0dHJpYnV0ZU5hbWVzIHx8IChleHBvcnRzLkF0dHJpYnV0ZU5hbWVzID0ge30pKTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPUF0dHJpYnV0ZU5hbWVzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/AttributeNames.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/index.js":
/*!******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/index.js ***!
  \******************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/instrumentation.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/types.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF9hYjY5ZTRjNTU2ZGZmMzQ5NDIwNjU2OTVmMDliOWE5MC9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLW15c3FsL2J1aWxkL3NyYy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQ0FBbUMsb0NBQW9DLGdCQUFnQjtBQUN2RixDQUFDO0FBQ0Q7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQSw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsYUFBYSxtQkFBTyxDQUFDLHFNQUFtQjtBQUN4QyxhQUFhLG1CQUFPLENBQUMsaUxBQVM7QUFDOUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYW53YXJcXERvY3VtZW50c1xcQ3ViZW50d2ViXFxjdWJlbnRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF9hYjY5ZTRjNTU2ZGZmMzQ5NDIwNjU2OTVmMDliOWE5MFxcbm9kZV9tb2R1bGVzXFxAb3BlbnRlbGVtZXRyeVxcaW5zdHJ1bWVudGF0aW9uLW15c3FsXFxidWlsZFxcc3JjXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbi8qXG4gKiBDb3B5cmlnaHQgVGhlIE9wZW5UZWxlbWV0cnkgQXV0aG9yc1xuICpcbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4gKiB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuXG4gKiBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbiAqXG4gKiAgICAgIGh0dHBzOi8vd3d3LmFwYWNoZS5vcmcvbGljZW5zZXMvTElDRU5TRS0yLjBcbiAqXG4gKiBVbmxlc3MgcmVxdWlyZWQgYnkgYXBwbGljYWJsZSBsYXcgb3IgYWdyZWVkIHRvIGluIHdyaXRpbmcsIHNvZnR3YXJlXG4gKiBkaXN0cmlidXRlZCB1bmRlciB0aGUgTGljZW5zZSBpcyBkaXN0cmlidXRlZCBvbiBhbiBcIkFTIElTXCIgQkFTSVMsXG4gKiBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgQ09ORElUSU9OUyBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC5cbiAqIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmRcbiAqIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuICovXG52YXIgX19jcmVhdGVCaW5kaW5nID0gKHRoaXMgJiYgdGhpcy5fX2NyZWF0ZUJpbmRpbmcpIHx8IChPYmplY3QuY3JlYXRlID8gKGZ1bmN0aW9uKG8sIG0sIGssIGsyKSB7XG4gICAgaWYgKGsyID09PSB1bmRlZmluZWQpIGsyID0gaztcbiAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkobywgazIsIHsgZW51bWVyYWJsZTogdHJ1ZSwgZ2V0OiBmdW5jdGlvbigpIHsgcmV0dXJuIG1ba107IH0gfSk7XG59KSA6IChmdW5jdGlvbihvLCBtLCBrLCBrMikge1xuICAgIGlmIChrMiA9PT0gdW5kZWZpbmVkKSBrMiA9IGs7XG4gICAgb1trMl0gPSBtW2tdO1xufSkpO1xudmFyIF9fZXhwb3J0U3RhciA9ICh0aGlzICYmIHRoaXMuX19leHBvcnRTdGFyKSB8fCBmdW5jdGlvbihtLCBleHBvcnRzKSB7XG4gICAgZm9yICh2YXIgcCBpbiBtKSBpZiAocCAhPT0gXCJkZWZhdWx0XCIgJiYgIU9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHkuY2FsbChleHBvcnRzLCBwKSkgX19jcmVhdGVCaW5kaW5nKGV4cG9ydHMsIG0sIHApO1xufTtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbl9fZXhwb3J0U3RhcihyZXF1aXJlKFwiLi9pbnN0cnVtZW50YXRpb25cIiksIGV4cG9ydHMpO1xuX19leHBvcnRTdGFyKHJlcXVpcmUoXCIuL3R5cGVzXCIpLCBleHBvcnRzKTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/instrumentation.js":
/*!****************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/instrumentation.js ***!
  \****************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.MySQLInstrumentation = void 0;\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst AttributeNames_1 = __webpack_require__(/*! ./AttributeNames */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/AttributeNames.js\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/utils.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/version.js\");\nclass MySQLInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, config);\n        this._setMetricInstruments();\n    }\n    setMeterProvider(meterProvider) {\n        super.setMeterProvider(meterProvider);\n        this._setMetricInstruments();\n    }\n    _setMetricInstruments() {\n        this._connectionsUsage = this.meter.createUpDownCounter('db.client.connections.usage', //TODO:: use semantic convention\n        {\n            description: 'The number of connections that are currently in state described by the state attribute.',\n            unit: '{connection}',\n        });\n    }\n    init() {\n        return [\n            new instrumentation_1.InstrumentationNodeModuleDefinition('mysql', ['>=2.0.0 <3'], (moduleExports) => {\n                if ((0, instrumentation_1.isWrapped)(moduleExports.createConnection)) {\n                    this._unwrap(moduleExports, 'createConnection');\n                }\n                this._wrap(moduleExports, 'createConnection', this._patchCreateConnection());\n                if ((0, instrumentation_1.isWrapped)(moduleExports.createPool)) {\n                    this._unwrap(moduleExports, 'createPool');\n                }\n                this._wrap(moduleExports, 'createPool', this._patchCreatePool());\n                if ((0, instrumentation_1.isWrapped)(moduleExports.createPoolCluster)) {\n                    this._unwrap(moduleExports, 'createPoolCluster');\n                }\n                this._wrap(moduleExports, 'createPoolCluster', this._patchCreatePoolCluster());\n                return moduleExports;\n            }, (moduleExports) => {\n                if (moduleExports === undefined)\n                    return;\n                this._unwrap(moduleExports, 'createConnection');\n                this._unwrap(moduleExports, 'createPool');\n                this._unwrap(moduleExports, 'createPoolCluster');\n            }),\n        ];\n    }\n    // global export function\n    _patchCreateConnection() {\n        return (originalCreateConnection) => {\n            const thisPlugin = this;\n            return function createConnection(_connectionUri) {\n                const originalResult = originalCreateConnection(...arguments);\n                // This is unwrapped on next call after unpatch\n                thisPlugin._wrap(originalResult, 'query', thisPlugin._patchQuery(originalResult));\n                return originalResult;\n            };\n        };\n    }\n    // global export function\n    _patchCreatePool() {\n        return (originalCreatePool) => {\n            const thisPlugin = this;\n            return function createPool(_config) {\n                const pool = originalCreatePool(...arguments);\n                thisPlugin._wrap(pool, 'query', thisPlugin._patchQuery(pool));\n                thisPlugin._wrap(pool, 'getConnection', thisPlugin._patchGetConnection(pool));\n                thisPlugin._wrap(pool, 'end', thisPlugin._patchPoolEnd(pool));\n                thisPlugin._setPoolcallbacks(pool, thisPlugin, '');\n                return pool;\n            };\n        };\n    }\n    _patchPoolEnd(pool) {\n        return (originalPoolEnd) => {\n            const thisPlugin = this;\n            return function end(callback) {\n                const nAll = pool._allConnections.length;\n                const nFree = pool._freeConnections.length;\n                const nUsed = nAll - nFree;\n                const poolName = (0, utils_1.getPoolName)(pool);\n                thisPlugin._connectionsUsage.add(-nUsed, {\n                    state: 'used',\n                    name: poolName,\n                });\n                thisPlugin._connectionsUsage.add(-nFree, {\n                    state: 'idle',\n                    name: poolName,\n                });\n                originalPoolEnd.apply(pool, arguments);\n            };\n        };\n    }\n    // global export function\n    _patchCreatePoolCluster() {\n        return (originalCreatePoolCluster) => {\n            const thisPlugin = this;\n            return function createPool(_config) {\n                const cluster = originalCreatePoolCluster(...arguments);\n                // This is unwrapped on next call after unpatch\n                thisPlugin._wrap(cluster, 'getConnection', thisPlugin._patchGetConnection(cluster));\n                thisPlugin._wrap(cluster, 'add', thisPlugin._patchAdd(cluster));\n                return cluster;\n            };\n        };\n    }\n    _patchAdd(cluster) {\n        return (originalAdd) => {\n            const thisPlugin = this;\n            return function add(id, config) {\n                // Unwrap if unpatch has been called\n                if (!thisPlugin['_enabled']) {\n                    thisPlugin._unwrap(cluster, 'add');\n                    return originalAdd.apply(cluster, arguments);\n                }\n                originalAdd.apply(cluster, arguments);\n                const nodes = cluster['_nodes'];\n                if (nodes) {\n                    const nodeId = typeof id === 'object'\n                        ? 'CLUSTER::' + cluster._lastId\n                        : String(id);\n                    const pool = nodes[nodeId].pool;\n                    thisPlugin._setPoolcallbacks(pool, thisPlugin, id);\n                }\n            };\n        };\n    }\n    // method on cluster or pool\n    _patchGetConnection(pool) {\n        return (originalGetConnection) => {\n            const thisPlugin = this;\n            return function getConnection(arg1, arg2, arg3) {\n                // Unwrap if unpatch has been called\n                if (!thisPlugin['_enabled']) {\n                    thisPlugin._unwrap(pool, 'getConnection');\n                    return originalGetConnection.apply(pool, arguments);\n                }\n                if (arguments.length === 1 && typeof arg1 === 'function') {\n                    const patchFn = thisPlugin._getConnectionCallbackPatchFn(arg1);\n                    return originalGetConnection.call(pool, patchFn);\n                }\n                if (arguments.length === 2 && typeof arg2 === 'function') {\n                    const patchFn = thisPlugin._getConnectionCallbackPatchFn(arg2);\n                    return originalGetConnection.call(pool, arg1, patchFn);\n                }\n                if (arguments.length === 3 && typeof arg3 === 'function') {\n                    const patchFn = thisPlugin._getConnectionCallbackPatchFn(arg3);\n                    return originalGetConnection.call(pool, arg1, arg2, patchFn);\n                }\n                return originalGetConnection.apply(pool, arguments);\n            };\n        };\n    }\n    _getConnectionCallbackPatchFn(cb) {\n        const thisPlugin = this;\n        const activeContext = api_1.context.active();\n        return function (err, connection) {\n            if (connection) {\n                // this is the callback passed into a query\n                // no need to unwrap\n                if (!(0, instrumentation_1.isWrapped)(connection.query)) {\n                    thisPlugin._wrap(connection, 'query', thisPlugin._patchQuery(connection));\n                }\n            }\n            if (typeof cb === 'function') {\n                api_1.context.with(activeContext, cb, this, err, connection);\n            }\n        };\n    }\n    _patchQuery(connection) {\n        return (originalQuery) => {\n            const thisPlugin = this;\n            return function query(query, _valuesOrCallback, _callback) {\n                if (!thisPlugin['_enabled']) {\n                    thisPlugin._unwrap(connection, 'query');\n                    return originalQuery.apply(connection, arguments);\n                }\n                const span = thisPlugin.tracer.startSpan((0, utils_1.getSpanName)(query), {\n                    kind: api_1.SpanKind.CLIENT,\n                    attributes: Object.assign(Object.assign({}, MySQLInstrumentation.COMMON_ATTRIBUTES), (0, utils_1.getConnectionAttributes)(connection.config)),\n                });\n                span.setAttribute(semantic_conventions_1.SEMATTRS_DB_STATEMENT, (0, utils_1.getDbStatement)(query));\n                if (thisPlugin.getConfig().enhancedDatabaseReporting) {\n                    let values;\n                    if (Array.isArray(_valuesOrCallback)) {\n                        values = _valuesOrCallback;\n                    }\n                    else if (arguments[2]) {\n                        values = [_valuesOrCallback];\n                    }\n                    span.setAttribute(AttributeNames_1.AttributeNames.MYSQL_VALUES, (0, utils_1.getDbValues)(query, values));\n                }\n                const cbIndex = Array.from(arguments).findIndex(arg => typeof arg === 'function');\n                const parentContext = api_1.context.active();\n                if (cbIndex === -1) {\n                    const streamableQuery = api_1.context.with(api_1.trace.setSpan(api_1.context.active(), span), () => {\n                        return originalQuery.apply(connection, arguments);\n                    });\n                    api_1.context.bind(parentContext, streamableQuery);\n                    return streamableQuery\n                        .on('error', err => span.setStatus({\n                        code: api_1.SpanStatusCode.ERROR,\n                        message: err.message,\n                    }))\n                        .on('end', () => {\n                        span.end();\n                    });\n                }\n                else {\n                    thisPlugin._wrap(arguments, cbIndex, thisPlugin._patchCallbackQuery(span, parentContext));\n                    return api_1.context.with(api_1.trace.setSpan(api_1.context.active(), span), () => {\n                        return originalQuery.apply(connection, arguments);\n                    });\n                }\n            };\n        };\n    }\n    _patchCallbackQuery(span, parentContext) {\n        return (originalCallback) => {\n            return function (err, results, fields) {\n                if (err) {\n                    span.setStatus({\n                        code: api_1.SpanStatusCode.ERROR,\n                        message: err.message,\n                    });\n                }\n                span.end();\n                return api_1.context.with(parentContext, () => originalCallback(...arguments));\n            };\n        };\n    }\n    _setPoolcallbacks(pool, thisPlugin, id) {\n        //TODO:: use semantic convention\n        const poolName = id || (0, utils_1.getPoolName)(pool);\n        pool.on('connection', connection => {\n            thisPlugin._connectionsUsage.add(1, {\n                state: 'idle',\n                name: poolName,\n            });\n        });\n        pool.on('acquire', connection => {\n            thisPlugin._connectionsUsage.add(-1, {\n                state: 'idle',\n                name: poolName,\n            });\n            thisPlugin._connectionsUsage.add(1, {\n                state: 'used',\n                name: poolName,\n            });\n        });\n        pool.on('release', connection => {\n            thisPlugin._connectionsUsage.add(-1, {\n                state: 'used',\n                name: poolName,\n            });\n            thisPlugin._connectionsUsage.add(1, {\n                state: 'idle',\n                name: poolName,\n            });\n        });\n    }\n}\nexports.MySQLInstrumentation = MySQLInstrumentation;\nMySQLInstrumentation.COMMON_ATTRIBUTES = {\n    [semantic_conventions_1.SEMATTRS_DB_SYSTEM]: semantic_conventions_1.DBSYSTEMVALUES_MYSQL,\n};\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/instrumentation.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/types.js":
/*!******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/types.js ***!
  \******************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF9hYjY5ZTRjNTU2ZGZmMzQ5NDIwNjU2OTVmMDliOWE5MC9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLW15c3FsL2J1aWxkL3NyYy90eXBlcy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbndhclxcRG9jdW1lbnRzXFxDdWJlbnR3ZWJcXGN1YmVudFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQG9wZW50ZWxlbWV0cnkraW5zdHJ1bWVudGF0X2FiNjllNGM1NTZkZmYzNDk0MjA2NTY5NWYwOWI5YTkwXFxub2RlX21vZHVsZXNcXEBvcGVudGVsZW1ldHJ5XFxpbnN0cnVtZW50YXRpb24tbXlzcWxcXGJ1aWxkXFxzcmNcXHR5cGVzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuLypcbiAqIENvcHlyaWdodCBUaGUgT3BlblRlbGVtZXRyeSBBdXRob3JzXG4gKlxuICogTGljZW5zZWQgdW5kZXIgdGhlIEFwYWNoZSBMaWNlbnNlLCBWZXJzaW9uIDIuMCAodGhlIFwiTGljZW5zZVwiKTtcbiAqIHlvdSBtYXkgbm90IHVzZSB0aGlzIGZpbGUgZXhjZXB0IGluIGNvbXBsaWFuY2Ugd2l0aCB0aGUgTGljZW5zZS5cbiAqIFlvdSBtYXkgb2J0YWluIGEgY29weSBvZiB0aGUgTGljZW5zZSBhdFxuICpcbiAqICAgICAgaHR0cHM6Ly93d3cuYXBhY2hlLm9yZy9saWNlbnNlcy9MSUNFTlNFLTIuMFxuICpcbiAqIFVubGVzcyByZXF1aXJlZCBieSBhcHBsaWNhYmxlIGxhdyBvciBhZ3JlZWQgdG8gaW4gd3JpdGluZywgc29mdHdhcmVcbiAqIGRpc3RyaWJ1dGVkIHVuZGVyIHRoZSBMaWNlbnNlIGlzIGRpc3RyaWJ1dGVkIG9uIGFuIFwiQVMgSVNcIiBCQVNJUyxcbiAqIFdJVEhPVVQgV0FSUkFOVElFUyBPUiBDT05ESVRJT05TIE9GIEFOWSBLSU5ELCBlaXRoZXIgZXhwcmVzcyBvciBpbXBsaWVkLlxuICogU2VlIHRoZSBMaWNlbnNlIGZvciB0aGUgc3BlY2lmaWMgbGFuZ3VhZ2UgZ292ZXJuaW5nIHBlcm1pc3Npb25zIGFuZFxuICogbGltaXRhdGlvbnMgdW5kZXIgdGhlIExpY2Vuc2UuXG4gKi9cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXR5cGVzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/types.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/utils.js":
/*!******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/utils.js ***!
  \******************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getPoolName = exports.arrayStringifyHelper = exports.getSpanName = exports.getDbValues = exports.getDbStatement = exports.getConnectionAttributes = void 0;\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\n/**\n * Get an Attributes map from a mysql connection config object\n *\n * @param config ConnectionConfig\n */\nfunction getConnectionAttributes(config) {\n    const { host, port, database, user } = getConfig(config);\n    const portNumber = parseInt(port, 10);\n    if (!isNaN(portNumber)) {\n        return {\n            [semantic_conventions_1.SEMATTRS_NET_PEER_NAME]: host,\n            [semantic_conventions_1.SEMATTRS_NET_PEER_PORT]: portNumber,\n            [semantic_conventions_1.SEMATTRS_DB_CONNECTION_STRING]: getJDBCString(host, port, database),\n            [semantic_conventions_1.SEMATTRS_DB_NAME]: database,\n            [semantic_conventions_1.SEMATTRS_DB_USER]: user,\n        };\n    }\n    return {\n        [semantic_conventions_1.SEMATTRS_NET_PEER_NAME]: host,\n        [semantic_conventions_1.SEMATTRS_DB_CONNECTION_STRING]: getJDBCString(host, port, database),\n        [semantic_conventions_1.SEMATTRS_DB_NAME]: database,\n        [semantic_conventions_1.SEMATTRS_DB_USER]: user,\n    };\n}\nexports.getConnectionAttributes = getConnectionAttributes;\nfunction getConfig(config) {\n    const { host, port, database, user } = (config && config.connectionConfig) || config || {};\n    return { host, port, database, user };\n}\nfunction getJDBCString(host, port, database) {\n    let jdbcString = `jdbc:mysql://${host || 'localhost'}`;\n    if (typeof port === 'number') {\n        jdbcString += `:${port}`;\n    }\n    if (typeof database === 'string') {\n        jdbcString += `/${database}`;\n    }\n    return jdbcString;\n}\n/**\n * @returns the database statement being executed.\n */\nfunction getDbStatement(query) {\n    if (typeof query === 'string') {\n        return query;\n    }\n    else {\n        return query.sql;\n    }\n}\nexports.getDbStatement = getDbStatement;\nfunction getDbValues(query, values) {\n    if (typeof query === 'string') {\n        return arrayStringifyHelper(values);\n    }\n    else {\n        // According to https://github.com/mysqljs/mysql#performing-queries\n        // The values argument will override the values in the option object.\n        return arrayStringifyHelper(values || query.values);\n    }\n}\nexports.getDbValues = getDbValues;\n/**\n * The span name SHOULD be set to a low cardinality value\n * representing the statement executed on the database.\n *\n * @returns SQL statement without variable arguments or SQL verb\n */\nfunction getSpanName(query) {\n    const rawQuery = typeof query === 'object' ? query.sql : query;\n    // Extract the SQL verb\n    const firstSpace = rawQuery === null || rawQuery === void 0 ? void 0 : rawQuery.indexOf(' ');\n    if (typeof firstSpace === 'number' && firstSpace !== -1) {\n        return rawQuery === null || rawQuery === void 0 ? void 0 : rawQuery.substring(0, firstSpace);\n    }\n    return rawQuery;\n}\nexports.getSpanName = getSpanName;\nfunction arrayStringifyHelper(arr) {\n    if (arr)\n        return `[${arr.toString()}]`;\n    return '';\n}\nexports.arrayStringifyHelper = arrayStringifyHelper;\nfunction getPoolName(pool) {\n    const c = pool.config.connectionConfig;\n    let poolName = '';\n    poolName += c.host ? `host: '${c.host}', ` : '';\n    poolName += c.port ? `port: ${c.port}, ` : '';\n    poolName += c.database ? `database: '${c.database}', ` : '';\n    poolName += c.user ? `user: '${c.user}'` : '';\n    if (!c.user) {\n        poolName = poolName.substring(0, poolName.length - 2); //omit last comma\n    }\n    return poolName.trim();\n}\nexports.getPoolName = getPoolName;\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF9hYjY5ZTRjNTU2ZGZmMzQ5NDIwNjU2OTVmMDliOWE5MC9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLW15c3FsL2J1aWxkL3NyYy91dGlscy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxtQkFBbUIsR0FBRyw0QkFBNEIsR0FBRyxtQkFBbUIsR0FBRyxtQkFBbUIsR0FBRyxzQkFBc0IsR0FBRywrQkFBK0I7QUFDekosK0JBQStCLG1CQUFPLENBQUMsMExBQXFDO0FBQzVFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVksNkJBQTZCO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwrQkFBK0I7QUFDL0I7QUFDQSxZQUFZLDZCQUE2QjtBQUN6QyxhQUFhO0FBQ2I7QUFDQTtBQUNBLHFDQUFxQyxvQkFBb0I7QUFDekQ7QUFDQSwwQkFBMEIsS0FBSztBQUMvQjtBQUNBO0FBQ0EsMEJBQTBCLFNBQVM7QUFDbkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQjtBQUN0QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQjtBQUNuQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQkFBbUI7QUFDbkI7QUFDQTtBQUNBLG1CQUFtQixlQUFlO0FBQ2xDO0FBQ0E7QUFDQSw0QkFBNEI7QUFDNUI7QUFDQTtBQUNBO0FBQ0EsbUNBQW1DLE9BQU87QUFDMUMsa0NBQWtDLE9BQU87QUFDekMsMkNBQTJDLFdBQVc7QUFDdEQsbUNBQW1DLE9BQU87QUFDMUM7QUFDQSwrREFBK0Q7QUFDL0Q7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CO0FBQ25CIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFud2FyXFxEb2N1bWVudHNcXEN1YmVudHdlYlxcY3ViZW50XFxub2RlX21vZHVsZXNcXC5wbnBtXFxAb3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfYWI2OWU0YzU1NmRmZjM0OTQyMDY1Njk1ZjA5YjlhOTBcXG5vZGVfbW9kdWxlc1xcQG9wZW50ZWxlbWV0cnlcXGluc3RydW1lbnRhdGlvbi1teXNxbFxcYnVpbGRcXHNyY1xcdXRpbHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG4vKlxuICogQ29weXJpZ2h0IFRoZSBPcGVuVGVsZW1ldHJ5IEF1dGhvcnNcbiAqXG4gKiBMaWNlbnNlZCB1bmRlciB0aGUgQXBhY2hlIExpY2Vuc2UsIFZlcnNpb24gMi4wICh0aGUgXCJMaWNlbnNlXCIpO1xuICogeW91IG1heSBub3QgdXNlIHRoaXMgZmlsZSBleGNlcHQgaW4gY29tcGxpYW5jZSB3aXRoIHRoZSBMaWNlbnNlLlxuICogWW91IG1heSBvYnRhaW4gYSBjb3B5IG9mIHRoZSBMaWNlbnNlIGF0XG4gKlxuICogICAgICBodHRwczovL3d3dy5hcGFjaGUub3JnL2xpY2Vuc2VzL0xJQ0VOU0UtMi4wXG4gKlxuICogVW5sZXNzIHJlcXVpcmVkIGJ5IGFwcGxpY2FibGUgbGF3IG9yIGFncmVlZCB0byBpbiB3cml0aW5nLCBzb2Z0d2FyZVxuICogZGlzdHJpYnV0ZWQgdW5kZXIgdGhlIExpY2Vuc2UgaXMgZGlzdHJpYnV0ZWQgb24gYW4gXCJBUyBJU1wiIEJBU0lTLFxuICogV0lUSE9VVCBXQVJSQU5USUVTIE9SIENPTkRJVElPTlMgT0YgQU5ZIEtJTkQsIGVpdGhlciBleHByZXNzIG9yIGltcGxpZWQuXG4gKiBTZWUgdGhlIExpY2Vuc2UgZm9yIHRoZSBzcGVjaWZpYyBsYW5ndWFnZSBnb3Zlcm5pbmcgcGVybWlzc2lvbnMgYW5kXG4gKiBsaW1pdGF0aW9ucyB1bmRlciB0aGUgTGljZW5zZS5cbiAqL1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5nZXRQb29sTmFtZSA9IGV4cG9ydHMuYXJyYXlTdHJpbmdpZnlIZWxwZXIgPSBleHBvcnRzLmdldFNwYW5OYW1lID0gZXhwb3J0cy5nZXREYlZhbHVlcyA9IGV4cG9ydHMuZ2V0RGJTdGF0ZW1lbnQgPSBleHBvcnRzLmdldENvbm5lY3Rpb25BdHRyaWJ1dGVzID0gdm9pZCAwO1xuY29uc3Qgc2VtYW50aWNfY29udmVudGlvbnNfMSA9IHJlcXVpcmUoXCJAb3BlbnRlbGVtZXRyeS9zZW1hbnRpYy1jb252ZW50aW9uc1wiKTtcbi8qKlxuICogR2V0IGFuIEF0dHJpYnV0ZXMgbWFwIGZyb20gYSBteXNxbCBjb25uZWN0aW9uIGNvbmZpZyBvYmplY3RcbiAqXG4gKiBAcGFyYW0gY29uZmlnIENvbm5lY3Rpb25Db25maWdcbiAqL1xuZnVuY3Rpb24gZ2V0Q29ubmVjdGlvbkF0dHJpYnV0ZXMoY29uZmlnKSB7XG4gICAgY29uc3QgeyBob3N0LCBwb3J0LCBkYXRhYmFzZSwgdXNlciB9ID0gZ2V0Q29uZmlnKGNvbmZpZyk7XG4gICAgY29uc3QgcG9ydE51bWJlciA9IHBhcnNlSW50KHBvcnQsIDEwKTtcbiAgICBpZiAoIWlzTmFOKHBvcnROdW1iZXIpKSB7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICBbc2VtYW50aWNfY29udmVudGlvbnNfMS5TRU1BVFRSU19ORVRfUEVFUl9OQU1FXTogaG9zdCxcbiAgICAgICAgICAgIFtzZW1hbnRpY19jb252ZW50aW9uc18xLlNFTUFUVFJTX05FVF9QRUVSX1BPUlRdOiBwb3J0TnVtYmVyLFxuICAgICAgICAgICAgW3NlbWFudGljX2NvbnZlbnRpb25zXzEuU0VNQVRUUlNfREJfQ09OTkVDVElPTl9TVFJJTkddOiBnZXRKREJDU3RyaW5nKGhvc3QsIHBvcnQsIGRhdGFiYXNlKSxcbiAgICAgICAgICAgIFtzZW1hbnRpY19jb252ZW50aW9uc18xLlNFTUFUVFJTX0RCX05BTUVdOiBkYXRhYmFzZSxcbiAgICAgICAgICAgIFtzZW1hbnRpY19jb252ZW50aW9uc18xLlNFTUFUVFJTX0RCX1VTRVJdOiB1c2VyLFxuICAgICAgICB9O1xuICAgIH1cbiAgICByZXR1cm4ge1xuICAgICAgICBbc2VtYW50aWNfY29udmVudGlvbnNfMS5TRU1BVFRSU19ORVRfUEVFUl9OQU1FXTogaG9zdCxcbiAgICAgICAgW3NlbWFudGljX2NvbnZlbnRpb25zXzEuU0VNQVRUUlNfREJfQ09OTkVDVElPTl9TVFJJTkddOiBnZXRKREJDU3RyaW5nKGhvc3QsIHBvcnQsIGRhdGFiYXNlKSxcbiAgICAgICAgW3NlbWFudGljX2NvbnZlbnRpb25zXzEuU0VNQVRUUlNfREJfTkFNRV06IGRhdGFiYXNlLFxuICAgICAgICBbc2VtYW50aWNfY29udmVudGlvbnNfMS5TRU1BVFRSU19EQl9VU0VSXTogdXNlcixcbiAgICB9O1xufVxuZXhwb3J0cy5nZXRDb25uZWN0aW9uQXR0cmlidXRlcyA9IGdldENvbm5lY3Rpb25BdHRyaWJ1dGVzO1xuZnVuY3Rpb24gZ2V0Q29uZmlnKGNvbmZpZykge1xuICAgIGNvbnN0IHsgaG9zdCwgcG9ydCwgZGF0YWJhc2UsIHVzZXIgfSA9IChjb25maWcgJiYgY29uZmlnLmNvbm5lY3Rpb25Db25maWcpIHx8IGNvbmZpZyB8fCB7fTtcbiAgICByZXR1cm4geyBob3N0LCBwb3J0LCBkYXRhYmFzZSwgdXNlciB9O1xufVxuZnVuY3Rpb24gZ2V0SkRCQ1N0cmluZyhob3N0LCBwb3J0LCBkYXRhYmFzZSkge1xuICAgIGxldCBqZGJjU3RyaW5nID0gYGpkYmM6bXlzcWw6Ly8ke2hvc3QgfHwgJ2xvY2FsaG9zdCd9YDtcbiAgICBpZiAodHlwZW9mIHBvcnQgPT09ICdudW1iZXInKSB7XG4gICAgICAgIGpkYmNTdHJpbmcgKz0gYDoke3BvcnR9YDtcbiAgICB9XG4gICAgaWYgKHR5cGVvZiBkYXRhYmFzZSA9PT0gJ3N0cmluZycpIHtcbiAgICAgICAgamRiY1N0cmluZyArPSBgLyR7ZGF0YWJhc2V9YDtcbiAgICB9XG4gICAgcmV0dXJuIGpkYmNTdHJpbmc7XG59XG4vKipcbiAqIEByZXR1cm5zIHRoZSBkYXRhYmFzZSBzdGF0ZW1lbnQgYmVpbmcgZXhlY3V0ZWQuXG4gKi9cbmZ1bmN0aW9uIGdldERiU3RhdGVtZW50KHF1ZXJ5KSB7XG4gICAgaWYgKHR5cGVvZiBxdWVyeSA9PT0gJ3N0cmluZycpIHtcbiAgICAgICAgcmV0dXJuIHF1ZXJ5O1xuICAgIH1cbiAgICBlbHNlIHtcbiAgICAgICAgcmV0dXJuIHF1ZXJ5LnNxbDtcbiAgICB9XG59XG5leHBvcnRzLmdldERiU3RhdGVtZW50ID0gZ2V0RGJTdGF0ZW1lbnQ7XG5mdW5jdGlvbiBnZXREYlZhbHVlcyhxdWVyeSwgdmFsdWVzKSB7XG4gICAgaWYgKHR5cGVvZiBxdWVyeSA9PT0gJ3N0cmluZycpIHtcbiAgICAgICAgcmV0dXJuIGFycmF5U3RyaW5naWZ5SGVscGVyKHZhbHVlcyk7XG4gICAgfVxuICAgIGVsc2Uge1xuICAgICAgICAvLyBBY2NvcmRpbmcgdG8gaHR0cHM6Ly9naXRodWIuY29tL215c3FsanMvbXlzcWwjcGVyZm9ybWluZy1xdWVyaWVzXG4gICAgICAgIC8vIFRoZSB2YWx1ZXMgYXJndW1lbnQgd2lsbCBvdmVycmlkZSB0aGUgdmFsdWVzIGluIHRoZSBvcHRpb24gb2JqZWN0LlxuICAgICAgICByZXR1cm4gYXJyYXlTdHJpbmdpZnlIZWxwZXIodmFsdWVzIHx8IHF1ZXJ5LnZhbHVlcyk7XG4gICAgfVxufVxuZXhwb3J0cy5nZXREYlZhbHVlcyA9IGdldERiVmFsdWVzO1xuLyoqXG4gKiBUaGUgc3BhbiBuYW1lIFNIT1VMRCBiZSBzZXQgdG8gYSBsb3cgY2FyZGluYWxpdHkgdmFsdWVcbiAqIHJlcHJlc2VudGluZyB0aGUgc3RhdGVtZW50IGV4ZWN1dGVkIG9uIHRoZSBkYXRhYmFzZS5cbiAqXG4gKiBAcmV0dXJucyBTUUwgc3RhdGVtZW50IHdpdGhvdXQgdmFyaWFibGUgYXJndW1lbnRzIG9yIFNRTCB2ZXJiXG4gKi9cbmZ1bmN0aW9uIGdldFNwYW5OYW1lKHF1ZXJ5KSB7XG4gICAgY29uc3QgcmF3UXVlcnkgPSB0eXBlb2YgcXVlcnkgPT09ICdvYmplY3QnID8gcXVlcnkuc3FsIDogcXVlcnk7XG4gICAgLy8gRXh0cmFjdCB0aGUgU1FMIHZlcmJcbiAgICBjb25zdCBmaXJzdFNwYWNlID0gcmF3UXVlcnkgPT09IG51bGwgfHwgcmF3UXVlcnkgPT09IHZvaWQgMCA/IHZvaWQgMCA6IHJhd1F1ZXJ5LmluZGV4T2YoJyAnKTtcbiAgICBpZiAodHlwZW9mIGZpcnN0U3BhY2UgPT09ICdudW1iZXInICYmIGZpcnN0U3BhY2UgIT09IC0xKSB7XG4gICAgICAgIHJldHVybiByYXdRdWVyeSA9PT0gbnVsbCB8fCByYXdRdWVyeSA9PT0gdm9pZCAwID8gdm9pZCAwIDogcmF3UXVlcnkuc3Vic3RyaW5nKDAsIGZpcnN0U3BhY2UpO1xuICAgIH1cbiAgICByZXR1cm4gcmF3UXVlcnk7XG59XG5leHBvcnRzLmdldFNwYW5OYW1lID0gZ2V0U3Bhbk5hbWU7XG5mdW5jdGlvbiBhcnJheVN0cmluZ2lmeUhlbHBlcihhcnIpIHtcbiAgICBpZiAoYXJyKVxuICAgICAgICByZXR1cm4gYFske2Fyci50b1N0cmluZygpfV1gO1xuICAgIHJldHVybiAnJztcbn1cbmV4cG9ydHMuYXJyYXlTdHJpbmdpZnlIZWxwZXIgPSBhcnJheVN0cmluZ2lmeUhlbHBlcjtcbmZ1bmN0aW9uIGdldFBvb2xOYW1lKHBvb2wpIHtcbiAgICBjb25zdCBjID0gcG9vbC5jb25maWcuY29ubmVjdGlvbkNvbmZpZztcbiAgICBsZXQgcG9vbE5hbWUgPSAnJztcbiAgICBwb29sTmFtZSArPSBjLmhvc3QgPyBgaG9zdDogJyR7Yy5ob3N0fScsIGAgOiAnJztcbiAgICBwb29sTmFtZSArPSBjLnBvcnQgPyBgcG9ydDogJHtjLnBvcnR9LCBgIDogJyc7XG4gICAgcG9vbE5hbWUgKz0gYy5kYXRhYmFzZSA/IGBkYXRhYmFzZTogJyR7Yy5kYXRhYmFzZX0nLCBgIDogJyc7XG4gICAgcG9vbE5hbWUgKz0gYy51c2VyID8gYHVzZXI6ICcke2MudXNlcn0nYCA6ICcnO1xuICAgIGlmICghYy51c2VyKSB7XG4gICAgICAgIHBvb2xOYW1lID0gcG9vbE5hbWUuc3Vic3RyaW5nKDAsIHBvb2xOYW1lLmxlbmd0aCAtIDIpOyAvL29taXQgbGFzdCBjb21tYVxuICAgIH1cbiAgICByZXR1cm4gcG9vbE5hbWUudHJpbSgpO1xufVxuZXhwb3J0cy5nZXRQb29sTmFtZSA9IGdldFBvb2xOYW1lO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dXRpbHMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/utils.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/version.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/version.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.45.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-mysql';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ab69e4c556dff34942065695f09b9a90/node_modules/@opentelemetry/instrumentation-mysql/build/src/version.js\n");

/***/ })

};
;