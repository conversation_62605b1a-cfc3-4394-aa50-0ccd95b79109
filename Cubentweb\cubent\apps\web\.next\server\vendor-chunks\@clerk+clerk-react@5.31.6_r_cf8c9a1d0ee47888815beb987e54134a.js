"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@clerk+clerk-react@5.31.6_r_cf8c9a1d0ee47888815beb987e54134a";
exports.ids = ["vendor-chunks/@clerk+clerk-react@5.31.6_r_cf8c9a1d0ee47888815beb987e54134a"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/@clerk+clerk-react@5.31.6_r_cf8c9a1d0ee47888815beb987e54134a/node_modules/@clerk/clerk-react/dist/chunk-OANWQR3B.mjs":
/*!*****************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@clerk+clerk-react@5.31.6_r_cf8c9a1d0ee47888815beb987e54134a/node_modules/@clerk/clerk-react/dist/chunk-OANWQR3B.mjs ***!
  \*****************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __privateAdd: () => (/* binding */ __privateAdd),\n/* harmony export */   __privateGet: () => (/* binding */ __privateGet),\n/* harmony export */   __privateMethod: () => (/* binding */ __privateMethod),\n/* harmony export */   __privateSet: () => (/* binding */ __privateSet)\n/* harmony export */ });\nvar __typeError = (msg) => {\n  throw TypeError(msg);\n};\nvar __accessCheck = (obj, member, msg) => member.has(obj) || __typeError(\"Cannot \" + msg);\nvar __privateGet = (obj, member, getter) => (__accessCheck(obj, member, \"read from private field\"), getter ? getter.call(obj) : member.get(obj));\nvar __privateAdd = (obj, member, value) => member.has(obj) ? __typeError(\"Cannot add the same private member more than once\") : member instanceof WeakSet ? member.add(obj) : member.set(obj, value);\nvar __privateSet = (obj, member, value, setter) => (__accessCheck(obj, member, \"write to private field\"), setter ? setter.call(obj, value) : member.set(obj, value), value);\nvar __privateMethod = (obj, member, method) => (__accessCheck(obj, member, \"access private method\"), method);\n\n\n//# sourceMappingURL=chunk-OANWQR3B.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BjbGVyaytjbGVyay1yZWFjdEA1LjMxLjZfcl9jZjhjOWExZDBlZTQ3ODg4ODE1YmViOTg3ZTU0MTM0YS9ub2RlX21vZHVsZXMvQGNsZXJrL2NsZXJrLXJlYWN0L2Rpc3QvY2h1bmstT0FOV1FSM0IubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQU9FO0FBQ0YiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYW53YXJcXERvY3VtZW50c1xcQ3ViZW50d2ViXFxjdWJlbnRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBjbGVyaytjbGVyay1yZWFjdEA1LjMxLjZfcl9jZjhjOWExZDBlZTQ3ODg4ODE1YmViOTg3ZTU0MTM0YVxcbm9kZV9tb2R1bGVzXFxAY2xlcmtcXGNsZXJrLXJlYWN0XFxkaXN0XFxjaHVuay1PQU5XUVIzQi5tanMiXSwic291cmNlc0NvbnRlbnQiOlsidmFyIF9fdHlwZUVycm9yID0gKG1zZykgPT4ge1xuICB0aHJvdyBUeXBlRXJyb3IobXNnKTtcbn07XG52YXIgX19hY2Nlc3NDaGVjayA9IChvYmosIG1lbWJlciwgbXNnKSA9PiBtZW1iZXIuaGFzKG9iaikgfHwgX190eXBlRXJyb3IoXCJDYW5ub3QgXCIgKyBtc2cpO1xudmFyIF9fcHJpdmF0ZUdldCA9IChvYmosIG1lbWJlciwgZ2V0dGVyKSA9PiAoX19hY2Nlc3NDaGVjayhvYmosIG1lbWJlciwgXCJyZWFkIGZyb20gcHJpdmF0ZSBmaWVsZFwiKSwgZ2V0dGVyID8gZ2V0dGVyLmNhbGwob2JqKSA6IG1lbWJlci5nZXQob2JqKSk7XG52YXIgX19wcml2YXRlQWRkID0gKG9iaiwgbWVtYmVyLCB2YWx1ZSkgPT4gbWVtYmVyLmhhcyhvYmopID8gX190eXBlRXJyb3IoXCJDYW5ub3QgYWRkIHRoZSBzYW1lIHByaXZhdGUgbWVtYmVyIG1vcmUgdGhhbiBvbmNlXCIpIDogbWVtYmVyIGluc3RhbmNlb2YgV2Vha1NldCA/IG1lbWJlci5hZGQob2JqKSA6IG1lbWJlci5zZXQob2JqLCB2YWx1ZSk7XG52YXIgX19wcml2YXRlU2V0ID0gKG9iaiwgbWVtYmVyLCB2YWx1ZSwgc2V0dGVyKSA9PiAoX19hY2Nlc3NDaGVjayhvYmosIG1lbWJlciwgXCJ3cml0ZSB0byBwcml2YXRlIGZpZWxkXCIpLCBzZXR0ZXIgPyBzZXR0ZXIuY2FsbChvYmosIHZhbHVlKSA6IG1lbWJlci5zZXQob2JqLCB2YWx1ZSksIHZhbHVlKTtcbnZhciBfX3ByaXZhdGVNZXRob2QgPSAob2JqLCBtZW1iZXIsIG1ldGhvZCkgPT4gKF9fYWNjZXNzQ2hlY2sob2JqLCBtZW1iZXIsIFwiYWNjZXNzIHByaXZhdGUgbWV0aG9kXCIpLCBtZXRob2QpO1xuXG5leHBvcnQge1xuICBfX3ByaXZhdGVHZXQsXG4gIF9fcHJpdmF0ZUFkZCxcbiAgX19wcml2YXRlU2V0LFxuICBfX3ByaXZhdGVNZXRob2Rcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1jaHVuay1PQU5XUVIzQi5tanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@clerk+clerk-react@5.31.6_r_cf8c9a1d0ee47888815beb987e54134a/node_modules/@clerk/clerk-react/dist/chunk-OANWQR3B.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@clerk+clerk-react@5.31.6_r_cf8c9a1d0ee47888815beb987e54134a/node_modules/@clerk/clerk-react/dist/chunk-T2VIWQBM.mjs":
/*!*****************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@clerk+clerk-react@5.31.6_r_cf8c9a1d0ee47888815beb987e54134a/node_modules/@clerk/clerk-react/dist/chunk-T2VIWQBM.mjs ***!
  \*****************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthContext: () => (/* binding */ AuthContext),\n/* harmony export */   AuthenticateWithRedirectCallback: () => (/* binding */ AuthenticateWithRedirectCallback),\n/* harmony export */   ClerkDegraded: () => (/* binding */ ClerkDegraded),\n/* harmony export */   ClerkFailed: () => (/* binding */ ClerkFailed),\n/* harmony export */   ClerkLoaded: () => (/* binding */ ClerkLoaded),\n/* harmony export */   ClerkLoading: () => (/* binding */ ClerkLoading),\n/* harmony export */   IsomorphicClerkContext: () => (/* binding */ IsomorphicClerkContext),\n/* harmony export */   MultisessionAppSupport: () => (/* binding */ MultisessionAppSupport),\n/* harmony export */   Protect: () => (/* binding */ Protect),\n/* harmony export */   RedirectToCreateOrganization: () => (/* binding */ RedirectToCreateOrganization),\n/* harmony export */   RedirectToOrganizationProfile: () => (/* binding */ RedirectToOrganizationProfile),\n/* harmony export */   RedirectToSignIn: () => (/* binding */ RedirectToSignIn),\n/* harmony export */   RedirectToSignUp: () => (/* binding */ RedirectToSignUp),\n/* harmony export */   RedirectToUserProfile: () => (/* binding */ RedirectToUserProfile),\n/* harmony export */   SignedIn: () => (/* binding */ SignedIn),\n/* harmony export */   SignedOut: () => (/* binding */ SignedOut),\n/* harmony export */   customLinkWrongProps: () => (/* binding */ customLinkWrongProps),\n/* harmony export */   customMenuItemsIgnoredComponent: () => (/* binding */ customMenuItemsIgnoredComponent),\n/* harmony export */   customPageWrongProps: () => (/* binding */ customPageWrongProps),\n/* harmony export */   customPagesIgnoredComponent: () => (/* binding */ customPagesIgnoredComponent),\n/* harmony export */   errorThrower: () => (/* binding */ errorThrower),\n/* harmony export */   incompatibleRoutingWithPathProvidedError: () => (/* binding */ incompatibleRoutingWithPathProvidedError),\n/* harmony export */   multipleChildrenInButtonComponent: () => (/* binding */ multipleChildrenInButtonComponent),\n/* harmony export */   multipleClerkProvidersError: () => (/* binding */ multipleClerkProvidersError),\n/* harmony export */   noPathProvidedError: () => (/* binding */ noPathProvidedError),\n/* harmony export */   organizationProfileLinkRenderedError: () => (/* binding */ organizationProfileLinkRenderedError),\n/* harmony export */   organizationProfilePageRenderedError: () => (/* binding */ organizationProfilePageRenderedError),\n/* harmony export */   setErrorThrowerOptions: () => (/* binding */ setErrorThrowerOptions),\n/* harmony export */   unsupportedNonBrowserDomainOrProxyUrlFunction: () => (/* binding */ unsupportedNonBrowserDomainOrProxyUrlFunction),\n/* harmony export */   useAuth: () => (/* binding */ useAuth),\n/* harmony export */   useClerk: () => (/* reexport safe */ _clerk_shared_react__WEBPACK_IMPORTED_MODULE_4__.useClerk),\n/* harmony export */   useDerivedAuth: () => (/* binding */ useDerivedAuth),\n/* harmony export */   useEmailLink: () => (/* binding */ useEmailLink),\n/* harmony export */   useOrganization: () => (/* reexport safe */ _clerk_shared_react__WEBPACK_IMPORTED_MODULE_4__.useOrganization),\n/* harmony export */   useOrganizationList: () => (/* reexport safe */ _clerk_shared_react__WEBPACK_IMPORTED_MODULE_4__.useOrganizationList),\n/* harmony export */   useReverification: () => (/* reexport safe */ _clerk_shared_react__WEBPACK_IMPORTED_MODULE_4__.useReverification),\n/* harmony export */   useSession: () => (/* reexport safe */ _clerk_shared_react__WEBPACK_IMPORTED_MODULE_4__.useSession),\n/* harmony export */   useSessionList: () => (/* reexport safe */ _clerk_shared_react__WEBPACK_IMPORTED_MODULE_4__.useSessionList),\n/* harmony export */   useSignIn: () => (/* binding */ useSignIn),\n/* harmony export */   useSignUp: () => (/* binding */ useSignUp),\n/* harmony export */   useUser: () => (/* reexport safe */ _clerk_shared_react__WEBPACK_IMPORTED_MODULE_4__.useUser),\n/* harmony export */   userButtonIgnoredComponent: () => (/* binding */ userButtonIgnoredComponent),\n/* harmony export */   userButtonMenuActionRenderedError: () => (/* binding */ userButtonMenuActionRenderedError),\n/* harmony export */   userButtonMenuItemLinkWrongProps: () => (/* binding */ userButtonMenuItemLinkWrongProps),\n/* harmony export */   userButtonMenuItemsActionWrongsProps: () => (/* binding */ userButtonMenuItemsActionWrongsProps),\n/* harmony export */   userButtonMenuItemsRenderedError: () => (/* binding */ userButtonMenuItemsRenderedError),\n/* harmony export */   userButtonMenuLinkRenderedError: () => (/* binding */ userButtonMenuLinkRenderedError),\n/* harmony export */   userProfileLinkRenderedError: () => (/* binding */ userProfileLinkRenderedError),\n/* harmony export */   userProfilePageRenderedError: () => (/* binding */ userProfilePageRenderedError),\n/* harmony export */   withClerk: () => (/* binding */ withClerk)\n/* harmony export */ });\n/* harmony import */ var _clerk_shared_error__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @clerk/shared/error */ \"(ssr)/../../node_modules/.pnpm/@clerk+shared@3.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/@clerk/shared/dist/error.mjs\");\n/* harmony import */ var _clerk_shared_authorization__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @clerk/shared/authorization */ \"(ssr)/../../node_modules/.pnpm/@clerk+shared@3.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/@clerk/shared/dist/authorization.mjs\");\n/* harmony import */ var _clerk_shared_telemetry__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @clerk/shared/telemetry */ \"(ssr)/../../node_modules/.pnpm/@clerk+shared@3.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/@clerk/shared/dist/telemetry.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _clerk_shared_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @clerk/shared/react */ \"(ssr)/../../node_modules/.pnpm/@clerk+shared@3.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/@clerk/shared/dist/react/index.mjs\");\n/* harmony import */ var _clerk_shared_deprecated__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @clerk/shared/deprecated */ \"(ssr)/../../node_modules/.pnpm/@clerk+shared@3.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/@clerk/shared/dist/deprecated.mjs\");\n// src/errors/errorThrower.ts\n\nvar errorThrower = (0,_clerk_shared_error__WEBPACK_IMPORTED_MODULE_0__.buildErrorThrower)({ packageName: \"@clerk/clerk-react\" });\nfunction setErrorThrowerOptions(options) {\n  errorThrower.setMessages(options).setPackageName(options);\n}\n\n// src/hooks/useAuth.ts\n\n\n\n\n// src/contexts/AuthContext.ts\n\nvar [AuthContext, useAuthContext] = (0,_clerk_shared_react__WEBPACK_IMPORTED_MODULE_4__.createContextAndHook)(\"AuthContext\");\n\n// src/contexts/IsomorphicClerkContext.tsx\n\nvar IsomorphicClerkContext = _clerk_shared_react__WEBPACK_IMPORTED_MODULE_4__.ClerkInstanceContext;\nvar useIsomorphicClerkContext = _clerk_shared_react__WEBPACK_IMPORTED_MODULE_4__.useClerkInstanceContext;\n\n// src/errors/messages.ts\nvar multipleClerkProvidersError = \"You've added multiple <ClerkProvider> components in your React component tree. Wrap your components in a single <ClerkProvider>.\";\nvar multipleChildrenInButtonComponent = (name) => `You've passed multiple children components to <${name}/>. You can only pass a single child component or text.`;\nvar invalidStateError = \"Invalid state. Feel free to submit a bug or reach out to support here: https://clerk.com/support\";\nvar unsupportedNonBrowserDomainOrProxyUrlFunction = \"Unsupported usage of isSatellite, domain or proxyUrl. The usage of isSatellite, domain or proxyUrl as function is not supported in non-browser environments.\";\nvar userProfilePageRenderedError = \"<UserProfile.Page /> component needs to be a direct child of `<UserProfile />` or `<UserButton />`.\";\nvar userProfileLinkRenderedError = \"<UserProfile.Link /> component needs to be a direct child of `<UserProfile />` or `<UserButton />`.\";\nvar organizationProfilePageRenderedError = \"<OrganizationProfile.Page /> component needs to be a direct child of `<OrganizationProfile />` or `<OrganizationSwitcher />`.\";\nvar organizationProfileLinkRenderedError = \"<OrganizationProfile.Link /> component needs to be a direct child of `<OrganizationProfile />` or `<OrganizationSwitcher />`.\";\nvar customPagesIgnoredComponent = (componentName) => `<${componentName} /> can only accept <${componentName}.Page /> and <${componentName}.Link /> as its children. Any other provided component will be ignored. Additionally, please ensure that the component is rendered in a client component.`;\nvar customPageWrongProps = (componentName) => `Missing props. <${componentName}.Page /> component requires the following props: url, label, labelIcon, alongside with children to be rendered inside the page.`;\nvar customLinkWrongProps = (componentName) => `Missing props. <${componentName}.Link /> component requires the following props: url, label and labelIcon.`;\nvar noPathProvidedError = (componentName) => `The <${componentName}/> component uses path-based routing by default unless a different routing strategy is provided using the \\`routing\\` prop. When path-based routing is used, you need to provide the path where the component is mounted on by using the \\`path\\` prop. Example: <${componentName} path={'/my-path'} />`;\nvar incompatibleRoutingWithPathProvidedError = (componentName) => `The \\`path\\` prop will only be respected when the Clerk component uses path-based routing. To resolve this error, pass \\`routing='path'\\` to the <${componentName}/> component, or drop the \\`path\\` prop to switch to hash-based routing. For more details please refer to our docs: https://clerk.com/docs`;\nvar userButtonIgnoredComponent = `<UserButton /> can only accept <UserButton.UserProfilePage />, <UserButton.UserProfileLink /> and <UserButton.MenuItems /> as its children. Any other provided component will be ignored. Additionally, please ensure that the component is rendered in a client component.`;\nvar customMenuItemsIgnoredComponent = \"<UserButton.MenuItems /> component can only accept <UserButton.Action /> and <UserButton.Link /> as its children. Any other provided component will be ignored. Additionally, please ensure that the component is rendered in a client component.\";\nvar userButtonMenuItemsRenderedError = \"<UserButton.MenuItems /> component needs to be a direct child of `<UserButton />`.\";\nvar userButtonMenuActionRenderedError = \"<UserButton.Action /> component needs to be a direct child of `<UserButton.MenuItems />`.\";\nvar userButtonMenuLinkRenderedError = \"<UserButton.Link /> component needs to be a direct child of `<UserButton.MenuItems />`.\";\nvar userButtonMenuItemLinkWrongProps = \"Missing props. <UserButton.Link /> component requires the following props: href, label and labelIcon.\";\nvar userButtonMenuItemsActionWrongsProps = \"Missing props. <UserButton.Action /> component requires the following props: label.\";\n\n// src/hooks/useAssertWrappedByClerkProvider.ts\n\nvar useAssertWrappedByClerkProvider = (source) => {\n  (0,_clerk_shared_react__WEBPACK_IMPORTED_MODULE_4__.useAssertWrappedByClerkProvider)(() => {\n    errorThrower.throwMissingClerkProviderError({ source });\n  });\n};\n\n// src/hooks/utils.ts\nvar clerkLoaded = (isomorphicClerk) => {\n  return new Promise((resolve) => {\n    const handler = (status) => {\n      if ([\"ready\", \"degraded\"].includes(status)) {\n        resolve();\n        isomorphicClerk.off(\"status\", handler);\n      }\n    };\n    isomorphicClerk.on(\"status\", handler, { notify: true });\n  });\n};\nvar createGetToken = (isomorphicClerk) => {\n  return async (options) => {\n    await clerkLoaded(isomorphicClerk);\n    if (!isomorphicClerk.session) {\n      return null;\n    }\n    return isomorphicClerk.session.getToken(options);\n  };\n};\nvar createSignOut = (isomorphicClerk) => {\n  return async (...args) => {\n    await clerkLoaded(isomorphicClerk);\n    return isomorphicClerk.signOut(...args);\n  };\n};\n\n// src/hooks/useAuth.ts\nvar useAuth = (initialAuthStateOrOptions = {}) => {\n  var _a, _b;\n  useAssertWrappedByClerkProvider(\"useAuth\");\n  const { treatPendingAsSignedOut, ...rest } = initialAuthStateOrOptions != null ? initialAuthStateOrOptions : {};\n  const initialAuthState = rest;\n  const authContextFromHook = useAuthContext();\n  let authContext = authContextFromHook;\n  if (authContext.sessionId === void 0 && authContext.userId === void 0) {\n    authContext = initialAuthState != null ? initialAuthState : {};\n  }\n  const isomorphicClerk = useIsomorphicClerkContext();\n  const getToken = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(createGetToken(isomorphicClerk), [isomorphicClerk]);\n  const signOut = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(createSignOut(isomorphicClerk), [isomorphicClerk]);\n  (_a = isomorphicClerk.telemetry) == null ? void 0 : _a.record((0,_clerk_shared_telemetry__WEBPACK_IMPORTED_MODULE_2__.eventMethodCalled)(\"useAuth\", { treatPendingAsSignedOut }));\n  return useDerivedAuth(\n    {\n      ...authContext,\n      getToken,\n      signOut\n    },\n    {\n      treatPendingAsSignedOut: treatPendingAsSignedOut != null ? treatPendingAsSignedOut : (_b = isomorphicClerk.__internal_getOption) == null ? void 0 : _b.call(isomorphicClerk, \"treatPendingAsSignedOut\")\n    }\n  );\n};\nfunction useDerivedAuth(authObject, { treatPendingAsSignedOut = true } = {}) {\n  const { userId, orgId, orgRole, has, signOut, getToken, orgPermissions, factorVerificationAge, sessionClaims } = authObject != null ? authObject : {};\n  const derivedHas = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(\n    (params) => {\n      if (has) {\n        return has(params);\n      }\n      return (0,_clerk_shared_authorization__WEBPACK_IMPORTED_MODULE_1__.createCheckAuthorization)({\n        userId,\n        orgId,\n        orgRole,\n        orgPermissions,\n        factorVerificationAge,\n        features: (sessionClaims == null ? void 0 : sessionClaims.fea) || \"\",\n        plans: (sessionClaims == null ? void 0 : sessionClaims.pla) || \"\"\n      })(params);\n    },\n    [has, userId, orgId, orgRole, orgPermissions, factorVerificationAge]\n  );\n  const payload = (0,_clerk_shared_authorization__WEBPACK_IMPORTED_MODULE_1__.resolveAuthState)({\n    authObject: {\n      ...authObject,\n      getToken,\n      signOut,\n      has: derivedHas\n    },\n    options: {\n      treatPendingAsSignedOut\n    }\n  });\n  if (!payload) {\n    return errorThrower.throw(invalidStateError);\n  }\n  return payload;\n}\n\n// src/hooks/useEmailLink.ts\n\nfunction useEmailLink(resource) {\n  const { startEmailLinkFlow, cancelEmailLinkFlow } = react__WEBPACK_IMPORTED_MODULE_3__.useMemo(() => resource.createEmailLinkFlow(), [resource]);\n  react__WEBPACK_IMPORTED_MODULE_3__.useEffect(() => {\n    return cancelEmailLinkFlow;\n  }, []);\n  return {\n    startEmailLinkFlow,\n    cancelEmailLinkFlow\n  };\n}\n\n// src/hooks/useSignIn.ts\n\n\nvar useSignIn = () => {\n  var _a;\n  useAssertWrappedByClerkProvider(\"useSignIn\");\n  const isomorphicClerk = useIsomorphicClerkContext();\n  const client = (0,_clerk_shared_react__WEBPACK_IMPORTED_MODULE_4__.useClientContext)();\n  (_a = isomorphicClerk.telemetry) == null ? void 0 : _a.record((0,_clerk_shared_telemetry__WEBPACK_IMPORTED_MODULE_2__.eventMethodCalled)(\"useSignIn\"));\n  if (!client) {\n    return { isLoaded: false, signIn: void 0, setActive: void 0 };\n  }\n  return {\n    isLoaded: true,\n    signIn: client.signIn,\n    setActive: isomorphicClerk.setActive\n  };\n};\n\n// src/hooks/useSignUp.ts\n\n\nvar useSignUp = () => {\n  var _a;\n  useAssertWrappedByClerkProvider(\"useSignUp\");\n  const isomorphicClerk = useIsomorphicClerkContext();\n  const client = (0,_clerk_shared_react__WEBPACK_IMPORTED_MODULE_4__.useClientContext)();\n  (_a = isomorphicClerk.telemetry) == null ? void 0 : _a.record((0,_clerk_shared_telemetry__WEBPACK_IMPORTED_MODULE_2__.eventMethodCalled)(\"useSignUp\"));\n  if (!client) {\n    return { isLoaded: false, signUp: void 0, setActive: void 0 };\n  }\n  return {\n    isLoaded: true,\n    signUp: client.signUp,\n    setActive: isomorphicClerk.setActive\n  };\n};\n\n// src/hooks/index.ts\n\n\n// src/components/controlComponents.tsx\n\n\n\n// src/contexts/SessionContext.tsx\n\n\n// src/components/withClerk.tsx\n\nvar withClerk = (Component, displayNameOrOptions) => {\n  const passedDisplayedName = typeof displayNameOrOptions === \"string\" ? displayNameOrOptions : displayNameOrOptions == null ? void 0 : displayNameOrOptions.component;\n  const displayName = passedDisplayedName || Component.displayName || Component.name || \"Component\";\n  Component.displayName = displayName;\n  const options = typeof displayNameOrOptions === \"string\" ? void 0 : displayNameOrOptions;\n  const HOC = (props) => {\n    useAssertWrappedByClerkProvider(displayName || \"withClerk\");\n    const clerk = useIsomorphicClerkContext();\n    if (!clerk.loaded && !(options == null ? void 0 : options.renderWhileLoading)) {\n      return null;\n    }\n    return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_3__.createElement(\n      Component,\n      {\n        ...props,\n        component: displayName,\n        clerk\n      }\n    );\n  };\n  HOC.displayName = `withClerk(${displayName})`;\n  return HOC;\n};\n\n// src/components/controlComponents.tsx\nvar SignedIn = ({ children, treatPendingAsSignedOut }) => {\n  useAssertWrappedByClerkProvider(\"SignedIn\");\n  const { userId } = useAuth({ treatPendingAsSignedOut });\n  if (userId) {\n    return children;\n  }\n  return null;\n};\nvar SignedOut = ({ children, treatPendingAsSignedOut }) => {\n  useAssertWrappedByClerkProvider(\"SignedOut\");\n  const { userId } = useAuth({ treatPendingAsSignedOut });\n  if (userId === null) {\n    return children;\n  }\n  return null;\n};\nvar ClerkLoaded = ({ children }) => {\n  useAssertWrappedByClerkProvider(\"ClerkLoaded\");\n  const isomorphicClerk = useIsomorphicClerkContext();\n  if (!isomorphicClerk.loaded) {\n    return null;\n  }\n  return children;\n};\nvar ClerkLoading = ({ children }) => {\n  useAssertWrappedByClerkProvider(\"ClerkLoading\");\n  const isomorphicClerk = useIsomorphicClerkContext();\n  if (isomorphicClerk.status !== \"loading\") {\n    return null;\n  }\n  return children;\n};\nvar ClerkFailed = ({ children }) => {\n  useAssertWrappedByClerkProvider(\"ClerkFailed\");\n  const isomorphicClerk = useIsomorphicClerkContext();\n  if (isomorphicClerk.status !== \"error\") {\n    return null;\n  }\n  return children;\n};\nvar ClerkDegraded = ({ children }) => {\n  useAssertWrappedByClerkProvider(\"ClerkDegraded\");\n  const isomorphicClerk = useIsomorphicClerkContext();\n  if (isomorphicClerk.status !== \"degraded\") {\n    return null;\n  }\n  return children;\n};\nvar Protect = ({ children, fallback, treatPendingAsSignedOut, ...restAuthorizedParams }) => {\n  useAssertWrappedByClerkProvider(\"Protect\");\n  const { isLoaded, has, userId } = useAuth({ treatPendingAsSignedOut });\n  if (!isLoaded) {\n    return null;\n  }\n  const unauthorized = fallback != null ? fallback : null;\n  const authorized = children;\n  if (!userId) {\n    return unauthorized;\n  }\n  if (typeof restAuthorizedParams.condition === \"function\") {\n    if (restAuthorizedParams.condition(has)) {\n      return authorized;\n    }\n    return unauthorized;\n  }\n  if (restAuthorizedParams.role || restAuthorizedParams.permission || restAuthorizedParams.feature || restAuthorizedParams.plan) {\n    if (has(restAuthorizedParams)) {\n      return authorized;\n    }\n    return unauthorized;\n  }\n  return authorized;\n};\nvar RedirectToSignIn = withClerk(({ clerk, ...props }) => {\n  const { client, session } = clerk;\n  const hasSignedInSessions = client.signedInSessions ? client.signedInSessions.length > 0 : (\n    // Compat for clerk-js<5.54.0 (which was released with the `signedInSessions` property)\n    client.activeSessions && client.activeSessions.length > 0\n  );\n  react__WEBPACK_IMPORTED_MODULE_3__.useEffect(() => {\n    if (session === null && hasSignedInSessions) {\n      void clerk.redirectToAfterSignOut();\n    } else {\n      void clerk.redirectToSignIn(props);\n    }\n  }, []);\n  return null;\n}, \"RedirectToSignIn\");\nvar RedirectToSignUp = withClerk(({ clerk, ...props }) => {\n  react__WEBPACK_IMPORTED_MODULE_3__.useEffect(() => {\n    void clerk.redirectToSignUp(props);\n  }, []);\n  return null;\n}, \"RedirectToSignUp\");\nvar RedirectToUserProfile = withClerk(({ clerk }) => {\n  react__WEBPACK_IMPORTED_MODULE_3__.useEffect(() => {\n    (0,_clerk_shared_deprecated__WEBPACK_IMPORTED_MODULE_5__.deprecated)(\"RedirectToUserProfile\", \"Use the `redirectToUserProfile()` method instead.\");\n    void clerk.redirectToUserProfile();\n  }, []);\n  return null;\n}, \"RedirectToUserProfile\");\nvar RedirectToOrganizationProfile = withClerk(({ clerk }) => {\n  react__WEBPACK_IMPORTED_MODULE_3__.useEffect(() => {\n    (0,_clerk_shared_deprecated__WEBPACK_IMPORTED_MODULE_5__.deprecated)(\"RedirectToOrganizationProfile\", \"Use the `redirectToOrganizationProfile()` method instead.\");\n    void clerk.redirectToOrganizationProfile();\n  }, []);\n  return null;\n}, \"RedirectToOrganizationProfile\");\nvar RedirectToCreateOrganization = withClerk(({ clerk }) => {\n  react__WEBPACK_IMPORTED_MODULE_3__.useEffect(() => {\n    (0,_clerk_shared_deprecated__WEBPACK_IMPORTED_MODULE_5__.deprecated)(\"RedirectToCreateOrganization\", \"Use the `redirectToCreateOrganization()` method instead.\");\n    void clerk.redirectToCreateOrganization();\n  }, []);\n  return null;\n}, \"RedirectToCreateOrganization\");\nvar AuthenticateWithRedirectCallback = withClerk(\n  ({ clerk, ...handleRedirectCallbackParams }) => {\n    react__WEBPACK_IMPORTED_MODULE_3__.useEffect(() => {\n      void clerk.handleRedirectCallback(handleRedirectCallbackParams);\n    }, []);\n    return null;\n  },\n  \"AuthenticateWithRedirectCallback\"\n);\nvar MultisessionAppSupport = ({ children }) => {\n  useAssertWrappedByClerkProvider(\"MultisessionAppSupport\");\n  const session = (0,_clerk_shared_react__WEBPACK_IMPORTED_MODULE_4__.useSessionContext)();\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_3__.createElement(react__WEBPACK_IMPORTED_MODULE_3__.Fragment, { key: session ? session.id : \"no-users\" }, children);\n};\n\n\n//# sourceMappingURL=chunk-T2VIWQBM.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@clerk+clerk-react@5.31.6_r_cf8c9a1d0ee47888815beb987e54134a/node_modules/@clerk/clerk-react/dist/chunk-T2VIWQBM.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@clerk+clerk-react@5.31.6_r_cf8c9a1d0ee47888815beb987e54134a/node_modules/@clerk/clerk-react/dist/index.mjs":
/*!********************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@clerk+clerk-react@5.31.6_r_cf8c9a1d0ee47888815beb987e54134a/node_modules/@clerk/clerk-react/dist/index.mjs ***!
  \********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthenticateWithRedirectCallback: () => (/* reexport safe */ _chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.AuthenticateWithRedirectCallback),\n/* harmony export */   ClerkDegraded: () => (/* reexport safe */ _chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.ClerkDegraded),\n/* harmony export */   ClerkFailed: () => (/* reexport safe */ _chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.ClerkFailed),\n/* harmony export */   ClerkLoaded: () => (/* reexport safe */ _chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.ClerkLoaded),\n/* harmony export */   ClerkLoading: () => (/* reexport safe */ _chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.ClerkLoading),\n/* harmony export */   ClerkProvider: () => (/* binding */ ClerkProvider),\n/* harmony export */   CreateOrganization: () => (/* binding */ CreateOrganization),\n/* harmony export */   GoogleOneTap: () => (/* binding */ GoogleOneTap),\n/* harmony export */   OrganizationList: () => (/* binding */ OrganizationList),\n/* harmony export */   OrganizationProfile: () => (/* binding */ OrganizationProfile),\n/* harmony export */   OrganizationSwitcher: () => (/* binding */ OrganizationSwitcher),\n/* harmony export */   PricingTable: () => (/* binding */ PricingTable),\n/* harmony export */   Protect: () => (/* reexport safe */ _chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.Protect),\n/* harmony export */   RedirectToCreateOrganization: () => (/* reexport safe */ _chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.RedirectToCreateOrganization),\n/* harmony export */   RedirectToOrganizationProfile: () => (/* reexport safe */ _chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.RedirectToOrganizationProfile),\n/* harmony export */   RedirectToSignIn: () => (/* reexport safe */ _chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.RedirectToSignIn),\n/* harmony export */   RedirectToSignUp: () => (/* reexport safe */ _chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.RedirectToSignUp),\n/* harmony export */   RedirectToUserProfile: () => (/* reexport safe */ _chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.RedirectToUserProfile),\n/* harmony export */   SignIn: () => (/* binding */ SignIn),\n/* harmony export */   SignInButton: () => (/* binding */ SignInButton),\n/* harmony export */   SignInWithMetamaskButton: () => (/* binding */ SignInWithMetamaskButton),\n/* harmony export */   SignOutButton: () => (/* binding */ SignOutButton),\n/* harmony export */   SignUp: () => (/* binding */ SignUp),\n/* harmony export */   SignUpButton: () => (/* binding */ SignUpButton),\n/* harmony export */   SignedIn: () => (/* reexport safe */ _chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.SignedIn),\n/* harmony export */   SignedOut: () => (/* reexport safe */ _chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.SignedOut),\n/* harmony export */   UserButton: () => (/* binding */ UserButton),\n/* harmony export */   UserProfile: () => (/* binding */ UserProfile),\n/* harmony export */   Waitlist: () => (/* binding */ Waitlist),\n/* harmony export */   useAuth: () => (/* reexport safe */ _chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.useAuth),\n/* harmony export */   useClerk: () => (/* reexport safe */ _chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.useClerk),\n/* harmony export */   useEmailLink: () => (/* reexport safe */ _chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.useEmailLink),\n/* harmony export */   useOrganization: () => (/* reexport safe */ _chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.useOrganization),\n/* harmony export */   useOrganizationList: () => (/* reexport safe */ _chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.useOrganizationList),\n/* harmony export */   useReverification: () => (/* reexport safe */ _chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.useReverification),\n/* harmony export */   useSession: () => (/* reexport safe */ _chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.useSession),\n/* harmony export */   useSessionList: () => (/* reexport safe */ _chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.useSessionList),\n/* harmony export */   useSignIn: () => (/* reexport safe */ _chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.useSignIn),\n/* harmony export */   useSignUp: () => (/* reexport safe */ _chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.useSignUp),\n/* harmony export */   useUser: () => (/* reexport safe */ _chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.useUser)\n/* harmony export */ });\n/* harmony import */ var _chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-T2VIWQBM.mjs */ \"(ssr)/../../node_modules/.pnpm/@clerk+clerk-react@5.31.6_r_cf8c9a1d0ee47888815beb987e54134a/node_modules/@clerk/clerk-react/dist/chunk-T2VIWQBM.mjs\");\n/* harmony import */ var _chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-OANWQR3B.mjs */ \"(ssr)/../../node_modules/.pnpm/@clerk+clerk-react@5.31.6_r_cf8c9a1d0ee47888815beb987e54134a/node_modules/@clerk/clerk-react/dist/chunk-OANWQR3B.mjs\");\n/* harmony import */ var _clerk_shared_loadClerkJsScript__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @clerk/shared/loadClerkJsScript */ \"(ssr)/../../node_modules/.pnpm/@clerk+shared@3.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/@clerk/shared/dist/loadClerkJsScript.mjs\");\n/* harmony import */ var _clerk_shared_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @clerk/shared/utils */ \"(ssr)/../../node_modules/.pnpm/@clerk+shared@3.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/@clerk/shared/dist/utils/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-dom */ \"(ssr)/../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _clerk_shared_object__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @clerk/shared/object */ \"(ssr)/../../node_modules/.pnpm/@clerk+shared@3.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/@clerk/shared/dist/object.mjs\");\n/* harmony import */ var _clerk_shared_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @clerk/shared/react */ \"(ssr)/../../node_modules/.pnpm/@clerk+shared@3.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/@clerk/shared/dist/react/index.mjs\");\n/* harmony import */ var _clerk_shared_keys__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @clerk/shared/keys */ \"(ssr)/../../node_modules/.pnpm/@clerk+shared@3.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/@clerk/shared/dist/keys.mjs\");\n/* harmony import */ var _clerk_shared_deriveState__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @clerk/shared/deriveState */ \"(ssr)/../../node_modules/.pnpm/@clerk+shared@3.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/@clerk/shared/dist/deriveState.mjs\");\n/* harmony import */ var _clerk_shared_browser__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @clerk/shared/browser */ \"(ssr)/../../node_modules/.pnpm/@clerk+shared@3.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/@clerk/shared/dist/browser.mjs\");\n/* harmony import */ var _clerk_shared_clerkEventBus__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @clerk/shared/clerkEventBus */ \"(ssr)/../../node_modules/.pnpm/@clerk+shared@3.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/@clerk/shared/dist/clerkEventBus.mjs\");\n\n\n\n// src/polyfills.ts\nif (typeof window !== \"undefined\" && !window.global) {\n  window.global = typeof global === \"undefined\" ? window : global;\n}\n\n// src/index.ts\n\n\n// src/components/uiComponents.tsx\n\n\n\n// src/utils/childrenUtils.tsx\n\nvar assertSingleChild = (children) => (name) => {\n  try {\n    return react__WEBPACK_IMPORTED_MODULE_4__.Children.only(children);\n  } catch {\n    return _chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.errorThrower.throw((0,_chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.multipleChildrenInButtonComponent)(name));\n  }\n};\nvar normalizeWithDefaultValue = (children, defaultText) => {\n  if (!children) {\n    children = defaultText;\n  }\n  if (typeof children === \"string\") {\n    children = /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_4__.createElement(\"button\", null, children);\n  }\n  return children;\n};\nvar safeExecute = (cb) => (...args) => {\n  if (cb && typeof cb === \"function\") {\n    return cb(...args);\n  }\n};\n\n// src/utils/isConstructor.ts\nfunction isConstructor(f) {\n  return typeof f === \"function\";\n}\n\n// src/utils/useMaxAllowedInstancesGuard.tsx\n\nvar counts = /* @__PURE__ */ new Map();\nfunction useMaxAllowedInstancesGuard(name, error, maxCount = 1) {\n  react__WEBPACK_IMPORTED_MODULE_4__.useEffect(() => {\n    const count = counts.get(name) || 0;\n    if (count == maxCount) {\n      return _chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.errorThrower.throw(error);\n    }\n    counts.set(name, count + 1);\n    return () => {\n      counts.set(name, (counts.get(name) || 1) - 1);\n    };\n  }, []);\n}\nfunction withMaxAllowedInstancesGuard(WrappedComponent, name, error) {\n  const displayName = WrappedComponent.displayName || WrappedComponent.name || name || \"Component\";\n  const Hoc = (props) => {\n    useMaxAllowedInstancesGuard(name, error);\n    return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_4__.createElement(WrappedComponent, { ...props });\n  };\n  Hoc.displayName = `withMaxAllowedInstancesGuard(${displayName})`;\n  return Hoc;\n}\n\n// src/utils/useCustomElementPortal.tsx\n\n\nvar useCustomElementPortal = (elements) => {\n  const initialState = Array(elements.length).fill(null);\n  const [nodes, setNodes] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(initialState);\n  return elements.map((el, index) => ({\n    id: el.id,\n    mount: (node) => setNodes((prevState) => prevState.map((n, i) => i === index ? node : n)),\n    unmount: () => setNodes((prevState) => prevState.map((n, i) => i === index ? null : n)),\n    portal: () => /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_4__.createElement(react__WEBPACK_IMPORTED_MODULE_4__.Fragment, null, nodes[index] ? (0,react_dom__WEBPACK_IMPORTED_MODULE_5__.createPortal)(el.component, nodes[index]) : null)\n  }));\n};\n\n// src/utils/useCustomPages.tsx\n\n\n\n// src/utils/componentValidation.ts\n\nvar isThatComponent = (v, component) => {\n  return !!v && react__WEBPACK_IMPORTED_MODULE_4__.isValidElement(v) && (v == null ? void 0 : v.type) === component;\n};\n\n// src/utils/useCustomPages.tsx\nvar useUserProfileCustomPages = (children, options) => {\n  const reorderItemsLabels = [\"account\", \"security\"];\n  return useCustomPages(\n    {\n      children,\n      reorderItemsLabels,\n      LinkComponent: UserProfileLink,\n      PageComponent: UserProfilePage,\n      MenuItemsComponent: MenuItems,\n      componentName: \"UserProfile\"\n    },\n    options\n  );\n};\nvar useOrganizationProfileCustomPages = (children, options) => {\n  const reorderItemsLabels = [\"general\", \"members\"];\n  return useCustomPages(\n    {\n      children,\n      reorderItemsLabels,\n      LinkComponent: OrganizationProfileLink,\n      PageComponent: OrganizationProfilePage,\n      componentName: \"OrganizationProfile\"\n    },\n    options\n  );\n};\nvar useSanitizedChildren = (children) => {\n  const sanitizedChildren = [];\n  const excludedComponents = [\n    OrganizationProfileLink,\n    OrganizationProfilePage,\n    MenuItems,\n    UserProfilePage,\n    UserProfileLink\n  ];\n  react__WEBPACK_IMPORTED_MODULE_4__.Children.forEach(children, (child) => {\n    if (!excludedComponents.some((component) => isThatComponent(child, component))) {\n      sanitizedChildren.push(child);\n    }\n  });\n  return sanitizedChildren;\n};\nvar useCustomPages = (params, options) => {\n  const { children, LinkComponent, PageComponent, MenuItemsComponent, reorderItemsLabels, componentName } = params;\n  const { allowForAnyChildren = false } = options || {};\n  const validChildren = [];\n  react__WEBPACK_IMPORTED_MODULE_4__.Children.forEach(children, (child) => {\n    if (!isThatComponent(child, PageComponent) && !isThatComponent(child, LinkComponent) && !isThatComponent(child, MenuItemsComponent)) {\n      if (child && !allowForAnyChildren) {\n        (0,_clerk_shared_utils__WEBPACK_IMPORTED_MODULE_3__.logErrorInDevMode)((0,_chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.customPagesIgnoredComponent)(componentName));\n      }\n      return;\n    }\n    const { props } = child;\n    const { children: children2, label, url, labelIcon } = props;\n    if (isThatComponent(child, PageComponent)) {\n      if (isReorderItem(props, reorderItemsLabels)) {\n        validChildren.push({ label });\n      } else if (isCustomPage(props)) {\n        validChildren.push({ label, labelIcon, children: children2, url });\n      } else {\n        (0,_clerk_shared_utils__WEBPACK_IMPORTED_MODULE_3__.logErrorInDevMode)((0,_chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.customPageWrongProps)(componentName));\n        return;\n      }\n    }\n    if (isThatComponent(child, LinkComponent)) {\n      if (isExternalLink(props)) {\n        validChildren.push({ label, labelIcon, url });\n      } else {\n        (0,_clerk_shared_utils__WEBPACK_IMPORTED_MODULE_3__.logErrorInDevMode)((0,_chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.customLinkWrongProps)(componentName));\n        return;\n      }\n    }\n  });\n  const customPageContents = [];\n  const customPageLabelIcons = [];\n  const customLinkLabelIcons = [];\n  validChildren.forEach((cp, index) => {\n    if (isCustomPage(cp)) {\n      customPageContents.push({ component: cp.children, id: index });\n      customPageLabelIcons.push({ component: cp.labelIcon, id: index });\n      return;\n    }\n    if (isExternalLink(cp)) {\n      customLinkLabelIcons.push({ component: cp.labelIcon, id: index });\n    }\n  });\n  const customPageContentsPortals = useCustomElementPortal(customPageContents);\n  const customPageLabelIconsPortals = useCustomElementPortal(customPageLabelIcons);\n  const customLinkLabelIconsPortals = useCustomElementPortal(customLinkLabelIcons);\n  const customPages = [];\n  const customPagesPortals = [];\n  validChildren.forEach((cp, index) => {\n    if (isReorderItem(cp, reorderItemsLabels)) {\n      customPages.push({ label: cp.label });\n      return;\n    }\n    if (isCustomPage(cp)) {\n      const {\n        portal: contentPortal,\n        mount,\n        unmount\n      } = customPageContentsPortals.find((p) => p.id === index);\n      const {\n        portal: labelPortal,\n        mount: mountIcon,\n        unmount: unmountIcon\n      } = customPageLabelIconsPortals.find((p) => p.id === index);\n      customPages.push({ label: cp.label, url: cp.url, mount, unmount, mountIcon, unmountIcon });\n      customPagesPortals.push(contentPortal);\n      customPagesPortals.push(labelPortal);\n      return;\n    }\n    if (isExternalLink(cp)) {\n      const {\n        portal: labelPortal,\n        mount: mountIcon,\n        unmount: unmountIcon\n      } = customLinkLabelIconsPortals.find((p) => p.id === index);\n      customPages.push({ label: cp.label, url: cp.url, mountIcon, unmountIcon });\n      customPagesPortals.push(labelPortal);\n      return;\n    }\n  });\n  return { customPages, customPagesPortals };\n};\nvar isReorderItem = (childProps, validItems) => {\n  const { children, label, url, labelIcon } = childProps;\n  return !children && !url && !labelIcon && validItems.some((v) => v === label);\n};\nvar isCustomPage = (childProps) => {\n  const { children, label, url, labelIcon } = childProps;\n  return !!children && !!url && !!labelIcon && !!label;\n};\nvar isExternalLink = (childProps) => {\n  const { children, label, url, labelIcon } = childProps;\n  return !children && !!url && !!labelIcon && !!label;\n};\n\n// src/utils/useCustomMenuItems.tsx\n\n\nvar useUserButtonCustomMenuItems = (children) => {\n  const reorderItemsLabels = [\"manageAccount\", \"signOut\"];\n  return useCustomMenuItems({\n    children,\n    reorderItemsLabels,\n    MenuItemsComponent: MenuItems,\n    MenuActionComponent: MenuAction,\n    MenuLinkComponent: MenuLink,\n    UserProfileLinkComponent: UserProfileLink,\n    UserProfilePageComponent: UserProfilePage\n  });\n};\nvar useCustomMenuItems = ({\n  children,\n  MenuItemsComponent,\n  MenuActionComponent,\n  MenuLinkComponent,\n  UserProfileLinkComponent,\n  UserProfilePageComponent,\n  reorderItemsLabels\n}) => {\n  const validChildren = [];\n  const customMenuItems = [];\n  const customMenuItemsPortals = [];\n  react__WEBPACK_IMPORTED_MODULE_4__.Children.forEach(children, (child) => {\n    if (!isThatComponent(child, MenuItemsComponent) && !isThatComponent(child, UserProfileLinkComponent) && !isThatComponent(child, UserProfilePageComponent)) {\n      if (child) {\n        (0,_clerk_shared_utils__WEBPACK_IMPORTED_MODULE_3__.logErrorInDevMode)(_chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.userButtonIgnoredComponent);\n      }\n      return;\n    }\n    if (isThatComponent(child, UserProfileLinkComponent) || isThatComponent(child, UserProfilePageComponent)) {\n      return;\n    }\n    const { props } = child;\n    react__WEBPACK_IMPORTED_MODULE_4__.Children.forEach(props.children, (child2) => {\n      if (!isThatComponent(child2, MenuActionComponent) && !isThatComponent(child2, MenuLinkComponent)) {\n        if (child2) {\n          (0,_clerk_shared_utils__WEBPACK_IMPORTED_MODULE_3__.logErrorInDevMode)(_chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.customMenuItemsIgnoredComponent);\n        }\n        return;\n      }\n      const { props: props2 } = child2;\n      const { label, labelIcon, href, onClick, open } = props2;\n      if (isThatComponent(child2, MenuActionComponent)) {\n        if (isReorderItem2(props2, reorderItemsLabels)) {\n          validChildren.push({ label });\n        } else if (isCustomMenuItem(props2)) {\n          const baseItem = {\n            label,\n            labelIcon\n          };\n          if (onClick !== void 0) {\n            validChildren.push({\n              ...baseItem,\n              onClick\n            });\n          } else if (open !== void 0) {\n            validChildren.push({\n              ...baseItem,\n              open: open.startsWith(\"/\") ? open : `/${open}`\n            });\n          } else {\n            (0,_clerk_shared_utils__WEBPACK_IMPORTED_MODULE_3__.logErrorInDevMode)(\"Custom menu item must have either onClick or open property\");\n            return;\n          }\n        } else {\n          (0,_clerk_shared_utils__WEBPACK_IMPORTED_MODULE_3__.logErrorInDevMode)(_chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.userButtonMenuItemsActionWrongsProps);\n          return;\n        }\n      }\n      if (isThatComponent(child2, MenuLinkComponent)) {\n        if (isExternalLink2(props2)) {\n          validChildren.push({ label, labelIcon, href });\n        } else {\n          (0,_clerk_shared_utils__WEBPACK_IMPORTED_MODULE_3__.logErrorInDevMode)(_chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.userButtonMenuItemLinkWrongProps);\n          return;\n        }\n      }\n    });\n  });\n  const customMenuItemLabelIcons = [];\n  const customLinkLabelIcons = [];\n  validChildren.forEach((mi, index) => {\n    if (isCustomMenuItem(mi)) {\n      customMenuItemLabelIcons.push({ component: mi.labelIcon, id: index });\n    }\n    if (isExternalLink2(mi)) {\n      customLinkLabelIcons.push({ component: mi.labelIcon, id: index });\n    }\n  });\n  const customMenuItemLabelIconsPortals = useCustomElementPortal(customMenuItemLabelIcons);\n  const customLinkLabelIconsPortals = useCustomElementPortal(customLinkLabelIcons);\n  validChildren.forEach((mi, index) => {\n    if (isReorderItem2(mi, reorderItemsLabels)) {\n      customMenuItems.push({\n        label: mi.label\n      });\n    }\n    if (isCustomMenuItem(mi)) {\n      const {\n        portal: iconPortal,\n        mount: mountIcon,\n        unmount: unmountIcon\n      } = customMenuItemLabelIconsPortals.find((p) => p.id === index);\n      const menuItem = {\n        label: mi.label,\n        mountIcon,\n        unmountIcon\n      };\n      if (\"onClick\" in mi) {\n        menuItem.onClick = mi.onClick;\n      } else if (\"open\" in mi) {\n        menuItem.open = mi.open;\n      }\n      customMenuItems.push(menuItem);\n      customMenuItemsPortals.push(iconPortal);\n    }\n    if (isExternalLink2(mi)) {\n      const {\n        portal: iconPortal,\n        mount: mountIcon,\n        unmount: unmountIcon\n      } = customLinkLabelIconsPortals.find((p) => p.id === index);\n      customMenuItems.push({\n        label: mi.label,\n        href: mi.href,\n        mountIcon,\n        unmountIcon\n      });\n      customMenuItemsPortals.push(iconPortal);\n    }\n  });\n  return { customMenuItems, customMenuItemsPortals };\n};\nvar isReorderItem2 = (childProps, validItems) => {\n  const { children, label, onClick, labelIcon } = childProps;\n  return !children && !onClick && !labelIcon && validItems.some((v) => v === label);\n};\nvar isCustomMenuItem = (childProps) => {\n  const { label, labelIcon, onClick, open } = childProps;\n  return !!labelIcon && !!label && (typeof onClick === \"function\" || typeof open === \"string\");\n};\nvar isExternalLink2 = (childProps) => {\n  const { label, href, labelIcon } = childProps;\n  return !!href && !!labelIcon && !!label;\n};\n\n// src/utils/useWaitForComponentMount.ts\n\nfunction waitForElementChildren(options) {\n  const { root = document == null ? void 0 : document.body, selector, timeout = 0 } = options;\n  return new Promise((resolve, reject) => {\n    if (!root) {\n      reject(new Error(\"No root element provided\"));\n      return;\n    }\n    let elementToWatch = root;\n    if (selector) {\n      elementToWatch = root == null ? void 0 : root.querySelector(selector);\n    }\n    const isElementAlreadyPresent = (elementToWatch == null ? void 0 : elementToWatch.childElementCount) && elementToWatch.childElementCount > 0;\n    if (isElementAlreadyPresent) {\n      resolve();\n      return;\n    }\n    const observer = new MutationObserver((mutationsList) => {\n      for (const mutation of mutationsList) {\n        if (mutation.type === \"childList\") {\n          if (!elementToWatch && selector) {\n            elementToWatch = root == null ? void 0 : root.querySelector(selector);\n          }\n          if ((elementToWatch == null ? void 0 : elementToWatch.childElementCount) && elementToWatch.childElementCount > 0) {\n            observer.disconnect();\n            resolve();\n            return;\n          }\n        }\n      }\n    });\n    observer.observe(root, { childList: true, subtree: true });\n    if (timeout > 0) {\n      setTimeout(() => {\n        observer.disconnect();\n        reject(new Error(`Timeout waiting for element children`));\n      }, timeout);\n    }\n  });\n}\nfunction useWaitForComponentMount(component) {\n  const watcherRef = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)();\n  const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(\"rendering\");\n  (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(() => {\n    if (!component) {\n      throw new Error(\"Clerk: no component name provided, unable to detect mount.\");\n    }\n    if (typeof window !== \"undefined\" && !watcherRef.current) {\n      watcherRef.current = waitForElementChildren({ selector: `[data-clerk-component=\"${component}\"]` }).then(() => {\n        setStatus(\"rendered\");\n      }).catch(() => {\n        setStatus(\"error\");\n      });\n    }\n  }, [component]);\n  return status;\n}\n\n// src/components/ClerkHostRenderer.tsx\n\n\n\nvar isMountProps = (props) => {\n  return \"mount\" in props;\n};\nvar isOpenProps = (props) => {\n  return \"open\" in props;\n};\nvar stripMenuItemIconHandlers = (menuItems) => {\n  return menuItems == null ? void 0 : menuItems.map(({ mountIcon, unmountIcon, ...rest }) => rest);\n};\nvar ClerkHostRenderer = class extends react__WEBPACK_IMPORTED_MODULE_4__.PureComponent {\n  constructor() {\n    super(...arguments);\n    this.rootRef = react__WEBPACK_IMPORTED_MODULE_4__.createRef();\n  }\n  componentDidUpdate(_prevProps) {\n    var _a, _b, _c, _d;\n    if (!isMountProps(_prevProps) || !isMountProps(this.props)) {\n      return;\n    }\n    const prevProps = (0,_clerk_shared_object__WEBPACK_IMPORTED_MODULE_6__.without)(_prevProps.props, \"customPages\", \"customMenuItems\", \"children\");\n    const newProps = (0,_clerk_shared_object__WEBPACK_IMPORTED_MODULE_6__.without)(this.props.props, \"customPages\", \"customMenuItems\", \"children\");\n    const customPagesChanged = ((_a = prevProps.customPages) == null ? void 0 : _a.length) !== ((_b = newProps.customPages) == null ? void 0 : _b.length);\n    const customMenuItemsChanged = ((_c = prevProps.customMenuItems) == null ? void 0 : _c.length) !== ((_d = newProps.customMenuItems) == null ? void 0 : _d.length);\n    const prevMenuItemsWithoutHandlers = stripMenuItemIconHandlers(_prevProps.props.customMenuItems);\n    const newMenuItemsWithoutHandlers = stripMenuItemIconHandlers(this.props.props.customMenuItems);\n    if (!(0,_clerk_shared_react__WEBPACK_IMPORTED_MODULE_7__.isDeeplyEqual)(prevProps, newProps) || !(0,_clerk_shared_react__WEBPACK_IMPORTED_MODULE_7__.isDeeplyEqual)(prevMenuItemsWithoutHandlers, newMenuItemsWithoutHandlers) || customPagesChanged || customMenuItemsChanged) {\n      if (this.rootRef.current) {\n        this.props.updateProps({ node: this.rootRef.current, props: this.props.props });\n      }\n    }\n  }\n  componentDidMount() {\n    if (this.rootRef.current) {\n      if (isMountProps(this.props)) {\n        this.props.mount(this.rootRef.current, this.props.props);\n      }\n      if (isOpenProps(this.props)) {\n        this.props.open(this.props.props);\n      }\n    }\n  }\n  componentWillUnmount() {\n    if (this.rootRef.current) {\n      if (isMountProps(this.props)) {\n        this.props.unmount(this.rootRef.current);\n      }\n      if (isOpenProps(this.props)) {\n        this.props.close();\n      }\n    }\n  }\n  render() {\n    const { hideRootHtmlElement = false } = this.props;\n    const rootAttributes = {\n      ref: this.rootRef,\n      ...this.props.rootProps,\n      ...this.props.component && { \"data-clerk-component\": this.props.component }\n    };\n    return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_4__.createElement(react__WEBPACK_IMPORTED_MODULE_4__.Fragment, null, !hideRootHtmlElement && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_4__.createElement(\"div\", { ...rootAttributes }), this.props.children);\n  }\n};\n\n// src/components/uiComponents.tsx\nvar CustomPortalsRenderer = (props) => {\n  var _a, _b;\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_4__.createElement(react__WEBPACK_IMPORTED_MODULE_4__.Fragment, null, (_a = props == null ? void 0 : props.customPagesPortals) == null ? void 0 : _a.map((portal, index) => (0,react__WEBPACK_IMPORTED_MODULE_4__.createElement)(portal, { key: index })), (_b = props == null ? void 0 : props.customMenuItemsPortals) == null ? void 0 : _b.map((portal, index) => (0,react__WEBPACK_IMPORTED_MODULE_4__.createElement)(portal, { key: index })));\n};\nvar SignIn = (0,_chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.withClerk)(\n  ({ clerk, component, fallback, ...props }) => {\n    const mountingStatus = useWaitForComponentMount(component);\n    const shouldShowFallback = mountingStatus === \"rendering\" || !clerk.loaded;\n    const rendererRootProps = {\n      ...shouldShowFallback && fallback && { style: { display: \"none\" } }\n    };\n    return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_4__.createElement(react__WEBPACK_IMPORTED_MODULE_4__.Fragment, null, shouldShowFallback && fallback, clerk.loaded && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_4__.createElement(\n      ClerkHostRenderer,\n      {\n        component,\n        mount: clerk.mountSignIn,\n        unmount: clerk.unmountSignIn,\n        updateProps: clerk.__unstable__updateProps,\n        props,\n        rootProps: rendererRootProps\n      }\n    ));\n  },\n  { component: \"SignIn\", renderWhileLoading: true }\n);\nvar SignUp = (0,_chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.withClerk)(\n  ({ clerk, component, fallback, ...props }) => {\n    const mountingStatus = useWaitForComponentMount(component);\n    const shouldShowFallback = mountingStatus === \"rendering\" || !clerk.loaded;\n    const rendererRootProps = {\n      ...shouldShowFallback && fallback && { style: { display: \"none\" } }\n    };\n    return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_4__.createElement(react__WEBPACK_IMPORTED_MODULE_4__.Fragment, null, shouldShowFallback && fallback, clerk.loaded && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_4__.createElement(\n      ClerkHostRenderer,\n      {\n        component,\n        mount: clerk.mountSignUp,\n        unmount: clerk.unmountSignUp,\n        updateProps: clerk.__unstable__updateProps,\n        props,\n        rootProps: rendererRootProps\n      }\n    ));\n  },\n  { component: \"SignUp\", renderWhileLoading: true }\n);\nfunction UserProfilePage({ children }) {\n  (0,_clerk_shared_utils__WEBPACK_IMPORTED_MODULE_3__.logErrorInDevMode)(_chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.userProfilePageRenderedError);\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_4__.createElement(react__WEBPACK_IMPORTED_MODULE_4__.Fragment, null, children);\n}\nfunction UserProfileLink({ children }) {\n  (0,_clerk_shared_utils__WEBPACK_IMPORTED_MODULE_3__.logErrorInDevMode)(_chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.userProfileLinkRenderedError);\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_4__.createElement(react__WEBPACK_IMPORTED_MODULE_4__.Fragment, null, children);\n}\nvar _UserProfile = (0,_chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.withClerk)(\n  ({\n    clerk,\n    component,\n    fallback,\n    ...props\n  }) => {\n    const mountingStatus = useWaitForComponentMount(component);\n    const shouldShowFallback = mountingStatus === \"rendering\" || !clerk.loaded;\n    const rendererRootProps = {\n      ...shouldShowFallback && fallback && { style: { display: \"none\" } }\n    };\n    const { customPages, customPagesPortals } = useUserProfileCustomPages(props.children);\n    return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_4__.createElement(react__WEBPACK_IMPORTED_MODULE_4__.Fragment, null, shouldShowFallback && fallback, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_4__.createElement(\n      ClerkHostRenderer,\n      {\n        component,\n        mount: clerk.mountUserProfile,\n        unmount: clerk.unmountUserProfile,\n        updateProps: clerk.__unstable__updateProps,\n        props: { ...props, customPages },\n        rootProps: rendererRootProps\n      },\n      /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_4__.createElement(CustomPortalsRenderer, { customPagesPortals })\n    ));\n  },\n  { component: \"UserProfile\", renderWhileLoading: true }\n);\nvar UserProfile = Object.assign(_UserProfile, {\n  Page: UserProfilePage,\n  Link: UserProfileLink\n});\nvar UserButtonContext = (0,react__WEBPACK_IMPORTED_MODULE_4__.createContext)({\n  mount: () => {\n  },\n  unmount: () => {\n  },\n  updateProps: () => {\n  }\n});\nvar _UserButton = (0,_chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.withClerk)(\n  ({\n    clerk,\n    component,\n    fallback,\n    ...props\n  }) => {\n    const mountingStatus = useWaitForComponentMount(component);\n    const shouldShowFallback = mountingStatus === \"rendering\" || !clerk.loaded;\n    const rendererRootProps = {\n      ...shouldShowFallback && fallback && { style: { display: \"none\" } }\n    };\n    const { customPages, customPagesPortals } = useUserProfileCustomPages(props.children, {\n      allowForAnyChildren: !!props.__experimental_asProvider\n    });\n    const userProfileProps = Object.assign(props.userProfileProps || {}, { customPages });\n    const { customMenuItems, customMenuItemsPortals } = useUserButtonCustomMenuItems(props.children);\n    const sanitizedChildren = useSanitizedChildren(props.children);\n    const passableProps = {\n      mount: clerk.mountUserButton,\n      unmount: clerk.unmountUserButton,\n      updateProps: clerk.__unstable__updateProps,\n      props: { ...props, userProfileProps, customMenuItems }\n    };\n    const portalProps = {\n      customPagesPortals,\n      customMenuItemsPortals\n    };\n    return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_4__.createElement(UserButtonContext.Provider, { value: passableProps }, shouldShowFallback && fallback, clerk.loaded && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_4__.createElement(\n      ClerkHostRenderer,\n      {\n        component,\n        ...passableProps,\n        hideRootHtmlElement: !!props.__experimental_asProvider,\n        rootProps: rendererRootProps\n      },\n      props.__experimental_asProvider ? sanitizedChildren : null,\n      /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_4__.createElement(CustomPortalsRenderer, { ...portalProps })\n    ));\n  },\n  { component: \"UserButton\", renderWhileLoading: true }\n);\nfunction MenuItems({ children }) {\n  (0,_clerk_shared_utils__WEBPACK_IMPORTED_MODULE_3__.logErrorInDevMode)(_chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.userButtonMenuItemsRenderedError);\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_4__.createElement(react__WEBPACK_IMPORTED_MODULE_4__.Fragment, null, children);\n}\nfunction MenuAction({ children }) {\n  (0,_clerk_shared_utils__WEBPACK_IMPORTED_MODULE_3__.logErrorInDevMode)(_chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.userButtonMenuActionRenderedError);\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_4__.createElement(react__WEBPACK_IMPORTED_MODULE_4__.Fragment, null, children);\n}\nfunction MenuLink({ children }) {\n  (0,_clerk_shared_utils__WEBPACK_IMPORTED_MODULE_3__.logErrorInDevMode)(_chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.userButtonMenuLinkRenderedError);\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_4__.createElement(react__WEBPACK_IMPORTED_MODULE_4__.Fragment, null, children);\n}\nfunction UserButtonOutlet(outletProps) {\n  const providerProps = (0,react__WEBPACK_IMPORTED_MODULE_4__.useContext)(UserButtonContext);\n  const portalProps = {\n    ...providerProps,\n    props: {\n      ...providerProps.props,\n      ...outletProps\n    }\n  };\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_4__.createElement(ClerkHostRenderer, { ...portalProps });\n}\nvar UserButton = Object.assign(_UserButton, {\n  UserProfilePage,\n  UserProfileLink,\n  MenuItems,\n  Action: MenuAction,\n  Link: MenuLink,\n  __experimental_Outlet: UserButtonOutlet\n});\nfunction OrganizationProfilePage({ children }) {\n  (0,_clerk_shared_utils__WEBPACK_IMPORTED_MODULE_3__.logErrorInDevMode)(_chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.organizationProfilePageRenderedError);\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_4__.createElement(react__WEBPACK_IMPORTED_MODULE_4__.Fragment, null, children);\n}\nfunction OrganizationProfileLink({ children }) {\n  (0,_clerk_shared_utils__WEBPACK_IMPORTED_MODULE_3__.logErrorInDevMode)(_chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.organizationProfileLinkRenderedError);\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_4__.createElement(react__WEBPACK_IMPORTED_MODULE_4__.Fragment, null, children);\n}\nvar _OrganizationProfile = (0,_chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.withClerk)(\n  ({\n    clerk,\n    component,\n    fallback,\n    ...props\n  }) => {\n    const mountingStatus = useWaitForComponentMount(component);\n    const shouldShowFallback = mountingStatus === \"rendering\" || !clerk.loaded;\n    const rendererRootProps = {\n      ...shouldShowFallback && fallback && { style: { display: \"none\" } }\n    };\n    const { customPages, customPagesPortals } = useOrganizationProfileCustomPages(props.children);\n    return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_4__.createElement(react__WEBPACK_IMPORTED_MODULE_4__.Fragment, null, shouldShowFallback && fallback, clerk.loaded && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_4__.createElement(\n      ClerkHostRenderer,\n      {\n        component,\n        mount: clerk.mountOrganizationProfile,\n        unmount: clerk.unmountOrganizationProfile,\n        updateProps: clerk.__unstable__updateProps,\n        props: { ...props, customPages },\n        rootProps: rendererRootProps\n      },\n      /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_4__.createElement(CustomPortalsRenderer, { customPagesPortals })\n    ));\n  },\n  { component: \"OrganizationProfile\", renderWhileLoading: true }\n);\nvar OrganizationProfile = Object.assign(_OrganizationProfile, {\n  Page: OrganizationProfilePage,\n  Link: OrganizationProfileLink\n});\nvar CreateOrganization = (0,_chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.withClerk)(\n  ({ clerk, component, fallback, ...props }) => {\n    const mountingStatus = useWaitForComponentMount(component);\n    const shouldShowFallback = mountingStatus === \"rendering\" || !clerk.loaded;\n    const rendererRootProps = {\n      ...shouldShowFallback && fallback && { style: { display: \"none\" } }\n    };\n    return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_4__.createElement(react__WEBPACK_IMPORTED_MODULE_4__.Fragment, null, shouldShowFallback && fallback, clerk.loaded && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_4__.createElement(\n      ClerkHostRenderer,\n      {\n        component,\n        mount: clerk.mountCreateOrganization,\n        unmount: clerk.unmountCreateOrganization,\n        updateProps: clerk.__unstable__updateProps,\n        props,\n        rootProps: rendererRootProps\n      }\n    ));\n  },\n  { component: \"CreateOrganization\", renderWhileLoading: true }\n);\nvar OrganizationSwitcherContext = (0,react__WEBPACK_IMPORTED_MODULE_4__.createContext)({\n  mount: () => {\n  },\n  unmount: () => {\n  },\n  updateProps: () => {\n  }\n});\nvar _OrganizationSwitcher = (0,_chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.withClerk)(\n  ({\n    clerk,\n    component,\n    fallback,\n    ...props\n  }) => {\n    const mountingStatus = useWaitForComponentMount(component);\n    const shouldShowFallback = mountingStatus === \"rendering\" || !clerk.loaded;\n    const rendererRootProps = {\n      ...shouldShowFallback && fallback && { style: { display: \"none\" } }\n    };\n    const { customPages, customPagesPortals } = useOrganizationProfileCustomPages(props.children, {\n      allowForAnyChildren: !!props.__experimental_asProvider\n    });\n    const organizationProfileProps = Object.assign(props.organizationProfileProps || {}, { customPages });\n    const sanitizedChildren = useSanitizedChildren(props.children);\n    const passableProps = {\n      mount: clerk.mountOrganizationSwitcher,\n      unmount: clerk.unmountOrganizationSwitcher,\n      updateProps: clerk.__unstable__updateProps,\n      props: { ...props, organizationProfileProps },\n      rootProps: rendererRootProps,\n      component\n    };\n    clerk.__experimental_prefetchOrganizationSwitcher();\n    return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_4__.createElement(OrganizationSwitcherContext.Provider, { value: passableProps }, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_4__.createElement(react__WEBPACK_IMPORTED_MODULE_4__.Fragment, null, shouldShowFallback && fallback, clerk.loaded && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_4__.createElement(\n      ClerkHostRenderer,\n      {\n        ...passableProps,\n        hideRootHtmlElement: !!props.__experimental_asProvider\n      },\n      props.__experimental_asProvider ? sanitizedChildren : null,\n      /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_4__.createElement(CustomPortalsRenderer, { customPagesPortals })\n    )));\n  },\n  { component: \"OrganizationSwitcher\", renderWhileLoading: true }\n);\nfunction OrganizationSwitcherOutlet(outletProps) {\n  const providerProps = (0,react__WEBPACK_IMPORTED_MODULE_4__.useContext)(OrganizationSwitcherContext);\n  const portalProps = {\n    ...providerProps,\n    props: {\n      ...providerProps.props,\n      ...outletProps\n    }\n  };\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_4__.createElement(ClerkHostRenderer, { ...portalProps });\n}\nvar OrganizationSwitcher = Object.assign(_OrganizationSwitcher, {\n  OrganizationProfilePage,\n  OrganizationProfileLink,\n  __experimental_Outlet: OrganizationSwitcherOutlet\n});\nvar OrganizationList = (0,_chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.withClerk)(\n  ({ clerk, component, fallback, ...props }) => {\n    const mountingStatus = useWaitForComponentMount(component);\n    const shouldShowFallback = mountingStatus === \"rendering\" || !clerk.loaded;\n    const rendererRootProps = {\n      ...shouldShowFallback && fallback && { style: { display: \"none\" } }\n    };\n    return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_4__.createElement(react__WEBPACK_IMPORTED_MODULE_4__.Fragment, null, shouldShowFallback && fallback, clerk.loaded && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_4__.createElement(\n      ClerkHostRenderer,\n      {\n        component,\n        mount: clerk.mountOrganizationList,\n        unmount: clerk.unmountOrganizationList,\n        updateProps: clerk.__unstable__updateProps,\n        props,\n        rootProps: rendererRootProps\n      }\n    ));\n  },\n  { component: \"OrganizationList\", renderWhileLoading: true }\n);\nvar GoogleOneTap = (0,_chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.withClerk)(\n  ({ clerk, component, fallback, ...props }) => {\n    const mountingStatus = useWaitForComponentMount(component);\n    const shouldShowFallback = mountingStatus === \"rendering\" || !clerk.loaded;\n    const rendererRootProps = {\n      ...shouldShowFallback && fallback && { style: { display: \"none\" } }\n    };\n    return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_4__.createElement(react__WEBPACK_IMPORTED_MODULE_4__.Fragment, null, shouldShowFallback && fallback, clerk.loaded && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_4__.createElement(\n      ClerkHostRenderer,\n      {\n        component,\n        open: clerk.openGoogleOneTap,\n        close: clerk.closeGoogleOneTap,\n        updateProps: clerk.__unstable__updateProps,\n        props,\n        rootProps: rendererRootProps\n      }\n    ));\n  },\n  { component: \"GoogleOneTap\", renderWhileLoading: true }\n);\nvar Waitlist = (0,_chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.withClerk)(\n  ({ clerk, component, fallback, ...props }) => {\n    const mountingStatus = useWaitForComponentMount(component);\n    const shouldShowFallback = mountingStatus === \"rendering\" || !clerk.loaded;\n    const rendererRootProps = {\n      ...shouldShowFallback && fallback && { style: { display: \"none\" } }\n    };\n    return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_4__.createElement(react__WEBPACK_IMPORTED_MODULE_4__.Fragment, null, shouldShowFallback && fallback, clerk.loaded && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_4__.createElement(\n      ClerkHostRenderer,\n      {\n        component,\n        mount: clerk.mountWaitlist,\n        unmount: clerk.unmountWaitlist,\n        updateProps: clerk.__unstable__updateProps,\n        props,\n        rootProps: rendererRootProps\n      }\n    ));\n  },\n  { component: \"Waitlist\", renderWhileLoading: true }\n);\nvar PricingTable = (0,_chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.withClerk)(\n  ({ clerk, component, fallback, ...props }) => {\n    const mountingStatus = useWaitForComponentMount(component);\n    const shouldShowFallback = mountingStatus === \"rendering\" || !clerk.loaded;\n    const rendererRootProps = {\n      ...shouldShowFallback && fallback && { style: { display: \"none\" } }\n    };\n    return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_4__.createElement(react__WEBPACK_IMPORTED_MODULE_4__.Fragment, null, shouldShowFallback && fallback, clerk.loaded && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_4__.createElement(\n      ClerkHostRenderer,\n      {\n        component,\n        mount: clerk.mountPricingTable,\n        unmount: clerk.unmountPricingTable,\n        updateProps: clerk.__unstable__updateProps,\n        props,\n        rootProps: rendererRootProps\n      }\n    ));\n  },\n  { component: \"PricingTable\", renderWhileLoading: true }\n);\n\n// src/components/SignInButton.tsx\n\nvar SignInButton = (0,_chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.withClerk)(\n  ({ clerk, children, ...props }) => {\n    const {\n      signUpFallbackRedirectUrl,\n      forceRedirectUrl,\n      fallbackRedirectUrl,\n      signUpForceRedirectUrl,\n      mode,\n      initialValues,\n      withSignUp,\n      oauthFlow,\n      ...rest\n    } = props;\n    children = normalizeWithDefaultValue(children, \"Sign in\");\n    const child = assertSingleChild(children)(\"SignInButton\");\n    const clickHandler = () => {\n      const opts = {\n        forceRedirectUrl,\n        fallbackRedirectUrl,\n        signUpFallbackRedirectUrl,\n        signUpForceRedirectUrl,\n        initialValues,\n        withSignUp,\n        oauthFlow\n      };\n      if (mode === \"modal\") {\n        return clerk.openSignIn({ ...opts, appearance: props.appearance });\n      }\n      return clerk.redirectToSignIn({\n        ...opts,\n        signInFallbackRedirectUrl: fallbackRedirectUrl,\n        signInForceRedirectUrl: forceRedirectUrl\n      });\n    };\n    const wrappedChildClickHandler = async (e) => {\n      if (child && typeof child === \"object\" && \"props\" in child) {\n        await safeExecute(child.props.onClick)(e);\n      }\n      return clickHandler();\n    };\n    const childProps = { ...rest, onClick: wrappedChildClickHandler };\n    return react__WEBPACK_IMPORTED_MODULE_4__.cloneElement(child, childProps);\n  },\n  { component: \"SignInButton\", renderWhileLoading: true }\n);\n\n// src/components/SignUpButton.tsx\n\nvar SignUpButton = (0,_chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.withClerk)(\n  ({ clerk, children, ...props }) => {\n    const {\n      fallbackRedirectUrl,\n      forceRedirectUrl,\n      signInFallbackRedirectUrl,\n      signInForceRedirectUrl,\n      mode,\n      unsafeMetadata,\n      initialValues,\n      oauthFlow,\n      ...rest\n    } = props;\n    children = normalizeWithDefaultValue(children, \"Sign up\");\n    const child = assertSingleChild(children)(\"SignUpButton\");\n    const clickHandler = () => {\n      const opts = {\n        fallbackRedirectUrl,\n        forceRedirectUrl,\n        signInFallbackRedirectUrl,\n        signInForceRedirectUrl,\n        unsafeMetadata,\n        initialValues,\n        oauthFlow\n      };\n      if (mode === \"modal\") {\n        return clerk.openSignUp({ ...opts, appearance: props.appearance });\n      }\n      return clerk.redirectToSignUp({\n        ...opts,\n        signUpFallbackRedirectUrl: fallbackRedirectUrl,\n        signUpForceRedirectUrl: forceRedirectUrl\n      });\n    };\n    const wrappedChildClickHandler = async (e) => {\n      if (child && typeof child === \"object\" && \"props\" in child) {\n        await safeExecute(child.props.onClick)(e);\n      }\n      return clickHandler();\n    };\n    const childProps = { ...rest, onClick: wrappedChildClickHandler };\n    return react__WEBPACK_IMPORTED_MODULE_4__.cloneElement(child, childProps);\n  },\n  { component: \"SignUpButton\", renderWhileLoading: true }\n);\n\n// src/components/SignOutButton.tsx\n\nvar SignOutButton = (0,_chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.withClerk)(\n  ({ clerk, children, ...props }) => {\n    const { redirectUrl = \"/\", signOutOptions, ...rest } = props;\n    children = normalizeWithDefaultValue(children, \"Sign out\");\n    const child = assertSingleChild(children)(\"SignOutButton\");\n    const clickHandler = () => clerk.signOut({ redirectUrl, ...signOutOptions });\n    const wrappedChildClickHandler = async (e) => {\n      await safeExecute(child.props.onClick)(e);\n      return clickHandler();\n    };\n    const childProps = { ...rest, onClick: wrappedChildClickHandler };\n    return react__WEBPACK_IMPORTED_MODULE_4__.cloneElement(child, childProps);\n  },\n  { component: \"SignOutButton\", renderWhileLoading: true }\n);\n\n// src/components/SignInWithMetamaskButton.tsx\n\nvar SignInWithMetamaskButton = (0,_chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.withClerk)(\n  ({ clerk, children, ...props }) => {\n    const { redirectUrl, ...rest } = props;\n    children = normalizeWithDefaultValue(children, \"Sign in with Metamask\");\n    const child = assertSingleChild(children)(\"SignInWithMetamaskButton\");\n    const clickHandler = async () => {\n      async function authenticate() {\n        await clerk.authenticateWithMetamask({ redirectUrl: redirectUrl || void 0 });\n      }\n      void authenticate();\n    };\n    const wrappedChildClickHandler = async (e) => {\n      await safeExecute(child.props.onClick)(e);\n      return clickHandler();\n    };\n    const childProps = { ...rest, onClick: wrappedChildClickHandler };\n    return react__WEBPACK_IMPORTED_MODULE_4__.cloneElement(child, childProps);\n  },\n  { component: \"SignInWithMetamask\", renderWhileLoading: true }\n);\n\n// src/contexts/ClerkProvider.tsx\n\n\n\n// src/contexts/ClerkContextProvider.tsx\n\n\n\n\n// src/isomorphicClerk.ts\n\n\n\n\nif (typeof globalThis.__BUILD_DISABLE_RHC__ === \"undefined\") {\n  globalThis.__BUILD_DISABLE_RHC__ = false;\n}\nvar SDK_METADATA = {\n  name: \"@clerk/clerk-react\",\n  version: \"5.31.6\",\n  environment: \"development\"\n};\nvar _status, _domain, _proxyUrl, _publishableKey, _eventBus, _instance, _IsomorphicClerk_instances, waitForClerkJS_fn;\nvar _IsomorphicClerk = class _IsomorphicClerk {\n  constructor(options) {\n    (0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_1__.__privateAdd)(this, _IsomorphicClerk_instances);\n    this.clerkjs = null;\n    this.preopenOneTap = null;\n    this.preopenUserVerification = null;\n    this.preopenSignIn = null;\n    this.preopenCheckout = null;\n    this.preopenPlanDetails = null;\n    this.preopenSignUp = null;\n    this.preopenUserProfile = null;\n    this.preopenOrganizationProfile = null;\n    this.preopenCreateOrganization = null;\n    this.preOpenWaitlist = null;\n    this.premountSignInNodes = /* @__PURE__ */ new Map();\n    this.premountSignUpNodes = /* @__PURE__ */ new Map();\n    this.premountUserProfileNodes = /* @__PURE__ */ new Map();\n    this.premountUserButtonNodes = /* @__PURE__ */ new Map();\n    this.premountOrganizationProfileNodes = /* @__PURE__ */ new Map();\n    this.premountCreateOrganizationNodes = /* @__PURE__ */ new Map();\n    this.premountOrganizationSwitcherNodes = /* @__PURE__ */ new Map();\n    this.premountOrganizationListNodes = /* @__PURE__ */ new Map();\n    this.premountMethodCalls = /* @__PURE__ */ new Map();\n    this.premountWaitlistNodes = /* @__PURE__ */ new Map();\n    this.premountPricingTableNodes = /* @__PURE__ */ new Map();\n    // A separate Map of `addListener` method calls to handle multiple listeners.\n    this.premountAddListenerCalls = /* @__PURE__ */ new Map();\n    this.loadedListeners = [];\n    (0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_1__.__privateAdd)(this, _status, \"loading\");\n    (0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_1__.__privateAdd)(this, _domain);\n    (0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_1__.__privateAdd)(this, _proxyUrl);\n    (0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_1__.__privateAdd)(this, _publishableKey);\n    (0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_1__.__privateAdd)(this, _eventBus, (0,_clerk_shared_clerkEventBus__WEBPACK_IMPORTED_MODULE_11__.createClerkEventBus)());\n    this.buildSignInUrl = (opts) => {\n      const callback = () => {\n        var _a;\n        return ((_a = this.clerkjs) == null ? void 0 : _a.buildSignInUrl(opts)) || \"\";\n      };\n      if (this.clerkjs && this.loaded) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"buildSignInUrl\", callback);\n      }\n    };\n    this.buildSignUpUrl = (opts) => {\n      const callback = () => {\n        var _a;\n        return ((_a = this.clerkjs) == null ? void 0 : _a.buildSignUpUrl(opts)) || \"\";\n      };\n      if (this.clerkjs && this.loaded) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"buildSignUpUrl\", callback);\n      }\n    };\n    this.buildAfterSignInUrl = (...args) => {\n      const callback = () => {\n        var _a;\n        return ((_a = this.clerkjs) == null ? void 0 : _a.buildAfterSignInUrl(...args)) || \"\";\n      };\n      if (this.clerkjs && this.loaded) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"buildAfterSignInUrl\", callback);\n      }\n    };\n    this.buildAfterSignUpUrl = (...args) => {\n      const callback = () => {\n        var _a;\n        return ((_a = this.clerkjs) == null ? void 0 : _a.buildAfterSignUpUrl(...args)) || \"\";\n      };\n      if (this.clerkjs && this.loaded) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"buildAfterSignUpUrl\", callback);\n      }\n    };\n    this.buildAfterSignOutUrl = () => {\n      const callback = () => {\n        var _a;\n        return ((_a = this.clerkjs) == null ? void 0 : _a.buildAfterSignOutUrl()) || \"\";\n      };\n      if (this.clerkjs && this.loaded) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"buildAfterSignOutUrl\", callback);\n      }\n    };\n    this.buildNewSubscriptionRedirectUrl = () => {\n      const callback = () => {\n        var _a;\n        return ((_a = this.clerkjs) == null ? void 0 : _a.buildNewSubscriptionRedirectUrl()) || \"\";\n      };\n      if (this.clerkjs && this.loaded) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"buildNewSubscriptionRedirectUrl\", callback);\n      }\n    };\n    this.buildAfterMultiSessionSingleSignOutUrl = () => {\n      const callback = () => {\n        var _a;\n        return ((_a = this.clerkjs) == null ? void 0 : _a.buildAfterMultiSessionSingleSignOutUrl()) || \"\";\n      };\n      if (this.clerkjs && this.loaded) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"buildAfterMultiSessionSingleSignOutUrl\", callback);\n      }\n    };\n    this.buildUserProfileUrl = () => {\n      const callback = () => {\n        var _a;\n        return ((_a = this.clerkjs) == null ? void 0 : _a.buildUserProfileUrl()) || \"\";\n      };\n      if (this.clerkjs && this.loaded) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"buildUserProfileUrl\", callback);\n      }\n    };\n    this.buildCreateOrganizationUrl = () => {\n      const callback = () => {\n        var _a;\n        return ((_a = this.clerkjs) == null ? void 0 : _a.buildCreateOrganizationUrl()) || \"\";\n      };\n      if (this.clerkjs && this.loaded) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"buildCreateOrganizationUrl\", callback);\n      }\n    };\n    this.buildOrganizationProfileUrl = () => {\n      const callback = () => {\n        var _a;\n        return ((_a = this.clerkjs) == null ? void 0 : _a.buildOrganizationProfileUrl()) || \"\";\n      };\n      if (this.clerkjs && this.loaded) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"buildOrganizationProfileUrl\", callback);\n      }\n    };\n    this.buildWaitlistUrl = () => {\n      const callback = () => {\n        var _a;\n        return ((_a = this.clerkjs) == null ? void 0 : _a.buildWaitlistUrl()) || \"\";\n      };\n      if (this.clerkjs && this.loaded) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"buildWaitlistUrl\", callback);\n      }\n    };\n    this.buildUrlWithAuth = (to) => {\n      const callback = () => {\n        var _a;\n        return ((_a = this.clerkjs) == null ? void 0 : _a.buildUrlWithAuth(to)) || \"\";\n      };\n      if (this.clerkjs && this.loaded) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"buildUrlWithAuth\", callback);\n      }\n    };\n    this.handleUnauthenticated = async () => {\n      const callback = () => {\n        var _a;\n        return (_a = this.clerkjs) == null ? void 0 : _a.handleUnauthenticated();\n      };\n      if (this.clerkjs && this.loaded) {\n        void callback();\n      } else {\n        this.premountMethodCalls.set(\"handleUnauthenticated\", callback);\n      }\n    };\n    this.on = (...args) => {\n      var _a;\n      if ((_a = this.clerkjs) == null ? void 0 : _a.on) {\n        return this.clerkjs.on(...args);\n      } else {\n        (0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_1__.__privateGet)(this, _eventBus).on(...args);\n      }\n    };\n    this.off = (...args) => {\n      var _a;\n      if ((_a = this.clerkjs) == null ? void 0 : _a.off) {\n        return this.clerkjs.off(...args);\n      } else {\n        (0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_1__.__privateGet)(this, _eventBus).off(...args);\n      }\n    };\n    /**\n     * @deprecated Please use `addStatusListener`. This api will be removed in the next major.\n     */\n    this.addOnLoaded = (cb) => {\n      this.loadedListeners.push(cb);\n      if (this.loaded) {\n        this.emitLoaded();\n      }\n    };\n    /**\n     * @deprecated Please use `__internal_setStatus`. This api will be removed in the next major.\n     */\n    this.emitLoaded = () => {\n      this.loadedListeners.forEach((cb) => cb());\n      this.loadedListeners = [];\n    };\n    this.beforeLoad = (clerkjs) => {\n      if (!clerkjs) {\n        throw new Error(\"Failed to hydrate latest Clerk JS\");\n      }\n    };\n    this.hydrateClerkJS = (clerkjs) => {\n      var _a;\n      if (!clerkjs) {\n        throw new Error(\"Failed to hydrate latest Clerk JS\");\n      }\n      this.clerkjs = clerkjs;\n      this.premountMethodCalls.forEach((cb) => cb());\n      this.premountAddListenerCalls.forEach((listenerHandlers, listener) => {\n        listenerHandlers.nativeUnsubscribe = clerkjs.addListener(listener);\n      });\n      (_a = (0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_1__.__privateGet)(this, _eventBus).internal.retrieveListeners(\"status\")) == null ? void 0 : _a.forEach((listener) => {\n        this.on(\"status\", listener, { notify: true });\n      });\n      if (this.preopenSignIn !== null) {\n        clerkjs.openSignIn(this.preopenSignIn);\n      }\n      if (this.preopenCheckout !== null) {\n        clerkjs.__internal_openCheckout(this.preopenCheckout);\n      }\n      if (this.preopenPlanDetails !== null) {\n        clerkjs.__internal_openPlanDetails(this.preopenPlanDetails);\n      }\n      if (this.preopenSignUp !== null) {\n        clerkjs.openSignUp(this.preopenSignUp);\n      }\n      if (this.preopenUserProfile !== null) {\n        clerkjs.openUserProfile(this.preopenUserProfile);\n      }\n      if (this.preopenUserVerification !== null) {\n        clerkjs.__internal_openReverification(this.preopenUserVerification);\n      }\n      if (this.preopenOneTap !== null) {\n        clerkjs.openGoogleOneTap(this.preopenOneTap);\n      }\n      if (this.preopenOrganizationProfile !== null) {\n        clerkjs.openOrganizationProfile(this.preopenOrganizationProfile);\n      }\n      if (this.preopenCreateOrganization !== null) {\n        clerkjs.openCreateOrganization(this.preopenCreateOrganization);\n      }\n      if (this.preOpenWaitlist !== null) {\n        clerkjs.openWaitlist(this.preOpenWaitlist);\n      }\n      this.premountSignInNodes.forEach((props, node) => {\n        clerkjs.mountSignIn(node, props);\n      });\n      this.premountSignUpNodes.forEach((props, node) => {\n        clerkjs.mountSignUp(node, props);\n      });\n      this.premountUserProfileNodes.forEach((props, node) => {\n        clerkjs.mountUserProfile(node, props);\n      });\n      this.premountUserButtonNodes.forEach((props, node) => {\n        clerkjs.mountUserButton(node, props);\n      });\n      this.premountOrganizationListNodes.forEach((props, node) => {\n        clerkjs.mountOrganizationList(node, props);\n      });\n      this.premountWaitlistNodes.forEach((props, node) => {\n        clerkjs.mountWaitlist(node, props);\n      });\n      this.premountPricingTableNodes.forEach((props, node) => {\n        clerkjs.mountPricingTable(node, props);\n      });\n      if (typeof this.clerkjs.status === \"undefined\") {\n        (0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_1__.__privateGet)(this, _eventBus).emit(_clerk_shared_clerkEventBus__WEBPACK_IMPORTED_MODULE_11__.clerkEvents.Status, \"ready\");\n      }\n      this.emitLoaded();\n      return this.clerkjs;\n    };\n    this.__unstable__updateProps = async (props) => {\n      const clerkjs = await (0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_1__.__privateMethod)(this, _IsomorphicClerk_instances, waitForClerkJS_fn).call(this);\n      if (clerkjs && \"__unstable__updateProps\" in clerkjs) {\n        return clerkjs.__unstable__updateProps(props);\n      }\n    };\n    this.__experimental_navigateToTask = async (params) => {\n      if (this.clerkjs) {\n        return this.clerkjs.__experimental_navigateToTask(params);\n      } else {\n        return Promise.reject();\n      }\n    };\n    /**\n     * `setActive` can be used to set the active session and/or organization.\n     */\n    this.setActive = (params) => {\n      if (this.clerkjs) {\n        return this.clerkjs.setActive(params);\n      } else {\n        return Promise.reject();\n      }\n    };\n    this.openSignIn = (props) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.openSignIn(props);\n      } else {\n        this.preopenSignIn = props;\n      }\n    };\n    this.closeSignIn = () => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.closeSignIn();\n      } else {\n        this.preopenSignIn = null;\n      }\n    };\n    this.__internal_openCheckout = (props) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.__internal_openCheckout(props);\n      } else {\n        this.preopenCheckout = props;\n      }\n    };\n    this.__internal_closeCheckout = () => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.__internal_closeCheckout();\n      } else {\n        this.preopenCheckout = null;\n      }\n    };\n    this.__internal_openPlanDetails = (props) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.__internal_openPlanDetails(props);\n      } else {\n        this.preopenPlanDetails = props;\n      }\n    };\n    this.__internal_closePlanDetails = () => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.__internal_closePlanDetails();\n      } else {\n        this.preopenPlanDetails = null;\n      }\n    };\n    this.__internal_openReverification = (props) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.__internal_openReverification(props);\n      } else {\n        this.preopenUserVerification = props;\n      }\n    };\n    this.__internal_closeReverification = () => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.__internal_closeReverification();\n      } else {\n        this.preopenUserVerification = null;\n      }\n    };\n    this.openGoogleOneTap = (props) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.openGoogleOneTap(props);\n      } else {\n        this.preopenOneTap = props;\n      }\n    };\n    this.closeGoogleOneTap = () => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.closeGoogleOneTap();\n      } else {\n        this.preopenOneTap = null;\n      }\n    };\n    this.openUserProfile = (props) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.openUserProfile(props);\n      } else {\n        this.preopenUserProfile = props;\n      }\n    };\n    this.closeUserProfile = () => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.closeUserProfile();\n      } else {\n        this.preopenUserProfile = null;\n      }\n    };\n    this.openOrganizationProfile = (props) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.openOrganizationProfile(props);\n      } else {\n        this.preopenOrganizationProfile = props;\n      }\n    };\n    this.closeOrganizationProfile = () => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.closeOrganizationProfile();\n      } else {\n        this.preopenOrganizationProfile = null;\n      }\n    };\n    this.openCreateOrganization = (props) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.openCreateOrganization(props);\n      } else {\n        this.preopenCreateOrganization = props;\n      }\n    };\n    this.closeCreateOrganization = () => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.closeCreateOrganization();\n      } else {\n        this.preopenCreateOrganization = null;\n      }\n    };\n    this.openWaitlist = (props) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.openWaitlist(props);\n      } else {\n        this.preOpenWaitlist = props;\n      }\n    };\n    this.closeWaitlist = () => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.closeWaitlist();\n      } else {\n        this.preOpenWaitlist = null;\n      }\n    };\n    this.openSignUp = (props) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.openSignUp(props);\n      } else {\n        this.preopenSignUp = props;\n      }\n    };\n    this.closeSignUp = () => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.closeSignUp();\n      } else {\n        this.preopenSignUp = null;\n      }\n    };\n    this.mountSignIn = (node, props) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.mountSignIn(node, props);\n      } else {\n        this.premountSignInNodes.set(node, props);\n      }\n    };\n    this.unmountSignIn = (node) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.unmountSignIn(node);\n      } else {\n        this.premountSignInNodes.delete(node);\n      }\n    };\n    this.mountSignUp = (node, props) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.mountSignUp(node, props);\n      } else {\n        this.premountSignUpNodes.set(node, props);\n      }\n    };\n    this.unmountSignUp = (node) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.unmountSignUp(node);\n      } else {\n        this.premountSignUpNodes.delete(node);\n      }\n    };\n    this.mountUserProfile = (node, props) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.mountUserProfile(node, props);\n      } else {\n        this.premountUserProfileNodes.set(node, props);\n      }\n    };\n    this.unmountUserProfile = (node) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.unmountUserProfile(node);\n      } else {\n        this.premountUserProfileNodes.delete(node);\n      }\n    };\n    this.mountOrganizationProfile = (node, props) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.mountOrganizationProfile(node, props);\n      } else {\n        this.premountOrganizationProfileNodes.set(node, props);\n      }\n    };\n    this.unmountOrganizationProfile = (node) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.unmountOrganizationProfile(node);\n      } else {\n        this.premountOrganizationProfileNodes.delete(node);\n      }\n    };\n    this.mountCreateOrganization = (node, props) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.mountCreateOrganization(node, props);\n      } else {\n        this.premountCreateOrganizationNodes.set(node, props);\n      }\n    };\n    this.unmountCreateOrganization = (node) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.unmountCreateOrganization(node);\n      } else {\n        this.premountCreateOrganizationNodes.delete(node);\n      }\n    };\n    this.mountOrganizationSwitcher = (node, props) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.mountOrganizationSwitcher(node, props);\n      } else {\n        this.premountOrganizationSwitcherNodes.set(node, props);\n      }\n    };\n    this.unmountOrganizationSwitcher = (node) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.unmountOrganizationSwitcher(node);\n      } else {\n        this.premountOrganizationSwitcherNodes.delete(node);\n      }\n    };\n    this.__experimental_prefetchOrganizationSwitcher = () => {\n      const callback = () => {\n        var _a;\n        return (_a = this.clerkjs) == null ? void 0 : _a.__experimental_prefetchOrganizationSwitcher();\n      };\n      if (this.clerkjs && this.loaded) {\n        void callback();\n      } else {\n        this.premountMethodCalls.set(\"__experimental_prefetchOrganizationSwitcher\", callback);\n      }\n    };\n    this.mountOrganizationList = (node, props) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.mountOrganizationList(node, props);\n      } else {\n        this.premountOrganizationListNodes.set(node, props);\n      }\n    };\n    this.unmountOrganizationList = (node) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.unmountOrganizationList(node);\n      } else {\n        this.premountOrganizationListNodes.delete(node);\n      }\n    };\n    this.mountUserButton = (node, userButtonProps) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.mountUserButton(node, userButtonProps);\n      } else {\n        this.premountUserButtonNodes.set(node, userButtonProps);\n      }\n    };\n    this.unmountUserButton = (node) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.unmountUserButton(node);\n      } else {\n        this.premountUserButtonNodes.delete(node);\n      }\n    };\n    this.mountWaitlist = (node, props) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.mountWaitlist(node, props);\n      } else {\n        this.premountWaitlistNodes.set(node, props);\n      }\n    };\n    this.unmountWaitlist = (node) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.unmountWaitlist(node);\n      } else {\n        this.premountWaitlistNodes.delete(node);\n      }\n    };\n    this.mountPricingTable = (node, props) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.mountPricingTable(node, props);\n      } else {\n        this.premountPricingTableNodes.set(node, props);\n      }\n    };\n    this.unmountPricingTable = (node) => {\n      if (this.clerkjs && this.loaded) {\n        this.clerkjs.unmountPricingTable(node);\n      } else {\n        this.premountPricingTableNodes.delete(node);\n      }\n    };\n    this.addListener = (listener) => {\n      if (this.clerkjs) {\n        return this.clerkjs.addListener(listener);\n      } else {\n        const unsubscribe = () => {\n          var _a;\n          const listenerHandlers = this.premountAddListenerCalls.get(listener);\n          if (listenerHandlers) {\n            (_a = listenerHandlers.nativeUnsubscribe) == null ? void 0 : _a.call(listenerHandlers);\n            this.premountAddListenerCalls.delete(listener);\n          }\n        };\n        this.premountAddListenerCalls.set(listener, { unsubscribe, nativeUnsubscribe: void 0 });\n        return unsubscribe;\n      }\n    };\n    this.navigate = (to) => {\n      const callback = () => {\n        var _a;\n        return (_a = this.clerkjs) == null ? void 0 : _a.navigate(to);\n      };\n      if (this.clerkjs && this.loaded) {\n        void callback();\n      } else {\n        this.premountMethodCalls.set(\"navigate\", callback);\n      }\n    };\n    this.redirectWithAuth = async (...args) => {\n      const callback = () => {\n        var _a;\n        return (_a = this.clerkjs) == null ? void 0 : _a.redirectWithAuth(...args);\n      };\n      if (this.clerkjs && this.loaded) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"redirectWithAuth\", callback);\n        return;\n      }\n    };\n    this.redirectToSignIn = async (opts) => {\n      const callback = () => {\n        var _a;\n        return (_a = this.clerkjs) == null ? void 0 : _a.redirectToSignIn(opts);\n      };\n      if (this.clerkjs && this.loaded) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"redirectToSignIn\", callback);\n        return;\n      }\n    };\n    this.redirectToSignUp = async (opts) => {\n      const callback = () => {\n        var _a;\n        return (_a = this.clerkjs) == null ? void 0 : _a.redirectToSignUp(opts);\n      };\n      if (this.clerkjs && this.loaded) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"redirectToSignUp\", callback);\n        return;\n      }\n    };\n    this.redirectToUserProfile = async () => {\n      const callback = () => {\n        var _a;\n        return (_a = this.clerkjs) == null ? void 0 : _a.redirectToUserProfile();\n      };\n      if (this.clerkjs && this.loaded) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"redirectToUserProfile\", callback);\n        return;\n      }\n    };\n    this.redirectToAfterSignUp = () => {\n      const callback = () => {\n        var _a;\n        return (_a = this.clerkjs) == null ? void 0 : _a.redirectToAfterSignUp();\n      };\n      if (this.clerkjs && this.loaded) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"redirectToAfterSignUp\", callback);\n      }\n    };\n    this.redirectToAfterSignIn = () => {\n      const callback = () => {\n        var _a;\n        return (_a = this.clerkjs) == null ? void 0 : _a.redirectToAfterSignIn();\n      };\n      if (this.clerkjs && this.loaded) {\n        callback();\n      } else {\n        this.premountMethodCalls.set(\"redirectToAfterSignIn\", callback);\n      }\n    };\n    this.redirectToAfterSignOut = () => {\n      const callback = () => {\n        var _a;\n        return (_a = this.clerkjs) == null ? void 0 : _a.redirectToAfterSignOut();\n      };\n      if (this.clerkjs && this.loaded) {\n        callback();\n      } else {\n        this.premountMethodCalls.set(\"redirectToAfterSignOut\", callback);\n      }\n    };\n    this.redirectToOrganizationProfile = async () => {\n      const callback = () => {\n        var _a;\n        return (_a = this.clerkjs) == null ? void 0 : _a.redirectToOrganizationProfile();\n      };\n      if (this.clerkjs && this.loaded) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"redirectToOrganizationProfile\", callback);\n        return;\n      }\n    };\n    this.redirectToCreateOrganization = async () => {\n      const callback = () => {\n        var _a;\n        return (_a = this.clerkjs) == null ? void 0 : _a.redirectToCreateOrganization();\n      };\n      if (this.clerkjs && this.loaded) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"redirectToCreateOrganization\", callback);\n        return;\n      }\n    };\n    this.redirectToWaitlist = async () => {\n      const callback = () => {\n        var _a;\n        return (_a = this.clerkjs) == null ? void 0 : _a.redirectToWaitlist();\n      };\n      if (this.clerkjs && this.loaded) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"redirectToWaitlist\", callback);\n        return;\n      }\n    };\n    this.handleRedirectCallback = async (params) => {\n      var _a;\n      const callback = () => {\n        var _a2;\n        return (_a2 = this.clerkjs) == null ? void 0 : _a2.handleRedirectCallback(params);\n      };\n      if (this.clerkjs && this.loaded) {\n        void ((_a = callback()) == null ? void 0 : _a.catch(() => {\n        }));\n      } else {\n        this.premountMethodCalls.set(\"handleRedirectCallback\", callback);\n      }\n    };\n    this.handleGoogleOneTapCallback = async (signInOrUp, params) => {\n      var _a;\n      const callback = () => {\n        var _a2;\n        return (_a2 = this.clerkjs) == null ? void 0 : _a2.handleGoogleOneTapCallback(signInOrUp, params);\n      };\n      if (this.clerkjs && this.loaded) {\n        void ((_a = callback()) == null ? void 0 : _a.catch(() => {\n        }));\n      } else {\n        this.premountMethodCalls.set(\"handleGoogleOneTapCallback\", callback);\n      }\n    };\n    this.handleEmailLinkVerification = async (params) => {\n      const callback = () => {\n        var _a;\n        return (_a = this.clerkjs) == null ? void 0 : _a.handleEmailLinkVerification(params);\n      };\n      if (this.clerkjs && this.loaded) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"handleEmailLinkVerification\", callback);\n      }\n    };\n    this.authenticateWithMetamask = async (params) => {\n      const callback = () => {\n        var _a;\n        return (_a = this.clerkjs) == null ? void 0 : _a.authenticateWithMetamask(params);\n      };\n      if (this.clerkjs && this.loaded) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"authenticateWithMetamask\", callback);\n      }\n    };\n    this.authenticateWithCoinbaseWallet = async (params) => {\n      const callback = () => {\n        var _a;\n        return (_a = this.clerkjs) == null ? void 0 : _a.authenticateWithCoinbaseWallet(params);\n      };\n      if (this.clerkjs && this.loaded) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"authenticateWithCoinbaseWallet\", callback);\n      }\n    };\n    this.authenticateWithOKXWallet = async (params) => {\n      const callback = () => {\n        var _a;\n        return (_a = this.clerkjs) == null ? void 0 : _a.authenticateWithOKXWallet(params);\n      };\n      if (this.clerkjs && this.loaded) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"authenticateWithOKXWallet\", callback);\n      }\n    };\n    this.authenticateWithWeb3 = async (params) => {\n      const callback = () => {\n        var _a;\n        return (_a = this.clerkjs) == null ? void 0 : _a.authenticateWithWeb3(params);\n      };\n      if (this.clerkjs && this.loaded) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"authenticateWithWeb3\", callback);\n      }\n    };\n    this.authenticateWithGoogleOneTap = async (params) => {\n      const clerkjs = await (0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_1__.__privateMethod)(this, _IsomorphicClerk_instances, waitForClerkJS_fn).call(this);\n      return clerkjs.authenticateWithGoogleOneTap(params);\n    };\n    this.createOrganization = async (params) => {\n      const callback = () => {\n        var _a;\n        return (_a = this.clerkjs) == null ? void 0 : _a.createOrganization(params);\n      };\n      if (this.clerkjs && this.loaded) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"createOrganization\", callback);\n      }\n    };\n    this.getOrganization = async (organizationId) => {\n      const callback = () => {\n        var _a;\n        return (_a = this.clerkjs) == null ? void 0 : _a.getOrganization(organizationId);\n      };\n      if (this.clerkjs && this.loaded) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"getOrganization\", callback);\n      }\n    };\n    this.joinWaitlist = async (params) => {\n      const callback = () => {\n        var _a;\n        return (_a = this.clerkjs) == null ? void 0 : _a.joinWaitlist(params);\n      };\n      if (this.clerkjs && this.loaded) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"joinWaitlist\", callback);\n      }\n    };\n    this.signOut = async (...args) => {\n      const callback = () => {\n        var _a;\n        return (_a = this.clerkjs) == null ? void 0 : _a.signOut(...args);\n      };\n      if (this.clerkjs && this.loaded) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"signOut\", callback);\n      }\n    };\n    const { Clerk = null, publishableKey } = options || {};\n    (0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_1__.__privateSet)(this, _publishableKey, publishableKey);\n    (0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_1__.__privateSet)(this, _proxyUrl, options == null ? void 0 : options.proxyUrl);\n    (0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_1__.__privateSet)(this, _domain, options == null ? void 0 : options.domain);\n    this.options = options;\n    this.Clerk = Clerk;\n    this.mode = (0,_clerk_shared_browser__WEBPACK_IMPORTED_MODULE_10__.inBrowser)() ? \"browser\" : \"server\";\n    if (!this.options.sdkMetadata) {\n      this.options.sdkMetadata = SDK_METADATA;\n    }\n    (0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_1__.__privateGet)(this, _eventBus).emit(_clerk_shared_clerkEventBus__WEBPACK_IMPORTED_MODULE_11__.clerkEvents.Status, \"loading\");\n    (0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_1__.__privateGet)(this, _eventBus).prioritizedOn(_clerk_shared_clerkEventBus__WEBPACK_IMPORTED_MODULE_11__.clerkEvents.Status, (status) => (0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_1__.__privateSet)(this, _status, status));\n    if ((0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_1__.__privateGet)(this, _publishableKey)) {\n      void this.loadClerkJS();\n    }\n  }\n  get publishableKey() {\n    return (0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_1__.__privateGet)(this, _publishableKey);\n  }\n  get loaded() {\n    var _a;\n    return ((_a = this.clerkjs) == null ? void 0 : _a.loaded) || false;\n  }\n  get status() {\n    var _a;\n    if (!this.clerkjs) {\n      return (0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_1__.__privateGet)(this, _status);\n    }\n    return ((_a = this.clerkjs) == null ? void 0 : _a.status) || /**\n     * Support older clerk-js versions.\n     * If clerk-js is available but `.status` is missing it we need to fallback to `.loaded`.\n     * Since \"degraded\" an \"error\" did not exist before,\n     * map \"loaded\" to \"ready\" and \"not loaded\" to \"loading\".\n     */\n    (this.clerkjs.loaded ? \"ready\" : \"loading\");\n  }\n  static getOrCreateInstance(options) {\n    if (!(0,_clerk_shared_browser__WEBPACK_IMPORTED_MODULE_10__.inBrowser)() || !(0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_1__.__privateGet)(this, _instance) || options.Clerk && (0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_1__.__privateGet)(this, _instance).Clerk !== options.Clerk || // Allow hot swapping PKs on the client\n    (0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_1__.__privateGet)(this, _instance).publishableKey !== options.publishableKey) {\n      (0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_1__.__privateSet)(this, _instance, new _IsomorphicClerk(options));\n    }\n    return (0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_1__.__privateGet)(this, _instance);\n  }\n  static clearInstance() {\n    (0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_1__.__privateSet)(this, _instance, null);\n  }\n  get domain() {\n    if (typeof window !== \"undefined\" && window.location) {\n      return (0,_clerk_shared_utils__WEBPACK_IMPORTED_MODULE_3__.handleValueOrFn)((0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_1__.__privateGet)(this, _domain), new URL(window.location.href), \"\");\n    }\n    if (typeof (0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_1__.__privateGet)(this, _domain) === \"function\") {\n      return _chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.errorThrower.throw(_chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.unsupportedNonBrowserDomainOrProxyUrlFunction);\n    }\n    return (0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_1__.__privateGet)(this, _domain) || \"\";\n  }\n  get proxyUrl() {\n    if (typeof window !== \"undefined\" && window.location) {\n      return (0,_clerk_shared_utils__WEBPACK_IMPORTED_MODULE_3__.handleValueOrFn)((0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_1__.__privateGet)(this, _proxyUrl), new URL(window.location.href), \"\");\n    }\n    if (typeof (0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_1__.__privateGet)(this, _proxyUrl) === \"function\") {\n      return _chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.errorThrower.throw(_chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.unsupportedNonBrowserDomainOrProxyUrlFunction);\n    }\n    return (0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_1__.__privateGet)(this, _proxyUrl) || \"\";\n  }\n  /**\n   * Accesses private options from the `Clerk` instance and defaults to\n   * `IsomorphicClerk` options when in SSR context.\n   *  @internal\n   */\n  __internal_getOption(key) {\n    var _a, _b;\n    return ((_a = this.clerkjs) == null ? void 0 : _a.__internal_getOption) ? (_b = this.clerkjs) == null ? void 0 : _b.__internal_getOption(key) : this.options[key];\n  }\n  get sdkMetadata() {\n    var _a;\n    return ((_a = this.clerkjs) == null ? void 0 : _a.sdkMetadata) || this.options.sdkMetadata || void 0;\n  }\n  get instanceType() {\n    var _a;\n    return (_a = this.clerkjs) == null ? void 0 : _a.instanceType;\n  }\n  get frontendApi() {\n    var _a;\n    return ((_a = this.clerkjs) == null ? void 0 : _a.frontendApi) || \"\";\n  }\n  get isStandardBrowser() {\n    var _a;\n    return ((_a = this.clerkjs) == null ? void 0 : _a.isStandardBrowser) || this.options.standardBrowser || false;\n  }\n  get isSatellite() {\n    if (typeof window !== \"undefined\" && window.location) {\n      return (0,_clerk_shared_utils__WEBPACK_IMPORTED_MODULE_3__.handleValueOrFn)(this.options.isSatellite, new URL(window.location.href), false);\n    }\n    if (typeof this.options.isSatellite === \"function\") {\n      return _chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.errorThrower.throw(_chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.unsupportedNonBrowserDomainOrProxyUrlFunction);\n    }\n    return false;\n  }\n  async loadClerkJS() {\n    var _a;\n    if (this.mode !== \"browser\" || this.loaded) {\n      return;\n    }\n    if (typeof window !== \"undefined\") {\n      window.__clerk_publishable_key = (0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_1__.__privateGet)(this, _publishableKey);\n      window.__clerk_proxy_url = this.proxyUrl;\n      window.__clerk_domain = this.domain;\n    }\n    try {\n      if (this.Clerk) {\n        let c;\n        if (isConstructor(this.Clerk)) {\n          c = new this.Clerk((0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_1__.__privateGet)(this, _publishableKey), {\n            proxyUrl: this.proxyUrl,\n            domain: this.domain\n          });\n          this.beforeLoad(c);\n          await c.load(this.options);\n        } else {\n          c = this.Clerk;\n          if (!c.loaded) {\n            this.beforeLoad(c);\n            await c.load(this.options);\n          }\n        }\n        global.Clerk = c;\n      } else if (!__BUILD_DISABLE_RHC__) {\n        if (!global.Clerk) {\n          await (0,_clerk_shared_loadClerkJsScript__WEBPACK_IMPORTED_MODULE_2__.loadClerkJsScript)({\n            ...this.options,\n            publishableKey: (0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_1__.__privateGet)(this, _publishableKey),\n            proxyUrl: this.proxyUrl,\n            domain: this.domain,\n            nonce: this.options.nonce\n          });\n        }\n        if (!global.Clerk) {\n          throw new Error(\"Failed to download latest ClerkJS. Contact <EMAIL>.\");\n        }\n        this.beforeLoad(global.Clerk);\n        await global.Clerk.load(this.options);\n      }\n      if ((_a = global.Clerk) == null ? void 0 : _a.loaded) {\n        return this.hydrateClerkJS(global.Clerk);\n      }\n      return;\n    } catch (err) {\n      const error = err;\n      (0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_1__.__privateGet)(this, _eventBus).emit(_clerk_shared_clerkEventBus__WEBPACK_IMPORTED_MODULE_11__.clerkEvents.Status, \"error\");\n      console.error(error.stack || error.message || error);\n      return;\n    }\n  }\n  get version() {\n    var _a;\n    return (_a = this.clerkjs) == null ? void 0 : _a.version;\n  }\n  get client() {\n    if (this.clerkjs) {\n      return this.clerkjs.client;\n    } else {\n      return void 0;\n    }\n  }\n  get session() {\n    if (this.clerkjs) {\n      return this.clerkjs.session;\n    } else {\n      return void 0;\n    }\n  }\n  get user() {\n    if (this.clerkjs) {\n      return this.clerkjs.user;\n    } else {\n      return void 0;\n    }\n  }\n  get organization() {\n    if (this.clerkjs) {\n      return this.clerkjs.organization;\n    } else {\n      return void 0;\n    }\n  }\n  get telemetry() {\n    if (this.clerkjs) {\n      return this.clerkjs.telemetry;\n    } else {\n      return void 0;\n    }\n  }\n  get __unstable__environment() {\n    if (this.clerkjs) {\n      return this.clerkjs.__unstable__environment;\n    } else {\n      return void 0;\n    }\n  }\n  get isSignedIn() {\n    if (this.clerkjs) {\n      return this.clerkjs.isSignedIn;\n    } else {\n      return false;\n    }\n  }\n  get billing() {\n    var _a;\n    return (_a = this.clerkjs) == null ? void 0 : _a.billing;\n  }\n  __unstable__setEnvironment(...args) {\n    if (this.clerkjs && \"__unstable__setEnvironment\" in this.clerkjs) {\n      this.clerkjs.__unstable__setEnvironment(args);\n    } else {\n      return void 0;\n    }\n  }\n};\n_status = new WeakMap();\n_domain = new WeakMap();\n_proxyUrl = new WeakMap();\n_publishableKey = new WeakMap();\n_eventBus = new WeakMap();\n_instance = new WeakMap();\n_IsomorphicClerk_instances = new WeakSet();\nwaitForClerkJS_fn = function() {\n  return new Promise((resolve) => {\n    this.addOnLoaded(() => resolve(this.clerkjs));\n  });\n};\n(0,_chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_1__.__privateAdd)(_IsomorphicClerk, _instance);\nvar IsomorphicClerk = _IsomorphicClerk;\n\n// src/contexts/ClerkContextProvider.tsx\nfunction ClerkContextProvider(props) {\n  const { isomorphicClerkOptions, initialState, children } = props;\n  const { isomorphicClerk: clerk, clerkStatus } = useLoadedIsomorphicClerk(isomorphicClerkOptions);\n  const [state, setState] = react__WEBPACK_IMPORTED_MODULE_4__.useState({\n    client: clerk.client,\n    session: clerk.session,\n    user: clerk.user,\n    organization: clerk.organization\n  });\n  react__WEBPACK_IMPORTED_MODULE_4__.useEffect(() => {\n    return clerk.addListener((e) => setState({ ...e }));\n  }, []);\n  const derivedState = (0,_clerk_shared_deriveState__WEBPACK_IMPORTED_MODULE_9__.deriveState)(clerk.loaded, state, initialState);\n  const clerkCtx = react__WEBPACK_IMPORTED_MODULE_4__.useMemo(\n    () => ({ value: clerk }),\n    [\n      // Only update the clerk reference on status change\n      clerkStatus\n    ]\n  );\n  const clientCtx = react__WEBPACK_IMPORTED_MODULE_4__.useMemo(() => ({ value: state.client }), [state.client]);\n  const {\n    sessionId,\n    sessionStatus,\n    sessionClaims,\n    session,\n    userId,\n    user,\n    orgId,\n    actor,\n    organization,\n    orgRole,\n    orgSlug,\n    orgPermissions,\n    factorVerificationAge\n  } = derivedState;\n  const authCtx = react__WEBPACK_IMPORTED_MODULE_4__.useMemo(() => {\n    const value = {\n      sessionId,\n      sessionStatus,\n      sessionClaims,\n      userId,\n      actor,\n      orgId,\n      orgRole,\n      orgSlug,\n      orgPermissions,\n      factorVerificationAge\n    };\n    return { value };\n  }, [sessionId, sessionStatus, userId, actor, orgId, orgRole, orgSlug, factorVerificationAge, sessionClaims == null ? void 0 : sessionClaims.__raw]);\n  const sessionCtx = react__WEBPACK_IMPORTED_MODULE_4__.useMemo(() => ({ value: session }), [sessionId, session]);\n  const userCtx = react__WEBPACK_IMPORTED_MODULE_4__.useMemo(() => ({ value: user }), [userId, user]);\n  const organizationCtx = react__WEBPACK_IMPORTED_MODULE_4__.useMemo(() => {\n    const value = {\n      organization\n    };\n    return { value };\n  }, [orgId, organization]);\n  return (\n    // @ts-expect-error value passed is of type IsomorphicClerk where the context expects LoadedClerk\n    /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_4__.createElement(_chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.IsomorphicClerkContext.Provider, { value: clerkCtx }, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_4__.createElement(_clerk_shared_react__WEBPACK_IMPORTED_MODULE_7__.ClientContext.Provider, { value: clientCtx }, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_4__.createElement(_clerk_shared_react__WEBPACK_IMPORTED_MODULE_7__.SessionContext.Provider, { value: sessionCtx }, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_4__.createElement(_clerk_shared_react__WEBPACK_IMPORTED_MODULE_7__.OrganizationProvider, { ...organizationCtx.value }, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_4__.createElement(_chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.AuthContext.Provider, { value: authCtx }, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_4__.createElement(_clerk_shared_react__WEBPACK_IMPORTED_MODULE_7__.UserContext.Provider, { value: userCtx }, children))))))\n  );\n}\nvar useLoadedIsomorphicClerk = (options) => {\n  const isomorphicClerk = react__WEBPACK_IMPORTED_MODULE_4__.useMemo(() => IsomorphicClerk.getOrCreateInstance(options), []);\n  const [clerkStatus, setStatus] = react__WEBPACK_IMPORTED_MODULE_4__.useState(isomorphicClerk.status);\n  react__WEBPACK_IMPORTED_MODULE_4__.useEffect(() => {\n    void isomorphicClerk.__unstable__updateProps({ appearance: options.appearance });\n  }, [options.appearance]);\n  react__WEBPACK_IMPORTED_MODULE_4__.useEffect(() => {\n    void isomorphicClerk.__unstable__updateProps({ options });\n  }, [options.localization]);\n  react__WEBPACK_IMPORTED_MODULE_4__.useEffect(() => {\n    isomorphicClerk.on(\"status\", setStatus);\n    return () => isomorphicClerk.off(\"status\", setStatus);\n  }, [isomorphicClerk]);\n  react__WEBPACK_IMPORTED_MODULE_4__.useEffect(() => {\n    return () => {\n      IsomorphicClerk.clearInstance();\n    };\n  }, []);\n  return { isomorphicClerk, clerkStatus };\n};\n\n// src/contexts/ClerkProvider.tsx\nfunction ClerkProviderBase(props) {\n  const { initialState, children, __internal_bypassMissingPublishableKey, ...restIsomorphicClerkOptions } = props;\n  const { publishableKey = \"\", Clerk: userInitialisedClerk } = restIsomorphicClerkOptions;\n  if (!userInitialisedClerk && !__internal_bypassMissingPublishableKey) {\n    if (!publishableKey) {\n      _chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.errorThrower.throwMissingPublishableKeyError();\n    } else if (publishableKey && !(0,_clerk_shared_keys__WEBPACK_IMPORTED_MODULE_8__.isPublishableKey)(publishableKey)) {\n      _chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.errorThrower.throwInvalidPublishableKeyError({ key: publishableKey });\n    }\n  }\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_4__.createElement(\n    ClerkContextProvider,\n    {\n      initialState,\n      isomorphicClerkOptions: restIsomorphicClerkOptions\n    },\n    children\n  );\n}\nvar ClerkProvider = withMaxAllowedInstancesGuard(ClerkProviderBase, \"ClerkProvider\", _chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.multipleClerkProvidersError);\nClerkProvider.displayName = \"ClerkProvider\";\n\n// src/index.ts\n(0,_chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.setErrorThrowerOptions)({ packageName: \"@clerk/clerk-react\" });\n(0,_clerk_shared_loadClerkJsScript__WEBPACK_IMPORTED_MODULE_2__.setClerkJsLoadingErrorPackageName)(\"@clerk/clerk-react\");\n\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@clerk+clerk-react@5.31.6_r_cf8c9a1d0ee47888815beb987e54134a/node_modules/@clerk/clerk-react/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@clerk+clerk-react@5.31.6_r_cf8c9a1d0ee47888815beb987e54134a/node_modules/@clerk/clerk-react/dist/internal.mjs":
/*!***********************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@clerk+clerk-react@5.31.6_r_cf8c9a1d0ee47888815beb987e54134a/node_modules/@clerk/clerk-react/dist/internal.mjs ***!
  \***********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MultisessionAppSupport: () => (/* reexport safe */ _chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.MultisessionAppSupport),\n/* harmony export */   buildClerkJsScriptAttributes: () => (/* reexport safe */ _clerk_shared_loadClerkJsScript__WEBPACK_IMPORTED_MODULE_2__.buildClerkJsScriptAttributes),\n/* harmony export */   clerkJsScriptUrl: () => (/* reexport safe */ _clerk_shared_loadClerkJsScript__WEBPACK_IMPORTED_MODULE_2__.clerkJsScriptUrl),\n/* harmony export */   setClerkJsLoadingErrorPackageName: () => (/* reexport safe */ _clerk_shared_loadClerkJsScript__WEBPACK_IMPORTED_MODULE_2__.setClerkJsLoadingErrorPackageName),\n/* harmony export */   setErrorThrowerOptions: () => (/* reexport safe */ _chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.setErrorThrowerOptions),\n/* harmony export */   useDerivedAuth: () => (/* reexport safe */ _chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.useDerivedAuth),\n/* harmony export */   useRoutingProps: () => (/* binding */ useRoutingProps)\n/* harmony export */ });\n/* harmony import */ var _chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-T2VIWQBM.mjs */ \"(ssr)/../../node_modules/.pnpm/@clerk+clerk-react@5.31.6_r_cf8c9a1d0ee47888815beb987e54134a/node_modules/@clerk/clerk-react/dist/chunk-T2VIWQBM.mjs\");\n/* harmony import */ var _chunk_OANWQR3B_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-OANWQR3B.mjs */ \"(ssr)/../../node_modules/.pnpm/@clerk+clerk-react@5.31.6_r_cf8c9a1d0ee47888815beb987e54134a/node_modules/@clerk/clerk-react/dist/chunk-OANWQR3B.mjs\");\n/* harmony import */ var _clerk_shared_loadClerkJsScript__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @clerk/shared/loadClerkJsScript */ \"(ssr)/../../node_modules/.pnpm/@clerk+shared@3.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/@clerk/shared/dist/loadClerkJsScript.mjs\");\n\n\n\n// src/hooks/useRoutingProps.ts\nfunction useRoutingProps(componentName, props, routingOptions) {\n  const path = props.path || (routingOptions == null ? void 0 : routingOptions.path);\n  const routing = props.routing || (routingOptions == null ? void 0 : routingOptions.routing) || \"path\";\n  if (routing === \"path\") {\n    if (!path) {\n      return _chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.errorThrower.throw((0,_chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.noPathProvidedError)(componentName));\n    }\n    return {\n      ...routingOptions,\n      ...props,\n      routing: \"path\"\n    };\n  }\n  if (props.path) {\n    return _chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.errorThrower.throw((0,_chunk_T2VIWQBM_mjs__WEBPACK_IMPORTED_MODULE_0__.incompatibleRoutingWithPathProvidedError)(componentName));\n  }\n  return {\n    ...routingOptions,\n    ...props,\n    path: void 0\n  };\n}\n\n// src/internal.ts\n\n\n//# sourceMappingURL=internal.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BjbGVyaytjbGVyay1yZWFjdEA1LjMxLjZfcl9jZjhjOWExZDBlZTQ3ODg4ODE1YmViOTg3ZTU0MTM0YS9ub2RlX21vZHVsZXMvQGNsZXJrL2NsZXJrLXJlYWN0L2Rpc3QvaW50ZXJuYWwubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFPOEI7QUFDQTs7QUFFOUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYSw2REFBWSxPQUFPLHdFQUFtQjtBQUNuRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyw2REFBWSxPQUFPLDZGQUF3QztBQUN0RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUt5QztBQVN2QztBQUNGIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFud2FyXFxEb2N1bWVudHNcXEN1YmVudHdlYlxcY3ViZW50XFxub2RlX21vZHVsZXNcXC5wbnBtXFxAY2xlcmsrY2xlcmstcmVhY3RANS4zMS42X3JfY2Y4YzlhMWQwZWU0Nzg4ODgxNWJlYjk4N2U1NDEzNGFcXG5vZGVfbW9kdWxlc1xcQGNsZXJrXFxjbGVyay1yZWFjdFxcZGlzdFxcaW50ZXJuYWwubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7XG4gIE11bHRpc2Vzc2lvbkFwcFN1cHBvcnQsXG4gIGVycm9yVGhyb3dlcixcbiAgaW5jb21wYXRpYmxlUm91dGluZ1dpdGhQYXRoUHJvdmlkZWRFcnJvcixcbiAgbm9QYXRoUHJvdmlkZWRFcnJvcixcbiAgc2V0RXJyb3JUaHJvd2VyT3B0aW9ucyxcbiAgdXNlRGVyaXZlZEF1dGhcbn0gZnJvbSBcIi4vY2h1bmstVDJWSVdRQk0ubWpzXCI7XG5pbXBvcnQgXCIuL2NodW5rLU9BTldRUjNCLm1qc1wiO1xuXG4vLyBzcmMvaG9va3MvdXNlUm91dGluZ1Byb3BzLnRzXG5mdW5jdGlvbiB1c2VSb3V0aW5nUHJvcHMoY29tcG9uZW50TmFtZSwgcHJvcHMsIHJvdXRpbmdPcHRpb25zKSB7XG4gIGNvbnN0IHBhdGggPSBwcm9wcy5wYXRoIHx8IChyb3V0aW5nT3B0aW9ucyA9PSBudWxsID8gdm9pZCAwIDogcm91dGluZ09wdGlvbnMucGF0aCk7XG4gIGNvbnN0IHJvdXRpbmcgPSBwcm9wcy5yb3V0aW5nIHx8IChyb3V0aW5nT3B0aW9ucyA9PSBudWxsID8gdm9pZCAwIDogcm91dGluZ09wdGlvbnMucm91dGluZykgfHwgXCJwYXRoXCI7XG4gIGlmIChyb3V0aW5nID09PSBcInBhdGhcIikge1xuICAgIGlmICghcGF0aCkge1xuICAgICAgcmV0dXJuIGVycm9yVGhyb3dlci50aHJvdyhub1BhdGhQcm92aWRlZEVycm9yKGNvbXBvbmVudE5hbWUpKTtcbiAgICB9XG4gICAgcmV0dXJuIHtcbiAgICAgIC4uLnJvdXRpbmdPcHRpb25zLFxuICAgICAgLi4ucHJvcHMsXG4gICAgICByb3V0aW5nOiBcInBhdGhcIlxuICAgIH07XG4gIH1cbiAgaWYgKHByb3BzLnBhdGgpIHtcbiAgICByZXR1cm4gZXJyb3JUaHJvd2VyLnRocm93KGluY29tcGF0aWJsZVJvdXRpbmdXaXRoUGF0aFByb3ZpZGVkRXJyb3IoY29tcG9uZW50TmFtZSkpO1xuICB9XG4gIHJldHVybiB7XG4gICAgLi4ucm91dGluZ09wdGlvbnMsXG4gICAgLi4ucHJvcHMsXG4gICAgcGF0aDogdm9pZCAwXG4gIH07XG59XG5cbi8vIHNyYy9pbnRlcm5hbC50c1xuaW1wb3J0IHtcbiAgY2xlcmtKc1NjcmlwdFVybCxcbiAgYnVpbGRDbGVya0pzU2NyaXB0QXR0cmlidXRlcyxcbiAgc2V0Q2xlcmtKc0xvYWRpbmdFcnJvclBhY2thZ2VOYW1lXG59IGZyb20gXCJAY2xlcmsvc2hhcmVkL2xvYWRDbGVya0pzU2NyaXB0XCI7XG5leHBvcnQge1xuICBNdWx0aXNlc3Npb25BcHBTdXBwb3J0LFxuICBidWlsZENsZXJrSnNTY3JpcHRBdHRyaWJ1dGVzLFxuICBjbGVya0pzU2NyaXB0VXJsLFxuICBzZXRDbGVya0pzTG9hZGluZ0Vycm9yUGFja2FnZU5hbWUsXG4gIHNldEVycm9yVGhyb3dlck9wdGlvbnMsXG4gIHVzZURlcml2ZWRBdXRoLFxuICB1c2VSb3V0aW5nUHJvcHNcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbnRlcm5hbC5tanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@clerk+clerk-react@5.31.6_r_cf8c9a1d0ee47888815beb987e54134a/node_modules/@clerk/clerk-react/dist/internal.mjs\n");

/***/ })

};
;