"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@opentelemetry+sql-common@0.40.1_@opentelemetry+api@1.9.0";
exports.ids = ["vendor-chunks/@opentelemetry+sql-common@0.40.1_@opentelemetry+api@1.9.0"];
exports.modules = {

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+sql-common@0.40.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/sql-common/build/src/index.js":
/*!****************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+sql-common@0.40.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/sql-common/build/src/index.js ***!
  \****************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.addSqlCommenterComment = void 0;\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst core_1 = __webpack_require__(/*! @opentelemetry/core */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+core@1.30.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/core/build/esm/index.js\");\n// NOTE: This function currently is returning false-positives\n// in cases where comment characters appear in string literals\n// (\"SELECT '-- not a comment';\" would return true, although has no comment)\nfunction hasValidSqlComment(query) {\n    const indexOpeningDashDashComment = query.indexOf('--');\n    if (indexOpeningDashDashComment >= 0) {\n        return true;\n    }\n    const indexOpeningSlashComment = query.indexOf('/*');\n    if (indexOpeningSlashComment < 0) {\n        return false;\n    }\n    const indexClosingSlashComment = query.indexOf('*/');\n    return indexOpeningDashDashComment < indexClosingSlashComment;\n}\n// sqlcommenter specification (https://google.github.io/sqlcommenter/spec/#value-serialization)\n// expects us to URL encode based on the RFC 3986 spec (https://en.wikipedia.org/wiki/Percent-encoding),\n// but encodeURIComponent does not handle some characters correctly (! ' ( ) *),\n// which means we need special handling for this\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/encodeURIComponent\nfunction fixedEncodeURIComponent(str) {\n    return encodeURIComponent(str).replace(/[!'()*]/g, c => `%${c.charCodeAt(0).toString(16).toUpperCase()}`);\n}\nfunction addSqlCommenterComment(span, query) {\n    if (typeof query !== 'string' || query.length === 0) {\n        return query;\n    }\n    // As per sqlcommenter spec we shall not add a comment if there already is a comment\n    // in the query\n    if (hasValidSqlComment(query)) {\n        return query;\n    }\n    const propagator = new core_1.W3CTraceContextPropagator();\n    const headers = {};\n    propagator.inject(api_1.trace.setSpan(api_1.ROOT_CONTEXT, span), headers, api_1.defaultTextMapSetter);\n    // sqlcommenter spec requires keys in the comment to be sorted lexicographically\n    const sortedKeys = Object.keys(headers).sort();\n    if (sortedKeys.length === 0) {\n        return query;\n    }\n    const commentString = sortedKeys\n        .map(key => {\n        const encodedValue = fixedEncodeURIComponent(headers[key]);\n        return `${key}='${encodedValue}'`;\n    })\n        .join(',');\n    return `${query} /*${commentString}*/`;\n}\nexports.addSqlCommenterComment = addSqlCommenterComment;\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+sql-common@0.40.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/sql-common/build/src/index.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+sql-common@0.40.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/sql-common/build/src/index.js":
/*!****************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+sql-common@0.40.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/sql-common/build/src/index.js ***!
  \****************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.addSqlCommenterComment = void 0;\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst core_1 = __webpack_require__(/*! @opentelemetry/core */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+core@1.30.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/core/build/esm/index.js\");\n// NOTE: This function currently is returning false-positives\n// in cases where comment characters appear in string literals\n// (\"SELECT '-- not a comment';\" would return true, although has no comment)\nfunction hasValidSqlComment(query) {\n    const indexOpeningDashDashComment = query.indexOf('--');\n    if (indexOpeningDashDashComment >= 0) {\n        return true;\n    }\n    const indexOpeningSlashComment = query.indexOf('/*');\n    if (indexOpeningSlashComment < 0) {\n        return false;\n    }\n    const indexClosingSlashComment = query.indexOf('*/');\n    return indexOpeningDashDashComment < indexClosingSlashComment;\n}\n// sqlcommenter specification (https://google.github.io/sqlcommenter/spec/#value-serialization)\n// expects us to URL encode based on the RFC 3986 spec (https://en.wikipedia.org/wiki/Percent-encoding),\n// but encodeURIComponent does not handle some characters correctly (! ' ( ) *),\n// which means we need special handling for this\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/encodeURIComponent\nfunction fixedEncodeURIComponent(str) {\n    return encodeURIComponent(str).replace(/[!'()*]/g, c => `%${c.charCodeAt(0).toString(16).toUpperCase()}`);\n}\nfunction addSqlCommenterComment(span, query) {\n    if (typeof query !== 'string' || query.length === 0) {\n        return query;\n    }\n    // As per sqlcommenter spec we shall not add a comment if there already is a comment\n    // in the query\n    if (hasValidSqlComment(query)) {\n        return query;\n    }\n    const propagator = new core_1.W3CTraceContextPropagator();\n    const headers = {};\n    propagator.inject(api_1.trace.setSpan(api_1.ROOT_CONTEXT, span), headers, api_1.defaultTextMapSetter);\n    // sqlcommenter spec requires keys in the comment to be sorted lexicographically\n    const sortedKeys = Object.keys(headers).sort();\n    if (sortedKeys.length === 0) {\n        return query;\n    }\n    const commentString = sortedKeys\n        .map(key => {\n        const encodedValue = fixedEncodeURIComponent(headers[key]);\n        return `${key}='${encodedValue}'`;\n    })\n        .join(',');\n    return `${query} /*${commentString}*/`;\n}\nexports.addSqlCommenterComment = addSqlCommenterComment;\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+sql-common@0.40.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/sql-common/build/src/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+sql-common@0.40.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/sql-common/build/src/index.js":
/*!****************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+sql-common@0.40.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/sql-common/build/src/index.js ***!
  \****************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.addSqlCommenterComment = void 0;\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst core_1 = __webpack_require__(/*! @opentelemetry/core */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+core@1.30.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/core/build/esm/index.js\");\n// NOTE: This function currently is returning false-positives\n// in cases where comment characters appear in string literals\n// (\"SELECT '-- not a comment';\" would return true, although has no comment)\nfunction hasValidSqlComment(query) {\n    const indexOpeningDashDashComment = query.indexOf('--');\n    if (indexOpeningDashDashComment >= 0) {\n        return true;\n    }\n    const indexOpeningSlashComment = query.indexOf('/*');\n    if (indexOpeningSlashComment < 0) {\n        return false;\n    }\n    const indexClosingSlashComment = query.indexOf('*/');\n    return indexOpeningDashDashComment < indexClosingSlashComment;\n}\n// sqlcommenter specification (https://google.github.io/sqlcommenter/spec/#value-serialization)\n// expects us to URL encode based on the RFC 3986 spec (https://en.wikipedia.org/wiki/Percent-encoding),\n// but encodeURIComponent does not handle some characters correctly (! ' ( ) *),\n// which means we need special handling for this\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/encodeURIComponent\nfunction fixedEncodeURIComponent(str) {\n    return encodeURIComponent(str).replace(/[!'()*]/g, c => `%${c.charCodeAt(0).toString(16).toUpperCase()}`);\n}\nfunction addSqlCommenterComment(span, query) {\n    if (typeof query !== 'string' || query.length === 0) {\n        return query;\n    }\n    // As per sqlcommenter spec we shall not add a comment if there already is a comment\n    // in the query\n    if (hasValidSqlComment(query)) {\n        return query;\n    }\n    const propagator = new core_1.W3CTraceContextPropagator();\n    const headers = {};\n    propagator.inject(api_1.trace.setSpan(api_1.ROOT_CONTEXT, span), headers, api_1.defaultTextMapSetter);\n    // sqlcommenter spec requires keys in the comment to be sorted lexicographically\n    const sortedKeys = Object.keys(headers).sort();\n    if (sortedKeys.length === 0) {\n        return query;\n    }\n    const commentString = sortedKeys\n        .map(key => {\n        const encodedValue = fixedEncodeURIComponent(headers[key]);\n        return `${key}='${encodedValue}'`;\n    })\n        .join(',');\n    return `${query} /*${commentString}*/`;\n}\nexports.addSqlCommenterComment = addSqlCommenterComment;\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+sql-common@0.40.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/sql-common/build/src/index.js\n");

/***/ })

};
;