"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-accordion@1_51304fa0823e9b2a065feab26851e0ad";
exports.ids = ["vendor-chunks/@radix-ui+react-accordion@1_51304fa0823e9b2a065feab26851e0ad"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/@radix-ui+react-accordion@1_51304fa0823e9b2a065feab26851e0ad/node_modules/@radix-ui/react-accordion/dist/index.mjs":
/*!***************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@radix-ui+react-accordion@1_51304fa0823e9b2a065feab26851e0ad/node_modules/@radix-ui/react-accordion/dist/index.mjs ***!
  \***************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Accordion: () => (/* binding */ Accordion),\n/* harmony export */   AccordionContent: () => (/* binding */ AccordionContent),\n/* harmony export */   AccordionHeader: () => (/* binding */ AccordionHeader),\n/* harmony export */   AccordionItem: () => (/* binding */ AccordionItem),\n/* harmony export */   AccordionTrigger: () => (/* binding */ AccordionTrigger),\n/* harmony export */   Content: () => (/* binding */ Content2),\n/* harmony export */   Header: () => (/* binding */ Header),\n/* harmony export */   Item: () => (/* binding */ Item),\n/* harmony export */   Root: () => (/* binding */ Root2),\n/* harmony export */   Trigger: () => (/* binding */ Trigger2),\n/* harmony export */   createAccordionScope: () => (/* binding */ createAccordionScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-context@1.1_efc475efe2315f1e47666d242c3ea3f4/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-collection */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-collection@_7918ae119f10c4289f30f285e519ea7e/node_modules/@radix-ui/react-collection/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-compose-ref_3a8f72d8524cae11dbbe71796c2b6a49/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+primitive@1.1.2/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-use-control_038f968d6df614ae636f20523f1cb043/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-primitive@2_0f3a82528133a7d37b0572d0c112c6a5/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_collapsible__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-collapsible */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-collapsible_cecf39ac1375c4a4148ed47a207c8f40/node_modules/@radix-ui/react-collapsible/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-id */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-id@1.1.1_@types+react@19.1.5_react@19.1.0/node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-direction */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-direction@1_e32f95a12a0e23976853758865c76117/node_modules/@radix-ui/react-direction/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Accordion,AccordionContent,AccordionHeader,AccordionItem,AccordionTrigger,Content,Header,Item,Root,Trigger,createAccordionScope auto */ // src/accordion.tsx\n\n\n\n\n\n\n\n\n\n\n\n\nvar ACCORDION_NAME = \"Accordion\";\nvar ACCORDION_KEYS = [\n    \"Home\",\n    \"End\",\n    \"ArrowDown\",\n    \"ArrowUp\",\n    \"ArrowLeft\",\n    \"ArrowRight\"\n];\nvar [Collection, useCollection, createCollectionScope] = (0,_radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_2__.createCollection)(ACCORDION_NAME);\nvar [createAccordionContext, createAccordionScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_3__.createContextScope)(ACCORDION_NAME, [\n    createCollectionScope,\n    _radix_ui_react_collapsible__WEBPACK_IMPORTED_MODULE_4__.createCollapsibleScope\n]);\nvar useCollapsibleScope = (0,_radix_ui_react_collapsible__WEBPACK_IMPORTED_MODULE_4__.createCollapsibleScope)();\nvar Accordion = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { type, ...accordionProps } = props;\n    const singleProps = accordionProps;\n    const multipleProps = accordionProps;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.Provider, {\n        scope: props.__scopeAccordion,\n        children: type === \"multiple\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(AccordionImplMultiple, {\n            ...multipleProps,\n            ref: forwardedRef\n        }) : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(AccordionImplSingle, {\n            ...singleProps,\n            ref: forwardedRef\n        })\n    });\n});\nAccordion.displayName = ACCORDION_NAME;\nvar [AccordionValueProvider, useAccordionValueContext] = createAccordionContext(ACCORDION_NAME);\nvar [AccordionCollapsibleProvider, useAccordionCollapsibleContext] = createAccordionContext(ACCORDION_NAME, {\n    collapsible: false\n});\nvar AccordionImplSingle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { value: valueProp, defaultValue, onValueChange = ()=>{}, collapsible = false, ...accordionSingleProps } = props;\n    const [value, setValue] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_5__.useControllableState)({\n        prop: valueProp,\n        defaultProp: defaultValue ?? \"\",\n        onChange: onValueChange,\n        caller: ACCORDION_NAME\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(AccordionValueProvider, {\n        scope: props.__scopeAccordion,\n        value: react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n            \"AccordionImplSingle.useMemo\": ()=>value ? [\n                    value\n                ] : []\n        }[\"AccordionImplSingle.useMemo\"], [\n            value\n        ]),\n        onItemOpen: setValue,\n        onItemClose: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"AccordionImplSingle.useCallback\": ()=>collapsible && setValue(\"\")\n        }[\"AccordionImplSingle.useCallback\"], [\n            collapsible,\n            setValue\n        ]),\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(AccordionCollapsibleProvider, {\n            scope: props.__scopeAccordion,\n            collapsible,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(AccordionImpl, {\n                ...accordionSingleProps,\n                ref: forwardedRef\n            })\n        })\n    });\n});\nvar AccordionImplMultiple = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { value: valueProp, defaultValue, onValueChange = ()=>{}, ...accordionMultipleProps } = props;\n    const [value, setValue] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_5__.useControllableState)({\n        prop: valueProp,\n        defaultProp: defaultValue ?? [],\n        onChange: onValueChange,\n        caller: ACCORDION_NAME\n    });\n    const handleItemOpen = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"AccordionImplMultiple.useCallback[handleItemOpen]\": (itemValue)=>setValue({\n                \"AccordionImplMultiple.useCallback[handleItemOpen]\": (prevValue = [])=>[\n                        ...prevValue,\n                        itemValue\n                    ]\n            }[\"AccordionImplMultiple.useCallback[handleItemOpen]\"])\n    }[\"AccordionImplMultiple.useCallback[handleItemOpen]\"], [\n        setValue\n    ]);\n    const handleItemClose = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"AccordionImplMultiple.useCallback[handleItemClose]\": (itemValue)=>setValue({\n                \"AccordionImplMultiple.useCallback[handleItemClose]\": (prevValue = [])=>prevValue.filter({\n                        \"AccordionImplMultiple.useCallback[handleItemClose]\": (value2)=>value2 !== itemValue\n                    }[\"AccordionImplMultiple.useCallback[handleItemClose]\"])\n            }[\"AccordionImplMultiple.useCallback[handleItemClose]\"])\n    }[\"AccordionImplMultiple.useCallback[handleItemClose]\"], [\n        setValue\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(AccordionValueProvider, {\n        scope: props.__scopeAccordion,\n        value,\n        onItemOpen: handleItemOpen,\n        onItemClose: handleItemClose,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(AccordionCollapsibleProvider, {\n            scope: props.__scopeAccordion,\n            collapsible: true,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(AccordionImpl, {\n                ...accordionMultipleProps,\n                ref: forwardedRef\n            })\n        })\n    });\n});\nvar [AccordionImplProvider, useAccordionContext] = createAccordionContext(ACCORDION_NAME);\nvar AccordionImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeAccordion, disabled, dir, orientation = \"vertical\", ...accordionProps } = props;\n    const accordionRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_6__.useComposedRefs)(accordionRef, forwardedRef);\n    const getItems = useCollection(__scopeAccordion);\n    const direction = (0,_radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_7__.useDirection)(dir);\n    const isDirectionLTR = direction === \"ltr\";\n    const handleKeyDown = (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onKeyDown, (event)=>{\n        if (!ACCORDION_KEYS.includes(event.key)) return;\n        const target = event.target;\n        const triggerCollection = getItems().filter((item)=>!item.ref.current?.disabled);\n        const triggerIndex = triggerCollection.findIndex((item)=>item.ref.current === target);\n        const triggerCount = triggerCollection.length;\n        if (triggerIndex === -1) return;\n        event.preventDefault();\n        let nextIndex = triggerIndex;\n        const homeIndex = 0;\n        const endIndex = triggerCount - 1;\n        const moveNext = ()=>{\n            nextIndex = triggerIndex + 1;\n            if (nextIndex > endIndex) {\n                nextIndex = homeIndex;\n            }\n        };\n        const movePrev = ()=>{\n            nextIndex = triggerIndex - 1;\n            if (nextIndex < homeIndex) {\n                nextIndex = endIndex;\n            }\n        };\n        switch(event.key){\n            case \"Home\":\n                nextIndex = homeIndex;\n                break;\n            case \"End\":\n                nextIndex = endIndex;\n                break;\n            case \"ArrowRight\":\n                if (orientation === \"horizontal\") {\n                    if (isDirectionLTR) {\n                        moveNext();\n                    } else {\n                        movePrev();\n                    }\n                }\n                break;\n            case \"ArrowDown\":\n                if (orientation === \"vertical\") {\n                    moveNext();\n                }\n                break;\n            case \"ArrowLeft\":\n                if (orientation === \"horizontal\") {\n                    if (isDirectionLTR) {\n                        movePrev();\n                    } else {\n                        moveNext();\n                    }\n                }\n                break;\n            case \"ArrowUp\":\n                if (orientation === \"vertical\") {\n                    movePrev();\n                }\n                break;\n        }\n        const clampedIndex = nextIndex % triggerCount;\n        triggerCollection[clampedIndex].ref.current?.focus();\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(AccordionImplProvider, {\n        scope: __scopeAccordion,\n        disabled,\n        direction: dir,\n        orientation,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.Slot, {\n            scope: __scopeAccordion,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_9__.Primitive.div, {\n                ...accordionProps,\n                \"data-orientation\": orientation,\n                ref: composedRefs,\n                onKeyDown: disabled ? void 0 : handleKeyDown\n            })\n        })\n    });\n});\nvar ITEM_NAME = \"AccordionItem\";\nvar [AccordionItemProvider, useAccordionItemContext] = createAccordionContext(ITEM_NAME);\nvar AccordionItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeAccordion, value, ...accordionItemProps } = props;\n    const accordionContext = useAccordionContext(ITEM_NAME, __scopeAccordion);\n    const valueContext = useAccordionValueContext(ITEM_NAME, __scopeAccordion);\n    const collapsibleScope = useCollapsibleScope(__scopeAccordion);\n    const triggerId = (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_10__.useId)();\n    const open = value && valueContext.value.includes(value) || false;\n    const disabled = accordionContext.disabled || props.disabled;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(AccordionItemProvider, {\n        scope: __scopeAccordion,\n        open,\n        disabled,\n        triggerId,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_collapsible__WEBPACK_IMPORTED_MODULE_4__.Root, {\n            \"data-orientation\": accordionContext.orientation,\n            \"data-state\": getState(open),\n            ...collapsibleScope,\n            ...accordionItemProps,\n            ref: forwardedRef,\n            disabled,\n            open,\n            onOpenChange: (open2)=>{\n                if (open2) {\n                    valueContext.onItemOpen(value);\n                } else {\n                    valueContext.onItemClose(value);\n                }\n            }\n        })\n    });\n});\nAccordionItem.displayName = ITEM_NAME;\nvar HEADER_NAME = \"AccordionHeader\";\nvar AccordionHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeAccordion, ...headerProps } = props;\n    const accordionContext = useAccordionContext(ACCORDION_NAME, __scopeAccordion);\n    const itemContext = useAccordionItemContext(HEADER_NAME, __scopeAccordion);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_9__.Primitive.h3, {\n        \"data-orientation\": accordionContext.orientation,\n        \"data-state\": getState(itemContext.open),\n        \"data-disabled\": itemContext.disabled ? \"\" : void 0,\n        ...headerProps,\n        ref: forwardedRef\n    });\n});\nAccordionHeader.displayName = HEADER_NAME;\nvar TRIGGER_NAME = \"AccordionTrigger\";\nvar AccordionTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeAccordion, ...triggerProps } = props;\n    const accordionContext = useAccordionContext(ACCORDION_NAME, __scopeAccordion);\n    const itemContext = useAccordionItemContext(TRIGGER_NAME, __scopeAccordion);\n    const collapsibleContext = useAccordionCollapsibleContext(TRIGGER_NAME, __scopeAccordion);\n    const collapsibleScope = useCollapsibleScope(__scopeAccordion);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.ItemSlot, {\n        scope: __scopeAccordion,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_collapsible__WEBPACK_IMPORTED_MODULE_4__.Trigger, {\n            \"aria-disabled\": itemContext.open && !collapsibleContext.collapsible || void 0,\n            \"data-orientation\": accordionContext.orientation,\n            id: itemContext.triggerId,\n            ...collapsibleScope,\n            ...triggerProps,\n            ref: forwardedRef\n        })\n    });\n});\nAccordionTrigger.displayName = TRIGGER_NAME;\nvar CONTENT_NAME = \"AccordionContent\";\nvar AccordionContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeAccordion, ...contentProps } = props;\n    const accordionContext = useAccordionContext(ACCORDION_NAME, __scopeAccordion);\n    const itemContext = useAccordionItemContext(CONTENT_NAME, __scopeAccordion);\n    const collapsibleScope = useCollapsibleScope(__scopeAccordion);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_collapsible__WEBPACK_IMPORTED_MODULE_4__.Content, {\n        role: \"region\",\n        \"aria-labelledby\": itemContext.triggerId,\n        \"data-orientation\": accordionContext.orientation,\n        ...collapsibleScope,\n        ...contentProps,\n        ref: forwardedRef,\n        style: {\n            [\"--radix-accordion-content-height\"]: \"var(--radix-collapsible-content-height)\",\n            [\"--radix-accordion-content-width\"]: \"var(--radix-collapsible-content-width)\",\n            ...props.style\n        }\n    });\n});\nAccordionContent.displayName = CONTENT_NAME;\nfunction getState(open) {\n    return open ? \"open\" : \"closed\";\n}\nvar Root2 = Accordion;\nvar Item = AccordionItem;\nvar Header = AccordionHeader;\nvar Trigger2 = AccordionTrigger;\nvar Content2 = AccordionContent;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@radix-ui+react-accordion@1_51304fa0823e9b2a065feab26851e0ad/node_modules/@radix-ui/react-accordion/dist/index.mjs\n");

/***/ })

};
;