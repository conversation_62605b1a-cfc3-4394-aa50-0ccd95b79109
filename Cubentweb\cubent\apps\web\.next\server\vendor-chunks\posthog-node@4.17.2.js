"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/posthog-node@4.17.2";
exports.ids = ["vendor-chunks/posthog-node@4.17.2"];
exports.modules = {

/***/ "(rsc)/../../node_modules/.pnpm/posthog-node@4.17.2/node_modules/posthog-node/lib/node/index.mjs":
/*!*************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/posthog-node@4.17.2/node_modules/posthog-node/lib/node/index.mjs ***!
  \*************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PostHog: () => (/* binding */ PostHog),\n/* harmony export */   PostHogSentryIntegration: () => (/* binding */ PostHogSentryIntegration),\n/* harmony export */   createEventProcessor: () => (/* binding */ createEventProcessor),\n/* harmony export */   sentryIntegration: () => (/* binding */ sentryIntegration),\n/* harmony export */   setupExpressErrorHandler: () => (/* binding */ setupExpressErrorHandler)\n/* harmony export */ });\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var node_fs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! node:fs */ \"node:fs\");\n/* harmony import */ var node_readline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! node:readline */ \"node:readline\");\n\n\n\n\n/**\r\n * @file Adapted from [posthog-js](https://github.com/PostHog/posthog-js/blob/8157df935a4d0e71d2fefef7127aa85ee51c82d1/src/extensions/sentry-integration.ts) with modifications for the Node SDK.\r\n */\n/**\r\n * Integrate Sentry with PostHog. This will add a direct link to the person in Sentry, and an $exception event in PostHog.\r\n *\r\n * ### Usage\r\n *\r\n *     Sentry.init({\r\n *          dsn: 'https://example',\r\n *          integrations: [\r\n *              new PostHogSentryIntegration(posthog)\r\n *          ]\r\n *     })\r\n *\r\n *     Sentry.setTag(PostHogSentryIntegration.POSTHOG_ID_TAG, 'some distinct id');\r\n *\r\n * @param {Object} [posthog] The posthog object\r\n * @param {string} [organization] Optional: The Sentry organization, used to send a direct link from PostHog to Sentry\r\n * @param {Number} [projectId] Optional: The Sentry project id, used to send a direct link from PostHog to Sentry\r\n * @param {string} [prefix] Optional: Url of a self-hosted sentry instance (default: https://sentry.io/organizations/)\r\n * @param {SeverityLevel[] | '*'} [severityAllowList] Optional: send events matching the provided levels. Use '*' to send all events (default: ['error'])\r\n */\nconst NAME = 'posthog-node';\nfunction createEventProcessor(_posthog, {\n  organization,\n  projectId,\n  prefix,\n  severityAllowList = ['error']\n} = {}) {\n  return event => {\n    const shouldProcessLevel = severityAllowList === '*' || severityAllowList.includes(event.level);\n    if (!shouldProcessLevel) {\n      return event;\n    }\n    if (!event.tags) {\n      event.tags = {};\n    }\n    // Get the PostHog user ID from a specific tag, which users can set on their Sentry scope as they need.\n    const userId = event.tags[PostHogSentryIntegration.POSTHOG_ID_TAG];\n    if (userId === undefined) {\n      // If we can't find a user ID, don't bother linking the event. We won't be able to send anything meaningful to PostHog without it.\n      return event;\n    }\n    const uiHost = _posthog.options.host ?? 'https://us.i.posthog.com';\n    const personUrl = new URL(`/project/${_posthog.apiKey}/person/${userId}`, uiHost).toString();\n    event.tags['PostHog Person URL'] = personUrl;\n    const exceptions = event.exception?.values || [];\n    const exceptionList = exceptions.map(exception => ({\n      ...exception,\n      stacktrace: exception.stacktrace ? {\n        ...exception.stacktrace,\n        type: 'raw',\n        frames: (exception.stacktrace.frames || []).map(frame => {\n          return {\n            ...frame,\n            platform: 'node:javascript'\n          };\n        })\n      } : undefined\n    }));\n    const properties = {\n      // PostHog Exception Properties,\n      $exception_message: exceptions[0]?.value || event.message,\n      $exception_type: exceptions[0]?.type,\n      $exception_personURL: personUrl,\n      $exception_level: event.level,\n      $exception_list: exceptionList,\n      // Sentry Exception Properties\n      $sentry_event_id: event.event_id,\n      $sentry_exception: event.exception,\n      $sentry_exception_message: exceptions[0]?.value || event.message,\n      $sentry_exception_type: exceptions[0]?.type,\n      $sentry_tags: event.tags\n    };\n    if (organization && projectId) {\n      properties['$sentry_url'] = (prefix || 'https://sentry.io/organizations/') + organization + '/issues/?project=' + projectId + '&query=' + event.event_id;\n    }\n    _posthog.capture({\n      event: '$exception',\n      distinctId: userId,\n      properties\n    });\n    return event;\n  };\n}\n// V8 integration - function based\nfunction sentryIntegration(_posthog, options) {\n  const processor = createEventProcessor(_posthog, options);\n  return {\n    name: NAME,\n    processEvent(event) {\n      return processor(event);\n    }\n  };\n}\n// V7 integration - class based\nclass PostHogSentryIntegration {\n  constructor(_posthog, organization, prefix, severityAllowList) {\n    this.name = NAME;\n    // setupOnce gets called by Sentry when it intializes the plugin\n    this.name = NAME;\n    this.setupOnce = function (addGlobalEventProcessor, getCurrentHub) {\n      const projectId = getCurrentHub()?.getClient()?.getDsn()?.projectId;\n      addGlobalEventProcessor(createEventProcessor(_posthog, {\n        organization,\n        projectId,\n        prefix,\n        severityAllowList\n      }));\n    };\n  }\n}\nPostHogSentryIntegration.POSTHOG_ID_TAG = 'posthog_distinct_id';\n\n// vendor from: https://github.com/LiosK/uuidv7/blob/f30b7a7faff73afbce0b27a46c638310f96912ba/src/index.ts\r\n// https://github.com/LiosK/uuidv7#license\r\n/**\r\n * uuidv7: An experimental implementation of the proposed UUID Version 7\r\n *\r\n * @license Apache-2.0\r\n * @copyright 2021-2023 LiosK\r\n * @packageDocumentation\r\n */\r\nconst DIGITS = \"0123456789abcdef\";\r\n/** Represents a UUID as a 16-byte byte array. */\r\nclass UUID {\r\n    /** @param bytes - The 16-byte byte array representation. */\r\n    constructor(bytes) {\r\n        this.bytes = bytes;\r\n    }\r\n    /**\r\n     * Creates an object from the internal representation, a 16-byte byte array\r\n     * containing the binary UUID representation in the big-endian byte order.\r\n     *\r\n     * This method does NOT shallow-copy the argument, and thus the created object\r\n     * holds the reference to the underlying buffer.\r\n     *\r\n     * @throws TypeError if the length of the argument is not 16.\r\n     */\r\n    static ofInner(bytes) {\r\n        if (bytes.length !== 16) {\r\n            throw new TypeError(\"not 128-bit length\");\r\n        }\r\n        else {\r\n            return new UUID(bytes);\r\n        }\r\n    }\r\n    /**\r\n     * Builds a byte array from UUIDv7 field values.\r\n     *\r\n     * @param unixTsMs - A 48-bit `unix_ts_ms` field value.\r\n     * @param randA - A 12-bit `rand_a` field value.\r\n     * @param randBHi - The higher 30 bits of 62-bit `rand_b` field value.\r\n     * @param randBLo - The lower 32 bits of 62-bit `rand_b` field value.\r\n     * @throws RangeError if any field value is out of the specified range.\r\n     */\r\n    static fromFieldsV7(unixTsMs, randA, randBHi, randBLo) {\r\n        if (!Number.isInteger(unixTsMs) ||\r\n            !Number.isInteger(randA) ||\r\n            !Number.isInteger(randBHi) ||\r\n            !Number.isInteger(randBLo) ||\r\n            unixTsMs < 0 ||\r\n            randA < 0 ||\r\n            randBHi < 0 ||\r\n            randBLo < 0 ||\r\n            unixTsMs > 281474976710655 ||\r\n            randA > 0xfff ||\r\n            randBHi > 1073741823 ||\r\n            randBLo > 4294967295) {\r\n            throw new RangeError(\"invalid field value\");\r\n        }\r\n        const bytes = new Uint8Array(16);\r\n        bytes[0] = unixTsMs / 2 ** 40;\r\n        bytes[1] = unixTsMs / 2 ** 32;\r\n        bytes[2] = unixTsMs / 2 ** 24;\r\n        bytes[3] = unixTsMs / 2 ** 16;\r\n        bytes[4] = unixTsMs / 2 ** 8;\r\n        bytes[5] = unixTsMs;\r\n        bytes[6] = 0x70 | (randA >>> 8);\r\n        bytes[7] = randA;\r\n        bytes[8] = 0x80 | (randBHi >>> 24);\r\n        bytes[9] = randBHi >>> 16;\r\n        bytes[10] = randBHi >>> 8;\r\n        bytes[11] = randBHi;\r\n        bytes[12] = randBLo >>> 24;\r\n        bytes[13] = randBLo >>> 16;\r\n        bytes[14] = randBLo >>> 8;\r\n        bytes[15] = randBLo;\r\n        return new UUID(bytes);\r\n    }\r\n    /**\r\n     * Builds a byte array from a string representation.\r\n     *\r\n     * This method accepts the following formats:\r\n     *\r\n     * - 32-digit hexadecimal format without hyphens: `0189dcd553117d408db09496a2eef37b`\r\n     * - 8-4-4-4-12 hyphenated format: `0189dcd5-5311-7d40-8db0-9496a2eef37b`\r\n     * - Hyphenated format with surrounding braces: `{0189dcd5-5311-7d40-8db0-9496a2eef37b}`\r\n     * - RFC 4122 URN format: `urn:uuid:0189dcd5-5311-7d40-8db0-9496a2eef37b`\r\n     *\r\n     * Leading and trailing whitespaces represents an error.\r\n     *\r\n     * @throws SyntaxError if the argument could not parse as a valid UUID string.\r\n     */\r\n    static parse(uuid) {\r\n        let hex = undefined;\r\n        switch (uuid.length) {\r\n            case 32:\r\n                hex = /^[0-9a-f]{32}$/i.exec(uuid)?.[0];\r\n                break;\r\n            case 36:\r\n                hex =\r\n                    /^([0-9a-f]{8})-([0-9a-f]{4})-([0-9a-f]{4})-([0-9a-f]{4})-([0-9a-f]{12})$/i\r\n                        .exec(uuid)\r\n                        ?.slice(1, 6)\r\n                        .join(\"\");\r\n                break;\r\n            case 38:\r\n                hex =\r\n                    /^\\{([0-9a-f]{8})-([0-9a-f]{4})-([0-9a-f]{4})-([0-9a-f]{4})-([0-9a-f]{12})\\}$/i\r\n                        .exec(uuid)\r\n                        ?.slice(1, 6)\r\n                        .join(\"\");\r\n                break;\r\n            case 45:\r\n                hex =\r\n                    /^urn:uuid:([0-9a-f]{8})-([0-9a-f]{4})-([0-9a-f]{4})-([0-9a-f]{4})-([0-9a-f]{12})$/i\r\n                        .exec(uuid)\r\n                        ?.slice(1, 6)\r\n                        .join(\"\");\r\n                break;\r\n        }\r\n        if (hex) {\r\n            const inner = new Uint8Array(16);\r\n            for (let i = 0; i < 16; i += 4) {\r\n                const n = parseInt(hex.substring(2 * i, 2 * i + 8), 16);\r\n                inner[i + 0] = n >>> 24;\r\n                inner[i + 1] = n >>> 16;\r\n                inner[i + 2] = n >>> 8;\r\n                inner[i + 3] = n;\r\n            }\r\n            return new UUID(inner);\r\n        }\r\n        else {\r\n            throw new SyntaxError(\"could not parse UUID string\");\r\n        }\r\n    }\r\n    /**\r\n     * @returns The 8-4-4-4-12 canonical hexadecimal string representation\r\n     * (`0189dcd5-5311-7d40-8db0-9496a2eef37b`).\r\n     */\r\n    toString() {\r\n        let text = \"\";\r\n        for (let i = 0; i < this.bytes.length; i++) {\r\n            text += DIGITS.charAt(this.bytes[i] >>> 4);\r\n            text += DIGITS.charAt(this.bytes[i] & 0xf);\r\n            if (i === 3 || i === 5 || i === 7 || i === 9) {\r\n                text += \"-\";\r\n            }\r\n        }\r\n        return text;\r\n    }\r\n    /**\r\n     * @returns The 32-digit hexadecimal representation without hyphens\r\n     * (`0189dcd553117d408db09496a2eef37b`).\r\n     */\r\n    toHex() {\r\n        let text = \"\";\r\n        for (let i = 0; i < this.bytes.length; i++) {\r\n            text += DIGITS.charAt(this.bytes[i] >>> 4);\r\n            text += DIGITS.charAt(this.bytes[i] & 0xf);\r\n        }\r\n        return text;\r\n    }\r\n    /** @returns The 8-4-4-4-12 canonical hexadecimal string representation. */\r\n    toJSON() {\r\n        return this.toString();\r\n    }\r\n    /**\r\n     * Reports the variant field value of the UUID or, if appropriate, \"NIL\" or\r\n     * \"MAX\".\r\n     *\r\n     * For convenience, this method reports \"NIL\" or \"MAX\" if `this` represents\r\n     * the Nil or Max UUID, although the Nil and Max UUIDs are technically\r\n     * subsumed under the variants `0b0` and `0b111`, respectively.\r\n     */\r\n    getVariant() {\r\n        const n = this.bytes[8] >>> 4;\r\n        if (n < 0) {\r\n            throw new Error(\"unreachable\");\r\n        }\r\n        else if (n <= 0b0111) {\r\n            return this.bytes.every((e) => e === 0) ? \"NIL\" : \"VAR_0\";\r\n        }\r\n        else if (n <= 0b1011) {\r\n            return \"VAR_10\";\r\n        }\r\n        else if (n <= 0b1101) {\r\n            return \"VAR_110\";\r\n        }\r\n        else if (n <= 0b1111) {\r\n            return this.bytes.every((e) => e === 0xff) ? \"MAX\" : \"VAR_RESERVED\";\r\n        }\r\n        else {\r\n            throw new Error(\"unreachable\");\r\n        }\r\n    }\r\n    /**\r\n     * Returns the version field value of the UUID or `undefined` if the UUID does\r\n     * not have the variant field value of `0b10`.\r\n     */\r\n    getVersion() {\r\n        return this.getVariant() === \"VAR_10\" ? this.bytes[6] >>> 4 : undefined;\r\n    }\r\n    /** Creates an object from `this`. */\r\n    clone() {\r\n        return new UUID(this.bytes.slice(0));\r\n    }\r\n    /** Returns true if `this` is equivalent to `other`. */\r\n    equals(other) {\r\n        return this.compareTo(other) === 0;\r\n    }\r\n    /**\r\n     * Returns a negative integer, zero, or positive integer if `this` is less\r\n     * than, equal to, or greater than `other`, respectively.\r\n     */\r\n    compareTo(other) {\r\n        for (let i = 0; i < 16; i++) {\r\n            const diff = this.bytes[i] - other.bytes[i];\r\n            if (diff !== 0) {\r\n                return Math.sign(diff);\r\n            }\r\n        }\r\n        return 0;\r\n    }\r\n}\r\n/**\r\n * Encapsulates the monotonic counter state.\r\n *\r\n * This class provides APIs to utilize a separate counter state from that of the\r\n * global generator used by {@link uuidv7} and {@link uuidv7obj}. In addition to\r\n * the default {@link generate} method, this class has {@link generateOrAbort}\r\n * that is useful to absolutely guarantee the monotonically increasing order of\r\n * generated UUIDs. See their respective documentation for details.\r\n */\r\nclass V7Generator {\r\n    /**\r\n     * Creates a generator object with the default random number generator, or\r\n     * with the specified one if passed as an argument. The specified random\r\n     * number generator should be cryptographically strong and securely seeded.\r\n     */\r\n    constructor(randomNumberGenerator) {\r\n        this.timestamp = 0;\r\n        this.counter = 0;\r\n        this.random = randomNumberGenerator ?? getDefaultRandom();\r\n    }\r\n    /**\r\n     * Generates a new UUIDv7 object from the current timestamp, or resets the\r\n     * generator upon significant timestamp rollback.\r\n     *\r\n     * This method returns a monotonically increasing UUID by reusing the previous\r\n     * timestamp even if the up-to-date timestamp is smaller than the immediately\r\n     * preceding UUID's. However, when such a clock rollback is considered\r\n     * significant (i.e., by more than ten seconds), this method resets the\r\n     * generator and returns a new UUID based on the given timestamp, breaking the\r\n     * increasing order of UUIDs.\r\n     *\r\n     * See {@link generateOrAbort} for the other mode of generation and\r\n     * {@link generateOrResetCore} for the low-level primitive.\r\n     */\r\n    generate() {\r\n        return this.generateOrResetCore(Date.now(), 10000);\r\n    }\r\n    /**\r\n     * Generates a new UUIDv7 object from the current timestamp, or returns\r\n     * `undefined` upon significant timestamp rollback.\r\n     *\r\n     * This method returns a monotonically increasing UUID by reusing the previous\r\n     * timestamp even if the up-to-date timestamp is smaller than the immediately\r\n     * preceding UUID's. However, when such a clock rollback is considered\r\n     * significant (i.e., by more than ten seconds), this method aborts and\r\n     * returns `undefined` immediately.\r\n     *\r\n     * See {@link generate} for the other mode of generation and\r\n     * {@link generateOrAbortCore} for the low-level primitive.\r\n     */\r\n    generateOrAbort() {\r\n        return this.generateOrAbortCore(Date.now(), 10000);\r\n    }\r\n    /**\r\n     * Generates a new UUIDv7 object from the `unixTsMs` passed, or resets the\r\n     * generator upon significant timestamp rollback.\r\n     *\r\n     * This method is equivalent to {@link generate} except that it takes a custom\r\n     * timestamp and clock rollback allowance.\r\n     *\r\n     * @param rollbackAllowance - The amount of `unixTsMs` rollback that is\r\n     * considered significant. A suggested value is `10_000` (milliseconds).\r\n     * @throws RangeError if `unixTsMs` is not a 48-bit positive integer.\r\n     */\r\n    generateOrResetCore(unixTsMs, rollbackAllowance) {\r\n        let value = this.generateOrAbortCore(unixTsMs, rollbackAllowance);\r\n        if (value === undefined) {\r\n            // reset state and resume\r\n            this.timestamp = 0;\r\n            value = this.generateOrAbortCore(unixTsMs, rollbackAllowance);\r\n        }\r\n        return value;\r\n    }\r\n    /**\r\n     * Generates a new UUIDv7 object from the `unixTsMs` passed, or returns\r\n     * `undefined` upon significant timestamp rollback.\r\n     *\r\n     * This method is equivalent to {@link generateOrAbort} except that it takes a\r\n     * custom timestamp and clock rollback allowance.\r\n     *\r\n     * @param rollbackAllowance - The amount of `unixTsMs` rollback that is\r\n     * considered significant. A suggested value is `10_000` (milliseconds).\r\n     * @throws RangeError if `unixTsMs` is not a 48-bit positive integer.\r\n     */\r\n    generateOrAbortCore(unixTsMs, rollbackAllowance) {\r\n        const MAX_COUNTER = 4398046511103;\r\n        if (!Number.isInteger(unixTsMs) ||\r\n            unixTsMs < 1 ||\r\n            unixTsMs > 281474976710655) {\r\n            throw new RangeError(\"`unixTsMs` must be a 48-bit positive integer\");\r\n        }\r\n        else if (rollbackAllowance < 0 || rollbackAllowance > 281474976710655) {\r\n            throw new RangeError(\"`rollbackAllowance` out of reasonable range\");\r\n        }\r\n        if (unixTsMs > this.timestamp) {\r\n            this.timestamp = unixTsMs;\r\n            this.resetCounter();\r\n        }\r\n        else if (unixTsMs + rollbackAllowance >= this.timestamp) {\r\n            // go on with previous timestamp if new one is not much smaller\r\n            this.counter++;\r\n            if (this.counter > MAX_COUNTER) {\r\n                // increment timestamp at counter overflow\r\n                this.timestamp++;\r\n                this.resetCounter();\r\n            }\r\n        }\r\n        else {\r\n            // abort if clock went backwards to unbearable extent\r\n            return undefined;\r\n        }\r\n        return UUID.fromFieldsV7(this.timestamp, Math.trunc(this.counter / 2 ** 30), this.counter & (2 ** 30 - 1), this.random.nextUint32());\r\n    }\r\n    /** Initializes the counter at a 42-bit random integer. */\r\n    resetCounter() {\r\n        this.counter =\r\n            this.random.nextUint32() * 0x400 + (this.random.nextUint32() & 0x3ff);\r\n    }\r\n    /**\r\n     * Generates a new UUIDv4 object utilizing the random number generator inside.\r\n     *\r\n     * @internal\r\n     */\r\n    generateV4() {\r\n        const bytes = new Uint8Array(Uint32Array.of(this.random.nextUint32(), this.random.nextUint32(), this.random.nextUint32(), this.random.nextUint32()).buffer);\r\n        bytes[6] = 0x40 | (bytes[6] >>> 4);\r\n        bytes[8] = 0x80 | (bytes[8] >>> 2);\r\n        return UUID.ofInner(bytes);\r\n    }\r\n}\r\n/** A global flag to force use of cryptographically strong RNG. */\r\n// declare const UUIDV7_DENY_WEAK_RNG: boolean;\r\n/** Returns the default random number generator available in the environment. */\r\nconst getDefaultRandom = () => {\r\n    // fix: crypto isn't available in react-native, always use Math.random\r\n    //   // detect Web Crypto API\r\n    //   if (\r\n    //     typeof crypto !== \"undefined\" &&\r\n    //     typeof crypto.getRandomValues !== \"undefined\"\r\n    //   ) {\r\n    //     return new BufferedCryptoRandom();\r\n    //   } else {\r\n    //     // fall back on Math.random() unless the flag is set to true\r\n    //     if (typeof UUIDV7_DENY_WEAK_RNG !== \"undefined\" && UUIDV7_DENY_WEAK_RNG) {\r\n    //       throw new Error(\"no cryptographically strong RNG available\");\r\n    //     }\r\n    //     return {\r\n    //       nextUint32: (): number =>\r\n    //         Math.trunc(Math.random() * 0x1_0000) * 0x1_0000 +\r\n    //         Math.trunc(Math.random() * 0x1_0000),\r\n    //     };\r\n    //   }\r\n    return {\r\n        nextUint32: () => Math.trunc(Math.random() * 65536) * 65536 +\r\n            Math.trunc(Math.random() * 65536),\r\n    };\r\n};\r\n// /**\r\n//  * Wraps `crypto.getRandomValues()` to enable buffering; this uses a small\r\n//  * buffer by default to avoid both unbearable throughput decline in some\r\n//  * environments and the waste of time and space for unused values.\r\n//  */\r\n// class BufferedCryptoRandom {\r\n//   private readonly buffer = new Uint32Array(8);\r\n//   private cursor = 0xffff;\r\n//   nextUint32(): number {\r\n//     if (this.cursor >= this.buffer.length) {\r\n//       crypto.getRandomValues(this.buffer);\r\n//       this.cursor = 0;\r\n//     }\r\n//     return this.buffer[this.cursor++];\r\n//   }\r\n// }\r\nlet defaultGenerator;\r\n/**\r\n * Generates a UUIDv7 string.\r\n *\r\n * @returns The 8-4-4-4-12 canonical hexadecimal string representation\r\n * (\"xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx\").\r\n */\r\nconst uuidv7 = () => uuidv7obj().toString();\r\n/** Generates a UUIDv7 object. */\r\nconst uuidv7obj = () => (defaultGenerator || (defaultGenerator = new V7Generator())).generate();\n\n// Portions of this file are derived from getsentry/sentry-javascript by Software, Inc. dba Sentry\n// Licensed under the MIT License\nfunction makeUncaughtExceptionHandler(captureFn, onFatalFn) {\n  let calledFatalError = false;\n  return Object.assign(error => {\n    // Attaching a listener to `uncaughtException` will prevent the node process from exiting. We generally do not\n    // want to alter this behaviour so we check for other listeners that users may have attached themselves and adjust\n    // exit behaviour of the SDK accordingly:\n    // - If other listeners are attached, do not exit.\n    // - If the only listener attached is ours, exit.\n    const userProvidedListenersCount = global.process.listeners('uncaughtException').filter(listener => {\n      // There are 2 listeners we ignore:\n      return (\n        // as soon as we're using domains this listener is attached by node itself\n        listener.name !== 'domainUncaughtExceptionClear' &&\n        // the handler we register in this integration\n        listener._posthogErrorHandler !== true\n      );\n    }).length;\n    const processWouldExit = userProvidedListenersCount === 0;\n    captureFn(error, {\n      mechanism: {\n        type: 'onuncaughtexception',\n        handled: false\n      }\n    });\n    if (!calledFatalError && processWouldExit) {\n      calledFatalError = true;\n      onFatalFn();\n    }\n  }, {\n    _posthogErrorHandler: true\n  });\n}\nfunction addUncaughtExceptionListener(captureFn, onFatalFn) {\n  global.process.on('uncaughtException', makeUncaughtExceptionHandler(captureFn, onFatalFn));\n}\nfunction addUnhandledRejectionListener(captureFn) {\n  global.process.on('unhandledRejection', reason => {\n    captureFn(reason, {\n      mechanism: {\n        type: 'onunhandledrejection',\n        handled: false\n      }\n    });\n  });\n}\n\n// Portions of this file are derived from getsentry/sentry-javascript by Software, Inc. dba Sentry\n// Licensed under the MIT License\nfunction isEvent(candidate) {\n  return typeof Event !== 'undefined' && isInstanceOf(candidate, Event);\n}\nfunction isPlainObject(candidate) {\n  return isBuiltin(candidate, 'Object');\n}\nfunction isError(candidate) {\n  switch (Object.prototype.toString.call(candidate)) {\n    case '[object Error]':\n    case '[object Exception]':\n    case '[object DOMException]':\n    case '[object WebAssembly.Exception]':\n      return true;\n    default:\n      return isInstanceOf(candidate, Error);\n  }\n}\nfunction isInstanceOf(candidate, base) {\n  try {\n    return candidate instanceof base;\n  } catch {\n    return false;\n  }\n}\nfunction isErrorEvent(event) {\n  return isBuiltin(event, 'ErrorEvent');\n}\nfunction isBuiltin(candidate, className) {\n  return Object.prototype.toString.call(candidate) === `[object ${className}]`;\n}\n\n// Portions of this file are derived from getsentry/sentry-javascript by Software, Inc. dba Sentry\nasync function propertiesFromUnknownInput(stackParser, frameModifiers, input, hint) {\n  const providedMechanism = hint && hint.mechanism;\n  const mechanism = providedMechanism || {\n    handled: true,\n    type: 'generic'\n  };\n  const errorList = getErrorList(mechanism, input, hint);\n  const exceptionList = await Promise.all(errorList.map(async error => {\n    const exception = await exceptionFromError(stackParser, frameModifiers, error);\n    exception.value = exception.value || '';\n    exception.type = exception.type || 'Error';\n    exception.mechanism = mechanism;\n    return exception;\n  }));\n  const properties = {\n    $exception_list: exceptionList\n  };\n  return properties;\n}\n// Flatten error causes into a list of errors\n// See: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error/cause\nfunction getErrorList(mechanism, input, hint) {\n  const error = getError(mechanism, input, hint);\n  if (error.cause) {\n    return [error, ...getErrorList(mechanism, error.cause, hint)];\n  }\n  return [error];\n}\nfunction getError(mechanism, exception, hint) {\n  if (isError(exception)) {\n    return exception;\n  }\n  mechanism.synthetic = true;\n  if (isPlainObject(exception)) {\n    const errorFromProp = getErrorPropertyFromObject(exception);\n    if (errorFromProp) {\n      return errorFromProp;\n    }\n    const message = getMessageForObject(exception);\n    const ex = hint?.syntheticException || new Error(message);\n    ex.message = message;\n    return ex;\n  }\n  // This handles when someone does: `throw \"something awesome\";`\n  // We use synthesized Error here so we can extract a (rough) stack trace.\n  const ex = hint?.syntheticException || new Error(exception);\n  ex.message = `${exception}`;\n  return ex;\n}\n/** If a plain object has a property that is an `Error`, return this error. */\nfunction getErrorPropertyFromObject(obj) {\n  for (const prop in obj) {\n    if (Object.prototype.hasOwnProperty.call(obj, prop)) {\n      const value = obj[prop];\n      if (isError(value)) {\n        return value;\n      }\n    }\n  }\n  return undefined;\n}\nfunction getMessageForObject(exception) {\n  if ('name' in exception && typeof exception.name === 'string') {\n    let message = `'${exception.name}' captured as exception`;\n    if ('message' in exception && typeof exception.message === 'string') {\n      message += ` with message '${exception.message}'`;\n    }\n    return message;\n  } else if ('message' in exception && typeof exception.message === 'string') {\n    return exception.message;\n  }\n  const keys = extractExceptionKeysForMessage(exception);\n  // Some ErrorEvent instances do not have an `error` property, which is why they are not handled before\n  // We still want to try to get a decent message for these cases\n  if (isErrorEvent(exception)) {\n    return `Event \\`ErrorEvent\\` captured as exception with message \\`${exception.message}\\``;\n  }\n  const className = getObjectClassName(exception);\n  return `${className && className !== 'Object' ? `'${className}'` : 'Object'} captured as exception with keys: ${keys}`;\n}\nfunction getObjectClassName(obj) {\n  try {\n    const prototype = Object.getPrototypeOf(obj);\n    return prototype ? prototype.constructor.name : undefined;\n  } catch (e) {\n    // ignore errors here\n  }\n}\n/**\r\n * Given any captured exception, extract its keys and create a sorted\r\n * and truncated list that will be used inside the event message.\r\n * eg. `Non-error exception captured with keys: foo, bar, baz`\r\n */\nfunction extractExceptionKeysForMessage(exception, maxLength = 40) {\n  const keys = Object.keys(convertToPlainObject(exception));\n  keys.sort();\n  const firstKey = keys[0];\n  if (!firstKey) {\n    return '[object has no keys]';\n  }\n  if (firstKey.length >= maxLength) {\n    return truncate(firstKey, maxLength);\n  }\n  for (let includedKeys = keys.length; includedKeys > 0; includedKeys--) {\n    const serialized = keys.slice(0, includedKeys).join(', ');\n    if (serialized.length > maxLength) {\n      continue;\n    }\n    if (includedKeys === keys.length) {\n      return serialized;\n    }\n    return truncate(serialized, maxLength);\n  }\n  return '';\n}\nfunction truncate(str, max = 0) {\n  if (typeof str !== 'string' || max === 0) {\n    return str;\n  }\n  return str.length <= max ? str : `${str.slice(0, max)}...`;\n}\n/**\r\n * Transforms any `Error` or `Event` into a plain object with all of their enumerable properties, and some of their\r\n * non-enumerable properties attached.\r\n *\r\n * @param value Initial source that we have to transform in order for it to be usable by the serializer\r\n * @returns An Event or Error turned into an object - or the value argument itself, when value is neither an Event nor\r\n *  an Error.\r\n */\nfunction convertToPlainObject(value) {\n  if (isError(value)) {\n    return {\n      message: value.message,\n      name: value.name,\n      stack: value.stack,\n      ...getOwnProperties(value)\n    };\n  } else if (isEvent(value)) {\n    const newObj = {\n      type: value.type,\n      target: serializeEventTarget(value.target),\n      currentTarget: serializeEventTarget(value.currentTarget),\n      ...getOwnProperties(value)\n    };\n    // TODO: figure out why this fails typing (I think CustomEvent is only supported in Node 19 onwards)\n    // if (typeof CustomEvent !== 'undefined' && isInstanceOf(value, CustomEvent)) {\n    //   newObj.detail = (value as unknown as CustomEvent).detail\n    // }\n    return newObj;\n  } else {\n    return value;\n  }\n}\n/** Filters out all but an object's own properties */\nfunction getOwnProperties(obj) {\n  if (typeof obj === 'object' && obj !== null) {\n    const extractedProps = {};\n    for (const property in obj) {\n      if (Object.prototype.hasOwnProperty.call(obj, property)) {\n        extractedProps[property] = obj[property];\n      }\n    }\n    return extractedProps;\n  } else {\n    return {};\n  }\n}\n/** Creates a string representation of the target of an `Event` object */\nfunction serializeEventTarget(target) {\n  try {\n    return Object.prototype.toString.call(target);\n  } catch (_oO) {\n    return '<unknown>';\n  }\n}\n/**\r\n * Extracts stack frames from the error and builds an Exception\r\n */\nasync function exceptionFromError(stackParser, frameModifiers, error) {\n  const exception = {\n    type: error.name || error.constructor.name,\n    value: error.message\n  };\n  let frames = parseStackFrames(stackParser, error);\n  for (const modifier of frameModifiers) {\n    frames = await modifier(frames);\n  }\n  if (frames.length) {\n    exception.stacktrace = {\n      frames,\n      type: 'raw'\n    };\n  }\n  return exception;\n}\n/**\r\n * Extracts stack frames from the error.stack string\r\n */\nfunction parseStackFrames(stackParser, error) {\n  return stackParser(error.stack || '', 1);\n}\n\nconst SHUTDOWN_TIMEOUT = 2000;\nclass ErrorTracking {\n  static async captureException(client, error, hint, distinctId, additionalProperties) {\n    const properties = {\n      ...additionalProperties\n    };\n    // Given stateless nature of Node SDK we capture exceptions using personless processing when no\n    // user can be determined because a distinct_id is not provided e.g. exception autocapture\n    if (!distinctId) {\n      properties.$process_person_profile = false;\n    }\n    const exceptionProperties = await propertiesFromUnknownInput(this.stackParser, this.frameModifiers, error, hint);\n    client.capture({\n      event: '$exception',\n      distinctId: distinctId || uuidv7(),\n      properties: {\n        ...exceptionProperties,\n        ...properties\n      }\n    });\n  }\n  constructor(client, options) {\n    this.client = client;\n    this._exceptionAutocaptureEnabled = options.enableExceptionAutocapture || false;\n    this.startAutocaptureIfEnabled();\n  }\n  startAutocaptureIfEnabled() {\n    if (this.isEnabled()) {\n      addUncaughtExceptionListener(this.onException.bind(this), this.onFatalError.bind(this));\n      addUnhandledRejectionListener(this.onException.bind(this));\n    }\n  }\n  onException(exception, hint) {\n    ErrorTracking.captureException(this.client, exception, hint);\n  }\n  async onFatalError() {\n    await this.client.shutdown(SHUTDOWN_TIMEOUT);\n  }\n  isEnabled() {\n    return !this.client.isDisabled && this._exceptionAutocaptureEnabled;\n  }\n}\n\nfunction setupExpressErrorHandler(_posthog, app) {\n  app.use((error, _, __, next) => {\n    const hint = {\n      mechanism: {\n        type: 'middleware',\n        handled: false\n      }\n    };\n    // Given stateless nature of Node SDK we capture exceptions using personless processing\n    // when no user can be determined e.g. in the case of exception autocapture\n    ErrorTracking.captureException(_posthog, error, hint, uuidv7(), {\n      $process_person_profile: false\n    });\n    next(error);\n  });\n}\n\n// Portions of this file are derived from getsentry/sentry-javascript by Software, Inc. dba Sentry\n/** Creates a function that gets the module name from a filename */\nfunction createGetModuleFromFilename(basePath = process.argv[1] ? (0,path__WEBPACK_IMPORTED_MODULE_0__.dirname)(process.argv[1]) : process.cwd(), isWindows = path__WEBPACK_IMPORTED_MODULE_0__.sep === '\\\\') {\n  const normalizedBase = isWindows ? normalizeWindowsPath(basePath) : basePath;\n  return filename => {\n    if (!filename) {\n      return;\n    }\n    const normalizedFilename = isWindows ? normalizeWindowsPath(filename) : filename;\n    // eslint-disable-next-line prefer-const\n    let {\n      dir,\n      base: file,\n      ext\n    } = path__WEBPACK_IMPORTED_MODULE_0__.posix.parse(normalizedFilename);\n    if (ext === '.js' || ext === '.mjs' || ext === '.cjs') {\n      file = file.slice(0, ext.length * -1);\n    }\n    // The file name might be URI-encoded which we want to decode to\n    // the original file name.\n    const decodedFile = decodeURIComponent(file);\n    if (!dir) {\n      // No dirname whatsoever\n      dir = '.';\n    }\n    const n = dir.lastIndexOf('/node_modules');\n    if (n > -1) {\n      return `${dir.slice(n + 14).replace(/\\//g, '.')}:${decodedFile}`;\n    }\n    // Let's see if it's a part of the main module\n    // To be a part of main module, it has to share the same base\n    if (dir.startsWith(normalizedBase)) {\n      const moduleName = dir.slice(normalizedBase.length + 1).replace(/\\//g, '.');\n      return moduleName ? `${moduleName}:${decodedFile}` : decodedFile;\n    }\n    return decodedFile;\n  };\n}\n/** normalizes Windows paths */\nfunction normalizeWindowsPath(path) {\n  return path.replace(/^[A-Z]:/, '') // remove Windows-style prefix\n  .replace(/\\\\/g, '/'); // replace all `\\` instances with `/`\n}\n\n// Portions of this file are derived from getsentry/sentry-javascript by Software, Inc. dba Sentry\n// Licensed under the MIT License\n/** A simple Least Recently Used map */\nclass ReduceableCache {\n  constructor(_maxSize) {\n    this._maxSize = _maxSize;\n    this._cache = new Map();\n  }\n  /** Get an entry or undefined if it was not in the cache. Re-inserts to update the recently used order */\n  get(key) {\n    const value = this._cache.get(key);\n    if (value === undefined) {\n      return undefined;\n    }\n    // Remove and re-insert to update the order\n    this._cache.delete(key);\n    this._cache.set(key, value);\n    return value;\n  }\n  /** Insert an entry and evict an older entry if we've reached maxSize */\n  set(key, value) {\n    this._cache.set(key, value);\n  }\n  /** Remove an entry and return the entry if it was in the cache */\n  reduce() {\n    while (this._cache.size >= this._maxSize) {\n      const value = this._cache.keys().next().value;\n      if (value) {\n        // keys() returns an iterator in insertion order so keys().next() gives us the oldest key\n        this._cache.delete(value);\n      }\n    }\n  }\n}\n\n// Portions of this file are derived from getsentry/sentry-javascript by Software, Inc. dba Sentry\nconst LRU_FILE_CONTENTS_CACHE = new ReduceableCache(25);\nconst LRU_FILE_CONTENTS_FS_READ_FAILED = new ReduceableCache(20);\nconst DEFAULT_LINES_OF_CONTEXT = 7;\n// Determines the upper bound of lineno/colno that we will attempt to read. Large colno values are likely to be\n// minified code while large lineno values are likely to be bundled code.\n// Exported for testing purposes.\nconst MAX_CONTEXTLINES_COLNO = 1000;\nconst MAX_CONTEXTLINES_LINENO = 10000;\nasync function addSourceContext(frames) {\n  // keep a lookup map of which files we've already enqueued to read,\n  // so we don't enqueue the same file multiple times which would cause multiple i/o reads\n  const filesToLines = {};\n  // Maps preserve insertion order, so we iterate in reverse, starting at the\n  // outermost frame and closer to where the exception has occurred (poor mans priority)\n  for (let i = frames.length - 1; i >= 0; i--) {\n    const frame = frames[i];\n    const filename = frame?.filename;\n    if (!frame || typeof filename !== 'string' || typeof frame.lineno !== 'number' || shouldSkipContextLinesForFile(filename) || shouldSkipContextLinesForFrame(frame)) {\n      continue;\n    }\n    const filesToLinesOutput = filesToLines[filename];\n    if (!filesToLinesOutput) {\n      filesToLines[filename] = [];\n    }\n    filesToLines[filename].push(frame.lineno);\n  }\n  const files = Object.keys(filesToLines);\n  if (files.length == 0) {\n    return frames;\n  }\n  const readlinePromises = [];\n  for (const file of files) {\n    // If we failed to read this before, dont try reading it again.\n    if (LRU_FILE_CONTENTS_FS_READ_FAILED.get(file)) {\n      continue;\n    }\n    const filesToLineRanges = filesToLines[file];\n    if (!filesToLineRanges) {\n      continue;\n    }\n    // Sort ranges so that they are sorted by line increasing order and match how the file is read.\n    filesToLineRanges.sort((a, b) => a - b);\n    // Check if the contents are already in the cache and if we can avoid reading the file again.\n    const ranges = makeLineReaderRanges(filesToLineRanges);\n    if (ranges.every(r => rangeExistsInContentCache(file, r))) {\n      continue;\n    }\n    const cache = emplace(LRU_FILE_CONTENTS_CACHE, file, {});\n    readlinePromises.push(getContextLinesFromFile(file, ranges, cache));\n  }\n  // The promise rejections are caught in order to prevent them from short circuiting Promise.all\n  await Promise.all(readlinePromises).catch(() => {});\n  // Perform the same loop as above, but this time we can assume all files are in the cache\n  // and attempt to add source context to frames.\n  if (frames && frames.length > 0) {\n    addSourceContextToFrames(frames, LRU_FILE_CONTENTS_CACHE);\n  }\n  // Once we're finished processing an exception reduce the files held in the cache\n  // so that we don't indefinetly increase the size of this map\n  LRU_FILE_CONTENTS_CACHE.reduce();\n  return frames;\n}\n/**\r\n * Extracts lines from a file and stores them in a cache.\r\n */\nfunction getContextLinesFromFile(path, ranges, output) {\n  return new Promise(resolve => {\n    // It is important *not* to have any async code between createInterface and the 'line' event listener\n    // as it will cause the 'line' event to\n    // be emitted before the listener is attached.\n    const stream = (0,node_fs__WEBPACK_IMPORTED_MODULE_1__.createReadStream)(path);\n    const lineReaded = (0,node_readline__WEBPACK_IMPORTED_MODULE_2__.createInterface)({\n      input: stream\n    });\n    // We need to explicitly destroy the stream to prevent memory leaks,\n    // removing the listeners on the readline interface is not enough.\n    // See: https://github.com/nodejs/node/issues/9002 and https://github.com/getsentry/sentry-javascript/issues/14892\n    function destroyStreamAndResolve() {\n      stream.destroy();\n      resolve();\n    }\n    // Init at zero and increment at the start of the loop because lines are 1 indexed.\n    let lineNumber = 0;\n    let currentRangeIndex = 0;\n    const range = ranges[currentRangeIndex];\n    if (range === undefined) {\n      // We should never reach this point, but if we do, we should resolve the promise to prevent it from hanging.\n      destroyStreamAndResolve();\n      return;\n    }\n    let rangeStart = range[0];\n    let rangeEnd = range[1];\n    // We use this inside Promise.all, so we need to resolve the promise even if there is an error\n    // to prevent Promise.all from short circuiting the rest.\n    function onStreamError() {\n      // Mark file path as failed to read and prevent multiple read attempts.\n      LRU_FILE_CONTENTS_FS_READ_FAILED.set(path, 1);\n      lineReaded.close();\n      lineReaded.removeAllListeners();\n      destroyStreamAndResolve();\n    }\n    // We need to handle the error event to prevent the process from crashing in < Node 16\n    // https://github.com/nodejs/node/pull/31603\n    stream.on('error', onStreamError);\n    lineReaded.on('error', onStreamError);\n    lineReaded.on('close', destroyStreamAndResolve);\n    lineReaded.on('line', line => {\n      lineNumber++;\n      if (lineNumber < rangeStart) {\n        return;\n      }\n      // !Warning: This mutates the cache by storing the snipped line into the cache.\n      output[lineNumber] = snipLine(line, 0);\n      if (lineNumber >= rangeEnd) {\n        if (currentRangeIndex === ranges.length - 1) {\n          // We need to close the file stream and remove listeners, else the reader will continue to run our listener;\n          lineReaded.close();\n          lineReaded.removeAllListeners();\n          return;\n        }\n        currentRangeIndex++;\n        const range = ranges[currentRangeIndex];\n        if (range === undefined) {\n          // This should never happen as it means we have a bug in the context.\n          lineReaded.close();\n          lineReaded.removeAllListeners();\n          return;\n        }\n        rangeStart = range[0];\n        rangeEnd = range[1];\n      }\n    });\n  });\n}\n/** Adds context lines to frames */\nfunction addSourceContextToFrames(frames, cache) {\n  for (const frame of frames) {\n    // Only add context if we have a filename and it hasn't already been added\n    if (frame.filename && frame.context_line === undefined && typeof frame.lineno === 'number') {\n      const contents = cache.get(frame.filename);\n      if (contents === undefined) {\n        continue;\n      }\n      addContextToFrame(frame.lineno, frame, contents);\n    }\n  }\n}\n/**\r\n * Resolves context lines before and after the given line number and appends them to the frame;\r\n */\nfunction addContextToFrame(lineno, frame, contents) {\n  // When there is no line number in the frame, attaching context is nonsensical and will even break grouping.\n  // We already check for lineno before calling this, but since StackFrame lineno is optional, we check it again.\n  if (frame.lineno === undefined || contents === undefined) {\n    return;\n  }\n  frame.pre_context = [];\n  for (let i = makeRangeStart(lineno); i < lineno; i++) {\n    // We always expect the start context as line numbers cannot be negative. If we dont find a line, then\n    // something went wrong somewhere. Clear the context and return without adding any linecontext.\n    const line = contents[i];\n    if (line === undefined) {\n      clearLineContext(frame);\n      return;\n    }\n    frame.pre_context.push(line);\n  }\n  // We should always have the context line. If we dont, something went wrong, so we clear the context and return\n  // without adding any linecontext.\n  if (contents[lineno] === undefined) {\n    clearLineContext(frame);\n    return;\n  }\n  frame.context_line = contents[lineno];\n  const end = makeRangeEnd(lineno);\n  frame.post_context = [];\n  for (let i = lineno + 1; i <= end; i++) {\n    // Since we dont track when the file ends, we cant clear the context if we dont find a line as it could\n    // just be that we reached the end of the file.\n    const line = contents[i];\n    if (line === undefined) {\n      break;\n    }\n    frame.post_context.push(line);\n  }\n}\n/**\r\n * Clears the context lines from a frame, used to reset a frame to its original state\r\n * if we fail to resolve all context lines for it.\r\n */\nfunction clearLineContext(frame) {\n  delete frame.pre_context;\n  delete frame.context_line;\n  delete frame.post_context;\n}\n/**\r\n * Determines if context lines should be skipped for a file.\r\n * - .min.(mjs|cjs|js) files are and not useful since they dont point to the original source\r\n * - node: prefixed modules are part of the runtime and cannot be resolved to a file\r\n * - data: skip json, wasm and inline js https://nodejs.org/api/esm.html#data-imports\r\n */\nfunction shouldSkipContextLinesForFile(path) {\n  // Test the most common prefix and extension first. These are the ones we\n  // are most likely to see in user applications and are the ones we can break out of first.\n  return path.startsWith('node:') || path.endsWith('.min.js') || path.endsWith('.min.cjs') || path.endsWith('.min.mjs') || path.startsWith('data:');\n}\n/**\r\n * Determines if we should skip contextlines based off the max lineno and colno values.\r\n */\nfunction shouldSkipContextLinesForFrame(frame) {\n  if (frame.lineno !== undefined && frame.lineno > MAX_CONTEXTLINES_LINENO) {\n    return true;\n  }\n  if (frame.colno !== undefined && frame.colno > MAX_CONTEXTLINES_COLNO) {\n    return true;\n  }\n  return false;\n}\n/**\r\n * Checks if we have all the contents that we need in the cache.\r\n */\nfunction rangeExistsInContentCache(file, range) {\n  const contents = LRU_FILE_CONTENTS_CACHE.get(file);\n  if (contents === undefined) {\n    return false;\n  }\n  for (let i = range[0]; i <= range[1]; i++) {\n    if (contents[i] === undefined) {\n      return false;\n    }\n  }\n  return true;\n}\n/**\r\n * Creates contiguous ranges of lines to read from a file. In the case where context lines overlap,\r\n * the ranges are merged to create a single range.\r\n */\nfunction makeLineReaderRanges(lines) {\n  if (!lines.length) {\n    return [];\n  }\n  let i = 0;\n  const line = lines[0];\n  if (typeof line !== 'number') {\n    return [];\n  }\n  let current = makeContextRange(line);\n  const out = [];\n  while (true) {\n    if (i === lines.length - 1) {\n      out.push(current);\n      break;\n    }\n    // If the next line falls into the current range, extend the current range to lineno + linecontext.\n    const next = lines[i + 1];\n    if (typeof next !== 'number') {\n      break;\n    }\n    if (next <= current[1]) {\n      current[1] = next + DEFAULT_LINES_OF_CONTEXT;\n    } else {\n      out.push(current);\n      current = makeContextRange(next);\n    }\n    i++;\n  }\n  return out;\n}\n// Determine start and end indices for context range (inclusive);\nfunction makeContextRange(line) {\n  return [makeRangeStart(line), makeRangeEnd(line)];\n}\n// Compute inclusive end context range\nfunction makeRangeStart(line) {\n  return Math.max(1, line - DEFAULT_LINES_OF_CONTEXT);\n}\n// Compute inclusive start context range\nfunction makeRangeEnd(line) {\n  return line + DEFAULT_LINES_OF_CONTEXT;\n}\n/**\r\n * Get or init map value\r\n */\nfunction emplace(map, key, contents) {\n  const value = map.get(key);\n  if (value === undefined) {\n    map.set(key, contents);\n    return contents;\n  }\n  return value;\n}\nfunction snipLine(line, colno) {\n  let newLine = line;\n  const lineLength = newLine.length;\n  if (lineLength <= 150) {\n    return newLine;\n  }\n  if (colno > lineLength) {\n    colno = lineLength;\n  }\n  let start = Math.max(colno - 60, 0);\n  if (start < 5) {\n    start = 0;\n  }\n  let end = Math.min(start + 140, lineLength);\n  if (end > lineLength - 5) {\n    end = lineLength;\n  }\n  if (end === lineLength) {\n    start = Math.max(end - 140, 0);\n  }\n  newLine = newLine.slice(start, end);\n  if (start > 0) {\n    newLine = `...${newLine}`;\n  }\n  if (end < lineLength) {\n    newLine += '...';\n  }\n  return newLine;\n}\n\nvar version = \"4.17.2\";\n\nvar PostHogPersistedProperty;\r\n(function (PostHogPersistedProperty) {\r\n    PostHogPersistedProperty[\"AnonymousId\"] = \"anonymous_id\";\r\n    PostHogPersistedProperty[\"DistinctId\"] = \"distinct_id\";\r\n    PostHogPersistedProperty[\"Props\"] = \"props\";\r\n    PostHogPersistedProperty[\"FeatureFlagDetails\"] = \"feature_flag_details\";\r\n    PostHogPersistedProperty[\"FeatureFlags\"] = \"feature_flags\";\r\n    PostHogPersistedProperty[\"FeatureFlagPayloads\"] = \"feature_flag_payloads\";\r\n    PostHogPersistedProperty[\"BootstrapFeatureFlagDetails\"] = \"bootstrap_feature_flag_details\";\r\n    PostHogPersistedProperty[\"BootstrapFeatureFlags\"] = \"bootstrap_feature_flags\";\r\n    PostHogPersistedProperty[\"BootstrapFeatureFlagPayloads\"] = \"bootstrap_feature_flag_payloads\";\r\n    PostHogPersistedProperty[\"OverrideFeatureFlags\"] = \"override_feature_flags\";\r\n    PostHogPersistedProperty[\"Queue\"] = \"queue\";\r\n    PostHogPersistedProperty[\"OptedOut\"] = \"opted_out\";\r\n    PostHogPersistedProperty[\"SessionId\"] = \"session_id\";\r\n    PostHogPersistedProperty[\"SessionLastTimestamp\"] = \"session_timestamp\";\r\n    PostHogPersistedProperty[\"PersonProperties\"] = \"person_properties\";\r\n    PostHogPersistedProperty[\"GroupProperties\"] = \"group_properties\";\r\n    PostHogPersistedProperty[\"InstalledAppBuild\"] = \"installed_app_build\";\r\n    PostHogPersistedProperty[\"InstalledAppVersion\"] = \"installed_app_version\";\r\n    PostHogPersistedProperty[\"SessionReplay\"] = \"session_replay\";\r\n    PostHogPersistedProperty[\"DecideEndpointWasHit\"] = \"decide_endpoint_was_hit\";\r\n    PostHogPersistedProperty[\"SurveyLastSeenDate\"] = \"survey_last_seen_date\";\r\n    PostHogPersistedProperty[\"SurveysSeen\"] = \"surveys_seen\";\r\n    PostHogPersistedProperty[\"Surveys\"] = \"surveys\";\r\n    PostHogPersistedProperty[\"RemoteConfig\"] = \"remote_config\";\r\n})(PostHogPersistedProperty || (PostHogPersistedProperty = {}));\r\nvar SurveyPosition;\r\n(function (SurveyPosition) {\r\n    SurveyPosition[\"Left\"] = \"left\";\r\n    SurveyPosition[\"Right\"] = \"right\";\r\n    SurveyPosition[\"Center\"] = \"center\";\r\n})(SurveyPosition || (SurveyPosition = {}));\r\nvar SurveyWidgetType;\r\n(function (SurveyWidgetType) {\r\n    SurveyWidgetType[\"Button\"] = \"button\";\r\n    SurveyWidgetType[\"Tab\"] = \"tab\";\r\n    SurveyWidgetType[\"Selector\"] = \"selector\";\r\n})(SurveyWidgetType || (SurveyWidgetType = {}));\r\nvar SurveyType;\r\n(function (SurveyType) {\r\n    SurveyType[\"Popover\"] = \"popover\";\r\n    SurveyType[\"API\"] = \"api\";\r\n    SurveyType[\"Widget\"] = \"widget\";\r\n})(SurveyType || (SurveyType = {}));\r\nvar SurveyQuestionDescriptionContentType;\r\n(function (SurveyQuestionDescriptionContentType) {\r\n    SurveyQuestionDescriptionContentType[\"Html\"] = \"html\";\r\n    SurveyQuestionDescriptionContentType[\"Text\"] = \"text\";\r\n})(SurveyQuestionDescriptionContentType || (SurveyQuestionDescriptionContentType = {}));\r\nvar SurveyRatingDisplay;\r\n(function (SurveyRatingDisplay) {\r\n    SurveyRatingDisplay[\"Number\"] = \"number\";\r\n    SurveyRatingDisplay[\"Emoji\"] = \"emoji\";\r\n})(SurveyRatingDisplay || (SurveyRatingDisplay = {}));\r\nvar SurveyQuestionType;\r\n(function (SurveyQuestionType) {\r\n    SurveyQuestionType[\"Open\"] = \"open\";\r\n    SurveyQuestionType[\"MultipleChoice\"] = \"multiple_choice\";\r\n    SurveyQuestionType[\"SingleChoice\"] = \"single_choice\";\r\n    SurveyQuestionType[\"Rating\"] = \"rating\";\r\n    SurveyQuestionType[\"Link\"] = \"link\";\r\n})(SurveyQuestionType || (SurveyQuestionType = {}));\r\nvar SurveyQuestionBranchingType;\r\n(function (SurveyQuestionBranchingType) {\r\n    SurveyQuestionBranchingType[\"NextQuestion\"] = \"next_question\";\r\n    SurveyQuestionBranchingType[\"End\"] = \"end\";\r\n    SurveyQuestionBranchingType[\"ResponseBased\"] = \"response_based\";\r\n    SurveyQuestionBranchingType[\"SpecificQuestion\"] = \"specific_question\";\r\n})(SurveyQuestionBranchingType || (SurveyQuestionBranchingType = {}));\r\nvar SurveyMatchType;\r\n(function (SurveyMatchType) {\r\n    SurveyMatchType[\"Regex\"] = \"regex\";\r\n    SurveyMatchType[\"NotRegex\"] = \"not_regex\";\r\n    SurveyMatchType[\"Exact\"] = \"exact\";\r\n    SurveyMatchType[\"IsNot\"] = \"is_not\";\r\n    SurveyMatchType[\"Icontains\"] = \"icontains\";\r\n    SurveyMatchType[\"NotIcontains\"] = \"not_icontains\";\r\n})(SurveyMatchType || (SurveyMatchType = {}));\r\n/** Sync with plugin-server/src/types.ts */\r\nvar ActionStepStringMatching;\r\n(function (ActionStepStringMatching) {\r\n    ActionStepStringMatching[\"Contains\"] = \"contains\";\r\n    ActionStepStringMatching[\"Exact\"] = \"exact\";\r\n    ActionStepStringMatching[\"Regex\"] = \"regex\";\r\n})(ActionStepStringMatching || (ActionStepStringMatching = {}));\n\nconst normalizeDecideResponse = (decideResponse) => {\r\n    if ('flags' in decideResponse) {\r\n        // Convert v4 format to v3 format\r\n        const featureFlags = getFlagValuesFromFlags(decideResponse.flags);\r\n        const featureFlagPayloads = getPayloadsFromFlags(decideResponse.flags);\r\n        return {\r\n            ...decideResponse,\r\n            featureFlags,\r\n            featureFlagPayloads,\r\n        };\r\n    }\r\n    else {\r\n        // Convert v3 format to v4 format\r\n        const featureFlags = decideResponse.featureFlags ?? {};\r\n        const featureFlagPayloads = Object.fromEntries(Object.entries(decideResponse.featureFlagPayloads || {}).map(([k, v]) => [k, parsePayload(v)]));\r\n        const flags = Object.fromEntries(Object.entries(featureFlags).map(([key, value]) => [\r\n            key,\r\n            getFlagDetailFromFlagAndPayload(key, value, featureFlagPayloads[key]),\r\n        ]));\r\n        return {\r\n            ...decideResponse,\r\n            featureFlags,\r\n            featureFlagPayloads,\r\n            flags,\r\n        };\r\n    }\r\n};\r\nfunction getFlagDetailFromFlagAndPayload(key, value, payload) {\r\n    return {\r\n        key: key,\r\n        enabled: typeof value === 'string' ? true : value,\r\n        variant: typeof value === 'string' ? value : undefined,\r\n        reason: undefined,\r\n        metadata: {\r\n            id: undefined,\r\n            version: undefined,\r\n            payload: payload ? JSON.stringify(payload) : undefined,\r\n            description: undefined,\r\n        },\r\n    };\r\n}\r\n/**\r\n * Get the flag values from the flags v4 response.\r\n * @param flags - The flags\r\n * @returns The flag values\r\n */\r\nconst getFlagValuesFromFlags = (flags) => {\r\n    return Object.fromEntries(Object.entries(flags ?? {})\r\n        .map(([key, detail]) => [key, getFeatureFlagValue(detail)])\r\n        .filter(([, value]) => value !== undefined));\r\n};\r\n/**\r\n * Get the payloads from the flags v4 response.\r\n * @param flags - The flags\r\n * @returns The payloads\r\n */\r\nconst getPayloadsFromFlags = (flags) => {\r\n    const safeFlags = flags ?? {};\r\n    return Object.fromEntries(Object.keys(safeFlags)\r\n        .filter((flag) => {\r\n        const details = safeFlags[flag];\r\n        return details.enabled && details.metadata && details.metadata.payload !== undefined;\r\n    })\r\n        .map((flag) => {\r\n        const payload = safeFlags[flag].metadata?.payload;\r\n        return [flag, payload ? parsePayload(payload) : undefined];\r\n    }));\r\n};\r\nconst getFeatureFlagValue = (detail) => {\r\n    return detail === undefined ? undefined : detail.variant ?? detail.enabled;\r\n};\r\nconst parsePayload = (response) => {\r\n    if (typeof response !== 'string') {\r\n        return response;\r\n    }\r\n    try {\r\n        return JSON.parse(response);\r\n    }\r\n    catch {\r\n        return response;\r\n    }\r\n};\n\n// Rollout constants\r\nconst NEW_FLAGS_ROLLOUT_PERCENTAGE = 1;\r\n// The fnv1a hashes of the tokens that are explicitly excluded from the rollout\r\n// see https://github.com/PostHog/posthog-js-lite/blob/main/posthog-core/src/utils.ts#L84\r\n// are hashed API tokens from our top 10 for each category supported by this SDK.\r\nconst NEW_FLAGS_EXCLUDED_HASHES = new Set([\r\n    // Node\r\n    '61be3dd8',\r\n    '96f6df5f',\r\n    '8cfdba9b',\r\n    'bf027177',\r\n    'e59430a8',\r\n    '7fa5500b',\r\n    '569798e9',\r\n    '04809ff7',\r\n    '0ebc61a5',\r\n    '32de7f98',\r\n    '3beeb69a',\r\n    '12d34ad9',\r\n    '733853ec',\r\n    '0645bb64',\r\n    '5dcbee21',\r\n    'b1f95fa3',\r\n    '2189e408',\r\n    '82b460c2',\r\n    '3a8cc979',\r\n    '29ef8843',\r\n    '2cdbf767',\r\n    '38084b54',\r\n    // React Native\r\n    '50f9f8de',\r\n    '41d0df91',\r\n    '5c236689',\r\n    'c11aedd3',\r\n    'ada46672',\r\n    'f4331ee1',\r\n    '42fed62a',\r\n    'c957462c',\r\n    'd62f705a',\r\n    // Web (lots of teams per org, hence lots of API tokens)\r\n    'e0162666',\r\n    '01b3e5cf',\r\n    '441cef7f',\r\n    'bb9cafee',\r\n    '8f348eb0',\r\n    'b2553f3a',\r\n    '97469d7d',\r\n    '39f21a76',\r\n    '03706dcc',\r\n    '27d50569',\r\n    '307584a7',\r\n    '6433e92e',\r\n    '150c7fbb',\r\n    '49f57f22',\r\n    '3772f65b',\r\n    '01eb8256',\r\n    '3c9e9234',\r\n    'f853c7f7',\r\n    'c0ac4b67',\r\n    'cd609d40',\r\n    '10ca9b1a',\r\n    '8a87f11b',\r\n    '8e8e5216',\r\n    '1f6b63b3',\r\n    'db7943dd',\r\n    '79b7164c',\r\n    '07f78e33',\r\n    '2d21b6fd',\r\n    '952db5ee',\r\n    'a7d3b43f',\r\n    '1924dd9c',\r\n    '84e1b8f6',\r\n    'dff631b6',\r\n    'c5aa8a79',\r\n    'fa133a95',\r\n    '498a4508',\r\n    '24748755',\r\n    '98f3d658',\r\n    '21bbda67',\r\n    '7dbfed69',\r\n    'be3ec24c',\r\n    'fc80b8e2',\r\n    '75cc0998',\r\n]);\r\nconst STRING_FORMAT = 'utf8';\r\nfunction assert(truthyValue, message) {\r\n    if (!truthyValue || typeof truthyValue !== 'string' || isEmpty(truthyValue)) {\r\n        throw new Error(message);\r\n    }\r\n}\r\nfunction isEmpty(truthyValue) {\r\n    if (truthyValue.trim().length === 0) {\r\n        return true;\r\n    }\r\n    return false;\r\n}\r\nfunction removeTrailingSlash(url) {\r\n    return url?.replace(/\\/+$/, '');\r\n}\r\nasync function retriable(fn, props) {\r\n    let lastError = null;\r\n    for (let i = 0; i < props.retryCount + 1; i++) {\r\n        if (i > 0) {\r\n            // don't wait when it's the last try\r\n            await new Promise((r) => setTimeout(r, props.retryDelay));\r\n        }\r\n        try {\r\n            const res = await fn();\r\n            return res;\r\n        }\r\n        catch (e) {\r\n            lastError = e;\r\n            if (!props.retryCheck(e)) {\r\n                throw e;\r\n            }\r\n        }\r\n    }\r\n    throw lastError;\r\n}\r\nfunction currentTimestamp() {\r\n    return new Date().getTime();\r\n}\r\nfunction currentISOTime() {\r\n    return new Date().toISOString();\r\n}\r\nfunction safeSetTimeout(fn, timeout) {\r\n    // NOTE: we use this so rarely that it is totally fine to do `safeSetTimeout(fn, 0)``\r\n    // rather than setImmediate.\r\n    const t = setTimeout(fn, timeout);\r\n    // We unref if available to prevent Node.js hanging on exit\r\n    t?.unref && t?.unref();\r\n    return t;\r\n}\r\nfunction getFetch() {\r\n    return typeof fetch !== 'undefined' ? fetch : typeof globalThis.fetch !== 'undefined' ? globalThis.fetch : undefined;\r\n}\r\n// FNV-1a hash function\r\n// https://en.wikipedia.org/wiki/Fowler%E2%80%93Noll%E2%80%93Vo_hash_function\r\n// I know, I know, I'm rolling my own hash function, but I didn't want to take on\r\n// a crypto dependency and this is just temporary anyway\r\nfunction fnv1a(str) {\r\n    let hash = 0x811c9dc5; // FNV offset basis\r\n    for (let i = 0; i < str.length; i++) {\r\n        hash ^= str.charCodeAt(i);\r\n        hash += (hash << 1) + (hash << 4) + (hash << 7) + (hash << 8) + (hash << 24);\r\n    }\r\n    // Convert to hex string, padding to 8 chars\r\n    return (hash >>> 0).toString(16).padStart(8, '0');\r\n}\r\nfunction isTokenInRollout(token, percentage = 0, excludedHashes) {\r\n    const tokenHash = fnv1a(token);\r\n    // Check excluded hashes (we're explicitly including these tokens from the rollout)\r\n    if (excludedHashes?.has(tokenHash)) {\r\n        return false;\r\n    }\r\n    // Convert hash to int and divide by max value to get number between 0-1\r\n    const hashInt = parseInt(tokenHash, 16);\r\n    const hashFloat = hashInt / 0xffffffff;\r\n    return hashFloat < percentage;\r\n}\r\nfunction allSettled(promises) {\r\n    return Promise.all(promises.map((p) => (p ?? Promise.resolve()).then((value) => ({ status: 'fulfilled', value }), (reason) => ({ status: 'rejected', reason }))));\r\n}\n\n// Copyright (c) 2013 Pieroxy <<EMAIL>>\r\n// This work is free. You can redistribute it and/or modify it\r\n// under the terms of the WTFPL, Version 2\r\n// For more information see LICENSE.txt or http://www.wtfpl.net/\r\n//\r\n// For more information, the home page:\r\n// http://pieroxy.net/blog/pages/lz-string/testing.html\r\n//\r\n// LZ-based compression algorithm, version 1.4.4\r\n// private property\r\nconst f = String.fromCharCode;\r\nconst keyStrBase64 = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';\r\nconst baseReverseDic = {};\r\nfunction getBaseValue(alphabet, character) {\r\n    if (!baseReverseDic[alphabet]) {\r\n        baseReverseDic[alphabet] = {};\r\n        for (let i = 0; i < alphabet.length; i++) {\r\n            baseReverseDic[alphabet][alphabet.charAt(i)] = i;\r\n        }\r\n    }\r\n    return baseReverseDic[alphabet][character];\r\n}\r\nconst LZString = {\r\n    compressToBase64: function (input) {\r\n        if (input == null) {\r\n            return '';\r\n        }\r\n        const res = LZString._compress(input, 6, function (a) {\r\n            return keyStrBase64.charAt(a);\r\n        });\r\n        switch (res.length % 4 // To produce valid Base64\r\n        ) {\r\n            default: // When could this happen ?\r\n            case 0:\r\n                return res;\r\n            case 1:\r\n                return res + '===';\r\n            case 2:\r\n                return res + '==';\r\n            case 3:\r\n                return res + '=';\r\n        }\r\n    },\r\n    decompressFromBase64: function (input) {\r\n        if (input == null) {\r\n            return '';\r\n        }\r\n        if (input == '') {\r\n            return null;\r\n        }\r\n        return LZString._decompress(input.length, 32, function (index) {\r\n            return getBaseValue(keyStrBase64, input.charAt(index));\r\n        });\r\n    },\r\n    compress: function (uncompressed) {\r\n        return LZString._compress(uncompressed, 16, function (a) {\r\n            return f(a);\r\n        });\r\n    },\r\n    _compress: function (uncompressed, bitsPerChar, getCharFromInt) {\r\n        if (uncompressed == null) {\r\n            return '';\r\n        }\r\n        const context_dictionary = {}, context_dictionaryToCreate = {}, context_data = [];\r\n        let i, value, context_c = '', context_wc = '', context_w = '', context_enlargeIn = 2, // Compensate for the first entry which should not count\r\n        context_dictSize = 3, context_numBits = 2, context_data_val = 0, context_data_position = 0, ii;\r\n        for (ii = 0; ii < uncompressed.length; ii += 1) {\r\n            context_c = uncompressed.charAt(ii);\r\n            if (!Object.prototype.hasOwnProperty.call(context_dictionary, context_c)) {\r\n                context_dictionary[context_c] = context_dictSize++;\r\n                context_dictionaryToCreate[context_c] = true;\r\n            }\r\n            context_wc = context_w + context_c;\r\n            if (Object.prototype.hasOwnProperty.call(context_dictionary, context_wc)) {\r\n                context_w = context_wc;\r\n            }\r\n            else {\r\n                if (Object.prototype.hasOwnProperty.call(context_dictionaryToCreate, context_w)) {\r\n                    if (context_w.charCodeAt(0) < 256) {\r\n                        for (i = 0; i < context_numBits; i++) {\r\n                            context_data_val = context_data_val << 1;\r\n                            if (context_data_position == bitsPerChar - 1) {\r\n                                context_data_position = 0;\r\n                                context_data.push(getCharFromInt(context_data_val));\r\n                                context_data_val = 0;\r\n                            }\r\n                            else {\r\n                                context_data_position++;\r\n                            }\r\n                        }\r\n                        value = context_w.charCodeAt(0);\r\n                        for (i = 0; i < 8; i++) {\r\n                            context_data_val = (context_data_val << 1) | (value & 1);\r\n                            if (context_data_position == bitsPerChar - 1) {\r\n                                context_data_position = 0;\r\n                                context_data.push(getCharFromInt(context_data_val));\r\n                                context_data_val = 0;\r\n                            }\r\n                            else {\r\n                                context_data_position++;\r\n                            }\r\n                            value = value >> 1;\r\n                        }\r\n                    }\r\n                    else {\r\n                        value = 1;\r\n                        for (i = 0; i < context_numBits; i++) {\r\n                            context_data_val = (context_data_val << 1) | value;\r\n                            if (context_data_position == bitsPerChar - 1) {\r\n                                context_data_position = 0;\r\n                                context_data.push(getCharFromInt(context_data_val));\r\n                                context_data_val = 0;\r\n                            }\r\n                            else {\r\n                                context_data_position++;\r\n                            }\r\n                            value = 0;\r\n                        }\r\n                        value = context_w.charCodeAt(0);\r\n                        for (i = 0; i < 16; i++) {\r\n                            context_data_val = (context_data_val << 1) | (value & 1);\r\n                            if (context_data_position == bitsPerChar - 1) {\r\n                                context_data_position = 0;\r\n                                context_data.push(getCharFromInt(context_data_val));\r\n                                context_data_val = 0;\r\n                            }\r\n                            else {\r\n                                context_data_position++;\r\n                            }\r\n                            value = value >> 1;\r\n                        }\r\n                    }\r\n                    context_enlargeIn--;\r\n                    if (context_enlargeIn == 0) {\r\n                        context_enlargeIn = Math.pow(2, context_numBits);\r\n                        context_numBits++;\r\n                    }\r\n                    delete context_dictionaryToCreate[context_w];\r\n                }\r\n                else {\r\n                    value = context_dictionary[context_w];\r\n                    for (i = 0; i < context_numBits; i++) {\r\n                        context_data_val = (context_data_val << 1) | (value & 1);\r\n                        if (context_data_position == bitsPerChar - 1) {\r\n                            context_data_position = 0;\r\n                            context_data.push(getCharFromInt(context_data_val));\r\n                            context_data_val = 0;\r\n                        }\r\n                        else {\r\n                            context_data_position++;\r\n                        }\r\n                        value = value >> 1;\r\n                    }\r\n                }\r\n                context_enlargeIn--;\r\n                if (context_enlargeIn == 0) {\r\n                    context_enlargeIn = Math.pow(2, context_numBits);\r\n                    context_numBits++;\r\n                }\r\n                // Add wc to the dictionary.\r\n                context_dictionary[context_wc] = context_dictSize++;\r\n                context_w = String(context_c);\r\n            }\r\n        }\r\n        // Output the code for w.\r\n        if (context_w !== '') {\r\n            if (Object.prototype.hasOwnProperty.call(context_dictionaryToCreate, context_w)) {\r\n                if (context_w.charCodeAt(0) < 256) {\r\n                    for (i = 0; i < context_numBits; i++) {\r\n                        context_data_val = context_data_val << 1;\r\n                        if (context_data_position == bitsPerChar - 1) {\r\n                            context_data_position = 0;\r\n                            context_data.push(getCharFromInt(context_data_val));\r\n                            context_data_val = 0;\r\n                        }\r\n                        else {\r\n                            context_data_position++;\r\n                        }\r\n                    }\r\n                    value = context_w.charCodeAt(0);\r\n                    for (i = 0; i < 8; i++) {\r\n                        context_data_val = (context_data_val << 1) | (value & 1);\r\n                        if (context_data_position == bitsPerChar - 1) {\r\n                            context_data_position = 0;\r\n                            context_data.push(getCharFromInt(context_data_val));\r\n                            context_data_val = 0;\r\n                        }\r\n                        else {\r\n                            context_data_position++;\r\n                        }\r\n                        value = value >> 1;\r\n                    }\r\n                }\r\n                else {\r\n                    value = 1;\r\n                    for (i = 0; i < context_numBits; i++) {\r\n                        context_data_val = (context_data_val << 1) | value;\r\n                        if (context_data_position == bitsPerChar - 1) {\r\n                            context_data_position = 0;\r\n                            context_data.push(getCharFromInt(context_data_val));\r\n                            context_data_val = 0;\r\n                        }\r\n                        else {\r\n                            context_data_position++;\r\n                        }\r\n                        value = 0;\r\n                    }\r\n                    value = context_w.charCodeAt(0);\r\n                    for (i = 0; i < 16; i++) {\r\n                        context_data_val = (context_data_val << 1) | (value & 1);\r\n                        if (context_data_position == bitsPerChar - 1) {\r\n                            context_data_position = 0;\r\n                            context_data.push(getCharFromInt(context_data_val));\r\n                            context_data_val = 0;\r\n                        }\r\n                        else {\r\n                            context_data_position++;\r\n                        }\r\n                        value = value >> 1;\r\n                    }\r\n                }\r\n                context_enlargeIn--;\r\n                if (context_enlargeIn == 0) {\r\n                    context_enlargeIn = Math.pow(2, context_numBits);\r\n                    context_numBits++;\r\n                }\r\n                delete context_dictionaryToCreate[context_w];\r\n            }\r\n            else {\r\n                value = context_dictionary[context_w];\r\n                for (i = 0; i < context_numBits; i++) {\r\n                    context_data_val = (context_data_val << 1) | (value & 1);\r\n                    if (context_data_position == bitsPerChar - 1) {\r\n                        context_data_position = 0;\r\n                        context_data.push(getCharFromInt(context_data_val));\r\n                        context_data_val = 0;\r\n                    }\r\n                    else {\r\n                        context_data_position++;\r\n                    }\r\n                    value = value >> 1;\r\n                }\r\n            }\r\n            context_enlargeIn--;\r\n            if (context_enlargeIn == 0) {\r\n                context_enlargeIn = Math.pow(2, context_numBits);\r\n                context_numBits++;\r\n            }\r\n        }\r\n        // Mark the end of the stream\r\n        value = 2;\r\n        for (i = 0; i < context_numBits; i++) {\r\n            context_data_val = (context_data_val << 1) | (value & 1);\r\n            if (context_data_position == bitsPerChar - 1) {\r\n                context_data_position = 0;\r\n                context_data.push(getCharFromInt(context_data_val));\r\n                context_data_val = 0;\r\n            }\r\n            else {\r\n                context_data_position++;\r\n            }\r\n            value = value >> 1;\r\n        }\r\n        // Flush the last char\r\n        while (true) {\r\n            context_data_val = context_data_val << 1;\r\n            if (context_data_position == bitsPerChar - 1) {\r\n                context_data.push(getCharFromInt(context_data_val));\r\n                break;\r\n            }\r\n            else {\r\n                context_data_position++;\r\n            }\r\n        }\r\n        return context_data.join('');\r\n    },\r\n    decompress: function (compressed) {\r\n        if (compressed == null) {\r\n            return '';\r\n        }\r\n        if (compressed == '') {\r\n            return null;\r\n        }\r\n        return LZString._decompress(compressed.length, 32768, function (index) {\r\n            return compressed.charCodeAt(index);\r\n        });\r\n    },\r\n    _decompress: function (length, resetValue, getNextValue) {\r\n        const dictionary = [], result = [], data = { val: getNextValue(0), position: resetValue, index: 1 };\r\n        let enlargeIn = 4, dictSize = 4, numBits = 3, entry = '', i, w, bits, resb, maxpower, power, c;\r\n        for (i = 0; i < 3; i += 1) {\r\n            dictionary[i] = i;\r\n        }\r\n        bits = 0;\r\n        maxpower = Math.pow(2, 2);\r\n        power = 1;\r\n        while (power != maxpower) {\r\n            resb = data.val & data.position;\r\n            data.position >>= 1;\r\n            if (data.position == 0) {\r\n                data.position = resetValue;\r\n                data.val = getNextValue(data.index++);\r\n            }\r\n            bits |= (resb > 0 ? 1 : 0) * power;\r\n            power <<= 1;\r\n        }\r\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n        switch ((bits)) {\r\n            case 0:\r\n                bits = 0;\r\n                maxpower = Math.pow(2, 8);\r\n                power = 1;\r\n                while (power != maxpower) {\r\n                    resb = data.val & data.position;\r\n                    data.position >>= 1;\r\n                    if (data.position == 0) {\r\n                        data.position = resetValue;\r\n                        data.val = getNextValue(data.index++);\r\n                    }\r\n                    bits |= (resb > 0 ? 1 : 0) * power;\r\n                    power <<= 1;\r\n                }\r\n                c = f(bits);\r\n                break;\r\n            case 1:\r\n                bits = 0;\r\n                maxpower = Math.pow(2, 16);\r\n                power = 1;\r\n                while (power != maxpower) {\r\n                    resb = data.val & data.position;\r\n                    data.position >>= 1;\r\n                    if (data.position == 0) {\r\n                        data.position = resetValue;\r\n                        data.val = getNextValue(data.index++);\r\n                    }\r\n                    bits |= (resb > 0 ? 1 : 0) * power;\r\n                    power <<= 1;\r\n                }\r\n                c = f(bits);\r\n                break;\r\n            case 2:\r\n                return '';\r\n        }\r\n        dictionary[3] = c;\r\n        w = c;\r\n        result.push(c);\r\n        while (true) {\r\n            if (data.index > length) {\r\n                return '';\r\n            }\r\n            bits = 0;\r\n            maxpower = Math.pow(2, numBits);\r\n            power = 1;\r\n            while (power != maxpower) {\r\n                resb = data.val & data.position;\r\n                data.position >>= 1;\r\n                if (data.position == 0) {\r\n                    data.position = resetValue;\r\n                    data.val = getNextValue(data.index++);\r\n                }\r\n                bits |= (resb > 0 ? 1 : 0) * power;\r\n                power <<= 1;\r\n            }\r\n            switch ((c = bits)) {\r\n                case 0:\r\n                    bits = 0;\r\n                    maxpower = Math.pow(2, 8);\r\n                    power = 1;\r\n                    while (power != maxpower) {\r\n                        resb = data.val & data.position;\r\n                        data.position >>= 1;\r\n                        if (data.position == 0) {\r\n                            data.position = resetValue;\r\n                            data.val = getNextValue(data.index++);\r\n                        }\r\n                        bits |= (resb > 0 ? 1 : 0) * power;\r\n                        power <<= 1;\r\n                    }\r\n                    dictionary[dictSize++] = f(bits);\r\n                    c = dictSize - 1;\r\n                    enlargeIn--;\r\n                    break;\r\n                case 1:\r\n                    bits = 0;\r\n                    maxpower = Math.pow(2, 16);\r\n                    power = 1;\r\n                    while (power != maxpower) {\r\n                        resb = data.val & data.position;\r\n                        data.position >>= 1;\r\n                        if (data.position == 0) {\r\n                            data.position = resetValue;\r\n                            data.val = getNextValue(data.index++);\r\n                        }\r\n                        bits |= (resb > 0 ? 1 : 0) * power;\r\n                        power <<= 1;\r\n                    }\r\n                    dictionary[dictSize++] = f(bits);\r\n                    c = dictSize - 1;\r\n                    enlargeIn--;\r\n                    break;\r\n                case 2:\r\n                    return result.join('');\r\n            }\r\n            if (enlargeIn == 0) {\r\n                enlargeIn = Math.pow(2, numBits);\r\n                numBits++;\r\n            }\r\n            if (dictionary[c]) {\r\n                entry = dictionary[c];\r\n            }\r\n            else {\r\n                if (c === dictSize) {\r\n                    entry = w + w.charAt(0);\r\n                }\r\n                else {\r\n                    return null;\r\n                }\r\n            }\r\n            result.push(entry);\r\n            // Add w+entry[0] to the dictionary.\r\n            dictionary[dictSize++] = w + entry.charAt(0);\r\n            enlargeIn--;\r\n            w = entry;\r\n            if (enlargeIn == 0) {\r\n                enlargeIn = Math.pow(2, numBits);\r\n                numBits++;\r\n            }\r\n        }\r\n    },\r\n};\n\nclass SimpleEventEmitter {\r\n    constructor() {\r\n        this.events = {};\r\n        this.events = {};\r\n    }\r\n    on(event, listener) {\r\n        if (!this.events[event]) {\r\n            this.events[event] = [];\r\n        }\r\n        this.events[event].push(listener);\r\n        return () => {\r\n            this.events[event] = this.events[event].filter((x) => x !== listener);\r\n        };\r\n    }\r\n    emit(event, payload) {\r\n        for (const listener of this.events[event] || []) {\r\n            listener(payload);\r\n        }\r\n        for (const listener of this.events['*'] || []) {\r\n            listener(event, payload);\r\n        }\r\n    }\r\n}\n\nclass PostHogFetchHttpError extends Error {\r\n    constructor(response, reqByteLength) {\r\n        super('HTTP error while fetching PostHog: status=' + response.status + ', reqByteLength=' + reqByteLength);\r\n        this.response = response;\r\n        this.reqByteLength = reqByteLength;\r\n        this.name = 'PostHogFetchHttpError';\r\n    }\r\n    get status() {\r\n        return this.response.status;\r\n    }\r\n    get text() {\r\n        return this.response.text();\r\n    }\r\n    get json() {\r\n        return this.response.json();\r\n    }\r\n}\r\nclass PostHogFetchNetworkError extends Error {\r\n    constructor(error) {\r\n        // TRICKY: \"cause\" is a newer property but is just ignored otherwise. Cast to any to ignore the type issue.\r\n        // eslint-disable-next-line @typescript-eslint/prefer-ts-expect-error\r\n        // @ts-ignore\r\n        super('Network error while fetching PostHog', error instanceof Error ? { cause: error } : {});\r\n        this.error = error;\r\n        this.name = 'PostHogFetchNetworkError';\r\n    }\r\n}\r\nasync function logFlushError(err) {\r\n    if (err instanceof PostHogFetchHttpError) {\r\n        let text = '';\r\n        try {\r\n            text = await err.text;\r\n        }\r\n        catch { }\r\n        console.error(`Error while flushing PostHog: message=${err.message}, response body=${text}`, err);\r\n    }\r\n    else {\r\n        console.error('Error while flushing PostHog', err);\r\n    }\r\n    return Promise.resolve();\r\n}\r\nfunction isPostHogFetchError(err) {\r\n    return typeof err === 'object' && (err instanceof PostHogFetchHttpError || err instanceof PostHogFetchNetworkError);\r\n}\r\nfunction isPostHogFetchContentTooLargeError(err) {\r\n    return typeof err === 'object' && err instanceof PostHogFetchHttpError && err.status === 413;\r\n}\r\nvar QuotaLimitedFeature;\r\n(function (QuotaLimitedFeature) {\r\n    QuotaLimitedFeature[\"FeatureFlags\"] = \"feature_flags\";\r\n    QuotaLimitedFeature[\"Recordings\"] = \"recordings\";\r\n})(QuotaLimitedFeature || (QuotaLimitedFeature = {}));\r\nclass PostHogCoreStateless {\r\n    constructor(apiKey, options) {\r\n        this.flushPromise = null;\r\n        this.shutdownPromise = null;\r\n        this.pendingPromises = {};\r\n        // internal\r\n        this._events = new SimpleEventEmitter();\r\n        this._isInitialized = false;\r\n        assert(apiKey, \"You must pass your PostHog project's api key.\");\r\n        this.apiKey = apiKey;\r\n        this.host = removeTrailingSlash(options?.host || 'https://us.i.posthog.com');\r\n        this.flushAt = options?.flushAt ? Math.max(options?.flushAt, 1) : 20;\r\n        this.maxBatchSize = Math.max(this.flushAt, options?.maxBatchSize ?? 100);\r\n        this.maxQueueSize = Math.max(this.flushAt, options?.maxQueueSize ?? 1000);\r\n        this.flushInterval = options?.flushInterval ?? 10000;\r\n        this.captureMode = options?.captureMode || 'json';\r\n        this.preloadFeatureFlags = options?.preloadFeatureFlags ?? true;\r\n        // If enable is explicitly set to false we override the optout\r\n        this.defaultOptIn = options?.defaultOptIn ?? true;\r\n        this.disableSurveys = options?.disableSurveys ?? false;\r\n        this._retryOptions = {\r\n            retryCount: options?.fetchRetryCount ?? 3,\r\n            retryDelay: options?.fetchRetryDelay ?? 3000,\r\n            retryCheck: isPostHogFetchError,\r\n        };\r\n        this.requestTimeout = options?.requestTimeout ?? 10000; // 10 seconds\r\n        this.featureFlagsRequestTimeoutMs = options?.featureFlagsRequestTimeoutMs ?? 3000; // 3 seconds\r\n        this.remoteConfigRequestTimeoutMs = options?.remoteConfigRequestTimeoutMs ?? 3000; // 3 seconds\r\n        this.disableGeoip = options?.disableGeoip ?? true;\r\n        this.disabled = options?.disabled ?? false;\r\n        this.historicalMigration = options?.historicalMigration ?? false;\r\n        // Init promise allows the derived class to block calls until it is ready\r\n        this._initPromise = Promise.resolve();\r\n        this._isInitialized = true;\r\n    }\r\n    logMsgIfDebug(fn) {\r\n        if (this.isDebug) {\r\n            fn();\r\n        }\r\n    }\r\n    wrap(fn) {\r\n        if (this.disabled) {\r\n            this.logMsgIfDebug(() => console.warn('[PostHog] The client is disabled'));\r\n            return;\r\n        }\r\n        if (this._isInitialized) {\r\n            // NOTE: We could also check for the \"opt in\" status here...\r\n            return fn();\r\n        }\r\n        this._initPromise.then(() => fn());\r\n    }\r\n    getCommonEventProperties() {\r\n        return {\r\n            $lib: this.getLibraryId(),\r\n            $lib_version: this.getLibraryVersion(),\r\n        };\r\n    }\r\n    get optedOut() {\r\n        return this.getPersistedProperty(PostHogPersistedProperty.OptedOut) ?? !this.defaultOptIn;\r\n    }\r\n    async optIn() {\r\n        this.wrap(() => {\r\n            this.setPersistedProperty(PostHogPersistedProperty.OptedOut, false);\r\n        });\r\n    }\r\n    async optOut() {\r\n        this.wrap(() => {\r\n            this.setPersistedProperty(PostHogPersistedProperty.OptedOut, true);\r\n        });\r\n    }\r\n    on(event, cb) {\r\n        return this._events.on(event, cb);\r\n    }\r\n    debug(enabled = true) {\r\n        this.removeDebugCallback?.();\r\n        if (enabled) {\r\n            const removeDebugCallback = this.on('*', (event, payload) => console.log('PostHog Debug', event, payload));\r\n            this.removeDebugCallback = () => {\r\n                removeDebugCallback();\r\n                this.removeDebugCallback = undefined;\r\n            };\r\n        }\r\n    }\r\n    get isDebug() {\r\n        return !!this.removeDebugCallback;\r\n    }\r\n    get isDisabled() {\r\n        return this.disabled;\r\n    }\r\n    buildPayload(payload) {\r\n        return {\r\n            distinct_id: payload.distinct_id,\r\n            event: payload.event,\r\n            properties: {\r\n                ...(payload.properties || {}),\r\n                ...this.getCommonEventProperties(), // Common PH props\r\n            },\r\n        };\r\n    }\r\n    addPendingPromise(promise) {\r\n        const promiseUUID = uuidv7();\r\n        this.pendingPromises[promiseUUID] = promise;\r\n        promise\r\n            .catch(() => { })\r\n            .finally(() => {\r\n            delete this.pendingPromises[promiseUUID];\r\n        });\r\n        return promise;\r\n    }\r\n    /***\r\n     *** TRACKING\r\n     ***/\r\n    identifyStateless(distinctId, properties, options) {\r\n        this.wrap(() => {\r\n            // The properties passed to identifyStateless are event properties.\r\n            // To add person properties, pass in all person properties to the `$set` and `$set_once` keys.\r\n            const payload = {\r\n                ...this.buildPayload({\r\n                    distinct_id: distinctId,\r\n                    event: '$identify',\r\n                    properties,\r\n                }),\r\n            };\r\n            this.enqueue('identify', payload, options);\r\n        });\r\n    }\r\n    async identifyStatelessImmediate(distinctId, properties, options) {\r\n        const payload = {\r\n            ...this.buildPayload({\r\n                distinct_id: distinctId,\r\n                event: '$identify',\r\n                properties,\r\n            }),\r\n        };\r\n        await this.sendImmediate('identify', payload, options);\r\n    }\r\n    captureStateless(distinctId, event, properties, options) {\r\n        this.wrap(() => {\r\n            const payload = this.buildPayload({ distinct_id: distinctId, event, properties });\r\n            this.enqueue('capture', payload, options);\r\n        });\r\n    }\r\n    async captureStatelessImmediate(distinctId, event, properties, options) {\r\n        const payload = this.buildPayload({ distinct_id: distinctId, event, properties });\r\n        await this.sendImmediate('capture', payload, options);\r\n    }\r\n    aliasStateless(alias, distinctId, properties, options) {\r\n        this.wrap(() => {\r\n            const payload = this.buildPayload({\r\n                event: '$create_alias',\r\n                distinct_id: distinctId,\r\n                properties: {\r\n                    ...(properties || {}),\r\n                    distinct_id: distinctId,\r\n                    alias,\r\n                },\r\n            });\r\n            this.enqueue('alias', payload, options);\r\n        });\r\n    }\r\n    async aliasStatelessImmediate(alias, distinctId, properties, options) {\r\n        const payload = this.buildPayload({\r\n            event: '$create_alias',\r\n            distinct_id: distinctId,\r\n            properties: {\r\n                ...(properties || {}),\r\n                distinct_id: distinctId,\r\n                alias,\r\n            },\r\n        });\r\n        await this.sendImmediate('alias', payload, options);\r\n    }\r\n    /***\r\n     *** GROUPS\r\n     ***/\r\n    groupIdentifyStateless(groupType, groupKey, groupProperties, options, distinctId, eventProperties) {\r\n        this.wrap(() => {\r\n            const payload = this.buildPayload({\r\n                distinct_id: distinctId || `$${groupType}_${groupKey}`,\r\n                event: '$groupidentify',\r\n                properties: {\r\n                    $group_type: groupType,\r\n                    $group_key: groupKey,\r\n                    $group_set: groupProperties || {},\r\n                    ...(eventProperties || {}),\r\n                },\r\n            });\r\n            this.enqueue('capture', payload, options);\r\n        });\r\n    }\r\n    async getRemoteConfig() {\r\n        await this._initPromise;\r\n        let host = this.host;\r\n        if (host === 'https://us.i.posthog.com') {\r\n            host = 'https://us-assets.i.posthog.com';\r\n        }\r\n        else if (host === 'https://eu.i.posthog.com') {\r\n            host = 'https://eu-assets.i.posthog.com';\r\n        }\r\n        const url = `${host}/array/${this.apiKey}/config`;\r\n        const fetchOptions = {\r\n            method: 'GET',\r\n            headers: { ...this.getCustomHeaders(), 'Content-Type': 'application/json' },\r\n        };\r\n        // Don't retry remote config API calls\r\n        return this.fetchWithRetry(url, fetchOptions, { retryCount: 0 }, this.remoteConfigRequestTimeoutMs)\r\n            .then((response) => response.json())\r\n            .catch((error) => {\r\n            this.logMsgIfDebug(() => console.error('Remote config could not be loaded', error));\r\n            this._events.emit('error', error);\r\n            return undefined;\r\n        });\r\n    }\r\n    /***\r\n     *** FEATURE FLAGS\r\n     ***/\r\n    async getDecide(distinctId, groups = {}, personProperties = {}, groupProperties = {}, extraPayload = {}) {\r\n        await this._initPromise;\r\n        // Check if the API token is in the new flags rollout\r\n        // This is a temporary measure to ensure that we can still use the old flags API\r\n        // while we migrate to the new flags API\r\n        const useFlags = isTokenInRollout(this.apiKey, NEW_FLAGS_ROLLOUT_PERCENTAGE, NEW_FLAGS_EXCLUDED_HASHES);\r\n        const url = useFlags ? `${this.host}/flags/?v=2` : `${this.host}/decide/?v=4`;\r\n        const fetchOptions = {\r\n            method: 'POST',\r\n            headers: { ...this.getCustomHeaders(), 'Content-Type': 'application/json' },\r\n            body: JSON.stringify({\r\n                token: this.apiKey,\r\n                distinct_id: distinctId,\r\n                groups,\r\n                person_properties: personProperties,\r\n                group_properties: groupProperties,\r\n                ...extraPayload,\r\n            }),\r\n        };\r\n        this.logMsgIfDebug(() => console.log('PostHog Debug', 'Decide URL', url));\r\n        // Don't retry /decide API calls\r\n        return this.fetchWithRetry(url, fetchOptions, { retryCount: 0 }, this.featureFlagsRequestTimeoutMs)\r\n            .then((response) => response.json())\r\n            .then((response) => normalizeDecideResponse(response))\r\n            .catch((error) => {\r\n            this._events.emit('error', error);\r\n            return undefined;\r\n        });\r\n    }\r\n    async getFeatureFlagStateless(key, distinctId, groups = {}, personProperties = {}, groupProperties = {}, disableGeoip) {\r\n        await this._initPromise;\r\n        const flagDetailResponse = await this.getFeatureFlagDetailStateless(key, distinctId, groups, personProperties, groupProperties, disableGeoip);\r\n        if (flagDetailResponse === undefined) {\r\n            // If we haven't loaded flags yet, or errored out, we respond with undefined\r\n            return {\r\n                response: undefined,\r\n                requestId: undefined,\r\n            };\r\n        }\r\n        let response = getFeatureFlagValue(flagDetailResponse.response);\r\n        if (response === undefined) {\r\n            // For cases where the flag is unknown, return false\r\n            response = false;\r\n        }\r\n        // If we have flags we either return the value (true or string) or false\r\n        return {\r\n            response,\r\n            requestId: flagDetailResponse.requestId,\r\n        };\r\n    }\r\n    async getFeatureFlagDetailStateless(key, distinctId, groups = {}, personProperties = {}, groupProperties = {}, disableGeoip) {\r\n        await this._initPromise;\r\n        const decideResponse = await this.getFeatureFlagDetailsStateless(distinctId, groups, personProperties, groupProperties, disableGeoip, [key]);\r\n        if (decideResponse === undefined) {\r\n            return undefined;\r\n        }\r\n        const featureFlags = decideResponse.flags;\r\n        const flagDetail = featureFlags[key];\r\n        return {\r\n            response: flagDetail,\r\n            requestId: decideResponse.requestId,\r\n        };\r\n    }\r\n    async getFeatureFlagPayloadStateless(key, distinctId, groups = {}, personProperties = {}, groupProperties = {}, disableGeoip) {\r\n        await this._initPromise;\r\n        const payloads = await this.getFeatureFlagPayloadsStateless(distinctId, groups, personProperties, groupProperties, disableGeoip, [key]);\r\n        if (!payloads) {\r\n            return undefined;\r\n        }\r\n        const response = payloads[key];\r\n        // Undefined means a loading or missing data issue. Null means evaluation happened and there was no match\r\n        if (response === undefined) {\r\n            return null;\r\n        }\r\n        return response;\r\n    }\r\n    async getFeatureFlagPayloadsStateless(distinctId, groups = {}, personProperties = {}, groupProperties = {}, disableGeoip, flagKeysToEvaluate) {\r\n        await this._initPromise;\r\n        const payloads = (await this.getFeatureFlagsAndPayloadsStateless(distinctId, groups, personProperties, groupProperties, disableGeoip, flagKeysToEvaluate)).payloads;\r\n        return payloads;\r\n    }\r\n    async getFeatureFlagsStateless(distinctId, groups = {}, personProperties = {}, groupProperties = {}, disableGeoip, flagKeysToEvaluate) {\r\n        await this._initPromise;\r\n        return await this.getFeatureFlagsAndPayloadsStateless(distinctId, groups, personProperties, groupProperties, disableGeoip, flagKeysToEvaluate);\r\n    }\r\n    async getFeatureFlagsAndPayloadsStateless(distinctId, groups = {}, personProperties = {}, groupProperties = {}, disableGeoip, flagKeysToEvaluate) {\r\n        await this._initPromise;\r\n        const featureFlagDetails = await this.getFeatureFlagDetailsStateless(distinctId, groups, personProperties, groupProperties, disableGeoip, flagKeysToEvaluate);\r\n        if (!featureFlagDetails) {\r\n            return {\r\n                flags: undefined,\r\n                payloads: undefined,\r\n                requestId: undefined,\r\n            };\r\n        }\r\n        return {\r\n            flags: featureFlagDetails.featureFlags,\r\n            payloads: featureFlagDetails.featureFlagPayloads,\r\n            requestId: featureFlagDetails.requestId,\r\n        };\r\n    }\r\n    async getFeatureFlagDetailsStateless(distinctId, groups = {}, personProperties = {}, groupProperties = {}, disableGeoip, flagKeysToEvaluate) {\r\n        await this._initPromise;\r\n        const extraPayload = {};\r\n        if (disableGeoip ?? this.disableGeoip) {\r\n            extraPayload['geoip_disable'] = true;\r\n        }\r\n        if (flagKeysToEvaluate) {\r\n            extraPayload['flag_keys_to_evaluate'] = flagKeysToEvaluate;\r\n        }\r\n        const decideResponse = await this.getDecide(distinctId, groups, personProperties, groupProperties, extraPayload);\r\n        if (decideResponse === undefined) {\r\n            // We probably errored out, so return undefined\r\n            return undefined;\r\n        }\r\n        // if there's an error on the decideResponse, log a console error, but don't throw an error\r\n        if (decideResponse.errorsWhileComputingFlags) {\r\n            console.error('[FEATURE FLAGS] Error while computing feature flags, some flags may be missing or incorrect. Learn more at https://posthog.com/docs/feature-flags/best-practices');\r\n        }\r\n        // Add check for quota limitation on feature flags\r\n        if (decideResponse.quotaLimited?.includes(QuotaLimitedFeature.FeatureFlags)) {\r\n            console.warn('[FEATURE FLAGS] Feature flags quota limit exceeded - feature flags unavailable. Learn more about billing limits at https://posthog.com/docs/billing/limits-alerts');\r\n            return {\r\n                flags: {},\r\n                featureFlags: {},\r\n                featureFlagPayloads: {},\r\n                requestId: decideResponse?.requestId,\r\n            };\r\n        }\r\n        return decideResponse;\r\n    }\r\n    /***\r\n     *** SURVEYS\r\n     ***/\r\n    async getSurveysStateless() {\r\n        await this._initPromise;\r\n        if (this.disableSurveys === true) {\r\n            this.logMsgIfDebug(() => console.log('PostHog Debug', 'Loading surveys is disabled.'));\r\n            return [];\r\n        }\r\n        const url = `${this.host}/api/surveys/?token=${this.apiKey}`;\r\n        const fetchOptions = {\r\n            method: 'GET',\r\n            headers: { ...this.getCustomHeaders(), 'Content-Type': 'application/json' },\r\n        };\r\n        const response = await this.fetchWithRetry(url, fetchOptions)\r\n            .then((response) => {\r\n            if (response.status !== 200 || !response.json) {\r\n                const msg = `Surveys API could not be loaded: ${response.status}`;\r\n                const error = new Error(msg);\r\n                this.logMsgIfDebug(() => console.error(error));\r\n                this._events.emit('error', new Error(msg));\r\n                return undefined;\r\n            }\r\n            return response.json();\r\n        })\r\n            .catch((error) => {\r\n            this.logMsgIfDebug(() => console.error('Surveys API could not be loaded', error));\r\n            this._events.emit('error', error);\r\n            return undefined;\r\n        });\r\n        const newSurveys = response?.surveys;\r\n        if (newSurveys) {\r\n            this.logMsgIfDebug(() => console.log('PostHog Debug', 'Surveys fetched from API: ', JSON.stringify(newSurveys)));\r\n        }\r\n        return newSurveys ?? [];\r\n    }\r\n    get props() {\r\n        if (!this._props) {\r\n            this._props = this.getPersistedProperty(PostHogPersistedProperty.Props);\r\n        }\r\n        return this._props || {};\r\n    }\r\n    set props(val) {\r\n        this._props = val;\r\n    }\r\n    async register(properties) {\r\n        this.wrap(() => {\r\n            this.props = {\r\n                ...this.props,\r\n                ...properties,\r\n            };\r\n            this.setPersistedProperty(PostHogPersistedProperty.Props, this.props);\r\n        });\r\n    }\r\n    async unregister(property) {\r\n        this.wrap(() => {\r\n            delete this.props[property];\r\n            this.setPersistedProperty(PostHogPersistedProperty.Props, this.props);\r\n        });\r\n    }\r\n    /***\r\n     *** QUEUEING AND FLUSHING\r\n     ***/\r\n    enqueue(type, _message, options) {\r\n        this.wrap(() => {\r\n            if (this.optedOut) {\r\n                this._events.emit(type, `Library is disabled. Not sending event. To re-enable, call posthog.optIn()`);\r\n                return;\r\n            }\r\n            const message = this.prepareMessage(type, _message, options);\r\n            const queue = this.getPersistedProperty(PostHogPersistedProperty.Queue) || [];\r\n            if (queue.length >= this.maxQueueSize) {\r\n                queue.shift();\r\n                this.logMsgIfDebug(() => console.info('Queue is full, the oldest event is dropped.'));\r\n            }\r\n            queue.push({ message });\r\n            this.setPersistedProperty(PostHogPersistedProperty.Queue, queue);\r\n            this._events.emit(type, message);\r\n            // Flush queued events if we meet the flushAt length\r\n            if (queue.length >= this.flushAt) {\r\n                this.flushBackground();\r\n            }\r\n            if (this.flushInterval && !this._flushTimer) {\r\n                this._flushTimer = safeSetTimeout(() => this.flushBackground(), this.flushInterval);\r\n            }\r\n        });\r\n    }\r\n    async sendImmediate(type, _message, options) {\r\n        if (this.disabled) {\r\n            this.logMsgIfDebug(() => console.warn('[PostHog] The client is disabled'));\r\n            return;\r\n        }\r\n        if (!this._isInitialized) {\r\n            await this._initPromise;\r\n        }\r\n        if (this.optedOut) {\r\n            this._events.emit(type, `Library is disabled. Not sending event. To re-enable, call posthog.optIn()`);\r\n            return;\r\n        }\r\n        const data = {\r\n            api_key: this.apiKey,\r\n            batch: [this.prepareMessage(type, _message, options)],\r\n            sent_at: currentISOTime(),\r\n        };\r\n        if (this.historicalMigration) {\r\n            data.historical_migration = true;\r\n        }\r\n        const payload = JSON.stringify(data);\r\n        const url = this.captureMode === 'form'\r\n            ? `${this.host}/e/?ip=1&_=${currentTimestamp()}&v=${this.getLibraryVersion()}`\r\n            : `${this.host}/batch/`;\r\n        const fetchOptions = this.captureMode === 'form'\r\n            ? {\r\n                method: 'POST',\r\n                mode: 'no-cors',\r\n                credentials: 'omit',\r\n                headers: { ...this.getCustomHeaders(), 'Content-Type': 'application/x-www-form-urlencoded' },\r\n                body: `data=${encodeURIComponent(LZString.compressToBase64(payload))}&compression=lz64`,\r\n            }\r\n            : {\r\n                method: 'POST',\r\n                headers: { ...this.getCustomHeaders(), 'Content-Type': 'application/json' },\r\n                body: payload,\r\n            };\r\n        try {\r\n            await this.fetchWithRetry(url, fetchOptions);\r\n        }\r\n        catch (err) {\r\n            this._events.emit('error', err);\r\n        }\r\n    }\r\n    prepareMessage(type, _message, options) {\r\n        const message = {\r\n            ..._message,\r\n            type: type,\r\n            library: this.getLibraryId(),\r\n            library_version: this.getLibraryVersion(),\r\n            timestamp: options?.timestamp ? options?.timestamp : currentISOTime(),\r\n            uuid: options?.uuid ? options.uuid : uuidv7(),\r\n        };\r\n        const addGeoipDisableProperty = options?.disableGeoip ?? this.disableGeoip;\r\n        if (addGeoipDisableProperty) {\r\n            if (!message.properties) {\r\n                message.properties = {};\r\n            }\r\n            message['properties']['$geoip_disable'] = true;\r\n        }\r\n        if (message.distinctId) {\r\n            message.distinct_id = message.distinctId;\r\n            delete message.distinctId;\r\n        }\r\n        return message;\r\n    }\r\n    clearFlushTimer() {\r\n        if (this._flushTimer) {\r\n            clearTimeout(this._flushTimer);\r\n            this._flushTimer = undefined;\r\n        }\r\n    }\r\n    /**\r\n     * Helper for flushing the queue in the background\r\n     * Avoids unnecessary promise errors\r\n     */\r\n    flushBackground() {\r\n        void this.flush().catch(async (err) => {\r\n            await logFlushError(err);\r\n        });\r\n    }\r\n    /**\r\n     * Flushes the queue\r\n     *\r\n     * This function will return a promise that will resolve when the flush is complete,\r\n     * or reject if there was an error (for example if the server or network is down).\r\n     *\r\n     * If there is already a flush in progress, this function will wait for that flush to complete.\r\n     *\r\n     * It's recommended to do error handling in the callback of the promise.\r\n     *\r\n     * @example\r\n     * posthog.flush().then(() => {\r\n     *   console.log('Flush complete')\r\n     * }).catch((err) => {\r\n     *   console.error('Flush failed', err)\r\n     * })\r\n     *\r\n     *\r\n     * @throws PostHogFetchHttpError\r\n     * @throws PostHogFetchNetworkError\r\n     * @throws Error\r\n     */\r\n    async flush() {\r\n        // Wait for the current flush operation to finish (regardless of success or failure), then try to flush again.\r\n        // Use allSettled instead of finally to be defensive around flush throwing errors immediately rather than rejecting.\r\n        // Use a custom allSettled implementation to avoid issues with patching Promise on RN\r\n        const nextFlushPromise = allSettled([this.flushPromise]).then(() => {\r\n            return this._flush();\r\n        });\r\n        this.flushPromise = nextFlushPromise;\r\n        void this.addPendingPromise(nextFlushPromise);\r\n        allSettled([nextFlushPromise]).then(() => {\r\n            // If there are no others waiting to flush, clear the promise.\r\n            // We don't strictly need to do this, but it could make debugging easier\r\n            if (this.flushPromise === nextFlushPromise) {\r\n                this.flushPromise = null;\r\n            }\r\n        });\r\n        return nextFlushPromise;\r\n    }\r\n    getCustomHeaders() {\r\n        // Don't set the user agent if we're not on a browser. The latest spec allows\r\n        // the User-Agent header (see https://fetch.spec.whatwg.org/#terminology-headers\r\n        // and https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest/setRequestHeader),\r\n        // but browsers such as Chrome and Safari have not caught up.\r\n        const customUserAgent = this.getCustomUserAgent();\r\n        const headers = {};\r\n        if (customUserAgent && customUserAgent !== '') {\r\n            headers['User-Agent'] = customUserAgent;\r\n        }\r\n        return headers;\r\n    }\r\n    async _flush() {\r\n        this.clearFlushTimer();\r\n        await this._initPromise;\r\n        let queue = this.getPersistedProperty(PostHogPersistedProperty.Queue) || [];\r\n        if (!queue.length) {\r\n            return;\r\n        }\r\n        const sentMessages = [];\r\n        const originalQueueLength = queue.length;\r\n        while (queue.length > 0 && sentMessages.length < originalQueueLength) {\r\n            const batchItems = queue.slice(0, this.maxBatchSize);\r\n            const batchMessages = batchItems.map((item) => item.message);\r\n            const persistQueueChange = () => {\r\n                const refreshedQueue = this.getPersistedProperty(PostHogPersistedProperty.Queue) || [];\r\n                const newQueue = refreshedQueue.slice(batchItems.length);\r\n                this.setPersistedProperty(PostHogPersistedProperty.Queue, newQueue);\r\n                queue = newQueue;\r\n            };\r\n            const data = {\r\n                api_key: this.apiKey,\r\n                batch: batchMessages,\r\n                sent_at: currentISOTime(),\r\n            };\r\n            if (this.historicalMigration) {\r\n                data.historical_migration = true;\r\n            }\r\n            const payload = JSON.stringify(data);\r\n            const url = this.captureMode === 'form'\r\n                ? `${this.host}/e/?ip=1&_=${currentTimestamp()}&v=${this.getLibraryVersion()}`\r\n                : `${this.host}/batch/`;\r\n            const fetchOptions = this.captureMode === 'form'\r\n                ? {\r\n                    method: 'POST',\r\n                    mode: 'no-cors',\r\n                    credentials: 'omit',\r\n                    headers: { ...this.getCustomHeaders(), 'Content-Type': 'application/x-www-form-urlencoded' },\r\n                    body: `data=${encodeURIComponent(LZString.compressToBase64(payload))}&compression=lz64`,\r\n                }\r\n                : {\r\n                    method: 'POST',\r\n                    headers: { ...this.getCustomHeaders(), 'Content-Type': 'application/json' },\r\n                    body: payload,\r\n                };\r\n            const retryOptions = {\r\n                retryCheck: (err) => {\r\n                    // don't automatically retry on 413 errors, we want to reduce the batch size first\r\n                    if (isPostHogFetchContentTooLargeError(err)) {\r\n                        return false;\r\n                    }\r\n                    // otherwise, retry on network errors\r\n                    return isPostHogFetchError(err);\r\n                },\r\n            };\r\n            try {\r\n                await this.fetchWithRetry(url, fetchOptions, retryOptions);\r\n            }\r\n            catch (err) {\r\n                if (isPostHogFetchContentTooLargeError(err) && batchMessages.length > 1) {\r\n                    // if we get a 413 error, we want to reduce the batch size and try again\r\n                    this.maxBatchSize = Math.max(1, Math.floor(batchMessages.length / 2));\r\n                    this.logMsgIfDebug(() => console.warn(`Received 413 when sending batch of size ${batchMessages.length}, reducing batch size to ${this.maxBatchSize}`));\r\n                    // do not persist the queue change, we want to retry the same batch\r\n                    continue;\r\n                }\r\n                // depending on the error type, eg a malformed JSON or broken queue, it'll always return an error\r\n                // and this will be an endless loop, in this case, if the error isn't a network issue, we always remove the items from the queue\r\n                if (!(err instanceof PostHogFetchNetworkError)) {\r\n                    persistQueueChange();\r\n                }\r\n                this._events.emit('error', err);\r\n                throw err;\r\n            }\r\n            persistQueueChange();\r\n            sentMessages.push(...batchMessages);\r\n        }\r\n        this._events.emit('flush', sentMessages);\r\n    }\r\n    async fetchWithRetry(url, options, retryOptions, requestTimeout) {\r\n        var _a;\r\n        (_a = AbortSignal).timeout ?? (_a.timeout = function timeout(ms) {\r\n            const ctrl = new AbortController();\r\n            setTimeout(() => ctrl.abort(), ms);\r\n            return ctrl.signal;\r\n        });\r\n        const body = options.body ? options.body : '';\r\n        let reqByteLength = -1;\r\n        try {\r\n            reqByteLength = Buffer.byteLength(body, STRING_FORMAT);\r\n        }\r\n        catch {\r\n            const encoded = new TextEncoder().encode(body);\r\n            reqByteLength = encoded.length;\r\n        }\r\n        return await retriable(async () => {\r\n            let res = null;\r\n            try {\r\n                res = await this.fetch(url, {\r\n                    signal: AbortSignal.timeout(requestTimeout ?? this.requestTimeout),\r\n                    ...options,\r\n                });\r\n            }\r\n            catch (e) {\r\n                // fetch will only throw on network errors or on timeouts\r\n                throw new PostHogFetchNetworkError(e);\r\n            }\r\n            // If we're in no-cors mode, we can't access the response status\r\n            // We only throw on HTTP errors if we're not in no-cors mode\r\n            // https://developer.mozilla.org/en-US/docs/Web/API/Request/mode#no-cors\r\n            const isNoCors = options.mode === 'no-cors';\r\n            if (!isNoCors && (res.status < 200 || res.status >= 400)) {\r\n                throw new PostHogFetchHttpError(res, reqByteLength);\r\n            }\r\n            return res;\r\n        }, { ...this._retryOptions, ...retryOptions });\r\n    }\r\n    async _shutdown(shutdownTimeoutMs = 30000) {\r\n        // A little tricky - we want to have a max shutdown time and enforce it, even if that means we have some\r\n        // dangling promises. We'll keep track of the timeout and resolve/reject based on that.\r\n        await this._initPromise;\r\n        let hasTimedOut = false;\r\n        this.clearFlushTimer();\r\n        const doShutdown = async () => {\r\n            try {\r\n                await Promise.all(Object.values(this.pendingPromises));\r\n                while (true) {\r\n                    const queue = this.getPersistedProperty(PostHogPersistedProperty.Queue) || [];\r\n                    if (queue.length === 0) {\r\n                        break;\r\n                    }\r\n                    // flush again to make sure we send all events, some of which might've been added\r\n                    // while we were waiting for the pending promises to resolve\r\n                    // For example, see sendFeatureFlags in posthog-node/src/posthog-node.ts::capture\r\n                    await this.flush();\r\n                    if (hasTimedOut) {\r\n                        break;\r\n                    }\r\n                }\r\n            }\r\n            catch (e) {\r\n                if (!isPostHogFetchError(e)) {\r\n                    throw e;\r\n                }\r\n                await logFlushError(e);\r\n            }\r\n        };\r\n        return Promise.race([\r\n            new Promise((_, reject) => {\r\n                safeSetTimeout(() => {\r\n                    this.logMsgIfDebug(() => console.error('Timed out while shutting down PostHog'));\r\n                    hasTimedOut = true;\r\n                    reject('Timeout while shutting down PostHog. Some events may not have been sent.');\r\n                }, shutdownTimeoutMs);\r\n            }),\r\n            doShutdown(),\r\n        ]);\r\n    }\r\n    /**\r\n     *  Call shutdown() once before the node process exits, so ensure that all events have been sent and all promises\r\n     *  have resolved. Do not use this function if you intend to keep using this PostHog instance after calling it.\r\n     * @param shutdownTimeoutMs\r\n     */\r\n    async shutdown(shutdownTimeoutMs = 30000) {\r\n        if (this.shutdownPromise) {\r\n            this.logMsgIfDebug(() => console.warn('shutdown() called while already shutting down. shutdown() is meant to be called once before process exit - use flush() for per-request cleanup'));\r\n        }\r\n        else {\r\n            this.shutdownPromise = this._shutdown(shutdownTimeoutMs).finally(() => {\r\n                this.shutdownPromise = null;\r\n            });\r\n        }\r\n        return this.shutdownPromise;\r\n    }\r\n}\n\n/**\r\n * Fetch wrapper\r\n *\r\n * We want to polyfill fetch when not available with axios but use it when it is.\r\n * NOTE: The current version of Axios has an issue when in non-node environments like Clouflare Workers.\r\n * This is currently solved by using the global fetch if available instead.\r\n * See https://github.com/PostHog/posthog-js-lite/issues/127 for more info\r\n */\nlet _fetch = getFetch();\nif (!_fetch) {\n  // eslint-disable-next-line @typescript-eslint/no-var-requires\n  const axios = require('axios');\n  _fetch = async (url, options) => {\n    const res = await axios.request({\n      url,\n      headers: options.headers,\n      method: options.method.toLowerCase(),\n      data: options.body,\n      signal: options.signal,\n      // fetch only throws on network errors, not on HTTP errors\n      validateStatus: () => true\n    });\n    return {\n      status: res.status,\n      text: async () => res.data,\n      json: async () => res.data\n    };\n  };\n}\n// NOTE: We have to export this as default, even though we prefer named exports as we are relying on detecting \"fetch\" in the global scope\nvar fetch$1 = _fetch;\n\n/**\r\n * A lazy value that is only computed when needed. Inspired by C#'s Lazy<T> class.\r\n */\nclass Lazy {\n  constructor(factory) {\n    this.factory = factory;\n  }\n  /**\r\n   * Gets the value, initializing it if necessary.\r\n   * Multiple concurrent calls will share the same initialization promise.\r\n   */\n  async getValue() {\n    if (this.value !== undefined) {\n      return this.value;\n    }\n    if (this.initializationPromise === undefined) {\n      this.initializationPromise = (async () => {\n        try {\n          const result = await this.factory();\n          this.value = result;\n          return result;\n        } finally {\n          // Clear the promise so we can retry if needed\n          this.initializationPromise = undefined;\n        }\n      })();\n    }\n    return this.initializationPromise;\n  }\n  /**\r\n   * Returns true if the value has been initialized.\r\n   */\n  isInitialized() {\n    return this.value !== undefined;\n  }\n  /**\r\n   * Returns a promise that resolves when the value is initialized.\r\n   * If already initialized, resolves immediately.\r\n   */\n  async waitForInitialization() {\n    if (this.isInitialized()) {\n      return;\n    }\n    await this.getValue();\n  }\n}\n\n/// <reference lib=\"dom\" />\nconst nodeCrypto = new Lazy(async () => {\n  try {\n    return await Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! crypto */ \"crypto\", 19));\n  } catch {\n    return undefined;\n  }\n});\nasync function getNodeCrypto() {\n  return await nodeCrypto.getValue();\n}\nconst webCrypto = new Lazy(async () => {\n  if (typeof globalThis.crypto?.subtle !== 'undefined') {\n    return globalThis.crypto.subtle;\n  }\n  try {\n    // Node.js: use built-in webcrypto and assign it if needed\n    const crypto = await nodeCrypto.getValue();\n    if (crypto?.webcrypto?.subtle) {\n      return crypto.webcrypto.subtle;\n    }\n  } catch {\n    // Ignore if not available\n  }\n  return undefined;\n});\nasync function getWebCrypto() {\n  return await webCrypto.getValue();\n}\n\n/// <reference lib=\"dom\" />\nasync function hashSHA1(text) {\n  // Try Node.js crypto first\n  const nodeCrypto = await getNodeCrypto();\n  if (nodeCrypto) {\n    return nodeCrypto.createHash('sha1').update(text).digest('hex');\n  }\n  const webCrypto = await getWebCrypto();\n  // Fall back to Web Crypto API\n  if (webCrypto) {\n    const hashBuffer = await webCrypto.digest('SHA-1', new TextEncoder().encode(text));\n    const hashArray = Array.from(new Uint8Array(hashBuffer));\n    return hashArray.map(byte => byte.toString(16).padStart(2, '0')).join('');\n  }\n  throw new Error('No crypto implementation available. Tried Node Crypto API and Web SubtleCrypto API');\n}\n\nconst SIXTY_SECONDS = 60 * 1000;\n// eslint-disable-next-line\nconst LONG_SCALE = 0xfffffffffffffff;\nconst NULL_VALUES_ALLOWED_OPERATORS = ['is_not'];\nclass ClientError extends Error {\n  constructor(message) {\n    super();\n    Error.captureStackTrace(this, this.constructor);\n    this.name = 'ClientError';\n    this.message = message;\n    Object.setPrototypeOf(this, ClientError.prototype);\n  }\n}\nclass InconclusiveMatchError extends Error {\n  constructor(message) {\n    super(message);\n    this.name = this.constructor.name;\n    Error.captureStackTrace(this, this.constructor);\n    // instanceof doesn't work in ES3 or ES5\n    // https://www.dannyguo.com/blog/how-to-fix-instanceof-not-working-for-custom-errors-in-typescript/\n    // this is the workaround\n    Object.setPrototypeOf(this, InconclusiveMatchError.prototype);\n  }\n}\nclass FeatureFlagsPoller {\n  constructor({\n    pollingInterval,\n    personalApiKey,\n    projectApiKey,\n    timeout,\n    host,\n    customHeaders,\n    ...options\n  }) {\n    this.debugMode = false;\n    this.shouldBeginExponentialBackoff = false;\n    this.backOffCount = 0;\n    this.pollingInterval = pollingInterval;\n    this.personalApiKey = personalApiKey;\n    this.featureFlags = [];\n    this.featureFlagsByKey = {};\n    this.groupTypeMapping = {};\n    this.cohorts = {};\n    this.loadedSuccessfullyOnce = false;\n    this.timeout = timeout;\n    this.projectApiKey = projectApiKey;\n    this.host = host;\n    this.poller = undefined;\n    this.fetch = options.fetch || fetch$1;\n    this.onError = options.onError;\n    this.customHeaders = customHeaders;\n    this.onLoad = options.onLoad;\n    void this.loadFeatureFlags();\n  }\n  debug(enabled = true) {\n    this.debugMode = enabled;\n  }\n  logMsgIfDebug(fn) {\n    if (this.debugMode) {\n      fn();\n    }\n  }\n  async getFeatureFlag(key, distinctId, groups = {}, personProperties = {}, groupProperties = {}) {\n    await this.loadFeatureFlags();\n    let response = undefined;\n    let featureFlag = undefined;\n    if (!this.loadedSuccessfullyOnce) {\n      return response;\n    }\n    for (const flag of this.featureFlags) {\n      if (key === flag.key) {\n        featureFlag = flag;\n        break;\n      }\n    }\n    if (featureFlag !== undefined) {\n      try {\n        response = await this.computeFlagLocally(featureFlag, distinctId, groups, personProperties, groupProperties);\n        this.logMsgIfDebug(() => console.debug(`Successfully computed flag locally: ${key} -> ${response}`));\n      } catch (e) {\n        if (e instanceof InconclusiveMatchError) {\n          this.logMsgIfDebug(() => console.debug(`InconclusiveMatchError when computing flag locally: ${key}: ${e}`));\n        } else if (e instanceof Error) {\n          this.onError?.(new Error(`Error computing flag locally: ${key}: ${e}`));\n        }\n      }\n    }\n    return response;\n  }\n  async computeFeatureFlagPayloadLocally(key, matchValue) {\n    await this.loadFeatureFlags();\n    let response = undefined;\n    if (!this.loadedSuccessfullyOnce) {\n      return undefined;\n    }\n    if (typeof matchValue == 'boolean') {\n      response = this.featureFlagsByKey?.[key]?.filters?.payloads?.[matchValue.toString()];\n    } else if (typeof matchValue == 'string') {\n      response = this.featureFlagsByKey?.[key]?.filters?.payloads?.[matchValue];\n    }\n    // Undefined means a loading or missing data issue. Null means evaluation happened and there was no match\n    if (response === undefined || response === null) {\n      return null;\n    }\n    try {\n      return JSON.parse(response);\n    } catch {\n      return response;\n    }\n  }\n  async getAllFlagsAndPayloads(distinctId, groups = {}, personProperties = {}, groupProperties = {}) {\n    await this.loadFeatureFlags();\n    const response = {};\n    const payloads = {};\n    let fallbackToDecide = this.featureFlags.length == 0;\n    await Promise.all(this.featureFlags.map(async flag => {\n      try {\n        const matchValue = await this.computeFlagLocally(flag, distinctId, groups, personProperties, groupProperties);\n        response[flag.key] = matchValue;\n        const matchPayload = await this.computeFeatureFlagPayloadLocally(flag.key, matchValue);\n        if (matchPayload) {\n          payloads[flag.key] = matchPayload;\n        }\n      } catch (e) {\n        if (e instanceof InconclusiveMatchError) ; else if (e instanceof Error) {\n          this.onError?.(new Error(`Error computing flag locally: ${flag.key}: ${e}`));\n        }\n        fallbackToDecide = true;\n      }\n    }));\n    return {\n      response,\n      payloads,\n      fallbackToDecide\n    };\n  }\n  async computeFlagLocally(flag, distinctId, groups = {}, personProperties = {}, groupProperties = {}) {\n    if (flag.ensure_experience_continuity) {\n      throw new InconclusiveMatchError('Flag has experience continuity enabled');\n    }\n    if (!flag.active) {\n      return false;\n    }\n    const flagFilters = flag.filters || {};\n    const aggregation_group_type_index = flagFilters.aggregation_group_type_index;\n    if (aggregation_group_type_index != undefined) {\n      const groupName = this.groupTypeMapping[String(aggregation_group_type_index)];\n      if (!groupName) {\n        this.logMsgIfDebug(() => console.warn(`[FEATURE FLAGS] Unknown group type index ${aggregation_group_type_index} for feature flag ${flag.key}`));\n        throw new InconclusiveMatchError('Flag has unknown group type index');\n      }\n      if (!(groupName in groups)) {\n        this.logMsgIfDebug(() => console.warn(`[FEATURE FLAGS] Can't compute group feature flag: ${flag.key} without group names passed in`));\n        return false;\n      }\n      const focusedGroupProperties = groupProperties[groupName];\n      return await this.matchFeatureFlagProperties(flag, groups[groupName], focusedGroupProperties);\n    } else {\n      return await this.matchFeatureFlagProperties(flag, distinctId, personProperties);\n    }\n  }\n  async matchFeatureFlagProperties(flag, distinctId, properties) {\n    const flagFilters = flag.filters || {};\n    const flagConditions = flagFilters.groups || [];\n    let isInconclusive = false;\n    let result = undefined;\n    // # Stable sort conditions with variant overrides to the top. This ensures that if overrides are present, they are\n    // # evaluated first, and the variant override is applied to the first matching condition.\n    const sortedFlagConditions = [...flagConditions].sort((conditionA, conditionB) => {\n      const AHasVariantOverride = !!conditionA.variant;\n      const BHasVariantOverride = !!conditionB.variant;\n      if (AHasVariantOverride && BHasVariantOverride) {\n        return 0;\n      } else if (AHasVariantOverride) {\n        return -1;\n      } else if (BHasVariantOverride) {\n        return 1;\n      } else {\n        return 0;\n      }\n    });\n    for (const condition of sortedFlagConditions) {\n      try {\n        if (await this.isConditionMatch(flag, distinctId, condition, properties)) {\n          const variantOverride = condition.variant;\n          const flagVariants = flagFilters.multivariate?.variants || [];\n          if (variantOverride && flagVariants.some(variant => variant.key === variantOverride)) {\n            result = variantOverride;\n          } else {\n            result = (await this.getMatchingVariant(flag, distinctId)) || true;\n          }\n          break;\n        }\n      } catch (e) {\n        if (e instanceof InconclusiveMatchError) {\n          isInconclusive = true;\n        } else {\n          throw e;\n        }\n      }\n    }\n    if (result !== undefined) {\n      return result;\n    } else if (isInconclusive) {\n      throw new InconclusiveMatchError(\"Can't determine if feature flag is enabled or not with given properties\");\n    }\n    // We can only return False when all conditions are False\n    return false;\n  }\n  async isConditionMatch(flag, distinctId, condition, properties) {\n    const rolloutPercentage = condition.rollout_percentage;\n    const warnFunction = msg => {\n      this.logMsgIfDebug(() => console.warn(msg));\n    };\n    if ((condition.properties || []).length > 0) {\n      for (const prop of condition.properties) {\n        const propertyType = prop.type;\n        let matches = false;\n        if (propertyType === 'cohort') {\n          matches = matchCohort(prop, properties, this.cohorts, this.debugMode);\n        } else {\n          matches = matchProperty(prop, properties, warnFunction);\n        }\n        if (!matches) {\n          return false;\n        }\n      }\n      if (rolloutPercentage == undefined) {\n        return true;\n      }\n    }\n    if (rolloutPercentage != undefined && (await _hash(flag.key, distinctId)) > rolloutPercentage / 100.0) {\n      return false;\n    }\n    return true;\n  }\n  async getMatchingVariant(flag, distinctId) {\n    const hashValue = await _hash(flag.key, distinctId, 'variant');\n    const matchingVariant = this.variantLookupTable(flag).find(variant => {\n      return hashValue >= variant.valueMin && hashValue < variant.valueMax;\n    });\n    if (matchingVariant) {\n      return matchingVariant.key;\n    }\n    return undefined;\n  }\n  variantLookupTable(flag) {\n    const lookupTable = [];\n    let valueMin = 0;\n    let valueMax = 0;\n    const flagFilters = flag.filters || {};\n    const multivariates = flagFilters.multivariate?.variants || [];\n    multivariates.forEach(variant => {\n      valueMax = valueMin + variant.rollout_percentage / 100.0;\n      lookupTable.push({\n        valueMin,\n        valueMax,\n        key: variant.key\n      });\n      valueMin = valueMax;\n    });\n    return lookupTable;\n  }\n  async loadFeatureFlags(forceReload = false) {\n    if (!this.loadedSuccessfullyOnce || forceReload) {\n      await this._loadFeatureFlags();\n    }\n  }\n  /**\r\n   * Returns true if the feature flags poller has loaded successfully at least once and has more than 0 feature flags.\r\n   * This is useful to check if local evaluation is ready before calling getFeatureFlag.\r\n   */\n  isLocalEvaluationReady() {\n    return (this.loadedSuccessfullyOnce ?? false) && (this.featureFlags?.length ?? 0) > 0;\n  }\n  /**\r\n   * If a client is misconfigured with an invalid or improper API key, the polling interval is doubled each time\r\n   * until a successful request is made, up to a maximum of 60 seconds.\r\n   *\r\n   * @returns The polling interval to use for the next request.\r\n   */\n  getPollingInterval() {\n    if (!this.shouldBeginExponentialBackoff) {\n      return this.pollingInterval;\n    }\n    return Math.min(SIXTY_SECONDS, this.pollingInterval * 2 ** this.backOffCount);\n  }\n  async _loadFeatureFlags() {\n    if (this.poller) {\n      clearTimeout(this.poller);\n      this.poller = undefined;\n    }\n    this.poller = setTimeout(() => this._loadFeatureFlags(), this.getPollingInterval());\n    try {\n      const res = await this._requestFeatureFlagDefinitions();\n      // Handle undefined res case, this shouldn't happen, but it doesn't hurt to handle it anyway\n      if (!res) {\n        // Don't override existing flags when something goes wrong\n        return;\n      }\n      // NB ON ERROR HANDLING & `loadedSuccessfullyOnce`:\n      //\n      // `loadedSuccessfullyOnce` indicates we've successfully loaded a valid set of flags at least once.\n      // If we set it to `true` in an error scenario (e.g. 402 Over Quota, 401 Invalid Key, etc.),\n      // any manual call to `loadFeatureFlags()` (without forceReload) will skip refetching entirely,\n      // leaving us stuck with zero or outdated flags. The poller does keep running, but we also want\n      // manual reloads to be possible as soon as the error condition is resolved.\n      //\n      // Therefore, on error statuses, we do *not* set `loadedSuccessfullyOnce = true`, ensuring that\n      // both the background poller and any subsequent manual calls can keep trying to load flags\n      // once the issue (quota, permission, rate limit, etc.) is resolved.\n      switch (res.status) {\n        case 401:\n          // Invalid API key\n          this.shouldBeginExponentialBackoff = true;\n          this.backOffCount += 1;\n          throw new ClientError(`Your project key or personal API key is invalid. Setting next polling interval to ${this.getPollingInterval()}ms. More information: https://posthog.com/docs/api#rate-limiting`);\n        case 402:\n          // Quota exceeded - clear all flags\n          console.warn('[FEATURE FLAGS] Feature flags quota limit exceeded - unsetting all local flags. Learn more about billing limits at https://posthog.com/docs/billing/limits-alerts');\n          this.featureFlags = [];\n          this.featureFlagsByKey = {};\n          this.groupTypeMapping = {};\n          this.cohorts = {};\n          return;\n        case 403:\n          // Permissions issue\n          this.shouldBeginExponentialBackoff = true;\n          this.backOffCount += 1;\n          throw new ClientError(`Your personal API key does not have permission to fetch feature flag definitions for local evaluation. Setting next polling interval to ${this.getPollingInterval()}ms. Are you sure you're using the correct personal and Project API key pair? More information: https://posthog.com/docs/api/overview`);\n        case 429:\n          // Rate limited\n          this.shouldBeginExponentialBackoff = true;\n          this.backOffCount += 1;\n          throw new ClientError(`You are being rate limited. Setting next polling interval to ${this.getPollingInterval()}ms. More information: https://posthog.com/docs/api#rate-limiting`);\n        case 200:\n          {\n            // Process successful response\n            const responseJson = (await res.json()) ?? {};\n            if (!('flags' in responseJson)) {\n              this.onError?.(new Error(`Invalid response when getting feature flags: ${JSON.stringify(responseJson)}`));\n              return;\n            }\n            this.featureFlags = responseJson.flags ?? [];\n            this.featureFlagsByKey = this.featureFlags.reduce((acc, curr) => (acc[curr.key] = curr, acc), {});\n            this.groupTypeMapping = responseJson.group_type_mapping || {};\n            this.cohorts = responseJson.cohorts || {};\n            this.loadedSuccessfullyOnce = true;\n            this.shouldBeginExponentialBackoff = false;\n            this.backOffCount = 0;\n            this.onLoad?.(this.featureFlags.length);\n            break;\n          }\n        default:\n          // Something else went wrong, or the server is down.\n          // In this case, don't override existing flags\n          return;\n      }\n    } catch (err) {\n      if (err instanceof ClientError) {\n        this.onError?.(err);\n      }\n    }\n  }\n  getPersonalApiKeyRequestOptions(method = 'GET') {\n    return {\n      method,\n      headers: {\n        ...this.customHeaders,\n        'Content-Type': 'application/json',\n        Authorization: `Bearer ${this.personalApiKey}`\n      }\n    };\n  }\n  async _requestFeatureFlagDefinitions() {\n    const url = `${this.host}/api/feature_flag/local_evaluation?token=${this.projectApiKey}&send_cohorts`;\n    const options = this.getPersonalApiKeyRequestOptions();\n    let abortTimeout = null;\n    if (this.timeout && typeof this.timeout === 'number') {\n      const controller = new AbortController();\n      abortTimeout = safeSetTimeout(() => {\n        controller.abort();\n      }, this.timeout);\n      options.signal = controller.signal;\n    }\n    try {\n      return await this.fetch(url, options);\n    } finally {\n      clearTimeout(abortTimeout);\n    }\n  }\n  stopPoller() {\n    clearTimeout(this.poller);\n  }\n  _requestRemoteConfigPayload(flagKey) {\n    const url = `${this.host}/api/projects/@current/feature_flags/${flagKey}/remote_config/`;\n    const options = this.getPersonalApiKeyRequestOptions();\n    let abortTimeout = null;\n    if (this.timeout && typeof this.timeout === 'number') {\n      const controller = new AbortController();\n      abortTimeout = safeSetTimeout(() => {\n        controller.abort();\n      }, this.timeout);\n      options.signal = controller.signal;\n    }\n    try {\n      return this.fetch(url, options);\n    } finally {\n      clearTimeout(abortTimeout);\n    }\n  }\n}\n// # This function takes a distinct_id and a feature flag key and returns a float between 0 and 1.\n// # Given the same distinct_id and key, it'll always return the same float. These floats are\n// # uniformly distributed between 0 and 1, so if we want to show this feature to 20% of traffic\n// # we can do _hash(key, distinct_id) < 0.2\nasync function _hash(key, distinctId, salt = '') {\n  const hashString = await hashSHA1(`${key}.${distinctId}${salt}`);\n  return parseInt(hashString.slice(0, 15), 16) / LONG_SCALE;\n}\nfunction matchProperty(property, propertyValues, warnFunction) {\n  const key = property.key;\n  const value = property.value;\n  const operator = property.operator || 'exact';\n  if (!(key in propertyValues)) {\n    throw new InconclusiveMatchError(`Property ${key} not found in propertyValues`);\n  } else if (operator === 'is_not_set') {\n    throw new InconclusiveMatchError(`Operator is_not_set is not supported`);\n  }\n  const overrideValue = propertyValues[key];\n  if (overrideValue == null && !NULL_VALUES_ALLOWED_OPERATORS.includes(operator)) {\n    // if the value is null, just fail the feature flag comparison\n    // this isn't an InconclusiveMatchError because the property value was provided.\n    if (warnFunction) {\n      warnFunction(`Property ${key} cannot have a value of null/undefined with the ${operator} operator`);\n    }\n    return false;\n  }\n  function computeExactMatch(value, overrideValue) {\n    if (Array.isArray(value)) {\n      return value.map(val => String(val).toLowerCase()).includes(String(overrideValue).toLowerCase());\n    }\n    return String(value).toLowerCase() === String(overrideValue).toLowerCase();\n  }\n  function compare(lhs, rhs, operator) {\n    if (operator === 'gt') {\n      return lhs > rhs;\n    } else if (operator === 'gte') {\n      return lhs >= rhs;\n    } else if (operator === 'lt') {\n      return lhs < rhs;\n    } else if (operator === 'lte') {\n      return lhs <= rhs;\n    } else {\n      throw new Error(`Invalid operator: ${operator}`);\n    }\n  }\n  switch (operator) {\n    case 'exact':\n      return computeExactMatch(value, overrideValue);\n    case 'is_not':\n      return !computeExactMatch(value, overrideValue);\n    case 'is_set':\n      return key in propertyValues;\n    case 'icontains':\n      return String(overrideValue).toLowerCase().includes(String(value).toLowerCase());\n    case 'not_icontains':\n      return !String(overrideValue).toLowerCase().includes(String(value).toLowerCase());\n    case 'regex':\n      return isValidRegex(String(value)) && String(overrideValue).match(String(value)) !== null;\n    case 'not_regex':\n      return isValidRegex(String(value)) && String(overrideValue).match(String(value)) === null;\n    case 'gt':\n    case 'gte':\n    case 'lt':\n    case 'lte':\n      {\n        // :TRICKY: We adjust comparison based on the override value passed in,\n        // to make sure we handle both numeric and string comparisons appropriately.\n        let parsedValue = typeof value === 'number' ? value : null;\n        if (typeof value === 'string') {\n          try {\n            parsedValue = parseFloat(value);\n          } catch (err) {\n            // pass\n          }\n        }\n        if (parsedValue != null && overrideValue != null) {\n          // check both null and undefined\n          if (typeof overrideValue === 'string') {\n            return compare(overrideValue, String(value), operator);\n          } else {\n            return compare(overrideValue, parsedValue, operator);\n          }\n        } else {\n          return compare(String(overrideValue), String(value), operator);\n        }\n      }\n    case 'is_date_after':\n    case 'is_date_before':\n      {\n        let parsedDate = relativeDateParseForFeatureFlagMatching(String(value));\n        if (parsedDate == null) {\n          parsedDate = convertToDateTime(value);\n        }\n        if (parsedDate == null) {\n          throw new InconclusiveMatchError(`Invalid date: ${value}`);\n        }\n        const overrideDate = convertToDateTime(overrideValue);\n        if (['is_date_before'].includes(operator)) {\n          return overrideDate < parsedDate;\n        }\n        return overrideDate > parsedDate;\n      }\n    default:\n      throw new InconclusiveMatchError(`Unknown operator: ${operator}`);\n  }\n}\nfunction matchCohort(property, propertyValues, cohortProperties, debugMode = false) {\n  const cohortId = String(property.value);\n  if (!(cohortId in cohortProperties)) {\n    throw new InconclusiveMatchError(\"can't match cohort without a given cohort property value\");\n  }\n  const propertyGroup = cohortProperties[cohortId];\n  return matchPropertyGroup(propertyGroup, propertyValues, cohortProperties, debugMode);\n}\nfunction matchPropertyGroup(propertyGroup, propertyValues, cohortProperties, debugMode = false) {\n  if (!propertyGroup) {\n    return true;\n  }\n  const propertyGroupType = propertyGroup.type;\n  const properties = propertyGroup.values;\n  if (!properties || properties.length === 0) {\n    // empty groups are no-ops, always match\n    return true;\n  }\n  let errorMatchingLocally = false;\n  if ('values' in properties[0]) {\n    // a nested property group\n    for (const prop of properties) {\n      try {\n        const matches = matchPropertyGroup(prop, propertyValues, cohortProperties, debugMode);\n        if (propertyGroupType === 'AND') {\n          if (!matches) {\n            return false;\n          }\n        } else {\n          // OR group\n          if (matches) {\n            return true;\n          }\n        }\n      } catch (err) {\n        if (err instanceof InconclusiveMatchError) {\n          if (debugMode) {\n            console.debug(`Failed to compute property ${prop} locally: ${err}`);\n          }\n          errorMatchingLocally = true;\n        } else {\n          throw err;\n        }\n      }\n    }\n    if (errorMatchingLocally) {\n      throw new InconclusiveMatchError(\"Can't match cohort without a given cohort property value\");\n    }\n    // if we get here, all matched in AND case, or none matched in OR case\n    return propertyGroupType === 'AND';\n  } else {\n    for (const prop of properties) {\n      try {\n        let matches;\n        if (prop.type === 'cohort') {\n          matches = matchCohort(prop, propertyValues, cohortProperties, debugMode);\n        } else {\n          matches = matchProperty(prop, propertyValues);\n        }\n        const negation = prop.negation || false;\n        if (propertyGroupType === 'AND') {\n          // if negated property, do the inverse\n          if (!matches && !negation) {\n            return false;\n          }\n          if (matches && negation) {\n            return false;\n          }\n        } else {\n          // OR group\n          if (matches && !negation) {\n            return true;\n          }\n          if (!matches && negation) {\n            return true;\n          }\n        }\n      } catch (err) {\n        if (err instanceof InconclusiveMatchError) {\n          if (debugMode) {\n            console.debug(`Failed to compute property ${prop} locally: ${err}`);\n          }\n          errorMatchingLocally = true;\n        } else {\n          throw err;\n        }\n      }\n    }\n    if (errorMatchingLocally) {\n      throw new InconclusiveMatchError(\"can't match cohort without a given cohort property value\");\n    }\n    // if we get here, all matched in AND case, or none matched in OR case\n    return propertyGroupType === 'AND';\n  }\n}\nfunction isValidRegex(regex) {\n  try {\n    new RegExp(regex);\n    return true;\n  } catch (err) {\n    return false;\n  }\n}\nfunction convertToDateTime(value) {\n  if (value instanceof Date) {\n    return value;\n  } else if (typeof value === 'string' || typeof value === 'number') {\n    const date = new Date(value);\n    if (!isNaN(date.valueOf())) {\n      return date;\n    }\n    throw new InconclusiveMatchError(`${value} is in an invalid date format`);\n  } else {\n    throw new InconclusiveMatchError(`The date provided ${value} must be a string, number, or date object`);\n  }\n}\nfunction relativeDateParseForFeatureFlagMatching(value) {\n  const regex = /^-?(?<number>[0-9]+)(?<interval>[a-z])$/;\n  const match = value.match(regex);\n  const parsedDt = new Date(new Date().toISOString());\n  if (match) {\n    if (!match.groups) {\n      return null;\n    }\n    const number = parseInt(match.groups['number']);\n    if (number >= 10000) {\n      // Guard against overflow, disallow numbers greater than 10_000\n      return null;\n    }\n    const interval = match.groups['interval'];\n    if (interval == 'h') {\n      parsedDt.setUTCHours(parsedDt.getUTCHours() - number);\n    } else if (interval == 'd') {\n      parsedDt.setUTCDate(parsedDt.getUTCDate() - number);\n    } else if (interval == 'w') {\n      parsedDt.setUTCDate(parsedDt.getUTCDate() - number * 7);\n    } else if (interval == 'm') {\n      parsedDt.setUTCMonth(parsedDt.getUTCMonth() - number);\n    } else if (interval == 'y') {\n      parsedDt.setUTCFullYear(parsedDt.getUTCFullYear() - number);\n    } else {\n      return null;\n    }\n    return parsedDt;\n  } else {\n    return null;\n  }\n}\n\nclass PostHogMemoryStorage {\n  constructor() {\n    this._memoryStorage = {};\n  }\n  getProperty(key) {\n    return this._memoryStorage[key];\n  }\n  setProperty(key, value) {\n    this._memoryStorage[key] = value !== null ? value : undefined;\n  }\n}\n\n// Standard local evaluation rate limit is 600 per minute (10 per second),\n// so the fastest a poller should ever be set is 100ms.\nconst MINIMUM_POLLING_INTERVAL = 100;\nconst THIRTY_SECONDS = 30 * 1000;\nconst MAX_CACHE_SIZE = 50 * 1000;\n// The actual exported Nodejs API.\nclass PostHogBackendClient extends PostHogCoreStateless {\n  constructor(apiKey, options = {}) {\n    super(apiKey, options);\n    this._memoryStorage = new PostHogMemoryStorage();\n    this.options = options;\n    this.options.featureFlagsPollingInterval = typeof options.featureFlagsPollingInterval === 'number' ? Math.max(options.featureFlagsPollingInterval, MINIMUM_POLLING_INTERVAL) : THIRTY_SECONDS;\n    if (options.personalApiKey) {\n      if (options.personalApiKey.includes('phc_')) {\n        throw new Error('Your Personal API key is invalid. These keys are prefixed with \"phx_\" and can be created in PostHog project settings.');\n      }\n      this.featureFlagsPoller = new FeatureFlagsPoller({\n        pollingInterval: this.options.featureFlagsPollingInterval,\n        personalApiKey: options.personalApiKey,\n        projectApiKey: apiKey,\n        timeout: options.requestTimeout ?? 10000,\n        host: this.host,\n        fetch: options.fetch,\n        onError: err => {\n          this._events.emit('error', err);\n        },\n        onLoad: count => {\n          this._events.emit('localEvaluationFlagsLoaded', count);\n        },\n        customHeaders: this.getCustomHeaders()\n      });\n    }\n    this.errorTracking = new ErrorTracking(this, options);\n    this.distinctIdHasSentFlagCalls = {};\n    this.maxCacheSize = options.maxCacheSize || MAX_CACHE_SIZE;\n  }\n  getPersistedProperty(key) {\n    return this._memoryStorage.getProperty(key);\n  }\n  setPersistedProperty(key, value) {\n    return this._memoryStorage.setProperty(key, value);\n  }\n  fetch(url, options) {\n    return this.options.fetch ? this.options.fetch(url, options) : fetch$1(url, options);\n  }\n  getLibraryVersion() {\n    return version;\n  }\n  getCustomUserAgent() {\n    return `${this.getLibraryId()}/${this.getLibraryVersion()}`;\n  }\n  enable() {\n    return super.optIn();\n  }\n  disable() {\n    return super.optOut();\n  }\n  debug(enabled = true) {\n    super.debug(enabled);\n    this.featureFlagsPoller?.debug(enabled);\n  }\n  capture(props) {\n    if (typeof props === 'string') {\n      this.logMsgIfDebug(() => console.warn('Called capture() with a string as the first argument when an object was expected.'));\n    }\n    const {\n      distinctId,\n      event,\n      properties,\n      groups,\n      sendFeatureFlags,\n      timestamp,\n      disableGeoip,\n      uuid\n    } = props;\n    const _capture = props => {\n      super.captureStateless(distinctId, event, props, {\n        timestamp,\n        disableGeoip,\n        uuid\n      });\n    };\n    const _getFlags = async (distinctId, groups, disableGeoip) => {\n      return (await super.getFeatureFlagsStateless(distinctId, groups, undefined, undefined, disableGeoip)).flags;\n    };\n    // :TRICKY: If we flush, or need to shut down, to not lose events we want this promise to resolve before we flush\n    const capturePromise = Promise.resolve().then(async () => {\n      if (sendFeatureFlags) {\n        // If we are sending feature flags, we need to make sure we have the latest flags\n        // return await super.getFeatureFlagsStateless(distinctId, groups, undefined, undefined, disableGeoip)\n        return await _getFlags(distinctId, groups, disableGeoip);\n      }\n      if (event === '$feature_flag_called') {\n        // If we're capturing a $feature_flag_called event, we don't want to enrich the event with cached flags that may be out of date.\n        return {};\n      }\n      if ((this.featureFlagsPoller?.featureFlags?.length || 0) > 0) {\n        // Otherwise we may as well check for the flags locally and include them if they are already loaded\n        const groupsWithStringValues = {};\n        for (const [key, value] of Object.entries(groups || {})) {\n          groupsWithStringValues[key] = String(value);\n        }\n        return await this.getAllFlags(distinctId, {\n          groups: groupsWithStringValues,\n          disableGeoip,\n          onlyEvaluateLocally: true\n        });\n      }\n      return {};\n    }).then(flags => {\n      // Derive the relevant flag properties to add\n      const additionalProperties = {};\n      if (flags) {\n        for (const [feature, variant] of Object.entries(flags)) {\n          additionalProperties[`$feature/${feature}`] = variant;\n        }\n      }\n      const activeFlags = Object.keys(flags || {}).filter(flag => flags?.[flag] !== false).sort();\n      if (activeFlags.length > 0) {\n        additionalProperties['$active_feature_flags'] = activeFlags;\n      }\n      return additionalProperties;\n    }).catch(() => {\n      // Something went wrong getting the flag info - we should capture the event anyways\n      return {};\n    }).then(additionalProperties => {\n      // No matter what - capture the event\n      _capture({\n        ...additionalProperties,\n        ...properties,\n        $groups: groups\n      });\n    });\n    this.addPendingPromise(capturePromise);\n  }\n  async captureImmediate(props) {\n    if (typeof props === 'string') {\n      this.logMsgIfDebug(() => console.warn('Called capture() with a string as the first argument when an object was expected.'));\n    }\n    const {\n      distinctId,\n      event,\n      properties,\n      groups,\n      sendFeatureFlags,\n      timestamp,\n      disableGeoip,\n      uuid\n    } = props;\n    const _capture = props => {\n      return super.captureStatelessImmediate(distinctId, event, props, {\n        timestamp,\n        disableGeoip,\n        uuid\n      });\n    };\n    const _getFlags = async (distinctId, groups, disableGeoip) => {\n      return (await super.getFeatureFlagsStateless(distinctId, groups, undefined, undefined, disableGeoip)).flags;\n    };\n    const capturePromise = Promise.resolve().then(async () => {\n      if (sendFeatureFlags) {\n        // If we are sending feature flags, we need to make sure we have the latest flags\n        // return await super.getFeatureFlagsStateless(distinctId, groups, undefined, undefined, disableGeoip)\n        return await _getFlags(distinctId, groups, disableGeoip);\n      }\n      if (event === '$feature_flag_called') {\n        // If we're capturing a $feature_flag_called event, we don't want to enrich the event with cached flags that may be out of date.\n        return {};\n      }\n      if ((this.featureFlagsPoller?.featureFlags?.length || 0) > 0) {\n        // Otherwise we may as well check for the flags locally and include them if they are already loaded\n        const groupsWithStringValues = {};\n        for (const [key, value] of Object.entries(groups || {})) {\n          groupsWithStringValues[key] = String(value);\n        }\n        return await this.getAllFlags(distinctId, {\n          groups: groupsWithStringValues,\n          disableGeoip,\n          onlyEvaluateLocally: true\n        });\n      }\n      return {};\n    }).then(flags => {\n      // Derive the relevant flag properties to add\n      const additionalProperties = {};\n      if (flags) {\n        for (const [feature, variant] of Object.entries(flags)) {\n          additionalProperties[`$feature/${feature}`] = variant;\n        }\n      }\n      const activeFlags = Object.keys(flags || {}).filter(flag => flags?.[flag] !== false).sort();\n      if (activeFlags.length > 0) {\n        additionalProperties['$active_feature_flags'] = activeFlags;\n      }\n      return additionalProperties;\n    }).catch(() => {\n      // Something went wrong getting the flag info - we should capture the event anyways\n      return {};\n    }).then(additionalProperties => {\n      // No matter what - capture the event\n      _capture({\n        ...additionalProperties,\n        ...properties,\n        $groups: groups\n      });\n    });\n    await capturePromise;\n  }\n  identify({\n    distinctId,\n    properties,\n    disableGeoip\n  }) {\n    // Catch properties passed as $set and move them to the top level\n    // promote $set and $set_once to top level\n    const userPropsOnce = properties?.$set_once;\n    delete properties?.$set_once;\n    // if no $set is provided we assume all properties are $set\n    const userProps = properties?.$set || properties;\n    super.identifyStateless(distinctId, {\n      $set: userProps,\n      $set_once: userPropsOnce\n    }, {\n      disableGeoip\n    });\n  }\n  async identifyImmediate({\n    distinctId,\n    properties,\n    disableGeoip\n  }) {\n    // promote $set and $set_once to top level\n    const userPropsOnce = properties?.$set_once;\n    delete properties?.$set_once;\n    // if no $set is provided we assume all properties are $set\n    const userProps = properties?.$set || properties;\n    await super.identifyStatelessImmediate(distinctId, {\n      $set: userProps,\n      $set_once: userPropsOnce\n    }, {\n      disableGeoip\n    });\n  }\n  alias(data) {\n    super.aliasStateless(data.alias, data.distinctId, undefined, {\n      disableGeoip: data.disableGeoip\n    });\n  }\n  async aliasImmediate(data) {\n    await super.aliasStatelessImmediate(data.alias, data.distinctId, undefined, {\n      disableGeoip: data.disableGeoip\n    });\n  }\n  isLocalEvaluationReady() {\n    return this.featureFlagsPoller?.isLocalEvaluationReady() ?? false;\n  }\n  async waitForLocalEvaluationReady(timeoutMs = THIRTY_SECONDS) {\n    if (this.isLocalEvaluationReady()) {\n      return true;\n    }\n    if (this.featureFlagsPoller === undefined) {\n      return false;\n    }\n    return new Promise(resolve => {\n      const timeout = setTimeout(() => {\n        cleanup();\n        resolve(false);\n      }, timeoutMs);\n      const cleanup = this._events.on('localEvaluationFlagsLoaded', count => {\n        clearTimeout(timeout);\n        cleanup();\n        resolve(count > 0);\n      });\n    });\n  }\n  async getFeatureFlag(key, distinctId, options) {\n    const {\n      groups,\n      disableGeoip\n    } = options || {};\n    let {\n      onlyEvaluateLocally,\n      sendFeatureFlagEvents,\n      personProperties,\n      groupProperties\n    } = options || {};\n    const adjustedProperties = this.addLocalPersonAndGroupProperties(distinctId, groups, personProperties, groupProperties);\n    personProperties = adjustedProperties.allPersonProperties;\n    groupProperties = adjustedProperties.allGroupProperties;\n    // set defaults\n    if (onlyEvaluateLocally == undefined) {\n      onlyEvaluateLocally = false;\n    }\n    if (sendFeatureFlagEvents == undefined) {\n      sendFeatureFlagEvents = true;\n    }\n    let response = await this.featureFlagsPoller?.getFeatureFlag(key, distinctId, groups, personProperties, groupProperties);\n    const flagWasLocallyEvaluated = response !== undefined;\n    let requestId = undefined;\n    let flagDetail = undefined;\n    if (!flagWasLocallyEvaluated && !onlyEvaluateLocally) {\n      const remoteResponse = await super.getFeatureFlagDetailStateless(key, distinctId, groups, personProperties, groupProperties, disableGeoip);\n      if (remoteResponse === undefined) {\n        return undefined;\n      }\n      flagDetail = remoteResponse.response;\n      response = getFeatureFlagValue(flagDetail);\n      requestId = remoteResponse?.requestId;\n    }\n    const featureFlagReportedKey = `${key}_${response}`;\n    if (sendFeatureFlagEvents && (!(distinctId in this.distinctIdHasSentFlagCalls) || !this.distinctIdHasSentFlagCalls[distinctId].includes(featureFlagReportedKey))) {\n      if (Object.keys(this.distinctIdHasSentFlagCalls).length >= this.maxCacheSize) {\n        this.distinctIdHasSentFlagCalls = {};\n      }\n      if (Array.isArray(this.distinctIdHasSentFlagCalls[distinctId])) {\n        this.distinctIdHasSentFlagCalls[distinctId].push(featureFlagReportedKey);\n      } else {\n        this.distinctIdHasSentFlagCalls[distinctId] = [featureFlagReportedKey];\n      }\n      this.capture({\n        distinctId,\n        event: '$feature_flag_called',\n        properties: {\n          $feature_flag: key,\n          $feature_flag_response: response,\n          $feature_flag_id: flagDetail?.metadata?.id,\n          $feature_flag_version: flagDetail?.metadata?.version,\n          $feature_flag_reason: flagDetail?.reason?.description ?? flagDetail?.reason?.code,\n          locally_evaluated: flagWasLocallyEvaluated,\n          [`$feature/${key}`]: response,\n          $feature_flag_request_id: requestId\n        },\n        groups,\n        disableGeoip\n      });\n    }\n    return response;\n  }\n  async getFeatureFlagPayload(key, distinctId, matchValue, options) {\n    const {\n      groups,\n      disableGeoip\n    } = options || {};\n    let {\n      onlyEvaluateLocally,\n      sendFeatureFlagEvents,\n      personProperties,\n      groupProperties\n    } = options || {};\n    const adjustedProperties = this.addLocalPersonAndGroupProperties(distinctId, groups, personProperties, groupProperties);\n    personProperties = adjustedProperties.allPersonProperties;\n    groupProperties = adjustedProperties.allGroupProperties;\n    let response = undefined;\n    const localEvaluationEnabled = this.featureFlagsPoller !== undefined;\n    if (localEvaluationEnabled) {\n      // Try to get match value locally if not provided\n      if (!matchValue) {\n        matchValue = await this.getFeatureFlag(key, distinctId, {\n          ...options,\n          onlyEvaluateLocally: true,\n          sendFeatureFlagEvents: false\n        });\n      }\n      if (matchValue) {\n        response = await this.featureFlagsPoller?.computeFeatureFlagPayloadLocally(key, matchValue);\n      }\n    }\n    //}\n    // set defaults\n    if (onlyEvaluateLocally == undefined) {\n      onlyEvaluateLocally = false;\n    }\n    if (sendFeatureFlagEvents == undefined) {\n      sendFeatureFlagEvents = true;\n    }\n    // set defaults\n    if (onlyEvaluateLocally == undefined) {\n      onlyEvaluateLocally = false;\n    }\n    const payloadWasLocallyEvaluated = response !== undefined;\n    if (!payloadWasLocallyEvaluated && !onlyEvaluateLocally) {\n      response = await super.getFeatureFlagPayloadStateless(key, distinctId, groups, personProperties, groupProperties, disableGeoip);\n    }\n    return response;\n  }\n  async getRemoteConfigPayload(flagKey) {\n    return (await this.featureFlagsPoller?._requestRemoteConfigPayload(flagKey))?.json();\n  }\n  async isFeatureEnabled(key, distinctId, options) {\n    const feat = await this.getFeatureFlag(key, distinctId, options);\n    if (feat === undefined) {\n      return undefined;\n    }\n    return !!feat || false;\n  }\n  async getAllFlags(distinctId, options) {\n    const response = await this.getAllFlagsAndPayloads(distinctId, options);\n    return response.featureFlags || {};\n  }\n  async getAllFlagsAndPayloads(distinctId, options) {\n    const {\n      groups,\n      disableGeoip\n    } = options || {};\n    let {\n      onlyEvaluateLocally,\n      personProperties,\n      groupProperties\n    } = options || {};\n    const adjustedProperties = this.addLocalPersonAndGroupProperties(distinctId, groups, personProperties, groupProperties);\n    personProperties = adjustedProperties.allPersonProperties;\n    groupProperties = adjustedProperties.allGroupProperties;\n    // set defaults\n    if (onlyEvaluateLocally == undefined) {\n      onlyEvaluateLocally = false;\n    }\n    const localEvaluationResult = await this.featureFlagsPoller?.getAllFlagsAndPayloads(distinctId, groups, personProperties, groupProperties);\n    let featureFlags = {};\n    let featureFlagPayloads = {};\n    let fallbackToDecide = true;\n    if (localEvaluationResult) {\n      featureFlags = localEvaluationResult.response;\n      featureFlagPayloads = localEvaluationResult.payloads;\n      fallbackToDecide = localEvaluationResult.fallbackToDecide;\n    }\n    if (fallbackToDecide && !onlyEvaluateLocally) {\n      const remoteEvaluationResult = await super.getFeatureFlagsAndPayloadsStateless(distinctId, groups, personProperties, groupProperties, disableGeoip);\n      featureFlags = {\n        ...featureFlags,\n        ...(remoteEvaluationResult.flags || {})\n      };\n      featureFlagPayloads = {\n        ...featureFlagPayloads,\n        ...(remoteEvaluationResult.payloads || {})\n      };\n    }\n    return {\n      featureFlags,\n      featureFlagPayloads\n    };\n  }\n  groupIdentify({\n    groupType,\n    groupKey,\n    properties,\n    distinctId,\n    disableGeoip\n  }) {\n    super.groupIdentifyStateless(groupType, groupKey, properties, {\n      disableGeoip\n    }, distinctId);\n  }\n  /**\r\n   * Reloads the feature flag definitions from the server for local evaluation.\r\n   * This is useful to call if you want to ensure that the feature flags are up to date before calling getFeatureFlag.\r\n   */\n  async reloadFeatureFlags() {\n    await this.featureFlagsPoller?.loadFeatureFlags(true);\n  }\n  async _shutdown(shutdownTimeoutMs) {\n    this.featureFlagsPoller?.stopPoller();\n    return super._shutdown(shutdownTimeoutMs);\n  }\n  addLocalPersonAndGroupProperties(distinctId, groups, personProperties, groupProperties) {\n    const allPersonProperties = {\n      distinct_id: distinctId,\n      ...(personProperties || {})\n    };\n    const allGroupProperties = {};\n    if (groups) {\n      for (const groupName of Object.keys(groups)) {\n        allGroupProperties[groupName] = {\n          $group_key: groups[groupName],\n          ...(groupProperties?.[groupName] || {})\n        };\n      }\n    }\n    return {\n      allPersonProperties,\n      allGroupProperties\n    };\n  }\n  captureException(error, distinctId, additionalProperties) {\n    const syntheticException = new Error('PostHog syntheticException');\n    ErrorTracking.captureException(this, error, {\n      syntheticException\n    }, distinctId, additionalProperties);\n  }\n}\n\n// Portions of this file are derived from getsentry/sentry-javascript by Software, Inc. dba Sentry\n// Licensed under the MIT License\n// This was originally forked from https://github.com/csnover/TraceKit, and was largely\n// re-written as part of raven - js.\n//\n// This code was later copied to the JavaScript mono - repo and further modified and\n// refactored over the years.\n// Copyright (c) 2013 Onur <NAME_EMAIL> and all TraceKit contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy of this\n// software and associated documentation files(the 'Software'), to deal in the Software\n// without restriction, including without limitation the rights to use, copy, modify,\n// merge, publish, distribute, sublicense, and / or sell copies of the Software, and to\n// permit persons to whom the Software is furnished to do so, subject to the following\n// conditions:\n//\n// The above copyright notice and this permission notice shall be included in all copies\n// or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED,\n// INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A\n// PARTICULAR PURPOSE AND NONINFRINGEMENT.IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT\n// HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF\n// CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE\n// OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\nconst WEBPACK_ERROR_REGEXP = /\\(error: (.*)\\)/;\nconst STACKTRACE_FRAME_LIMIT = 50;\nconst UNKNOWN_FUNCTION = '?';\n/** Node Stack line parser */\nfunction node(getModule) {\n  const FILENAME_MATCH = /^\\s*[-]{4,}$/;\n  const FULL_MATCH = /at (?:async )?(?:(.+?)\\s+\\()?(?:(.+):(\\d+):(\\d+)?|([^)]+))\\)?/;\n  return line => {\n    const lineMatch = line.match(FULL_MATCH);\n    if (lineMatch) {\n      let object;\n      let method;\n      let functionName;\n      let typeName;\n      let methodName;\n      if (lineMatch[1]) {\n        functionName = lineMatch[1];\n        let methodStart = functionName.lastIndexOf('.');\n        if (functionName[methodStart - 1] === '.') {\n          methodStart--;\n        }\n        if (methodStart > 0) {\n          object = functionName.slice(0, methodStart);\n          method = functionName.slice(methodStart + 1);\n          const objectEnd = object.indexOf('.Module');\n          if (objectEnd > 0) {\n            functionName = functionName.slice(objectEnd + 1);\n            object = object.slice(0, objectEnd);\n          }\n        }\n        typeName = undefined;\n      }\n      if (method) {\n        typeName = object;\n        methodName = method;\n      }\n      if (method === '<anonymous>') {\n        methodName = undefined;\n        functionName = undefined;\n      }\n      if (functionName === undefined) {\n        methodName = methodName || UNKNOWN_FUNCTION;\n        functionName = typeName ? `${typeName}.${methodName}` : methodName;\n      }\n      let filename = lineMatch[2]?.startsWith('file://') ? lineMatch[2].slice(7) : lineMatch[2];\n      const isNative = lineMatch[5] === 'native';\n      // If it's a Windows path, trim the leading slash so that `/C:/foo` becomes `C:/foo`\n      if (filename?.match(/\\/[A-Z]:/)) {\n        filename = filename.slice(1);\n      }\n      if (!filename && lineMatch[5] && !isNative) {\n        filename = lineMatch[5];\n      }\n      return {\n        filename: filename ? decodeURI(filename) : undefined,\n        module: getModule ? getModule(filename) : undefined,\n        function: functionName,\n        lineno: _parseIntOrUndefined(lineMatch[3]),\n        colno: _parseIntOrUndefined(lineMatch[4]),\n        in_app: filenameIsInApp(filename || '', isNative),\n        platform: 'node:javascript'\n      };\n    }\n    if (line.match(FILENAME_MATCH)) {\n      return {\n        filename: line,\n        platform: 'node:javascript'\n      };\n    }\n    return undefined;\n  };\n}\n/**\r\n * Does this filename look like it's part of the app code?\r\n */\nfunction filenameIsInApp(filename, isNative = false) {\n  const isInternal = isNative || filename &&\n  // It's not internal if it's an absolute linux path\n  !filename.startsWith('/') &&\n  // It's not internal if it's an absolute windows path\n  !filename.match(/^[A-Z]:/) &&\n  // It's not internal if the path is starting with a dot\n  !filename.startsWith('.') &&\n  // It's not internal if the frame has a protocol. In node, this is usually the case if the file got pre-processed with a bundler like webpack\n  !filename.match(/^[a-zA-Z]([a-zA-Z0-9.\\-+])*:\\/\\//); // Schema from: https://stackoverflow.com/a/3641782\n  // in_app is all that's not an internal Node function or a module within node_modules\n  // note that isNative appears to return true even for node core libraries\n  // see https://github.com/getsentry/raven-node/issues/176\n  return !isInternal && filename !== undefined && !filename.includes('node_modules/');\n}\nfunction _parseIntOrUndefined(input) {\n  return parseInt(input || '', 10) || undefined;\n}\nfunction nodeStackLineParser(getModule) {\n  return [90, node(getModule)];\n}\nfunction createStackParser(getModule) {\n  const parsers = [nodeStackLineParser(getModule)];\n  const sortedParsers = parsers.sort((a, b) => a[0] - b[0]).map(p => p[1]);\n  return (stack, skipFirstLines = 0) => {\n    const frames = [];\n    const lines = stack.split('\\n');\n    for (let i = skipFirstLines; i < lines.length; i++) {\n      const line = lines[i];\n      // Ignore lines over 1kb as they are unlikely to be stack frames.\n      if (line.length > 1024) {\n        continue;\n      }\n      // https://github.com/getsentry/sentry-javascript/issues/5459\n      // Remove webpack (error: *) wrappers\n      const cleanedLine = WEBPACK_ERROR_REGEXP.test(line) ? line.replace(WEBPACK_ERROR_REGEXP, '$1') : line;\n      // https://github.com/getsentry/sentry-javascript/issues/7813\n      // Skip Error: lines\n      if (cleanedLine.match(/\\S*Error: /)) {\n        continue;\n      }\n      for (const parser of sortedParsers) {\n        const frame = parser(cleanedLine);\n        if (frame) {\n          frames.push(frame);\n          break;\n        }\n      }\n      if (frames.length >= STACKTRACE_FRAME_LIMIT) {\n        break;\n      }\n    }\n    return reverseAndStripFrames(frames);\n  };\n}\nfunction reverseAndStripFrames(stack) {\n  if (!stack.length) {\n    return [];\n  }\n  const localStack = Array.from(stack);\n  localStack.reverse();\n  return localStack.slice(0, STACKTRACE_FRAME_LIMIT).map(frame => ({\n    ...frame,\n    filename: frame.filename || getLastStackFrame(localStack).filename,\n    function: frame.function || UNKNOWN_FUNCTION\n  }));\n}\nfunction getLastStackFrame(arr) {\n  return arr[arr.length - 1] || {};\n}\n\nErrorTracking.stackParser = createStackParser(createGetModuleFromFilename());\nErrorTracking.frameModifiers = [addSourceContext];\nclass PostHog extends PostHogBackendClient {\n  getLibraryId() {\n    return 'posthog-node';\n  }\n}\n\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/posthog-node@4.17.2/node_modules/posthog-node/lib/node/index.mjs\n");

/***/ })

};
;