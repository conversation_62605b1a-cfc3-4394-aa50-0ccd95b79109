{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@bufbuild+protobuf@1.10.1/node_modules/@bufbuild/protobuf/dist/esm/private/assert.js"], "sourcesContent": ["// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n/**\n * Assert that condition is truthy or throw error (with message)\n */\nexport function assert(condition, msg) {\n    // eslint-disable-next-line @typescript-eslint/strict-boolean-expressions -- we want the implicit conversion to boolean\n    if (!condition) {\n        throw new Error(msg);\n    }\n}\nconst FLOAT32_MAX = 3.4028234663852886e38, FLOAT32_MIN = -3.4028234663852886e38, UINT32_MAX = 0xffffffff, INT32_MAX = 0x7fffffff, INT32_MIN = -0x80000000;\n/**\n * Assert a valid signed protobuf 32-bit integer.\n */\nexport function assertInt32(arg) {\n    if (typeof arg !== \"number\")\n        throw new Error(\"invalid int 32: \" + typeof arg);\n    if (!Number.isInteger(arg) || arg > INT32_MAX || arg < INT32_MIN)\n        throw new Error(\"invalid int 32: \" + arg); // eslint-disable-line @typescript-eslint/restrict-plus-operands -- we want the implicit conversion to string\n}\n/**\n * Assert a valid unsigned protobuf 32-bit integer.\n */\nexport function assertUInt32(arg) {\n    if (typeof arg !== \"number\")\n        throw new Error(\"invalid uint 32: \" + typeof arg);\n    if (!Number.isInteger(arg) || arg > UINT32_MAX || arg < 0)\n        throw new Error(\"invalid uint 32: \" + arg); // eslint-disable-line @typescript-eslint/restrict-plus-operands -- we want the implicit conversion to string\n}\n/**\n * Assert a valid protobuf float value.\n */\nexport function assertFloat32(arg) {\n    if (typeof arg !== \"number\")\n        throw new Error(\"invalid float 32: \" + typeof arg);\n    if (!Number.isFinite(arg))\n        return;\n    if (arg > FLOAT32_MAX || arg < FLOAT32_MIN)\n        throw new Error(\"invalid float 32: \" + arg); // eslint-disable-line @typescript-eslint/restrict-plus-operands -- we want the implicit conversion to string\n}\n"], "names": [], "mappings": "AAAA,6CAA6C;AAC7C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC;;CAEC;;;;;;AACM,SAAS,OAAO,SAAS,EAAE,GAAG;IACjC,uHAAuH;IACvH,IAAI,CAAC,WAAW;QACZ,MAAM,IAAI,MAAM;IACpB;AACJ;AACA,MAAM,cAAc,uBAAuB,cAAc,CAAC,uBAAuB,aAAa,YAAY,YAAY,YAAY,YAAY,CAAC;AAIxI,SAAS,YAAY,GAAG;IAC3B,IAAI,OAAO,QAAQ,UACf,MAAM,IAAI,MAAM,qBAAqB,OAAO;IAChD,IAAI,CAAC,OAAO,SAAS,CAAC,QAAQ,MAAM,aAAa,MAAM,WACnD,MAAM,IAAI,MAAM,qBAAqB,MAAM,6GAA6G;AAChK;AAIO,SAAS,aAAa,GAAG;IAC5B,IAAI,OAAO,QAAQ,UACf,MAAM,IAAI,MAAM,sBAAsB,OAAO;IACjD,IAAI,CAAC,OAAO,SAAS,CAAC,QAAQ,MAAM,cAAc,MAAM,GACpD,MAAM,IAAI,MAAM,sBAAsB,MAAM,6GAA6G;AACjK;AAIO,SAAS,cAAc,GAAG;IAC7B,IAAI,OAAO,QAAQ,UACf,MAAM,IAAI,MAAM,uBAAuB,OAAO;IAClD,IAAI,CAAC,OAAO,QAAQ,CAAC,MACjB;IACJ,IAAI,MAAM,eAAe,MAAM,aAC3B,MAAM,IAAI,MAAM,uBAAuB,MAAM,6GAA6G;AAClK", "ignoreList": [0]}}, {"offset": {"line": 54, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@bufbuild+protobuf@1.10.1/node_modules/@bufbuild/protobuf/dist/esm/private/enum.js"], "sourcesContent": ["// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nimport { assert } from \"./assert.js\";\nconst enumTypeSymbol = Symbol(\"@bufbuild/protobuf/enum-type\");\n/**\n * Get reflection information from a generated enum.\n * If this function is called on something other than a generated\n * enum, it raises an error.\n */\nexport function getEnumType(enumObject) {\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access,@typescript-eslint/no-unsafe-assignment,@typescript-eslint/no-explicit-any\n    const t = enumObject[enumTypeSymbol];\n    assert(t, \"missing enum type on enum object\");\n    return t; // eslint-disable-line @typescript-eslint/no-unsafe-return\n}\n/**\n * Sets reflection information on a generated enum.\n */\nexport function setEnumType(enumObject, typeName, values, opt) {\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-explicit-any\n    enumObject[enumTypeSymbol] = makeEnumType(typeName, values.map((v) => ({\n        no: v.no,\n        name: v.name,\n        localName: enumObject[v.no],\n    })), opt);\n}\n/**\n * Create a new EnumType with the given values.\n */\nexport function makeEnumType(typeName, values, \n// eslint-disable-next-line @typescript-eslint/no-unused-vars\n_opt) {\n    const names = Object.create(null);\n    const numbers = Object.create(null);\n    const normalValues = [];\n    for (const value of values) {\n        // We do not surface options at this time\n        // const value: EnumValueInfo = {...v, options: v.options ?? emptyReadonlyObject};\n        const n = normalizeEnumValue(value);\n        normalValues.push(n);\n        names[value.name] = n;\n        numbers[value.no] = n;\n    }\n    return {\n        typeName,\n        values: normalValues,\n        // We do not surface options at this time\n        // options: opt?.options ?? Object.create(null),\n        findName(name) {\n            return names[name];\n        },\n        findNumber(no) {\n            return numbers[no];\n        },\n    };\n}\n/**\n * Create a new enum object with the given values.\n * Sets reflection information.\n */\nexport function makeEnum(typeName, values, opt) {\n    const enumObject = {};\n    for (const value of values) {\n        const n = normalizeEnumValue(value);\n        enumObject[n.localName] = n.no;\n        enumObject[n.no] = n.localName;\n    }\n    setEnumType(enumObject, typeName, values, opt);\n    return enumObject;\n}\nfunction normalizeEnumValue(value) {\n    if (\"localName\" in value) {\n        return value;\n    }\n    return Object.assign(Object.assign({}, value), { localName: value.name });\n}\n"], "names": [], "mappings": "AAAA,6CAA6C;AAC7C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;;;;;;AACjC;;AACA,MAAM,iBAAiB,OAAO;AAMvB,SAAS,YAAY,UAAU;IAClC,iJAAiJ;IACjJ,MAAM,IAAI,UAAU,CAAC,eAAe;IACpC,CAAA,GAAA,sPAAA,CAAA,SAAM,AAAD,EAAE,GAAG;IACV,OAAO,GAAG,0DAA0D;AACxE;AAIO,SAAS,YAAY,UAAU,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG;IACzD,0GAA0G;IAC1G,UAAU,CAAC,eAAe,GAAG,aAAa,UAAU,OAAO,GAAG,CAAC,CAAC,IAAM,CAAC;YACnE,IAAI,EAAE,EAAE;YACR,MAAM,EAAE,IAAI;YACZ,WAAW,UAAU,CAAC,EAAE,EAAE,CAAC;QAC/B,CAAC,IAAI;AACT;AAIO,SAAS,aAAa,QAAQ,EAAE,MAAM,EAC7C,6DAA6D;AAC7D,IAAI;IACA,MAAM,QAAQ,OAAO,MAAM,CAAC;IAC5B,MAAM,UAAU,OAAO,MAAM,CAAC;IAC9B,MAAM,eAAe,EAAE;IACvB,KAAK,MAAM,SAAS,OAAQ;QACxB,yCAAyC;QACzC,kFAAkF;QAClF,MAAM,IAAI,mBAAmB;QAC7B,aAAa,IAAI,CAAC;QAClB,KAAK,CAAC,MAAM,IAAI,CAAC,GAAG;QACpB,OAAO,CAAC,MAAM,EAAE,CAAC,GAAG;IACxB;IACA,OAAO;QACH;QACA,QAAQ;QACR,yCAAyC;QACzC,gDAAgD;QAChD,UAAS,IAAI;YACT,OAAO,KAAK,CAAC,KAAK;QACtB;QACA,YAAW,EAAE;YACT,OAAO,OAAO,CAAC,GAAG;QACtB;IACJ;AACJ;AAKO,SAAS,SAAS,QAAQ,EAAE,MAAM,EAAE,GAAG;IAC1C,MAAM,aAAa,CAAC;IACpB,KAAK,MAAM,SAAS,OAAQ;QACxB,MAAM,IAAI,mBAAmB;QAC7B,UAAU,CAAC,EAAE,SAAS,CAAC,GAAG,EAAE,EAAE;QAC9B,UAAU,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,SAAS;IAClC;IACA,YAAY,YAAY,UAAU,QAAQ;IAC1C,OAAO;AACX;AACA,SAAS,mBAAmB,KAAK;IAC7B,IAAI,eAAe,OAAO;QACtB,OAAO;IACX;IACA,OAAO,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,QAAQ;QAAE,WAAW,MAAM,IAAI;IAAC;AAC3E", "ignoreList": [0]}}, {"offset": {"line": 140, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@bufbuild+protobuf@1.10.1/node_modules/@bufbuild/protobuf/dist/esm/message.js"], "sourcesContent": ["// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n/**\n * Message is the base class of every message, generated, or created at\n * runtime.\n *\n * It is _not_ safe to extend this class. If you want to create a message at\n * run time, use proto3.makeMessageType().\n */\nexport class Message {\n    /**\n     * Compare with a message of the same type.\n     * Note that this function disregards extensions and unknown fields.\n     */\n    equals(other) {\n        return this.getType().runtime.util.equals(this.getType(), this, other);\n    }\n    /**\n     * Create a deep copy.\n     */\n    clone() {\n        return this.getType().runtime.util.clone(this);\n    }\n    /**\n     * Parse from binary data, merging fields.\n     *\n     * Repeated fields are appended. Map entries are added, overwriting\n     * existing keys.\n     *\n     * If a message field is already present, it will be merged with the\n     * new data.\n     */\n    fromBinary(bytes, options) {\n        const type = this.getType(), format = type.runtime.bin, opt = format.makeReadOptions(options);\n        format.readMessage(this, opt.readerFactory(bytes), bytes.byteLength, opt);\n        return this;\n    }\n    /**\n     * Parse a message from a JSON value.\n     */\n    fromJson(jsonValue, options) {\n        const type = this.getType(), format = type.runtime.json, opt = format.makeReadOptions(options);\n        format.readMessage(type, jsonValue, opt, this);\n        return this;\n    }\n    /**\n     * Parse a message from a JSON string.\n     */\n    fromJsonString(jsonString, options) {\n        let json;\n        try {\n            json = JSON.parse(jsonString);\n        }\n        catch (e) {\n            throw new Error(`cannot decode ${this.getType().typeName} from JSON: ${e instanceof Error ? e.message : String(e)}`);\n        }\n        return this.fromJson(json, options);\n    }\n    /**\n     * Serialize the message to binary data.\n     */\n    toBinary(options) {\n        const type = this.getType(), bin = type.runtime.bin, opt = bin.makeWriteOptions(options), writer = opt.writerFactory();\n        bin.writeMessage(this, writer, opt);\n        return writer.finish();\n    }\n    /**\n     * Serialize the message to a JSON value, a JavaScript value that can be\n     * passed to JSON.stringify().\n     */\n    toJson(options) {\n        const type = this.getType(), json = type.runtime.json, opt = json.makeWriteOptions(options);\n        return json.writeMessage(this, opt);\n    }\n    /**\n     * Serialize the message to a JSON string.\n     */\n    toJsonString(options) {\n        var _a;\n        const value = this.toJson(options);\n        return JSON.stringify(value, null, (_a = options === null || options === void 0 ? void 0 : options.prettySpaces) !== null && _a !== void 0 ? _a : 0);\n    }\n    /**\n     * Override for serialization behavior. This will be invoked when calling\n     * JSON.stringify on this message (i.e. JSON.stringify(msg)).\n     *\n     * Note that this will not serialize google.protobuf.Any with a packed\n     * message because the protobuf JSON format specifies that it needs to be\n     * unpacked, and this is only possible with a type registry to look up the\n     * message type.  As a result, attempting to serialize a message with this\n     * type will throw an Error.\n     *\n     * This method is protected because you should not need to invoke it\n     * directly -- instead use JSON.stringify or toJsonString for\n     * stringified JSON.  Alternatively, if actual JSON is desired, you should\n     * use toJson.\n     */\n    toJSON() {\n        return this.toJson({\n            emitDefaultValues: true,\n        });\n    }\n    /**\n     * Retrieve the MessageType of this message - a singleton that represents\n     * the protobuf message declaration and provides metadata for reflection-\n     * based operations.\n     */\n    getType() {\n        // Any class that extends Message _must_ provide a complete static\n        // implementation of MessageType.\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access,@typescript-eslint/no-unsafe-return\n        return Object.getPrototypeOf(this).constructor;\n    }\n}\n"], "names": [], "mappings": "AAAA,6CAA6C;AAC7C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC;;;;;;CAMC;;;AACM,MAAM;IACT;;;KAGC,GACD,OAAO,KAAK,EAAE;QACV,OAAO,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,EAAE;IACpE;IACA;;KAEC,GACD,QAAQ;QACJ,OAAO,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI;IACjD;IACA;;;;;;;;KAQC,GACD,WAAW,KAAK,EAAE,OAAO,EAAE;QACvB,MAAM,OAAO,IAAI,CAAC,OAAO,IAAI,SAAS,KAAK,OAAO,CAAC,GAAG,EAAE,MAAM,OAAO,eAAe,CAAC;QACrF,OAAO,WAAW,CAAC,IAAI,EAAE,IAAI,aAAa,CAAC,QAAQ,MAAM,UAAU,EAAE;QACrE,OAAO,IAAI;IACf;IACA;;KAEC,GACD,SAAS,SAAS,EAAE,OAAO,EAAE;QACzB,MAAM,OAAO,IAAI,CAAC,OAAO,IAAI,SAAS,KAAK,OAAO,CAAC,IAAI,EAAE,MAAM,OAAO,eAAe,CAAC;QACtF,OAAO,WAAW,CAAC,MAAM,WAAW,KAAK,IAAI;QAC7C,OAAO,IAAI;IACf;IACA;;KAEC,GACD,eAAe,UAAU,EAAE,OAAO,EAAE;QAChC,IAAI;QACJ,IAAI;YACA,OAAO,KAAK,KAAK,CAAC;QACtB,EACA,OAAO,GAAG;YACN,MAAM,IAAI,MAAM,CAAC,cAAc,EAAE,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC,YAAY,EAAE,aAAa,QAAQ,EAAE,OAAO,GAAG,OAAO,IAAI;QACvH;QACA,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM;IAC/B;IACA;;KAEC,GACD,SAAS,OAAO,EAAE;QACd,MAAM,OAAO,IAAI,CAAC,OAAO,IAAI,MAAM,KAAK,OAAO,CAAC,GAAG,EAAE,MAAM,IAAI,gBAAgB,CAAC,UAAU,SAAS,IAAI,aAAa;QACpH,IAAI,YAAY,CAAC,IAAI,EAAE,QAAQ;QAC/B,OAAO,OAAO,MAAM;IACxB;IACA;;;KAGC,GACD,OAAO,OAAO,EAAE;QACZ,MAAM,OAAO,IAAI,CAAC,OAAO,IAAI,OAAO,KAAK,OAAO,CAAC,IAAI,EAAE,MAAM,KAAK,gBAAgB,CAAC;QACnF,OAAO,KAAK,YAAY,CAAC,IAAI,EAAE;IACnC;IACA;;KAEC,GACD,aAAa,OAAO,EAAE;QAClB,IAAI;QACJ,MAAM,QAAQ,IAAI,CAAC,MAAM,CAAC;QAC1B,OAAO,KAAK,SAAS,CAAC,OAAO,MAAM,CAAC,KAAK,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,YAAY,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;IACtJ;IACA;;;;;;;;;;;;;;KAcC,GACD,SAAS;QACL,OAAO,IAAI,CAAC,MAAM,CAAC;YACf,mBAAmB;QACvB;IACJ;IACA;;;;KAIC,GACD,UAAU;QACN,kEAAkE;QAClE,iCAAiC;QACjC,0GAA0G;QAC1G,OAAO,OAAO,cAAc,CAAC,IAAI,EAAE,WAAW;IAClD;AACJ", "ignoreList": [0]}}, {"offset": {"line": 262, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@bufbuild+protobuf@1.10.1/node_modules/@bufbuild/protobuf/dist/esm/private/message-type.js"], "sourcesContent": ["// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nimport { Message } from \"../message.js\";\n/**\n * Create a new message type using the given runtime.\n */\nexport function makeMessageType(runtime, typeName, fields, opt) {\n    var _a;\n    const localName = (_a = opt === null || opt === void 0 ? void 0 : opt.localName) !== null && _a !== void 0 ? _a : typeName.substring(typeName.lastIndexOf(\".\") + 1);\n    const type = {\n        [localName]: function (data) {\n            runtime.util.initFields(this);\n            runtime.util.initPartial(data, this);\n        },\n    }[localName];\n    Object.setPrototypeOf(type.prototype, new Message());\n    Object.assign(type, {\n        runtime,\n        typeName,\n        fields: runtime.util.newFieldList(fields),\n        fromBinary(bytes, options) {\n            return new type().fromBinary(bytes, options);\n        },\n        fromJson(jsonValue, options) {\n            return new type().fromJson(jsonValue, options);\n        },\n        fromJsonString(jsonString, options) {\n            return new type().fromJsonString(jsonString, options);\n        },\n        equals(a, b) {\n            return runtime.util.equals(type, a, b);\n        },\n    });\n    return type;\n}\n"], "names": [], "mappings": "AAAA,6CAA6C;AAC7C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;;;AACjC;;AAIO,SAAS,gBAAgB,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG;IAC1D,IAAI;IACJ,MAAM,YAAY,CAAC,KAAK,QAAQ,QAAQ,QAAQ,KAAK,IAAI,KAAK,IAAI,IAAI,SAAS,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,SAAS,SAAS,CAAC,SAAS,WAAW,CAAC,OAAO;IACjK,MAAM,OAAO;QACT,CAAC,UAAU,EAAE,SAAU,IAAI;YACvB,QAAQ,IAAI,CAAC,UAAU,CAAC,IAAI;YAC5B,QAAQ,IAAI,CAAC,WAAW,CAAC,MAAM,IAAI;QACvC;IACJ,CAAC,CAAC,UAAU;IACZ,OAAO,cAAc,CAAC,KAAK,SAAS,EAAE,IAAI,4OAAA,CAAA,UAAO;IACjD,OAAO,MAAM,CAAC,MAAM;QAChB;QACA;QACA,QAAQ,QAAQ,IAAI,CAAC,YAAY,CAAC;QAClC,YAAW,KAAK,EAAE,OAAO;YACrB,OAAO,IAAI,OAAO,UAAU,CAAC,OAAO;QACxC;QACA,UAAS,SAAS,EAAE,OAAO;YACvB,OAAO,IAAI,OAAO,QAAQ,CAAC,WAAW;QAC1C;QACA,gBAAe,UAAU,EAAE,OAAO;YAC9B,OAAO,IAAI,OAAO,cAAc,CAAC,YAAY;QACjD;QACA,QAAO,CAAC,EAAE,CAAC;YACP,OAAO,QAAQ,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG;QACxC;IACJ;IACA,OAAO;AACX", "ignoreList": [0]}}, {"offset": {"line": 315, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@bufbuild+protobuf@1.10.1/node_modules/@bufbuild/protobuf/dist/esm/google/varint.js"], "sourcesContent": ["// Copyright 2008 Google Inc.  All rights reserved.\n//\n// Redistribution and use in source and binary forms, with or without\n// modification, are permitted provided that the following conditions are\n// met:\n//\n// * Redistributions of source code must retain the above copyright\n// notice, this list of conditions and the following disclaimer.\n// * Redistributions in binary form must reproduce the above\n// copyright notice, this list of conditions and the following disclaimer\n// in the documentation and/or other materials provided with the\n// distribution.\n// * Neither the name of Google Inc. nor the names of its\n// contributors may be used to endorse or promote products derived from\n// this software without specific prior written permission.\n//\n// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS\n// \"AS IS\" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT\n// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR\n// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT\n// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,\n// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT\n// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,\n// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY\n// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE\n// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n//\n// Code generated by the Protocol Buffer compiler is owned by the owner\n// of the input file used when generating it.  This code is not\n// standalone and requires a support library to be linked with it.  This\n// support library is itself covered by the above license.\n/* eslint-disable prefer-const,@typescript-eslint/restrict-plus-operands */\n/**\n * Read a 64 bit varint as two JS numbers.\n *\n * Returns tuple:\n * [0]: low bits\n * [1]: high bits\n *\n * Copyright 2008 Google Inc.  All rights reserved.\n *\n * See https://github.com/protocolbuffers/protobuf/blob/8a71927d74a4ce34efe2d8769fda198f52d20d12/js/experimental/runtime/kernel/buffer_decoder.js#L175\n */\nexport function varint64read() {\n    let lowBits = 0;\n    let highBits = 0;\n    for (let shift = 0; shift < 28; shift += 7) {\n        let b = this.buf[this.pos++];\n        lowBits |= (b & 0x7f) << shift;\n        if ((b & 0x80) == 0) {\n            this.assertBounds();\n            return [lowBits, highBits];\n        }\n    }\n    let middleByte = this.buf[this.pos++];\n    // last four bits of the first 32 bit number\n    lowBits |= (middleByte & 0x0f) << 28;\n    // 3 upper bits are part of the next 32 bit number\n    highBits = (middleByte & 0x70) >> 4;\n    if ((middleByte & 0x80) == 0) {\n        this.assertBounds();\n        return [lowBits, highBits];\n    }\n    for (let shift = 3; shift <= 31; shift += 7) {\n        let b = this.buf[this.pos++];\n        highBits |= (b & 0x7f) << shift;\n        if ((b & 0x80) == 0) {\n            this.assertBounds();\n            return [lowBits, highBits];\n        }\n    }\n    throw new Error(\"invalid varint\");\n}\n/**\n * Write a 64 bit varint, given as two JS numbers, to the given bytes array.\n *\n * Copyright 2008 Google Inc.  All rights reserved.\n *\n * See https://github.com/protocolbuffers/protobuf/blob/8a71927d74a4ce34efe2d8769fda198f52d20d12/js/experimental/runtime/kernel/writer.js#L344\n */\nexport function varint64write(lo, hi, bytes) {\n    for (let i = 0; i < 28; i = i + 7) {\n        const shift = lo >>> i;\n        const hasNext = !(shift >>> 7 == 0 && hi == 0);\n        const byte = (hasNext ? shift | 0x80 : shift) & 0xff;\n        bytes.push(byte);\n        if (!hasNext) {\n            return;\n        }\n    }\n    const splitBits = ((lo >>> 28) & 0x0f) | ((hi & 0x07) << 4);\n    const hasMoreBits = !(hi >> 3 == 0);\n    bytes.push((hasMoreBits ? splitBits | 0x80 : splitBits) & 0xff);\n    if (!hasMoreBits) {\n        return;\n    }\n    for (let i = 3; i < 31; i = i + 7) {\n        const shift = hi >>> i;\n        const hasNext = !(shift >>> 7 == 0);\n        const byte = (hasNext ? shift | 0x80 : shift) & 0xff;\n        bytes.push(byte);\n        if (!hasNext) {\n            return;\n        }\n    }\n    bytes.push((hi >>> 31) & 0x01);\n}\n// constants for binary math\nconst TWO_PWR_32_DBL = 0x100000000;\n/**\n * Parse decimal string of 64 bit integer value as two JS numbers.\n *\n * Copyright 2008 Google Inc.  All rights reserved.\n *\n * See https://github.com/protocolbuffers/protobuf-javascript/blob/a428c58273abad07c66071d9753bc4d1289de426/experimental/runtime/int64.js#L10\n */\nexport function int64FromString(dec) {\n    // Check for minus sign.\n    const minus = dec[0] === \"-\";\n    if (minus) {\n        dec = dec.slice(1);\n    }\n    // Work 6 decimal digits at a time, acting like we're converting base 1e6\n    // digits to binary. This is safe to do with floating point math because\n    // Number.isSafeInteger(ALL_32_BITS * 1e6) == true.\n    const base = 1e6;\n    let lowBits = 0;\n    let highBits = 0;\n    function add1e6digit(begin, end) {\n        // Note: Number('') is 0.\n        const digit1e6 = Number(dec.slice(begin, end));\n        highBits *= base;\n        lowBits = lowBits * base + digit1e6;\n        // Carry bits from lowBits to\n        if (lowBits >= TWO_PWR_32_DBL) {\n            highBits = highBits + ((lowBits / TWO_PWR_32_DBL) | 0);\n            lowBits = lowBits % TWO_PWR_32_DBL;\n        }\n    }\n    add1e6digit(-24, -18);\n    add1e6digit(-18, -12);\n    add1e6digit(-12, -6);\n    add1e6digit(-6);\n    return minus ? negate(lowBits, highBits) : newBits(lowBits, highBits);\n}\n/**\n * Losslessly converts a 64-bit signed integer in 32:32 split representation\n * into a decimal string.\n *\n * Copyright 2008 Google Inc.  All rights reserved.\n *\n * See https://github.com/protocolbuffers/protobuf-javascript/blob/a428c58273abad07c66071d9753bc4d1289de426/experimental/runtime/int64.js#L10\n */\nexport function int64ToString(lo, hi) {\n    let bits = newBits(lo, hi);\n    // If we're treating the input as a signed value and the high bit is set, do\n    // a manual two's complement conversion before the decimal conversion.\n    const negative = (bits.hi & 0x80000000);\n    if (negative) {\n        bits = negate(bits.lo, bits.hi);\n    }\n    const result = uInt64ToString(bits.lo, bits.hi);\n    return negative ? \"-\" + result : result;\n}\n/**\n * Losslessly converts a 64-bit unsigned integer in 32:32 split representation\n * into a decimal string.\n *\n * Copyright 2008 Google Inc.  All rights reserved.\n *\n * See https://github.com/protocolbuffers/protobuf-javascript/blob/a428c58273abad07c66071d9753bc4d1289de426/experimental/runtime/int64.js#L10\n */\nexport function uInt64ToString(lo, hi) {\n    ({ lo, hi } = toUnsigned(lo, hi));\n    // Skip the expensive conversion if the number is small enough to use the\n    // built-in conversions.\n    // Number.MAX_SAFE_INTEGER = 0x001FFFFF FFFFFFFF, thus any number with\n    // highBits <= 0x1FFFFF can be safely expressed with a double and retain\n    // integer precision.\n    // Proven by: Number.isSafeInteger(0x1FFFFF * 2**32 + 0xFFFFFFFF) == true.\n    if (hi <= 0x1FFFFF) {\n        return String(TWO_PWR_32_DBL * hi + lo);\n    }\n    // What this code is doing is essentially converting the input number from\n    // base-2 to base-1e7, which allows us to represent the 64-bit range with\n    // only 3 (very large) digits. Those digits are then trivial to convert to\n    // a base-10 string.\n    // The magic numbers used here are -\n    // 2^24 = 16777216 = (1,6777216) in base-1e7.\n    // 2^48 = 281474976710656 = (2,8147497,6710656) in base-1e7.\n    // Split 32:32 representation into 16:24:24 representation so our\n    // intermediate digits don't overflow.\n    const low = lo & 0xFFFFFF;\n    const mid = ((lo >>> 24) | (hi << 8)) & 0xFFFFFF;\n    const high = (hi >> 16) & 0xFFFF;\n    // Assemble our three base-1e7 digits, ignoring carries. The maximum\n    // value in a digit at this step is representable as a 48-bit integer, which\n    // can be stored in a 64-bit floating point number.\n    let digitA = low + (mid * 6777216) + (high * 6710656);\n    let digitB = mid + (high * 8147497);\n    let digitC = (high * 2);\n    // Apply carries from A to B and from B to C.\n    const base = 10000000;\n    if (digitA >= base) {\n        digitB += Math.floor(digitA / base);\n        digitA %= base;\n    }\n    if (digitB >= base) {\n        digitC += Math.floor(digitB / base);\n        digitB %= base;\n    }\n    // If digitC is 0, then we should have returned in the trivial code path\n    // at the top for non-safe integers. Given this, we can assume both digitB\n    // and digitA need leading zeros.\n    return digitC.toString() + decimalFrom1e7WithLeadingZeros(digitB) +\n        decimalFrom1e7WithLeadingZeros(digitA);\n}\nfunction toUnsigned(lo, hi) {\n    return { lo: lo >>> 0, hi: hi >>> 0 };\n}\nfunction newBits(lo, hi) {\n    return { lo: lo | 0, hi: hi | 0 };\n}\n/**\n * Returns two's compliment negation of input.\n * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Operators/Bitwise_Operators#Signed_32-bit_integers\n */\nfunction negate(lowBits, highBits) {\n    highBits = ~highBits;\n    if (lowBits) {\n        lowBits = ~lowBits + 1;\n    }\n    else {\n        // If lowBits is 0, then bitwise-not is 0xFFFFFFFF,\n        // adding 1 to that, results in 0x100000000, which leaves\n        // the low bits 0x0 and simply adds one to the high bits.\n        highBits += 1;\n    }\n    return newBits(lowBits, highBits);\n}\n/**\n * Returns decimal representation of digit1e7 with leading zeros.\n */\nconst decimalFrom1e7WithLeadingZeros = (digit1e7) => {\n    const partial = String(digit1e7);\n    return \"0000000\".slice(partial.length) + partial;\n};\n/**\n * Write a 32 bit varint, signed or unsigned. Same as `varint64write(0, value, bytes)`\n *\n * Copyright 2008 Google Inc.  All rights reserved.\n *\n * See https://github.com/protocolbuffers/protobuf/blob/1b18833f4f2a2f681f4e4a25cdf3b0a43115ec26/js/binary/encoder.js#L144\n */\nexport function varint32write(value, bytes) {\n    if (value >= 0) {\n        // write value as varint 32\n        while (value > 0x7f) {\n            bytes.push((value & 0x7f) | 0x80);\n            value = value >>> 7;\n        }\n        bytes.push(value);\n    }\n    else {\n        for (let i = 0; i < 9; i++) {\n            bytes.push((value & 127) | 128);\n            value = value >> 7;\n        }\n        bytes.push(1);\n    }\n}\n/**\n * Read an unsigned 32 bit varint.\n *\n * See https://github.com/protocolbuffers/protobuf/blob/8a71927d74a4ce34efe2d8769fda198f52d20d12/js/experimental/runtime/kernel/buffer_decoder.js#L220\n */\nexport function varint32read() {\n    let b = this.buf[this.pos++];\n    let result = b & 0x7f;\n    if ((b & 0x80) == 0) {\n        this.assertBounds();\n        return result;\n    }\n    b = this.buf[this.pos++];\n    result |= (b & 0x7f) << 7;\n    if ((b & 0x80) == 0) {\n        this.assertBounds();\n        return result;\n    }\n    b = this.buf[this.pos++];\n    result |= (b & 0x7f) << 14;\n    if ((b & 0x80) == 0) {\n        this.assertBounds();\n        return result;\n    }\n    b = this.buf[this.pos++];\n    result |= (b & 0x7f) << 21;\n    if ((b & 0x80) == 0) {\n        this.assertBounds();\n        return result;\n    }\n    // Extract only last 4 bits\n    b = this.buf[this.pos++];\n    result |= (b & 0x0f) << 28;\n    for (let readBytes = 5; (b & 0x80) !== 0 && readBytes < 10; readBytes++)\n        b = this.buf[this.pos++];\n    if ((b & 0x80) != 0)\n        throw new Error(\"invalid varint\");\n    this.assertBounds();\n    // Result can have 32 bits, convert it to unsigned\n    return result >>> 0;\n}\n"], "names": [], "mappings": "AAAA,mDAAmD;AACnD,EAAE;AACF,qEAAqE;AACrE,yEAAyE;AACzE,OAAO;AACP,EAAE;AACF,mEAAmE;AACnE,gEAAgE;AAChE,4DAA4D;AAC5D,yEAAyE;AACzE,gEAAgE;AAChE,gBAAgB;AAChB,yDAAyD;AACzD,uEAAuE;AACvE,2DAA2D;AAC3D,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,wEAAwE;AACxE,uEAAuE;AACvE,wEAAwE;AACxE,mEAAmE;AACnE,wEAAwE;AACxE,wEAAwE;AACxE,sEAAsE;AACtE,wEAAwE;AACxE,uEAAuE;AACvE,EAAE;AACF,uEAAuE;AACvE,+DAA+D;AAC/D,wEAAwE;AACxE,0DAA0D;AAC1D,yEAAyE,GACzE;;;;;;;;;;CAUC;;;;;;;;;AACM,SAAS;IACZ,IAAI,UAAU;IACd,IAAI,WAAW;IACf,IAAK,IAAI,QAAQ,GAAG,QAAQ,IAAI,SAAS,EAAG;QACxC,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG;QAC5B,WAAW,CAAC,IAAI,IAAI,KAAK;QACzB,IAAI,CAAC,IAAI,IAAI,KAAK,GAAG;YACjB,IAAI,CAAC,YAAY;YACjB,OAAO;gBAAC;gBAAS;aAAS;QAC9B;IACJ;IACA,IAAI,aAAa,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG;IACrC,4CAA4C;IAC5C,WAAW,CAAC,aAAa,IAAI,KAAK;IAClC,kDAAkD;IAClD,WAAW,CAAC,aAAa,IAAI,KAAK;IAClC,IAAI,CAAC,aAAa,IAAI,KAAK,GAAG;QAC1B,IAAI,CAAC,YAAY;QACjB,OAAO;YAAC;YAAS;SAAS;IAC9B;IACA,IAAK,IAAI,QAAQ,GAAG,SAAS,IAAI,SAAS,EAAG;QACzC,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG;QAC5B,YAAY,CAAC,IAAI,IAAI,KAAK;QAC1B,IAAI,CAAC,IAAI,IAAI,KAAK,GAAG;YACjB,IAAI,CAAC,YAAY;YACjB,OAAO;gBAAC;gBAAS;aAAS;QAC9B;IACJ;IACA,MAAM,IAAI,MAAM;AACpB;AAQO,SAAS,cAAc,EAAE,EAAE,EAAE,EAAE,KAAK;IACvC,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI,IAAI,EAAG;QAC/B,MAAM,QAAQ,OAAO;QACrB,MAAM,UAAU,CAAC,CAAC,UAAU,KAAK,KAAK,MAAM,CAAC;QAC7C,MAAM,OAAO,CAAC,UAAU,QAAQ,OAAO,KAAK,IAAI;QAChD,MAAM,IAAI,CAAC;QACX,IAAI,CAAC,SAAS;YACV;QACJ;IACJ;IACA,MAAM,YAAY,AAAE,OAAO,KAAM,OAAS,CAAC,KAAK,IAAI,KAAK;IACzD,MAAM,cAAc,CAAC,CAAC,MAAM,KAAK,CAAC;IAClC,MAAM,IAAI,CAAC,CAAC,cAAc,YAAY,OAAO,SAAS,IAAI;IAC1D,IAAI,CAAC,aAAa;QACd;IACJ;IACA,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI,IAAI,EAAG;QAC/B,MAAM,QAAQ,OAAO;QACrB,MAAM,UAAU,CAAC,CAAC,UAAU,KAAK,CAAC;QAClC,MAAM,OAAO,CAAC,UAAU,QAAQ,OAAO,KAAK,IAAI;QAChD,MAAM,IAAI,CAAC;QACX,IAAI,CAAC,SAAS;YACV;QACJ;IACJ;IACA,MAAM,IAAI,CAAC,AAAC,OAAO,KAAM;AAC7B;AACA,4BAA4B;AAC5B,MAAM,iBAAiB;AAQhB,SAAS,gBAAgB,GAAG;IAC/B,wBAAwB;IACxB,MAAM,QAAQ,GAAG,CAAC,EAAE,KAAK;IACzB,IAAI,OAAO;QACP,MAAM,IAAI,KAAK,CAAC;IACpB;IACA,yEAAyE;IACzE,wEAAwE;IACxE,mDAAmD;IACnD,MAAM,OAAO;IACb,IAAI,UAAU;IACd,IAAI,WAAW;IACf,SAAS,YAAY,KAAK,EAAE,GAAG;QAC3B,yBAAyB;QACzB,MAAM,WAAW,OAAO,IAAI,KAAK,CAAC,OAAO;QACzC,YAAY;QACZ,UAAU,UAAU,OAAO;QAC3B,6BAA6B;QAC7B,IAAI,WAAW,gBAAgB;YAC3B,WAAW,WAAW,CAAC,AAAC,UAAU,iBAAkB,CAAC;YACrD,UAAU,UAAU;QACxB;IACJ;IACA,YAAY,CAAC,IAAI,CAAC;IAClB,YAAY,CAAC,IAAI,CAAC;IAClB,YAAY,CAAC,IAAI,CAAC;IAClB,YAAY,CAAC;IACb,OAAO,QAAQ,OAAO,SAAS,YAAY,QAAQ,SAAS;AAChE;AASO,SAAS,cAAc,EAAE,EAAE,EAAE;IAChC,IAAI,OAAO,QAAQ,IAAI;IACvB,4EAA4E;IAC5E,sEAAsE;IACtE,MAAM,WAAY,KAAK,EAAE,GAAG;IAC5B,IAAI,UAAU;QACV,OAAO,OAAO,KAAK,EAAE,EAAE,KAAK,EAAE;IAClC;IACA,MAAM,SAAS,eAAe,KAAK,EAAE,EAAE,KAAK,EAAE;IAC9C,OAAO,WAAW,MAAM,SAAS;AACrC;AASO,SAAS,eAAe,EAAE,EAAE,EAAE;IACjC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,WAAW,IAAI,GAAG;IAChC,yEAAyE;IACzE,wBAAwB;IACxB,sEAAsE;IACtE,wEAAwE;IACxE,qBAAqB;IACrB,0EAA0E;IAC1E,IAAI,MAAM,UAAU;QAChB,OAAO,OAAO,iBAAiB,KAAK;IACxC;IACA,0EAA0E;IAC1E,yEAAyE;IACzE,0EAA0E;IAC1E,oBAAoB;IACpB,oCAAoC;IACpC,6CAA6C;IAC7C,4DAA4D;IAC5D,iEAAiE;IACjE,sCAAsC;IACtC,MAAM,MAAM,KAAK;IACjB,MAAM,MAAM,CAAC,AAAC,OAAO,KAAO,MAAM,CAAE,IAAI;IACxC,MAAM,OAAO,AAAC,MAAM,KAAM;IAC1B,oEAAoE;IACpE,4EAA4E;IAC5E,mDAAmD;IACnD,IAAI,SAAS,MAAO,MAAM,UAAY,OAAO;IAC7C,IAAI,SAAS,MAAO,OAAO;IAC3B,IAAI,SAAU,OAAO;IACrB,6CAA6C;IAC7C,MAAM,OAAO;IACb,IAAI,UAAU,MAAM;QAChB,UAAU,KAAK,KAAK,CAAC,SAAS;QAC9B,UAAU;IACd;IACA,IAAI,UAAU,MAAM;QAChB,UAAU,KAAK,KAAK,CAAC,SAAS;QAC9B,UAAU;IACd;IACA,wEAAwE;IACxE,0EAA0E;IAC1E,iCAAiC;IACjC,OAAO,OAAO,QAAQ,KAAK,+BAA+B,UACtD,+BAA+B;AACvC;AACA,SAAS,WAAW,EAAE,EAAE,EAAE;IACtB,OAAO;QAAE,IAAI,OAAO;QAAG,IAAI,OAAO;IAAE;AACxC;AACA,SAAS,QAAQ,EAAE,EAAE,EAAE;IACnB,OAAO;QAAE,IAAI,KAAK;QAAG,IAAI,KAAK;IAAE;AACpC;AACA;;;CAGC,GACD,SAAS,OAAO,OAAO,EAAE,QAAQ;IAC7B,WAAW,CAAC;IACZ,IAAI,SAAS;QACT,UAAU,CAAC,UAAU;IACzB,OACK;QACD,mDAAmD;QACnD,yDAAyD;QACzD,yDAAyD;QACzD,YAAY;IAChB;IACA,OAAO,QAAQ,SAAS;AAC5B;AACA;;CAEC,GACD,MAAM,iCAAiC,CAAC;IACpC,MAAM,UAAU,OAAO;IACvB,OAAO,UAAU,KAAK,CAAC,QAAQ,MAAM,IAAI;AAC7C;AAQO,SAAS,cAAc,KAAK,EAAE,KAAK;IACtC,IAAI,SAAS,GAAG;QACZ,2BAA2B;QAC3B,MAAO,QAAQ,KAAM;YACjB,MAAM,IAAI,CAAC,AAAC,QAAQ,OAAQ;YAC5B,QAAQ,UAAU;QACtB;QACA,MAAM,IAAI,CAAC;IACf,OACK;QACD,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;YACxB,MAAM,IAAI,CAAC,AAAC,QAAQ,MAAO;YAC3B,QAAQ,SAAS;QACrB;QACA,MAAM,IAAI,CAAC;IACf;AACJ;AAMO,SAAS;IACZ,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG;IAC5B,IAAI,SAAS,IAAI;IACjB,IAAI,CAAC,IAAI,IAAI,KAAK,GAAG;QACjB,IAAI,CAAC,YAAY;QACjB,OAAO;IACX;IACA,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG;IACxB,UAAU,CAAC,IAAI,IAAI,KAAK;IACxB,IAAI,CAAC,IAAI,IAAI,KAAK,GAAG;QACjB,IAAI,CAAC,YAAY;QACjB,OAAO;IACX;IACA,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG;IACxB,UAAU,CAAC,IAAI,IAAI,KAAK;IACxB,IAAI,CAAC,IAAI,IAAI,KAAK,GAAG;QACjB,IAAI,CAAC,YAAY;QACjB,OAAO;IACX;IACA,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG;IACxB,UAAU,CAAC,IAAI,IAAI,KAAK;IACxB,IAAI,CAAC,IAAI,IAAI,KAAK,GAAG;QACjB,IAAI,CAAC,YAAY;QACjB,OAAO;IACX;IACA,2BAA2B;IAC3B,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG;IACxB,UAAU,CAAC,IAAI,IAAI,KAAK;IACxB,IAAK,IAAI,YAAY,GAAG,CAAC,IAAI,IAAI,MAAM,KAAK,YAAY,IAAI,YACxD,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG;IAC5B,IAAI,CAAC,IAAI,IAAI,KAAK,GACd,MAAM,IAAI,MAAM;IACpB,IAAI,CAAC,YAAY;IACjB,kDAAkD;IAClD,OAAO,WAAW;AACtB", "ignoreList": [0]}}, {"offset": {"line": 607, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@bufbuild+protobuf@1.10.1/node_modules/@bufbuild/protobuf/dist/esm/proto-int64.js"], "sourcesContent": ["// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nimport { assert } from \"./private/assert.js\";\nimport { int64FromString, int64ToString, uInt64ToString, } from \"./google/varint.js\";\nfunction makeInt64Support() {\n    const dv = new DataView(new ArrayBuffer(8));\n    // note that Safari 14 implements BigInt, but not the DataView methods\n    const ok = typeof BigInt === \"function\" &&\n        typeof dv.getBigInt64 === \"function\" &&\n        typeof dv.getBigUint64 === \"function\" &&\n        typeof dv.setBigInt64 === \"function\" &&\n        typeof dv.setBigUint64 === \"function\" &&\n        (typeof process != \"object\" ||\n            typeof process.env != \"object\" ||\n            process.env.BUF_BIGINT_DISABLE !== \"1\");\n    if (ok) {\n        const MIN = BigInt(\"-9223372036854775808\"), MAX = BigInt(\"9223372036854775807\"), UMIN = BigInt(\"0\"), UMAX = BigInt(\"18446744073709551615\");\n        return {\n            zero: BigInt(0),\n            supported: true,\n            parse(value) {\n                const bi = typeof value == \"bigint\" ? value : BigInt(value);\n                if (bi > MAX || bi < MIN) {\n                    throw new Error(`int64 invalid: ${value}`);\n                }\n                return bi;\n            },\n            uParse(value) {\n                const bi = typeof value == \"bigint\" ? value : BigInt(value);\n                if (bi > UMAX || bi < UMIN) {\n                    throw new Error(`uint64 invalid: ${value}`);\n                }\n                return bi;\n            },\n            enc(value) {\n                dv.setBigInt64(0, this.parse(value), true);\n                return {\n                    lo: dv.getInt32(0, true),\n                    hi: dv.getInt32(4, true),\n                };\n            },\n            uEnc(value) {\n                dv.setBigInt64(0, this.uParse(value), true);\n                return {\n                    lo: dv.getInt32(0, true),\n                    hi: dv.getInt32(4, true),\n                };\n            },\n            dec(lo, hi) {\n                dv.setInt32(0, lo, true);\n                dv.setInt32(4, hi, true);\n                return dv.getBigInt64(0, true);\n            },\n            uDec(lo, hi) {\n                dv.setInt32(0, lo, true);\n                dv.setInt32(4, hi, true);\n                return dv.getBigUint64(0, true);\n            },\n        };\n    }\n    const assertInt64String = (value) => assert(/^-?[0-9]+$/.test(value), `int64 invalid: ${value}`);\n    const assertUInt64String = (value) => assert(/^[0-9]+$/.test(value), `uint64 invalid: ${value}`);\n    return {\n        zero: \"0\",\n        supported: false,\n        parse(value) {\n            if (typeof value != \"string\") {\n                value = value.toString();\n            }\n            assertInt64String(value);\n            return value;\n        },\n        uParse(value) {\n            if (typeof value != \"string\") {\n                value = value.toString();\n            }\n            assertUInt64String(value);\n            return value;\n        },\n        enc(value) {\n            if (typeof value != \"string\") {\n                value = value.toString();\n            }\n            assertInt64String(value);\n            return int64FromString(value);\n        },\n        uEnc(value) {\n            if (typeof value != \"string\") {\n                value = value.toString();\n            }\n            assertUInt64String(value);\n            return int64FromString(value);\n        },\n        dec(lo, hi) {\n            return int64ToString(lo, hi);\n        },\n        uDec(lo, hi) {\n            return uInt64ToString(lo, hi);\n        },\n    };\n}\nexport const protoInt64 = makeInt64Support();\n"], "names": [], "mappings": "AAAA,6CAA6C;AAC7C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;;;AACjC;AACA;;;AACA,SAAS;IACL,MAAM,KAAK,IAAI,SAAS,IAAI,YAAY;IACxC,sEAAsE;IACtE,MAAM,KAAK,OAAO,WAAW,cACzB,OAAO,GAAG,WAAW,KAAK,cAC1B,OAAO,GAAG,YAAY,KAAK,cAC3B,OAAO,GAAG,WAAW,KAAK,cAC1B,OAAO,GAAG,YAAY,KAAK,cAC3B,CAAC,OAAO,WAAW,YACf,OAAO,QAAQ,GAAG,IAAI,YACtB,QAAQ,GAAG,CAAC,kBAAkB,KAAK,GAAG;IAC9C,IAAI,IAAI;QACJ,MAAM,MAAM,OAAO,yBAAyB,MAAM,OAAO,wBAAwB,OAAO,OAAO,MAAM,OAAO,OAAO;QACnH,OAAO;YACH,MAAM,OAAO;YACb,WAAW;YACX,OAAM,KAAK;gBACP,MAAM,KAAK,OAAO,SAAS,WAAW,QAAQ,OAAO;gBACrD,IAAI,KAAK,OAAO,KAAK,KAAK;oBACtB,MAAM,IAAI,MAAM,CAAC,eAAe,EAAE,OAAO;gBAC7C;gBACA,OAAO;YACX;YACA,QAAO,KAAK;gBACR,MAAM,KAAK,OAAO,SAAS,WAAW,QAAQ,OAAO;gBACrD,IAAI,KAAK,QAAQ,KAAK,MAAM;oBACxB,MAAM,IAAI,MAAM,CAAC,gBAAgB,EAAE,OAAO;gBAC9C;gBACA,OAAO;YACX;YACA,KAAI,KAAK;gBACL,GAAG,WAAW,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ;gBACrC,OAAO;oBACH,IAAI,GAAG,QAAQ,CAAC,GAAG;oBACnB,IAAI,GAAG,QAAQ,CAAC,GAAG;gBACvB;YACJ;YACA,MAAK,KAAK;gBACN,GAAG,WAAW,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ;gBACtC,OAAO;oBACH,IAAI,GAAG,QAAQ,CAAC,GAAG;oBACnB,IAAI,GAAG,QAAQ,CAAC,GAAG;gBACvB;YACJ;YACA,KAAI,EAAE,EAAE,EAAE;gBACN,GAAG,QAAQ,CAAC,GAAG,IAAI;gBACnB,GAAG,QAAQ,CAAC,GAAG,IAAI;gBACnB,OAAO,GAAG,WAAW,CAAC,GAAG;YAC7B;YACA,MAAK,EAAE,EAAE,EAAE;gBACP,GAAG,QAAQ,CAAC,GAAG,IAAI;gBACnB,GAAG,QAAQ,CAAC,GAAG,IAAI;gBACnB,OAAO,GAAG,YAAY,CAAC,GAAG;YAC9B;QACJ;IACJ;IACA,MAAM,oBAAoB,CAAC,QAAU,CAAA,GAAA,sPAAA,CAAA,SAAM,AAAD,EAAE,aAAa,IAAI,CAAC,QAAQ,CAAC,eAAe,EAAE,OAAO;IAC/F,MAAM,qBAAqB,CAAC,QAAU,CAAA,GAAA,sPAAA,CAAA,SAAM,AAAD,EAAE,WAAW,IAAI,CAAC,QAAQ,CAAC,gBAAgB,EAAE,OAAO;IAC/F,OAAO;QACH,MAAM;QACN,WAAW;QACX,OAAM,KAAK;YACP,IAAI,OAAO,SAAS,UAAU;gBAC1B,QAAQ,MAAM,QAAQ;YAC1B;YACA,kBAAkB;YAClB,OAAO;QACX;QACA,QAAO,KAAK;YACR,IAAI,OAAO,SAAS,UAAU;gBAC1B,QAAQ,MAAM,QAAQ;YAC1B;YACA,mBAAmB;YACnB,OAAO;QACX;QACA,KAAI,KAAK;YACL,IAAI,OAAO,SAAS,UAAU;gBAC1B,QAAQ,MAAM,QAAQ;YAC1B;YACA,kBAAkB;YAClB,OAAO,CAAA,GAAA,qPAAA,CAAA,kBAAe,AAAD,EAAE;QAC3B;QACA,MAAK,KAAK;YACN,IAAI,OAAO,SAAS,UAAU;gBAC1B,QAAQ,MAAM,QAAQ;YAC1B;YACA,mBAAmB;YACnB,OAAO,CAAA,GAAA,qPAAA,CAAA,kBAAe,AAAD,EAAE;QAC3B;QACA,KAAI,EAAE,EAAE,EAAE;YACN,OAAO,CAAA,GAAA,qPAAA,CAAA,gBAAa,AAAD,EAAE,IAAI;QAC7B;QACA,MAAK,EAAE,EAAE,EAAE;YACP,OAAO,CAAA,GAAA,qPAAA,CAAA,iBAAc,AAAD,EAAE,IAAI;QAC9B;IACJ;AACJ;AACO,MAAM,aAAa", "ignoreList": [0]}}, {"offset": {"line": 724, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@bufbuild+protobuf@1.10.1/node_modules/@bufbuild/protobuf/dist/esm/scalar.js"], "sourcesContent": ["// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n/**\n * Scalar value types. This is a subset of field types declared by protobuf\n * enum google.protobuf.FieldDescriptorProto.Type The types GROUP and MESSAGE\n * are omitted, but the numerical values are identical.\n */\nexport var ScalarType;\n(function (ScalarType) {\n    // 0 is reserved for errors.\n    // Order is weird for historical reasons.\n    ScalarType[ScalarType[\"DOUBLE\"] = 1] = \"DOUBLE\";\n    ScalarType[ScalarType[\"FLOAT\"] = 2] = \"FLOAT\";\n    // Not ZigZag encoded.  Negative numbers take 10 bytes.  Use TYPE_SINT64 if\n    // negative values are likely.\n    ScalarType[ScalarType[\"INT64\"] = 3] = \"INT64\";\n    ScalarType[ScalarType[\"UINT64\"] = 4] = \"UINT64\";\n    // Not ZigZag encoded.  Negative numbers take 10 bytes.  Use TYPE_SINT32 if\n    // negative values are likely.\n    ScalarType[ScalarType[\"INT32\"] = 5] = \"INT32\";\n    ScalarType[ScalarType[\"FIXED64\"] = 6] = \"FIXED64\";\n    ScalarType[ScalarType[\"FIXED32\"] = 7] = \"FIXED32\";\n    ScalarType[ScalarType[\"BOOL\"] = 8] = \"BOOL\";\n    ScalarType[ScalarType[\"STRING\"] = 9] = \"STRING\";\n    // Tag-delimited aggregate.\n    // Group type is deprecated and not supported in proto3. However, Proto3\n    // implementations should still be able to parse the group wire format and\n    // treat group fields as unknown fields.\n    // TYPE_GROUP = 10,\n    // TYPE_MESSAGE = 11,  // Length-delimited aggregate.\n    // New in version 2.\n    ScalarType[ScalarType[\"BYTES\"] = 12] = \"BYTES\";\n    ScalarType[ScalarType[\"UINT32\"] = 13] = \"UINT32\";\n    // TYPE_ENUM = 14,\n    ScalarType[ScalarType[\"SFIXED32\"] = 15] = \"SFIXED32\";\n    ScalarType[ScalarType[\"SFIXED64\"] = 16] = \"SFIXED64\";\n    ScalarType[ScalarType[\"SINT32\"] = 17] = \"SINT32\";\n    ScalarType[ScalarType[\"SINT64\"] = 18] = \"SINT64\";\n})(ScalarType || (ScalarType = {}));\n/**\n * JavaScript representation of fields with 64 bit integral types (int64, uint64,\n * sint64, fixed64, sfixed64).\n *\n * This is a subset of google.protobuf.FieldOptions.JSType, which defines JS_NORMAL,\n * JS_STRING, and JS_NUMBER. Protobuf-ES uses BigInt by default, but will use\n * String if `[jstype = JS_STRING]` is specified.\n *\n * ```protobuf\n * uint64 field_a = 1; // BigInt\n * uint64 field_b = 2 [jstype = JS_NORMAL]; // BigInt\n * uint64 field_b = 2 [jstype = JS_NUMBER]; // BigInt\n * uint64 field_b = 2 [jstype = JS_STRING]; // String\n * ```\n */\nexport var LongType;\n(function (LongType) {\n    /**\n     * Use JavaScript BigInt.\n     */\n    LongType[LongType[\"BIGINT\"] = 0] = \"BIGINT\";\n    /**\n     * Use JavaScript String.\n     *\n     * Field option `[jstype = JS_STRING]`.\n     */\n    LongType[LongType[\"STRING\"] = 1] = \"STRING\";\n})(LongType || (LongType = {}));\n"], "names": [], "mappings": "AAAA,6CAA6C;AAC7C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC;;;;CAIC;;;;AACM,IAAI;AACX,CAAC,SAAU,UAAU;IACjB,4BAA4B;IAC5B,yCAAyC;IACzC,UAAU,CAAC,UAAU,CAAC,SAAS,GAAG,EAAE,GAAG;IACvC,UAAU,CAAC,UAAU,CAAC,QAAQ,GAAG,EAAE,GAAG;IACtC,2EAA2E;IAC3E,8BAA8B;IAC9B,UAAU,CAAC,UAAU,CAAC,QAAQ,GAAG,EAAE,GAAG;IACtC,UAAU,CAAC,UAAU,CAAC,SAAS,GAAG,EAAE,GAAG;IACvC,2EAA2E;IAC3E,8BAA8B;IAC9B,UAAU,CAAC,UAAU,CAAC,QAAQ,GAAG,EAAE,GAAG;IACtC,UAAU,CAAC,UAAU,CAAC,UAAU,GAAG,EAAE,GAAG;IACxC,UAAU,CAAC,UAAU,CAAC,UAAU,GAAG,EAAE,GAAG;IACxC,UAAU,CAAC,UAAU,CAAC,OAAO,GAAG,EAAE,GAAG;IACrC,UAAU,CAAC,UAAU,CAAC,SAAS,GAAG,EAAE,GAAG;IACvC,2BAA2B;IAC3B,wEAAwE;IACxE,0EAA0E;IAC1E,wCAAwC;IACxC,mBAAmB;IACnB,qDAAqD;IACrD,oBAAoB;IACpB,UAAU,CAAC,UAAU,CAAC,QAAQ,GAAG,GAAG,GAAG;IACvC,UAAU,CAAC,UAAU,CAAC,SAAS,GAAG,GAAG,GAAG;IACxC,kBAAkB;IAClB,UAAU,CAAC,UAAU,CAAC,WAAW,GAAG,GAAG,GAAG;IAC1C,UAAU,CAAC,UAAU,CAAC,WAAW,GAAG,GAAG,GAAG;IAC1C,UAAU,CAAC,UAAU,CAAC,SAAS,GAAG,GAAG,GAAG;IACxC,UAAU,CAAC,UAAU,CAAC,SAAS,GAAG,GAAG,GAAG;AAC5C,CAAC,EAAE,cAAc,CAAC,aAAa,CAAC,CAAC;AAgB1B,IAAI;AACX,CAAC,SAAU,QAAQ;IACf;;KAEC,GACD,QAAQ,CAAC,QAAQ,CAAC,SAAS,GAAG,EAAE,GAAG;IACnC;;;;KAIC,GACD,QAAQ,CAAC,QAAQ,CAAC,SAAS,GAAG,EAAE,GAAG;AACvC,CAAC,EAAE,YAAY,CAAC,WAAW,CAAC,CAAC", "ignoreList": [0]}}, {"offset": {"line": 794, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@bufbuild+protobuf@1.10.1/node_modules/@bufbuild/protobuf/dist/esm/private/scalars.js"], "sourcesContent": ["// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nimport { protoInt64 } from \"../proto-int64.js\";\nimport { LongType, ScalarType } from \"../scalar.js\";\n/**\n * Returns true if both scalar values are equal.\n */\nexport function scalarEquals(type, a, b) {\n    if (a === b) {\n        // This correctly matches equal values except BYTES and (possibly) 64-bit integers.\n        return true;\n    }\n    // Special case BYTES - we need to compare each byte individually\n    if (type == ScalarType.BYTES) {\n        if (!(a instanceof Uint8Array) || !(b instanceof Uint8Array)) {\n            return false;\n        }\n        if (a.length !== b.length) {\n            return false;\n        }\n        for (let i = 0; i < a.length; i++) {\n            if (a[i] !== b[i]) {\n                return false;\n            }\n        }\n        return true;\n    }\n    // Special case 64-bit integers - we support number, string and bigint representation.\n    // eslint-disable-next-line @typescript-eslint/switch-exhaustiveness-check\n    switch (type) {\n        case ScalarType.UINT64:\n        case ScalarType.FIXED64:\n        case ScalarType.INT64:\n        case ScalarType.SFIXED64:\n        case ScalarType.SINT64:\n            // Loose comparison will match between 0n, 0 and \"0\".\n            return a == b;\n    }\n    // Anything that hasn't been caught by strict comparison or special cased\n    // BYTES and 64-bit integers is not equal.\n    return false;\n}\n/**\n * Returns the zero value for the given scalar type.\n */\nexport function scalarZeroValue(type, longType) {\n    switch (type) {\n        case ScalarType.BOOL:\n            return false;\n        case ScalarType.UINT64:\n        case ScalarType.FIXED64:\n        case ScalarType.INT64:\n        case ScalarType.SFIXED64:\n        case ScalarType.SINT64:\n            // eslint-disable-next-line @typescript-eslint/no-unsafe-enum-comparison -- acceptable since it's covered by tests\n            return (longType == 0 ? protoInt64.zero : \"0\");\n        case ScalarType.DOUBLE:\n        case ScalarType.FLOAT:\n            return 0.0;\n        case ScalarType.BYTES:\n            return new Uint8Array(0);\n        case ScalarType.STRING:\n            return \"\";\n        default:\n            // Handles INT32, UINT32, SINT32, FIXED32, SFIXED32.\n            // We do not use individual cases to save a few bytes code size.\n            return 0;\n    }\n}\n/**\n * Returns true for a zero-value. For example, an integer has the zero-value `0`,\n * a boolean is `false`, a string is `\"\"`, and bytes is an empty Uint8Array.\n *\n * In proto3, zero-values are not written to the wire, unless the field is\n * optional or repeated.\n */\nexport function isScalarZeroValue(type, value) {\n    switch (type) {\n        case ScalarType.BOOL:\n            return value === false;\n        case ScalarType.STRING:\n            return value === \"\";\n        case ScalarType.BYTES:\n            return value instanceof Uint8Array && !value.byteLength;\n        default:\n            return value == 0; // Loose comparison matches 0n, 0 and \"0\"\n    }\n}\n"], "names": [], "mappings": "AAAA,6CAA6C;AAC7C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;;;;;AACjC;AACA;;;AAIO,SAAS,aAAa,IAAI,EAAE,CAAC,EAAE,CAAC;IACnC,IAAI,MAAM,GAAG;QACT,mFAAmF;QACnF,OAAO;IACX;IACA,iEAAiE;IACjE,IAAI,QAAQ,2OAAA,CAAA,aAAU,CAAC,KAAK,EAAE;QAC1B,IAAI,CAAC,CAAC,aAAa,UAAU,KAAK,CAAC,CAAC,aAAa,UAAU,GAAG;YAC1D,OAAO;QACX;QACA,IAAI,EAAE,MAAM,KAAK,EAAE,MAAM,EAAE;YACvB,OAAO;QACX;QACA,IAAK,IAAI,IAAI,GAAG,IAAI,EAAE,MAAM,EAAE,IAAK;YAC/B,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,EAAE;gBACf,OAAO;YACX;QACJ;QACA,OAAO;IACX;IACA,sFAAsF;IACtF,0EAA0E;IAC1E,OAAQ;QACJ,KAAK,2OAAA,CAAA,aAAU,CAAC,MAAM;QACtB,KAAK,2OAAA,CAAA,aAAU,CAAC,OAAO;QACvB,KAAK,2OAAA,CAAA,aAAU,CAAC,KAAK;QACrB,KAAK,2OAAA,CAAA,aAAU,CAAC,QAAQ;QACxB,KAAK,2OAAA,CAAA,aAAU,CAAC,MAAM;YAClB,qDAAqD;YACrD,OAAO,KAAK;IACpB;IACA,yEAAyE;IACzE,0CAA0C;IAC1C,OAAO;AACX;AAIO,SAAS,gBAAgB,IAAI,EAAE,QAAQ;IAC1C,OAAQ;QACJ,KAAK,2OAAA,CAAA,aAAU,CAAC,IAAI;YAChB,OAAO;QACX,KAAK,2OAAA,CAAA,aAAU,CAAC,MAAM;QACtB,KAAK,2OAAA,CAAA,aAAU,CAAC,OAAO;QACvB,KAAK,2OAAA,CAAA,aAAU,CAAC,KAAK;QACrB,KAAK,2OAAA,CAAA,aAAU,CAAC,QAAQ;QACxB,KAAK,2OAAA,CAAA,aAAU,CAAC,MAAM;YAClB,kHAAkH;YAClH,OAAQ,YAAY,IAAI,mPAAA,CAAA,aAAU,CAAC,IAAI,GAAG;QAC9C,KAAK,2OAAA,CAAA,aAAU,CAAC,MAAM;QACtB,KAAK,2OAAA,CAAA,aAAU,CAAC,KAAK;YACjB,OAAO;QACX,KAAK,2OAAA,CAAA,aAAU,CAAC,KAAK;YACjB,OAAO,IAAI,WAAW;QAC1B,KAAK,2OAAA,CAAA,aAAU,CAAC,MAAM;YAClB,OAAO;QACX;YACI,oDAAoD;YACpD,gEAAgE;YAChE,OAAO;IACf;AACJ;AAQO,SAAS,kBAAkB,IAAI,EAAE,KAAK;IACzC,OAAQ;QACJ,KAAK,2OAAA,CAAA,aAAU,CAAC,IAAI;YAChB,OAAO,UAAU;QACrB,KAAK,2OAAA,CAAA,aAAU,CAAC,MAAM;YAClB,OAAO,UAAU;QACrB,KAAK,2OAAA,CAAA,aAAU,CAAC,KAAK;YACjB,OAAO,iBAAiB,cAAc,CAAC,MAAM,UAAU;QAC3D;YACI,OAAO,SAAS,GAAG,yCAAyC;IACpE;AACJ", "ignoreList": [0]}}, {"offset": {"line": 893, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@bufbuild+protobuf@1.10.1/node_modules/@bufbuild/protobuf/dist/esm/private/extensions.js"], "sourcesContent": ["// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nimport { scalarZeroValue } from \"./scalars.js\";\nimport { WireType } from \"../binary-encoding.js\";\n/**\n * Create a new extension using the given runtime.\n */\nexport function makeExtension(runtime, typeName, extendee, field) {\n    let fi;\n    return {\n        typeName,\n        extendee,\n        get field() {\n            if (!fi) {\n                const i = (typeof field == \"function\" ? field() : field);\n                i.name = typeName.split(\".\").pop();\n                i.jsonName = `[${typeName}]`;\n                fi = runtime.util.newFieldList([i]).list()[0];\n            }\n            return fi;\n        },\n        runtime,\n    };\n}\n/**\n * Create a container that allows us to read extension fields into it with the\n * same logic as regular fields.\n */\nexport function createExtensionContainer(extension) {\n    const localName = extension.field.localName;\n    const container = Object.create(null);\n    container[localName] = initExtensionField(extension);\n    return [container, () => container[localName]];\n}\nfunction initExtensionField(ext) {\n    const field = ext.field;\n    if (field.repeated) {\n        return [];\n    }\n    if (field.default !== undefined) {\n        return field.default;\n    }\n    switch (field.kind) {\n        case \"enum\":\n            return field.T.values[0].no;\n        case \"scalar\":\n            return scalarZeroValue(field.T, field.L);\n        case \"message\":\n            // eslint-disable-next-line no-case-declarations\n            const T = field.T, value = new T();\n            return T.fieldWrapper ? T.fieldWrapper.unwrapField(value) : value;\n        case \"map\":\n            throw \"map fields are not allowed to be extensions\";\n    }\n}\n/**\n * Helper to filter unknown fields, optimized based on field type.\n */\nexport function filterUnknownFields(unknownFields, field) {\n    if (!field.repeated && (field.kind == \"enum\" || field.kind == \"scalar\")) {\n        // singular scalar fields do not merge, we pick the last\n        for (let i = unknownFields.length - 1; i >= 0; --i) {\n            if (unknownFields[i].no == field.no) {\n                return [unknownFields[i]];\n            }\n        }\n        return [];\n    }\n    return unknownFields.filter((uf) => uf.no === field.no);\n}\n"], "names": [], "mappings": "AAAA,6CAA6C;AAC7C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;;;;;AACjC;;;AAKO,SAAS,cAAc,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK;IAC5D,IAAI;IACJ,OAAO;QACH;QACA;QACA,IAAI,SAAQ;YACR,IAAI,CAAC,IAAI;gBACL,MAAM,IAAK,OAAO,SAAS,aAAa,UAAU;gBAClD,EAAE,IAAI,GAAG,SAAS,KAAK,CAAC,KAAK,GAAG;gBAChC,EAAE,QAAQ,GAAG,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;gBAC5B,KAAK,QAAQ,IAAI,CAAC,YAAY,CAAC;oBAAC;iBAAE,EAAE,IAAI,EAAE,CAAC,EAAE;YACjD;YACA,OAAO;QACX;QACA;IACJ;AACJ;AAKO,SAAS,yBAAyB,SAAS;IAC9C,MAAM,YAAY,UAAU,KAAK,CAAC,SAAS;IAC3C,MAAM,YAAY,OAAO,MAAM,CAAC;IAChC,SAAS,CAAC,UAAU,GAAG,mBAAmB;IAC1C,OAAO;QAAC;QAAW,IAAM,SAAS,CAAC,UAAU;KAAC;AAClD;AACA,SAAS,mBAAmB,GAAG;IAC3B,MAAM,QAAQ,IAAI,KAAK;IACvB,IAAI,MAAM,QAAQ,EAAE;QAChB,OAAO,EAAE;IACb;IACA,IAAI,MAAM,OAAO,KAAK,WAAW;QAC7B,OAAO,MAAM,OAAO;IACxB;IACA,OAAQ,MAAM,IAAI;QACd,KAAK;YACD,OAAO,MAAM,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE;QAC/B,KAAK;YACD,OAAO,CAAA,GAAA,uPAAA,CAAA,kBAAe,AAAD,EAAE,MAAM,CAAC,EAAE,MAAM,CAAC;QAC3C,KAAK;YACD,gDAAgD;YAChD,MAAM,IAAI,MAAM,CAAC,EAAE,QAAQ,IAAI;YAC/B,OAAO,EAAE,YAAY,GAAG,EAAE,YAAY,CAAC,WAAW,CAAC,SAAS;QAChE,KAAK;YACD,MAAM;IACd;AACJ;AAIO,SAAS,oBAAoB,aAAa,EAAE,KAAK;IACpD,IAAI,CAAC,MAAM,QAAQ,IAAI,CAAC,MAAM,IAAI,IAAI,UAAU,MAAM,IAAI,IAAI,QAAQ,GAAG;QACrE,wDAAwD;QACxD,IAAK,IAAI,IAAI,cAAc,MAAM,GAAG,GAAG,KAAK,GAAG,EAAE,EAAG;YAChD,IAAI,aAAa,CAAC,EAAE,CAAC,EAAE,IAAI,MAAM,EAAE,EAAE;gBACjC,OAAO;oBAAC,aAAa,CAAC,EAAE;iBAAC;YAC7B;QACJ;QACA,OAAO,EAAE;IACb;IACA,OAAO,cAAc,MAAM,CAAC,CAAC,KAAO,GAAG,EAAE,KAAK,MAAM,EAAE;AAC1D", "ignoreList": [0]}}, {"offset": {"line": 983, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@bufbuild+protobuf@1.10.1/node_modules/@bufbuild/protobuf/dist/esm/proto-base64.js"], "sourcesContent": ["// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n/* eslint-disable @typescript-eslint/ban-ts-comment, @typescript-eslint/no-unnecessary-condition, prefer-const */\n// lookup table from base64 character to byte\nlet encTable = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\".split(\"\");\n// lookup table from base64 character *code* to byte because lookup by number is fast\nlet decTable = [];\nfor (let i = 0; i < encTable.length; i++)\n    decTable[encTable[i].charCodeAt(0)] = i;\n// support base64url variants\ndecTable[\"-\".charCodeAt(0)] = encTable.indexOf(\"+\");\ndecTable[\"_\".charCodeAt(0)] = encTable.indexOf(\"/\");\nexport const protoBase64 = {\n    /**\n     * Decodes a base64 string to a byte array.\n     *\n     * - ignores white-space, including line breaks and tabs\n     * - allows inner padding (can decode concatenated base64 strings)\n     * - does not require padding\n     * - understands base64url encoding:\n     *   \"-\" instead of \"+\",\n     *   \"_\" instead of \"/\",\n     *   no padding\n     */\n    dec(base64Str) {\n        // estimate byte size, not accounting for inner padding and whitespace\n        let es = (base64Str.length * 3) / 4;\n        if (base64Str[base64Str.length - 2] == \"=\")\n            es -= 2;\n        else if (base64Str[base64Str.length - 1] == \"=\")\n            es -= 1;\n        let bytes = new Uint8Array(es), bytePos = 0, // position in byte array\n        groupPos = 0, // position in base64 group\n        b, // current byte\n        p = 0; // previous byte\n        for (let i = 0; i < base64Str.length; i++) {\n            b = decTable[base64Str.charCodeAt(i)];\n            if (b === undefined) {\n                switch (base64Str[i]) {\n                    // @ts-ignore TS7029: Fallthrough case in switch\n                    case \"=\":\n                        groupPos = 0; // reset state when padding found\n                    // @ts-ignore TS7029: Fallthrough case in switch\n                    case \"\\n\":\n                    case \"\\r\":\n                    case \"\\t\":\n                    case \" \":\n                        continue; // skip white-space, and padding\n                    default:\n                        throw Error(\"invalid base64 string.\");\n                }\n            }\n            switch (groupPos) {\n                case 0:\n                    p = b;\n                    groupPos = 1;\n                    break;\n                case 1:\n                    bytes[bytePos++] = (p << 2) | ((b & 48) >> 4);\n                    p = b;\n                    groupPos = 2;\n                    break;\n                case 2:\n                    bytes[bytePos++] = ((p & 15) << 4) | ((b & 60) >> 2);\n                    p = b;\n                    groupPos = 3;\n                    break;\n                case 3:\n                    bytes[bytePos++] = ((p & 3) << 6) | b;\n                    groupPos = 0;\n                    break;\n            }\n        }\n        if (groupPos == 1)\n            throw Error(\"invalid base64 string.\");\n        return bytes.subarray(0, bytePos);\n    },\n    /**\n     * Encode a byte array to a base64 string.\n     */\n    enc(bytes) {\n        let base64 = \"\", groupPos = 0, // position in base64 group\n        b, // current byte\n        p = 0; // carry over from previous byte\n        for (let i = 0; i < bytes.length; i++) {\n            b = bytes[i];\n            switch (groupPos) {\n                case 0:\n                    base64 += encTable[b >> 2];\n                    p = (b & 3) << 4;\n                    groupPos = 1;\n                    break;\n                case 1:\n                    base64 += encTable[p | (b >> 4)];\n                    p = (b & 15) << 2;\n                    groupPos = 2;\n                    break;\n                case 2:\n                    base64 += encTable[p | (b >> 6)];\n                    base64 += encTable[b & 63];\n                    groupPos = 0;\n                    break;\n            }\n        }\n        // add output padding\n        if (groupPos) {\n            base64 += encTable[p];\n            base64 += \"=\";\n            if (groupPos == 1)\n                base64 += \"=\";\n        }\n        return base64;\n    },\n};\n"], "names": [], "mappings": "AAAA,6CAA6C;AAC7C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC,+GAA+G,GAC/G,6CAA6C;;;;AAC7C,IAAI,WAAW,mEAAmE,KAAK,CAAC;AACxF,qFAAqF;AACrF,IAAI,WAAW,EAAE;AACjB,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,IACjC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC,UAAU,CAAC,GAAG,GAAG;AAC1C,6BAA6B;AAC7B,QAAQ,CAAC,IAAI,UAAU,CAAC,GAAG,GAAG,SAAS,OAAO,CAAC;AAC/C,QAAQ,CAAC,IAAI,UAAU,CAAC,GAAG,GAAG,SAAS,OAAO,CAAC;AACxC,MAAM,cAAc;IACvB;;;;;;;;;;KAUC,GACD,KAAI,SAAS;QACT,sEAAsE;QACtE,IAAI,KAAK,AAAC,UAAU,MAAM,GAAG,IAAK;QAClC,IAAI,SAAS,CAAC,UAAU,MAAM,GAAG,EAAE,IAAI,KACnC,MAAM;aACL,IAAI,SAAS,CAAC,UAAU,MAAM,GAAG,EAAE,IAAI,KACxC,MAAM;QACV,IAAI,QAAQ,IAAI,WAAW,KAAK,UAAU,GAC1C,WAAW,GACX,GACA,IAAI,GAAG,gBAAgB;QACvB,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;YACvC,IAAI,QAAQ,CAAC,UAAU,UAAU,CAAC,GAAG;YACrC,IAAI,MAAM,WAAW;gBACjB,OAAQ,SAAS,CAAC,EAAE;oBAChB,gDAAgD;oBAChD,KAAK;wBACD,WAAW,GAAG,iCAAiC;oBACnD,gDAAgD;oBAChD,KAAK;oBACL,KAAK;oBACL,KAAK;oBACL,KAAK;wBACD,UAAU,gCAAgC;oBAC9C;wBACI,MAAM,MAAM;gBACpB;YACJ;YACA,OAAQ;gBACJ,KAAK;oBACD,IAAI;oBACJ,WAAW;oBACX;gBACJ,KAAK;oBACD,KAAK,CAAC,UAAU,GAAG,AAAC,KAAK,IAAM,CAAC,IAAI,EAAE,KAAK;oBAC3C,IAAI;oBACJ,WAAW;oBACX;gBACJ,KAAK;oBACD,KAAK,CAAC,UAAU,GAAG,AAAC,CAAC,IAAI,EAAE,KAAK,IAAM,CAAC,IAAI,EAAE,KAAK;oBAClD,IAAI;oBACJ,WAAW;oBACX;gBACJ,KAAK;oBACD,KAAK,CAAC,UAAU,GAAG,AAAC,CAAC,IAAI,CAAC,KAAK,IAAK;oBACpC,WAAW;oBACX;YACR;QACJ;QACA,IAAI,YAAY,GACZ,MAAM,MAAM;QAChB,OAAO,MAAM,QAAQ,CAAC,GAAG;IAC7B;IACA;;KAEC,GACD,KAAI,KAAK;QACL,IAAI,SAAS,IAAI,WAAW,GAC5B,GACA,IAAI,GAAG,gCAAgC;QACvC,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YACnC,IAAI,KAAK,CAAC,EAAE;YACZ,OAAQ;gBACJ,KAAK;oBACD,UAAU,QAAQ,CAAC,KAAK,EAAE;oBAC1B,IAAI,CAAC,IAAI,CAAC,KAAK;oBACf,WAAW;oBACX;gBACJ,KAAK;oBACD,UAAU,QAAQ,CAAC,IAAK,KAAK,EAAG;oBAChC,IAAI,CAAC,IAAI,EAAE,KAAK;oBAChB,WAAW;oBACX;gBACJ,KAAK;oBACD,UAAU,QAAQ,CAAC,IAAK,KAAK,EAAG;oBAChC,UAAU,QAAQ,CAAC,IAAI,GAAG;oBAC1B,WAAW;oBACX;YACR;QACJ;QACA,qBAAqB;QACrB,IAAI,UAAU;YACV,UAAU,QAAQ,CAAC,EAAE;YACrB,UAAU;YACV,IAAI,YAAY,GACZ,UAAU;QAClB;QACA,OAAO;IACX;AACJ", "ignoreList": [0]}}, {"offset": {"line": 1104, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@bufbuild+protobuf@1.10.1/node_modules/@bufbuild/protobuf/dist/esm/extension-accessor.js"], "sourcesContent": ["// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nimport { assert } from \"./private/assert.js\";\nimport { createExtensionContainer, filterUnknownFields, } from \"./private/extensions.js\";\n/**\n * Retrieve an extension value from a message.\n *\n * The function never returns undefined. Use hasExtension() to check whether an\n * extension is set. If the extension is not set, this function returns the\n * default value (if one was specified in the protobuf source), or the zero value\n * (for example `0` for numeric types, `[]` for repeated extension fields, and\n * an empty message instance for message fields).\n *\n * Extensions are stored as unknown fields on a message. To mutate an extension\n * value, make sure to store the new value with setExtension() after mutating.\n *\n * If the extension does not extend the given message, an error is raised.\n */\nexport function getExtension(message, extension, options) {\n    assertExtendee(extension, message);\n    const opt = extension.runtime.bin.makeReadOptions(options);\n    const ufs = filterUnknownFields(message.getType().runtime.bin.listUnknownFields(message), extension.field);\n    const [container, get] = createExtensionContainer(extension);\n    for (const uf of ufs) {\n        extension.runtime.bin.readField(container, opt.readerFactory(uf.data), extension.field, uf.wireType, opt);\n    }\n    return get();\n}\n/**\n * Set an extension value on a message. If the message already has a value for\n * this extension, the value is replaced.\n *\n * If the extension does not extend the given message, an error is raised.\n */\nexport function setExtension(message, extension, value, options) {\n    assertExtendee(extension, message);\n    const readOpt = extension.runtime.bin.makeReadOptions(options);\n    const writeOpt = extension.runtime.bin.makeWriteOptions(options);\n    if (hasExtension(message, extension)) {\n        const ufs = message\n            .getType()\n            .runtime.bin.listUnknownFields(message)\n            .filter((uf) => uf.no != extension.field.no);\n        message.getType().runtime.bin.discardUnknownFields(message);\n        for (const uf of ufs) {\n            message\n                .getType()\n                .runtime.bin.onUnknownField(message, uf.no, uf.wireType, uf.data);\n        }\n    }\n    const writer = writeOpt.writerFactory();\n    let f = extension.field;\n    // Implicit presence does not apply to extensions, see https://github.com/protocolbuffers/protobuf/issues/8234\n    // We patch the field info to use explicit presence:\n    if (!f.opt && !f.repeated && (f.kind == \"enum\" || f.kind == \"scalar\")) {\n        f = Object.assign(Object.assign({}, extension.field), { opt: true });\n    }\n    extension.runtime.bin.writeField(f, value, writer, writeOpt);\n    const reader = readOpt.readerFactory(writer.finish());\n    while (reader.pos < reader.len) {\n        const [no, wireType] = reader.tag();\n        const data = reader.skip(wireType, no);\n        message.getType().runtime.bin.onUnknownField(message, no, wireType, data);\n    }\n}\n/**\n * Remove an extension value from a message.\n *\n * If the extension does not extend the given message, an error is raised.\n */\nexport function clearExtension(message, extension) {\n    assertExtendee(extension, message);\n    if (hasExtension(message, extension)) {\n        const bin = message.getType().runtime.bin;\n        const ufs = bin\n            .listUnknownFields(message)\n            .filter((uf) => uf.no != extension.field.no);\n        bin.discardUnknownFields(message);\n        for (const uf of ufs) {\n            bin.onUnknownField(message, uf.no, uf.wireType, uf.data);\n        }\n    }\n}\n/**\n * Check whether an extension is set on a message.\n */\nexport function hasExtension(message, extension) {\n    const messageType = message.getType();\n    return (extension.extendee.typeName === messageType.typeName &&\n        !!messageType.runtime.bin\n            .listUnknownFields(message)\n            .find((uf) => uf.no == extension.field.no));\n}\nfunction assertExtendee(extension, message) {\n    assert(extension.extendee.typeName == message.getType().typeName, `extension ${extension.typeName} can only be applied to message ${extension.extendee.typeName}`);\n}\n"], "names": [], "mappings": "AAAA,6CAA6C;AAC7C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;;;;;;AACjC;AACA;;;AAeO,SAAS,aAAa,OAAO,EAAE,SAAS,EAAE,OAAO;IACpD,eAAe,WAAW;IAC1B,MAAM,MAAM,UAAU,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC;IAClD,MAAM,MAAM,CAAA,GAAA,0PAAA,CAAA,sBAAmB,AAAD,EAAE,QAAQ,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,UAAU,UAAU,KAAK;IACzG,MAAM,CAAC,WAAW,IAAI,GAAG,CAAA,GAAA,0PAAA,CAAA,2BAAwB,AAAD,EAAE;IAClD,KAAK,MAAM,MAAM,IAAK;QAClB,UAAU,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,WAAW,IAAI,aAAa,CAAC,GAAG,IAAI,GAAG,UAAU,KAAK,EAAE,GAAG,QAAQ,EAAE;IACzG;IACA,OAAO;AACX;AAOO,SAAS,aAAa,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO;IAC3D,eAAe,WAAW;IAC1B,MAAM,UAAU,UAAU,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC;IACtD,MAAM,WAAW,UAAU,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC;IACxD,IAAI,aAAa,SAAS,YAAY;QAClC,MAAM,MAAM,QACP,OAAO,GACP,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,SAC9B,MAAM,CAAC,CAAC,KAAO,GAAG,EAAE,IAAI,UAAU,KAAK,CAAC,EAAE;QAC/C,QAAQ,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC;QACnD,KAAK,MAAM,MAAM,IAAK;YAClB,QACK,OAAO,GACP,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,SAAS,GAAG,EAAE,EAAE,GAAG,QAAQ,EAAE,GAAG,IAAI;QACxE;IACJ;IACA,MAAM,SAAS,SAAS,aAAa;IACrC,IAAI,IAAI,UAAU,KAAK;IACvB,8GAA8G;IAC9G,oDAAoD;IACpD,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,QAAQ,IAAI,CAAC,EAAE,IAAI,IAAI,UAAU,EAAE,IAAI,IAAI,QAAQ,GAAG;QACnE,IAAI,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,UAAU,KAAK,GAAG;YAAE,KAAK;QAAK;IACtE;IACA,UAAU,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,OAAO,QAAQ;IACnD,MAAM,SAAS,QAAQ,aAAa,CAAC,OAAO,MAAM;IAClD,MAAO,OAAO,GAAG,GAAG,OAAO,GAAG,CAAE;QAC5B,MAAM,CAAC,IAAI,SAAS,GAAG,OAAO,GAAG;QACjC,MAAM,OAAO,OAAO,IAAI,CAAC,UAAU;QACnC,QAAQ,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,SAAS,IAAI,UAAU;IACxE;AACJ;AAMO,SAAS,eAAe,OAAO,EAAE,SAAS;IAC7C,eAAe,WAAW;IAC1B,IAAI,aAAa,SAAS,YAAY;QAClC,MAAM,MAAM,QAAQ,OAAO,GAAG,OAAO,CAAC,GAAG;QACzC,MAAM,MAAM,IACP,iBAAiB,CAAC,SAClB,MAAM,CAAC,CAAC,KAAO,GAAG,EAAE,IAAI,UAAU,KAAK,CAAC,EAAE;QAC/C,IAAI,oBAAoB,CAAC;QACzB,KAAK,MAAM,MAAM,IAAK;YAClB,IAAI,cAAc,CAAC,SAAS,GAAG,EAAE,EAAE,GAAG,QAAQ,EAAE,GAAG,IAAI;QAC3D;IACJ;AACJ;AAIO,SAAS,aAAa,OAAO,EAAE,SAAS;IAC3C,MAAM,cAAc,QAAQ,OAAO;IACnC,OAAQ,UAAU,QAAQ,CAAC,QAAQ,KAAK,YAAY,QAAQ,IACxD,CAAC,CAAC,YAAY,OAAO,CAAC,GAAG,CACpB,iBAAiB,CAAC,SAClB,IAAI,CAAC,CAAC,KAAO,GAAG,EAAE,IAAI,UAAU,KAAK,CAAC,EAAE;AACrD;AACA,SAAS,eAAe,SAAS,EAAE,OAAO;IACtC,CAAA,GAAA,sPAAA,CAAA,SAAM,AAAD,EAAE,UAAU,QAAQ,CAAC,QAAQ,IAAI,QAAQ,OAAO,GAAG,QAAQ,EAAE,CAAC,UAAU,EAAE,UAAU,QAAQ,CAAC,gCAAgC,EAAE,UAAU,QAAQ,CAAC,QAAQ,EAAE;AACrK", "ignoreList": [0]}}, {"offset": {"line": 1189, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@bufbuild+protobuf@1.10.1/node_modules/@bufbuild/protobuf/dist/esm/private/reflect.js"], "sourcesContent": ["// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nimport { isScalarZeroValue, scalarZeroValue } from \"./scalars.js\";\n/**\n * Returns true if the field is set.\n */\nexport function isFieldSet(field, target) {\n    const localName = field.localName;\n    if (field.repeated) {\n        return target[localName].length > 0;\n    }\n    if (field.oneof) {\n        return target[field.oneof.localName].case === localName; // eslint-disable-line @typescript-eslint/no-unsafe-member-access\n    }\n    switch (field.kind) {\n        case \"enum\":\n        case \"scalar\":\n            if (field.opt || field.req) {\n                // explicit presence\n                return target[localName] !== undefined;\n            }\n            // implicit presence\n            if (field.kind == \"enum\") {\n                return target[localName] !== field.T.values[0].no;\n            }\n            return !isScalarZeroValue(field.T, target[localName]);\n        case \"message\":\n            return target[localName] !== undefined;\n        case \"map\":\n            return Object.keys(target[localName]).length > 0; // eslint-disable-line @typescript-eslint/no-unsafe-argument\n    }\n}\n/**\n * Resets the field, so that isFieldSet() will return false.\n */\nexport function clearField(field, target) {\n    const localName = field.localName;\n    const implicitPresence = !field.opt && !field.req;\n    if (field.repeated) {\n        target[localName] = [];\n    }\n    else if (field.oneof) {\n        target[field.oneof.localName] = { case: undefined };\n    }\n    else {\n        switch (field.kind) {\n            case \"map\":\n                target[localName] = {};\n                break;\n            case \"enum\":\n                target[localName] = implicitPresence ? field.T.values[0].no : undefined;\n                break;\n            case \"scalar\":\n                target[localName] = implicitPresence\n                    ? scalarZeroValue(field.T, field.L)\n                    : undefined;\n                break;\n            case \"message\":\n                target[localName] = undefined;\n                break;\n        }\n    }\n}\n"], "names": [], "mappings": "AAAA,6CAA6C;AAC7C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;;;;AACjC;;AAIO,SAAS,WAAW,KAAK,EAAE,MAAM;IACpC,MAAM,YAAY,MAAM,SAAS;IACjC,IAAI,MAAM,QAAQ,EAAE;QAChB,OAAO,MAAM,CAAC,UAAU,CAAC,MAAM,GAAG;IACtC;IACA,IAAI,MAAM,KAAK,EAAE;QACb,OAAO,MAAM,CAAC,MAAM,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,KAAK,WAAW,iEAAiE;IAC9H;IACA,OAAQ,MAAM,IAAI;QACd,KAAK;QACL,KAAK;YACD,IAAI,MAAM,GAAG,IAAI,MAAM,GAAG,EAAE;gBACxB,oBAAoB;gBACpB,OAAO,MAAM,CAAC,UAAU,KAAK;YACjC;YACA,oBAAoB;YACpB,IAAI,MAAM,IAAI,IAAI,QAAQ;gBACtB,OAAO,MAAM,CAAC,UAAU,KAAK,MAAM,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE;YACrD;YACA,OAAO,CAAC,CAAA,GAAA,uPAAA,CAAA,oBAAiB,AAAD,EAAE,MAAM,CAAC,EAAE,MAAM,CAAC,UAAU;QACxD,KAAK;YACD,OAAO,MAAM,CAAC,UAAU,KAAK;QACjC,KAAK;YACD,OAAO,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,MAAM,GAAG,GAAG,4DAA4D;IACtH;AACJ;AAIO,SAAS,WAAW,KAAK,EAAE,MAAM;IACpC,MAAM,YAAY,MAAM,SAAS;IACjC,MAAM,mBAAmB,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,GAAG;IACjD,IAAI,MAAM,QAAQ,EAAE;QAChB,MAAM,CAAC,UAAU,GAAG,EAAE;IAC1B,OACK,IAAI,MAAM,KAAK,EAAE;QAClB,MAAM,CAAC,MAAM,KAAK,CAAC,SAAS,CAAC,GAAG;YAAE,MAAM;QAAU;IACtD,OACK;QACD,OAAQ,MAAM,IAAI;YACd,KAAK;gBACD,MAAM,CAAC,UAAU,GAAG,CAAC;gBACrB;YACJ,KAAK;gBACD,MAAM,CAAC,UAAU,GAAG,mBAAmB,MAAM,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,GAAG;gBAC9D;YACJ,KAAK;gBACD,MAAM,CAAC,UAAU,GAAG,mBACd,CAAA,GAAA,uPAAA,CAAA,kBAAe,AAAD,EAAE,MAAM,CAAC,EAAE,MAAM,CAAC,IAChC;gBACN;YACJ,KAAK;gBACD,MAAM,CAAC,UAAU,GAAG;gBACpB;QACR;IACJ;AACJ", "ignoreList": [0]}}, {"offset": {"line": 1266, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@bufbuild+protobuf@1.10.1/node_modules/@bufbuild/protobuf/dist/esm/is-message.js"], "sourcesContent": ["// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nimport { Message } from \"./message.js\";\n/**\n * Check whether the given object is any subtype of Message or is a specific\n * Message by passing the type.\n *\n * Just like `instanceof`, `isMessage` narrows the type. The advantage of\n * `isMessage` is that it compares identity by the message type name, not by\n * class identity. This makes it robust against the dual package hazard and\n * similar situations, where the same message is duplicated.\n *\n * This function is _mostly_ equivalent to the `instanceof` operator. For\n * example, `isMessage(foo, MyMessage)` is the same as `foo instanceof MyMessage`,\n * and `isMessage(foo)` is the same as `foo instanceof Message`. In most cases,\n * `isMessage` should be preferred over `instanceof`.\n *\n * However, due to the fact that `isMessage` does not use class identity, there\n * are subtle differences between this function and `instanceof`. Notably,\n * calling `isMessage` on an explicit type of Message will return false.\n */\nexport function isMessage(arg, type) {\n    if (arg === null || typeof arg != \"object\") {\n        return false;\n    }\n    if (!Object.getOwnPropertyNames(Message.prototype).every((m) => m in arg && typeof arg[m] == \"function\")) {\n        return false;\n    }\n    const actualType = arg.getType();\n    if (actualType === null ||\n        typeof actualType != \"function\" ||\n        !(\"typeName\" in actualType) ||\n        typeof actualType.typeName != \"string\") {\n        return false;\n    }\n    return type === undefined ? true : actualType.typeName == type.typeName;\n}\n"], "names": [], "mappings": "AAAA,6CAA6C;AAC7C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;;;AACjC;;AAmBO,SAAS,UAAU,GAAG,EAAE,IAAI;IAC/B,IAAI,QAAQ,QAAQ,OAAO,OAAO,UAAU;QACxC,OAAO;IACX;IACA,IAAI,CAAC,OAAO,mBAAmB,CAAC,4OAAA,CAAA,UAAO,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC,IAAM,KAAK,OAAO,OAAO,GAAG,CAAC,EAAE,IAAI,aAAa;QACtG,OAAO;IACX;IACA,MAAM,aAAa,IAAI,OAAO;IAC9B,IAAI,eAAe,QACf,OAAO,cAAc,cACrB,CAAC,CAAC,cAAc,UAAU,KAC1B,OAAO,WAAW,QAAQ,IAAI,UAAU;QACxC,OAAO;IACX;IACA,OAAO,SAAS,YAAY,OAAO,WAAW,QAAQ,IAAI,KAAK,QAAQ;AAC3E", "ignoreList": [0]}}, {"offset": {"line": 1303, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@bufbuild+protobuf@1.10.1/node_modules/@bufbuild/protobuf/dist/esm/private/field-wrapper.js"], "sourcesContent": ["// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nimport { Message } from \"../message.js\";\nimport { ScalarType } from \"../scalar.js\";\nimport { isMessage } from \"../is-message.js\";\n/**\n * Wrap a primitive message field value in its corresponding wrapper\n * message. This function is idempotent.\n */\nexport function wrapField(type, value) {\n    if (isMessage(value) || !type.fieldWrapper) {\n        return value;\n    }\n    return type.fieldWrapper.wrapField(value);\n}\n/**\n * If the given field uses one of the well-known wrapper types, return\n * the primitive type it wraps.\n */\nexport function getUnwrappedFieldType(field) {\n    if (field.fieldKind !== \"message\") {\n        return undefined;\n    }\n    if (field.repeated) {\n        return undefined;\n    }\n    if (field.oneof != undefined) {\n        return undefined;\n    }\n    return wktWrapperToScalarType[field.message.typeName];\n}\nconst wktWrapperToScalarType = {\n    \"google.protobuf.DoubleValue\": ScalarType.DOUBLE,\n    \"google.protobuf.FloatValue\": ScalarType.FLOAT,\n    \"google.protobuf.Int64Value\": ScalarType.INT64,\n    \"google.protobuf.UInt64Value\": ScalarType.UINT64,\n    \"google.protobuf.Int32Value\": ScalarType.INT32,\n    \"google.protobuf.UInt32Value\": ScalarType.UINT32,\n    \"google.protobuf.BoolValue\": ScalarType.BOOL,\n    \"google.protobuf.StringValue\": ScalarType.STRING,\n    \"google.protobuf.BytesValue\": ScalarType.BYTES,\n};\n"], "names": [], "mappings": "AAAA,6CAA6C;AAC7C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;;;;AAEjC;AACA;;;;AAKO,SAAS,UAAU,IAAI,EAAE,KAAK;IACjC,IAAI,CAAA,GAAA,kPAAA,CAAA,YAAS,AAAD,EAAE,UAAU,CAAC,KAAK,YAAY,EAAE;QACxC,OAAO;IACX;IACA,OAAO,KAAK,YAAY,CAAC,SAAS,CAAC;AACvC;AAKO,SAAS,sBAAsB,KAAK;IACvC,IAAI,MAAM,SAAS,KAAK,WAAW;QAC/B,OAAO;IACX;IACA,IAAI,MAAM,QAAQ,EAAE;QAChB,OAAO;IACX;IACA,IAAI,MAAM,KAAK,IAAI,WAAW;QAC1B,OAAO;IACX;IACA,OAAO,sBAAsB,CAAC,MAAM,OAAO,CAAC,QAAQ,CAAC;AACzD;AACA,MAAM,yBAAyB;IAC3B,+BAA+B,2OAAA,CAAA,aAAU,CAAC,MAAM;IAChD,8BAA8B,2OAAA,CAAA,aAAU,CAAC,KAAK;IAC9C,8BAA8B,2OAAA,CAAA,aAAU,CAAC,KAAK;IAC9C,+BAA+B,2OAAA,CAAA,aAAU,CAAC,MAAM;IAChD,8BAA8B,2OAAA,CAAA,aAAU,CAAC,KAAK;IAC9C,+BAA+B,2OAAA,CAAA,aAAU,CAAC,MAAM;IAChD,6BAA6B,2OAAA,CAAA,aAAU,CAAC,IAAI;IAC5C,+BAA+B,2OAAA,CAAA,aAAU,CAAC,MAAM;IAChD,8BAA8B,2OAAA,CAAA,aAAU,CAAC,KAAK;AAClD", "ignoreList": [0]}}, {"offset": {"line": 1360, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@bufbuild+protobuf@1.10.1/node_modules/@bufbuild/protobuf/dist/esm/private/json-format.js"], "sourcesContent": ["// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nimport { Message } from \"../message.js\";\nimport { assert, assertFloat32, assertInt32, assertUInt32 } from \"./assert.js\";\nimport { protoInt64 } from \"../proto-int64.js\";\nimport { protoBase64 } from \"../proto-base64.js\";\nimport { createExtensionContainer } from \"./extensions.js\";\nimport { getExtension, hasExtension, setExtension, } from \"../extension-accessor.js\";\nimport { clearField, isFieldSet } from \"./reflect.js\";\nimport { wrapField } from \"./field-wrapper.js\";\nimport { scalarZeroValue } from \"./scalars.js\";\nimport { isScalarZeroValue } from \"./scalars.js\";\nimport { LongType, ScalarType } from \"../scalar.js\";\nimport { isMessage } from \"../is-message.js\";\n/* eslint-disable no-case-declarations,@typescript-eslint/no-unsafe-argument,@typescript-eslint/no-unsafe-assignment,@typescript-eslint/no-unsafe-member-access,@typescript-eslint/no-unsafe-call */\n// Default options for parsing JSON.\nconst jsonReadDefaults = {\n    ignoreUnknownFields: false,\n};\n// Default options for serializing to JSON.\nconst jsonWriteDefaults = {\n    emitDefaultValues: false,\n    enumAsInteger: false,\n    useProtoFieldName: false,\n    prettySpaces: 0,\n};\nfunction makeReadOptions(options) {\n    return options ? Object.assign(Object.assign({}, jsonReadDefaults), options) : jsonReadDefaults;\n}\nfunction makeWriteOptions(options) {\n    return options ? Object.assign(Object.assign({}, jsonWriteDefaults), options) : jsonWriteDefaults;\n}\nconst tokenNull = Symbol();\nconst tokenIgnoredUnknownEnum = Symbol();\nexport function makeJsonFormat() {\n    return {\n        makeReadOptions,\n        makeWriteOptions,\n        readMessage(type, json, options, message) {\n            if (json == null || Array.isArray(json) || typeof json != \"object\") {\n                throw new Error(`cannot decode message ${type.typeName} from JSON: ${debugJsonValue(json)}`);\n            }\n            message = message !== null && message !== void 0 ? message : new type();\n            const oneofSeen = new Map();\n            const registry = options.typeRegistry;\n            for (const [jsonKey, jsonValue] of Object.entries(json)) {\n                const field = type.fields.findJsonName(jsonKey);\n                if (field) {\n                    if (field.oneof) {\n                        if (jsonValue === null && field.kind == \"scalar\") {\n                            // see conformance test Required.Proto3.JsonInput.OneofFieldNull{First,Second}\n                            continue;\n                        }\n                        const seen = oneofSeen.get(field.oneof);\n                        if (seen !== undefined) {\n                            throw new Error(`cannot decode message ${type.typeName} from JSON: multiple keys for oneof \"${field.oneof.name}\" present: \"${seen}\", \"${jsonKey}\"`);\n                        }\n                        oneofSeen.set(field.oneof, jsonKey);\n                    }\n                    readField(message, jsonValue, field, options, type);\n                }\n                else {\n                    let found = false;\n                    if ((registry === null || registry === void 0 ? void 0 : registry.findExtension) &&\n                        jsonKey.startsWith(\"[\") &&\n                        jsonKey.endsWith(\"]\")) {\n                        const ext = registry.findExtension(jsonKey.substring(1, jsonKey.length - 1));\n                        if (ext && ext.extendee.typeName == type.typeName) {\n                            found = true;\n                            const [container, get] = createExtensionContainer(ext);\n                            readField(container, jsonValue, ext.field, options, ext);\n                            // We pass on the options as BinaryReadOptions/BinaryWriteOptions,\n                            // so that users can bring their own binary reader and writer factories\n                            // if necessary.\n                            setExtension(message, ext, get(), options);\n                        }\n                    }\n                    if (!found && !options.ignoreUnknownFields) {\n                        throw new Error(`cannot decode message ${type.typeName} from JSON: key \"${jsonKey}\" is unknown`);\n                    }\n                }\n            }\n            return message;\n        },\n        writeMessage(message, options) {\n            const type = message.getType();\n            const json = {};\n            let field;\n            try {\n                for (field of type.fields.byNumber()) {\n                    if (!isFieldSet(field, message)) {\n                        // eslint-disable-next-line @typescript-eslint/strict-boolean-expressions\n                        if (field.req) {\n                            throw `required field not set`;\n                        }\n                        if (!options.emitDefaultValues) {\n                            continue;\n                        }\n                        if (!canEmitFieldDefaultValue(field)) {\n                            continue;\n                        }\n                    }\n                    const value = field.oneof\n                        ? message[field.oneof.localName].value\n                        : message[field.localName];\n                    const jsonValue = writeField(field, value, options);\n                    if (jsonValue !== undefined) {\n                        json[options.useProtoFieldName ? field.name : field.jsonName] =\n                            jsonValue;\n                    }\n                }\n                const registry = options.typeRegistry;\n                if (registry === null || registry === void 0 ? void 0 : registry.findExtensionFor) {\n                    for (const uf of type.runtime.bin.listUnknownFields(message)) {\n                        const ext = registry.findExtensionFor(type.typeName, uf.no);\n                        if (ext && hasExtension(message, ext)) {\n                            // We pass on the options as BinaryReadOptions, so that users can bring their own\n                            // binary reader factory if necessary.\n                            const value = getExtension(message, ext, options);\n                            const jsonValue = writeField(ext.field, value, options);\n                            if (jsonValue !== undefined) {\n                                json[ext.field.jsonName] = jsonValue;\n                            }\n                        }\n                    }\n                }\n            }\n            catch (e) {\n                const m = field\n                    ? `cannot encode field ${type.typeName}.${field.name} to JSON`\n                    : `cannot encode message ${type.typeName} to JSON`;\n                const r = e instanceof Error ? e.message : String(e);\n                throw new Error(m + (r.length > 0 ? `: ${r}` : \"\"));\n            }\n            return json;\n        },\n        readScalar(type, json, longType) {\n            // The signature of our internal function has changed. For backwards-\n            // compatibility, we support the old form that is part of the public API\n            // through the interface JsonFormat.\n            return readScalar(type, json, longType !== null && longType !== void 0 ? longType : LongType.BIGINT, true);\n        },\n        writeScalar(type, value, emitDefaultValues) {\n            // The signature of our internal function has changed. For backwards-\n            // compatibility, we support the old form that is part of the public API\n            // through the interface JsonFormat.\n            if (value === undefined) {\n                return undefined;\n            }\n            if (emitDefaultValues || isScalarZeroValue(type, value)) {\n                return writeScalar(type, value);\n            }\n            return undefined;\n        },\n        debug: debugJsonValue,\n    };\n}\nfunction debugJsonValue(json) {\n    if (json === null) {\n        return \"null\";\n    }\n    switch (typeof json) {\n        case \"object\":\n            return Array.isArray(json) ? \"array\" : \"object\";\n        case \"string\":\n            return json.length > 100 ? \"string\" : `\"${json.split('\"').join('\\\\\"')}\"`;\n        default:\n            return String(json);\n    }\n}\n// Read a JSON value for a field.\n// The \"parentType\" argument is only used to provide context in errors.\nfunction readField(target, jsonValue, field, options, parentType) {\n    let localName = field.localName;\n    if (field.repeated) {\n        assert(field.kind != \"map\");\n        if (jsonValue === null) {\n            return;\n        }\n        if (!Array.isArray(jsonValue)) {\n            throw new Error(`cannot decode field ${parentType.typeName}.${field.name} from JSON: ${debugJsonValue(jsonValue)}`);\n        }\n        const targetArray = target[localName];\n        for (const jsonItem of jsonValue) {\n            if (jsonItem === null) {\n                throw new Error(`cannot decode field ${parentType.typeName}.${field.name} from JSON: ${debugJsonValue(jsonItem)}`);\n            }\n            switch (field.kind) {\n                case \"message\":\n                    targetArray.push(field.T.fromJson(jsonItem, options));\n                    break;\n                case \"enum\":\n                    const enumValue = readEnum(field.T, jsonItem, options.ignoreUnknownFields, true);\n                    if (enumValue !== tokenIgnoredUnknownEnum) {\n                        targetArray.push(enumValue);\n                    }\n                    break;\n                case \"scalar\":\n                    try {\n                        targetArray.push(readScalar(field.T, jsonItem, field.L, true));\n                    }\n                    catch (e) {\n                        let m = `cannot decode field ${parentType.typeName}.${field.name} from JSON: ${debugJsonValue(jsonItem)}`;\n                        if (e instanceof Error && e.message.length > 0) {\n                            m += `: ${e.message}`;\n                        }\n                        throw new Error(m);\n                    }\n                    break;\n            }\n        }\n    }\n    else if (field.kind == \"map\") {\n        if (jsonValue === null) {\n            return;\n        }\n        if (typeof jsonValue != \"object\" || Array.isArray(jsonValue)) {\n            throw new Error(`cannot decode field ${parentType.typeName}.${field.name} from JSON: ${debugJsonValue(jsonValue)}`);\n        }\n        const targetMap = target[localName];\n        for (const [jsonMapKey, jsonMapValue] of Object.entries(jsonValue)) {\n            if (jsonMapValue === null) {\n                throw new Error(`cannot decode field ${parentType.typeName}.${field.name} from JSON: map value null`);\n            }\n            let key;\n            try {\n                key = readMapKey(field.K, jsonMapKey);\n            }\n            catch (e) {\n                let m = `cannot decode map key for field ${parentType.typeName}.${field.name} from JSON: ${debugJsonValue(jsonValue)}`;\n                if (e instanceof Error && e.message.length > 0) {\n                    m += `: ${e.message}`;\n                }\n                throw new Error(m);\n            }\n            switch (field.V.kind) {\n                case \"message\":\n                    targetMap[key] = field.V.T.fromJson(jsonMapValue, options);\n                    break;\n                case \"enum\":\n                    const enumValue = readEnum(field.V.T, jsonMapValue, options.ignoreUnknownFields, true);\n                    if (enumValue !== tokenIgnoredUnknownEnum) {\n                        targetMap[key] = enumValue;\n                    }\n                    break;\n                case \"scalar\":\n                    try {\n                        targetMap[key] = readScalar(field.V.T, jsonMapValue, LongType.BIGINT, true);\n                    }\n                    catch (e) {\n                        let m = `cannot decode map value for field ${parentType.typeName}.${field.name} from JSON: ${debugJsonValue(jsonValue)}`;\n                        if (e instanceof Error && e.message.length > 0) {\n                            m += `: ${e.message}`;\n                        }\n                        throw new Error(m);\n                    }\n                    break;\n            }\n        }\n    }\n    else {\n        if (field.oneof) {\n            target = target[field.oneof.localName] = { case: localName };\n            localName = \"value\";\n        }\n        switch (field.kind) {\n            case \"message\":\n                const messageType = field.T;\n                if (jsonValue === null &&\n                    messageType.typeName != \"google.protobuf.Value\") {\n                    return;\n                }\n                let currentValue = target[localName];\n                if (isMessage(currentValue)) {\n                    currentValue.fromJson(jsonValue, options);\n                }\n                else {\n                    target[localName] = currentValue = messageType.fromJson(jsonValue, options);\n                    if (messageType.fieldWrapper && !field.oneof) {\n                        target[localName] =\n                            messageType.fieldWrapper.unwrapField(currentValue);\n                    }\n                }\n                break;\n            case \"enum\":\n                const enumValue = readEnum(field.T, jsonValue, options.ignoreUnknownFields, false);\n                switch (enumValue) {\n                    case tokenNull:\n                        clearField(field, target);\n                        break;\n                    case tokenIgnoredUnknownEnum:\n                        break;\n                    default:\n                        target[localName] = enumValue;\n                        break;\n                }\n                break;\n            case \"scalar\":\n                try {\n                    const scalarValue = readScalar(field.T, jsonValue, field.L, false);\n                    switch (scalarValue) {\n                        case tokenNull:\n                            clearField(field, target);\n                            break;\n                        default:\n                            target[localName] = scalarValue;\n                            break;\n                    }\n                }\n                catch (e) {\n                    let m = `cannot decode field ${parentType.typeName}.${field.name} from JSON: ${debugJsonValue(jsonValue)}`;\n                    if (e instanceof Error && e.message.length > 0) {\n                        m += `: ${e.message}`;\n                    }\n                    throw new Error(m);\n                }\n                break;\n        }\n    }\n}\nfunction readMapKey(type, json) {\n    if (type === ScalarType.BOOL) {\n        // eslint-disable-next-line @typescript-eslint/switch-exhaustiveness-check\n        switch (json) {\n            case \"true\":\n                json = true;\n                break;\n            case \"false\":\n                json = false;\n                break;\n        }\n    }\n    return readScalar(type, json, LongType.BIGINT, true).toString();\n}\nfunction readScalar(type, json, longType, nullAsZeroValue) {\n    if (json === null) {\n        if (nullAsZeroValue) {\n            return scalarZeroValue(type, longType);\n        }\n        return tokenNull;\n    }\n    // every valid case in the switch below returns, and every fall\n    // through is regarded as a failure.\n    switch (type) {\n        // float, double: JSON value will be a number or one of the special string values \"NaN\", \"Infinity\", and \"-Infinity\".\n        // Either numbers or strings are accepted. Exponent notation is also accepted.\n        case ScalarType.DOUBLE:\n        case ScalarType.FLOAT:\n            if (json === \"NaN\")\n                return Number.NaN;\n            if (json === \"Infinity\")\n                return Number.POSITIVE_INFINITY;\n            if (json === \"-Infinity\")\n                return Number.NEGATIVE_INFINITY;\n            if (json === \"\") {\n                // empty string is not a number\n                break;\n            }\n            if (typeof json == \"string\" && json.trim().length !== json.length) {\n                // extra whitespace\n                break;\n            }\n            if (typeof json != \"string\" && typeof json != \"number\") {\n                break;\n            }\n            const float = Number(json);\n            if (Number.isNaN(float)) {\n                // not a number\n                break;\n            }\n            if (!Number.isFinite(float)) {\n                // infinity and -infinity are handled by string representation above, so this is an error\n                break;\n            }\n            if (type == ScalarType.FLOAT)\n                assertFloat32(float);\n            return float;\n        // int32, fixed32, uint32: JSON value will be a decimal number. Either numbers or strings are accepted.\n        case ScalarType.INT32:\n        case ScalarType.FIXED32:\n        case ScalarType.SFIXED32:\n        case ScalarType.SINT32:\n        case ScalarType.UINT32:\n            let int32;\n            if (typeof json == \"number\")\n                int32 = json;\n            else if (typeof json == \"string\" && json.length > 0) {\n                if (json.trim().length === json.length)\n                    int32 = Number(json);\n            }\n            if (int32 === undefined)\n                break;\n            if (type == ScalarType.UINT32 || type == ScalarType.FIXED32)\n                assertUInt32(int32);\n            else\n                assertInt32(int32);\n            return int32;\n        // int64, fixed64, uint64: JSON value will be a decimal string. Either numbers or strings are accepted.\n        case ScalarType.INT64:\n        case ScalarType.SFIXED64:\n        case ScalarType.SINT64:\n            if (typeof json != \"number\" && typeof json != \"string\")\n                break;\n            const long = protoInt64.parse(json);\n            // eslint-disable-next-line @typescript-eslint/strict-boolean-expressions\n            return longType ? long.toString() : long;\n        case ScalarType.FIXED64:\n        case ScalarType.UINT64:\n            if (typeof json != \"number\" && typeof json != \"string\")\n                break;\n            const uLong = protoInt64.uParse(json);\n            // eslint-disable-next-line @typescript-eslint/strict-boolean-expressions\n            return longType ? uLong.toString() : uLong;\n        // bool:\n        case ScalarType.BOOL:\n            if (typeof json !== \"boolean\")\n                break;\n            return json;\n        // string:\n        case ScalarType.STRING:\n            if (typeof json !== \"string\") {\n                break;\n            }\n            // A string must always contain UTF-8 encoded or 7-bit ASCII.\n            // We validate with encodeURIComponent, which appears to be the fastest widely available option.\n            try {\n                encodeURIComponent(json);\n            }\n            catch (e) {\n                throw new Error(\"invalid UTF8\");\n            }\n            return json;\n        // bytes: JSON value will be the data encoded as a string using standard base64 encoding with paddings.\n        // Either standard or URL-safe base64 encoding with/without paddings are accepted.\n        case ScalarType.BYTES:\n            if (json === \"\")\n                return new Uint8Array(0);\n            if (typeof json !== \"string\")\n                break;\n            return protoBase64.dec(json);\n    }\n    throw new Error();\n}\nfunction readEnum(type, json, ignoreUnknownFields, nullAsZeroValue) {\n    if (json === null) {\n        if (type.typeName == \"google.protobuf.NullValue\") {\n            return 0; // google.protobuf.NullValue.NULL_VALUE = 0\n        }\n        return nullAsZeroValue ? type.values[0].no : tokenNull;\n    }\n    // eslint-disable-next-line @typescript-eslint/switch-exhaustiveness-check\n    switch (typeof json) {\n        case \"number\":\n            if (Number.isInteger(json)) {\n                return json;\n            }\n            break;\n        case \"string\":\n            const value = type.findName(json);\n            if (value !== undefined) {\n                return value.no;\n            }\n            if (ignoreUnknownFields) {\n                return tokenIgnoredUnknownEnum;\n            }\n            break;\n    }\n    throw new Error(`cannot decode enum ${type.typeName} from JSON: ${debugJsonValue(json)}`);\n}\n// Decide whether an unset field should be emitted with JSON write option `emitDefaultValues`\nfunction canEmitFieldDefaultValue(field) {\n    if (field.repeated || field.kind == \"map\") {\n        // maps are {}, repeated fields are []\n        return true;\n    }\n    if (field.oneof) {\n        // oneof fields are never emitted\n        return false;\n    }\n    if (field.kind == \"message\") {\n        // singular message field are allowed to emit JSON null, but we do not\n        return false;\n    }\n    // eslint-disable-next-line @typescript-eslint/strict-boolean-expressions\n    if (field.opt || field.req) {\n        // the field uses explicit presence, so we cannot emit a zero value\n        return false;\n    }\n    return true;\n}\nfunction writeField(field, value, options) {\n    if (field.kind == \"map\") {\n        assert(typeof value == \"object\" && value != null);\n        const jsonObj = {};\n        const entries = Object.entries(value);\n        switch (field.V.kind) {\n            case \"scalar\":\n                for (const [entryKey, entryValue] of entries) {\n                    jsonObj[entryKey.toString()] = writeScalar(field.V.T, entryValue); // JSON standard allows only (double quoted) string as property key\n                }\n                break;\n            case \"message\":\n                for (const [entryKey, entryValue] of entries) {\n                    // JSON standard allows only (double quoted) string as property key\n                    jsonObj[entryKey.toString()] = entryValue.toJson(options);\n                }\n                break;\n            case \"enum\":\n                const enumType = field.V.T;\n                for (const [entryKey, entryValue] of entries) {\n                    // JSON standard allows only (double quoted) string as property key\n                    jsonObj[entryKey.toString()] = writeEnum(enumType, entryValue, options.enumAsInteger);\n                }\n                break;\n        }\n        return options.emitDefaultValues || entries.length > 0\n            ? jsonObj\n            : undefined;\n    }\n    if (field.repeated) {\n        assert(Array.isArray(value));\n        const jsonArr = [];\n        switch (field.kind) {\n            case \"scalar\":\n                for (let i = 0; i < value.length; i++) {\n                    jsonArr.push(writeScalar(field.T, value[i]));\n                }\n                break;\n            case \"enum\":\n                for (let i = 0; i < value.length; i++) {\n                    jsonArr.push(writeEnum(field.T, value[i], options.enumAsInteger));\n                }\n                break;\n            case \"message\":\n                for (let i = 0; i < value.length; i++) {\n                    jsonArr.push(value[i].toJson(options));\n                }\n                break;\n        }\n        return options.emitDefaultValues || jsonArr.length > 0\n            ? jsonArr\n            : undefined;\n    }\n    switch (field.kind) {\n        case \"scalar\":\n            return writeScalar(field.T, value);\n        case \"enum\":\n            return writeEnum(field.T, value, options.enumAsInteger);\n        case \"message\":\n            return wrapField(field.T, value).toJson(options);\n    }\n}\nfunction writeEnum(type, value, enumAsInteger) {\n    var _a;\n    assert(typeof value == \"number\");\n    if (type.typeName == \"google.protobuf.NullValue\") {\n        return null;\n    }\n    if (enumAsInteger) {\n        return value;\n    }\n    const val = type.findNumber(value);\n    return (_a = val === null || val === void 0 ? void 0 : val.name) !== null && _a !== void 0 ? _a : value; // if we don't know the enum value, just return the number\n}\nfunction writeScalar(type, value) {\n    switch (type) {\n        // int32, fixed32, uint32: JSON value will be a decimal number. Either numbers or strings are accepted.\n        case ScalarType.INT32:\n        case ScalarType.SFIXED32:\n        case ScalarType.SINT32:\n        case ScalarType.FIXED32:\n        case ScalarType.UINT32:\n            assert(typeof value == \"number\");\n            return value;\n        // float, double: JSON value will be a number or one of the special string values \"NaN\", \"Infinity\", and \"-Infinity\".\n        // Either numbers or strings are accepted. Exponent notation is also accepted.\n        case ScalarType.FLOAT:\n        // assertFloat32(value);\n        case ScalarType.DOUBLE: // eslint-disable-line no-fallthrough\n            assert(typeof value == \"number\");\n            if (Number.isNaN(value))\n                return \"NaN\";\n            if (value === Number.POSITIVE_INFINITY)\n                return \"Infinity\";\n            if (value === Number.NEGATIVE_INFINITY)\n                return \"-Infinity\";\n            return value;\n        // string:\n        case ScalarType.STRING:\n            assert(typeof value == \"string\");\n            return value;\n        // bool:\n        case ScalarType.BOOL:\n            assert(typeof value == \"boolean\");\n            return value;\n        // JSON value will be a decimal string. Either numbers or strings are accepted.\n        case ScalarType.UINT64:\n        case ScalarType.FIXED64:\n        case ScalarType.INT64:\n        case ScalarType.SFIXED64:\n        case ScalarType.SINT64:\n            assert(typeof value == \"bigint\" ||\n                typeof value == \"string\" ||\n                typeof value == \"number\");\n            return value.toString();\n        // bytes: JSON value will be the data encoded as a string using standard base64 encoding with paddings.\n        // Either standard or URL-safe base64 encoding with/without paddings are accepted.\n        case ScalarType.BYTES:\n            assert(value instanceof Uint8Array);\n            return protoBase64.enc(value);\n    }\n}\n"], "names": [], "mappings": "AAAA,6CAA6C;AAC7C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;;;AAEjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;;;;;;;;;;;;;AACA,kMAAkM,GAClM,oCAAoC;AACpC,MAAM,mBAAmB;IACrB,qBAAqB;AACzB;AACA,2CAA2C;AAC3C,MAAM,oBAAoB;IACtB,mBAAmB;IACnB,eAAe;IACf,mBAAmB;IACnB,cAAc;AAClB;AACA,SAAS,gBAAgB,OAAO;IAC5B,OAAO,UAAU,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,mBAAmB,WAAW;AACnF;AACA,SAAS,iBAAiB,OAAO;IAC7B,OAAO,UAAU,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,oBAAoB,WAAW;AACpF;AACA,MAAM,YAAY;AAClB,MAAM,0BAA0B;AACzB,SAAS;IACZ,OAAO;QACH;QACA;QACA,aAAY,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO;YACpC,IAAI,QAAQ,QAAQ,MAAM,OAAO,CAAC,SAAS,OAAO,QAAQ,UAAU;gBAChE,MAAM,IAAI,MAAM,CAAC,sBAAsB,EAAE,KAAK,QAAQ,CAAC,YAAY,EAAE,eAAe,OAAO;YAC/F;YACA,UAAU,YAAY,QAAQ,YAAY,KAAK,IAAI,UAAU,IAAI;YACjE,MAAM,YAAY,IAAI;YACtB,MAAM,WAAW,QAAQ,YAAY;YACrC,KAAK,MAAM,CAAC,SAAS,UAAU,IAAI,OAAO,OAAO,CAAC,MAAO;gBACrD,MAAM,QAAQ,KAAK,MAAM,CAAC,YAAY,CAAC;gBACvC,IAAI,OAAO;oBACP,IAAI,MAAM,KAAK,EAAE;wBACb,IAAI,cAAc,QAAQ,MAAM,IAAI,IAAI,UAAU;4BAE9C;wBACJ;wBACA,MAAM,OAAO,UAAU,GAAG,CAAC,MAAM,KAAK;wBACtC,IAAI,SAAS,WAAW;4BACpB,MAAM,IAAI,MAAM,CAAC,sBAAsB,EAAE,KAAK,QAAQ,CAAC,qCAAqC,EAAE,MAAM,KAAK,CAAC,IAAI,CAAC,YAAY,EAAE,KAAK,IAAI,EAAE,QAAQ,CAAC,CAAC;wBACtJ;wBACA,UAAU,GAAG,CAAC,MAAM,KAAK,EAAE;oBAC/B;oBACA,UAAU,SAAS,WAAW,OAAO,SAAS;gBAClD,OACK;oBACD,IAAI,QAAQ;oBACZ,IAAI,CAAC,aAAa,QAAQ,aAAa,KAAK,IAAI,KAAK,IAAI,SAAS,aAAa,KAC3E,QAAQ,UAAU,CAAC,QACnB,QAAQ,QAAQ,CAAC,MAAM;wBACvB,MAAM,MAAM,SAAS,aAAa,CAAC,QAAQ,SAAS,CAAC,GAAG,QAAQ,MAAM,GAAG;wBACzE,IAAI,OAAO,IAAI,QAAQ,CAAC,QAAQ,IAAI,KAAK,QAAQ,EAAE;4BAC/C,QAAQ;4BACR,MAAM,CAAC,WAAW,IAAI,GAAG,CAAA,GAAA,0PAAA,CAAA,2BAAwB,AAAD,EAAE;4BAClD,UAAU,WAAW,WAAW,IAAI,KAAK,EAAE,SAAS;4BACpD,kEAAkE;4BAClE,uEAAuE;4BACvE,gBAAgB;4BAChB,CAAA,GAAA,0PAAA,CAAA,eAAY,AAAD,EAAE,SAAS,KAAK,OAAO;wBACtC;oBACJ;oBACA,IAAI,CAAC,SAAS,CAAC,QAAQ,mBAAmB,EAAE;wBACxC,MAAM,IAAI,MAAM,CAAC,sBAAsB,EAAE,KAAK,QAAQ,CAAC,iBAAiB,EAAE,QAAQ,YAAY,CAAC;oBACnG;gBACJ;YACJ;YACA,OAAO;QACX;QACA,cAAa,OAAO,EAAE,OAAO;YACzB,MAAM,OAAO,QAAQ,OAAO;YAC5B,MAAM,OAAO,CAAC;YACd,IAAI;YACJ,IAAI;gBACA,KAAK,SAAS,KAAK,MAAM,CAAC,QAAQ,GAAI;oBAClC,IAAI,CAAC,CAAA,GAAA,uPAAA,CAAA,aAAU,AAAD,EAAE,OAAO,UAAU;wBAC7B,yEAAyE;wBACzE,IAAI,MAAM,GAAG,EAAE;4BACX,MAAM,CAAC,sBAAsB,CAAC;wBAClC;wBACA,IAAI,CAAC,QAAQ,iBAAiB,EAAE;4BAC5B;wBACJ;wBACA,IAAI,CAAC,yBAAyB,QAAQ;4BAClC;wBACJ;oBACJ;oBACA,MAAM,QAAQ,MAAM,KAAK,GACnB,OAAO,CAAC,MAAM,KAAK,CAAC,SAAS,CAAC,CAAC,KAAK,GACpC,OAAO,CAAC,MAAM,SAAS,CAAC;oBAC9B,MAAM,YAAY,WAAW,OAAO,OAAO;oBAC3C,IAAI,cAAc,WAAW;wBACzB,IAAI,CAAC,QAAQ,iBAAiB,GAAG,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,GACzD;oBACR;gBACJ;gBACA,MAAM,WAAW,QAAQ,YAAY;gBACrC,IAAI,aAAa,QAAQ,aAAa,KAAK,IAAI,KAAK,IAAI,SAAS,gBAAgB,EAAE;oBAC/E,KAAK,MAAM,MAAM,KAAK,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,SAAU;wBAC1D,MAAM,MAAM,SAAS,gBAAgB,CAAC,KAAK,QAAQ,EAAE,GAAG,EAAE;wBAC1D,IAAI,OAAO,CAAA,GAAA,0PAAA,CAAA,eAAY,AAAD,EAAE,SAAS,MAAM;4BACnC,iFAAiF;4BACjF,sCAAsC;4BACtC,MAAM,QAAQ,CAAA,GAAA,0PAAA,CAAA,eAAY,AAAD,EAAE,SAAS,KAAK;4BACzC,MAAM,YAAY,WAAW,IAAI,KAAK,EAAE,OAAO;4BAC/C,IAAI,cAAc,WAAW;gCACzB,IAAI,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,GAAG;4BAC/B;wBACJ;oBACJ;gBACJ;YACJ,EACA,OAAO,GAAG;gBACN,MAAM,IAAI,QACJ,CAAC,oBAAoB,EAAE,KAAK,QAAQ,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,QAAQ,CAAC,GAC5D,CAAC,sBAAsB,EAAE,KAAK,QAAQ,CAAC,QAAQ,CAAC;gBACtD,MAAM,IAAI,aAAa,QAAQ,EAAE,OAAO,GAAG,OAAO;gBAClD,MAAM,IAAI,MAAM,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC,EAAE,EAAE,GAAG,GAAG,EAAE;YACrD;YACA,OAAO;QACX;QACA,YAAW,IAAI,EAAE,IAAI,EAAE,QAAQ;YAC3B,qEAAqE;YACrE,wEAAwE;YACxE,oCAAoC;YACpC,OAAO,WAAW,MAAM,MAAM,aAAa,QAAQ,aAAa,KAAK,IAAI,WAAW,2OAAA,CAAA,WAAQ,CAAC,MAAM,EAAE;QACzG;QACA,aAAY,IAAI,EAAE,KAAK,EAAE,iBAAiB;YACtC,qEAAqE;YACrE,wEAAwE;YACxE,oCAAoC;YACpC,IAAI,UAAU,WAAW;gBACrB,OAAO;YACX;YACA,IAAI,qBAAqB,CAAA,GAAA,uPAAA,CAAA,oBAAiB,AAAD,EAAE,MAAM,QAAQ;gBACrD,OAAO,YAAY,MAAM;YAC7B;YACA,OAAO;QACX;QACA,OAAO;IACX;AACJ;AACA,SAAS,eAAe,IAAI;IACxB,IAAI,SAAS,MAAM;QACf,OAAO;IACX;IACA,OAAQ,OAAO;QACX,KAAK;YACD,OAAO,MAAM,OAAO,CAAC,QAAQ,UAAU;QAC3C,KAAK;YACD,OAAO,KAAK,MAAM,GAAG,MAAM,WAAW,CAAC,CAAC,EAAE,KAAK,KAAK,CAAC,KAAK,IAAI,CAAC,OAAO,CAAC,CAAC;QAC5E;YACI,OAAO,OAAO;IACtB;AACJ;AACA,iCAAiC;AACjC,uEAAuE;AACvE,SAAS,UAAU,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,UAAU;IAC5D,IAAI,YAAY,MAAM,SAAS;IAC/B,IAAI,MAAM,QAAQ,EAAE;QAChB,CAAA,GAAA,sPAAA,CAAA,SAAM,AAAD,EAAE,MAAM,IAAI,IAAI;QACrB,IAAI,cAAc,MAAM;YACpB;QACJ;QACA,IAAI,CAAC,MAAM,OAAO,CAAC,YAAY;YAC3B,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,WAAW,QAAQ,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,YAAY,EAAE,eAAe,YAAY;QACtH;QACA,MAAM,cAAc,MAAM,CAAC,UAAU;QACrC,KAAK,MAAM,YAAY,UAAW;YAC9B,IAAI,aAAa,MAAM;gBACnB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,WAAW,QAAQ,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,YAAY,EAAE,eAAe,WAAW;YACrH;YACA,OAAQ,MAAM,IAAI;gBACd,KAAK;oBACD,YAAY,IAAI,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,UAAU;oBAC5C;gBACJ,KAAK;oBACD,MAAM,YAAY,SAAS,MAAM,CAAC,EAAE,UAAU,QAAQ,mBAAmB,EAAE;oBAC3E,IAAI,cAAc,yBAAyB;wBACvC,YAAY,IAAI,CAAC;oBACrB;oBACA;gBACJ,KAAK;oBACD,IAAI;wBACA,YAAY,IAAI,CAAC,WAAW,MAAM,CAAC,EAAE,UAAU,MAAM,CAAC,EAAE;oBAC5D,EACA,OAAO,GAAG;wBACN,IAAI,IAAI,CAAC,oBAAoB,EAAE,WAAW,QAAQ,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,YAAY,EAAE,eAAe,WAAW;wBACzG,IAAI,aAAa,SAAS,EAAE,OAAO,CAAC,MAAM,GAAG,GAAG;4BAC5C,KAAK,CAAC,EAAE,EAAE,EAAE,OAAO,EAAE;wBACzB;wBACA,MAAM,IAAI,MAAM;oBACpB;oBACA;YACR;QACJ;IACJ,OACK,IAAI,MAAM,IAAI,IAAI,OAAO;QAC1B,IAAI,cAAc,MAAM;YACpB;QACJ;QACA,IAAI,OAAO,aAAa,YAAY,MAAM,OAAO,CAAC,YAAY;YAC1D,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,WAAW,QAAQ,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,YAAY,EAAE,eAAe,YAAY;QACtH;QACA,MAAM,YAAY,MAAM,CAAC,UAAU;QACnC,KAAK,MAAM,CAAC,YAAY,aAAa,IAAI,OAAO,OAAO,CAAC,WAAY;YAChE,IAAI,iBAAiB,MAAM;gBACvB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,WAAW,QAAQ,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,0BAA0B,CAAC;YACxG;YACA,IAAI;YACJ,IAAI;gBACA,MAAM,WAAW,MAAM,CAAC,EAAE;YAC9B,EACA,OAAO,GAAG;gBACN,IAAI,IAAI,CAAC,gCAAgC,EAAE,WAAW,QAAQ,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,YAAY,EAAE,eAAe,YAAY;gBACtH,IAAI,aAAa,SAAS,EAAE,OAAO,CAAC,MAAM,GAAG,GAAG;oBAC5C,KAAK,CAAC,EAAE,EAAE,EAAE,OAAO,EAAE;gBACzB;gBACA,MAAM,IAAI,MAAM;YACpB;YACA,OAAQ,MAAM,CAAC,CAAC,IAAI;gBAChB,KAAK;oBACD,SAAS,CAAC,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,cAAc;oBAClD;gBACJ,KAAK;oBACD,MAAM,YAAY,SAAS,MAAM,CAAC,CAAC,CAAC,EAAE,cAAc,QAAQ,mBAAmB,EAAE;oBACjF,IAAI,cAAc,yBAAyB;wBACvC,SAAS,CAAC,IAAI,GAAG;oBACrB;oBACA;gBACJ,KAAK;oBACD,IAAI;wBACA,SAAS,CAAC,IAAI,GAAG,WAAW,MAAM,CAAC,CAAC,CAAC,EAAE,cAAc,2OAAA,CAAA,WAAQ,CAAC,MAAM,EAAE;oBAC1E,EACA,OAAO,GAAG;wBACN,IAAI,IAAI,CAAC,kCAAkC,EAAE,WAAW,QAAQ,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,YAAY,EAAE,eAAe,YAAY;wBACxH,IAAI,aAAa,SAAS,EAAE,OAAO,CAAC,MAAM,GAAG,GAAG;4BAC5C,KAAK,CAAC,EAAE,EAAE,EAAE,OAAO,EAAE;wBACzB;wBACA,MAAM,IAAI,MAAM;oBACpB;oBACA;YACR;QACJ;IACJ,OACK;QACD,IAAI,MAAM,KAAK,EAAE;YACb,SAAS,MAAM,CAAC,MAAM,KAAK,CAAC,SAAS,CAAC,GAAG;gBAAE,MAAM;YAAU;YAC3D,YAAY;QAChB;QACA,OAAQ,MAAM,IAAI;YACd,KAAK;gBACD,MAAM,cAAc,MAAM,CAAC;gBAC3B,IAAI,cAAc,QACd,YAAY,QAAQ,IAAI,yBAAyB;oBACjD;gBACJ;gBACA,IAAI,eAAe,MAAM,CAAC,UAAU;gBACpC,IAAI,CAAA,GAAA,kPAAA,CAAA,YAAS,AAAD,EAAE,eAAe;oBACzB,aAAa,QAAQ,CAAC,WAAW;gBACrC,OACK;oBACD,MAAM,CAAC,UAAU,GAAG,eAAe,YAAY,QAAQ,CAAC,WAAW;oBACnE,IAAI,YAAY,YAAY,IAAI,CAAC,MAAM,KAAK,EAAE;wBAC1C,MAAM,CAAC,UAAU,GACb,YAAY,YAAY,CAAC,WAAW,CAAC;oBAC7C;gBACJ;gBACA;YACJ,KAAK;gBACD,MAAM,YAAY,SAAS,MAAM,CAAC,EAAE,WAAW,QAAQ,mBAAmB,EAAE;gBAC5E,OAAQ;oBACJ,KAAK;wBACD,CAAA,GAAA,uPAAA,CAAA,aAAU,AAAD,EAAE,OAAO;wBAClB;oBACJ,KAAK;wBACD;oBACJ;wBACI,MAAM,CAAC,UAAU,GAAG;wBACpB;gBACR;gBACA;YACJ,KAAK;gBACD,IAAI;oBACA,MAAM,cAAc,WAAW,MAAM,CAAC,EAAE,WAAW,MAAM,CAAC,EAAE;oBAC5D,OAAQ;wBACJ,KAAK;4BACD,CAAA,GAAA,uPAAA,CAAA,aAAU,AAAD,EAAE,OAAO;4BAClB;wBACJ;4BACI,MAAM,CAAC,UAAU,GAAG;4BACpB;oBACR;gBACJ,EACA,OAAO,GAAG;oBACN,IAAI,IAAI,CAAC,oBAAoB,EAAE,WAAW,QAAQ,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,YAAY,EAAE,eAAe,YAAY;oBAC1G,IAAI,aAAa,SAAS,EAAE,OAAO,CAAC,MAAM,GAAG,GAAG;wBAC5C,KAAK,CAAC,EAAE,EAAE,EAAE,OAAO,EAAE;oBACzB;oBACA,MAAM,IAAI,MAAM;gBACpB;gBACA;QACR;IACJ;AACJ;AACA,SAAS,WAAW,IAAI,EAAE,IAAI;IAC1B,IAAI,SAAS,2OAAA,CAAA,aAAU,CAAC,IAAI,EAAE;QAC1B,0EAA0E;QAC1E,OAAQ;YACJ,KAAK;gBACD,OAAO;gBACP;YACJ,KAAK;gBACD,OAAO;gBACP;QACR;IACJ;IACA,OAAO,WAAW,MAAM,MAAM,2OAAA,CAAA,WAAQ,CAAC,MAAM,EAAE,MAAM,QAAQ;AACjE;AACA,SAAS,WAAW,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,eAAe;IACrD,IAAI,SAAS,MAAM;QACf,IAAI,iBAAiB;YACjB,OAAO,CAAA,GAAA,uPAAA,CAAA,kBAAe,AAAD,EAAE,MAAM;QACjC;QACA,OAAO;IACX;IACA,+DAA+D;IAC/D,oCAAoC;IACpC,OAAQ;QACJ,qHAAqH;QACrH,8EAA8E;QAC9E,KAAK,2OAAA,CAAA,aAAU,CAAC,MAAM;QACtB,KAAK,2OAAA,CAAA,aAAU,CAAC,KAAK;YACjB,IAAI,SAAS,OACT,OAAO,OAAO,GAAG;YACrB,IAAI,SAAS,YACT,OAAO,OAAO,iBAAiB;YACnC,IAAI,SAAS,aACT,OAAO,OAAO,iBAAiB;YACnC,IAAI,SAAS,IAAI;gBAEb;YACJ;YACA,IAAI,OAAO,QAAQ,YAAY,KAAK,IAAI,GAAG,MAAM,KAAK,KAAK,MAAM,EAAE;gBAE/D;YACJ;YACA,IAAI,OAAO,QAAQ,YAAY,OAAO,QAAQ,UAAU;gBACpD;YACJ;YACA,MAAM,QAAQ,OAAO;YACrB,IAAI,OAAO,KAAK,CAAC,QAAQ;gBAErB;YACJ;YACA,IAAI,CAAC,OAAO,QAAQ,CAAC,QAAQ;gBAEzB;YACJ;YACA,IAAI,QAAQ,2OAAA,CAAA,aAAU,CAAC,KAAK,EACxB,CAAA,GAAA,sPAAA,CAAA,gBAAa,AAAD,EAAE;YAClB,OAAO;QACX,uGAAuG;QACvG,KAAK,2OAAA,CAAA,aAAU,CAAC,KAAK;QACrB,KAAK,2OAAA,CAAA,aAAU,CAAC,OAAO;QACvB,KAAK,2OAAA,CAAA,aAAU,CAAC,QAAQ;QACxB,KAAK,2OAAA,CAAA,aAAU,CAAC,MAAM;QACtB,KAAK,2OAAA,CAAA,aAAU,CAAC,MAAM;YAClB,IAAI;YACJ,IAAI,OAAO,QAAQ,UACf,QAAQ;iBACP,IAAI,OAAO,QAAQ,YAAY,KAAK,MAAM,GAAG,GAAG;gBACjD,IAAI,KAAK,IAAI,GAAG,MAAM,KAAK,KAAK,MAAM,EAClC,QAAQ,OAAO;YACvB;YACA,IAAI,UAAU,WACV;YACJ,IAAI,QAAQ,2OAAA,CAAA,aAAU,CAAC,MAAM,IAAI,QAAQ,2OAAA,CAAA,aAAU,CAAC,OAAO,EACvD,CAAA,GAAA,sPAAA,CAAA,eAAY,AAAD,EAAE;iBAEb,CAAA,GAAA,sPAAA,CAAA,cAAW,AAAD,EAAE;YAChB,OAAO;QACX,uGAAuG;QACvG,KAAK,2OAAA,CAAA,aAAU,CAAC,KAAK;QACrB,KAAK,2OAAA,CAAA,aAAU,CAAC,QAAQ;QACxB,KAAK,2OAAA,CAAA,aAAU,CAAC,MAAM;YAClB,IAAI,OAAO,QAAQ,YAAY,OAAO,QAAQ,UAC1C;YACJ,MAAM,OAAO,mPAAA,CAAA,aAAU,CAAC,KAAK,CAAC;YAC9B,yEAAyE;YACzE,OAAO,WAAW,KAAK,QAAQ,KAAK;QACxC,KAAK,2OAAA,CAAA,aAAU,CAAC,OAAO;QACvB,KAAK,2OAAA,CAAA,aAAU,CAAC,MAAM;YAClB,IAAI,OAAO,QAAQ,YAAY,OAAO,QAAQ,UAC1C;YACJ,MAAM,QAAQ,mPAAA,CAAA,aAAU,CAAC,MAAM,CAAC;YAChC,yEAAyE;YACzE,OAAO,WAAW,MAAM,QAAQ,KAAK;QACzC,QAAQ;QACR,KAAK,2OAAA,CAAA,aAAU,CAAC,IAAI;YAChB,IAAI,OAAO,SAAS,WAChB;YACJ,OAAO;QACX,UAAU;QACV,KAAK,2OAAA,CAAA,aAAU,CAAC,MAAM;YAClB,IAAI,OAAO,SAAS,UAAU;gBAC1B;YACJ;YACA,6DAA6D;YAC7D,gGAAgG;YAChG,IAAI;gBACA,mBAAmB;YACvB,EACA,OAAO,GAAG;gBACN,MAAM,IAAI,MAAM;YACpB;YACA,OAAO;QACX,uGAAuG;QACvG,kFAAkF;QAClF,KAAK,2OAAA,CAAA,aAAU,CAAC,KAAK;YACjB,IAAI,SAAS,IACT,OAAO,IAAI,WAAW;YAC1B,IAAI,OAAO,SAAS,UAChB;YACJ,OAAO,oPAAA,CAAA,cAAW,CAAC,GAAG,CAAC;IAC/B;IACA,MAAM,IAAI;AACd;AACA,SAAS,SAAS,IAAI,EAAE,IAAI,EAAE,mBAAmB,EAAE,eAAe;IAC9D,IAAI,SAAS,MAAM;QACf,IAAI,KAAK,QAAQ,IAAI,6BAA6B;YAC9C,OAAO,GAAG,2CAA2C;QACzD;QACA,OAAO,kBAAkB,KAAK,MAAM,CAAC,EAAE,CAAC,EAAE,GAAG;IACjD;IACA,0EAA0E;IAC1E,OAAQ,OAAO;QACX,KAAK;YACD,IAAI,OAAO,SAAS,CAAC,OAAO;gBACxB,OAAO;YACX;YACA;QACJ,KAAK;YACD,MAAM,QAAQ,KAAK,QAAQ,CAAC;YAC5B,IAAI,UAAU,WAAW;gBACrB,OAAO,MAAM,EAAE;YACnB;YACA,IAAI,qBAAqB;gBACrB,OAAO;YACX;YACA;IACR;IACA,MAAM,IAAI,MAAM,CAAC,mBAAmB,EAAE,KAAK,QAAQ,CAAC,YAAY,EAAE,eAAe,OAAO;AAC5F;AACA,6FAA6F;AAC7F,SAAS,yBAAyB,KAAK;IACnC,IAAI,MAAM,QAAQ,IAAI,MAAM,IAAI,IAAI,OAAO;QACvC,sCAAsC;QACtC,OAAO;IACX;IACA,IAAI,MAAM,KAAK,EAAE;QACb,iCAAiC;QACjC,OAAO;IACX;IACA,IAAI,MAAM,IAAI,IAAI,WAAW;QACzB,sEAAsE;QACtE,OAAO;IACX;IACA,yEAAyE;IACzE,IAAI,MAAM,GAAG,IAAI,MAAM,GAAG,EAAE;QACxB,mEAAmE;QACnE,OAAO;IACX;IACA,OAAO;AACX;AACA,SAAS,WAAW,KAAK,EAAE,KAAK,EAAE,OAAO;IACrC,IAAI,MAAM,IAAI,IAAI,OAAO;QACrB,CAAA,GAAA,sPAAA,CAAA,SAAM,AAAD,EAAE,OAAO,SAAS,YAAY,SAAS;QAC5C,MAAM,UAAU,CAAC;QACjB,MAAM,UAAU,OAAO,OAAO,CAAC;QAC/B,OAAQ,MAAM,CAAC,CAAC,IAAI;YAChB,KAAK;gBACD,KAAK,MAAM,CAAC,UAAU,WAAW,IAAI,QAAS;oBAC1C,OAAO,CAAC,SAAS,QAAQ,GAAG,GAAG,YAAY,MAAM,CAAC,CAAC,CAAC,EAAE,aAAa,mEAAmE;gBAC1I;gBACA;YACJ,KAAK;gBACD,KAAK,MAAM,CAAC,UAAU,WAAW,IAAI,QAAS;oBAC1C,mEAAmE;oBACnE,OAAO,CAAC,SAAS,QAAQ,GAAG,GAAG,WAAW,MAAM,CAAC;gBACrD;gBACA;YACJ,KAAK;gBACD,MAAM,WAAW,MAAM,CAAC,CAAC,CAAC;gBAC1B,KAAK,MAAM,CAAC,UAAU,WAAW,IAAI,QAAS;oBAC1C,mEAAmE;oBACnE,OAAO,CAAC,SAAS,QAAQ,GAAG,GAAG,UAAU,UAAU,YAAY,QAAQ,aAAa;gBACxF;gBACA;QACR;QACA,OAAO,QAAQ,iBAAiB,IAAI,QAAQ,MAAM,GAAG,IAC/C,UACA;IACV;IACA,IAAI,MAAM,QAAQ,EAAE;QAChB,CAAA,GAAA,sPAAA,CAAA,SAAM,AAAD,EAAE,MAAM,OAAO,CAAC;QACrB,MAAM,UAAU,EAAE;QAClB,OAAQ,MAAM,IAAI;YACd,KAAK;gBACD,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;oBACnC,QAAQ,IAAI,CAAC,YAAY,MAAM,CAAC,EAAE,KAAK,CAAC,EAAE;gBAC9C;gBACA;YACJ,KAAK;gBACD,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;oBACnC,QAAQ,IAAI,CAAC,UAAU,MAAM,CAAC,EAAE,KAAK,CAAC,EAAE,EAAE,QAAQ,aAAa;gBACnE;gBACA;YACJ,KAAK;gBACD,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;oBACnC,QAAQ,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,MAAM,CAAC;gBACjC;gBACA;QACR;QACA,OAAO,QAAQ,iBAAiB,IAAI,QAAQ,MAAM,GAAG,IAC/C,UACA;IACV;IACA,OAAQ,MAAM,IAAI;QACd,KAAK;YACD,OAAO,YAAY,MAAM,CAAC,EAAE;QAChC,KAAK;YACD,OAAO,UAAU,MAAM,CAAC,EAAE,OAAO,QAAQ,aAAa;QAC1D,KAAK;YACD,OAAO,CAAA,GAAA,gQAAA,CAAA,YAAS,AAAD,EAAE,MAAM,CAAC,EAAE,OAAO,MAAM,CAAC;IAChD;AACJ;AACA,SAAS,UAAU,IAAI,EAAE,KAAK,EAAE,aAAa;IACzC,IAAI;IACJ,CAAA,GAAA,sPAAA,CAAA,SAAM,AAAD,EAAE,OAAO,SAAS;IACvB,IAAI,KAAK,QAAQ,IAAI,6BAA6B;QAC9C,OAAO;IACX;IACA,IAAI,eAAe;QACf,OAAO;IACX;IACA,MAAM,MAAM,KAAK,UAAU,CAAC;IAC5B,OAAO,CAAC,KAAK,QAAQ,QAAQ,QAAQ,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,OAAO,0DAA0D;AACvK;AACA,SAAS,YAAY,IAAI,EAAE,KAAK;IAC5B,OAAQ;QACJ,uGAAuG;QACvG,KAAK,2OAAA,CAAA,aAAU,CAAC,KAAK;QACrB,KAAK,2OAAA,CAAA,aAAU,CAAC,QAAQ;QACxB,KAAK,2OAAA,CAAA,aAAU,CAAC,MAAM;QACtB,KAAK,2OAAA,CAAA,aAAU,CAAC,OAAO;QACvB,KAAK,2OAAA,CAAA,aAAU,CAAC,MAAM;YAClB,CAAA,GAAA,sPAAA,CAAA,SAAM,AAAD,EAAE,OAAO,SAAS;YACvB,OAAO;QACX,qHAAqH;QACrH,8EAA8E;QAC9E,KAAK,2OAAA,CAAA,aAAU,CAAC,KAAK;QACrB,wBAAwB;QACxB,KAAK,2OAAA,CAAA,aAAU,CAAC,MAAM;YAClB,CAAA,GAAA,sPAAA,CAAA,SAAM,AAAD,EAAE,OAAO,SAAS;YACvB,IAAI,OAAO,KAAK,CAAC,QACb,OAAO;YACX,IAAI,UAAU,OAAO,iBAAiB,EAClC,OAAO;YACX,IAAI,UAAU,OAAO,iBAAiB,EAClC,OAAO;YACX,OAAO;QACX,UAAU;QACV,KAAK,2OAAA,CAAA,aAAU,CAAC,MAAM;YAClB,CAAA,GAAA,sPAAA,CAAA,SAAM,AAAD,EAAE,OAAO,SAAS;YACvB,OAAO;QACX,QAAQ;QACR,KAAK,2OAAA,CAAA,aAAU,CAAC,IAAI;YAChB,CAAA,GAAA,sPAAA,CAAA,SAAM,AAAD,EAAE,OAAO,SAAS;YACvB,OAAO;QACX,+EAA+E;QAC/E,KAAK,2OAAA,CAAA,aAAU,CAAC,MAAM;QACtB,KAAK,2OAAA,CAAA,aAAU,CAAC,OAAO;QACvB,KAAK,2OAAA,CAAA,aAAU,CAAC,KAAK;QACrB,KAAK,2OAAA,CAAA,aAAU,CAAC,QAAQ;QACxB,KAAK,2OAAA,CAAA,aAAU,CAAC,MAAM;YAClB,CAAA,GAAA,sPAAA,CAAA,SAAM,AAAD,EAAE,OAAO,SAAS,YACnB,OAAO,SAAS,YAChB,OAAO,SAAS;YACpB,OAAO,MAAM,QAAQ;QACzB,uGAAuG;QACvG,kFAAkF;QAClF,KAAK,2OAAA,CAAA,aAAU,CAAC,KAAK;YACjB,CAAA,GAAA,sPAAA,CAAA,SAAM,AAAD,EAAE,iBAAiB;YACxB,OAAO,oPAAA,CAAA,cAAW,CAAC,GAAG,CAAC;IAC/B;AACJ", "ignoreList": [0]}}, {"offset": {"line": 1956, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@bufbuild+protobuf@1.10.1/node_modules/@bufbuild/protobuf/dist/esm/binary-encoding.js"], "sourcesContent": ["// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nimport { varint32read, varint32write, varint64read, varint64write, } from \"./google/varint.js\";\nimport { assertFloat32, assertInt32, assertUInt32 } from \"./private/assert.js\";\nimport { protoInt64 } from \"./proto-int64.js\";\n/* eslint-disable prefer-const,no-case-declarations,@typescript-eslint/restrict-plus-operands */\n/**\n * Protobuf binary format wire types.\n *\n * A wire type provides just enough information to find the length of the\n * following value.\n *\n * See https://developers.google.com/protocol-buffers/docs/encoding#structure\n */\nexport var WireType;\n(function (WireType) {\n    /**\n     * Used for int32, int64, uint32, uint64, sint32, sint64, bool, enum\n     */\n    WireType[WireType[\"Varint\"] = 0] = \"Varint\";\n    /**\n     * Used for fixed64, sfixed64, double.\n     * Always 8 bytes with little-endian byte order.\n     */\n    WireType[WireType[\"Bit64\"] = 1] = \"Bit64\";\n    /**\n     * Used for string, bytes, embedded messages, packed repeated fields\n     *\n     * Only repeated numeric types (types which use the varint, 32-bit,\n     * or 64-bit wire types) can be packed. In proto3, such fields are\n     * packed by default.\n     */\n    WireType[WireType[\"LengthDelimited\"] = 2] = \"LengthDelimited\";\n    /**\n     * Start of a tag-delimited aggregate, such as a proto2 group, or a message\n     * in editions with message_encoding = DELIMITED.\n     */\n    WireType[WireType[\"StartGroup\"] = 3] = \"StartGroup\";\n    /**\n     * End of a tag-delimited aggregate.\n     */\n    WireType[WireType[\"EndGroup\"] = 4] = \"EndGroup\";\n    /**\n     * Used for fixed32, sfixed32, float.\n     * Always 4 bytes with little-endian byte order.\n     */\n    WireType[WireType[\"Bit32\"] = 5] = \"Bit32\";\n})(WireType || (WireType = {}));\nexport class BinaryWriter {\n    constructor(textEncoder) {\n        /**\n         * Previous fork states.\n         */\n        this.stack = [];\n        this.textEncoder = textEncoder !== null && textEncoder !== void 0 ? textEncoder : new TextEncoder();\n        this.chunks = [];\n        this.buf = [];\n    }\n    /**\n     * Return all bytes written and reset this writer.\n     */\n    finish() {\n        this.chunks.push(new Uint8Array(this.buf)); // flush the buffer\n        let len = 0;\n        for (let i = 0; i < this.chunks.length; i++)\n            len += this.chunks[i].length;\n        let bytes = new Uint8Array(len);\n        let offset = 0;\n        for (let i = 0; i < this.chunks.length; i++) {\n            bytes.set(this.chunks[i], offset);\n            offset += this.chunks[i].length;\n        }\n        this.chunks = [];\n        return bytes;\n    }\n    /**\n     * Start a new fork for length-delimited data like a message\n     * or a packed repeated field.\n     *\n     * Must be joined later with `join()`.\n     */\n    fork() {\n        this.stack.push({ chunks: this.chunks, buf: this.buf });\n        this.chunks = [];\n        this.buf = [];\n        return this;\n    }\n    /**\n     * Join the last fork. Write its length and bytes, then\n     * return to the previous state.\n     */\n    join() {\n        // get chunk of fork\n        let chunk = this.finish();\n        // restore previous state\n        let prev = this.stack.pop();\n        if (!prev)\n            throw new Error(\"invalid state, fork stack empty\");\n        this.chunks = prev.chunks;\n        this.buf = prev.buf;\n        // write length of chunk as varint\n        this.uint32(chunk.byteLength);\n        return this.raw(chunk);\n    }\n    /**\n     * Writes a tag (field number and wire type).\n     *\n     * Equivalent to `uint32( (fieldNo << 3 | type) >>> 0 )`.\n     *\n     * Generated code should compute the tag ahead of time and call `uint32()`.\n     */\n    tag(fieldNo, type) {\n        return this.uint32(((fieldNo << 3) | type) >>> 0);\n    }\n    /**\n     * Write a chunk of raw bytes.\n     */\n    raw(chunk) {\n        if (this.buf.length) {\n            this.chunks.push(new Uint8Array(this.buf));\n            this.buf = [];\n        }\n        this.chunks.push(chunk);\n        return this;\n    }\n    /**\n     * Write a `uint32` value, an unsigned 32 bit varint.\n     */\n    uint32(value) {\n        assertUInt32(value);\n        // write value as varint 32, inlined for speed\n        while (value > 0x7f) {\n            this.buf.push((value & 0x7f) | 0x80);\n            value = value >>> 7;\n        }\n        this.buf.push(value);\n        return this;\n    }\n    /**\n     * Write a `int32` value, a signed 32 bit varint.\n     */\n    int32(value) {\n        assertInt32(value);\n        varint32write(value, this.buf);\n        return this;\n    }\n    /**\n     * Write a `bool` value, a variant.\n     */\n    bool(value) {\n        this.buf.push(value ? 1 : 0);\n        return this;\n    }\n    /**\n     * Write a `bytes` value, length-delimited arbitrary data.\n     */\n    bytes(value) {\n        this.uint32(value.byteLength); // write length of chunk as varint\n        return this.raw(value);\n    }\n    /**\n     * Write a `string` value, length-delimited data converted to UTF-8 text.\n     */\n    string(value) {\n        let chunk = this.textEncoder.encode(value);\n        this.uint32(chunk.byteLength); // write length of chunk as varint\n        return this.raw(chunk);\n    }\n    /**\n     * Write a `float` value, 32-bit floating point number.\n     */\n    float(value) {\n        assertFloat32(value);\n        let chunk = new Uint8Array(4);\n        new DataView(chunk.buffer).setFloat32(0, value, true);\n        return this.raw(chunk);\n    }\n    /**\n     * Write a `double` value, a 64-bit floating point number.\n     */\n    double(value) {\n        let chunk = new Uint8Array(8);\n        new DataView(chunk.buffer).setFloat64(0, value, true);\n        return this.raw(chunk);\n    }\n    /**\n     * Write a `fixed32` value, an unsigned, fixed-length 32-bit integer.\n     */\n    fixed32(value) {\n        assertUInt32(value);\n        let chunk = new Uint8Array(4);\n        new DataView(chunk.buffer).setUint32(0, value, true);\n        return this.raw(chunk);\n    }\n    /**\n     * Write a `sfixed32` value, a signed, fixed-length 32-bit integer.\n     */\n    sfixed32(value) {\n        assertInt32(value);\n        let chunk = new Uint8Array(4);\n        new DataView(chunk.buffer).setInt32(0, value, true);\n        return this.raw(chunk);\n    }\n    /**\n     * Write a `sint32` value, a signed, zigzag-encoded 32-bit varint.\n     */\n    sint32(value) {\n        assertInt32(value);\n        // zigzag encode\n        value = ((value << 1) ^ (value >> 31)) >>> 0;\n        varint32write(value, this.buf);\n        return this;\n    }\n    /**\n     * Write a `fixed64` value, a signed, fixed-length 64-bit integer.\n     */\n    sfixed64(value) {\n        let chunk = new Uint8Array(8), view = new DataView(chunk.buffer), tc = protoInt64.enc(value);\n        view.setInt32(0, tc.lo, true);\n        view.setInt32(4, tc.hi, true);\n        return this.raw(chunk);\n    }\n    /**\n     * Write a `fixed64` value, an unsigned, fixed-length 64 bit integer.\n     */\n    fixed64(value) {\n        let chunk = new Uint8Array(8), view = new DataView(chunk.buffer), tc = protoInt64.uEnc(value);\n        view.setInt32(0, tc.lo, true);\n        view.setInt32(4, tc.hi, true);\n        return this.raw(chunk);\n    }\n    /**\n     * Write a `int64` value, a signed 64-bit varint.\n     */\n    int64(value) {\n        let tc = protoInt64.enc(value);\n        varint64write(tc.lo, tc.hi, this.buf);\n        return this;\n    }\n    /**\n     * Write a `sint64` value, a signed, zig-zag-encoded 64-bit varint.\n     */\n    sint64(value) {\n        let tc = protoInt64.enc(value), \n        // zigzag encode\n        sign = tc.hi >> 31, lo = (tc.lo << 1) ^ sign, hi = ((tc.hi << 1) | (tc.lo >>> 31)) ^ sign;\n        varint64write(lo, hi, this.buf);\n        return this;\n    }\n    /**\n     * Write a `uint64` value, an unsigned 64-bit varint.\n     */\n    uint64(value) {\n        let tc = protoInt64.uEnc(value);\n        varint64write(tc.lo, tc.hi, this.buf);\n        return this;\n    }\n}\nexport class BinaryReader {\n    constructor(buf, textDecoder) {\n        this.varint64 = varint64read; // dirty cast for `this`\n        /**\n         * Read a `uint32` field, an unsigned 32 bit varint.\n         */\n        this.uint32 = varint32read; // dirty cast for `this` and access to protected `buf`\n        this.buf = buf;\n        this.len = buf.length;\n        this.pos = 0;\n        this.view = new DataView(buf.buffer, buf.byteOffset, buf.byteLength);\n        this.textDecoder = textDecoder !== null && textDecoder !== void 0 ? textDecoder : new TextDecoder();\n    }\n    /**\n     * Reads a tag - field number and wire type.\n     */\n    tag() {\n        let tag = this.uint32(), fieldNo = tag >>> 3, wireType = tag & 7;\n        if (fieldNo <= 0 || wireType < 0 || wireType > 5)\n            throw new Error(\"illegal tag: field no \" + fieldNo + \" wire type \" + wireType);\n        return [fieldNo, wireType];\n    }\n    /**\n     * Skip one element and return the skipped data.\n     *\n     * When skipping StartGroup, provide the tags field number to check for\n     * matching field number in the EndGroup tag.\n     */\n    skip(wireType, fieldNo) {\n        let start = this.pos;\n        switch (wireType) {\n            case WireType.Varint:\n                while (this.buf[this.pos++] & 0x80) {\n                    // ignore\n                }\n                break;\n            // eslint-disable-next-line\n            // @ts-ignore TS7029: Fallthrough case in switch\n            case WireType.Bit64:\n                this.pos += 4;\n            // eslint-disable-next-line\n            // @ts-ignore TS7029: Fallthrough case in switch\n            case WireType.Bit32:\n                this.pos += 4;\n                break;\n            case WireType.LengthDelimited:\n                let len = this.uint32();\n                this.pos += len;\n                break;\n            case WireType.StartGroup:\n                for (;;) {\n                    const [fn, wt] = this.tag();\n                    if (wt === WireType.EndGroup) {\n                        if (fieldNo !== undefined && fn !== fieldNo) {\n                            throw new Error(\"invalid end group tag\");\n                        }\n                        break;\n                    }\n                    this.skip(wt, fn);\n                }\n                break;\n            default:\n                throw new Error(\"cant skip wire type \" + wireType);\n        }\n        this.assertBounds();\n        return this.buf.subarray(start, this.pos);\n    }\n    /**\n     * Throws error if position in byte array is out of range.\n     */\n    assertBounds() {\n        if (this.pos > this.len)\n            throw new RangeError(\"premature EOF\");\n    }\n    /**\n     * Read a `int32` field, a signed 32 bit varint.\n     */\n    int32() {\n        return this.uint32() | 0;\n    }\n    /**\n     * Read a `sint32` field, a signed, zigzag-encoded 32-bit varint.\n     */\n    sint32() {\n        let zze = this.uint32();\n        // decode zigzag\n        return (zze >>> 1) ^ -(zze & 1);\n    }\n    /**\n     * Read a `int64` field, a signed 64-bit varint.\n     */\n    int64() {\n        return protoInt64.dec(...this.varint64());\n    }\n    /**\n     * Read a `uint64` field, an unsigned 64-bit varint.\n     */\n    uint64() {\n        return protoInt64.uDec(...this.varint64());\n    }\n    /**\n     * Read a `sint64` field, a signed, zig-zag-encoded 64-bit varint.\n     */\n    sint64() {\n        let [lo, hi] = this.varint64();\n        // decode zig zag\n        let s = -(lo & 1);\n        lo = ((lo >>> 1) | ((hi & 1) << 31)) ^ s;\n        hi = (hi >>> 1) ^ s;\n        return protoInt64.dec(lo, hi);\n    }\n    /**\n     * Read a `bool` field, a variant.\n     */\n    bool() {\n        let [lo, hi] = this.varint64();\n        return lo !== 0 || hi !== 0;\n    }\n    /**\n     * Read a `fixed32` field, an unsigned, fixed-length 32-bit integer.\n     */\n    fixed32() {\n        return this.view.getUint32((this.pos += 4) - 4, true);\n    }\n    /**\n     * Read a `sfixed32` field, a signed, fixed-length 32-bit integer.\n     */\n    sfixed32() {\n        return this.view.getInt32((this.pos += 4) - 4, true);\n    }\n    /**\n     * Read a `fixed64` field, an unsigned, fixed-length 64 bit integer.\n     */\n    fixed64() {\n        return protoInt64.uDec(this.sfixed32(), this.sfixed32());\n    }\n    /**\n     * Read a `fixed64` field, a signed, fixed-length 64-bit integer.\n     */\n    sfixed64() {\n        return protoInt64.dec(this.sfixed32(), this.sfixed32());\n    }\n    /**\n     * Read a `float` field, 32-bit floating point number.\n     */\n    float() {\n        return this.view.getFloat32((this.pos += 4) - 4, true);\n    }\n    /**\n     * Read a `double` field, a 64-bit floating point number.\n     */\n    double() {\n        return this.view.getFloat64((this.pos += 8) - 8, true);\n    }\n    /**\n     * Read a `bytes` field, length-delimited arbitrary data.\n     */\n    bytes() {\n        let len = this.uint32(), start = this.pos;\n        this.pos += len;\n        this.assertBounds();\n        return this.buf.subarray(start, start + len);\n    }\n    /**\n     * Read a `string` field, length-delimited data converted to UTF-8 text.\n     */\n    string() {\n        return this.textDecoder.decode(this.bytes());\n    }\n}\n"], "names": [], "mappings": "AAAA,6CAA6C;AAC7C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;;;;;AACjC;AACA;AACA;;;;AAUO,IAAI;AACX,CAAC,SAAU,QAAQ;IACf;;KAEC,GACD,QAAQ,CAAC,QAAQ,CAAC,SAAS,GAAG,EAAE,GAAG;IACnC;;;KAGC,GACD,QAAQ,CAAC,QAAQ,CAAC,QAAQ,GAAG,EAAE,GAAG;IAClC;;;;;;KAMC,GACD,QAAQ,CAAC,QAAQ,CAAC,kBAAkB,GAAG,EAAE,GAAG;IAC5C;;;KAGC,GACD,QAAQ,CAAC,QAAQ,CAAC,aAAa,GAAG,EAAE,GAAG;IACvC;;KAEC,GACD,QAAQ,CAAC,QAAQ,CAAC,WAAW,GAAG,EAAE,GAAG;IACrC;;;KAGC,GACD,QAAQ,CAAC,QAAQ,CAAC,QAAQ,GAAG,EAAE,GAAG;AACtC,CAAC,EAAE,YAAY,CAAC,WAAW,CAAC,CAAC;AACtB,MAAM;IACT,YAAY,WAAW,CAAE;QACrB;;SAEC,GACD,IAAI,CAAC,KAAK,GAAG,EAAE;QACf,IAAI,CAAC,WAAW,GAAG,gBAAgB,QAAQ,gBAAgB,KAAK,IAAI,cAAc,IAAI;QACtF,IAAI,CAAC,MAAM,GAAG,EAAE;QAChB,IAAI,CAAC,GAAG,GAAG,EAAE;IACjB;IACA;;KAEC,GACD,SAAS;QACL,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,WAAW,IAAI,CAAC,GAAG,IAAI,mBAAmB;QAC/D,IAAI,MAAM;QACV,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,IACpC,OAAO,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM;QAChC,IAAI,QAAQ,IAAI,WAAW;QAC3B,IAAI,SAAS;QACb,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,IAAK;YACzC,MAAM,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE;YAC1B,UAAU,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM;QACnC;QACA,IAAI,CAAC,MAAM,GAAG,EAAE;QAChB,OAAO;IACX;IACA;;;;;KAKC,GACD,OAAO;QACH,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;YAAE,QAAQ,IAAI,CAAC,MAAM;YAAE,KAAK,IAAI,CAAC,GAAG;QAAC;QACrD,IAAI,CAAC,MAAM,GAAG,EAAE;QAChB,IAAI,CAAC,GAAG,GAAG,EAAE;QACb,OAAO,IAAI;IACf;IACA;;;KAGC,GACD,OAAO;QACH,oBAAoB;QACpB,IAAI,QAAQ,IAAI,CAAC,MAAM;QACvB,yBAAyB;QACzB,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG;QACzB,IAAI,CAAC,MACD,MAAM,IAAI,MAAM;QACpB,IAAI,CAAC,MAAM,GAAG,KAAK,MAAM;QACzB,IAAI,CAAC,GAAG,GAAG,KAAK,GAAG;QACnB,kCAAkC;QAClC,IAAI,CAAC,MAAM,CAAC,MAAM,UAAU;QAC5B,OAAO,IAAI,CAAC,GAAG,CAAC;IACpB;IACA;;;;;;KAMC,GACD,IAAI,OAAO,EAAE,IAAI,EAAE;QACf,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,AAAC,WAAW,IAAK,IAAI,MAAM;IACnD;IACA;;KAEC,GACD,IAAI,KAAK,EAAE;QACP,IAAI,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE;YACjB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,WAAW,IAAI,CAAC,GAAG;YACxC,IAAI,CAAC,GAAG,GAAG,EAAE;QACjB;QACA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;QACjB,OAAO,IAAI;IACf;IACA;;KAEC,GACD,OAAO,KAAK,EAAE;QACV,CAAA,GAAA,sPAAA,CAAA,eAAY,AAAD,EAAE;QACb,8CAA8C;QAC9C,MAAO,QAAQ,KAAM;YACjB,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,AAAC,QAAQ,OAAQ;YAC/B,QAAQ,UAAU;QACtB;QACA,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC;QACd,OAAO,IAAI;IACf;IACA;;KAEC,GACD,MAAM,KAAK,EAAE;QACT,CAAA,GAAA,sPAAA,CAAA,cAAW,AAAD,EAAE;QACZ,CAAA,GAAA,qPAAA,CAAA,gBAAa,AAAD,EAAE,OAAO,IAAI,CAAC,GAAG;QAC7B,OAAO,IAAI;IACf;IACA;;KAEC,GACD,KAAK,KAAK,EAAE;QACR,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,IAAI;QAC1B,OAAO,IAAI;IACf;IACA;;KAEC,GACD,MAAM,KAAK,EAAE;QACT,IAAI,CAAC,MAAM,CAAC,MAAM,UAAU,GAAG,kCAAkC;QACjE,OAAO,IAAI,CAAC,GAAG,CAAC;IACpB;IACA;;KAEC,GACD,OAAO,KAAK,EAAE;QACV,IAAI,QAAQ,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;QACpC,IAAI,CAAC,MAAM,CAAC,MAAM,UAAU,GAAG,kCAAkC;QACjE,OAAO,IAAI,CAAC,GAAG,CAAC;IACpB;IACA;;KAEC,GACD,MAAM,KAAK,EAAE;QACT,CAAA,GAAA,sPAAA,CAAA,gBAAa,AAAD,EAAE;QACd,IAAI,QAAQ,IAAI,WAAW;QAC3B,IAAI,SAAS,MAAM,MAAM,EAAE,UAAU,CAAC,GAAG,OAAO;QAChD,OAAO,IAAI,CAAC,GAAG,CAAC;IACpB;IACA;;KAEC,GACD,OAAO,KAAK,EAAE;QACV,IAAI,QAAQ,IAAI,WAAW;QAC3B,IAAI,SAAS,MAAM,MAAM,EAAE,UAAU,CAAC,GAAG,OAAO;QAChD,OAAO,IAAI,CAAC,GAAG,CAAC;IACpB;IACA;;KAEC,GACD,QAAQ,KAAK,EAAE;QACX,CAAA,GAAA,sPAAA,CAAA,eAAY,AAAD,EAAE;QACb,IAAI,QAAQ,IAAI,WAAW;QAC3B,IAAI,SAAS,MAAM,MAAM,EAAE,SAAS,CAAC,GAAG,OAAO;QAC/C,OAAO,IAAI,CAAC,GAAG,CAAC;IACpB;IACA;;KAEC,GACD,SAAS,KAAK,EAAE;QACZ,CAAA,GAAA,sPAAA,CAAA,cAAW,AAAD,EAAE;QACZ,IAAI,QAAQ,IAAI,WAAW;QAC3B,IAAI,SAAS,MAAM,MAAM,EAAE,QAAQ,CAAC,GAAG,OAAO;QAC9C,OAAO,IAAI,CAAC,GAAG,CAAC;IACpB;IACA;;KAEC,GACD,OAAO,KAAK,EAAE;QACV,CAAA,GAAA,sPAAA,CAAA,cAAW,AAAD,EAAE;QACZ,gBAAgB;QAChB,QAAQ,CAAC,AAAC,SAAS,IAAM,SAAS,EAAG,MAAM;QAC3C,CAAA,GAAA,qPAAA,CAAA,gBAAa,AAAD,EAAE,OAAO,IAAI,CAAC,GAAG;QAC7B,OAAO,IAAI;IACf;IACA;;KAEC,GACD,SAAS,KAAK,EAAE;QACZ,IAAI,QAAQ,IAAI,WAAW,IAAI,OAAO,IAAI,SAAS,MAAM,MAAM,GAAG,KAAK,mPAAA,CAAA,aAAU,CAAC,GAAG,CAAC;QACtF,KAAK,QAAQ,CAAC,GAAG,GAAG,EAAE,EAAE;QACxB,KAAK,QAAQ,CAAC,GAAG,GAAG,EAAE,EAAE;QACxB,OAAO,IAAI,CAAC,GAAG,CAAC;IACpB;IACA;;KAEC,GACD,QAAQ,KAAK,EAAE;QACX,IAAI,QAAQ,IAAI,WAAW,IAAI,OAAO,IAAI,SAAS,MAAM,MAAM,GAAG,KAAK,mPAAA,CAAA,aAAU,CAAC,IAAI,CAAC;QACvF,KAAK,QAAQ,CAAC,GAAG,GAAG,EAAE,EAAE;QACxB,KAAK,QAAQ,CAAC,GAAG,GAAG,EAAE,EAAE;QACxB,OAAO,IAAI,CAAC,GAAG,CAAC;IACpB;IACA;;KAEC,GACD,MAAM,KAAK,EAAE;QACT,IAAI,KAAK,mPAAA,CAAA,aAAU,CAAC,GAAG,CAAC;QACxB,CAAA,GAAA,qPAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE,IAAI,CAAC,GAAG;QACpC,OAAO,IAAI;IACf;IACA;;KAEC,GACD,OAAO,KAAK,EAAE;QACV,IAAI,KAAK,mPAAA,CAAA,aAAU,CAAC,GAAG,CAAC,QACxB,gBAAgB;QAChB,OAAO,GAAG,EAAE,IAAI,IAAI,KAAK,AAAC,GAAG,EAAE,IAAI,IAAK,MAAM,KAAK,CAAC,AAAC,GAAG,EAAE,IAAI,IAAM,GAAG,EAAE,KAAK,EAAG,IAAI;QACrF,CAAA,GAAA,qPAAA,CAAA,gBAAa,AAAD,EAAE,IAAI,IAAI,IAAI,CAAC,GAAG;QAC9B,OAAO,IAAI;IACf;IACA;;KAEC,GACD,OAAO,KAAK,EAAE;QACV,IAAI,KAAK,mPAAA,CAAA,aAAU,CAAC,IAAI,CAAC;QACzB,CAAA,GAAA,qPAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE,IAAI,CAAC,GAAG;QACpC,OAAO,IAAI;IACf;AACJ;AACO,MAAM;IACT,YAAY,GAAG,EAAE,WAAW,CAAE;QAC1B,IAAI,CAAC,QAAQ,GAAG,qPAAA,CAAA,eAAY,EAAE,wBAAwB;QACtD;;SAEC,GACD,IAAI,CAAC,MAAM,GAAG,qPAAA,CAAA,eAAY,EAAE,sDAAsD;QAClF,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,GAAG,GAAG,IAAI,MAAM;QACrB,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,IAAI,GAAG,IAAI,SAAS,IAAI,MAAM,EAAE,IAAI,UAAU,EAAE,IAAI,UAAU;QACnE,IAAI,CAAC,WAAW,GAAG,gBAAgB,QAAQ,gBAAgB,KAAK,IAAI,cAAc,IAAI;IAC1F;IACA;;KAEC,GACD,MAAM;QACF,IAAI,MAAM,IAAI,CAAC,MAAM,IAAI,UAAU,QAAQ,GAAG,WAAW,MAAM;QAC/D,IAAI,WAAW,KAAK,WAAW,KAAK,WAAW,GAC3C,MAAM,IAAI,MAAM,2BAA2B,UAAU,gBAAgB;QACzE,OAAO;YAAC;YAAS;SAAS;IAC9B;IACA;;;;;KAKC,GACD,KAAK,QAAQ,EAAE,OAAO,EAAE;QACpB,IAAI,QAAQ,IAAI,CAAC,GAAG;QACpB,OAAQ;YACJ,KAAK,SAAS,MAAM;gBAChB,MAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,KAAM;gBAChC,SAAS;gBACb;gBACA;YACJ,2BAA2B;YAC3B,gDAAgD;YAChD,KAAK,SAAS,KAAK;gBACf,IAAI,CAAC,GAAG,IAAI;YAChB,2BAA2B;YAC3B,gDAAgD;YAChD,KAAK,SAAS,KAAK;gBACf,IAAI,CAAC,GAAG,IAAI;gBACZ;YACJ,KAAK,SAAS,eAAe;gBACzB,IAAI,MAAM,IAAI,CAAC,MAAM;gBACrB,IAAI,CAAC,GAAG,IAAI;gBACZ;YACJ,KAAK,SAAS,UAAU;gBACpB,OAAS;oBACL,MAAM,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG;oBACzB,IAAI,OAAO,SAAS,QAAQ,EAAE;wBAC1B,IAAI,YAAY,aAAa,OAAO,SAAS;4BACzC,MAAM,IAAI,MAAM;wBACpB;wBACA;oBACJ;oBACA,IAAI,CAAC,IAAI,CAAC,IAAI;gBAClB;gBACA;YACJ;gBACI,MAAM,IAAI,MAAM,yBAAyB;QACjD;QACA,IAAI,CAAC,YAAY;QACjB,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,IAAI,CAAC,GAAG;IAC5C;IACA;;KAEC,GACD,eAAe;QACX,IAAI,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,EACnB,MAAM,IAAI,WAAW;IAC7B;IACA;;KAEC,GACD,QAAQ;QACJ,OAAO,IAAI,CAAC,MAAM,KAAK;IAC3B;IACA;;KAEC,GACD,SAAS;QACL,IAAI,MAAM,IAAI,CAAC,MAAM;QACrB,gBAAgB;QAChB,OAAO,AAAC,QAAQ,IAAK,CAAC,CAAC,MAAM,CAAC;IAClC;IACA;;KAEC,GACD,QAAQ;QACJ,OAAO,mPAAA,CAAA,aAAU,CAAC,GAAG,IAAI,IAAI,CAAC,QAAQ;IAC1C;IACA;;KAEC,GACD,SAAS;QACL,OAAO,mPAAA,CAAA,aAAU,CAAC,IAAI,IAAI,IAAI,CAAC,QAAQ;IAC3C;IACA;;KAEC,GACD,SAAS;QACL,IAAI,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC,QAAQ;QAC5B,iBAAiB;QACjB,IAAI,IAAI,CAAC,CAAC,KAAK,CAAC;QAChB,KAAK,CAAC,AAAC,OAAO,IAAM,CAAC,KAAK,CAAC,KAAK,EAAG,IAAI;QACvC,KAAK,AAAC,OAAO,IAAK;QAClB,OAAO,mPAAA,CAAA,aAAU,CAAC,GAAG,CAAC,IAAI;IAC9B;IACA;;KAEC,GACD,OAAO;QACH,IAAI,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC,QAAQ;QAC5B,OAAO,OAAO,KAAK,OAAO;IAC9B;IACA;;KAEC,GACD,UAAU;QACN,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,GAAG;IACpD;IACA;;KAEC,GACD,WAAW;QACP,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,GAAG;IACnD;IACA;;KAEC,GACD,UAAU;QACN,OAAO,mPAAA,CAAA,aAAU,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ;IACzD;IACA;;KAEC,GACD,WAAW;QACP,OAAO,mPAAA,CAAA,aAAU,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ;IACxD;IACA;;KAEC,GACD,QAAQ;QACJ,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,GAAG;IACrD;IACA;;KAEC,GACD,SAAS;QACL,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,GAAG;IACrD;IACA;;KAEC,GACD,QAAQ;QACJ,IAAI,MAAM,IAAI,CAAC,MAAM,IAAI,QAAQ,IAAI,CAAC,GAAG;QACzC,IAAI,CAAC,GAAG,IAAI;QACZ,IAAI,CAAC,YAAY;QACjB,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,QAAQ;IAC5C;IACA;;KAEC,GACD,SAAS;QACL,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK;IAC7C;AACJ", "ignoreList": [0]}}, {"offset": {"line": 2356, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@bufbuild+protobuf@1.10.1/node_modules/@bufbuild/protobuf/dist/esm/private/binary-format.js"], "sourcesContent": ["// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nimport { BinaryReader, BinaryWriter, WireType } from \"../binary-encoding.js\";\nimport { Message } from \"../message.js\";\nimport { wrapField } from \"./field-wrapper.js\";\nimport { scalarZeroValue } from \"./scalars.js\";\nimport { assert } from \"./assert.js\";\nimport { isFieldSet } from \"./reflect.js\";\nimport { LongType, ScalarType } from \"../scalar.js\";\nimport { isMessage } from \"../is-message.js\";\n/* eslint-disable prefer-const,no-case-declarations,@typescript-eslint/no-explicit-any,@typescript-eslint/no-unsafe-argument,@typescript-eslint/no-unsafe-assignment,@typescript-eslint/no-unsafe-member-access,@typescript-eslint/no-unsafe-call,@typescript-eslint/no-unsafe-return */\nconst unknownFieldsSymbol = Symbol(\"@bufbuild/protobuf/unknown-fields\");\n// Default options for parsing binary data.\nconst readDefaults = {\n    readUnknownFields: true,\n    readerFactory: (bytes) => new BinaryReader(bytes),\n};\n// Default options for serializing binary data.\nconst writeDefaults = {\n    writeUnknownFields: true,\n    writerFactory: () => new BinaryWriter(),\n};\nfunction makeReadOptions(options) {\n    return options ? Object.assign(Object.assign({}, readDefaults), options) : readDefaults;\n}\nfunction makeWriteOptions(options) {\n    return options ? Object.assign(Object.assign({}, writeDefaults), options) : writeDefaults;\n}\nexport function makeBinaryFormat() {\n    return {\n        makeReadOptions,\n        makeWriteOptions,\n        listUnknownFields(message) {\n            var _a;\n            return (_a = message[unknownFieldsSymbol]) !== null && _a !== void 0 ? _a : [];\n        },\n        discardUnknownFields(message) {\n            delete message[unknownFieldsSymbol];\n        },\n        writeUnknownFields(message, writer) {\n            const m = message;\n            const c = m[unknownFieldsSymbol];\n            if (c) {\n                for (const f of c) {\n                    writer.tag(f.no, f.wireType).raw(f.data);\n                }\n            }\n        },\n        onUnknownField(message, no, wireType, data) {\n            const m = message;\n            if (!Array.isArray(m[unknownFieldsSymbol])) {\n                m[unknownFieldsSymbol] = [];\n            }\n            m[unknownFieldsSymbol].push({ no, wireType, data });\n        },\n        readMessage(message, reader, lengthOrEndTagFieldNo, options, delimitedMessageEncoding) {\n            const type = message.getType();\n            // eslint-disable-next-line @typescript-eslint/strict-boolean-expressions\n            const end = delimitedMessageEncoding\n                ? reader.len\n                : reader.pos + lengthOrEndTagFieldNo;\n            let fieldNo, wireType;\n            while (reader.pos < end) {\n                [fieldNo, wireType] = reader.tag();\n                if (delimitedMessageEncoding === true &&\n                    wireType == WireType.EndGroup) {\n                    break;\n                }\n                const field = type.fields.find(fieldNo);\n                if (!field) {\n                    const data = reader.skip(wireType, fieldNo);\n                    if (options.readUnknownFields) {\n                        this.onUnknownField(message, fieldNo, wireType, data);\n                    }\n                    continue;\n                }\n                readField(message, reader, field, wireType, options);\n            }\n            if (delimitedMessageEncoding && // eslint-disable-line @typescript-eslint/strict-boolean-expressions\n                (wireType != WireType.EndGroup || fieldNo !== lengthOrEndTagFieldNo)) {\n                throw new Error(`invalid end group tag`);\n            }\n        },\n        readField,\n        writeMessage(message, writer, options) {\n            const type = message.getType();\n            for (const field of type.fields.byNumber()) {\n                if (!isFieldSet(field, message)) {\n                    if (field.req) {\n                        throw new Error(`cannot encode field ${type.typeName}.${field.name} to binary: required field not set`);\n                    }\n                    continue;\n                }\n                const value = field.oneof\n                    ? message[field.oneof.localName].value\n                    : message[field.localName];\n                writeField(field, value, writer, options);\n            }\n            if (options.writeUnknownFields) {\n                this.writeUnknownFields(message, writer);\n            }\n            return writer;\n        },\n        writeField(field, value, writer, options) {\n            // The behavior of our internal function has changed, it does no longer\n            // accept `undefined` values for singular scalar and map.\n            // For backwards-compatibility, we support the old form that is part of\n            // the public API through the interface BinaryFormat.\n            if (value === undefined) {\n                return undefined;\n            }\n            writeField(field, value, writer, options);\n        },\n    };\n}\nfunction readField(target, // eslint-disable-line @typescript-eslint/no-explicit-any -- `any` is the best choice for dynamic access\nreader, field, wireType, options) {\n    let { repeated, localName } = field;\n    if (field.oneof) {\n        target = target[field.oneof.localName];\n        if (target.case != localName) {\n            delete target.value;\n        }\n        target.case = localName;\n        localName = \"value\";\n    }\n    switch (field.kind) {\n        case \"scalar\":\n        case \"enum\":\n            const scalarType = field.kind == \"enum\" ? ScalarType.INT32 : field.T;\n            let read = readScalar;\n            // eslint-disable-next-line @typescript-eslint/no-unsafe-enum-comparison -- acceptable since it's covered by tests\n            if (field.kind == \"scalar\" && field.L > 0) {\n                read = readScalarLTString;\n            }\n            if (repeated) {\n                let arr = target[localName]; // safe to assume presence of array, oneof cannot contain repeated values\n                const isPacked = wireType == WireType.LengthDelimited &&\n                    scalarType != ScalarType.STRING &&\n                    scalarType != ScalarType.BYTES;\n                if (isPacked) {\n                    let e = reader.uint32() + reader.pos;\n                    while (reader.pos < e) {\n                        arr.push(read(reader, scalarType));\n                    }\n                }\n                else {\n                    arr.push(read(reader, scalarType));\n                }\n            }\n            else {\n                target[localName] = read(reader, scalarType);\n            }\n            break;\n        case \"message\":\n            const messageType = field.T;\n            if (repeated) {\n                // safe to assume presence of array, oneof cannot contain repeated values\n                target[localName].push(readMessageField(reader, new messageType(), options, field));\n            }\n            else {\n                if (isMessage(target[localName])) {\n                    readMessageField(reader, target[localName], options, field);\n                }\n                else {\n                    target[localName] = readMessageField(reader, new messageType(), options, field);\n                    if (messageType.fieldWrapper && !field.oneof && !field.repeated) {\n                        target[localName] = messageType.fieldWrapper.unwrapField(target[localName]);\n                    }\n                }\n            }\n            break;\n        case \"map\":\n            let [mapKey, mapVal] = readMapEntry(field, reader, options);\n            // safe to assume presence of map object, oneof cannot contain repeated values\n            target[localName][mapKey] = mapVal;\n            break;\n    }\n}\n// Read a message, avoiding MessageType.fromBinary() to re-use the\n// BinaryReadOptions and the IBinaryReader.\nfunction readMessageField(reader, message, options, field) {\n    const format = message.getType().runtime.bin;\n    const delimited = field === null || field === void 0 ? void 0 : field.delimited;\n    format.readMessage(message, reader, delimited ? field.no : reader.uint32(), // eslint-disable-line @typescript-eslint/strict-boolean-expressions\n    options, delimited);\n    return message;\n}\n// Read a map field, expecting key field = 1, value field = 2\nfunction readMapEntry(field, reader, options) {\n    const length = reader.uint32(), end = reader.pos + length;\n    let key, val;\n    while (reader.pos < end) {\n        const [fieldNo] = reader.tag();\n        switch (fieldNo) {\n            case 1:\n                key = readScalar(reader, field.K);\n                break;\n            case 2:\n                switch (field.V.kind) {\n                    case \"scalar\":\n                        val = readScalar(reader, field.V.T);\n                        break;\n                    case \"enum\":\n                        val = reader.int32();\n                        break;\n                    case \"message\":\n                        val = readMessageField(reader, new field.V.T(), options, undefined);\n                        break;\n                }\n                break;\n        }\n    }\n    if (key === undefined) {\n        key = scalarZeroValue(field.K, LongType.BIGINT);\n    }\n    if (typeof key != \"string\" && typeof key != \"number\") {\n        key = key.toString();\n    }\n    if (val === undefined) {\n        switch (field.V.kind) {\n            case \"scalar\":\n                val = scalarZeroValue(field.V.T, LongType.BIGINT);\n                break;\n            case \"enum\":\n                val = field.V.T.values[0].no;\n                break;\n            case \"message\":\n                val = new field.V.T();\n                break;\n        }\n    }\n    return [key, val];\n}\n// Read a scalar value, but return 64 bit integral types (int64, uint64,\n// sint64, fixed64, sfixed64) as string instead of bigint.\nfunction readScalarLTString(reader, type) {\n    const v = readScalar(reader, type);\n    return typeof v == \"bigint\" ? v.toString() : v;\n}\n// Does not use scalarTypeInfo() for better performance.\nfunction readScalar(reader, type) {\n    switch (type) {\n        case ScalarType.STRING:\n            return reader.string();\n        case ScalarType.BOOL:\n            return reader.bool();\n        case ScalarType.DOUBLE:\n            return reader.double();\n        case ScalarType.FLOAT:\n            return reader.float();\n        case ScalarType.INT32:\n            return reader.int32();\n        case ScalarType.INT64:\n            return reader.int64();\n        case ScalarType.UINT64:\n            return reader.uint64();\n        case ScalarType.FIXED64:\n            return reader.fixed64();\n        case ScalarType.BYTES:\n            return reader.bytes();\n        case ScalarType.FIXED32:\n            return reader.fixed32();\n        case ScalarType.SFIXED32:\n            return reader.sfixed32();\n        case ScalarType.SFIXED64:\n            return reader.sfixed64();\n        case ScalarType.SINT64:\n            return reader.sint64();\n        case ScalarType.UINT32:\n            return reader.uint32();\n        case ScalarType.SINT32:\n            return reader.sint32();\n    }\n}\nfunction writeField(field, value, writer, options) {\n    assert(value !== undefined);\n    const repeated = field.repeated;\n    switch (field.kind) {\n        case \"scalar\":\n        case \"enum\":\n            let scalarType = field.kind == \"enum\" ? ScalarType.INT32 : field.T;\n            if (repeated) {\n                assert(Array.isArray(value));\n                if (field.packed) {\n                    writePacked(writer, scalarType, field.no, value);\n                }\n                else {\n                    for (const item of value) {\n                        writeScalar(writer, scalarType, field.no, item);\n                    }\n                }\n            }\n            else {\n                writeScalar(writer, scalarType, field.no, value);\n            }\n            break;\n        case \"message\":\n            if (repeated) {\n                assert(Array.isArray(value));\n                for (const item of value) {\n                    writeMessageField(writer, options, field, item);\n                }\n            }\n            else {\n                writeMessageField(writer, options, field, value);\n            }\n            break;\n        case \"map\":\n            assert(typeof value == \"object\" && value != null);\n            for (const [key, val] of Object.entries(value)) {\n                writeMapEntry(writer, options, field, key, val);\n            }\n            break;\n    }\n}\nexport function writeMapEntry(writer, options, field, key, value) {\n    writer.tag(field.no, WireType.LengthDelimited);\n    writer.fork();\n    // javascript only allows number or string for object properties\n    // we convert from our representation to the protobuf type\n    let keyValue = key;\n    // eslint-disable-next-line @typescript-eslint/switch-exhaustiveness-check -- we deliberately handle just the special cases for map keys\n    switch (field.K) {\n        case ScalarType.INT32:\n        case ScalarType.FIXED32:\n        case ScalarType.UINT32:\n        case ScalarType.SFIXED32:\n        case ScalarType.SINT32:\n            keyValue = Number.parseInt(key);\n            break;\n        case ScalarType.BOOL:\n            assert(key == \"true\" || key == \"false\");\n            keyValue = key == \"true\";\n            break;\n    }\n    // write key, expecting key field number = 1\n    writeScalar(writer, field.K, 1, keyValue);\n    // write value, expecting value field number = 2\n    switch (field.V.kind) {\n        case \"scalar\":\n            writeScalar(writer, field.V.T, 2, value);\n            break;\n        case \"enum\":\n            writeScalar(writer, ScalarType.INT32, 2, value);\n            break;\n        case \"message\":\n            assert(value !== undefined);\n            writer.tag(2, WireType.LengthDelimited).bytes(value.toBinary(options));\n            break;\n    }\n    writer.join();\n}\n// Value must not be undefined\nfunction writeMessageField(writer, options, field, value) {\n    const message = wrapField(field.T, value);\n    // eslint-disable-next-line @typescript-eslint/strict-boolean-expressions\n    if (field.delimited)\n        writer\n            .tag(field.no, WireType.StartGroup)\n            .raw(message.toBinary(options))\n            .tag(field.no, WireType.EndGroup);\n    else\n        writer\n            .tag(field.no, WireType.LengthDelimited)\n            .bytes(message.toBinary(options));\n}\nfunction writeScalar(writer, type, fieldNo, value) {\n    assert(value !== undefined);\n    let [wireType, method] = scalarTypeInfo(type);\n    writer.tag(fieldNo, wireType)[method](value);\n}\nfunction writePacked(writer, type, fieldNo, value) {\n    if (!value.length) {\n        return;\n    }\n    writer.tag(fieldNo, WireType.LengthDelimited).fork();\n    let [, method] = scalarTypeInfo(type);\n    for (let i = 0; i < value.length; i++) {\n        writer[method](value[i]);\n    }\n    writer.join();\n}\n/**\n * Get information for writing a scalar value.\n *\n * Returns tuple:\n * [0]: appropriate WireType\n * [1]: name of the appropriate method of IBinaryWriter\n * [2]: whether the given value is a default value for proto3 semantics\n *\n * If argument `value` is omitted, [2] is always false.\n */\n// TODO replace call-sites writeScalar() and writePacked(), then remove\nfunction scalarTypeInfo(type) {\n    let wireType = WireType.Varint;\n    // eslint-disable-next-line @typescript-eslint/switch-exhaustiveness-check -- INT32, UINT32, SINT32 are covered by the defaults\n    switch (type) {\n        case ScalarType.BYTES:\n        case ScalarType.STRING:\n            wireType = WireType.LengthDelimited;\n            break;\n        case ScalarType.DOUBLE:\n        case ScalarType.FIXED64:\n        case ScalarType.SFIXED64:\n            wireType = WireType.Bit64;\n            break;\n        case ScalarType.FIXED32:\n        case ScalarType.SFIXED32:\n        case ScalarType.FLOAT:\n            wireType = WireType.Bit32;\n            break;\n    }\n    const method = ScalarType[type].toLowerCase();\n    return [wireType, method];\n}\n"], "names": [], "mappings": "AAAA,6CAA6C;AAC7C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;;;;AACjC;AAEA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AACA,sRAAsR,GACtR,MAAM,sBAAsB,OAAO;AACnC,2CAA2C;AAC3C,MAAM,eAAe;IACjB,mBAAmB;IACnB,eAAe,CAAC,QAAU,IAAI,uPAAA,CAAA,eAAY,CAAC;AAC/C;AACA,+CAA+C;AAC/C,MAAM,gBAAgB;IAClB,oBAAoB;IACpB,eAAe,IAAM,IAAI,uPAAA,CAAA,eAAY;AACzC;AACA,SAAS,gBAAgB,OAAO;IAC5B,OAAO,UAAU,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,eAAe,WAAW;AAC/E;AACA,SAAS,iBAAiB,OAAO;IAC7B,OAAO,UAAU,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,gBAAgB,WAAW;AAChF;AACO,SAAS;IACZ,OAAO;QACH;QACA;QACA,mBAAkB,OAAO;YACrB,IAAI;YACJ,OAAO,CAAC,KAAK,OAAO,CAAC,oBAAoB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE;QAClF;QACA,sBAAqB,OAAO;YACxB,OAAO,OAAO,CAAC,oBAAoB;QACvC;QACA,oBAAmB,OAAO,EAAE,MAAM;YAC9B,MAAM,IAAI;YACV,MAAM,IAAI,CAAC,CAAC,oBAAoB;YAChC,IAAI,GAAG;gBACH,KAAK,MAAM,KAAK,EAAG;oBACf,OAAO,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,QAAQ,EAAE,GAAG,CAAC,EAAE,IAAI;gBAC3C;YACJ;QACJ;QACA,gBAAe,OAAO,EAAE,EAAE,EAAE,QAAQ,EAAE,IAAI;YACtC,MAAM,IAAI;YACV,IAAI,CAAC,MAAM,OAAO,CAAC,CAAC,CAAC,oBAAoB,GAAG;gBACxC,CAAC,CAAC,oBAAoB,GAAG,EAAE;YAC/B;YACA,CAAC,CAAC,oBAAoB,CAAC,IAAI,CAAC;gBAAE;gBAAI;gBAAU;YAAK;QACrD;QACA,aAAY,OAAO,EAAE,MAAM,EAAE,qBAAqB,EAAE,OAAO,EAAE,wBAAwB;YACjF,MAAM,OAAO,QAAQ,OAAO;YAC5B,yEAAyE;YACzE,MAAM,MAAM,2BACN,OAAO,GAAG,GACV,OAAO,GAAG,GAAG;YACnB,IAAI,SAAS;YACb,MAAO,OAAO,GAAG,GAAG,IAAK;gBACrB,CAAC,SAAS,SAAS,GAAG,OAAO,GAAG;gBAChC,IAAI,6BAA6B,QAC7B,YAAY,uPAAA,CAAA,WAAQ,CAAC,QAAQ,EAAE;oBAC/B;gBACJ;gBACA,MAAM,QAAQ,KAAK,MAAM,CAAC,IAAI,CAAC;gBAC/B,IAAI,CAAC,OAAO;oBACR,MAAM,OAAO,OAAO,IAAI,CAAC,UAAU;oBACnC,IAAI,QAAQ,iBAAiB,EAAE;wBAC3B,IAAI,CAAC,cAAc,CAAC,SAAS,SAAS,UAAU;oBACpD;oBACA;gBACJ;gBACA,UAAU,SAAS,QAAQ,OAAO,UAAU;YAChD;YACA,IAAI,4BAA4B,oEAAoE;YAChG,CAAC,YAAY,uPAAA,CAAA,WAAQ,CAAC,QAAQ,IAAI,YAAY,qBAAqB,GAAG;gBACtE,MAAM,IAAI,MAAM,CAAC,qBAAqB,CAAC;YAC3C;QACJ;QACA;QACA,cAAa,OAAO,EAAE,MAAM,EAAE,OAAO;YACjC,MAAM,OAAO,QAAQ,OAAO;YAC5B,KAAK,MAAM,SAAS,KAAK,MAAM,CAAC,QAAQ,GAAI;gBACxC,IAAI,CAAC,CAAA,GAAA,uPAAA,CAAA,aAAU,AAAD,EAAE,OAAO,UAAU;oBAC7B,IAAI,MAAM,GAAG,EAAE;wBACX,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,KAAK,QAAQ,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,kCAAkC,CAAC;oBAC1G;oBACA;gBACJ;gBACA,MAAM,QAAQ,MAAM,KAAK,GACnB,OAAO,CAAC,MAAM,KAAK,CAAC,SAAS,CAAC,CAAC,KAAK,GACpC,OAAO,CAAC,MAAM,SAAS,CAAC;gBAC9B,WAAW,OAAO,OAAO,QAAQ;YACrC;YACA,IAAI,QAAQ,kBAAkB,EAAE;gBAC5B,IAAI,CAAC,kBAAkB,CAAC,SAAS;YACrC;YACA,OAAO;QACX;QACA,YAAW,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO;YACpC,uEAAuE;YACvE,yDAAyD;YACzD,uEAAuE;YACvE,qDAAqD;YACrD,IAAI,UAAU,WAAW;gBACrB,OAAO;YACX;YACA,WAAW,OAAO,OAAO,QAAQ;QACrC;IACJ;AACJ;AACA,SAAS,UAAU,MAAM,EACzB,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO;IAC5B,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG;IAC9B,IAAI,MAAM,KAAK,EAAE;QACb,SAAS,MAAM,CAAC,MAAM,KAAK,CAAC,SAAS,CAAC;QACtC,IAAI,OAAO,IAAI,IAAI,WAAW;YAC1B,OAAO,OAAO,KAAK;QACvB;QACA,OAAO,IAAI,GAAG;QACd,YAAY;IAChB;IACA,OAAQ,MAAM,IAAI;QACd,KAAK;QACL,KAAK;YACD,MAAM,aAAa,MAAM,IAAI,IAAI,SAAS,2OAAA,CAAA,aAAU,CAAC,KAAK,GAAG,MAAM,CAAC;YACpE,IAAI,OAAO;YACX,kHAAkH;YAClH,IAAI,MAAM,IAAI,IAAI,YAAY,MAAM,CAAC,GAAG,GAAG;gBACvC,OAAO;YACX;YACA,IAAI,UAAU;gBACV,IAAI,MAAM,MAAM,CAAC,UAAU,EAAE,yEAAyE;gBACtG,MAAM,WAAW,YAAY,uPAAA,CAAA,WAAQ,CAAC,eAAe,IACjD,cAAc,2OAAA,CAAA,aAAU,CAAC,MAAM,IAC/B,cAAc,2OAAA,CAAA,aAAU,CAAC,KAAK;gBAClC,IAAI,UAAU;oBACV,IAAI,IAAI,OAAO,MAAM,KAAK,OAAO,GAAG;oBACpC,MAAO,OAAO,GAAG,GAAG,EAAG;wBACnB,IAAI,IAAI,CAAC,KAAK,QAAQ;oBAC1B;gBACJ,OACK;oBACD,IAAI,IAAI,CAAC,KAAK,QAAQ;gBAC1B;YACJ,OACK;gBACD,MAAM,CAAC,UAAU,GAAG,KAAK,QAAQ;YACrC;YACA;QACJ,KAAK;YACD,MAAM,cAAc,MAAM,CAAC;YAC3B,IAAI,UAAU;gBACV,yEAAyE;gBACzE,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,iBAAiB,QAAQ,IAAI,eAAe,SAAS;YAChF,OACK;gBACD,IAAI,CAAA,GAAA,kPAAA,CAAA,YAAS,AAAD,EAAE,MAAM,CAAC,UAAU,GAAG;oBAC9B,iBAAiB,QAAQ,MAAM,CAAC,UAAU,EAAE,SAAS;gBACzD,OACK;oBACD,MAAM,CAAC,UAAU,GAAG,iBAAiB,QAAQ,IAAI,eAAe,SAAS;oBACzE,IAAI,YAAY,YAAY,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,QAAQ,EAAE;wBAC7D,MAAM,CAAC,UAAU,GAAG,YAAY,YAAY,CAAC,WAAW,CAAC,MAAM,CAAC,UAAU;oBAC9E;gBACJ;YACJ;YACA;QACJ,KAAK;YACD,IAAI,CAAC,QAAQ,OAAO,GAAG,aAAa,OAAO,QAAQ;YACnD,8EAA8E;YAC9E,MAAM,CAAC,UAAU,CAAC,OAAO,GAAG;YAC5B;IACR;AACJ;AACA,kEAAkE;AAClE,2CAA2C;AAC3C,SAAS,iBAAiB,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK;IACrD,MAAM,SAAS,QAAQ,OAAO,GAAG,OAAO,CAAC,GAAG;IAC5C,MAAM,YAAY,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,SAAS;IAC/E,OAAO,WAAW,CAAC,SAAS,QAAQ,YAAY,MAAM,EAAE,GAAG,OAAO,MAAM,IACxE,SAAS;IACT,OAAO;AACX;AACA,6DAA6D;AAC7D,SAAS,aAAa,KAAK,EAAE,MAAM,EAAE,OAAO;IACxC,MAAM,SAAS,OAAO,MAAM,IAAI,MAAM,OAAO,GAAG,GAAG;IACnD,IAAI,KAAK;IACT,MAAO,OAAO,GAAG,GAAG,IAAK;QACrB,MAAM,CAAC,QAAQ,GAAG,OAAO,GAAG;QAC5B,OAAQ;YACJ,KAAK;gBACD,MAAM,WAAW,QAAQ,MAAM,CAAC;gBAChC;YACJ,KAAK;gBACD,OAAQ,MAAM,CAAC,CAAC,IAAI;oBAChB,KAAK;wBACD,MAAM,WAAW,QAAQ,MAAM,CAAC,CAAC,CAAC;wBAClC;oBACJ,KAAK;wBACD,MAAM,OAAO,KAAK;wBAClB;oBACJ,KAAK;wBACD,MAAM,iBAAiB,QAAQ,IAAI,MAAM,CAAC,CAAC,CAAC,IAAI,SAAS;wBACzD;gBACR;gBACA;QACR;IACJ;IACA,IAAI,QAAQ,WAAW;QACnB,MAAM,CAAA,GAAA,uPAAA,CAAA,kBAAe,AAAD,EAAE,MAAM,CAAC,EAAE,2OAAA,CAAA,WAAQ,CAAC,MAAM;IAClD;IACA,IAAI,OAAO,OAAO,YAAY,OAAO,OAAO,UAAU;QAClD,MAAM,IAAI,QAAQ;IACtB;IACA,IAAI,QAAQ,WAAW;QACnB,OAAQ,MAAM,CAAC,CAAC,IAAI;YAChB,KAAK;gBACD,MAAM,CAAA,GAAA,uPAAA,CAAA,kBAAe,AAAD,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,2OAAA,CAAA,WAAQ,CAAC,MAAM;gBAChD;YACJ,KAAK;gBACD,MAAM,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE;gBAC5B;YACJ,KAAK;gBACD,MAAM,IAAI,MAAM,CAAC,CAAC,CAAC;gBACnB;QACR;IACJ;IACA,OAAO;QAAC;QAAK;KAAI;AACrB;AACA,wEAAwE;AACxE,0DAA0D;AAC1D,SAAS,mBAAmB,MAAM,EAAE,IAAI;IACpC,MAAM,IAAI,WAAW,QAAQ;IAC7B,OAAO,OAAO,KAAK,WAAW,EAAE,QAAQ,KAAK;AACjD;AACA,wDAAwD;AACxD,SAAS,WAAW,MAAM,EAAE,IAAI;IAC5B,OAAQ;QACJ,KAAK,2OAAA,CAAA,aAAU,CAAC,MAAM;YAClB,OAAO,OAAO,MAAM;QACxB,KAAK,2OAAA,CAAA,aAAU,CAAC,IAAI;YAChB,OAAO,OAAO,IAAI;QACtB,KAAK,2OAAA,CAAA,aAAU,CAAC,MAAM;YAClB,OAAO,OAAO,MAAM;QACxB,KAAK,2OAAA,CAAA,aAAU,CAAC,KAAK;YACjB,OAAO,OAAO,KAAK;QACvB,KAAK,2OAAA,CAAA,aAAU,CAAC,KAAK;YACjB,OAAO,OAAO,KAAK;QACvB,KAAK,2OAAA,CAAA,aAAU,CAAC,KAAK;YACjB,OAAO,OAAO,KAAK;QACvB,KAAK,2OAAA,CAAA,aAAU,CAAC,MAAM;YAClB,OAAO,OAAO,MAAM;QACxB,KAAK,2OAAA,CAAA,aAAU,CAAC,OAAO;YACnB,OAAO,OAAO,OAAO;QACzB,KAAK,2OAAA,CAAA,aAAU,CAAC,KAAK;YACjB,OAAO,OAAO,KAAK;QACvB,KAAK,2OAAA,CAAA,aAAU,CAAC,OAAO;YACnB,OAAO,OAAO,OAAO;QACzB,KAAK,2OAAA,CAAA,aAAU,CAAC,QAAQ;YACpB,OAAO,OAAO,QAAQ;QAC1B,KAAK,2OAAA,CAAA,aAAU,CAAC,QAAQ;YACpB,OAAO,OAAO,QAAQ;QAC1B,KAAK,2OAAA,CAAA,aAAU,CAAC,MAAM;YAClB,OAAO,OAAO,MAAM;QACxB,KAAK,2OAAA,CAAA,aAAU,CAAC,MAAM;YAClB,OAAO,OAAO,MAAM;QACxB,KAAK,2OAAA,CAAA,aAAU,CAAC,MAAM;YAClB,OAAO,OAAO,MAAM;IAC5B;AACJ;AACA,SAAS,WAAW,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO;IAC7C,CAAA,GAAA,sPAAA,CAAA,SAAM,AAAD,EAAE,UAAU;IACjB,MAAM,WAAW,MAAM,QAAQ;IAC/B,OAAQ,MAAM,IAAI;QACd,KAAK;QACL,KAAK;YACD,IAAI,aAAa,MAAM,IAAI,IAAI,SAAS,2OAAA,CAAA,aAAU,CAAC,KAAK,GAAG,MAAM,CAAC;YAClE,IAAI,UAAU;gBACV,CAAA,GAAA,sPAAA,CAAA,SAAM,AAAD,EAAE,MAAM,OAAO,CAAC;gBACrB,IAAI,MAAM,MAAM,EAAE;oBACd,YAAY,QAAQ,YAAY,MAAM,EAAE,EAAE;gBAC9C,OACK;oBACD,KAAK,MAAM,QAAQ,MAAO;wBACtB,YAAY,QAAQ,YAAY,MAAM,EAAE,EAAE;oBAC9C;gBACJ;YACJ,OACK;gBACD,YAAY,QAAQ,YAAY,MAAM,EAAE,EAAE;YAC9C;YACA;QACJ,KAAK;YACD,IAAI,UAAU;gBACV,CAAA,GAAA,sPAAA,CAAA,SAAM,AAAD,EAAE,MAAM,OAAO,CAAC;gBACrB,KAAK,MAAM,QAAQ,MAAO;oBACtB,kBAAkB,QAAQ,SAAS,OAAO;gBAC9C;YACJ,OACK;gBACD,kBAAkB,QAAQ,SAAS,OAAO;YAC9C;YACA;QACJ,KAAK;YACD,CAAA,GAAA,sPAAA,CAAA,SAAM,AAAD,EAAE,OAAO,SAAS,YAAY,SAAS;YAC5C,KAAK,MAAM,CAAC,KAAK,IAAI,IAAI,OAAO,OAAO,CAAC,OAAQ;gBAC5C,cAAc,QAAQ,SAAS,OAAO,KAAK;YAC/C;YACA;IACR;AACJ;AACO,SAAS,cAAc,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK;IAC5D,OAAO,GAAG,CAAC,MAAM,EAAE,EAAE,uPAAA,CAAA,WAAQ,CAAC,eAAe;IAC7C,OAAO,IAAI;IACX,gEAAgE;IAChE,0DAA0D;IAC1D,IAAI,WAAW;IACf,wIAAwI;IACxI,OAAQ,MAAM,CAAC;QACX,KAAK,2OAAA,CAAA,aAAU,CAAC,KAAK;QACrB,KAAK,2OAAA,CAAA,aAAU,CAAC,OAAO;QACvB,KAAK,2OAAA,CAAA,aAAU,CAAC,MAAM;QACtB,KAAK,2OAAA,CAAA,aAAU,CAAC,QAAQ;QACxB,KAAK,2OAAA,CAAA,aAAU,CAAC,MAAM;YAClB,WAAW,OAAO,QAAQ,CAAC;YAC3B;QACJ,KAAK,2OAAA,CAAA,aAAU,CAAC,IAAI;YAChB,CAAA,GAAA,sPAAA,CAAA,SAAM,AAAD,EAAE,OAAO,UAAU,OAAO;YAC/B,WAAW,OAAO;YAClB;IACR;IACA,4CAA4C;IAC5C,YAAY,QAAQ,MAAM,CAAC,EAAE,GAAG;IAChC,gDAAgD;IAChD,OAAQ,MAAM,CAAC,CAAC,IAAI;QAChB,KAAK;YACD,YAAY,QAAQ,MAAM,CAAC,CAAC,CAAC,EAAE,GAAG;YAClC;QACJ,KAAK;YACD,YAAY,QAAQ,2OAAA,CAAA,aAAU,CAAC,KAAK,EAAE,GAAG;YACzC;QACJ,KAAK;YACD,CAAA,GAAA,sPAAA,CAAA,SAAM,AAAD,EAAE,UAAU;YACjB,OAAO,GAAG,CAAC,GAAG,uPAAA,CAAA,WAAQ,CAAC,eAAe,EAAE,KAAK,CAAC,MAAM,QAAQ,CAAC;YAC7D;IACR;IACA,OAAO,IAAI;AACf;AACA,8BAA8B;AAC9B,SAAS,kBAAkB,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK;IACpD,MAAM,UAAU,CAAA,GAAA,gQAAA,CAAA,YAAS,AAAD,EAAE,MAAM,CAAC,EAAE;IACnC,yEAAyE;IACzE,IAAI,MAAM,SAAS,EACf,OACK,GAAG,CAAC,MAAM,EAAE,EAAE,uPAAA,CAAA,WAAQ,CAAC,UAAU,EACjC,GAAG,CAAC,QAAQ,QAAQ,CAAC,UACrB,GAAG,CAAC,MAAM,EAAE,EAAE,uPAAA,CAAA,WAAQ,CAAC,QAAQ;SAEpC,OACK,GAAG,CAAC,MAAM,EAAE,EAAE,uPAAA,CAAA,WAAQ,CAAC,eAAe,EACtC,KAAK,CAAC,QAAQ,QAAQ,CAAC;AACpC;AACA,SAAS,YAAY,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK;IAC7C,CAAA,GAAA,sPAAA,CAAA,SAAM,AAAD,EAAE,UAAU;IACjB,IAAI,CAAC,UAAU,OAAO,GAAG,eAAe;IACxC,OAAO,GAAG,CAAC,SAAS,SAAS,CAAC,OAAO,CAAC;AAC1C;AACA,SAAS,YAAY,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK;IAC7C,IAAI,CAAC,MAAM,MAAM,EAAE;QACf;IACJ;IACA,OAAO,GAAG,CAAC,SAAS,uPAAA,CAAA,WAAQ,CAAC,eAAe,EAAE,IAAI;IAClD,IAAI,GAAG,OAAO,GAAG,eAAe;IAChC,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;QACnC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;IAC3B;IACA,OAAO,IAAI;AACf;AACA;;;;;;;;;CASC,GACD,uEAAuE;AACvE,SAAS,eAAe,IAAI;IACxB,IAAI,WAAW,uPAAA,CAAA,WAAQ,CAAC,MAAM;IAC9B,+HAA+H;IAC/H,OAAQ;QACJ,KAAK,2OAAA,CAAA,aAAU,CAAC,KAAK;QACrB,KAAK,2OAAA,CAAA,aAAU,CAAC,MAAM;YAClB,WAAW,uPAAA,CAAA,WAAQ,CAAC,eAAe;YACnC;QACJ,KAAK,2OAAA,CAAA,aAAU,CAAC,MAAM;QACtB,KAAK,2OAAA,CAAA,aAAU,CAAC,OAAO;QACvB,KAAK,2OAAA,CAAA,aAAU,CAAC,QAAQ;YACpB,WAAW,uPAAA,CAAA,WAAQ,CAAC,KAAK;YACzB;QACJ,KAAK,2OAAA,CAAA,aAAU,CAAC,OAAO;QACvB,KAAK,2OAAA,CAAA,aAAU,CAAC,QAAQ;QACxB,KAAK,2OAAA,CAAA,aAAU,CAAC,KAAK;YACjB,WAAW,uPAAA,CAAA,WAAQ,CAAC,KAAK;YACzB;IACR;IACA,MAAM,SAAS,2OAAA,CAAA,aAAU,CAAC,KAAK,CAAC,WAAW;IAC3C,OAAO;QAAC;QAAU;KAAO;AAC7B", "ignoreList": [0]}}, {"offset": {"line": 2785, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@bufbuild+protobuf@1.10.1/node_modules/@bufbuild/protobuf/dist/esm/private/util-common.js"], "sourcesContent": ["// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nimport { setEnumType } from \"./enum.js\";\nimport { Message } from \"../message.js\";\nimport { scalarEquals } from \"./scalars.js\";\nimport { ScalarType } from \"../scalar.js\";\nimport { isMessage } from \"../is-message.js\";\n/* eslint-disable @typescript-eslint/no-explicit-any,@typescript-eslint/no-unsafe-assignment,@typescript-eslint/no-unsafe-member-access,@typescript-eslint/no-unsafe-return,@typescript-eslint/no-unsafe-argument,no-case-declarations */\nexport function makeUtilCommon() {\n    return {\n        setEnumType,\n        initPartial(source, target) {\n            if (source === undefined) {\n                return;\n            }\n            const type = target.getType();\n            for (const member of type.fields.byMember()) {\n                const localName = member.localName, t = target, s = source;\n                if (s[localName] == null) {\n                    // TODO if source is a Message instance, we should use isFieldSet() here to support future field presence\n                    continue;\n                }\n                switch (member.kind) {\n                    case \"oneof\":\n                        const sk = s[localName].case;\n                        if (sk === undefined) {\n                            continue;\n                        }\n                        const sourceField = member.findField(sk);\n                        let val = s[localName].value;\n                        if (sourceField &&\n                            sourceField.kind == \"message\" &&\n                            !isMessage(val, sourceField.T)) {\n                            val = new sourceField.T(val);\n                        }\n                        else if (sourceField &&\n                            sourceField.kind === \"scalar\" &&\n                            sourceField.T === ScalarType.BYTES) {\n                            val = toU8Arr(val);\n                        }\n                        t[localName] = { case: sk, value: val };\n                        break;\n                    case \"scalar\":\n                    case \"enum\":\n                        let copy = s[localName];\n                        if (member.T === ScalarType.BYTES) {\n                            copy = member.repeated\n                                ? copy.map(toU8Arr)\n                                : toU8Arr(copy);\n                        }\n                        t[localName] = copy;\n                        break;\n                    case \"map\":\n                        switch (member.V.kind) {\n                            case \"scalar\":\n                            case \"enum\":\n                                if (member.V.T === ScalarType.BYTES) {\n                                    for (const [k, v] of Object.entries(s[localName])) {\n                                        t[localName][k] = toU8Arr(v);\n                                    }\n                                }\n                                else {\n                                    Object.assign(t[localName], s[localName]);\n                                }\n                                break;\n                            case \"message\":\n                                const messageType = member.V.T;\n                                for (const k of Object.keys(s[localName])) {\n                                    let val = s[localName][k];\n                                    if (!messageType.fieldWrapper) {\n                                        // We only take partial input for messages that are not a wrapper type.\n                                        // For those messages, we recursively normalize the partial input.\n                                        val = new messageType(val);\n                                    }\n                                    t[localName][k] = val;\n                                }\n                                break;\n                        }\n                        break;\n                    case \"message\":\n                        const mt = member.T;\n                        if (member.repeated) {\n                            t[localName] = s[localName].map((val) => isMessage(val, mt) ? val : new mt(val));\n                        }\n                        else {\n                            const val = s[localName];\n                            if (mt.fieldWrapper) {\n                                if (\n                                // We can't use BytesValue.typeName as that will create a circular import\n                                mt.typeName === \"google.protobuf.BytesValue\") {\n                                    t[localName] = toU8Arr(val);\n                                }\n                                else {\n                                    t[localName] = val;\n                                }\n                            }\n                            else {\n                                t[localName] = isMessage(val, mt) ? val : new mt(val);\n                            }\n                        }\n                        break;\n                }\n            }\n        },\n        // TODO use isFieldSet() here to support future field presence\n        equals(type, a, b) {\n            if (a === b) {\n                return true;\n            }\n            if (!a || !b) {\n                return false;\n            }\n            return type.fields.byMember().every((m) => {\n                const va = a[m.localName];\n                const vb = b[m.localName];\n                if (m.repeated) {\n                    if (va.length !== vb.length) {\n                        return false;\n                    }\n                    // eslint-disable-next-line @typescript-eslint/switch-exhaustiveness-check -- repeated fields are never \"map\"\n                    switch (m.kind) {\n                        case \"message\":\n                            return va.every((a, i) => m.T.equals(a, vb[i]));\n                        case \"scalar\":\n                            return va.every((a, i) => scalarEquals(m.T, a, vb[i]));\n                        case \"enum\":\n                            return va.every((a, i) => scalarEquals(ScalarType.INT32, a, vb[i]));\n                    }\n                    throw new Error(`repeated cannot contain ${m.kind}`);\n                }\n                switch (m.kind) {\n                    case \"message\":\n                        let a = va;\n                        let b = vb;\n                        if (m.T.fieldWrapper) {\n                            if (a !== undefined && !isMessage(a)) {\n                                a = m.T.fieldWrapper.wrapField(a);\n                            }\n                            if (b !== undefined && !isMessage(b)) {\n                                b = m.T.fieldWrapper.wrapField(b);\n                            }\n                        }\n                        return m.T.equals(a, b);\n                    case \"enum\":\n                        return scalarEquals(ScalarType.INT32, va, vb);\n                    case \"scalar\":\n                        return scalarEquals(m.T, va, vb);\n                    case \"oneof\":\n                        if (va.case !== vb.case) {\n                            return false;\n                        }\n                        const s = m.findField(va.case);\n                        if (s === undefined) {\n                            return true;\n                        }\n                        // eslint-disable-next-line @typescript-eslint/switch-exhaustiveness-check -- oneof fields are never \"map\"\n                        switch (s.kind) {\n                            case \"message\":\n                                return s.T.equals(va.value, vb.value);\n                            case \"enum\":\n                                return scalarEquals(ScalarType.INT32, va.value, vb.value);\n                            case \"scalar\":\n                                return scalarEquals(s.T, va.value, vb.value);\n                        }\n                        throw new Error(`oneof cannot contain ${s.kind}`);\n                    case \"map\":\n                        const keys = Object.keys(va).concat(Object.keys(vb));\n                        switch (m.V.kind) {\n                            case \"message\":\n                                const messageType = m.V.T;\n                                return keys.every((k) => messageType.equals(va[k], vb[k]));\n                            case \"enum\":\n                                return keys.every((k) => scalarEquals(ScalarType.INT32, va[k], vb[k]));\n                            case \"scalar\":\n                                const scalarType = m.V.T;\n                                return keys.every((k) => scalarEquals(scalarType, va[k], vb[k]));\n                        }\n                        break;\n                }\n            });\n        },\n        // TODO use isFieldSet() here to support future field presence\n        clone(message) {\n            const type = message.getType(), target = new type(), any = target;\n            for (const member of type.fields.byMember()) {\n                const source = message[member.localName];\n                let copy;\n                if (member.repeated) {\n                    copy = source.map(cloneSingularField);\n                }\n                else if (member.kind == \"map\") {\n                    copy = any[member.localName];\n                    for (const [key, v] of Object.entries(source)) {\n                        copy[key] = cloneSingularField(v);\n                    }\n                }\n                else if (member.kind == \"oneof\") {\n                    const f = member.findField(source.case);\n                    copy = f\n                        ? { case: source.case, value: cloneSingularField(source.value) }\n                        : { case: undefined };\n                }\n                else {\n                    copy = cloneSingularField(source);\n                }\n                any[member.localName] = copy;\n            }\n            for (const uf of type.runtime.bin.listUnknownFields(message)) {\n                type.runtime.bin.onUnknownField(any, uf.no, uf.wireType, uf.data);\n            }\n            return target;\n        },\n    };\n}\n// clone a single field value - i.e. the element type of repeated fields, the value type of maps\nfunction cloneSingularField(value) {\n    if (value === undefined) {\n        return value;\n    }\n    if (isMessage(value)) {\n        return value.clone();\n    }\n    if (value instanceof Uint8Array) {\n        const c = new Uint8Array(value.byteLength);\n        c.set(value);\n        return c;\n    }\n    return value;\n}\n// converts any ArrayLike<number> to Uint8Array if necessary.\nfunction toU8Arr(input) {\n    return input instanceof Uint8Array ? input : new Uint8Array(input);\n}\n"], "names": [], "mappings": "AAAA,6CAA6C;AAC7C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;;;AACjC;AAEA;AACA;AACA;;;;;;AAEO,SAAS;IACZ,OAAO;QACH,aAAA,oPAAA,CAAA,cAAW;QACX,aAAY,MAAM,EAAE,MAAM;YACtB,IAAI,WAAW,WAAW;gBACtB;YACJ;YACA,MAAM,OAAO,OAAO,OAAO;YAC3B,KAAK,MAAM,UAAU,KAAK,MAAM,CAAC,QAAQ,GAAI;gBACzC,MAAM,YAAY,OAAO,SAAS,EAAE,IAAI,QAAQ,IAAI;gBACpD,IAAI,CAAC,CAAC,UAAU,IAAI,MAAM;oBAEtB;gBACJ;gBACA,OAAQ,OAAO,IAAI;oBACf,KAAK;wBACD,MAAM,KAAK,CAAC,CAAC,UAAU,CAAC,IAAI;wBAC5B,IAAI,OAAO,WAAW;4BAClB;wBACJ;wBACA,MAAM,cAAc,OAAO,SAAS,CAAC;wBACrC,IAAI,MAAM,CAAC,CAAC,UAAU,CAAC,KAAK;wBAC5B,IAAI,eACA,YAAY,IAAI,IAAI,aACpB,CAAC,CAAA,GAAA,kPAAA,CAAA,YAAS,AAAD,EAAE,KAAK,YAAY,CAAC,GAAG;4BAChC,MAAM,IAAI,YAAY,CAAC,CAAC;wBAC5B,OACK,IAAI,eACL,YAAY,IAAI,KAAK,YACrB,YAAY,CAAC,KAAK,2OAAA,CAAA,aAAU,CAAC,KAAK,EAAE;4BACpC,MAAM,QAAQ;wBAClB;wBACA,CAAC,CAAC,UAAU,GAAG;4BAAE,MAAM;4BAAI,OAAO;wBAAI;wBACtC;oBACJ,KAAK;oBACL,KAAK;wBACD,IAAI,OAAO,CAAC,CAAC,UAAU;wBACvB,IAAI,OAAO,CAAC,KAAK,2OAAA,CAAA,aAAU,CAAC,KAAK,EAAE;4BAC/B,OAAO,OAAO,QAAQ,GAChB,KAAK,GAAG,CAAC,WACT,QAAQ;wBAClB;wBACA,CAAC,CAAC,UAAU,GAAG;wBACf;oBACJ,KAAK;wBACD,OAAQ,OAAO,CAAC,CAAC,IAAI;4BACjB,KAAK;4BACL,KAAK;gCACD,IAAI,OAAO,CAAC,CAAC,CAAC,KAAK,2OAAA,CAAA,aAAU,CAAC,KAAK,EAAE;oCACjC,KAAK,MAAM,CAAC,GAAG,EAAE,IAAI,OAAO,OAAO,CAAC,CAAC,CAAC,UAAU,EAAG;wCAC/C,CAAC,CAAC,UAAU,CAAC,EAAE,GAAG,QAAQ;oCAC9B;gCACJ,OACK;oCACD,OAAO,MAAM,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,UAAU;gCAC5C;gCACA;4BACJ,KAAK;gCACD,MAAM,cAAc,OAAO,CAAC,CAAC,CAAC;gCAC9B,KAAK,MAAM,KAAK,OAAO,IAAI,CAAC,CAAC,CAAC,UAAU,EAAG;oCACvC,IAAI,MAAM,CAAC,CAAC,UAAU,CAAC,EAAE;oCACzB,IAAI,CAAC,YAAY,YAAY,EAAE;wCAC3B,uEAAuE;wCACvE,kEAAkE;wCAClE,MAAM,IAAI,YAAY;oCAC1B;oCACA,CAAC,CAAC,UAAU,CAAC,EAAE,GAAG;gCACtB;gCACA;wBACR;wBACA;oBACJ,KAAK;wBACD,MAAM,KAAK,OAAO,CAAC;wBACnB,IAAI,OAAO,QAAQ,EAAE;4BACjB,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,MAAQ,CAAA,GAAA,kPAAA,CAAA,YAAS,AAAD,EAAE,KAAK,MAAM,MAAM,IAAI,GAAG;wBAC/E,OACK;4BACD,MAAM,MAAM,CAAC,CAAC,UAAU;4BACxB,IAAI,GAAG,YAAY,EAAE;gCACjB,IACA,yEAAyE;gCACzE,GAAG,QAAQ,KAAK,8BAA8B;oCAC1C,CAAC,CAAC,UAAU,GAAG,QAAQ;gCAC3B,OACK;oCACD,CAAC,CAAC,UAAU,GAAG;gCACnB;4BACJ,OACK;gCACD,CAAC,CAAC,UAAU,GAAG,CAAA,GAAA,kPAAA,CAAA,YAAS,AAAD,EAAE,KAAK,MAAM,MAAM,IAAI,GAAG;4BACrD;wBACJ;wBACA;gBACR;YACJ;QACJ;QACA,8DAA8D;QAC9D,QAAO,IAAI,EAAE,CAAC,EAAE,CAAC;YACb,IAAI,MAAM,GAAG;gBACT,OAAO;YACX;YACA,IAAI,CAAC,KAAK,CAAC,GAAG;gBACV,OAAO;YACX;YACA,OAAO,KAAK,MAAM,CAAC,QAAQ,GAAG,KAAK,CAAC,CAAC;gBACjC,MAAM,KAAK,CAAC,CAAC,EAAE,SAAS,CAAC;gBACzB,MAAM,KAAK,CAAC,CAAC,EAAE,SAAS,CAAC;gBACzB,IAAI,EAAE,QAAQ,EAAE;oBACZ,IAAI,GAAG,MAAM,KAAK,GAAG,MAAM,EAAE;wBACzB,OAAO;oBACX;oBACA,6GAA6G;oBAC7G,OAAQ,EAAE,IAAI;wBACV,KAAK;4BACD,OAAO,GAAG,KAAK,CAAC,CAAC,GAAG,IAAM,EAAE,CAAC,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE;wBACjD,KAAK;4BACD,OAAO,GAAG,KAAK,CAAC,CAAC,GAAG,IAAM,CAAA,GAAA,uPAAA,CAAA,eAAY,AAAD,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE;wBACxD,KAAK;4BACD,OAAO,GAAG,KAAK,CAAC,CAAC,GAAG,IAAM,CAAA,GAAA,uPAAA,CAAA,eAAY,AAAD,EAAE,2OAAA,CAAA,aAAU,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,EAAE;oBACzE;oBACA,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,EAAE,IAAI,EAAE;gBACvD;gBACA,OAAQ,EAAE,IAAI;oBACV,KAAK;wBACD,IAAI,KAAI;wBACR,IAAI,KAAI;wBACR,IAAI,EAAE,CAAC,CAAC,YAAY,EAAE;4BAClB,IAAI,OAAM,aAAa,CAAC,CAAA,GAAA,kPAAA,CAAA,YAAS,AAAD,EAAE,KAAI;gCAClC,KAAI,EAAE,CAAC,CAAC,YAAY,CAAC,SAAS,CAAC;4BACnC;4BACA,IAAI,OAAM,aAAa,CAAC,CAAA,GAAA,kPAAA,CAAA,YAAS,AAAD,EAAE,KAAI;gCAClC,KAAI,EAAE,CAAC,CAAC,YAAY,CAAC,SAAS,CAAC;4BACnC;wBACJ;wBACA,OAAO,EAAE,CAAC,CAAC,MAAM,CAAC,IAAG;oBACzB,KAAK;wBACD,OAAO,CAAA,GAAA,uPAAA,CAAA,eAAY,AAAD,EAAE,2OAAA,CAAA,aAAU,CAAC,KAAK,EAAE,IAAI;oBAC9C,KAAK;wBACD,OAAO,CAAA,GAAA,uPAAA,CAAA,eAAY,AAAD,EAAE,EAAE,CAAC,EAAE,IAAI;oBACjC,KAAK;wBACD,IAAI,GAAG,IAAI,KAAK,GAAG,IAAI,EAAE;4BACrB,OAAO;wBACX;wBACA,MAAM,IAAI,EAAE,SAAS,CAAC,GAAG,IAAI;wBAC7B,IAAI,MAAM,WAAW;4BACjB,OAAO;wBACX;wBACA,0GAA0G;wBAC1G,OAAQ,EAAE,IAAI;4BACV,KAAK;gCACD,OAAO,EAAE,CAAC,CAAC,MAAM,CAAC,GAAG,KAAK,EAAE,GAAG,KAAK;4BACxC,KAAK;gCACD,OAAO,CAAA,GAAA,uPAAA,CAAA,eAAY,AAAD,EAAE,2OAAA,CAAA,aAAU,CAAC,KAAK,EAAE,GAAG,KAAK,EAAE,GAAG,KAAK;4BAC5D,KAAK;gCACD,OAAO,CAAA,GAAA,uPAAA,CAAA,eAAY,AAAD,EAAE,EAAE,CAAC,EAAE,GAAG,KAAK,EAAE,GAAG,KAAK;wBACnD;wBACA,MAAM,IAAI,MAAM,CAAC,qBAAqB,EAAE,EAAE,IAAI,EAAE;oBACpD,KAAK;wBACD,MAAM,OAAO,OAAO,IAAI,CAAC,IAAI,MAAM,CAAC,OAAO,IAAI,CAAC;wBAChD,OAAQ,EAAE,CAAC,CAAC,IAAI;4BACZ,KAAK;gCACD,MAAM,cAAc,EAAE,CAAC,CAAC,CAAC;gCACzB,OAAO,KAAK,KAAK,CAAC,CAAC,IAAM,YAAY,MAAM,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE;4BAC5D,KAAK;gCACD,OAAO,KAAK,KAAK,CAAC,CAAC,IAAM,CAAA,GAAA,uPAAA,CAAA,eAAY,AAAD,EAAE,2OAAA,CAAA,aAAU,CAAC,KAAK,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE;4BACxE,KAAK;gCACD,MAAM,aAAa,EAAE,CAAC,CAAC,CAAC;gCACxB,OAAO,KAAK,KAAK,CAAC,CAAC,IAAM,CAAA,GAAA,uPAAA,CAAA,eAAY,AAAD,EAAE,YAAY,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE;wBACtE;wBACA;gBACR;YACJ;QACJ;QACA,8DAA8D;QAC9D,OAAM,OAAO;YACT,MAAM,OAAO,QAAQ,OAAO,IAAI,SAAS,IAAI,QAAQ,MAAM;YAC3D,KAAK,MAAM,UAAU,KAAK,MAAM,CAAC,QAAQ,GAAI;gBACzC,MAAM,SAAS,OAAO,CAAC,OAAO,SAAS,CAAC;gBACxC,IAAI;gBACJ,IAAI,OAAO,QAAQ,EAAE;oBACjB,OAAO,OAAO,GAAG,CAAC;gBACtB,OACK,IAAI,OAAO,IAAI,IAAI,OAAO;oBAC3B,OAAO,GAAG,CAAC,OAAO,SAAS,CAAC;oBAC5B,KAAK,MAAM,CAAC,KAAK,EAAE,IAAI,OAAO,OAAO,CAAC,QAAS;wBAC3C,IAAI,CAAC,IAAI,GAAG,mBAAmB;oBACnC;gBACJ,OACK,IAAI,OAAO,IAAI,IAAI,SAAS;oBAC7B,MAAM,IAAI,OAAO,SAAS,CAAC,OAAO,IAAI;oBACtC,OAAO,IACD;wBAAE,MAAM,OAAO,IAAI;wBAAE,OAAO,mBAAmB,OAAO,KAAK;oBAAE,IAC7D;wBAAE,MAAM;oBAAU;gBAC5B,OACK;oBACD,OAAO,mBAAmB;gBAC9B;gBACA,GAAG,CAAC,OAAO,SAAS,CAAC,GAAG;YAC5B;YACA,KAAK,MAAM,MAAM,KAAK,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,SAAU;gBAC1D,KAAK,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,KAAK,GAAG,EAAE,EAAE,GAAG,QAAQ,EAAE,GAAG,IAAI;YACpE;YACA,OAAO;QACX;IACJ;AACJ;AACA,gGAAgG;AAChG,SAAS,mBAAmB,KAAK;IAC7B,IAAI,UAAU,WAAW;QACrB,OAAO;IACX;IACA,IAAI,CAAA,GAAA,kPAAA,CAAA,YAAS,AAAD,EAAE,QAAQ;QAClB,OAAO,MAAM,KAAK;IACtB;IACA,IAAI,iBAAiB,YAAY;QAC7B,MAAM,IAAI,IAAI,WAAW,MAAM,UAAU;QACzC,EAAE,GAAG,CAAC;QACN,OAAO;IACX;IACA,OAAO;AACX;AACA,6DAA6D;AAC7D,SAAS,QAAQ,KAAK;IAClB,OAAO,iBAAiB,aAAa,QAAQ,IAAI,WAAW;AAChE", "ignoreList": [0]}}, {"offset": {"line": 3031, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@bufbuild+protobuf@1.10.1/node_modules/@bufbuild/protobuf/dist/esm/private/proto-runtime.js"], "sourcesContent": ["// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nimport { getEnumType, makeEnum, makeEnumType } from \"./enum.js\";\nimport { makeMessageType } from \"./message-type.js\";\nimport { makeExtension } from \"./extensions.js\";\nimport { makeJsonFormat } from \"./json-format.js\";\nimport { makeBinaryFormat } from \"./binary-format.js\";\nimport { makeUtilCommon } from \"./util-common.js\";\nexport function makeProtoRuntime(syntax, newFieldList, initFields) {\n    return {\n        syntax,\n        json: makeJsonFormat(),\n        bin: makeBinaryFormat(),\n        util: Object.assign(Object.assign({}, makeUtilCommon()), { newFieldList,\n            initFields }),\n        makeMessageType(typeName, fields, opt) {\n            return makeMessageType(this, typeName, fields, opt);\n        },\n        makeEnum,\n        makeEnumType,\n        getEnumType,\n        makeExtension(typeName, extendee, field) {\n            return makeExtension(this, typeName, extendee, field);\n        },\n    };\n}\n"], "names": [], "mappings": "AAAA,6CAA6C;AAC7C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;;;AACjC;AACA;AACA;AACA;AACA;AACA;;;;;;;AACO,SAAS,iBAAiB,MAAM,EAAE,YAAY,EAAE,UAAU;IAC7D,OAAO;QACH;QACA,MAAM,CAAA,GAAA,8PAAA,CAAA,iBAAc,AAAD;QACnB,KAAK,CAAA,GAAA,gQAAA,CAAA,mBAAgB,AAAD;QACpB,MAAM,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,CAAA,GAAA,8PAAA,CAAA,iBAAc,AAAD,MAAM;YAAE;YACvD;QAAW;QACf,iBAAgB,QAAQ,EAAE,MAAM,EAAE,GAAG;YACjC,OAAO,CAAA,GAAA,+PAAA,CAAA,kBAAe,AAAD,EAAE,IAAI,EAAE,UAAU,QAAQ;QACnD;QACA,UAAA,oPAAA,CAAA,WAAQ;QACR,cAAA,oPAAA,CAAA,eAAY;QACZ,aAAA,oPAAA,CAAA,cAAW;QACX,eAAc,QAAQ,EAAE,QAAQ,EAAE,KAAK;YACnC,OAAO,CAAA,GAAA,0PAAA,CAAA,gBAAa,AAAD,EAAE,IAAI,EAAE,UAAU,UAAU;QACnD;IACJ;AACJ", "ignoreList": [0]}}, {"offset": {"line": 3085, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@bufbuild+protobuf@1.10.1/node_modules/@bufbuild/protobuf/dist/esm/private/field-list.js"], "sourcesContent": ["// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nexport class InternalFieldList {\n    constructor(fields, normalizer) {\n        this._fields = fields;\n        this._normalizer = normalizer;\n    }\n    findJsonName(jsonName) {\n        if (!this.jsonNames) {\n            const t = {};\n            for (const f of this.list()) {\n                t[f.jsonName] = t[f.name] = f;\n            }\n            this.jsonNames = t;\n        }\n        return this.jsonNames[jsonName];\n    }\n    find(fieldNo) {\n        if (!this.numbers) {\n            const t = {};\n            for (const f of this.list()) {\n                t[f.no] = f;\n            }\n            this.numbers = t;\n        }\n        return this.numbers[fieldNo];\n    }\n    list() {\n        if (!this.all) {\n            this.all = this._normalizer(this._fields);\n        }\n        return this.all;\n    }\n    byNumber() {\n        if (!this.numbersAsc) {\n            this.numbersAsc = this.list()\n                .concat()\n                .sort((a, b) => a.no - b.no);\n        }\n        return this.numbersAsc;\n    }\n    byMember() {\n        if (!this.members) {\n            this.members = [];\n            const a = this.members;\n            let o;\n            for (const f of this.list()) {\n                if (f.oneof) {\n                    if (f.oneof !== o) {\n                        o = f.oneof;\n                        a.push(o);\n                    }\n                }\n                else {\n                    a.push(f);\n                }\n            }\n        }\n        return this.members;\n    }\n}\n"], "names": [], "mappings": "AAAA,6CAA6C;AAC7C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;;;AAC1B,MAAM;IACT,YAAY,MAAM,EAAE,UAAU,CAAE;QAC5B,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,WAAW,GAAG;IACvB;IACA,aAAa,QAAQ,EAAE;QACnB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,MAAM,IAAI,CAAC;YACX,KAAK,MAAM,KAAK,IAAI,CAAC,IAAI,GAAI;gBACzB,CAAC,CAAC,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG;YAChC;YACA,IAAI,CAAC,SAAS,GAAG;QACrB;QACA,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS;IACnC;IACA,KAAK,OAAO,EAAE;QACV,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACf,MAAM,IAAI,CAAC;YACX,KAAK,MAAM,KAAK,IAAI,CAAC,IAAI,GAAI;gBACzB,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG;YACd;YACA,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ;IAChC;IACA,OAAO;QACH,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;YACX,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO;QAC5C;QACA,OAAO,IAAI,CAAC,GAAG;IACnB;IACA,WAAW;QACP,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAClB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,IAAI,GACtB,MAAM,GACN,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,EAAE,GAAG,EAAE,EAAE;QACnC;QACA,OAAO,IAAI,CAAC,UAAU;IAC1B;IACA,WAAW;QACP,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACf,IAAI,CAAC,OAAO,GAAG,EAAE;YACjB,MAAM,IAAI,IAAI,CAAC,OAAO;YACtB,IAAI;YACJ,KAAK,MAAM,KAAK,IAAI,CAAC,IAAI,GAAI;gBACzB,IAAI,EAAE,KAAK,EAAE;oBACT,IAAI,EAAE,KAAK,KAAK,GAAG;wBACf,IAAI,EAAE,KAAK;wBACX,EAAE,IAAI,CAAC;oBACX;gBACJ,OACK;oBACD,EAAE,IAAI,CAAC;gBACX;YACJ;QACJ;QACA,OAAO,IAAI,CAAC,OAAO;IACvB;AACJ", "ignoreList": [0]}}, {"offset": {"line": 3163, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@bufbuild+protobuf@1.10.1/node_modules/@bufbuild/protobuf/dist/esm/private/names.js"], "sourcesContent": ["// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n/**\n * Returns the name of a protobuf element in generated code.\n *\n * Field names - including oneofs - are converted to lowerCamelCase. For\n * messages, enumerations and services, the package name is stripped from\n * the type name. For nested messages and enumerations, the names are joined\n * with an underscore. For methods, the first character is made lowercase.\n */\nexport function localName(desc) {\n    switch (desc.kind) {\n        case \"field\":\n            return localFieldName(desc.name, desc.oneof !== undefined);\n        case \"oneof\":\n            return localOneofName(desc.name);\n        case \"enum\":\n        case \"message\":\n        case \"service\":\n        case \"extension\": {\n            const pkg = desc.file.proto.package;\n            const offset = pkg === undefined ? 0 : pkg.length + 1;\n            const name = desc.typeName.substring(offset).replace(/\\./g, \"_\");\n            // For services, we only care about safe identifiers, not safe object properties,\n            // but we have shipped v1 with a bug that respected object properties, and we\n            // do not want to introduce a breaking change, so we continue to escape for\n            // safe object properties.\n            // See https://github.com/bufbuild/protobuf-es/pull/391\n            return safeObjectProperty(safeIdentifier(name));\n        }\n        case \"enum_value\": {\n            let name = desc.name;\n            const sharedPrefix = desc.parent.sharedPrefix;\n            if (sharedPrefix !== undefined) {\n                name = name.substring(sharedPrefix.length);\n            }\n            return safeObjectProperty(name);\n        }\n        case \"rpc\": {\n            let name = desc.name;\n            if (name.length == 0) {\n                return name;\n            }\n            name = name[0].toLowerCase() + name.substring(1);\n            return safeObjectProperty(name);\n        }\n    }\n}\n/**\n * Returns the name of a field in generated code.\n */\nexport function localFieldName(protoName, inOneof) {\n    const name = protoCamelCase(protoName);\n    if (inOneof) {\n        // oneof member names are not properties, but values of the `case` property.\n        return name;\n    }\n    return safeObjectProperty(safeMessageProperty(name));\n}\n/**\n * Returns the name of a oneof group in generated code.\n */\nexport function localOneofName(protoName) {\n    return localFieldName(protoName, false);\n}\n/**\n * Returns the JSON name for a protobuf field, exactly like protoc does.\n */\nexport const fieldJsonName = protoCamelCase;\n/**\n * Finds a prefix shared by enum values, for example `MY_ENUM_` for\n * `enum MyEnum {MY_ENUM_A=0; MY_ENUM_B=1;}`.\n */\nexport function findEnumSharedPrefix(enumName, valueNames) {\n    const prefix = camelToSnakeCase(enumName) + \"_\";\n    for (const name of valueNames) {\n        if (!name.toLowerCase().startsWith(prefix)) {\n            return undefined;\n        }\n        const shortName = name.substring(prefix.length);\n        if (shortName.length == 0) {\n            return undefined;\n        }\n        if (/^\\d/.test(shortName)) {\n            // identifiers must not start with numbers\n            return undefined;\n        }\n    }\n    return prefix;\n}\n/**\n * Converts lowerCamelCase or UpperCamelCase into lower_snake_case.\n * This is used to find shared prefixes in an enum.\n */\nfunction camelToSnakeCase(camel) {\n    return (camel.substring(0, 1) + camel.substring(1).replace(/[A-Z]/g, (c) => \"_\" + c)).toLowerCase();\n}\n/**\n * Converts snake_case to protoCamelCase according to the convention\n * used by protoc to convert a field name to a JSON name.\n */\nfunction protoCamelCase(snakeCase) {\n    let capNext = false;\n    const b = [];\n    for (let i = 0; i < snakeCase.length; i++) {\n        let c = snakeCase.charAt(i);\n        switch (c) {\n            case \"_\":\n                capNext = true;\n                break;\n            case \"0\":\n            case \"1\":\n            case \"2\":\n            case \"3\":\n            case \"4\":\n            case \"5\":\n            case \"6\":\n            case \"7\":\n            case \"8\":\n            case \"9\":\n                b.push(c);\n                capNext = false;\n                break;\n            default:\n                if (capNext) {\n                    capNext = false;\n                    c = c.toUpperCase();\n                }\n                b.push(c);\n                break;\n        }\n    }\n    return b.join(\"\");\n}\n/**\n * Names that cannot be used for identifiers, such as class names,\n * but _can_ be used for object properties.\n */\nconst reservedIdentifiers = new Set([\n    // ECMAScript 2015 keywords\n    \"break\",\n    \"case\",\n    \"catch\",\n    \"class\",\n    \"const\",\n    \"continue\",\n    \"debugger\",\n    \"default\",\n    \"delete\",\n    \"do\",\n    \"else\",\n    \"export\",\n    \"extends\",\n    \"false\",\n    \"finally\",\n    \"for\",\n    \"function\",\n    \"if\",\n    \"import\",\n    \"in\",\n    \"instanceof\",\n    \"new\",\n    \"null\",\n    \"return\",\n    \"super\",\n    \"switch\",\n    \"this\",\n    \"throw\",\n    \"true\",\n    \"try\",\n    \"typeof\",\n    \"var\",\n    \"void\",\n    \"while\",\n    \"with\",\n    \"yield\",\n    // ECMAScript 2015 future reserved keywords\n    \"enum\",\n    \"implements\",\n    \"interface\",\n    \"let\",\n    \"package\",\n    \"private\",\n    \"protected\",\n    \"public\",\n    \"static\",\n    // Class name cannot be 'Object' when targeting ES5 with module CommonJS\n    \"Object\",\n    // TypeScript keywords that cannot be used for types (as opposed to variables)\n    \"bigint\",\n    \"number\",\n    \"boolean\",\n    \"string\",\n    \"object\",\n    // Identifiers reserved for the runtime, so we can generate legible code\n    \"globalThis\",\n    \"Uint8Array\",\n    \"Partial\",\n]);\n/**\n * Names that cannot be used for object properties because they are reserved\n * by built-in JavaScript properties.\n */\nconst reservedObjectProperties = new Set([\n    // names reserved by JavaScript\n    \"constructor\",\n    \"toString\",\n    \"toJSON\",\n    \"valueOf\",\n]);\n/**\n * Names that cannot be used for object properties because they are reserved\n * by the runtime.\n */\nconst reservedMessageProperties = new Set([\n    // names reserved by the runtime\n    \"getType\",\n    \"clone\",\n    \"equals\",\n    \"fromBinary\",\n    \"fromJson\",\n    \"fromJsonString\",\n    \"toBinary\",\n    \"toJson\",\n    \"toJsonString\",\n    // names reserved by the runtime for the future\n    \"toObject\",\n]);\nconst fallback = (name) => `${name}$`;\n/**\n * Will wrap names that are Object prototype properties or names reserved\n * for `Message`s.\n */\nconst safeMessageProperty = (name) => {\n    if (reservedMessageProperties.has(name)) {\n        return fallback(name);\n    }\n    return name;\n};\n/**\n * Names that cannot be used for object properties because they are reserved\n * by built-in JavaScript properties.\n */\nexport const safeObjectProperty = (name) => {\n    if (reservedObjectProperties.has(name)) {\n        return fallback(name);\n    }\n    return name;\n};\n/**\n * Names that can be used for identifiers or class properties\n */\nexport const safeIdentifier = (name) => {\n    if (reservedIdentifiers.has(name)) {\n        return fallback(name);\n    }\n    return name;\n};\n"], "names": [], "mappings": "AAAA,6CAA6C;AAC7C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC;;;;;;;CAOC;;;;;;;;;AACM,SAAS,UAAU,IAAI;IAC1B,OAAQ,KAAK,IAAI;QACb,KAAK;YACD,OAAO,eAAe,KAAK,IAAI,EAAE,KAAK,KAAK,KAAK;QACpD,KAAK;YACD,OAAO,eAAe,KAAK,IAAI;QACnC,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YAAa;gBACd,MAAM,MAAM,KAAK,IAAI,CAAC,KAAK,CAAC,OAAO;gBACnC,MAAM,SAAS,QAAQ,YAAY,IAAI,IAAI,MAAM,GAAG;gBACpD,MAAM,OAAO,KAAK,QAAQ,CAAC,SAAS,CAAC,QAAQ,OAAO,CAAC,OAAO;gBAC5D,iFAAiF;gBACjF,6EAA6E;gBAC7E,2EAA2E;gBAC3E,0BAA0B;gBAC1B,uDAAuD;gBACvD,OAAO,mBAAmB,eAAe;YAC7C;QACA,KAAK;YAAc;gBACf,IAAI,OAAO,KAAK,IAAI;gBACpB,MAAM,eAAe,KAAK,MAAM,CAAC,YAAY;gBAC7C,IAAI,iBAAiB,WAAW;oBAC5B,OAAO,KAAK,SAAS,CAAC,aAAa,MAAM;gBAC7C;gBACA,OAAO,mBAAmB;YAC9B;QACA,KAAK;YAAO;gBACR,IAAI,OAAO,KAAK,IAAI;gBACpB,IAAI,KAAK,MAAM,IAAI,GAAG;oBAClB,OAAO;gBACX;gBACA,OAAO,IAAI,CAAC,EAAE,CAAC,WAAW,KAAK,KAAK,SAAS,CAAC;gBAC9C,OAAO,mBAAmB;YAC9B;IACJ;AACJ;AAIO,SAAS,eAAe,SAAS,EAAE,OAAO;IAC7C,MAAM,OAAO,eAAe;IAC5B,IAAI,SAAS;QACT,4EAA4E;QAC5E,OAAO;IACX;IACA,OAAO,mBAAmB,oBAAoB;AAClD;AAIO,SAAS,eAAe,SAAS;IACpC,OAAO,eAAe,WAAW;AACrC;AAIO,MAAM,gBAAgB;AAKtB,SAAS,qBAAqB,QAAQ,EAAE,UAAU;IACrD,MAAM,SAAS,iBAAiB,YAAY;IAC5C,KAAK,MAAM,QAAQ,WAAY;QAC3B,IAAI,CAAC,KAAK,WAAW,GAAG,UAAU,CAAC,SAAS;YACxC,OAAO;QACX;QACA,MAAM,YAAY,KAAK,SAAS,CAAC,OAAO,MAAM;QAC9C,IAAI,UAAU,MAAM,IAAI,GAAG;YACvB,OAAO;QACX;QACA,IAAI,MAAM,IAAI,CAAC,YAAY;YACvB,0CAA0C;YAC1C,OAAO;QACX;IACJ;IACA,OAAO;AACX;AACA;;;CAGC,GACD,SAAS,iBAAiB,KAAK;IAC3B,OAAO,CAAC,MAAM,SAAS,CAAC,GAAG,KAAK,MAAM,SAAS,CAAC,GAAG,OAAO,CAAC,UAAU,CAAC,IAAM,MAAM,EAAE,EAAE,WAAW;AACrG;AACA;;;CAGC,GACD,SAAS,eAAe,SAAS;IAC7B,IAAI,UAAU;IACd,MAAM,IAAI,EAAE;IACZ,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;QACvC,IAAI,IAAI,UAAU,MAAM,CAAC;QACzB,OAAQ;YACJ,KAAK;gBACD,UAAU;gBACV;YACJ,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACD,EAAE,IAAI,CAAC;gBACP,UAAU;gBACV;YACJ;gBACI,IAAI,SAAS;oBACT,UAAU;oBACV,IAAI,EAAE,WAAW;gBACrB;gBACA,EAAE,IAAI,CAAC;gBACP;QACR;IACJ;IACA,OAAO,EAAE,IAAI,CAAC;AAClB;AACA;;;CAGC,GACD,MAAM,sBAAsB,IAAI,IAAI;IAChC,2BAA2B;IAC3B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,2CAA2C;IAC3C;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,wEAAwE;IACxE;IACA,8EAA8E;IAC9E;IACA;IACA;IACA;IACA;IACA,wEAAwE;IACxE;IACA;IACA;CACH;AACD;;;CAGC,GACD,MAAM,2BAA2B,IAAI,IAAI;IACrC,+BAA+B;IAC/B;IACA;IACA;IACA;CACH;AACD;;;CAGC,GACD,MAAM,4BAA4B,IAAI,IAAI;IACtC,gCAAgC;IAChC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,+CAA+C;IAC/C;CACH;AACD,MAAM,WAAW,CAAC,OAAS,GAAG,KAAK,CAAC,CAAC;AACrC;;;CAGC,GACD,MAAM,sBAAsB,CAAC;IACzB,IAAI,0BAA0B,GAAG,CAAC,OAAO;QACrC,OAAO,SAAS;IACpB;IACA,OAAO;AACX;AAKO,MAAM,qBAAqB,CAAC;IAC/B,IAAI,yBAAyB,GAAG,CAAC,OAAO;QACpC,OAAO,SAAS;IACpB;IACA,OAAO;AACX;AAIO,MAAM,iBAAiB,CAAC;IAC3B,IAAI,oBAAoB,GAAG,CAAC,OAAO;QAC/B,OAAO,SAAS;IACpB;IACA,OAAO;AACX", "ignoreList": [0]}}, {"offset": {"line": 3423, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@bufbuild+protobuf@1.10.1/node_modules/@bufbuild/protobuf/dist/esm/private/field.js"], "sourcesContent": ["// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nimport { localOneofName } from \"./names.js\";\nimport { assert } from \"./assert.js\";\nexport class InternalOneofInfo {\n    constructor(name) {\n        this.kind = \"oneof\";\n        this.repeated = false;\n        this.packed = false;\n        this.opt = false;\n        this.req = false;\n        this.default = undefined;\n        this.fields = [];\n        this.name = name;\n        this.localName = localOneofName(name);\n    }\n    addField(field) {\n        assert(field.oneof === this, `field ${field.name} not one of ${this.name}`);\n        this.fields.push(field);\n    }\n    findField(localName) {\n        if (!this._lookup) {\n            this._lookup = Object.create(null);\n            for (let i = 0; i < this.fields.length; i++) {\n                this._lookup[this.fields[i].localName] = this.fields[i];\n            }\n        }\n        return this._lookup[localName];\n    }\n}\n"], "names": [], "mappings": "AAAA,6CAA6C;AAC7C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;;;AACjC;AACA;;;AACO,MAAM;IACT,YAAY,IAAI,CAAE;QACd,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,MAAM,GAAG,EAAE;QAChB,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,SAAS,GAAG,CAAA,GAAA,qPAAA,CAAA,iBAAc,AAAD,EAAE;IACpC;IACA,SAAS,KAAK,EAAE;QACZ,CAAA,GAAA,sPAAA,CAAA,SAAM,AAAD,EAAE,MAAM,KAAK,KAAK,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,IAAI,EAAE;QAC1E,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;IACrB;IACA,UAAU,SAAS,EAAE;QACjB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACf,IAAI,CAAC,OAAO,GAAG,OAAO,MAAM,CAAC;YAC7B,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,IAAK;gBACzC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE;YAC3D;QACJ;QACA,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU;IAClC;AACJ", "ignoreList": [0]}}, {"offset": {"line": 3475, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@bufbuild+protobuf@1.10.1/node_modules/@bufbuild/protobuf/dist/esm/private/field-normalize.js"], "sourcesContent": ["// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nimport { InternalOneofInfo } from \"./field.js\";\nimport { fieldJsonName, localFieldName } from \"./names.js\";\nimport { LongType, ScalarType } from \"../scalar.js\";\n/**\n * Convert a collection of field info to an array of normalized FieldInfo.\n *\n * The argument `packedByDefault` specifies whether fields that do not specify\n * `packed` should be packed (proto3) or unpacked (proto2).\n */\nexport function normalizeFieldInfos(fieldInfos, packedByDefault) {\n    var _a, _b, _c, _d, _e, _f;\n    const r = [];\n    let o;\n    for (const field of typeof fieldInfos == \"function\"\n        ? fieldInfos()\n        : fieldInfos) {\n        const f = field;\n        f.localName = localFieldName(field.name, field.oneof !== undefined);\n        f.jsonName = (_a = field.jsonName) !== null && _a !== void 0 ? _a : fieldJsonName(field.name);\n        f.repeated = (_b = field.repeated) !== null && _b !== void 0 ? _b : false;\n        if (field.kind == \"scalar\") {\n            f.L = (_c = field.L) !== null && _c !== void 0 ? _c : LongType.BIGINT;\n        }\n        f.delimited = (_d = field.delimited) !== null && _d !== void 0 ? _d : false;\n        f.req = (_e = field.req) !== null && _e !== void 0 ? _e : false;\n        f.opt = (_f = field.opt) !== null && _f !== void 0 ? _f : false;\n        if (field.packed === undefined) {\n            if (packedByDefault) {\n                f.packed =\n                    field.kind == \"enum\" ||\n                        (field.kind == \"scalar\" &&\n                            field.T != ScalarType.BYTES &&\n                            field.T != ScalarType.STRING);\n            }\n            else {\n                f.packed = false;\n            }\n        }\n        // We do not surface options at this time\n        // f.options = field.options ?? emptyReadonlyObject;\n        if (field.oneof !== undefined) {\n            const ooname = typeof field.oneof == \"string\" ? field.oneof : field.oneof.name;\n            if (!o || o.name != ooname) {\n                o = new InternalOneofInfo(ooname);\n            }\n            f.oneof = o;\n            o.addField(f);\n        }\n        r.push(f);\n    }\n    return r;\n}\n"], "names": [], "mappings": "AAAA,6CAA6C;AAC7C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;;;AACjC;AACA;AACA;;;;AAOO,SAAS,oBAAoB,UAAU,EAAE,eAAe;IAC3D,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;IACxB,MAAM,IAAI,EAAE;IACZ,IAAI;IACJ,KAAK,MAAM,SAAS,OAAO,cAAc,aACnC,eACA,WAAY;QACd,MAAM,IAAI;QACV,EAAE,SAAS,GAAG,CAAA,GAAA,qPAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,IAAI,EAAE,MAAM,KAAK,KAAK;QACzD,EAAE,QAAQ,GAAG,CAAC,KAAK,MAAM,QAAQ,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,CAAA,GAAA,qPAAA,CAAA,gBAAa,AAAD,EAAE,MAAM,IAAI;QAC5F,EAAE,QAAQ,GAAG,CAAC,KAAK,MAAM,QAAQ,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;QACpE,IAAI,MAAM,IAAI,IAAI,UAAU;YACxB,EAAE,CAAC,GAAG,CAAC,KAAK,MAAM,CAAC,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,2OAAA,CAAA,WAAQ,CAAC,MAAM;QACzE;QACA,EAAE,SAAS,GAAG,CAAC,KAAK,MAAM,SAAS,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;QACtE,EAAE,GAAG,GAAG,CAAC,KAAK,MAAM,GAAG,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;QAC1D,EAAE,GAAG,GAAG,CAAC,KAAK,MAAM,GAAG,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;QAC1D,IAAI,MAAM,MAAM,KAAK,WAAW;YAC5B,IAAI,iBAAiB;gBACjB,EAAE,MAAM,GACJ,MAAM,IAAI,IAAI,UACT,MAAM,IAAI,IAAI,YACX,MAAM,CAAC,IAAI,2OAAA,CAAA,aAAU,CAAC,KAAK,IAC3B,MAAM,CAAC,IAAI,2OAAA,CAAA,aAAU,CAAC,MAAM;YAC5C,OACK;gBACD,EAAE,MAAM,GAAG;YACf;QACJ;QACA,yCAAyC;QACzC,oDAAoD;QACpD,IAAI,MAAM,KAAK,KAAK,WAAW;YAC3B,MAAM,SAAS,OAAO,MAAM,KAAK,IAAI,WAAW,MAAM,KAAK,GAAG,MAAM,KAAK,CAAC,IAAI;YAC9E,IAAI,CAAC,KAAK,EAAE,IAAI,IAAI,QAAQ;gBACxB,IAAI,IAAI,qPAAA,CAAA,oBAAiB,CAAC;YAC9B;YACA,EAAE,KAAK,GAAG;YACV,EAAE,QAAQ,CAAC;QACf;QACA,EAAE,IAAI,CAAC;IACX;IACA,OAAO;AACX", "ignoreList": [0]}}, {"offset": {"line": 3539, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@bufbuild+protobuf@1.10.1/node_modules/@bufbuild/protobuf/dist/esm/proto3.js"], "sourcesContent": ["// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nimport { makeProtoRuntime } from \"./private/proto-runtime.js\";\nimport { InternalFieldList } from \"./private/field-list.js\";\nimport { scalarZeroValue } from \"./private/scalars.js\";\nimport { normalizeFieldInfos } from \"./private/field-normalize.js\";\n/**\n * Provides functionality for messages defined with the proto3 syntax.\n */\nexport const proto3 = makeProtoRuntime(\"proto3\", (fields) => {\n    return new InternalFieldList(fields, (source) => normalizeFieldInfos(source, true));\n}, \n// TODO merge with proto2 and initExtensionField, also see initPartial, equals, clone\n(target) => {\n    for (const member of target.getType().fields.byMember()) {\n        if (member.opt) {\n            continue;\n        }\n        const name = member.localName, t = target;\n        if (member.repeated) {\n            t[name] = [];\n            continue;\n        }\n        switch (member.kind) {\n            case \"oneof\":\n                t[name] = { case: undefined };\n                break;\n            case \"enum\":\n                t[name] = 0;\n                break;\n            case \"map\":\n                t[name] = {};\n                break;\n            case \"scalar\":\n                t[name] = scalarZeroValue(member.T, member.L);\n                break;\n            case \"message\":\n                // message fields are always optional in proto3\n                break;\n        }\n    }\n});\n"], "names": [], "mappings": "AAAA,6CAA6C;AAC7C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;;;AACjC;AACA;AACA;AACA;;;;;AAIO,MAAM,SAAS,CAAA,GAAA,gQAAA,CAAA,mBAAgB,AAAD,EAAE,UAAU,CAAC;IAC9C,OAAO,IAAI,6PAAA,CAAA,oBAAiB,CAAC,QAAQ,CAAC,SAAW,CAAA,GAAA,kQAAA,CAAA,sBAAmB,AAAD,EAAE,QAAQ;AACjF,GACA,qFAAqF;AACrF,CAAC;IACG,KAAK,MAAM,UAAU,OAAO,OAAO,GAAG,MAAM,CAAC,QAAQ,GAAI;QACrD,IAAI,OAAO,GAAG,EAAE;YACZ;QACJ;QACA,MAAM,OAAO,OAAO,SAAS,EAAE,IAAI;QACnC,IAAI,OAAO,QAAQ,EAAE;YACjB,CAAC,CAAC,KAAK,GAAG,EAAE;YACZ;QACJ;QACA,OAAQ,OAAO,IAAI;YACf,KAAK;gBACD,CAAC,CAAC,KAAK,GAAG;oBAAE,MAAM;gBAAU;gBAC5B;YACJ,KAAK;gBACD,CAAC,CAAC,KAAK,GAAG;gBACV;YACJ,KAAK;gBACD,CAAC,CAAC,KAAK,GAAG,CAAC;gBACX;YACJ,KAAK;gBACD,CAAC,CAAC,KAAK,GAAG,CAAA,GAAA,uPAAA,CAAA,kBAAe,AAAD,EAAE,OAAO,CAAC,EAAE,OAAO,CAAC;gBAC5C;YACJ,KAAK;gBAED;QACR;IACJ;AACJ", "ignoreList": [0]}}, {"offset": {"line": 3602, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@bufbuild+protobuf@1.10.1/node_modules/@bufbuild/protobuf/dist/esm/google/protobuf/timestamp_pb.js"], "sourcesContent": ["// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nimport { Message } from \"../../message.js\";\nimport { protoInt64 } from \"../../proto-int64.js\";\nimport { proto3 } from \"../../proto3.js\";\n/**\n * A Timestamp represents a point in time independent of any time zone or local\n * calendar, encoded as a count of seconds and fractions of seconds at\n * nanosecond resolution. The count is relative to an epoch at UTC midnight on\n * January 1, 1970, in the proleptic Gregorian calendar which extends the\n * Gregorian calendar backwards to year one.\n *\n * All minutes are 60 seconds long. Leap seconds are \"smeared\" so that no leap\n * second table is needed for interpretation, using a [24-hour linear\n * smear](https://developers.google.com/time/smear).\n *\n * The range is from 0001-01-01T00:00:00Z to 9999-12-31T23:59:59.999999999Z. By\n * restricting to that range, we ensure that we can convert to and from [RFC\n * 3339](https://www.ietf.org/rfc/rfc3339.txt) date strings.\n *\n * # Examples\n *\n * Example 1: Compute Timestamp from POSIX `time()`.\n *\n *     Timestamp timestamp;\n *     timestamp.set_seconds(time(NULL));\n *     timestamp.set_nanos(0);\n *\n * Example 2: Compute Timestamp from POSIX `gettimeofday()`.\n *\n *     struct timeval tv;\n *     gettimeofday(&tv, NULL);\n *\n *     Timestamp timestamp;\n *     timestamp.set_seconds(tv.tv_sec);\n *     timestamp.set_nanos(tv.tv_usec * 1000);\n *\n * Example 3: Compute Timestamp from Win32 `GetSystemTimeAsFileTime()`.\n *\n *     FILETIME ft;\n *     GetSystemTimeAsFileTime(&ft);\n *     UINT64 ticks = (((UINT64)ft.dwHighDateTime) << 32) | ft.dwLowDateTime;\n *\n *     // A Windows tick is 100 nanoseconds. Windows epoch 1601-01-01T00:00:00Z\n *     // is 11644473600 seconds before Unix epoch 1970-01-01T00:00:00Z.\n *     Timestamp timestamp;\n *     timestamp.set_seconds((INT64) ((ticks / 10000000) - 11644473600LL));\n *     timestamp.set_nanos((INT32) ((ticks % 10000000) * 100));\n *\n * Example 4: Compute Timestamp from Java `System.currentTimeMillis()`.\n *\n *     long millis = System.currentTimeMillis();\n *\n *     Timestamp timestamp = Timestamp.newBuilder().setSeconds(millis / 1000)\n *         .setNanos((int) ((millis % 1000) * 1000000)).build();\n *\n * Example 5: Compute Timestamp from Java `Instant.now()`.\n *\n *     Instant now = Instant.now();\n *\n *     Timestamp timestamp =\n *         Timestamp.newBuilder().setSeconds(now.getEpochSecond())\n *             .setNanos(now.getNano()).build();\n *\n * Example 6: Compute Timestamp from current time in Python.\n *\n *     timestamp = Timestamp()\n *     timestamp.GetCurrentTime()\n *\n * # JSON Mapping\n *\n * In JSON format, the Timestamp type is encoded as a string in the\n * [RFC 3339](https://www.ietf.org/rfc/rfc3339.txt) format. That is, the\n * format is \"{year}-{month}-{day}T{hour}:{min}:{sec}[.{frac_sec}]Z\"\n * where {year} is always expressed using four digits while {month}, {day},\n * {hour}, {min}, and {sec} are zero-padded to two digits each. The fractional\n * seconds, which can go up to 9 digits (i.e. up to 1 nanosecond resolution),\n * are optional. The \"Z\" suffix indicates the timezone (\"UTC\"); the timezone\n * is required. A proto3 JSON serializer should always use UTC (as indicated by\n * \"Z\") when printing the Timestamp type and a proto3 JSON parser should be\n * able to accept both UTC and other timezones (as indicated by an offset).\n *\n * For example, \"2017-01-15T01:30:15.01Z\" encodes 15.01 seconds past\n * 01:30 UTC on January 15, 2017.\n *\n * In JavaScript, one can convert a Date object to this format using the\n * standard\n * [toISOString()](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Date/toISOString)\n * method. In Python, a standard `datetime.datetime` object can be converted\n * to this format using\n * [`strftime`](https://docs.python.org/2/library/time.html#time.strftime) with\n * the time format spec '%Y-%m-%dT%H:%M:%S.%fZ'. Likewise, in Java, one can use\n * the Joda Time's [`ISODateTimeFormat.dateTime()`](\n * http://joda-time.sourceforge.net/apidocs/org/joda/time/format/ISODateTimeFormat.html#dateTime()\n * ) to obtain a formatter capable of generating timestamps in this format.\n *\n *\n * @generated from message google.protobuf.Timestamp\n */\nexport class Timestamp extends Message {\n    constructor(data) {\n        super();\n        /**\n         * Represents seconds of UTC time since Unix epoch\n         * 1970-01-01T00:00:00Z. Must be from 0001-01-01T00:00:00Z to\n         * 9999-12-31T23:59:59Z inclusive.\n         *\n         * @generated from field: int64 seconds = 1;\n         */\n        this.seconds = protoInt64.zero;\n        /**\n         * Non-negative fractions of a second at nanosecond resolution. Negative\n         * second values with fractions must still have non-negative nanos values\n         * that count forward in time. Must be from 0 to 999,999,999\n         * inclusive.\n         *\n         * @generated from field: int32 nanos = 2;\n         */\n        this.nanos = 0;\n        proto3.util.initPartial(data, this);\n    }\n    fromJson(json, options) {\n        if (typeof json !== \"string\") {\n            throw new Error(`cannot decode google.protobuf.Timestamp from JSON: ${proto3.json.debug(json)}`);\n        }\n        const matches = json.match(/^([0-9]{4})-([0-9]{2})-([0-9]{2})T([0-9]{2}):([0-9]{2}):([0-9]{2})(?:Z|\\.([0-9]{3,9})Z|([+-][0-9][0-9]:[0-9][0-9]))$/);\n        if (!matches) {\n            throw new Error(`cannot decode google.protobuf.Timestamp from JSON: invalid RFC 3339 string`);\n        }\n        const ms = Date.parse(matches[1] + \"-\" + matches[2] + \"-\" + matches[3] + \"T\" + matches[4] + \":\" + matches[5] + \":\" + matches[6] + (matches[8] ? matches[8] : \"Z\"));\n        if (Number.isNaN(ms)) {\n            throw new Error(`cannot decode google.protobuf.Timestamp from JSON: invalid RFC 3339 string`);\n        }\n        if (ms < Date.parse(\"0001-01-01T00:00:00Z\") || ms > Date.parse(\"9999-12-31T23:59:59Z\")) {\n            throw new Error(`cannot decode message google.protobuf.Timestamp from JSON: must be from 0001-01-01T00:00:00Z to 9999-12-31T23:59:59Z inclusive`);\n        }\n        this.seconds = protoInt64.parse(ms / 1000);\n        this.nanos = 0;\n        if (matches[7]) {\n            this.nanos = (parseInt(\"1\" + matches[7] + \"0\".repeat(9 - matches[7].length)) - 1000000000);\n        }\n        return this;\n    }\n    toJson(options) {\n        const ms = Number(this.seconds) * 1000;\n        if (ms < Date.parse(\"0001-01-01T00:00:00Z\") || ms > Date.parse(\"9999-12-31T23:59:59Z\")) {\n            throw new Error(`cannot encode google.protobuf.Timestamp to JSON: must be from 0001-01-01T00:00:00Z to 9999-12-31T23:59:59Z inclusive`);\n        }\n        if (this.nanos < 0) {\n            throw new Error(`cannot encode google.protobuf.Timestamp to JSON: nanos must not be negative`);\n        }\n        let z = \"Z\";\n        if (this.nanos > 0) {\n            const nanosStr = (this.nanos + 1000000000).toString().substring(1);\n            if (nanosStr.substring(3) === \"000000\") {\n                z = \".\" + nanosStr.substring(0, 3) + \"Z\";\n            }\n            else if (nanosStr.substring(6) === \"000\") {\n                z = \".\" + nanosStr.substring(0, 6) + \"Z\";\n            }\n            else {\n                z = \".\" + nanosStr + \"Z\";\n            }\n        }\n        return new Date(ms).toISOString().replace(\".000Z\", z);\n    }\n    toDate() {\n        return new Date(Number(this.seconds) * 1000 + Math.ceil(this.nanos / 1000000));\n    }\n    static now() {\n        return Timestamp.fromDate(new Date());\n    }\n    static fromDate(date) {\n        const ms = date.getTime();\n        return new Timestamp({\n            seconds: protoInt64.parse(Math.floor(ms / 1000)),\n            nanos: (ms % 1000) * 1000000,\n        });\n    }\n    static fromBinary(bytes, options) {\n        return new Timestamp().fromBinary(bytes, options);\n    }\n    static fromJson(jsonValue, options) {\n        return new Timestamp().fromJson(jsonValue, options);\n    }\n    static fromJsonString(jsonString, options) {\n        return new Timestamp().fromJsonString(jsonString, options);\n    }\n    static equals(a, b) {\n        return proto3.util.equals(Timestamp, a, b);\n    }\n}\nTimestamp.runtime = proto3;\nTimestamp.typeName = \"google.protobuf.Timestamp\";\nTimestamp.fields = proto3.util.newFieldList(() => [\n    { no: 1, name: \"seconds\", kind: \"scalar\", T: 3 /* ScalarType.INT64 */ },\n    { no: 2, name: \"nanos\", kind: \"scalar\", T: 5 /* ScalarType.INT32 */ },\n]);\n"], "names": [], "mappings": "AAAA,6CAA6C;AAC7C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;;;AACjC;AACA;AACA;;;;AA+FO,MAAM,kBAAkB,4OAAA,CAAA,UAAO;IAClC,YAAY,IAAI,CAAE;QACd,KAAK;QACL;;;;;;SAMC,GACD,IAAI,CAAC,OAAO,GAAG,mPAAA,CAAA,aAAU,CAAC,IAAI;QAC9B;;;;;;;SAOC,GACD,IAAI,CAAC,KAAK,GAAG;QACb,2OAAA,CAAA,SAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,IAAI;IACtC;IACA,SAAS,IAAI,EAAE,OAAO,EAAE;QACpB,IAAI,OAAO,SAAS,UAAU;YAC1B,MAAM,IAAI,MAAM,CAAC,mDAAmD,EAAE,2OAAA,CAAA,SAAM,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO;QACnG;QACA,MAAM,UAAU,KAAK,KAAK,CAAC;QAC3B,IAAI,CAAC,SAAS;YACV,MAAM,IAAI,MAAM,CAAC,0EAA0E,CAAC;QAChG;QACA,MAAM,KAAK,KAAK,KAAK,CAAC,OAAO,CAAC,EAAE,GAAG,MAAM,OAAO,CAAC,EAAE,GAAG,MAAM,OAAO,CAAC,EAAE,GAAG,MAAM,OAAO,CAAC,EAAE,GAAG,MAAM,OAAO,CAAC,EAAE,GAAG,MAAM,OAAO,CAAC,EAAE,GAAG,CAAC,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,GAAG,GAAG;QAChK,IAAI,OAAO,KAAK,CAAC,KAAK;YAClB,MAAM,IAAI,MAAM,CAAC,0EAA0E,CAAC;QAChG;QACA,IAAI,KAAK,KAAK,KAAK,CAAC,2BAA2B,KAAK,KAAK,KAAK,CAAC,yBAAyB;YACpF,MAAM,IAAI,MAAM,CAAC,8HAA8H,CAAC;QACpJ;QACA,IAAI,CAAC,OAAO,GAAG,mPAAA,CAAA,aAAU,CAAC,KAAK,CAAC,KAAK;QACrC,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,OAAO,CAAC,EAAE,EAAE;YACZ,IAAI,CAAC,KAAK,GAAI,SAAS,MAAM,OAAO,CAAC,EAAE,GAAG,IAAI,MAAM,CAAC,IAAI,OAAO,CAAC,EAAE,CAAC,MAAM,KAAK;QACnF;QACA,OAAO,IAAI;IACf;IACA,OAAO,OAAO,EAAE;QACZ,MAAM,KAAK,OAAO,IAAI,CAAC,OAAO,IAAI;QAClC,IAAI,KAAK,KAAK,KAAK,CAAC,2BAA2B,KAAK,KAAK,KAAK,CAAC,yBAAyB;YACpF,MAAM,IAAI,MAAM,CAAC,oHAAoH,CAAC;QAC1I;QACA,IAAI,IAAI,CAAC,KAAK,GAAG,GAAG;YAChB,MAAM,IAAI,MAAM,CAAC,2EAA2E,CAAC;QACjG;QACA,IAAI,IAAI;QACR,IAAI,IAAI,CAAC,KAAK,GAAG,GAAG;YAChB,MAAM,WAAW,CAAC,IAAI,CAAC,KAAK,GAAG,UAAU,EAAE,QAAQ,GAAG,SAAS,CAAC;YAChE,IAAI,SAAS,SAAS,CAAC,OAAO,UAAU;gBACpC,IAAI,MAAM,SAAS,SAAS,CAAC,GAAG,KAAK;YACzC,OACK,IAAI,SAAS,SAAS,CAAC,OAAO,OAAO;gBACtC,IAAI,MAAM,SAAS,SAAS,CAAC,GAAG,KAAK;YACzC,OACK;gBACD,IAAI,MAAM,WAAW;YACzB;QACJ;QACA,OAAO,IAAI,KAAK,IAAI,WAAW,GAAG,OAAO,CAAC,SAAS;IACvD;IACA,SAAS;QACL,OAAO,IAAI,KAAK,OAAO,IAAI,CAAC,OAAO,IAAI,OAAO,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG;IACzE;IACA,OAAO,MAAM;QACT,OAAO,UAAU,QAAQ,CAAC,IAAI;IAClC;IACA,OAAO,SAAS,IAAI,EAAE;QAClB,MAAM,KAAK,KAAK,OAAO;QACvB,OAAO,IAAI,UAAU;YACjB,SAAS,mPAAA,CAAA,aAAU,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC,KAAK;YAC1C,OAAO,AAAC,KAAK,OAAQ;QACzB;IACJ;IACA,OAAO,WAAW,KAAK,EAAE,OAAO,EAAE;QAC9B,OAAO,IAAI,YAAY,UAAU,CAAC,OAAO;IAC7C;IACA,OAAO,SAAS,SAAS,EAAE,OAAO,EAAE;QAChC,OAAO,IAAI,YAAY,QAAQ,CAAC,WAAW;IAC/C;IACA,OAAO,eAAe,UAAU,EAAE,OAAO,EAAE;QACvC,OAAO,IAAI,YAAY,cAAc,CAAC,YAAY;IACtD;IACA,OAAO,OAAO,CAAC,EAAE,CAAC,EAAE;QAChB,OAAO,2OAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG;IAC5C;AACJ;AACA,UAAU,OAAO,GAAG,2OAAA,CAAA,SAAM;AAC1B,UAAU,QAAQ,GAAG;AACrB,UAAU,MAAM,GAAG,2OAAA,CAAA,SAAM,CAAC,IAAI,CAAC,YAAY,CAAC,IAAM;QAC9C;YAAE,IAAI;YAAG,MAAM;YAAW,MAAM;YAAU,GAAG,EAAE,oBAAoB;QAAG;QACtE;YAAE,IAAI;YAAG,MAAM;YAAS,MAAM;YAAU,GAAG,EAAE,oBAAoB;QAAG;KACvE", "ignoreList": [0]}}, {"offset": {"line": 3735, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@bufbuild+protobuf@1.10.1/node_modules/@bufbuild/protobuf/dist/esm/service-type.js"], "sourcesContent": ["// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n/**\n * MethodKind represents the four method types that can be declared in\n * protobuf with the `stream` keyword:\n *\n * 1. Unary:           rpc (Input) returns (Output)\n * 2. ServerStreaming: rpc (Input) returns (stream Output)\n * 3. ClientStreaming: rpc (stream Input) returns (Output)\n * 4. BiDiStreaming:   rpc (stream Input) returns (stream Output)\n */\nexport var MethodKind;\n(function (MethodKind) {\n    MethodKind[MethodKind[\"Unary\"] = 0] = \"Unary\";\n    MethodKind[MethodKind[\"ServerStreaming\"] = 1] = \"ServerStreaming\";\n    MethodKind[MethodKind[\"ClientStreaming\"] = 2] = \"ClientStreaming\";\n    MethodKind[MethodKind[\"BiDiStreaming\"] = 3] = \"BiDiStreaming\";\n})(MethodKind || (MethodKind = {}));\n/**\n * Is this method side-effect-free (or safe in HTTP parlance), or just\n * idempotent, or neither? HTTP based RPC implementation may choose GET verb\n * for safe methods, and PUT verb for idempotent methods instead of the\n * default POST.\n *\n * This enum matches the protobuf enum google.protobuf.MethodOptions.IdempotencyLevel,\n * defined in the well-known type google/protobuf/descriptor.proto, but\n * drops UNKNOWN.\n */\nexport var MethodIdempotency;\n(function (MethodIdempotency) {\n    /**\n     * Idempotent, no side effects.\n     */\n    MethodIdempotency[MethodIdempotency[\"NoSideEffects\"] = 1] = \"NoSideEffects\";\n    /**\n     * Idempotent, but may have side effects.\n     */\n    MethodIdempotency[MethodIdempotency[\"Idempotent\"] = 2] = \"Idempotent\";\n})(MethodIdempotency || (MethodIdempotency = {}));\n"], "names": [], "mappings": "AAAA,6CAA6C;AAC7C,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AACjC;;;;;;;;CAQC;;;;AACM,IAAI;AACX,CAAC,SAAU,UAAU;IACjB,UAAU,CAAC,UAAU,CAAC,QAAQ,GAAG,EAAE,GAAG;IACtC,UAAU,CAAC,UAAU,CAAC,kBAAkB,GAAG,EAAE,GAAG;IAChD,UAAU,CAAC,UAAU,CAAC,kBAAkB,GAAG,EAAE,GAAG;IAChD,UAAU,CAAC,UAAU,CAAC,gBAAgB,GAAG,EAAE,GAAG;AAClD,CAAC,EAAE,cAAc,CAAC,aAAa,CAAC,CAAC;AAW1B,IAAI;AACX,CAAC,SAAU,iBAAiB;IACxB;;KAEC,GACD,iBAAiB,CAAC,iBAAiB,CAAC,gBAAgB,GAAG,EAAE,GAAG;IAC5D;;KAEC,GACD,iBAAiB,CAAC,iBAAiB,CAAC,aAAa,GAAG,EAAE,GAAG;AAC7D,CAAC,EAAE,qBAAqB,CAAC,oBAAoB,CAAC,CAAC", "ignoreList": [0]}}]}