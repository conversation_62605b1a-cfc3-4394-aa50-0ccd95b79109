"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94";
exports.ids = ["vendor-chunks/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94"];
exports.modules = {

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/enums/SemanticAttributes.js":
/*!**************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/enums/SemanticAttributes.js ***!
  \**************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.SemanticAttributes = void 0;\n// DO NOT EDIT, this is an Auto-generated file from scripts/semconv/templates//templates/SemanticAttributes.ts.j2\nexports.SemanticAttributes = {\n    /**\n     * State of the HTTP connection in the HTTP connection pool.\n     */\n    HTTP_CONNECTION_STATE: 'http.connection.state',\n    /**\n    * Describes a class of error the operation ended with.\n    *\n    * Note: The `error.type` SHOULD be predictable and SHOULD have low cardinality.\n  Instrumentations SHOULD document the list of errors they report.\n  \n  The cardinality of `error.type` within one instrumentation library SHOULD be low.\n  Telemetry consumers that aggregate data from multiple instrumentation libraries and applications\n  should be prepared for `error.type` to have high cardinality at query time when no\n  additional filters are applied.\n  \n  If the operation has completed successfully, instrumentations SHOULD NOT set `error.type`.\n  \n  If a specific domain defines its own set of error identifiers (such as HTTP or gRPC status codes),\n  it&#39;s RECOMMENDED to:\n  \n  * Use a domain-specific attribute\n  * Set `error.type` to capture all errors, regardless of whether they are defined within the domain-specific set or not.\n    */\n    ERROR_TYPE: 'error.type',\n    /**\n     * The size of the request payload body in bytes. This is the number of bytes transferred excluding headers and is often, but not always, present as the [Content-Length](https://www.rfc-editor.org/rfc/rfc9110.html#field.content-length) header. For requests using transport encoding, this should be the compressed size.\n     */\n    HTTP_REQUEST_BODY_SIZE: 'http.request.body.size',\n    /**\n    * HTTP request method.\n    *\n    * Note: HTTP request method value SHOULD be &#34;known&#34; to the instrumentation.\n  By default, this convention defines &#34;known&#34; methods as the ones listed in [RFC9110](https://www.rfc-editor.org/rfc/rfc9110.html#name-methods)\n  and the PATCH method defined in [RFC5789](https://www.rfc-editor.org/rfc/rfc5789.html).\n  \n  If the HTTP request method is not known to instrumentation, it MUST set the `http.request.method` attribute to `_OTHER`.\n  \n  If the HTTP instrumentation could end up converting valid HTTP request methods to `_OTHER`, then it MUST provide a way to override\n  the list of known HTTP methods. If this override is done via environment variable, then the environment variable MUST be named\n  OTEL_INSTRUMENTATION_HTTP_KNOWN_METHODS and support a comma-separated list of case-sensitive known HTTP methods\n  (this list MUST be a full override of the default known method, it is not a list of known methods in addition to the defaults).\n  \n  HTTP method names are case-sensitive and `http.request.method` attribute value MUST match a known HTTP method name exactly.\n  Instrumentations for specific web frameworks that consider HTTP methods to be case insensitive, SHOULD populate a canonical equivalent.\n  Tracing instrumentations that do so, MUST also set `http.request.method_original` to the original value.\n    */\n    HTTP_REQUEST_METHOD: 'http.request.method',\n    /**\n     * Original HTTP method sent by the client in the request line.\n     */\n    HTTP_REQUEST_METHOD_ORIGINAL: 'http.request.method_original',\n    /**\n     * The ordinal number of request resending attempt (for any reason, including redirects).\n     *\n     * Note: The resend count SHOULD be updated each time an HTTP request gets resent by the client, regardless of what was the cause of the resending (e.g. redirection, authorization failure, 503 Server Unavailable, network issues, or any other).\n     */\n    HTTP_REQUEST_RESEND_COUNT: 'http.request.resend_count',\n    /**\n     * The size of the response payload body in bytes. This is the number of bytes transferred excluding headers and is often, but not always, present as the [Content-Length](https://www.rfc-editor.org/rfc/rfc9110.html#field.content-length) header. For requests using transport encoding, this should be the compressed size.\n     */\n    HTTP_RESPONSE_BODY_SIZE: 'http.response.body.size',\n    /**\n     * [HTTP response status code](https://tools.ietf.org/html/rfc7231#section-6).\n     */\n    HTTP_RESPONSE_STATUS_CODE: 'http.response.status_code',\n    /**\n    * The matched route, that is, the path template in the format used by the respective server framework.\n    *\n    * Note: MUST NOT be populated when this is not supported by the HTTP server framework as the route attribute should have low-cardinality and the URI path can NOT substitute it.\n  SHOULD include the [application root](/docs/http/http-spans.md#http-server-definitions) if there is one.\n    */\n    HTTP_ROUTE: 'http.route',\n    /**\n     * Peer address of the network connection - IP address or Unix domain socket name.\n     */\n    NETWORK_PEER_ADDRESS: 'network.peer.address',\n    /**\n     * Peer port number of the network connection.\n     */\n    NETWORK_PEER_PORT: 'network.peer.port',\n    /**\n     * [OSI application layer](https://osi-model.com/application-layer/) or non-OSI equivalent.\n     *\n     * Note: The value SHOULD be normalized to lowercase.\n     */\n    NETWORK_PROTOCOL_NAME: 'network.protocol.name',\n    /**\n     * Version of the protocol specified in `network.protocol.name`.\n     *\n     * Note: `network.protocol.version` refers to the version of the protocol used and might be different from the protocol client&#39;s version. If the HTTP client has a version of `0.27.2`, but sends HTTP version `1.1`, this attribute should be set to `1.1`.\n     */\n    NETWORK_PROTOCOL_VERSION: 'network.protocol.version',\n    /**\n     * Server domain name if available without reverse DNS lookup; otherwise, IP address or Unix domain socket name.\n     *\n     * Note: When observed from the client side, and when communicating through an intermediary, `server.address` SHOULD represent the server address behind any intermediaries, for example proxies, if it&#39;s available.\n     */\n    SERVER_ADDRESS: 'server.address',\n    /**\n     * Server port number.\n     *\n     * Note: When observed from the client side, and when communicating through an intermediary, `server.port` SHOULD represent the server port behind any intermediaries, for example proxies, if it&#39;s available.\n     */\n    SERVER_PORT: 'server.port',\n    /**\n    * Absolute URL describing a network resource according to [RFC3986](https://www.rfc-editor.org/rfc/rfc3986).\n    *\n    * Note: For network calls, URL usually has `scheme://host[:port][path][?query][#fragment]` format, where the fragment is not transmitted over HTTP, but if it is known, it SHOULD be included nevertheless.\n  `url.full` MUST NOT contain credentials passed via URL in form of `https://username:<EMAIL>/`. In such case username and password SHOULD be redacted and attribute&#39;s value SHOULD be `https://REDACTED:<EMAIL>/`.\n  `url.full` SHOULD capture the absolute URL when it is available (or can be reconstructed) and SHOULD NOT be validated or modified except for sanitizing purposes.\n    */\n    URL_FULL: 'url.full',\n    /**\n     * The [URI path](https://www.rfc-editor.org/rfc/rfc3986#section-3.3) component.\n     */\n    URL_PATH: 'url.path',\n    /**\n     * The [URI query](https://www.rfc-editor.org/rfc/rfc3986#section-3.4) component.\n     *\n     * Note: Sensitive content provided in query string SHOULD be scrubbed when instrumentations can identify it.\n     */\n    URL_QUERY: 'url.query',\n    /**\n     * The [URI scheme](https://www.rfc-editor.org/rfc/rfc3986#section-3.1) component identifying the used protocol.\n     */\n    URL_SCHEME: 'url.scheme',\n    /**\n     * Value of the [HTTP User-Agent](https://www.rfc-editor.org/rfc/rfc9110.html#field.user-agent) header sent by the client.\n     */\n    USER_AGENT_ORIGINAL: 'user_agent.original',\n};\n//# sourceMappingURL=SemanticAttributes.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/enums/SemanticAttributes.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/index.js":
/*!*******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/index.js ***!
  \*******************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./undici */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/undici.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/types.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/index.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/types.js":
/*!*******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/types.js ***!
  \*******************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9Ab3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfZWE0MjAyM2ZlYjYzMGNmODFmOTdiZGNlMThhNDZlOTQvbm9kZV9tb2R1bGVzL0BvcGVudGVsZW1ldHJ5L2luc3RydW1lbnRhdGlvbi11bmRpY2kvYnVpbGQvc3JjL3R5cGVzLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdEIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFud2FyXFxEb2N1bWVudHNcXEN1YmVudHdlYlxcY3ViZW50XFxub2RlX21vZHVsZXNcXC5wbnBtXFxAb3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfZWE0MjAyM2ZlYjYzMGNmODFmOTdiZGNlMThhNDZlOTRcXG5vZGVfbW9kdWxlc1xcQG9wZW50ZWxlbWV0cnlcXGluc3RydW1lbnRhdGlvbi11bmRpY2lcXGJ1aWxkXFxzcmNcXHR5cGVzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dHlwZXMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/types.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/undici.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/undici.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.UndiciInstrumentation = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst diagch = __webpack_require__(/*! diagnostics_channel */ \"diagnostics_channel\");\nconst url_1 = __webpack_require__(/*! url */ \"url\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/version.js\");\nconst SemanticAttributes_1 = __webpack_require__(/*! ./enums/SemanticAttributes */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/enums/SemanticAttributes.js\");\nconst core_1 = __webpack_require__(/*! @opentelemetry/core */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+core@1.30.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/core/build/esm/index.js\");\n// A combination of https://github.com/elastic/apm-agent-nodejs and\n// https://github.com/gadget-inc/opentelemetry-instrumentations/blob/main/packages/opentelemetry-instrumentation-undici/src/index.ts\nclass UndiciInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, config);\n        this._recordFromReq = new WeakMap();\n    }\n    // No need to instrument files/modules\n    init() {\n        return undefined;\n    }\n    disable() {\n        super.disable();\n        this._channelSubs.forEach(sub => sub.unsubscribe());\n        this._channelSubs.length = 0;\n    }\n    enable() {\n        // \"enabled\" handling is currently a bit messy with InstrumentationBase.\n        // If constructed with `{enabled: false}`, this `.enable()` is still called,\n        // and `this.getConfig().enabled !== this.isEnabled()`, creating confusion.\n        //\n        // For now, this class will setup for instrumenting if `.enable()` is\n        // called, but use `this.getConfig().enabled` to determine if\n        // instrumentation should be generated. This covers the more likely common\n        // case of config being given a construction time, rather than later via\n        // `instance.enable()`, `.disable()`, or `.setConfig()` calls.\n        super.enable();\n        // This method is called by the super-class constructor before ours is\n        // called. So we need to ensure the property is initalized.\n        this._channelSubs = this._channelSubs || [];\n        // Avoid to duplicate subscriptions\n        if (this._channelSubs.length > 0) {\n            return;\n        }\n        this.subscribeToChannel('undici:request:create', this.onRequestCreated.bind(this));\n        this.subscribeToChannel('undici:client:sendHeaders', this.onRequestHeaders.bind(this));\n        this.subscribeToChannel('undici:request:headers', this.onResponseHeaders.bind(this));\n        this.subscribeToChannel('undici:request:trailers', this.onDone.bind(this));\n        this.subscribeToChannel('undici:request:error', this.onError.bind(this));\n    }\n    _updateMetricInstruments() {\n        this._httpClientDurationHistogram = this.meter.createHistogram('http.client.request.duration', {\n            description: 'Measures the duration of outbound HTTP requests.',\n            unit: 's',\n            valueType: api_1.ValueType.DOUBLE,\n            advice: {\n                explicitBucketBoundaries: [\n                    0.005, 0.01, 0.025, 0.05, 0.075, 0.1, 0.25, 0.5, 0.75, 1, 2.5, 5,\n                    7.5, 10,\n                ],\n            },\n        });\n    }\n    subscribeToChannel(diagnosticChannel, onMessage) {\n        var _a;\n        // `diagnostics_channel` had a ref counting bug until v18.19.0.\n        // https://github.com/nodejs/node/pull/47520\n        const [major, minor] = process.version\n            .replace('v', '')\n            .split('.')\n            .map(n => Number(n));\n        const useNewSubscribe = major > 18 || (major === 18 && minor >= 19);\n        let unsubscribe;\n        if (useNewSubscribe) {\n            (_a = diagch.subscribe) === null || _a === void 0 ? void 0 : _a.call(diagch, diagnosticChannel, onMessage);\n            unsubscribe = () => { var _a; return (_a = diagch.unsubscribe) === null || _a === void 0 ? void 0 : _a.call(diagch, diagnosticChannel, onMessage); };\n        }\n        else {\n            const channel = diagch.channel(diagnosticChannel);\n            channel.subscribe(onMessage);\n            unsubscribe = () => channel.unsubscribe(onMessage);\n        }\n        this._channelSubs.push({\n            name: diagnosticChannel,\n            unsubscribe,\n        });\n    }\n    // This is the 1st message we receive for each request (fired after request creation). Here we will\n    // create the span and populate some atttributes, then link the span to the request for further\n    // span processing\n    onRequestCreated({ request }) {\n        // Ignore if:\n        // - instrumentation is disabled\n        // - ignored by config\n        // - method is 'CONNECT'\n        const config = this.getConfig();\n        const enabled = config.enabled !== false;\n        const shouldIgnoreReq = (0, instrumentation_1.safeExecuteInTheMiddle)(() => {\n            var _a;\n            return !enabled ||\n                request.method === 'CONNECT' ||\n                ((_a = config.ignoreRequestHook) === null || _a === void 0 ? void 0 : _a.call(config, request));\n        }, e => e && this._diag.error('caught ignoreRequestHook error: ', e), true);\n        if (shouldIgnoreReq) {\n            return;\n        }\n        const startTime = (0, core_1.hrTime)();\n        let requestUrl;\n        try {\n            requestUrl = new url_1.URL(request.path, request.origin);\n        }\n        catch (err) {\n            this._diag.warn('could not determine url.full:', err);\n            // Skip instrumenting this request.\n            return;\n        }\n        const urlScheme = requestUrl.protocol.replace(':', '');\n        const requestMethod = this.getRequestMethod(request.method);\n        const attributes = {\n            [SemanticAttributes_1.SemanticAttributes.HTTP_REQUEST_METHOD]: requestMethod,\n            [SemanticAttributes_1.SemanticAttributes.HTTP_REQUEST_METHOD_ORIGINAL]: request.method,\n            [SemanticAttributes_1.SemanticAttributes.URL_FULL]: requestUrl.toString(),\n            [SemanticAttributes_1.SemanticAttributes.URL_PATH]: requestUrl.pathname,\n            [SemanticAttributes_1.SemanticAttributes.URL_QUERY]: requestUrl.search,\n            [SemanticAttributes_1.SemanticAttributes.URL_SCHEME]: urlScheme,\n        };\n        const schemePorts = { https: '443', http: '80' };\n        const serverAddress = requestUrl.hostname;\n        const serverPort = requestUrl.port || schemePorts[urlScheme];\n        attributes[SemanticAttributes_1.SemanticAttributes.SERVER_ADDRESS] = serverAddress;\n        if (serverPort && !isNaN(Number(serverPort))) {\n            attributes[SemanticAttributes_1.SemanticAttributes.SERVER_PORT] = Number(serverPort);\n        }\n        // Get user agent from headers\n        let userAgent;\n        if (Array.isArray(request.headers)) {\n            const idx = request.headers.findIndex(h => h.toLowerCase() === 'user-agent');\n            if (idx >= 0) {\n                userAgent = request.headers[idx + 1];\n            }\n        }\n        else if (typeof request.headers === 'string') {\n            const headers = request.headers.split('\\r\\n');\n            const uaHeader = headers.find(h => h.toLowerCase().startsWith('user-agent'));\n            userAgent =\n                uaHeader && uaHeader.substring(uaHeader.indexOf(':') + 1).trim();\n        }\n        if (userAgent) {\n            attributes[SemanticAttributes_1.SemanticAttributes.USER_AGENT_ORIGINAL] = userAgent;\n        }\n        // Get attributes from the hook if present\n        const hookAttributes = (0, instrumentation_1.safeExecuteInTheMiddle)(() => { var _a; return (_a = config.startSpanHook) === null || _a === void 0 ? void 0 : _a.call(config, request); }, e => e && this._diag.error('caught startSpanHook error: ', e), true);\n        if (hookAttributes) {\n            Object.entries(hookAttributes).forEach(([key, val]) => {\n                attributes[key] = val;\n            });\n        }\n        // Check if parent span is required via config and:\n        // - if a parent is required but not present, we use a `NoopSpan` to still\n        //   propagate context without recording it.\n        // - create a span otherwise\n        const activeCtx = api_1.context.active();\n        const currentSpan = api_1.trace.getSpan(activeCtx);\n        let span;\n        if (config.requireParentforSpans &&\n            (!currentSpan || !api_1.trace.isSpanContextValid(currentSpan.spanContext()))) {\n            span = api_1.trace.wrapSpanContext(api_1.INVALID_SPAN_CONTEXT);\n        }\n        else {\n            span = this.tracer.startSpan(requestMethod === '_OTHER' ? 'HTTP' : requestMethod, {\n                kind: api_1.SpanKind.CLIENT,\n                attributes: attributes,\n            }, activeCtx);\n        }\n        // Execute the request hook if defined\n        (0, instrumentation_1.safeExecuteInTheMiddle)(() => { var _a; return (_a = config.requestHook) === null || _a === void 0 ? void 0 : _a.call(config, span, request); }, e => e && this._diag.error('caught requestHook error: ', e), true);\n        // Context propagation goes last so no hook can tamper\n        // the propagation headers\n        const requestContext = api_1.trace.setSpan(api_1.context.active(), span);\n        const addedHeaders = {};\n        api_1.propagation.inject(requestContext, addedHeaders);\n        const headerEntries = Object.entries(addedHeaders);\n        for (let i = 0; i < headerEntries.length; i++) {\n            const [k, v] = headerEntries[i];\n            if (typeof request.addHeader === 'function') {\n                request.addHeader(k, v);\n            }\n            else if (typeof request.headers === 'string') {\n                request.headers += `${k}: ${v}\\r\\n`;\n            }\n            else if (Array.isArray(request.headers)) {\n                // undici@6.11.0 accidentally, briefly removed `request.addHeader()`.\n                request.headers.push(k, v);\n            }\n        }\n        this._recordFromReq.set(request, { span, attributes, startTime });\n    }\n    // This is the 2nd message we receive for each request. It is fired when connection with\n    // the remote is established and about to send the first byte. Here we do have info about the\n    // remote address and port so we can populate some `network.*` attributes into the span\n    onRequestHeaders({ request, socket }) {\n        var _a;\n        const record = this._recordFromReq.get(request);\n        if (!record) {\n            return;\n        }\n        const config = this.getConfig();\n        const { span } = record;\n        const { remoteAddress, remotePort } = socket;\n        const spanAttributes = {\n            [SemanticAttributes_1.SemanticAttributes.NETWORK_PEER_ADDRESS]: remoteAddress,\n            [SemanticAttributes_1.SemanticAttributes.NETWORK_PEER_PORT]: remotePort,\n        };\n        // After hooks have been processed (which may modify request headers)\n        // we can collect the headers based on the configuration\n        if ((_a = config.headersToSpanAttributes) === null || _a === void 0 ? void 0 : _a.requestHeaders) {\n            const headersToAttribs = new Set(config.headersToSpanAttributes.requestHeaders.map(n => n.toLowerCase()));\n            // headers could be in form\n            // ['name: value', ...] for v5\n            // ['name', 'value', ...] for v6\n            const rawHeaders = Array.isArray(request.headers)\n                ? request.headers\n                : request.headers.split('\\r\\n');\n            rawHeaders.forEach((h, idx) => {\n                const sepIndex = h.indexOf(':');\n                const hasSeparator = sepIndex !== -1;\n                const name = (hasSeparator ? h.substring(0, sepIndex) : h).toLowerCase();\n                const value = hasSeparator\n                    ? h.substring(sepIndex + 1)\n                    : rawHeaders[idx + 1];\n                if (headersToAttribs.has(name)) {\n                    spanAttributes[`http.request.header.${name}`] = value.trim();\n                }\n            });\n        }\n        span.setAttributes(spanAttributes);\n    }\n    // This is the 3rd message we get for each request and it's fired when the server\n    // headers are received, body may not be accessible yet.\n    // From the response headers we can set the status and content length\n    onResponseHeaders({ request, response, }) {\n        var _a, _b;\n        const record = this._recordFromReq.get(request);\n        if (!record) {\n            return;\n        }\n        const { span, attributes } = record;\n        const spanAttributes = {\n            [SemanticAttributes_1.SemanticAttributes.HTTP_RESPONSE_STATUS_CODE]: response.statusCode,\n        };\n        const config = this.getConfig();\n        // Execute the response hook if defined\n        (0, instrumentation_1.safeExecuteInTheMiddle)(() => { var _a; return (_a = config.responseHook) === null || _a === void 0 ? void 0 : _a.call(config, span, { request, response }); }, e => e && this._diag.error('caught responseHook error: ', e), true);\n        const headersToAttribs = new Set();\n        if ((_a = config.headersToSpanAttributes) === null || _a === void 0 ? void 0 : _a.responseHeaders) {\n            (_b = config.headersToSpanAttributes) === null || _b === void 0 ? void 0 : _b.responseHeaders.forEach(name => headersToAttribs.add(name.toLowerCase()));\n        }\n        for (let idx = 0; idx < response.headers.length; idx = idx + 2) {\n            const name = response.headers[idx].toString().toLowerCase();\n            const value = response.headers[idx + 1];\n            if (headersToAttribs.has(name)) {\n                spanAttributes[`http.response.header.${name}`] = value.toString();\n            }\n            if (name === 'content-length') {\n                const contentLength = Number(value.toString());\n                if (!isNaN(contentLength)) {\n                    spanAttributes['http.response.header.content-length'] = contentLength;\n                }\n            }\n        }\n        span.setAttributes(spanAttributes);\n        span.setStatus({\n            code: response.statusCode >= 400\n                ? api_1.SpanStatusCode.ERROR\n                : api_1.SpanStatusCode.UNSET,\n        });\n        record.attributes = Object.assign(attributes, spanAttributes);\n    }\n    // This is the last event we receive if the request went without any errors\n    onDone({ request }) {\n        const record = this._recordFromReq.get(request);\n        if (!record) {\n            return;\n        }\n        const { span, attributes, startTime } = record;\n        // End the span\n        span.end();\n        this._recordFromReq.delete(request);\n        // Record metrics\n        this.recordRequestDuration(attributes, startTime);\n    }\n    // This is the event we get when something is wrong in the request like\n    // - invalid options when calling `fetch` global API or any undici method for request\n    // - connectivity errors such as unreachable host\n    // - requests aborted through an `AbortController.signal`\n    // NOTE: server errors are considered valid responses and it's the lib consumer\n    // who should deal with that.\n    onError({ request, error }) {\n        const record = this._recordFromReq.get(request);\n        if (!record) {\n            return;\n        }\n        const { span, attributes, startTime } = record;\n        // NOTE: in `undici@6.3.0` when request aborted the error type changes from\n        // a custom error (`RequestAbortedError`) to a built-in `DOMException` carrying\n        // some differences:\n        // - `code` is from DOMEXception (ABORT_ERR: 20)\n        // - `message` changes\n        // - stacktrace is smaller and contains node internal frames\n        span.recordException(error);\n        span.setStatus({\n            code: api_1.SpanStatusCode.ERROR,\n            message: error.message,\n        });\n        span.end();\n        this._recordFromReq.delete(request);\n        // Record metrics (with the error)\n        attributes[SemanticAttributes_1.SemanticAttributes.ERROR_TYPE] = error.message;\n        this.recordRequestDuration(attributes, startTime);\n    }\n    recordRequestDuration(attributes, startTime) {\n        // Time to record metrics\n        const metricsAttributes = {};\n        // Get the attribs already in span attributes\n        const keysToCopy = [\n            SemanticAttributes_1.SemanticAttributes.HTTP_RESPONSE_STATUS_CODE,\n            SemanticAttributes_1.SemanticAttributes.HTTP_REQUEST_METHOD,\n            SemanticAttributes_1.SemanticAttributes.SERVER_ADDRESS,\n            SemanticAttributes_1.SemanticAttributes.SERVER_PORT,\n            SemanticAttributes_1.SemanticAttributes.URL_SCHEME,\n            SemanticAttributes_1.SemanticAttributes.ERROR_TYPE,\n        ];\n        keysToCopy.forEach(key => {\n            if (key in attributes) {\n                metricsAttributes[key] = attributes[key];\n            }\n        });\n        // Take the duration and record it\n        const durationSeconds = (0, core_1.hrTimeToMilliseconds)((0, core_1.hrTimeDuration)(startTime, (0, core_1.hrTime)())) / 1000;\n        this._httpClientDurationHistogram.record(durationSeconds, metricsAttributes);\n    }\n    getRequestMethod(original) {\n        const knownMethods = {\n            CONNECT: true,\n            OPTIONS: true,\n            HEAD: true,\n            GET: true,\n            POST: true,\n            PUT: true,\n            PATCH: true,\n            DELETE: true,\n            TRACE: true,\n        };\n        if (original.toUpperCase() in knownMethods) {\n            return original.toUpperCase();\n        }\n        return '_OTHER';\n    }\n}\nexports.UndiciInstrumentation = UndiciInstrumentation;\n//# sourceMappingURL=undici.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/undici.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/version.js":
/*!*********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/version.js ***!
  \*********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.10.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-undici';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9Ab3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfZWE0MjAyM2ZlYjYzMGNmODFmOTdiZGNlMThhNDZlOTQvbm9kZV9tb2R1bGVzL0BvcGVudGVsZW1ldHJ5L2luc3RydW1lbnRhdGlvbi11bmRpY2kvYnVpbGQvc3JjL3ZlcnNpb24uanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0Qsb0JBQW9CLEdBQUcsdUJBQXVCO0FBQzlDO0FBQ0EsdUJBQXVCO0FBQ3ZCLG9CQUFvQjtBQUNwQiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbndhclxcRG9jdW1lbnRzXFxDdWJlbnR3ZWJcXGN1YmVudFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQG9wZW50ZWxlbWV0cnkraW5zdHJ1bWVudGF0X2VhNDIwMjNmZWI2MzBjZjgxZjk3YmRjZTE4YTQ2ZTk0XFxub2RlX21vZHVsZXNcXEBvcGVudGVsZW1ldHJ5XFxpbnN0cnVtZW50YXRpb24tdW5kaWNpXFxidWlsZFxcc3JjXFx2ZXJzaW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuLypcbiAqIENvcHlyaWdodCBUaGUgT3BlblRlbGVtZXRyeSBBdXRob3JzXG4gKlxuICogTGljZW5zZWQgdW5kZXIgdGhlIEFwYWNoZSBMaWNlbnNlLCBWZXJzaW9uIDIuMCAodGhlIFwiTGljZW5zZVwiKTtcbiAqIHlvdSBtYXkgbm90IHVzZSB0aGlzIGZpbGUgZXhjZXB0IGluIGNvbXBsaWFuY2Ugd2l0aCB0aGUgTGljZW5zZS5cbiAqIFlvdSBtYXkgb2J0YWluIGEgY29weSBvZiB0aGUgTGljZW5zZSBhdFxuICpcbiAqICAgICAgaHR0cHM6Ly93d3cuYXBhY2hlLm9yZy9saWNlbnNlcy9MSUNFTlNFLTIuMFxuICpcbiAqIFVubGVzcyByZXF1aXJlZCBieSBhcHBsaWNhYmxlIGxhdyBvciBhZ3JlZWQgdG8gaW4gd3JpdGluZywgc29mdHdhcmVcbiAqIGRpc3RyaWJ1dGVkIHVuZGVyIHRoZSBMaWNlbnNlIGlzIGRpc3RyaWJ1dGVkIG9uIGFuIFwiQVMgSVNcIiBCQVNJUyxcbiAqIFdJVEhPVVQgV0FSUkFOVElFUyBPUiBDT05ESVRJT05TIE9GIEFOWSBLSU5ELCBlaXRoZXIgZXhwcmVzcyBvciBpbXBsaWVkLlxuICogU2VlIHRoZSBMaWNlbnNlIGZvciB0aGUgc3BlY2lmaWMgbGFuZ3VhZ2UgZ292ZXJuaW5nIHBlcm1pc3Npb25zIGFuZFxuICogbGltaXRhdGlvbnMgdW5kZXIgdGhlIExpY2Vuc2UuXG4gKi9cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuUEFDS0FHRV9OQU1FID0gZXhwb3J0cy5QQUNLQUdFX1ZFUlNJT04gPSB2b2lkIDA7XG4vLyB0aGlzIGlzIGF1dG9nZW5lcmF0ZWQgZmlsZSwgc2VlIHNjcmlwdHMvdmVyc2lvbi11cGRhdGUuanNcbmV4cG9ydHMuUEFDS0FHRV9WRVJTSU9OID0gJzAuMTAuMSc7XG5leHBvcnRzLlBBQ0tBR0VfTkFNRSA9ICdAb3BlbnRlbGVtZXRyeS9pbnN0cnVtZW50YXRpb24tdW5kaWNpJztcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXZlcnNpb24uanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/version.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/enums/SemanticAttributes.js":
/*!**************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/enums/SemanticAttributes.js ***!
  \**************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.SemanticAttributes = void 0;\n// DO NOT EDIT, this is an Auto-generated file from scripts/semconv/templates//templates/SemanticAttributes.ts.j2\nexports.SemanticAttributes = {\n    /**\n     * State of the HTTP connection in the HTTP connection pool.\n     */\n    HTTP_CONNECTION_STATE: 'http.connection.state',\n    /**\n    * Describes a class of error the operation ended with.\n    *\n    * Note: The `error.type` SHOULD be predictable and SHOULD have low cardinality.\n  Instrumentations SHOULD document the list of errors they report.\n  \n  The cardinality of `error.type` within one instrumentation library SHOULD be low.\n  Telemetry consumers that aggregate data from multiple instrumentation libraries and applications\n  should be prepared for `error.type` to have high cardinality at query time when no\n  additional filters are applied.\n  \n  If the operation has completed successfully, instrumentations SHOULD NOT set `error.type`.\n  \n  If a specific domain defines its own set of error identifiers (such as HTTP or gRPC status codes),\n  it&#39;s RECOMMENDED to:\n  \n  * Use a domain-specific attribute\n  * Set `error.type` to capture all errors, regardless of whether they are defined within the domain-specific set or not.\n    */\n    ERROR_TYPE: 'error.type',\n    /**\n     * The size of the request payload body in bytes. This is the number of bytes transferred excluding headers and is often, but not always, present as the [Content-Length](https://www.rfc-editor.org/rfc/rfc9110.html#field.content-length) header. For requests using transport encoding, this should be the compressed size.\n     */\n    HTTP_REQUEST_BODY_SIZE: 'http.request.body.size',\n    /**\n    * HTTP request method.\n    *\n    * Note: HTTP request method value SHOULD be &#34;known&#34; to the instrumentation.\n  By default, this convention defines &#34;known&#34; methods as the ones listed in [RFC9110](https://www.rfc-editor.org/rfc/rfc9110.html#name-methods)\n  and the PATCH method defined in [RFC5789](https://www.rfc-editor.org/rfc/rfc5789.html).\n  \n  If the HTTP request method is not known to instrumentation, it MUST set the `http.request.method` attribute to `_OTHER`.\n  \n  If the HTTP instrumentation could end up converting valid HTTP request methods to `_OTHER`, then it MUST provide a way to override\n  the list of known HTTP methods. If this override is done via environment variable, then the environment variable MUST be named\n  OTEL_INSTRUMENTATION_HTTP_KNOWN_METHODS and support a comma-separated list of case-sensitive known HTTP methods\n  (this list MUST be a full override of the default known method, it is not a list of known methods in addition to the defaults).\n  \n  HTTP method names are case-sensitive and `http.request.method` attribute value MUST match a known HTTP method name exactly.\n  Instrumentations for specific web frameworks that consider HTTP methods to be case insensitive, SHOULD populate a canonical equivalent.\n  Tracing instrumentations that do so, MUST also set `http.request.method_original` to the original value.\n    */\n    HTTP_REQUEST_METHOD: 'http.request.method',\n    /**\n     * Original HTTP method sent by the client in the request line.\n     */\n    HTTP_REQUEST_METHOD_ORIGINAL: 'http.request.method_original',\n    /**\n     * The ordinal number of request resending attempt (for any reason, including redirects).\n     *\n     * Note: The resend count SHOULD be updated each time an HTTP request gets resent by the client, regardless of what was the cause of the resending (e.g. redirection, authorization failure, 503 Server Unavailable, network issues, or any other).\n     */\n    HTTP_REQUEST_RESEND_COUNT: 'http.request.resend_count',\n    /**\n     * The size of the response payload body in bytes. This is the number of bytes transferred excluding headers and is often, but not always, present as the [Content-Length](https://www.rfc-editor.org/rfc/rfc9110.html#field.content-length) header. For requests using transport encoding, this should be the compressed size.\n     */\n    HTTP_RESPONSE_BODY_SIZE: 'http.response.body.size',\n    /**\n     * [HTTP response status code](https://tools.ietf.org/html/rfc7231#section-6).\n     */\n    HTTP_RESPONSE_STATUS_CODE: 'http.response.status_code',\n    /**\n    * The matched route, that is, the path template in the format used by the respective server framework.\n    *\n    * Note: MUST NOT be populated when this is not supported by the HTTP server framework as the route attribute should have low-cardinality and the URI path can NOT substitute it.\n  SHOULD include the [application root](/docs/http/http-spans.md#http-server-definitions) if there is one.\n    */\n    HTTP_ROUTE: 'http.route',\n    /**\n     * Peer address of the network connection - IP address or Unix domain socket name.\n     */\n    NETWORK_PEER_ADDRESS: 'network.peer.address',\n    /**\n     * Peer port number of the network connection.\n     */\n    NETWORK_PEER_PORT: 'network.peer.port',\n    /**\n     * [OSI application layer](https://osi-model.com/application-layer/) or non-OSI equivalent.\n     *\n     * Note: The value SHOULD be normalized to lowercase.\n     */\n    NETWORK_PROTOCOL_NAME: 'network.protocol.name',\n    /**\n     * Version of the protocol specified in `network.protocol.name`.\n     *\n     * Note: `network.protocol.version` refers to the version of the protocol used and might be different from the protocol client&#39;s version. If the HTTP client has a version of `0.27.2`, but sends HTTP version `1.1`, this attribute should be set to `1.1`.\n     */\n    NETWORK_PROTOCOL_VERSION: 'network.protocol.version',\n    /**\n     * Server domain name if available without reverse DNS lookup; otherwise, IP address or Unix domain socket name.\n     *\n     * Note: When observed from the client side, and when communicating through an intermediary, `server.address` SHOULD represent the server address behind any intermediaries, for example proxies, if it&#39;s available.\n     */\n    SERVER_ADDRESS: 'server.address',\n    /**\n     * Server port number.\n     *\n     * Note: When observed from the client side, and when communicating through an intermediary, `server.port` SHOULD represent the server port behind any intermediaries, for example proxies, if it&#39;s available.\n     */\n    SERVER_PORT: 'server.port',\n    /**\n    * Absolute URL describing a network resource according to [RFC3986](https://www.rfc-editor.org/rfc/rfc3986).\n    *\n    * Note: For network calls, URL usually has `scheme://host[:port][path][?query][#fragment]` format, where the fragment is not transmitted over HTTP, but if it is known, it SHOULD be included nevertheless.\n  `url.full` MUST NOT contain credentials passed via URL in form of `https://username:<EMAIL>/`. In such case username and password SHOULD be redacted and attribute&#39;s value SHOULD be `https://REDACTED:<EMAIL>/`.\n  `url.full` SHOULD capture the absolute URL when it is available (or can be reconstructed) and SHOULD NOT be validated or modified except for sanitizing purposes.\n    */\n    URL_FULL: 'url.full',\n    /**\n     * The [URI path](https://www.rfc-editor.org/rfc/rfc3986#section-3.3) component.\n     */\n    URL_PATH: 'url.path',\n    /**\n     * The [URI query](https://www.rfc-editor.org/rfc/rfc3986#section-3.4) component.\n     *\n     * Note: Sensitive content provided in query string SHOULD be scrubbed when instrumentations can identify it.\n     */\n    URL_QUERY: 'url.query',\n    /**\n     * The [URI scheme](https://www.rfc-editor.org/rfc/rfc3986#section-3.1) component identifying the used protocol.\n     */\n    URL_SCHEME: 'url.scheme',\n    /**\n     * Value of the [HTTP User-Agent](https://www.rfc-editor.org/rfc/rfc9110.html#field.user-agent) header sent by the client.\n     */\n    USER_AGENT_ORIGINAL: 'user_agent.original',\n};\n//# sourceMappingURL=SemanticAttributes.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/enums/SemanticAttributes.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/index.js":
/*!*******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/index.js ***!
  \*******************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./undici */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/undici.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/types.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/index.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/types.js":
/*!*******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/types.js ***!
  \*******************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF9lYTQyMDIzZmViNjMwY2Y4MWY5N2JkY2UxOGE0NmU5NC9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLXVuZGljaS9idWlsZC9zcmMvdHlwZXMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYW53YXJcXERvY3VtZW50c1xcQ3ViZW50d2ViXFxjdWJlbnRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF9lYTQyMDIzZmViNjMwY2Y4MWY5N2JkY2UxOGE0NmU5NFxcbm9kZV9tb2R1bGVzXFxAb3BlbnRlbGVtZXRyeVxcaW5zdHJ1bWVudGF0aW9uLXVuZGljaVxcYnVpbGRcXHNyY1xcdHlwZXMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD10eXBlcy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/types.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/undici.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/undici.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.UndiciInstrumentation = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst diagch = __webpack_require__(/*! diagnostics_channel */ \"diagnostics_channel\");\nconst url_1 = __webpack_require__(/*! url */ \"url\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/version.js\");\nconst SemanticAttributes_1 = __webpack_require__(/*! ./enums/SemanticAttributes */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/enums/SemanticAttributes.js\");\nconst core_1 = __webpack_require__(/*! @opentelemetry/core */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+core@1.30.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/core/build/esm/index.js\");\n// A combination of https://github.com/elastic/apm-agent-nodejs and\n// https://github.com/gadget-inc/opentelemetry-instrumentations/blob/main/packages/opentelemetry-instrumentation-undici/src/index.ts\nclass UndiciInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, config);\n        this._recordFromReq = new WeakMap();\n    }\n    // No need to instrument files/modules\n    init() {\n        return undefined;\n    }\n    disable() {\n        super.disable();\n        this._channelSubs.forEach(sub => sub.unsubscribe());\n        this._channelSubs.length = 0;\n    }\n    enable() {\n        // \"enabled\" handling is currently a bit messy with InstrumentationBase.\n        // If constructed with `{enabled: false}`, this `.enable()` is still called,\n        // and `this.getConfig().enabled !== this.isEnabled()`, creating confusion.\n        //\n        // For now, this class will setup for instrumenting if `.enable()` is\n        // called, but use `this.getConfig().enabled` to determine if\n        // instrumentation should be generated. This covers the more likely common\n        // case of config being given a construction time, rather than later via\n        // `instance.enable()`, `.disable()`, or `.setConfig()` calls.\n        super.enable();\n        // This method is called by the super-class constructor before ours is\n        // called. So we need to ensure the property is initalized.\n        this._channelSubs = this._channelSubs || [];\n        // Avoid to duplicate subscriptions\n        if (this._channelSubs.length > 0) {\n            return;\n        }\n        this.subscribeToChannel('undici:request:create', this.onRequestCreated.bind(this));\n        this.subscribeToChannel('undici:client:sendHeaders', this.onRequestHeaders.bind(this));\n        this.subscribeToChannel('undici:request:headers', this.onResponseHeaders.bind(this));\n        this.subscribeToChannel('undici:request:trailers', this.onDone.bind(this));\n        this.subscribeToChannel('undici:request:error', this.onError.bind(this));\n    }\n    _updateMetricInstruments() {\n        this._httpClientDurationHistogram = this.meter.createHistogram('http.client.request.duration', {\n            description: 'Measures the duration of outbound HTTP requests.',\n            unit: 's',\n            valueType: api_1.ValueType.DOUBLE,\n            advice: {\n                explicitBucketBoundaries: [\n                    0.005, 0.01, 0.025, 0.05, 0.075, 0.1, 0.25, 0.5, 0.75, 1, 2.5, 5,\n                    7.5, 10,\n                ],\n            },\n        });\n    }\n    subscribeToChannel(diagnosticChannel, onMessage) {\n        var _a;\n        // `diagnostics_channel` had a ref counting bug until v18.19.0.\n        // https://github.com/nodejs/node/pull/47520\n        const [major, minor] = process.version\n            .replace('v', '')\n            .split('.')\n            .map(n => Number(n));\n        const useNewSubscribe = major > 18 || (major === 18 && minor >= 19);\n        let unsubscribe;\n        if (useNewSubscribe) {\n            (_a = diagch.subscribe) === null || _a === void 0 ? void 0 : _a.call(diagch, diagnosticChannel, onMessage);\n            unsubscribe = () => { var _a; return (_a = diagch.unsubscribe) === null || _a === void 0 ? void 0 : _a.call(diagch, diagnosticChannel, onMessage); };\n        }\n        else {\n            const channel = diagch.channel(diagnosticChannel);\n            channel.subscribe(onMessage);\n            unsubscribe = () => channel.unsubscribe(onMessage);\n        }\n        this._channelSubs.push({\n            name: diagnosticChannel,\n            unsubscribe,\n        });\n    }\n    // This is the 1st message we receive for each request (fired after request creation). Here we will\n    // create the span and populate some atttributes, then link the span to the request for further\n    // span processing\n    onRequestCreated({ request }) {\n        // Ignore if:\n        // - instrumentation is disabled\n        // - ignored by config\n        // - method is 'CONNECT'\n        const config = this.getConfig();\n        const enabled = config.enabled !== false;\n        const shouldIgnoreReq = (0, instrumentation_1.safeExecuteInTheMiddle)(() => {\n            var _a;\n            return !enabled ||\n                request.method === 'CONNECT' ||\n                ((_a = config.ignoreRequestHook) === null || _a === void 0 ? void 0 : _a.call(config, request));\n        }, e => e && this._diag.error('caught ignoreRequestHook error: ', e), true);\n        if (shouldIgnoreReq) {\n            return;\n        }\n        const startTime = (0, core_1.hrTime)();\n        let requestUrl;\n        try {\n            requestUrl = new url_1.URL(request.path, request.origin);\n        }\n        catch (err) {\n            this._diag.warn('could not determine url.full:', err);\n            // Skip instrumenting this request.\n            return;\n        }\n        const urlScheme = requestUrl.protocol.replace(':', '');\n        const requestMethod = this.getRequestMethod(request.method);\n        const attributes = {\n            [SemanticAttributes_1.SemanticAttributes.HTTP_REQUEST_METHOD]: requestMethod,\n            [SemanticAttributes_1.SemanticAttributes.HTTP_REQUEST_METHOD_ORIGINAL]: request.method,\n            [SemanticAttributes_1.SemanticAttributes.URL_FULL]: requestUrl.toString(),\n            [SemanticAttributes_1.SemanticAttributes.URL_PATH]: requestUrl.pathname,\n            [SemanticAttributes_1.SemanticAttributes.URL_QUERY]: requestUrl.search,\n            [SemanticAttributes_1.SemanticAttributes.URL_SCHEME]: urlScheme,\n        };\n        const schemePorts = { https: '443', http: '80' };\n        const serverAddress = requestUrl.hostname;\n        const serverPort = requestUrl.port || schemePorts[urlScheme];\n        attributes[SemanticAttributes_1.SemanticAttributes.SERVER_ADDRESS] = serverAddress;\n        if (serverPort && !isNaN(Number(serverPort))) {\n            attributes[SemanticAttributes_1.SemanticAttributes.SERVER_PORT] = Number(serverPort);\n        }\n        // Get user agent from headers\n        let userAgent;\n        if (Array.isArray(request.headers)) {\n            const idx = request.headers.findIndex(h => h.toLowerCase() === 'user-agent');\n            if (idx >= 0) {\n                userAgent = request.headers[idx + 1];\n            }\n        }\n        else if (typeof request.headers === 'string') {\n            const headers = request.headers.split('\\r\\n');\n            const uaHeader = headers.find(h => h.toLowerCase().startsWith('user-agent'));\n            userAgent =\n                uaHeader && uaHeader.substring(uaHeader.indexOf(':') + 1).trim();\n        }\n        if (userAgent) {\n            attributes[SemanticAttributes_1.SemanticAttributes.USER_AGENT_ORIGINAL] = userAgent;\n        }\n        // Get attributes from the hook if present\n        const hookAttributes = (0, instrumentation_1.safeExecuteInTheMiddle)(() => { var _a; return (_a = config.startSpanHook) === null || _a === void 0 ? void 0 : _a.call(config, request); }, e => e && this._diag.error('caught startSpanHook error: ', e), true);\n        if (hookAttributes) {\n            Object.entries(hookAttributes).forEach(([key, val]) => {\n                attributes[key] = val;\n            });\n        }\n        // Check if parent span is required via config and:\n        // - if a parent is required but not present, we use a `NoopSpan` to still\n        //   propagate context without recording it.\n        // - create a span otherwise\n        const activeCtx = api_1.context.active();\n        const currentSpan = api_1.trace.getSpan(activeCtx);\n        let span;\n        if (config.requireParentforSpans &&\n            (!currentSpan || !api_1.trace.isSpanContextValid(currentSpan.spanContext()))) {\n            span = api_1.trace.wrapSpanContext(api_1.INVALID_SPAN_CONTEXT);\n        }\n        else {\n            span = this.tracer.startSpan(requestMethod === '_OTHER' ? 'HTTP' : requestMethod, {\n                kind: api_1.SpanKind.CLIENT,\n                attributes: attributes,\n            }, activeCtx);\n        }\n        // Execute the request hook if defined\n        (0, instrumentation_1.safeExecuteInTheMiddle)(() => { var _a; return (_a = config.requestHook) === null || _a === void 0 ? void 0 : _a.call(config, span, request); }, e => e && this._diag.error('caught requestHook error: ', e), true);\n        // Context propagation goes last so no hook can tamper\n        // the propagation headers\n        const requestContext = api_1.trace.setSpan(api_1.context.active(), span);\n        const addedHeaders = {};\n        api_1.propagation.inject(requestContext, addedHeaders);\n        const headerEntries = Object.entries(addedHeaders);\n        for (let i = 0; i < headerEntries.length; i++) {\n            const [k, v] = headerEntries[i];\n            if (typeof request.addHeader === 'function') {\n                request.addHeader(k, v);\n            }\n            else if (typeof request.headers === 'string') {\n                request.headers += `${k}: ${v}\\r\\n`;\n            }\n            else if (Array.isArray(request.headers)) {\n                // undici@6.11.0 accidentally, briefly removed `request.addHeader()`.\n                request.headers.push(k, v);\n            }\n        }\n        this._recordFromReq.set(request, { span, attributes, startTime });\n    }\n    // This is the 2nd message we receive for each request. It is fired when connection with\n    // the remote is established and about to send the first byte. Here we do have info about the\n    // remote address and port so we can populate some `network.*` attributes into the span\n    onRequestHeaders({ request, socket }) {\n        var _a;\n        const record = this._recordFromReq.get(request);\n        if (!record) {\n            return;\n        }\n        const config = this.getConfig();\n        const { span } = record;\n        const { remoteAddress, remotePort } = socket;\n        const spanAttributes = {\n            [SemanticAttributes_1.SemanticAttributes.NETWORK_PEER_ADDRESS]: remoteAddress,\n            [SemanticAttributes_1.SemanticAttributes.NETWORK_PEER_PORT]: remotePort,\n        };\n        // After hooks have been processed (which may modify request headers)\n        // we can collect the headers based on the configuration\n        if ((_a = config.headersToSpanAttributes) === null || _a === void 0 ? void 0 : _a.requestHeaders) {\n            const headersToAttribs = new Set(config.headersToSpanAttributes.requestHeaders.map(n => n.toLowerCase()));\n            // headers could be in form\n            // ['name: value', ...] for v5\n            // ['name', 'value', ...] for v6\n            const rawHeaders = Array.isArray(request.headers)\n                ? request.headers\n                : request.headers.split('\\r\\n');\n            rawHeaders.forEach((h, idx) => {\n                const sepIndex = h.indexOf(':');\n                const hasSeparator = sepIndex !== -1;\n                const name = (hasSeparator ? h.substring(0, sepIndex) : h).toLowerCase();\n                const value = hasSeparator\n                    ? h.substring(sepIndex + 1)\n                    : rawHeaders[idx + 1];\n                if (headersToAttribs.has(name)) {\n                    spanAttributes[`http.request.header.${name}`] = value.trim();\n                }\n            });\n        }\n        span.setAttributes(spanAttributes);\n    }\n    // This is the 3rd message we get for each request and it's fired when the server\n    // headers are received, body may not be accessible yet.\n    // From the response headers we can set the status and content length\n    onResponseHeaders({ request, response, }) {\n        var _a, _b;\n        const record = this._recordFromReq.get(request);\n        if (!record) {\n            return;\n        }\n        const { span, attributes } = record;\n        const spanAttributes = {\n            [SemanticAttributes_1.SemanticAttributes.HTTP_RESPONSE_STATUS_CODE]: response.statusCode,\n        };\n        const config = this.getConfig();\n        // Execute the response hook if defined\n        (0, instrumentation_1.safeExecuteInTheMiddle)(() => { var _a; return (_a = config.responseHook) === null || _a === void 0 ? void 0 : _a.call(config, span, { request, response }); }, e => e && this._diag.error('caught responseHook error: ', e), true);\n        const headersToAttribs = new Set();\n        if ((_a = config.headersToSpanAttributes) === null || _a === void 0 ? void 0 : _a.responseHeaders) {\n            (_b = config.headersToSpanAttributes) === null || _b === void 0 ? void 0 : _b.responseHeaders.forEach(name => headersToAttribs.add(name.toLowerCase()));\n        }\n        for (let idx = 0; idx < response.headers.length; idx = idx + 2) {\n            const name = response.headers[idx].toString().toLowerCase();\n            const value = response.headers[idx + 1];\n            if (headersToAttribs.has(name)) {\n                spanAttributes[`http.response.header.${name}`] = value.toString();\n            }\n            if (name === 'content-length') {\n                const contentLength = Number(value.toString());\n                if (!isNaN(contentLength)) {\n                    spanAttributes['http.response.header.content-length'] = contentLength;\n                }\n            }\n        }\n        span.setAttributes(spanAttributes);\n        span.setStatus({\n            code: response.statusCode >= 400\n                ? api_1.SpanStatusCode.ERROR\n                : api_1.SpanStatusCode.UNSET,\n        });\n        record.attributes = Object.assign(attributes, spanAttributes);\n    }\n    // This is the last event we receive if the request went without any errors\n    onDone({ request }) {\n        const record = this._recordFromReq.get(request);\n        if (!record) {\n            return;\n        }\n        const { span, attributes, startTime } = record;\n        // End the span\n        span.end();\n        this._recordFromReq.delete(request);\n        // Record metrics\n        this.recordRequestDuration(attributes, startTime);\n    }\n    // This is the event we get when something is wrong in the request like\n    // - invalid options when calling `fetch` global API or any undici method for request\n    // - connectivity errors such as unreachable host\n    // - requests aborted through an `AbortController.signal`\n    // NOTE: server errors are considered valid responses and it's the lib consumer\n    // who should deal with that.\n    onError({ request, error }) {\n        const record = this._recordFromReq.get(request);\n        if (!record) {\n            return;\n        }\n        const { span, attributes, startTime } = record;\n        // NOTE: in `undici@6.3.0` when request aborted the error type changes from\n        // a custom error (`RequestAbortedError`) to a built-in `DOMException` carrying\n        // some differences:\n        // - `code` is from DOMEXception (ABORT_ERR: 20)\n        // - `message` changes\n        // - stacktrace is smaller and contains node internal frames\n        span.recordException(error);\n        span.setStatus({\n            code: api_1.SpanStatusCode.ERROR,\n            message: error.message,\n        });\n        span.end();\n        this._recordFromReq.delete(request);\n        // Record metrics (with the error)\n        attributes[SemanticAttributes_1.SemanticAttributes.ERROR_TYPE] = error.message;\n        this.recordRequestDuration(attributes, startTime);\n    }\n    recordRequestDuration(attributes, startTime) {\n        // Time to record metrics\n        const metricsAttributes = {};\n        // Get the attribs already in span attributes\n        const keysToCopy = [\n            SemanticAttributes_1.SemanticAttributes.HTTP_RESPONSE_STATUS_CODE,\n            SemanticAttributes_1.SemanticAttributes.HTTP_REQUEST_METHOD,\n            SemanticAttributes_1.SemanticAttributes.SERVER_ADDRESS,\n            SemanticAttributes_1.SemanticAttributes.SERVER_PORT,\n            SemanticAttributes_1.SemanticAttributes.URL_SCHEME,\n            SemanticAttributes_1.SemanticAttributes.ERROR_TYPE,\n        ];\n        keysToCopy.forEach(key => {\n            if (key in attributes) {\n                metricsAttributes[key] = attributes[key];\n            }\n        });\n        // Take the duration and record it\n        const durationSeconds = (0, core_1.hrTimeToMilliseconds)((0, core_1.hrTimeDuration)(startTime, (0, core_1.hrTime)())) / 1000;\n        this._httpClientDurationHistogram.record(durationSeconds, metricsAttributes);\n    }\n    getRequestMethod(original) {\n        const knownMethods = {\n            CONNECT: true,\n            OPTIONS: true,\n            HEAD: true,\n            GET: true,\n            POST: true,\n            PUT: true,\n            PATCH: true,\n            DELETE: true,\n            TRACE: true,\n        };\n        if (original.toUpperCase() in knownMethods) {\n            return original.toUpperCase();\n        }\n        return '_OTHER';\n    }\n}\nexports.UndiciInstrumentation = UndiciInstrumentation;\n//# sourceMappingURL=undici.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/undici.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/version.js":
/*!*********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/version.js ***!
  \*********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.10.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-undici';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/version.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/enums/SemanticAttributes.js":
/*!**************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/enums/SemanticAttributes.js ***!
  \**************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.SemanticAttributes = void 0;\n// DO NOT EDIT, this is an Auto-generated file from scripts/semconv/templates//templates/SemanticAttributes.ts.j2\nexports.SemanticAttributes = {\n    /**\n     * State of the HTTP connection in the HTTP connection pool.\n     */\n    HTTP_CONNECTION_STATE: 'http.connection.state',\n    /**\n    * Describes a class of error the operation ended with.\n    *\n    * Note: The `error.type` SHOULD be predictable and SHOULD have low cardinality.\n  Instrumentations SHOULD document the list of errors they report.\n  \n  The cardinality of `error.type` within one instrumentation library SHOULD be low.\n  Telemetry consumers that aggregate data from multiple instrumentation libraries and applications\n  should be prepared for `error.type` to have high cardinality at query time when no\n  additional filters are applied.\n  \n  If the operation has completed successfully, instrumentations SHOULD NOT set `error.type`.\n  \n  If a specific domain defines its own set of error identifiers (such as HTTP or gRPC status codes),\n  it&#39;s RECOMMENDED to:\n  \n  * Use a domain-specific attribute\n  * Set `error.type` to capture all errors, regardless of whether they are defined within the domain-specific set or not.\n    */\n    ERROR_TYPE: 'error.type',\n    /**\n     * The size of the request payload body in bytes. This is the number of bytes transferred excluding headers and is often, but not always, present as the [Content-Length](https://www.rfc-editor.org/rfc/rfc9110.html#field.content-length) header. For requests using transport encoding, this should be the compressed size.\n     */\n    HTTP_REQUEST_BODY_SIZE: 'http.request.body.size',\n    /**\n    * HTTP request method.\n    *\n    * Note: HTTP request method value SHOULD be &#34;known&#34; to the instrumentation.\n  By default, this convention defines &#34;known&#34; methods as the ones listed in [RFC9110](https://www.rfc-editor.org/rfc/rfc9110.html#name-methods)\n  and the PATCH method defined in [RFC5789](https://www.rfc-editor.org/rfc/rfc5789.html).\n  \n  If the HTTP request method is not known to instrumentation, it MUST set the `http.request.method` attribute to `_OTHER`.\n  \n  If the HTTP instrumentation could end up converting valid HTTP request methods to `_OTHER`, then it MUST provide a way to override\n  the list of known HTTP methods. If this override is done via environment variable, then the environment variable MUST be named\n  OTEL_INSTRUMENTATION_HTTP_KNOWN_METHODS and support a comma-separated list of case-sensitive known HTTP methods\n  (this list MUST be a full override of the default known method, it is not a list of known methods in addition to the defaults).\n  \n  HTTP method names are case-sensitive and `http.request.method` attribute value MUST match a known HTTP method name exactly.\n  Instrumentations for specific web frameworks that consider HTTP methods to be case insensitive, SHOULD populate a canonical equivalent.\n  Tracing instrumentations that do so, MUST also set `http.request.method_original` to the original value.\n    */\n    HTTP_REQUEST_METHOD: 'http.request.method',\n    /**\n     * Original HTTP method sent by the client in the request line.\n     */\n    HTTP_REQUEST_METHOD_ORIGINAL: 'http.request.method_original',\n    /**\n     * The ordinal number of request resending attempt (for any reason, including redirects).\n     *\n     * Note: The resend count SHOULD be updated each time an HTTP request gets resent by the client, regardless of what was the cause of the resending (e.g. redirection, authorization failure, 503 Server Unavailable, network issues, or any other).\n     */\n    HTTP_REQUEST_RESEND_COUNT: 'http.request.resend_count',\n    /**\n     * The size of the response payload body in bytes. This is the number of bytes transferred excluding headers and is often, but not always, present as the [Content-Length](https://www.rfc-editor.org/rfc/rfc9110.html#field.content-length) header. For requests using transport encoding, this should be the compressed size.\n     */\n    HTTP_RESPONSE_BODY_SIZE: 'http.response.body.size',\n    /**\n     * [HTTP response status code](https://tools.ietf.org/html/rfc7231#section-6).\n     */\n    HTTP_RESPONSE_STATUS_CODE: 'http.response.status_code',\n    /**\n    * The matched route, that is, the path template in the format used by the respective server framework.\n    *\n    * Note: MUST NOT be populated when this is not supported by the HTTP server framework as the route attribute should have low-cardinality and the URI path can NOT substitute it.\n  SHOULD include the [application root](/docs/http/http-spans.md#http-server-definitions) if there is one.\n    */\n    HTTP_ROUTE: 'http.route',\n    /**\n     * Peer address of the network connection - IP address or Unix domain socket name.\n     */\n    NETWORK_PEER_ADDRESS: 'network.peer.address',\n    /**\n     * Peer port number of the network connection.\n     */\n    NETWORK_PEER_PORT: 'network.peer.port',\n    /**\n     * [OSI application layer](https://osi-model.com/application-layer/) or non-OSI equivalent.\n     *\n     * Note: The value SHOULD be normalized to lowercase.\n     */\n    NETWORK_PROTOCOL_NAME: 'network.protocol.name',\n    /**\n     * Version of the protocol specified in `network.protocol.name`.\n     *\n     * Note: `network.protocol.version` refers to the version of the protocol used and might be different from the protocol client&#39;s version. If the HTTP client has a version of `0.27.2`, but sends HTTP version `1.1`, this attribute should be set to `1.1`.\n     */\n    NETWORK_PROTOCOL_VERSION: 'network.protocol.version',\n    /**\n     * Server domain name if available without reverse DNS lookup; otherwise, IP address or Unix domain socket name.\n     *\n     * Note: When observed from the client side, and when communicating through an intermediary, `server.address` SHOULD represent the server address behind any intermediaries, for example proxies, if it&#39;s available.\n     */\n    SERVER_ADDRESS: 'server.address',\n    /**\n     * Server port number.\n     *\n     * Note: When observed from the client side, and when communicating through an intermediary, `server.port` SHOULD represent the server port behind any intermediaries, for example proxies, if it&#39;s available.\n     */\n    SERVER_PORT: 'server.port',\n    /**\n    * Absolute URL describing a network resource according to [RFC3986](https://www.rfc-editor.org/rfc/rfc3986).\n    *\n    * Note: For network calls, URL usually has `scheme://host[:port][path][?query][#fragment]` format, where the fragment is not transmitted over HTTP, but if it is known, it SHOULD be included nevertheless.\n  `url.full` MUST NOT contain credentials passed via URL in form of `https://username:<EMAIL>/`. In such case username and password SHOULD be redacted and attribute&#39;s value SHOULD be `https://REDACTED:<EMAIL>/`.\n  `url.full` SHOULD capture the absolute URL when it is available (or can be reconstructed) and SHOULD NOT be validated or modified except for sanitizing purposes.\n    */\n    URL_FULL: 'url.full',\n    /**\n     * The [URI path](https://www.rfc-editor.org/rfc/rfc3986#section-3.3) component.\n     */\n    URL_PATH: 'url.path',\n    /**\n     * The [URI query](https://www.rfc-editor.org/rfc/rfc3986#section-3.4) component.\n     *\n     * Note: Sensitive content provided in query string SHOULD be scrubbed when instrumentations can identify it.\n     */\n    URL_QUERY: 'url.query',\n    /**\n     * The [URI scheme](https://www.rfc-editor.org/rfc/rfc3986#section-3.1) component identifying the used protocol.\n     */\n    URL_SCHEME: 'url.scheme',\n    /**\n     * Value of the [HTTP User-Agent](https://www.rfc-editor.org/rfc/rfc9110.html#field.user-agent) header sent by the client.\n     */\n    USER_AGENT_ORIGINAL: 'user_agent.original',\n};\n//# sourceMappingURL=SemanticAttributes.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/enums/SemanticAttributes.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/index.js":
/*!*******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/index.js ***!
  \*******************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./undici */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/undici.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/types.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/types.js":
/*!*******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/types.js ***!
  \*******************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF9lYTQyMDIzZmViNjMwY2Y4MWY5N2JkY2UxOGE0NmU5NC9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLXVuZGljaS9idWlsZC9zcmMvdHlwZXMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYW53YXJcXERvY3VtZW50c1xcQ3ViZW50d2ViXFxjdWJlbnRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF9lYTQyMDIzZmViNjMwY2Y4MWY5N2JkY2UxOGE0NmU5NFxcbm9kZV9tb2R1bGVzXFxAb3BlbnRlbGVtZXRyeVxcaW5zdHJ1bWVudGF0aW9uLXVuZGljaVxcYnVpbGRcXHNyY1xcdHlwZXMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD10eXBlcy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/types.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/undici.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/undici.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.UndiciInstrumentation = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst diagch = __webpack_require__(/*! diagnostics_channel */ \"diagnostics_channel\");\nconst url_1 = __webpack_require__(/*! url */ \"url\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/version.js\");\nconst SemanticAttributes_1 = __webpack_require__(/*! ./enums/SemanticAttributes */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/enums/SemanticAttributes.js\");\nconst core_1 = __webpack_require__(/*! @opentelemetry/core */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+core@1.30.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/core/build/esm/index.js\");\n// A combination of https://github.com/elastic/apm-agent-nodejs and\n// https://github.com/gadget-inc/opentelemetry-instrumentations/blob/main/packages/opentelemetry-instrumentation-undici/src/index.ts\nclass UndiciInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, config);\n        this._recordFromReq = new WeakMap();\n    }\n    // No need to instrument files/modules\n    init() {\n        return undefined;\n    }\n    disable() {\n        super.disable();\n        this._channelSubs.forEach(sub => sub.unsubscribe());\n        this._channelSubs.length = 0;\n    }\n    enable() {\n        // \"enabled\" handling is currently a bit messy with InstrumentationBase.\n        // If constructed with `{enabled: false}`, this `.enable()` is still called,\n        // and `this.getConfig().enabled !== this.isEnabled()`, creating confusion.\n        //\n        // For now, this class will setup for instrumenting if `.enable()` is\n        // called, but use `this.getConfig().enabled` to determine if\n        // instrumentation should be generated. This covers the more likely common\n        // case of config being given a construction time, rather than later via\n        // `instance.enable()`, `.disable()`, or `.setConfig()` calls.\n        super.enable();\n        // This method is called by the super-class constructor before ours is\n        // called. So we need to ensure the property is initalized.\n        this._channelSubs = this._channelSubs || [];\n        // Avoid to duplicate subscriptions\n        if (this._channelSubs.length > 0) {\n            return;\n        }\n        this.subscribeToChannel('undici:request:create', this.onRequestCreated.bind(this));\n        this.subscribeToChannel('undici:client:sendHeaders', this.onRequestHeaders.bind(this));\n        this.subscribeToChannel('undici:request:headers', this.onResponseHeaders.bind(this));\n        this.subscribeToChannel('undici:request:trailers', this.onDone.bind(this));\n        this.subscribeToChannel('undici:request:error', this.onError.bind(this));\n    }\n    _updateMetricInstruments() {\n        this._httpClientDurationHistogram = this.meter.createHistogram('http.client.request.duration', {\n            description: 'Measures the duration of outbound HTTP requests.',\n            unit: 's',\n            valueType: api_1.ValueType.DOUBLE,\n            advice: {\n                explicitBucketBoundaries: [\n                    0.005, 0.01, 0.025, 0.05, 0.075, 0.1, 0.25, 0.5, 0.75, 1, 2.5, 5,\n                    7.5, 10,\n                ],\n            },\n        });\n    }\n    subscribeToChannel(diagnosticChannel, onMessage) {\n        var _a;\n        // `diagnostics_channel` had a ref counting bug until v18.19.0.\n        // https://github.com/nodejs/node/pull/47520\n        const [major, minor] = process.version\n            .replace('v', '')\n            .split('.')\n            .map(n => Number(n));\n        const useNewSubscribe = major > 18 || (major === 18 && minor >= 19);\n        let unsubscribe;\n        if (useNewSubscribe) {\n            (_a = diagch.subscribe) === null || _a === void 0 ? void 0 : _a.call(diagch, diagnosticChannel, onMessage);\n            unsubscribe = () => { var _a; return (_a = diagch.unsubscribe) === null || _a === void 0 ? void 0 : _a.call(diagch, diagnosticChannel, onMessage); };\n        }\n        else {\n            const channel = diagch.channel(diagnosticChannel);\n            channel.subscribe(onMessage);\n            unsubscribe = () => channel.unsubscribe(onMessage);\n        }\n        this._channelSubs.push({\n            name: diagnosticChannel,\n            unsubscribe,\n        });\n    }\n    // This is the 1st message we receive for each request (fired after request creation). Here we will\n    // create the span and populate some atttributes, then link the span to the request for further\n    // span processing\n    onRequestCreated({ request }) {\n        // Ignore if:\n        // - instrumentation is disabled\n        // - ignored by config\n        // - method is 'CONNECT'\n        const config = this.getConfig();\n        const enabled = config.enabled !== false;\n        const shouldIgnoreReq = (0, instrumentation_1.safeExecuteInTheMiddle)(() => {\n            var _a;\n            return !enabled ||\n                request.method === 'CONNECT' ||\n                ((_a = config.ignoreRequestHook) === null || _a === void 0 ? void 0 : _a.call(config, request));\n        }, e => e && this._diag.error('caught ignoreRequestHook error: ', e), true);\n        if (shouldIgnoreReq) {\n            return;\n        }\n        const startTime = (0, core_1.hrTime)();\n        let requestUrl;\n        try {\n            requestUrl = new url_1.URL(request.path, request.origin);\n        }\n        catch (err) {\n            this._diag.warn('could not determine url.full:', err);\n            // Skip instrumenting this request.\n            return;\n        }\n        const urlScheme = requestUrl.protocol.replace(':', '');\n        const requestMethod = this.getRequestMethod(request.method);\n        const attributes = {\n            [SemanticAttributes_1.SemanticAttributes.HTTP_REQUEST_METHOD]: requestMethod,\n            [SemanticAttributes_1.SemanticAttributes.HTTP_REQUEST_METHOD_ORIGINAL]: request.method,\n            [SemanticAttributes_1.SemanticAttributes.URL_FULL]: requestUrl.toString(),\n            [SemanticAttributes_1.SemanticAttributes.URL_PATH]: requestUrl.pathname,\n            [SemanticAttributes_1.SemanticAttributes.URL_QUERY]: requestUrl.search,\n            [SemanticAttributes_1.SemanticAttributes.URL_SCHEME]: urlScheme,\n        };\n        const schemePorts = { https: '443', http: '80' };\n        const serverAddress = requestUrl.hostname;\n        const serverPort = requestUrl.port || schemePorts[urlScheme];\n        attributes[SemanticAttributes_1.SemanticAttributes.SERVER_ADDRESS] = serverAddress;\n        if (serverPort && !isNaN(Number(serverPort))) {\n            attributes[SemanticAttributes_1.SemanticAttributes.SERVER_PORT] = Number(serverPort);\n        }\n        // Get user agent from headers\n        let userAgent;\n        if (Array.isArray(request.headers)) {\n            const idx = request.headers.findIndex(h => h.toLowerCase() === 'user-agent');\n            if (idx >= 0) {\n                userAgent = request.headers[idx + 1];\n            }\n        }\n        else if (typeof request.headers === 'string') {\n            const headers = request.headers.split('\\r\\n');\n            const uaHeader = headers.find(h => h.toLowerCase().startsWith('user-agent'));\n            userAgent =\n                uaHeader && uaHeader.substring(uaHeader.indexOf(':') + 1).trim();\n        }\n        if (userAgent) {\n            attributes[SemanticAttributes_1.SemanticAttributes.USER_AGENT_ORIGINAL] = userAgent;\n        }\n        // Get attributes from the hook if present\n        const hookAttributes = (0, instrumentation_1.safeExecuteInTheMiddle)(() => { var _a; return (_a = config.startSpanHook) === null || _a === void 0 ? void 0 : _a.call(config, request); }, e => e && this._diag.error('caught startSpanHook error: ', e), true);\n        if (hookAttributes) {\n            Object.entries(hookAttributes).forEach(([key, val]) => {\n                attributes[key] = val;\n            });\n        }\n        // Check if parent span is required via config and:\n        // - if a parent is required but not present, we use a `NoopSpan` to still\n        //   propagate context without recording it.\n        // - create a span otherwise\n        const activeCtx = api_1.context.active();\n        const currentSpan = api_1.trace.getSpan(activeCtx);\n        let span;\n        if (config.requireParentforSpans &&\n            (!currentSpan || !api_1.trace.isSpanContextValid(currentSpan.spanContext()))) {\n            span = api_1.trace.wrapSpanContext(api_1.INVALID_SPAN_CONTEXT);\n        }\n        else {\n            span = this.tracer.startSpan(requestMethod === '_OTHER' ? 'HTTP' : requestMethod, {\n                kind: api_1.SpanKind.CLIENT,\n                attributes: attributes,\n            }, activeCtx);\n        }\n        // Execute the request hook if defined\n        (0, instrumentation_1.safeExecuteInTheMiddle)(() => { var _a; return (_a = config.requestHook) === null || _a === void 0 ? void 0 : _a.call(config, span, request); }, e => e && this._diag.error('caught requestHook error: ', e), true);\n        // Context propagation goes last so no hook can tamper\n        // the propagation headers\n        const requestContext = api_1.trace.setSpan(api_1.context.active(), span);\n        const addedHeaders = {};\n        api_1.propagation.inject(requestContext, addedHeaders);\n        const headerEntries = Object.entries(addedHeaders);\n        for (let i = 0; i < headerEntries.length; i++) {\n            const [k, v] = headerEntries[i];\n            if (typeof request.addHeader === 'function') {\n                request.addHeader(k, v);\n            }\n            else if (typeof request.headers === 'string') {\n                request.headers += `${k}: ${v}\\r\\n`;\n            }\n            else if (Array.isArray(request.headers)) {\n                // undici@6.11.0 accidentally, briefly removed `request.addHeader()`.\n                request.headers.push(k, v);\n            }\n        }\n        this._recordFromReq.set(request, { span, attributes, startTime });\n    }\n    // This is the 2nd message we receive for each request. It is fired when connection with\n    // the remote is established and about to send the first byte. Here we do have info about the\n    // remote address and port so we can populate some `network.*` attributes into the span\n    onRequestHeaders({ request, socket }) {\n        var _a;\n        const record = this._recordFromReq.get(request);\n        if (!record) {\n            return;\n        }\n        const config = this.getConfig();\n        const { span } = record;\n        const { remoteAddress, remotePort } = socket;\n        const spanAttributes = {\n            [SemanticAttributes_1.SemanticAttributes.NETWORK_PEER_ADDRESS]: remoteAddress,\n            [SemanticAttributes_1.SemanticAttributes.NETWORK_PEER_PORT]: remotePort,\n        };\n        // After hooks have been processed (which may modify request headers)\n        // we can collect the headers based on the configuration\n        if ((_a = config.headersToSpanAttributes) === null || _a === void 0 ? void 0 : _a.requestHeaders) {\n            const headersToAttribs = new Set(config.headersToSpanAttributes.requestHeaders.map(n => n.toLowerCase()));\n            // headers could be in form\n            // ['name: value', ...] for v5\n            // ['name', 'value', ...] for v6\n            const rawHeaders = Array.isArray(request.headers)\n                ? request.headers\n                : request.headers.split('\\r\\n');\n            rawHeaders.forEach((h, idx) => {\n                const sepIndex = h.indexOf(':');\n                const hasSeparator = sepIndex !== -1;\n                const name = (hasSeparator ? h.substring(0, sepIndex) : h).toLowerCase();\n                const value = hasSeparator\n                    ? h.substring(sepIndex + 1)\n                    : rawHeaders[idx + 1];\n                if (headersToAttribs.has(name)) {\n                    spanAttributes[`http.request.header.${name}`] = value.trim();\n                }\n            });\n        }\n        span.setAttributes(spanAttributes);\n    }\n    // This is the 3rd message we get for each request and it's fired when the server\n    // headers are received, body may not be accessible yet.\n    // From the response headers we can set the status and content length\n    onResponseHeaders({ request, response, }) {\n        var _a, _b;\n        const record = this._recordFromReq.get(request);\n        if (!record) {\n            return;\n        }\n        const { span, attributes } = record;\n        const spanAttributes = {\n            [SemanticAttributes_1.SemanticAttributes.HTTP_RESPONSE_STATUS_CODE]: response.statusCode,\n        };\n        const config = this.getConfig();\n        // Execute the response hook if defined\n        (0, instrumentation_1.safeExecuteInTheMiddle)(() => { var _a; return (_a = config.responseHook) === null || _a === void 0 ? void 0 : _a.call(config, span, { request, response }); }, e => e && this._diag.error('caught responseHook error: ', e), true);\n        const headersToAttribs = new Set();\n        if ((_a = config.headersToSpanAttributes) === null || _a === void 0 ? void 0 : _a.responseHeaders) {\n            (_b = config.headersToSpanAttributes) === null || _b === void 0 ? void 0 : _b.responseHeaders.forEach(name => headersToAttribs.add(name.toLowerCase()));\n        }\n        for (let idx = 0; idx < response.headers.length; idx = idx + 2) {\n            const name = response.headers[idx].toString().toLowerCase();\n            const value = response.headers[idx + 1];\n            if (headersToAttribs.has(name)) {\n                spanAttributes[`http.response.header.${name}`] = value.toString();\n            }\n            if (name === 'content-length') {\n                const contentLength = Number(value.toString());\n                if (!isNaN(contentLength)) {\n                    spanAttributes['http.response.header.content-length'] = contentLength;\n                }\n            }\n        }\n        span.setAttributes(spanAttributes);\n        span.setStatus({\n            code: response.statusCode >= 400\n                ? api_1.SpanStatusCode.ERROR\n                : api_1.SpanStatusCode.UNSET,\n        });\n        record.attributes = Object.assign(attributes, spanAttributes);\n    }\n    // This is the last event we receive if the request went without any errors\n    onDone({ request }) {\n        const record = this._recordFromReq.get(request);\n        if (!record) {\n            return;\n        }\n        const { span, attributes, startTime } = record;\n        // End the span\n        span.end();\n        this._recordFromReq.delete(request);\n        // Record metrics\n        this.recordRequestDuration(attributes, startTime);\n    }\n    // This is the event we get when something is wrong in the request like\n    // - invalid options when calling `fetch` global API or any undici method for request\n    // - connectivity errors such as unreachable host\n    // - requests aborted through an `AbortController.signal`\n    // NOTE: server errors are considered valid responses and it's the lib consumer\n    // who should deal with that.\n    onError({ request, error }) {\n        const record = this._recordFromReq.get(request);\n        if (!record) {\n            return;\n        }\n        const { span, attributes, startTime } = record;\n        // NOTE: in `undici@6.3.0` when request aborted the error type changes from\n        // a custom error (`RequestAbortedError`) to a built-in `DOMException` carrying\n        // some differences:\n        // - `code` is from DOMEXception (ABORT_ERR: 20)\n        // - `message` changes\n        // - stacktrace is smaller and contains node internal frames\n        span.recordException(error);\n        span.setStatus({\n            code: api_1.SpanStatusCode.ERROR,\n            message: error.message,\n        });\n        span.end();\n        this._recordFromReq.delete(request);\n        // Record metrics (with the error)\n        attributes[SemanticAttributes_1.SemanticAttributes.ERROR_TYPE] = error.message;\n        this.recordRequestDuration(attributes, startTime);\n    }\n    recordRequestDuration(attributes, startTime) {\n        // Time to record metrics\n        const metricsAttributes = {};\n        // Get the attribs already in span attributes\n        const keysToCopy = [\n            SemanticAttributes_1.SemanticAttributes.HTTP_RESPONSE_STATUS_CODE,\n            SemanticAttributes_1.SemanticAttributes.HTTP_REQUEST_METHOD,\n            SemanticAttributes_1.SemanticAttributes.SERVER_ADDRESS,\n            SemanticAttributes_1.SemanticAttributes.SERVER_PORT,\n            SemanticAttributes_1.SemanticAttributes.URL_SCHEME,\n            SemanticAttributes_1.SemanticAttributes.ERROR_TYPE,\n        ];\n        keysToCopy.forEach(key => {\n            if (key in attributes) {\n                metricsAttributes[key] = attributes[key];\n            }\n        });\n        // Take the duration and record it\n        const durationSeconds = (0, core_1.hrTimeToMilliseconds)((0, core_1.hrTimeDuration)(startTime, (0, core_1.hrTime)())) / 1000;\n        this._httpClientDurationHistogram.record(durationSeconds, metricsAttributes);\n    }\n    getRequestMethod(original) {\n        const knownMethods = {\n            CONNECT: true,\n            OPTIONS: true,\n            HEAD: true,\n            GET: true,\n            POST: true,\n            PUT: true,\n            PATCH: true,\n            DELETE: true,\n            TRACE: true,\n        };\n        if (original.toUpperCase() in knownMethods) {\n            return original.toUpperCase();\n        }\n        return '_OTHER';\n    }\n}\nexports.UndiciInstrumentation = UndiciInstrumentation;\n//# sourceMappingURL=undici.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/undici.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/version.js":
/*!*********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/version.js ***!
  \*********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.10.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-undici';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/version.js\n");

/***/ })

};
;