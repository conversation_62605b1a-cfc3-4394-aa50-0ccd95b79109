"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/use-deep-compare@1.3.0_react@19.1.0";
exports.ids = ["vendor-chunks/use-deep-compare@1.3.0_react@19.1.0"];
exports.modules = {

/***/ "(rsc)/../../node_modules/.pnpm/use-deep-compare@1.3.0_react@19.1.0/node_modules/use-deep-compare/dist-web/index.js":
/*!********************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/use-deep-compare@1.3.0_react@19.1.0/node_modules/use-deep-compare/dist-web/index.js ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDeepCompareCallback: () => (/* binding */ useDeepCompareCallback),\n/* harmony export */   useDeepCompareEffect: () => (/* binding */ useDeepCompareEffect),\n/* harmony export */   useDeepCompareImperativeHandle: () => (/* binding */ useDeepCompareImperativeHandle),\n/* harmony export */   useDeepCompareLayoutEffect: () => (/* binding */ useDeepCompareLayoutEffect),\n/* harmony export */   useDeepCompareMemo: () => (/* binding */ useDeepCompareMemo)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var dequal__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! dequal */ \"(rsc)/../../node_modules/.pnpm/dequal@2.0.3/node_modules/dequal/dist/index.mjs\");\n\n\n\nfunction useDeepCompareMemoize(dependencies) {\n  const dependenciesRef = react__WEBPACK_IMPORTED_MODULE_0___default().useRef(dependencies);\n  const signalRef = react__WEBPACK_IMPORTED_MODULE_0___default().useRef(0);\n\n  if (!(0,dequal__WEBPACK_IMPORTED_MODULE_1__.dequal)(dependencies, dependenciesRef.current)) {\n    dependenciesRef.current = dependencies;\n    signalRef.current += 1;\n  }\n\n  return react__WEBPACK_IMPORTED_MODULE_0___default().useMemo(() => dependenciesRef.current, [signalRef.current]);\n}\n\n/**\n * `useDeepCompareCallback` will return a memoized version of the callback that\n * only changes if one of the `dependencies` has changed.\n *\n * Warning: `useDeepCompareCallback` should not be used with dependencies that\n * are all primitive values. Use `React.useCallback` instead.\n *\n * @see {@link https://react.dev/reference/react/useCallback}\n */\n\nfunction useDeepCompareCallback(callback, dependencies) {\n  return react__WEBPACK_IMPORTED_MODULE_0___default().useCallback(callback, useDeepCompareMemoize(dependencies));\n}\n\n/**\n * Accepts a function that contains imperative, possibly effectful code.\n *\n * Warning: `useDeepCompareEffect` should not be used with dependencies that\n * are all primitive values. Use `React.useEffect` instead.\n *\n * @see {@link https://react.dev/reference/react/useEffect}\n */\n\nfunction useDeepCompareEffect(effect, dependencies) {\n  react__WEBPACK_IMPORTED_MODULE_0___default().useEffect(effect, useDeepCompareMemoize(dependencies));\n}\n\n/**\n * `useDeepCompareImperativeHandle` customizes the instance value that is exposed to parent components when using `ref`.\n * As always, imperative code using refs should be avoided in most cases.\n *\n * `useDeepCompareImperativeHandle` should be used with `React.forwardRef`.\n *\n * It's similar to `useImperativeHandle`, but uses deep comparison on the dependencies.\n *\n * Warning: `useDeepCompareImperativeHandle` should not be used with dependencies that\n * are all primitive values. Use `React.useImperativeHandle` instead.\n *\n * @see {@link https://react.dev/reference/react/useImperativeHandle}\n */\n\nfunction useDeepCompareImperativeHandle(ref, init, dependencies) {\n  react__WEBPACK_IMPORTED_MODULE_0___default().useImperativeHandle(ref, init, useDeepCompareMemoize(dependencies));\n}\n\n/**\n * The signature is identical to `useDeepCompareEffect`, but it fires synchronously after all DOM mutations.\n * Use this to read layout from the DOM and synchronously re-render. Updates scheduled inside\n * `useDeepCompareLayoutEffect` will be flushed synchronously, before the browser has a chance to paint.\n *\n * Prefer the standard `useDeepCompareEffect` when possible to avoid blocking visual updates.\n *\n * If you’re migrating code from a class component, `useDeepCompareLayoutEffect` fires in the same phase as\n * `componentDidMount` and `componentDidUpdate`.\n *\n * Warning: `useDeepCompareLayoutEffect` should not be used with dependencies that\n * are all primitive values. Use `React.useLayoutEffect` instead.\n *\n * @see {@link https://react.dev/reference/react/useLayoutEffect}\n */\n\nfunction useDeepCompareLayoutEffect(effect, dependencies) {\n  react__WEBPACK_IMPORTED_MODULE_0___default().useLayoutEffect(effect, useDeepCompareMemoize(dependencies));\n}\n\n/**\n * `useDeepCompareMemo` will only recompute the memoized value when one of the\n * `dependencies` has changed.\n *\n * Warning: `useDeepCompareMemo` should not be used with dependencies that\n * are all primitive values. Use `React.useMemo` instead.\n *\n * @see {@link https://react.dev/reference/react/useMemo}\n */\n\nfunction useDeepCompareMemo(factory, dependencies) {\n  return react__WEBPACK_IMPORTED_MODULE_0___default().useMemo(factory, useDeepCompareMemoize(dependencies));\n}\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/use-deep-compare@1.3.0_react@19.1.0/node_modules/use-deep-compare/dist-web/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/use-deep-compare@1.3.0_react@19.1.0/node_modules/use-deep-compare/dist-web/index.js":
/*!********************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/use-deep-compare@1.3.0_react@19.1.0/node_modules/use-deep-compare/dist-web/index.js ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDeepCompareCallback: () => (/* binding */ useDeepCompareCallback),\n/* harmony export */   useDeepCompareEffect: () => (/* binding */ useDeepCompareEffect),\n/* harmony export */   useDeepCompareImperativeHandle: () => (/* binding */ useDeepCompareImperativeHandle),\n/* harmony export */   useDeepCompareLayoutEffect: () => (/* binding */ useDeepCompareLayoutEffect),\n/* harmony export */   useDeepCompareMemo: () => (/* binding */ useDeepCompareMemo)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var dequal__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! dequal */ \"(ssr)/../../node_modules/.pnpm/dequal@2.0.3/node_modules/dequal/dist/index.mjs\");\n\n\n\nfunction useDeepCompareMemoize(dependencies) {\n  const dependenciesRef = react__WEBPACK_IMPORTED_MODULE_0___default().useRef(dependencies);\n  const signalRef = react__WEBPACK_IMPORTED_MODULE_0___default().useRef(0);\n\n  if (!(0,dequal__WEBPACK_IMPORTED_MODULE_1__.dequal)(dependencies, dependenciesRef.current)) {\n    dependenciesRef.current = dependencies;\n    signalRef.current += 1;\n  }\n\n  return react__WEBPACK_IMPORTED_MODULE_0___default().useMemo(() => dependenciesRef.current, [signalRef.current]);\n}\n\n/**\n * `useDeepCompareCallback` will return a memoized version of the callback that\n * only changes if one of the `dependencies` has changed.\n *\n * Warning: `useDeepCompareCallback` should not be used with dependencies that\n * are all primitive values. Use `React.useCallback` instead.\n *\n * @see {@link https://react.dev/reference/react/useCallback}\n */\n\nfunction useDeepCompareCallback(callback, dependencies) {\n  return react__WEBPACK_IMPORTED_MODULE_0___default().useCallback(callback, useDeepCompareMemoize(dependencies));\n}\n\n/**\n * Accepts a function that contains imperative, possibly effectful code.\n *\n * Warning: `useDeepCompareEffect` should not be used with dependencies that\n * are all primitive values. Use `React.useEffect` instead.\n *\n * @see {@link https://react.dev/reference/react/useEffect}\n */\n\nfunction useDeepCompareEffect(effect, dependencies) {\n  react__WEBPACK_IMPORTED_MODULE_0___default().useEffect(effect, useDeepCompareMemoize(dependencies));\n}\n\n/**\n * `useDeepCompareImperativeHandle` customizes the instance value that is exposed to parent components when using `ref`.\n * As always, imperative code using refs should be avoided in most cases.\n *\n * `useDeepCompareImperativeHandle` should be used with `React.forwardRef`.\n *\n * It's similar to `useImperativeHandle`, but uses deep comparison on the dependencies.\n *\n * Warning: `useDeepCompareImperativeHandle` should not be used with dependencies that\n * are all primitive values. Use `React.useImperativeHandle` instead.\n *\n * @see {@link https://react.dev/reference/react/useImperativeHandle}\n */\n\nfunction useDeepCompareImperativeHandle(ref, init, dependencies) {\n  react__WEBPACK_IMPORTED_MODULE_0___default().useImperativeHandle(ref, init, useDeepCompareMemoize(dependencies));\n}\n\n/**\n * The signature is identical to `useDeepCompareEffect`, but it fires synchronously after all DOM mutations.\n * Use this to read layout from the DOM and synchronously re-render. Updates scheduled inside\n * `useDeepCompareLayoutEffect` will be flushed synchronously, before the browser has a chance to paint.\n *\n * Prefer the standard `useDeepCompareEffect` when possible to avoid blocking visual updates.\n *\n * If you’re migrating code from a class component, `useDeepCompareLayoutEffect` fires in the same phase as\n * `componentDidMount` and `componentDidUpdate`.\n *\n * Warning: `useDeepCompareLayoutEffect` should not be used with dependencies that\n * are all primitive values. Use `React.useLayoutEffect` instead.\n *\n * @see {@link https://react.dev/reference/react/useLayoutEffect}\n */\n\nfunction useDeepCompareLayoutEffect(effect, dependencies) {\n  react__WEBPACK_IMPORTED_MODULE_0___default().useLayoutEffect(effect, useDeepCompareMemoize(dependencies));\n}\n\n/**\n * `useDeepCompareMemo` will only recompute the memoized value when one of the\n * `dependencies` has changed.\n *\n * Warning: `useDeepCompareMemo` should not be used with dependencies that\n * are all primitive values. Use `React.useMemo` instead.\n *\n * @see {@link https://react.dev/reference/react/useMemo}\n */\n\nfunction useDeepCompareMemo(factory, dependencies) {\n  return react__WEBPACK_IMPORTED_MODULE_0___default().useMemo(factory, useDeepCompareMemoize(dependencies));\n}\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/use-deep-compare@1.3.0_react@19.1.0/node_modules/use-deep-compare/dist-web/index.js\n");

/***/ })

};
;