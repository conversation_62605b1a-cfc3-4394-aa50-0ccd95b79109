import { useState, useEffect } from "react"
import { VSCodeButton, VSCodeCheckbox } from "@vscode/webview-ui-toolkit/react"
import { 
	User, 
	Crown, 
	TrendingUp, 
	Settings, 
	ExternalLink, 
	LogOut,
	Shield,
	AlertTriangle
} from "lucide-react"

import type { CloudUserInfo } from "@cubent/types"

import { useAppTranslation } from "@src/i18n/TranslationContext"
import { vscode } from "@src/utils/vscode"
import { useAuthStore, useIsAuthenticated, useIsAuthenticating, useUserInfo, useAuthError } from "@src/stores/authStore"
import { Button, Progress } from "@src/components/ui"
import { Section } from "@src/components/settings/Section"
import { SectionHeader } from "@src/components/settings/SectionHeader"

interface UserProfile {
	id: string
	email: string
	name?: string
	picture?: string
	subscriptionTier: string
	subscriptionStatus: string
	preferences: {
		usageWarningsEnabled: boolean
		trialExpiryNotifications: boolean
		detailedUsageTracking: boolean
		costAlertsEnabled: boolean
	}
}

interface UsageStats {
	currentPeriod: {
		tokensUsed: number
		tokensLimit: number
		costUsed: number
		costLimit: number
		requestsUsed: number
		requestsLimit: number
	}
}

type AccountViewProps = {
	userInfo: CloudUserInfo | null
	onDone: () => void
}

export const AccountView = ({ userInfo, onDone }: AccountViewProps) => {
	const { t } = useAppTranslation()
	const rooLogoUri = (window as any).IMAGES_BASE_URI + "/qapt-logo.svg"

	// Use auth store for device OAuth state
	const isAuthenticated = useIsAuthenticated()
	const isAuthenticating = useIsAuthenticating()
	const authUserInfo = useUserInfo()
	const authError = useAuthError()

	// User management state
	const [userProfile, setUserProfile] = useState<UserProfile | null>(null)
	const [usageStats, setUsageStats] = useState<UsageStats | null>(null)
	const [loading, setLoading] = useState(true)

	// Use auth store user info if available, fallback to prop
	const displayUserInfo = authUserInfo || userInfo
	const showAsAuthenticated = isAuthenticated

	useEffect(() => {
		if (showAsAuthenticated) {
			loadUserData()
		} else {
			// Clear data when not authenticated
			setUserProfile(null)
			setUsageStats(null)
			setLoading(false)
		}
	}, [showAsAuthenticated, isAuthenticated])

	// Listen for auth store changes to handle logout
	useEffect(() => {
		const unsubscribe = useAuthStore.subscribe(
			(state) => state.isAuthenticated,
			(isAuthenticated) => {
				if (!isAuthenticated) {
					// User has been logged out, clear local state
					setUserProfile(null)
					setUsageStats(null)
					setLoading(false)
				}
			}
		)

		return unsubscribe
	}, [])

	const loadUserData = async () => {
		try {
			vscode.postMessage({ type: "getUserProfile" })
			vscode.postMessage({ type: "getUserUsageStats" })
			
			// Set fallback data after a delay if no response
			setTimeout(() => {
				if (!userProfile && displayUserInfo) {
					setUserProfile({
						id: "user_" + Date.now(),
						email: displayUserInfo.email || "",
						name: displayUserInfo.name,
						picture: displayUserInfo.picture,
						subscriptionTier: "free_trial",
						subscriptionStatus: "active",
						preferences: {
							usageWarningsEnabled: true,
							trialExpiryNotifications: true,
							detailedUsageTracking: false,
							costAlertsEnabled: true,
						}
					})
				}
				setLoading(false)
			}, 1000)
		} catch (error) {
			console.error("Error loading user data:", error)
			setLoading(false)
		}
	}

	// Listen for responses from extension
	useEffect(() => {
		const handleMessage = (event: MessageEvent) => {
			const message = event.data
			switch (message.type) {
				case "userProfile":
					setUserProfile(message.data)
					break
				case "usageStats":
					setUsageStats(message.data)
					break
				case "authStateChanged":
					// Handle auth state changes from extension
					if (message.isAuthenticated === false && message.hasActiveSession === false && message.userInfo === null) {
						// User has been logged out
						setUserProfile(null)
						setUsageStats(null)
						setLoading(false)
					}
					break
			}
		}

		window.addEventListener("message", handleMessage)
		return () => window.removeEventListener("message", handleMessage)
	}, [])

	const handleSignIn = () => {
		vscode.postMessage({ type: "deviceOAuthSignIn" })
	}

	const handleSignOut = () => {
		vscode.postMessage({ type: "rooCloudSignOut" })
		// Clear local state immediately
		setUserProfile(null)
		setUsageStats(null)
	}

	const handlePreferenceChange = async (key: string, value: boolean) => {
		if (!userProfile) return

		const updatedPreferences = {
			...userProfile.preferences,
			[key]: value
		}

		vscode.postMessage({
			type: "updateUserPreferences",
			preferences: updatedPreferences
		})

		setUserProfile({
			...userProfile,
			preferences: updatedPreferences
		})
	}

	const getSubscriptionDisplayName = (tier: string) => {
		switch (tier) {
			case "free_trial": return "Free Trial"
			case "basic": return "Basic Plan"
			case "pro": return "Pro Plan"
			case "enterprise": return "Enterprise Plan"
			default: return tier
		}
	}

	const getUsagePercentage = (used: number, limit: number) => {
		return limit > 0 ? Math.min((used / limit) * 100, 100) : 0
	}

	return (
		<div className="flex flex-col h-full overflow-auto">
			{/* Header */}
			<SectionHeader>
				<div className="flex justify-between items-center w-full">
					<span>{t("account:title")}</span>
					<VSCodeButton appearance="primary" onClick={onDone}>
						{t("settings:common.done")}
					</VSCodeButton>
				</div>
			</SectionHeader>

			{showAsAuthenticated ? (
				<div className="flex-1 overflow-auto">
					{/* Profile Section */}
					<Section className="!pb-3">
						<div className="flex items-center gap-3">
							{/* Small Avatar */}
							<div className="w-8 h-8 rounded-full overflow-hidden flex-shrink-0">
								{displayUserInfo?.picture ? (
									<img
										src={displayUserInfo.picture}
										alt={t("account:profilePicture")}
										className="w-full h-full object-cover"
									/>
								) : (
									<div className="w-full h-full flex items-center justify-center bg-vscode-button-background text-vscode-button-foreground text-xs font-medium">
										{displayUserInfo?.name?.charAt(0) || displayUserInfo?.email?.charAt(0) || "?"}
									</div>
								)}
							</div>

							{/* User Info */}
							<div className="flex-1 min-w-0">
								<div className="flex items-center gap-2">
									<h3 className="text-vscode-foreground font-medium truncate text-sm m-0">
										{displayUserInfo?.name || t("account:unknownUser")}
									</h3>
									{userProfile?.subscriptionTier === "enterprise" && (
										<Crown className="w-3 h-3 text-yellow-500 flex-shrink-0" />
									)}
								</div>
								<p className="text-xs text-vscode-descriptionForeground truncate m-0">
									{displayUserInfo?.email || ""}
								</p>
							</div>

							{/* Sign Out Button */}
							<VSCodeButton
								appearance="secondary"
								onClick={handleSignOut}
								title="Sign Out">
								<LogOut className="w-4 h-4" />
							</VSCodeButton>
						</div>
					</Section>

					{loading ? (
						<Section>
							<div className="flex justify-center py-4">
								<div className="animate-spin rounded-full h-6 w-6 border-b-2 border-vscode-foreground"></div>
							</div>
						</Section>
					) : (
						<>
							{/* Subscription Status */}
							{userProfile && (
								<Section>
									<div className="flex items-center gap-2 mb-3">
										<Crown className="w-4 h-4 text-vscode-foreground" />
										<h4 className="text-vscode-foreground font-medium m-0">Subscription</h4>
									</div>
									<div className="bg-vscode-sideBar-background rounded border border-vscode-widget-border p-3 space-y-2">
										<div className="flex justify-between text-sm">
											<span className="text-vscode-descriptionForeground">Plan</span>
											<span className="text-vscode-foreground">{getSubscriptionDisplayName(userProfile.subscriptionTier)}</span>
										</div>
										<div className="flex justify-between text-sm">
											<span className="text-vscode-descriptionForeground">Status</span>
											<span className="text-vscode-foreground capitalize">{userProfile.subscriptionStatus}</span>
										</div>
									</div>
								</Section>
							)}

							{/* Usage Statistics */}
							{usageStats && usageStats.currentPeriod && (
								<Section>
									<div className="flex items-center gap-2 mb-3">
										<TrendingUp className="w-4 h-4 text-vscode-foreground" />
										<h4 className="text-vscode-foreground font-medium m-0">Usage This Month</h4>
									</div>
									<div className="bg-vscode-sideBar-background rounded border border-vscode-widget-border p-3 space-y-3">
										{/* Tokens Usage */}
										<div>
											<div className="flex justify-between text-sm mb-1">
												<span className="text-vscode-descriptionForeground">Tokens</span>
												<span className="text-vscode-foreground">
													{(usageStats.currentPeriod.tokensUsed || 0).toLocaleString()} / {(usageStats.currentPeriod.tokensLimit || 0).toLocaleString()}
												</span>
											</div>
											<Progress
												value={getUsagePercentage(usageStats.currentPeriod.tokensUsed || 0, usageStats.currentPeriod.tokensLimit || 0)}
												className="h-2"
											/>
										</div>
									</div>
								</Section>
							)}

							{/* Preferences */}
							{userProfile && (
								<Section>
									<div className="flex items-center gap-2 mb-3">
										<Settings className="w-4 h-4 text-vscode-foreground" />
										<h4 className="text-vscode-foreground font-medium m-0">Preferences</h4>
									</div>
									<div className="space-y-3">
										<VSCodeCheckbox
											checked={userProfile.preferences.detailedUsageTracking}
											onChange={(e: any) => handlePreferenceChange("detailedUsageTracking", e.target.checked)}>
											Detailed Usage Tracking
										</VSCodeCheckbox>

										<VSCodeCheckbox
											checked={userProfile.preferences.usageWarningsEnabled}
											onChange={(e: any) => handlePreferenceChange("usageWarningsEnabled", e.target.checked)}>
											Usage Warnings
										</VSCodeCheckbox>

										<VSCodeCheckbox
											checked={userProfile.preferences.costAlertsEnabled}
											onChange={(e: any) => handlePreferenceChange("costAlertsEnabled", e.target.checked)}>
											Cost Alerts
										</VSCodeCheckbox>
									</div>
								</Section>
							)}

							{/* Actions */}
							<Section>
								<div className="space-y-2">
									<VSCodeButton
										appearance="secondary"
										onClick={() => vscode.postMessage({ type: "showUsage" })}
										className="w-full">
										<TrendingUp className="w-4 h-4 mr-2" />
										View Usage Details
									</VSCodeButton>

									<VSCodeButton
										appearance="secondary"
										onClick={() => window.postMessage({ type: "action", action: "openExternal", data: { url: "https://cubent.dev/account" } }, "*")}
										className="w-full">
										<ExternalLink className="w-4 h-4 mr-2" />
										Manage Account Online
									</VSCodeButton>
								</div>
							</Section>
						</>
					)}
				</div>
			) : (
				<div className="flex-1 flex flex-col items-center justify-center p-8 text-center">
					<div className="w-16 h-16 mb-6 flex items-center justify-center">
						<div
							className="w-12 h-12 bg-vscode-foreground opacity-60"
							style={{
								WebkitMaskImage: `url('${rooLogoUri}')`,
								WebkitMaskRepeat: "no-repeat",
								WebkitMaskSize: "contain",
								maskImage: `url('${rooLogoUri}')`,
								maskRepeat: "no-repeat",
								maskSize: "contain",
							}}>
							<img src={rooLogoUri} alt="cubent logo" className="w-12 h-12 opacity-0" />
						</div>
					</div>

					<h2 className="text-lg font-medium text-vscode-foreground mb-2">
						Welcome to Cubent
					</h2>
					<p className="text-vscode-descriptionForeground mb-6 max-w-md text-sm">
						Sign in to access your account, manage your subscription, and track your usage.
					</p>

					{isAuthenticating && (
						<div className="mb-6 text-center">
							<div className="flex justify-center mb-3">
								<div className="animate-spin rounded-full h-6 w-6 border-b-2 border-vscode-foreground"></div>
							</div>
							<p className="text-sm text-vscode-descriptionForeground mb-1">
								{t("account:authenticating") || "Authenticating..."}
							</p>
							<p className="text-xs text-vscode-descriptionForeground">
								{t("account:authInstructions") || "Complete authentication in your browser"}
							</p>
						</div>
					)}

					{authError && (
						<div className="mb-6 p-3 bg-vscode-inputValidation-errorBackground rounded border border-vscode-inputValidation-errorBorder">
							<div className="flex items-center gap-2">
								<AlertTriangle className="w-4 h-4 text-vscode-inputValidation-errorForeground" />
								<p className="text-sm text-vscode-inputValidation-errorForeground">{authError}</p>
							</div>
						</div>
					)}

					<div className="flex flex-col gap-3 w-full max-w-xs">
						<VSCodeButton
							appearance="primary"
							onClick={handleSignIn}
							disabled={isAuthenticating}
							className="w-full">
							{isAuthenticating ? "Authenticating..." : t("account:signIn") || "Sign In"}
						</VSCodeButton>
						
						{!isAuthenticating && (
							<VSCodeButton
								appearance="secondary"
								onClick={() => vscode.postMessage({ type: "rooCloudSignIn" })}
								className="w-full">
								{t("account:signInLegacy") || "Sign In (Legacy)"}
							</VSCodeButton>
						)}
					</div>

					<div className="mt-8 text-xs text-vscode-descriptionForeground">
						<p>By signing in, you agree to our Terms of Service and Privacy Policy</p>
					</div>
				</div>
			)}
		</div>
	)
}
