"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/embla-carousel@8.6.0";
exports.ids = ["vendor-chunks/embla-carousel@8.6.0"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/embla-carousel@8.6.0/node_modules/embla-carousel/esm/embla-carousel.esm.js":
/*!***********************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/embla-carousel@8.6.0/node_modules/embla-carousel/esm/embla-carousel.esm.js ***!
  \***********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ EmblaCarousel)\n/* harmony export */ });\nfunction isNumber(subject) {\n  return typeof subject === 'number';\n}\nfunction isString(subject) {\n  return typeof subject === 'string';\n}\nfunction isBoolean(subject) {\n  return typeof subject === 'boolean';\n}\nfunction isObject(subject) {\n  return Object.prototype.toString.call(subject) === '[object Object]';\n}\nfunction mathAbs(n) {\n  return Math.abs(n);\n}\nfunction mathSign(n) {\n  return Math.sign(n);\n}\nfunction deltaAbs(valueB, valueA) {\n  return mathAbs(valueB - valueA);\n}\nfunction factorAbs(valueB, valueA) {\n  if (valueB === 0 || valueA === 0) return 0;\n  if (mathAbs(valueB) <= mathAbs(valueA)) return 0;\n  const diff = deltaAbs(mathAbs(valueB), mathAbs(valueA));\n  return mathAbs(diff / valueB);\n}\nfunction roundToTwoDecimals(num) {\n  return Math.round(num * 100) / 100;\n}\nfunction arrayKeys(array) {\n  return objectKeys(array).map(Number);\n}\nfunction arrayLast(array) {\n  return array[arrayLastIndex(array)];\n}\nfunction arrayLastIndex(array) {\n  return Math.max(0, array.length - 1);\n}\nfunction arrayIsLastIndex(array, index) {\n  return index === arrayLastIndex(array);\n}\nfunction arrayFromNumber(n, startAt = 0) {\n  return Array.from(Array(n), (_, i) => startAt + i);\n}\nfunction objectKeys(object) {\n  return Object.keys(object);\n}\nfunction objectsMergeDeep(objectA, objectB) {\n  return [objectA, objectB].reduce((mergedObjects, currentObject) => {\n    objectKeys(currentObject).forEach(key => {\n      const valueA = mergedObjects[key];\n      const valueB = currentObject[key];\n      const areObjects = isObject(valueA) && isObject(valueB);\n      mergedObjects[key] = areObjects ? objectsMergeDeep(valueA, valueB) : valueB;\n    });\n    return mergedObjects;\n  }, {});\n}\nfunction isMouseEvent(evt, ownerWindow) {\n  return typeof ownerWindow.MouseEvent !== 'undefined' && evt instanceof ownerWindow.MouseEvent;\n}\n\nfunction Alignment(align, viewSize) {\n  const predefined = {\n    start,\n    center,\n    end\n  };\n  function start() {\n    return 0;\n  }\n  function center(n) {\n    return end(n) / 2;\n  }\n  function end(n) {\n    return viewSize - n;\n  }\n  function measure(n, index) {\n    if (isString(align)) return predefined[align](n);\n    return align(viewSize, n, index);\n  }\n  const self = {\n    measure\n  };\n  return self;\n}\n\nfunction EventStore() {\n  let listeners = [];\n  function add(node, type, handler, options = {\n    passive: true\n  }) {\n    let removeListener;\n    if ('addEventListener' in node) {\n      node.addEventListener(type, handler, options);\n      removeListener = () => node.removeEventListener(type, handler, options);\n    } else {\n      const legacyMediaQueryList = node;\n      legacyMediaQueryList.addListener(handler);\n      removeListener = () => legacyMediaQueryList.removeListener(handler);\n    }\n    listeners.push(removeListener);\n    return self;\n  }\n  function clear() {\n    listeners = listeners.filter(remove => remove());\n  }\n  const self = {\n    add,\n    clear\n  };\n  return self;\n}\n\nfunction Animations(ownerDocument, ownerWindow, update, render) {\n  const documentVisibleHandler = EventStore();\n  const fixedTimeStep = 1000 / 60;\n  let lastTimeStamp = null;\n  let accumulatedTime = 0;\n  let animationId = 0;\n  function init() {\n    documentVisibleHandler.add(ownerDocument, 'visibilitychange', () => {\n      if (ownerDocument.hidden) reset();\n    });\n  }\n  function destroy() {\n    stop();\n    documentVisibleHandler.clear();\n  }\n  function animate(timeStamp) {\n    if (!animationId) return;\n    if (!lastTimeStamp) {\n      lastTimeStamp = timeStamp;\n      update();\n      update();\n    }\n    const timeElapsed = timeStamp - lastTimeStamp;\n    lastTimeStamp = timeStamp;\n    accumulatedTime += timeElapsed;\n    while (accumulatedTime >= fixedTimeStep) {\n      update();\n      accumulatedTime -= fixedTimeStep;\n    }\n    const alpha = accumulatedTime / fixedTimeStep;\n    render(alpha);\n    if (animationId) {\n      animationId = ownerWindow.requestAnimationFrame(animate);\n    }\n  }\n  function start() {\n    if (animationId) return;\n    animationId = ownerWindow.requestAnimationFrame(animate);\n  }\n  function stop() {\n    ownerWindow.cancelAnimationFrame(animationId);\n    lastTimeStamp = null;\n    accumulatedTime = 0;\n    animationId = 0;\n  }\n  function reset() {\n    lastTimeStamp = null;\n    accumulatedTime = 0;\n  }\n  const self = {\n    init,\n    destroy,\n    start,\n    stop,\n    update,\n    render\n  };\n  return self;\n}\n\nfunction Axis(axis, contentDirection) {\n  const isRightToLeft = contentDirection === 'rtl';\n  const isVertical = axis === 'y';\n  const scroll = isVertical ? 'y' : 'x';\n  const cross = isVertical ? 'x' : 'y';\n  const sign = !isVertical && isRightToLeft ? -1 : 1;\n  const startEdge = getStartEdge();\n  const endEdge = getEndEdge();\n  function measureSize(nodeRect) {\n    const {\n      height,\n      width\n    } = nodeRect;\n    return isVertical ? height : width;\n  }\n  function getStartEdge() {\n    if (isVertical) return 'top';\n    return isRightToLeft ? 'right' : 'left';\n  }\n  function getEndEdge() {\n    if (isVertical) return 'bottom';\n    return isRightToLeft ? 'left' : 'right';\n  }\n  function direction(n) {\n    return n * sign;\n  }\n  const self = {\n    scroll,\n    cross,\n    startEdge,\n    endEdge,\n    measureSize,\n    direction\n  };\n  return self;\n}\n\nfunction Limit(min = 0, max = 0) {\n  const length = mathAbs(min - max);\n  function reachedMin(n) {\n    return n < min;\n  }\n  function reachedMax(n) {\n    return n > max;\n  }\n  function reachedAny(n) {\n    return reachedMin(n) || reachedMax(n);\n  }\n  function constrain(n) {\n    if (!reachedAny(n)) return n;\n    return reachedMin(n) ? min : max;\n  }\n  function removeOffset(n) {\n    if (!length) return n;\n    return n - length * Math.ceil((n - max) / length);\n  }\n  const self = {\n    length,\n    max,\n    min,\n    constrain,\n    reachedAny,\n    reachedMax,\n    reachedMin,\n    removeOffset\n  };\n  return self;\n}\n\nfunction Counter(max, start, loop) {\n  const {\n    constrain\n  } = Limit(0, max);\n  const loopEnd = max + 1;\n  let counter = withinLimit(start);\n  function withinLimit(n) {\n    return !loop ? constrain(n) : mathAbs((loopEnd + n) % loopEnd);\n  }\n  function get() {\n    return counter;\n  }\n  function set(n) {\n    counter = withinLimit(n);\n    return self;\n  }\n  function add(n) {\n    return clone().set(get() + n);\n  }\n  function clone() {\n    return Counter(max, get(), loop);\n  }\n  const self = {\n    get,\n    set,\n    add,\n    clone\n  };\n  return self;\n}\n\nfunction DragHandler(axis, rootNode, ownerDocument, ownerWindow, target, dragTracker, location, animation, scrollTo, scrollBody, scrollTarget, index, eventHandler, percentOfView, dragFree, dragThreshold, skipSnaps, baseFriction, watchDrag) {\n  const {\n    cross: crossAxis,\n    direction\n  } = axis;\n  const focusNodes = ['INPUT', 'SELECT', 'TEXTAREA'];\n  const nonPassiveEvent = {\n    passive: false\n  };\n  const initEvents = EventStore();\n  const dragEvents = EventStore();\n  const goToNextThreshold = Limit(50, 225).constrain(percentOfView.measure(20));\n  const snapForceBoost = {\n    mouse: 300,\n    touch: 400\n  };\n  const freeForceBoost = {\n    mouse: 500,\n    touch: 600\n  };\n  const baseSpeed = dragFree ? 43 : 25;\n  let isMoving = false;\n  let startScroll = 0;\n  let startCross = 0;\n  let pointerIsDown = false;\n  let preventScroll = false;\n  let preventClick = false;\n  let isMouse = false;\n  function init(emblaApi) {\n    if (!watchDrag) return;\n    function downIfAllowed(evt) {\n      if (isBoolean(watchDrag) || watchDrag(emblaApi, evt)) down(evt);\n    }\n    const node = rootNode;\n    initEvents.add(node, 'dragstart', evt => evt.preventDefault(), nonPassiveEvent).add(node, 'touchmove', () => undefined, nonPassiveEvent).add(node, 'touchend', () => undefined).add(node, 'touchstart', downIfAllowed).add(node, 'mousedown', downIfAllowed).add(node, 'touchcancel', up).add(node, 'contextmenu', up).add(node, 'click', click, true);\n  }\n  function destroy() {\n    initEvents.clear();\n    dragEvents.clear();\n  }\n  function addDragEvents() {\n    const node = isMouse ? ownerDocument : rootNode;\n    dragEvents.add(node, 'touchmove', move, nonPassiveEvent).add(node, 'touchend', up).add(node, 'mousemove', move, nonPassiveEvent).add(node, 'mouseup', up);\n  }\n  function isFocusNode(node) {\n    const nodeName = node.nodeName || '';\n    return focusNodes.includes(nodeName);\n  }\n  function forceBoost() {\n    const boost = dragFree ? freeForceBoost : snapForceBoost;\n    const type = isMouse ? 'mouse' : 'touch';\n    return boost[type];\n  }\n  function allowedForce(force, targetChanged) {\n    const next = index.add(mathSign(force) * -1);\n    const baseForce = scrollTarget.byDistance(force, !dragFree).distance;\n    if (dragFree || mathAbs(force) < goToNextThreshold) return baseForce;\n    if (skipSnaps && targetChanged) return baseForce * 0.5;\n    return scrollTarget.byIndex(next.get(), 0).distance;\n  }\n  function down(evt) {\n    const isMouseEvt = isMouseEvent(evt, ownerWindow);\n    isMouse = isMouseEvt;\n    preventClick = dragFree && isMouseEvt && !evt.buttons && isMoving;\n    isMoving = deltaAbs(target.get(), location.get()) >= 2;\n    if (isMouseEvt && evt.button !== 0) return;\n    if (isFocusNode(evt.target)) return;\n    pointerIsDown = true;\n    dragTracker.pointerDown(evt);\n    scrollBody.useFriction(0).useDuration(0);\n    target.set(location);\n    addDragEvents();\n    startScroll = dragTracker.readPoint(evt);\n    startCross = dragTracker.readPoint(evt, crossAxis);\n    eventHandler.emit('pointerDown');\n  }\n  function move(evt) {\n    const isTouchEvt = !isMouseEvent(evt, ownerWindow);\n    if (isTouchEvt && evt.touches.length >= 2) return up(evt);\n    const lastScroll = dragTracker.readPoint(evt);\n    const lastCross = dragTracker.readPoint(evt, crossAxis);\n    const diffScroll = deltaAbs(lastScroll, startScroll);\n    const diffCross = deltaAbs(lastCross, startCross);\n    if (!preventScroll && !isMouse) {\n      if (!evt.cancelable) return up(evt);\n      preventScroll = diffScroll > diffCross;\n      if (!preventScroll) return up(evt);\n    }\n    const diff = dragTracker.pointerMove(evt);\n    if (diffScroll > dragThreshold) preventClick = true;\n    scrollBody.useFriction(0.3).useDuration(0.75);\n    animation.start();\n    target.add(direction(diff));\n    evt.preventDefault();\n  }\n  function up(evt) {\n    const currentLocation = scrollTarget.byDistance(0, false);\n    const targetChanged = currentLocation.index !== index.get();\n    const rawForce = dragTracker.pointerUp(evt) * forceBoost();\n    const force = allowedForce(direction(rawForce), targetChanged);\n    const forceFactor = factorAbs(rawForce, force);\n    const speed = baseSpeed - 10 * forceFactor;\n    const friction = baseFriction + forceFactor / 50;\n    preventScroll = false;\n    pointerIsDown = false;\n    dragEvents.clear();\n    scrollBody.useDuration(speed).useFriction(friction);\n    scrollTo.distance(force, !dragFree);\n    isMouse = false;\n    eventHandler.emit('pointerUp');\n  }\n  function click(evt) {\n    if (preventClick) {\n      evt.stopPropagation();\n      evt.preventDefault();\n      preventClick = false;\n    }\n  }\n  function pointerDown() {\n    return pointerIsDown;\n  }\n  const self = {\n    init,\n    destroy,\n    pointerDown\n  };\n  return self;\n}\n\nfunction DragTracker(axis, ownerWindow) {\n  const logInterval = 170;\n  let startEvent;\n  let lastEvent;\n  function readTime(evt) {\n    return evt.timeStamp;\n  }\n  function readPoint(evt, evtAxis) {\n    const property = evtAxis || axis.scroll;\n    const coord = `client${property === 'x' ? 'X' : 'Y'}`;\n    return (isMouseEvent(evt, ownerWindow) ? evt : evt.touches[0])[coord];\n  }\n  function pointerDown(evt) {\n    startEvent = evt;\n    lastEvent = evt;\n    return readPoint(evt);\n  }\n  function pointerMove(evt) {\n    const diff = readPoint(evt) - readPoint(lastEvent);\n    const expired = readTime(evt) - readTime(startEvent) > logInterval;\n    lastEvent = evt;\n    if (expired) startEvent = evt;\n    return diff;\n  }\n  function pointerUp(evt) {\n    if (!startEvent || !lastEvent) return 0;\n    const diffDrag = readPoint(lastEvent) - readPoint(startEvent);\n    const diffTime = readTime(evt) - readTime(startEvent);\n    const expired = readTime(evt) - readTime(lastEvent) > logInterval;\n    const force = diffDrag / diffTime;\n    const isFlick = diffTime && !expired && mathAbs(force) > 0.1;\n    return isFlick ? force : 0;\n  }\n  const self = {\n    pointerDown,\n    pointerMove,\n    pointerUp,\n    readPoint\n  };\n  return self;\n}\n\nfunction NodeRects() {\n  function measure(node) {\n    const {\n      offsetTop,\n      offsetLeft,\n      offsetWidth,\n      offsetHeight\n    } = node;\n    const offset = {\n      top: offsetTop,\n      right: offsetLeft + offsetWidth,\n      bottom: offsetTop + offsetHeight,\n      left: offsetLeft,\n      width: offsetWidth,\n      height: offsetHeight\n    };\n    return offset;\n  }\n  const self = {\n    measure\n  };\n  return self;\n}\n\nfunction PercentOfView(viewSize) {\n  function measure(n) {\n    return viewSize * (n / 100);\n  }\n  const self = {\n    measure\n  };\n  return self;\n}\n\nfunction ResizeHandler(container, eventHandler, ownerWindow, slides, axis, watchResize, nodeRects) {\n  const observeNodes = [container].concat(slides);\n  let resizeObserver;\n  let containerSize;\n  let slideSizes = [];\n  let destroyed = false;\n  function readSize(node) {\n    return axis.measureSize(nodeRects.measure(node));\n  }\n  function init(emblaApi) {\n    if (!watchResize) return;\n    containerSize = readSize(container);\n    slideSizes = slides.map(readSize);\n    function defaultCallback(entries) {\n      for (const entry of entries) {\n        if (destroyed) return;\n        const isContainer = entry.target === container;\n        const slideIndex = slides.indexOf(entry.target);\n        const lastSize = isContainer ? containerSize : slideSizes[slideIndex];\n        const newSize = readSize(isContainer ? container : slides[slideIndex]);\n        const diffSize = mathAbs(newSize - lastSize);\n        if (diffSize >= 0.5) {\n          emblaApi.reInit();\n          eventHandler.emit('resize');\n          break;\n        }\n      }\n    }\n    resizeObserver = new ResizeObserver(entries => {\n      if (isBoolean(watchResize) || watchResize(emblaApi, entries)) {\n        defaultCallback(entries);\n      }\n    });\n    ownerWindow.requestAnimationFrame(() => {\n      observeNodes.forEach(node => resizeObserver.observe(node));\n    });\n  }\n  function destroy() {\n    destroyed = true;\n    if (resizeObserver) resizeObserver.disconnect();\n  }\n  const self = {\n    init,\n    destroy\n  };\n  return self;\n}\n\nfunction ScrollBody(location, offsetLocation, previousLocation, target, baseDuration, baseFriction) {\n  let scrollVelocity = 0;\n  let scrollDirection = 0;\n  let scrollDuration = baseDuration;\n  let scrollFriction = baseFriction;\n  let rawLocation = location.get();\n  let rawLocationPrevious = 0;\n  function seek() {\n    const displacement = target.get() - location.get();\n    const isInstant = !scrollDuration;\n    let scrollDistance = 0;\n    if (isInstant) {\n      scrollVelocity = 0;\n      previousLocation.set(target);\n      location.set(target);\n      scrollDistance = displacement;\n    } else {\n      previousLocation.set(location);\n      scrollVelocity += displacement / scrollDuration;\n      scrollVelocity *= scrollFriction;\n      rawLocation += scrollVelocity;\n      location.add(scrollVelocity);\n      scrollDistance = rawLocation - rawLocationPrevious;\n    }\n    scrollDirection = mathSign(scrollDistance);\n    rawLocationPrevious = rawLocation;\n    return self;\n  }\n  function settled() {\n    const diff = target.get() - offsetLocation.get();\n    return mathAbs(diff) < 0.001;\n  }\n  function duration() {\n    return scrollDuration;\n  }\n  function direction() {\n    return scrollDirection;\n  }\n  function velocity() {\n    return scrollVelocity;\n  }\n  function useBaseDuration() {\n    return useDuration(baseDuration);\n  }\n  function useBaseFriction() {\n    return useFriction(baseFriction);\n  }\n  function useDuration(n) {\n    scrollDuration = n;\n    return self;\n  }\n  function useFriction(n) {\n    scrollFriction = n;\n    return self;\n  }\n  const self = {\n    direction,\n    duration,\n    velocity,\n    seek,\n    settled,\n    useBaseFriction,\n    useBaseDuration,\n    useFriction,\n    useDuration\n  };\n  return self;\n}\n\nfunction ScrollBounds(limit, location, target, scrollBody, percentOfView) {\n  const pullBackThreshold = percentOfView.measure(10);\n  const edgeOffsetTolerance = percentOfView.measure(50);\n  const frictionLimit = Limit(0.1, 0.99);\n  let disabled = false;\n  function shouldConstrain() {\n    if (disabled) return false;\n    if (!limit.reachedAny(target.get())) return false;\n    if (!limit.reachedAny(location.get())) return false;\n    return true;\n  }\n  function constrain(pointerDown) {\n    if (!shouldConstrain()) return;\n    const edge = limit.reachedMin(location.get()) ? 'min' : 'max';\n    const diffToEdge = mathAbs(limit[edge] - location.get());\n    const diffToTarget = target.get() - location.get();\n    const friction = frictionLimit.constrain(diffToEdge / edgeOffsetTolerance);\n    target.subtract(diffToTarget * friction);\n    if (!pointerDown && mathAbs(diffToTarget) < pullBackThreshold) {\n      target.set(limit.constrain(target.get()));\n      scrollBody.useDuration(25).useBaseFriction();\n    }\n  }\n  function toggleActive(active) {\n    disabled = !active;\n  }\n  const self = {\n    shouldConstrain,\n    constrain,\n    toggleActive\n  };\n  return self;\n}\n\nfunction ScrollContain(viewSize, contentSize, snapsAligned, containScroll, pixelTolerance) {\n  const scrollBounds = Limit(-contentSize + viewSize, 0);\n  const snapsBounded = measureBounded();\n  const scrollContainLimit = findScrollContainLimit();\n  const snapsContained = measureContained();\n  function usePixelTolerance(bound, snap) {\n    return deltaAbs(bound, snap) <= 1;\n  }\n  function findScrollContainLimit() {\n    const startSnap = snapsBounded[0];\n    const endSnap = arrayLast(snapsBounded);\n    const min = snapsBounded.lastIndexOf(startSnap);\n    const max = snapsBounded.indexOf(endSnap) + 1;\n    return Limit(min, max);\n  }\n  function measureBounded() {\n    return snapsAligned.map((snapAligned, index) => {\n      const {\n        min,\n        max\n      } = scrollBounds;\n      const snap = scrollBounds.constrain(snapAligned);\n      const isFirst = !index;\n      const isLast = arrayIsLastIndex(snapsAligned, index);\n      if (isFirst) return max;\n      if (isLast) return min;\n      if (usePixelTolerance(min, snap)) return min;\n      if (usePixelTolerance(max, snap)) return max;\n      return snap;\n    }).map(scrollBound => parseFloat(scrollBound.toFixed(3)));\n  }\n  function measureContained() {\n    if (contentSize <= viewSize + pixelTolerance) return [scrollBounds.max];\n    if (containScroll === 'keepSnaps') return snapsBounded;\n    const {\n      min,\n      max\n    } = scrollContainLimit;\n    return snapsBounded.slice(min, max);\n  }\n  const self = {\n    snapsContained,\n    scrollContainLimit\n  };\n  return self;\n}\n\nfunction ScrollLimit(contentSize, scrollSnaps, loop) {\n  const max = scrollSnaps[0];\n  const min = loop ? max - contentSize : arrayLast(scrollSnaps);\n  const limit = Limit(min, max);\n  const self = {\n    limit\n  };\n  return self;\n}\n\nfunction ScrollLooper(contentSize, limit, location, vectors) {\n  const jointSafety = 0.1;\n  const min = limit.min + jointSafety;\n  const max = limit.max + jointSafety;\n  const {\n    reachedMin,\n    reachedMax\n  } = Limit(min, max);\n  function shouldLoop(direction) {\n    if (direction === 1) return reachedMax(location.get());\n    if (direction === -1) return reachedMin(location.get());\n    return false;\n  }\n  function loop(direction) {\n    if (!shouldLoop(direction)) return;\n    const loopDistance = contentSize * (direction * -1);\n    vectors.forEach(v => v.add(loopDistance));\n  }\n  const self = {\n    loop\n  };\n  return self;\n}\n\nfunction ScrollProgress(limit) {\n  const {\n    max,\n    length\n  } = limit;\n  function get(n) {\n    const currentLocation = n - max;\n    return length ? currentLocation / -length : 0;\n  }\n  const self = {\n    get\n  };\n  return self;\n}\n\nfunction ScrollSnaps(axis, alignment, containerRect, slideRects, slidesToScroll) {\n  const {\n    startEdge,\n    endEdge\n  } = axis;\n  const {\n    groupSlides\n  } = slidesToScroll;\n  const alignments = measureSizes().map(alignment.measure);\n  const snaps = measureUnaligned();\n  const snapsAligned = measureAligned();\n  function measureSizes() {\n    return groupSlides(slideRects).map(rects => arrayLast(rects)[endEdge] - rects[0][startEdge]).map(mathAbs);\n  }\n  function measureUnaligned() {\n    return slideRects.map(rect => containerRect[startEdge] - rect[startEdge]).map(snap => -mathAbs(snap));\n  }\n  function measureAligned() {\n    return groupSlides(snaps).map(g => g[0]).map((snap, index) => snap + alignments[index]);\n  }\n  const self = {\n    snaps,\n    snapsAligned\n  };\n  return self;\n}\n\nfunction SlideRegistry(containSnaps, containScroll, scrollSnaps, scrollContainLimit, slidesToScroll, slideIndexes) {\n  const {\n    groupSlides\n  } = slidesToScroll;\n  const {\n    min,\n    max\n  } = scrollContainLimit;\n  const slideRegistry = createSlideRegistry();\n  function createSlideRegistry() {\n    const groupedSlideIndexes = groupSlides(slideIndexes);\n    const doNotContain = !containSnaps || containScroll === 'keepSnaps';\n    if (scrollSnaps.length === 1) return [slideIndexes];\n    if (doNotContain) return groupedSlideIndexes;\n    return groupedSlideIndexes.slice(min, max).map((group, index, groups) => {\n      const isFirst = !index;\n      const isLast = arrayIsLastIndex(groups, index);\n      if (isFirst) {\n        const range = arrayLast(groups[0]) + 1;\n        return arrayFromNumber(range);\n      }\n      if (isLast) {\n        const range = arrayLastIndex(slideIndexes) - arrayLast(groups)[0] + 1;\n        return arrayFromNumber(range, arrayLast(groups)[0]);\n      }\n      return group;\n    });\n  }\n  const self = {\n    slideRegistry\n  };\n  return self;\n}\n\nfunction ScrollTarget(loop, scrollSnaps, contentSize, limit, targetVector) {\n  const {\n    reachedAny,\n    removeOffset,\n    constrain\n  } = limit;\n  function minDistance(distances) {\n    return distances.concat().sort((a, b) => mathAbs(a) - mathAbs(b))[0];\n  }\n  function findTargetSnap(target) {\n    const distance = loop ? removeOffset(target) : constrain(target);\n    const ascDiffsToSnaps = scrollSnaps.map((snap, index) => ({\n      diff: shortcut(snap - distance, 0),\n      index\n    })).sort((d1, d2) => mathAbs(d1.diff) - mathAbs(d2.diff));\n    const {\n      index\n    } = ascDiffsToSnaps[0];\n    return {\n      index,\n      distance\n    };\n  }\n  function shortcut(target, direction) {\n    const targets = [target, target + contentSize, target - contentSize];\n    if (!loop) return target;\n    if (!direction) return minDistance(targets);\n    const matchingTargets = targets.filter(t => mathSign(t) === direction);\n    if (matchingTargets.length) return minDistance(matchingTargets);\n    return arrayLast(targets) - contentSize;\n  }\n  function byIndex(index, direction) {\n    const diffToSnap = scrollSnaps[index] - targetVector.get();\n    const distance = shortcut(diffToSnap, direction);\n    return {\n      index,\n      distance\n    };\n  }\n  function byDistance(distance, snap) {\n    const target = targetVector.get() + distance;\n    const {\n      index,\n      distance: targetSnapDistance\n    } = findTargetSnap(target);\n    const reachedBound = !loop && reachedAny(target);\n    if (!snap || reachedBound) return {\n      index,\n      distance\n    };\n    const diffToSnap = scrollSnaps[index] - targetSnapDistance;\n    const snapDistance = distance + shortcut(diffToSnap, 0);\n    return {\n      index,\n      distance: snapDistance\n    };\n  }\n  const self = {\n    byDistance,\n    byIndex,\n    shortcut\n  };\n  return self;\n}\n\nfunction ScrollTo(animation, indexCurrent, indexPrevious, scrollBody, scrollTarget, targetVector, eventHandler) {\n  function scrollTo(target) {\n    const distanceDiff = target.distance;\n    const indexDiff = target.index !== indexCurrent.get();\n    targetVector.add(distanceDiff);\n    if (distanceDiff) {\n      if (scrollBody.duration()) {\n        animation.start();\n      } else {\n        animation.update();\n        animation.render(1);\n        animation.update();\n      }\n    }\n    if (indexDiff) {\n      indexPrevious.set(indexCurrent.get());\n      indexCurrent.set(target.index);\n      eventHandler.emit('select');\n    }\n  }\n  function distance(n, snap) {\n    const target = scrollTarget.byDistance(n, snap);\n    scrollTo(target);\n  }\n  function index(n, direction) {\n    const targetIndex = indexCurrent.clone().set(n);\n    const target = scrollTarget.byIndex(targetIndex.get(), direction);\n    scrollTo(target);\n  }\n  const self = {\n    distance,\n    index\n  };\n  return self;\n}\n\nfunction SlideFocus(root, slides, slideRegistry, scrollTo, scrollBody, eventStore, eventHandler, watchFocus) {\n  const focusListenerOptions = {\n    passive: true,\n    capture: true\n  };\n  let lastTabPressTime = 0;\n  function init(emblaApi) {\n    if (!watchFocus) return;\n    function defaultCallback(index) {\n      const nowTime = new Date().getTime();\n      const diffTime = nowTime - lastTabPressTime;\n      if (diffTime > 10) return;\n      eventHandler.emit('slideFocusStart');\n      root.scrollLeft = 0;\n      const group = slideRegistry.findIndex(group => group.includes(index));\n      if (!isNumber(group)) return;\n      scrollBody.useDuration(0);\n      scrollTo.index(group, 0);\n      eventHandler.emit('slideFocus');\n    }\n    eventStore.add(document, 'keydown', registerTabPress, false);\n    slides.forEach((slide, slideIndex) => {\n      eventStore.add(slide, 'focus', evt => {\n        if (isBoolean(watchFocus) || watchFocus(emblaApi, evt)) {\n          defaultCallback(slideIndex);\n        }\n      }, focusListenerOptions);\n    });\n  }\n  function registerTabPress(event) {\n    if (event.code === 'Tab') lastTabPressTime = new Date().getTime();\n  }\n  const self = {\n    init\n  };\n  return self;\n}\n\nfunction Vector1D(initialValue) {\n  let value = initialValue;\n  function get() {\n    return value;\n  }\n  function set(n) {\n    value = normalizeInput(n);\n  }\n  function add(n) {\n    value += normalizeInput(n);\n  }\n  function subtract(n) {\n    value -= normalizeInput(n);\n  }\n  function normalizeInput(n) {\n    return isNumber(n) ? n : n.get();\n  }\n  const self = {\n    get,\n    set,\n    add,\n    subtract\n  };\n  return self;\n}\n\nfunction Translate(axis, container) {\n  const translate = axis.scroll === 'x' ? x : y;\n  const containerStyle = container.style;\n  let previousTarget = null;\n  let disabled = false;\n  function x(n) {\n    return `translate3d(${n}px,0px,0px)`;\n  }\n  function y(n) {\n    return `translate3d(0px,${n}px,0px)`;\n  }\n  function to(target) {\n    if (disabled) return;\n    const newTarget = roundToTwoDecimals(axis.direction(target));\n    if (newTarget === previousTarget) return;\n    containerStyle.transform = translate(newTarget);\n    previousTarget = newTarget;\n  }\n  function toggleActive(active) {\n    disabled = !active;\n  }\n  function clear() {\n    if (disabled) return;\n    containerStyle.transform = '';\n    if (!container.getAttribute('style')) container.removeAttribute('style');\n  }\n  const self = {\n    clear,\n    to,\n    toggleActive\n  };\n  return self;\n}\n\nfunction SlideLooper(axis, viewSize, contentSize, slideSizes, slideSizesWithGaps, snaps, scrollSnaps, location, slides) {\n  const roundingSafety = 0.5;\n  const ascItems = arrayKeys(slideSizesWithGaps);\n  const descItems = arrayKeys(slideSizesWithGaps).reverse();\n  const loopPoints = startPoints().concat(endPoints());\n  function removeSlideSizes(indexes, from) {\n    return indexes.reduce((a, i) => {\n      return a - slideSizesWithGaps[i];\n    }, from);\n  }\n  function slidesInGap(indexes, gap) {\n    return indexes.reduce((a, i) => {\n      const remainingGap = removeSlideSizes(a, gap);\n      return remainingGap > 0 ? a.concat([i]) : a;\n    }, []);\n  }\n  function findSlideBounds(offset) {\n    return snaps.map((snap, index) => ({\n      start: snap - slideSizes[index] + roundingSafety + offset,\n      end: snap + viewSize - roundingSafety + offset\n    }));\n  }\n  function findLoopPoints(indexes, offset, isEndEdge) {\n    const slideBounds = findSlideBounds(offset);\n    return indexes.map(index => {\n      const initial = isEndEdge ? 0 : -contentSize;\n      const altered = isEndEdge ? contentSize : 0;\n      const boundEdge = isEndEdge ? 'end' : 'start';\n      const loopPoint = slideBounds[index][boundEdge];\n      return {\n        index,\n        loopPoint,\n        slideLocation: Vector1D(-1),\n        translate: Translate(axis, slides[index]),\n        target: () => location.get() > loopPoint ? initial : altered\n      };\n    });\n  }\n  function startPoints() {\n    const gap = scrollSnaps[0];\n    const indexes = slidesInGap(descItems, gap);\n    return findLoopPoints(indexes, contentSize, false);\n  }\n  function endPoints() {\n    const gap = viewSize - scrollSnaps[0] - 1;\n    const indexes = slidesInGap(ascItems, gap);\n    return findLoopPoints(indexes, -contentSize, true);\n  }\n  function canLoop() {\n    return loopPoints.every(({\n      index\n    }) => {\n      const otherIndexes = ascItems.filter(i => i !== index);\n      return removeSlideSizes(otherIndexes, viewSize) <= 0.1;\n    });\n  }\n  function loop() {\n    loopPoints.forEach(loopPoint => {\n      const {\n        target,\n        translate,\n        slideLocation\n      } = loopPoint;\n      const shiftLocation = target();\n      if (shiftLocation === slideLocation.get()) return;\n      translate.to(shiftLocation);\n      slideLocation.set(shiftLocation);\n    });\n  }\n  function clear() {\n    loopPoints.forEach(loopPoint => loopPoint.translate.clear());\n  }\n  const self = {\n    canLoop,\n    clear,\n    loop,\n    loopPoints\n  };\n  return self;\n}\n\nfunction SlidesHandler(container, eventHandler, watchSlides) {\n  let mutationObserver;\n  let destroyed = false;\n  function init(emblaApi) {\n    if (!watchSlides) return;\n    function defaultCallback(mutations) {\n      for (const mutation of mutations) {\n        if (mutation.type === 'childList') {\n          emblaApi.reInit();\n          eventHandler.emit('slidesChanged');\n          break;\n        }\n      }\n    }\n    mutationObserver = new MutationObserver(mutations => {\n      if (destroyed) return;\n      if (isBoolean(watchSlides) || watchSlides(emblaApi, mutations)) {\n        defaultCallback(mutations);\n      }\n    });\n    mutationObserver.observe(container, {\n      childList: true\n    });\n  }\n  function destroy() {\n    if (mutationObserver) mutationObserver.disconnect();\n    destroyed = true;\n  }\n  const self = {\n    init,\n    destroy\n  };\n  return self;\n}\n\nfunction SlidesInView(container, slides, eventHandler, threshold) {\n  const intersectionEntryMap = {};\n  let inViewCache = null;\n  let notInViewCache = null;\n  let intersectionObserver;\n  let destroyed = false;\n  function init() {\n    intersectionObserver = new IntersectionObserver(entries => {\n      if (destroyed) return;\n      entries.forEach(entry => {\n        const index = slides.indexOf(entry.target);\n        intersectionEntryMap[index] = entry;\n      });\n      inViewCache = null;\n      notInViewCache = null;\n      eventHandler.emit('slidesInView');\n    }, {\n      root: container.parentElement,\n      threshold\n    });\n    slides.forEach(slide => intersectionObserver.observe(slide));\n  }\n  function destroy() {\n    if (intersectionObserver) intersectionObserver.disconnect();\n    destroyed = true;\n  }\n  function createInViewList(inView) {\n    return objectKeys(intersectionEntryMap).reduce((list, slideIndex) => {\n      const index = parseInt(slideIndex);\n      const {\n        isIntersecting\n      } = intersectionEntryMap[index];\n      const inViewMatch = inView && isIntersecting;\n      const notInViewMatch = !inView && !isIntersecting;\n      if (inViewMatch || notInViewMatch) list.push(index);\n      return list;\n    }, []);\n  }\n  function get(inView = true) {\n    if (inView && inViewCache) return inViewCache;\n    if (!inView && notInViewCache) return notInViewCache;\n    const slideIndexes = createInViewList(inView);\n    if (inView) inViewCache = slideIndexes;\n    if (!inView) notInViewCache = slideIndexes;\n    return slideIndexes;\n  }\n  const self = {\n    init,\n    destroy,\n    get\n  };\n  return self;\n}\n\nfunction SlideSizes(axis, containerRect, slideRects, slides, readEdgeGap, ownerWindow) {\n  const {\n    measureSize,\n    startEdge,\n    endEdge\n  } = axis;\n  const withEdgeGap = slideRects[0] && readEdgeGap;\n  const startGap = measureStartGap();\n  const endGap = measureEndGap();\n  const slideSizes = slideRects.map(measureSize);\n  const slideSizesWithGaps = measureWithGaps();\n  function measureStartGap() {\n    if (!withEdgeGap) return 0;\n    const slideRect = slideRects[0];\n    return mathAbs(containerRect[startEdge] - slideRect[startEdge]);\n  }\n  function measureEndGap() {\n    if (!withEdgeGap) return 0;\n    const style = ownerWindow.getComputedStyle(arrayLast(slides));\n    return parseFloat(style.getPropertyValue(`margin-${endEdge}`));\n  }\n  function measureWithGaps() {\n    return slideRects.map((rect, index, rects) => {\n      const isFirst = !index;\n      const isLast = arrayIsLastIndex(rects, index);\n      if (isFirst) return slideSizes[index] + startGap;\n      if (isLast) return slideSizes[index] + endGap;\n      return rects[index + 1][startEdge] - rect[startEdge];\n    }).map(mathAbs);\n  }\n  const self = {\n    slideSizes,\n    slideSizesWithGaps,\n    startGap,\n    endGap\n  };\n  return self;\n}\n\nfunction SlidesToScroll(axis, viewSize, slidesToScroll, loop, containerRect, slideRects, startGap, endGap, pixelTolerance) {\n  const {\n    startEdge,\n    endEdge,\n    direction\n  } = axis;\n  const groupByNumber = isNumber(slidesToScroll);\n  function byNumber(array, groupSize) {\n    return arrayKeys(array).filter(i => i % groupSize === 0).map(i => array.slice(i, i + groupSize));\n  }\n  function bySize(array) {\n    if (!array.length) return [];\n    return arrayKeys(array).reduce((groups, rectB, index) => {\n      const rectA = arrayLast(groups) || 0;\n      const isFirst = rectA === 0;\n      const isLast = rectB === arrayLastIndex(array);\n      const edgeA = containerRect[startEdge] - slideRects[rectA][startEdge];\n      const edgeB = containerRect[startEdge] - slideRects[rectB][endEdge];\n      const gapA = !loop && isFirst ? direction(startGap) : 0;\n      const gapB = !loop && isLast ? direction(endGap) : 0;\n      const chunkSize = mathAbs(edgeB - gapB - (edgeA + gapA));\n      if (index && chunkSize > viewSize + pixelTolerance) groups.push(rectB);\n      if (isLast) groups.push(array.length);\n      return groups;\n    }, []).map((currentSize, index, groups) => {\n      const previousSize = Math.max(groups[index - 1] || 0);\n      return array.slice(previousSize, currentSize);\n    });\n  }\n  function groupSlides(array) {\n    return groupByNumber ? byNumber(array, slidesToScroll) : bySize(array);\n  }\n  const self = {\n    groupSlides\n  };\n  return self;\n}\n\nfunction Engine(root, container, slides, ownerDocument, ownerWindow, options, eventHandler) {\n  // Options\n  const {\n    align,\n    axis: scrollAxis,\n    direction,\n    startIndex,\n    loop,\n    duration,\n    dragFree,\n    dragThreshold,\n    inViewThreshold,\n    slidesToScroll: groupSlides,\n    skipSnaps,\n    containScroll,\n    watchResize,\n    watchSlides,\n    watchDrag,\n    watchFocus\n  } = options;\n  // Measurements\n  const pixelTolerance = 2;\n  const nodeRects = NodeRects();\n  const containerRect = nodeRects.measure(container);\n  const slideRects = slides.map(nodeRects.measure);\n  const axis = Axis(scrollAxis, direction);\n  const viewSize = axis.measureSize(containerRect);\n  const percentOfView = PercentOfView(viewSize);\n  const alignment = Alignment(align, viewSize);\n  const containSnaps = !loop && !!containScroll;\n  const readEdgeGap = loop || !!containScroll;\n  const {\n    slideSizes,\n    slideSizesWithGaps,\n    startGap,\n    endGap\n  } = SlideSizes(axis, containerRect, slideRects, slides, readEdgeGap, ownerWindow);\n  const slidesToScroll = SlidesToScroll(axis, viewSize, groupSlides, loop, containerRect, slideRects, startGap, endGap, pixelTolerance);\n  const {\n    snaps,\n    snapsAligned\n  } = ScrollSnaps(axis, alignment, containerRect, slideRects, slidesToScroll);\n  const contentSize = -arrayLast(snaps) + arrayLast(slideSizesWithGaps);\n  const {\n    snapsContained,\n    scrollContainLimit\n  } = ScrollContain(viewSize, contentSize, snapsAligned, containScroll, pixelTolerance);\n  const scrollSnaps = containSnaps ? snapsContained : snapsAligned;\n  const {\n    limit\n  } = ScrollLimit(contentSize, scrollSnaps, loop);\n  // Indexes\n  const index = Counter(arrayLastIndex(scrollSnaps), startIndex, loop);\n  const indexPrevious = index.clone();\n  const slideIndexes = arrayKeys(slides);\n  // Animation\n  const update = ({\n    dragHandler,\n    scrollBody,\n    scrollBounds,\n    options: {\n      loop\n    }\n  }) => {\n    if (!loop) scrollBounds.constrain(dragHandler.pointerDown());\n    scrollBody.seek();\n  };\n  const render = ({\n    scrollBody,\n    translate,\n    location,\n    offsetLocation,\n    previousLocation,\n    scrollLooper,\n    slideLooper,\n    dragHandler,\n    animation,\n    eventHandler,\n    scrollBounds,\n    options: {\n      loop\n    }\n  }, alpha) => {\n    const shouldSettle = scrollBody.settled();\n    const withinBounds = !scrollBounds.shouldConstrain();\n    const hasSettled = loop ? shouldSettle : shouldSettle && withinBounds;\n    const hasSettledAndIdle = hasSettled && !dragHandler.pointerDown();\n    if (hasSettledAndIdle) animation.stop();\n    const interpolatedLocation = location.get() * alpha + previousLocation.get() * (1 - alpha);\n    offsetLocation.set(interpolatedLocation);\n    if (loop) {\n      scrollLooper.loop(scrollBody.direction());\n      slideLooper.loop();\n    }\n    translate.to(offsetLocation.get());\n    if (hasSettledAndIdle) eventHandler.emit('settle');\n    if (!hasSettled) eventHandler.emit('scroll');\n  };\n  const animation = Animations(ownerDocument, ownerWindow, () => update(engine), alpha => render(engine, alpha));\n  // Shared\n  const friction = 0.68;\n  const startLocation = scrollSnaps[index.get()];\n  const location = Vector1D(startLocation);\n  const previousLocation = Vector1D(startLocation);\n  const offsetLocation = Vector1D(startLocation);\n  const target = Vector1D(startLocation);\n  const scrollBody = ScrollBody(location, offsetLocation, previousLocation, target, duration, friction);\n  const scrollTarget = ScrollTarget(loop, scrollSnaps, contentSize, limit, target);\n  const scrollTo = ScrollTo(animation, index, indexPrevious, scrollBody, scrollTarget, target, eventHandler);\n  const scrollProgress = ScrollProgress(limit);\n  const eventStore = EventStore();\n  const slidesInView = SlidesInView(container, slides, eventHandler, inViewThreshold);\n  const {\n    slideRegistry\n  } = SlideRegistry(containSnaps, containScroll, scrollSnaps, scrollContainLimit, slidesToScroll, slideIndexes);\n  const slideFocus = SlideFocus(root, slides, slideRegistry, scrollTo, scrollBody, eventStore, eventHandler, watchFocus);\n  // Engine\n  const engine = {\n    ownerDocument,\n    ownerWindow,\n    eventHandler,\n    containerRect,\n    slideRects,\n    animation,\n    axis,\n    dragHandler: DragHandler(axis, root, ownerDocument, ownerWindow, target, DragTracker(axis, ownerWindow), location, animation, scrollTo, scrollBody, scrollTarget, index, eventHandler, percentOfView, dragFree, dragThreshold, skipSnaps, friction, watchDrag),\n    eventStore,\n    percentOfView,\n    index,\n    indexPrevious,\n    limit,\n    location,\n    offsetLocation,\n    previousLocation,\n    options,\n    resizeHandler: ResizeHandler(container, eventHandler, ownerWindow, slides, axis, watchResize, nodeRects),\n    scrollBody,\n    scrollBounds: ScrollBounds(limit, offsetLocation, target, scrollBody, percentOfView),\n    scrollLooper: ScrollLooper(contentSize, limit, offsetLocation, [location, offsetLocation, previousLocation, target]),\n    scrollProgress,\n    scrollSnapList: scrollSnaps.map(scrollProgress.get),\n    scrollSnaps,\n    scrollTarget,\n    scrollTo,\n    slideLooper: SlideLooper(axis, viewSize, contentSize, slideSizes, slideSizesWithGaps, snaps, scrollSnaps, offsetLocation, slides),\n    slideFocus,\n    slidesHandler: SlidesHandler(container, eventHandler, watchSlides),\n    slidesInView,\n    slideIndexes,\n    slideRegistry,\n    slidesToScroll,\n    target,\n    translate: Translate(axis, container)\n  };\n  return engine;\n}\n\nfunction EventHandler() {\n  let listeners = {};\n  let api;\n  function init(emblaApi) {\n    api = emblaApi;\n  }\n  function getListeners(evt) {\n    return listeners[evt] || [];\n  }\n  function emit(evt) {\n    getListeners(evt).forEach(e => e(api, evt));\n    return self;\n  }\n  function on(evt, cb) {\n    listeners[evt] = getListeners(evt).concat([cb]);\n    return self;\n  }\n  function off(evt, cb) {\n    listeners[evt] = getListeners(evt).filter(e => e !== cb);\n    return self;\n  }\n  function clear() {\n    listeners = {};\n  }\n  const self = {\n    init,\n    emit,\n    off,\n    on,\n    clear\n  };\n  return self;\n}\n\nconst defaultOptions = {\n  align: 'center',\n  axis: 'x',\n  container: null,\n  slides: null,\n  containScroll: 'trimSnaps',\n  direction: 'ltr',\n  slidesToScroll: 1,\n  inViewThreshold: 0,\n  breakpoints: {},\n  dragFree: false,\n  dragThreshold: 10,\n  loop: false,\n  skipSnaps: false,\n  duration: 25,\n  startIndex: 0,\n  active: true,\n  watchDrag: true,\n  watchResize: true,\n  watchSlides: true,\n  watchFocus: true\n};\n\nfunction OptionsHandler(ownerWindow) {\n  function mergeOptions(optionsA, optionsB) {\n    return objectsMergeDeep(optionsA, optionsB || {});\n  }\n  function optionsAtMedia(options) {\n    const optionsAtMedia = options.breakpoints || {};\n    const matchedMediaOptions = objectKeys(optionsAtMedia).filter(media => ownerWindow.matchMedia(media).matches).map(media => optionsAtMedia[media]).reduce((a, mediaOption) => mergeOptions(a, mediaOption), {});\n    return mergeOptions(options, matchedMediaOptions);\n  }\n  function optionsMediaQueries(optionsList) {\n    return optionsList.map(options => objectKeys(options.breakpoints || {})).reduce((acc, mediaQueries) => acc.concat(mediaQueries), []).map(ownerWindow.matchMedia);\n  }\n  const self = {\n    mergeOptions,\n    optionsAtMedia,\n    optionsMediaQueries\n  };\n  return self;\n}\n\nfunction PluginsHandler(optionsHandler) {\n  let activePlugins = [];\n  function init(emblaApi, plugins) {\n    activePlugins = plugins.filter(({\n      options\n    }) => optionsHandler.optionsAtMedia(options).active !== false);\n    activePlugins.forEach(plugin => plugin.init(emblaApi, optionsHandler));\n    return plugins.reduce((map, plugin) => Object.assign(map, {\n      [plugin.name]: plugin\n    }), {});\n  }\n  function destroy() {\n    activePlugins = activePlugins.filter(plugin => plugin.destroy());\n  }\n  const self = {\n    init,\n    destroy\n  };\n  return self;\n}\n\nfunction EmblaCarousel(root, userOptions, userPlugins) {\n  const ownerDocument = root.ownerDocument;\n  const ownerWindow = ownerDocument.defaultView;\n  const optionsHandler = OptionsHandler(ownerWindow);\n  const pluginsHandler = PluginsHandler(optionsHandler);\n  const mediaHandlers = EventStore();\n  const eventHandler = EventHandler();\n  const {\n    mergeOptions,\n    optionsAtMedia,\n    optionsMediaQueries\n  } = optionsHandler;\n  const {\n    on,\n    off,\n    emit\n  } = eventHandler;\n  const reInit = reActivate;\n  let destroyed = false;\n  let engine;\n  let optionsBase = mergeOptions(defaultOptions, EmblaCarousel.globalOptions);\n  let options = mergeOptions(optionsBase);\n  let pluginList = [];\n  let pluginApis;\n  let container;\n  let slides;\n  function storeElements() {\n    const {\n      container: userContainer,\n      slides: userSlides\n    } = options;\n    const customContainer = isString(userContainer) ? root.querySelector(userContainer) : userContainer;\n    container = customContainer || root.children[0];\n    const customSlides = isString(userSlides) ? container.querySelectorAll(userSlides) : userSlides;\n    slides = [].slice.call(customSlides || container.children);\n  }\n  function createEngine(options) {\n    const engine = Engine(root, container, slides, ownerDocument, ownerWindow, options, eventHandler);\n    if (options.loop && !engine.slideLooper.canLoop()) {\n      const optionsWithoutLoop = Object.assign({}, options, {\n        loop: false\n      });\n      return createEngine(optionsWithoutLoop);\n    }\n    return engine;\n  }\n  function activate(withOptions, withPlugins) {\n    if (destroyed) return;\n    optionsBase = mergeOptions(optionsBase, withOptions);\n    options = optionsAtMedia(optionsBase);\n    pluginList = withPlugins || pluginList;\n    storeElements();\n    engine = createEngine(options);\n    optionsMediaQueries([optionsBase, ...pluginList.map(({\n      options\n    }) => options)]).forEach(query => mediaHandlers.add(query, 'change', reActivate));\n    if (!options.active) return;\n    engine.translate.to(engine.location.get());\n    engine.animation.init();\n    engine.slidesInView.init();\n    engine.slideFocus.init(self);\n    engine.eventHandler.init(self);\n    engine.resizeHandler.init(self);\n    engine.slidesHandler.init(self);\n    if (engine.options.loop) engine.slideLooper.loop();\n    if (container.offsetParent && slides.length) engine.dragHandler.init(self);\n    pluginApis = pluginsHandler.init(self, pluginList);\n  }\n  function reActivate(withOptions, withPlugins) {\n    const startIndex = selectedScrollSnap();\n    deActivate();\n    activate(mergeOptions({\n      startIndex\n    }, withOptions), withPlugins);\n    eventHandler.emit('reInit');\n  }\n  function deActivate() {\n    engine.dragHandler.destroy();\n    engine.eventStore.clear();\n    engine.translate.clear();\n    engine.slideLooper.clear();\n    engine.resizeHandler.destroy();\n    engine.slidesHandler.destroy();\n    engine.slidesInView.destroy();\n    engine.animation.destroy();\n    pluginsHandler.destroy();\n    mediaHandlers.clear();\n  }\n  function destroy() {\n    if (destroyed) return;\n    destroyed = true;\n    mediaHandlers.clear();\n    deActivate();\n    eventHandler.emit('destroy');\n    eventHandler.clear();\n  }\n  function scrollTo(index, jump, direction) {\n    if (!options.active || destroyed) return;\n    engine.scrollBody.useBaseFriction().useDuration(jump === true ? 0 : options.duration);\n    engine.scrollTo.index(index, direction || 0);\n  }\n  function scrollNext(jump) {\n    const next = engine.index.add(1).get();\n    scrollTo(next, jump, -1);\n  }\n  function scrollPrev(jump) {\n    const prev = engine.index.add(-1).get();\n    scrollTo(prev, jump, 1);\n  }\n  function canScrollNext() {\n    const next = engine.index.add(1).get();\n    return next !== selectedScrollSnap();\n  }\n  function canScrollPrev() {\n    const prev = engine.index.add(-1).get();\n    return prev !== selectedScrollSnap();\n  }\n  function scrollSnapList() {\n    return engine.scrollSnapList;\n  }\n  function scrollProgress() {\n    return engine.scrollProgress.get(engine.offsetLocation.get());\n  }\n  function selectedScrollSnap() {\n    return engine.index.get();\n  }\n  function previousScrollSnap() {\n    return engine.indexPrevious.get();\n  }\n  function slidesInView() {\n    return engine.slidesInView.get();\n  }\n  function slidesNotInView() {\n    return engine.slidesInView.get(false);\n  }\n  function plugins() {\n    return pluginApis;\n  }\n  function internalEngine() {\n    return engine;\n  }\n  function rootNode() {\n    return root;\n  }\n  function containerNode() {\n    return container;\n  }\n  function slideNodes() {\n    return slides;\n  }\n  const self = {\n    canScrollNext,\n    canScrollPrev,\n    containerNode,\n    internalEngine,\n    destroy,\n    off,\n    on,\n    emit,\n    plugins,\n    previousScrollSnap,\n    reInit,\n    rootNode,\n    scrollNext,\n    scrollPrev,\n    scrollProgress,\n    scrollSnapList,\n    scrollTo,\n    selectedScrollSnap,\n    slideNodes,\n    slidesInView,\n    slidesNotInView\n  };\n  activate(userOptions, userPlugins);\n  setTimeout(() => eventHandler.emit('init'), 0);\n  return self;\n}\nEmblaCarousel.globalOptions = undefined;\n\n\n//# sourceMappingURL=embla-carousel.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/embla-carousel@8.6.0/node_modules/embla-carousel/esm/embla-carousel.esm.js\n");

/***/ })

};
;