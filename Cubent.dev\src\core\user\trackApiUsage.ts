import * as vscode from "vscode"

/**
 * Track API usage by sending it to the webapp
 */
export async function trackApiUsage(
	context: vscode.ExtensionContext,
	modelId: string,
	tokensUsed: number,
	requestsMade: number = 1,
	costAccrued: number = 0,
	hasImages: boolean = false,
): Promise<void> {
	try {
		// Get authentication token from secrets
		const token = await context.secrets.get("cubent.webapp.sessionToken")

		if (!token) {
			// User not authenticated, skip tracking
			return
		}

		// Make request to webapp API
		const response = await fetch("https://cubent.dev/api/extension/usage", {
			method: "POST",
			headers: {
				Authorization: `Bearer ${token}`,
				"Content-Type": "application/json",
			},
			body: JSON.stringify({
				tokensUsed,
				requestsMade,
				costAccrued,
				modelId,
				hasImages,
				date: new Date().toISOString(),
			}),
		})

		if (!response.ok) {
			console.warn("[trackApiUsage] Failed to track usage:", response.status, response.statusText)
		}
	} catch (error) {
		console.error("[trackApiUsage] Error tracking API usage:", error)
		// Don't throw error for usage tracking failures
	}
}

/**
 * Check if user can make a request (has enough Cubent Units)
 */
export async function canMakeRequest(
	context: vscode.ExtensionContext,
	modelId: string,
	hasImages: boolean = false,
): Promise<boolean> {
	try {
		// Get authentication token from secrets
		const token = await context.secrets.get("cubent.webapp.sessionToken")

		if (!token) {
			// User not authenticated, allow request (will be handled by auth flow)
			return true
		}

		// Get current usage stats
		const response = await fetch("https://cubent.dev/api/extension/usage?days=30", {
			headers: {
				Authorization: `Bearer ${token}`,
				"Content-Type": "application/json",
			},
		})

		if (response.ok) {
			const usageData = await response.json()
			// Simple check: if usage is under 95%, allow request
			return usageData.currentMonth?.usagePercentage < 95
		}

		// If we can't check usage, allow request
		return true
	} catch (error) {
		console.error("[canMakeRequest] Error checking request permission:", error)
		// Allow request if we can't check usage
		return true
	}
}
