"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_packages_cms_basehub_next-toolbar_client-toolbar-CIQDQ5LJ_js"],{

/***/ "(app-pages-browser)/../../packages/cms/.basehub/next-toolbar/client-toolbar-CIQDQ5LJ.js":
/*!***************************************************************************!*\
  !*** ../../packages/cms/.basehub/next-toolbar/client-toolbar-CIQDQ5LJ.js ***!
  \***************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ClientToolbar: () => (/* binding */ ClientToolbar)\n/* harmony export */ });\n/* harmony import */ var _chunk_YSQDPG26_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-YSQDPG26.js */ \"(app-pages-browser)/../../packages/cms/.basehub/next-toolbar/chunk-YSQDPG26.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/api/navigation.js\");\n// This file was generated by basehub. Do not edit directly. Read more: https://basehub.com/docs/api-reference/basehub-sdk\n/* eslint-disable */ /* eslint-disable eslint-comments/no-restricted-disable */ /* tslint:disable */ /* __next_internal_client_entry_do_not_use__ ClientToolbar auto */ var _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$(), _s4 = $RefreshSig$();\n\n// ../../node_modules/.pnpm/lodash.debounce@4.0.8/node_modules/lodash.debounce/index.js\nvar require_lodash = (0,_chunk_YSQDPG26_js__WEBPACK_IMPORTED_MODULE_0__.__commonJS)({\n    \"../../node_modules/.pnpm/lodash.debounce@4.0.8/node_modules/lodash.debounce/index.js\" (exports, module) {\n        var FUNC_ERROR_TEXT = \"Expected a function\";\n        var NAN = 0 / 0;\n        var symbolTag = \"[object Symbol]\";\n        var reTrim = /^\\s+|\\s+$/g;\n        var reIsBadHex = /^[-+]0x[0-9a-f]+$/i;\n        var reIsBinary = /^0b[01]+$/i;\n        var reIsOctal = /^0o[0-7]+$/i;\n        var freeParseInt = parseInt;\n        var freeGlobal = typeof __webpack_require__.g == \"object\" && __webpack_require__.g && __webpack_require__.g.Object === Object && __webpack_require__.g;\n        var freeSelf = typeof self == \"object\" && self && self.Object === Object && self;\n        var root = freeGlobal || freeSelf || Function(\"return this\")();\n        var objectProto = Object.prototype;\n        var objectToString = objectProto.toString;\n        var nativeMax = Math.max;\n        var nativeMin = Math.min;\n        var now = function() {\n            return root.Date.now();\n        };\n        function debounce3(func, wait, options) {\n            var lastArgs, lastThis, maxWait, result, timerId, lastCallTime, lastInvokeTime = 0, leading = false, maxing = false, trailing = true;\n            if (typeof func != \"function\") {\n                throw new TypeError(FUNC_ERROR_TEXT);\n            }\n            wait = toNumber(wait) || 0;\n            if (isObject(options)) {\n                leading = !!options.leading;\n                maxing = \"maxWait\" in options;\n                maxWait = maxing ? nativeMax(toNumber(options.maxWait) || 0, wait) : maxWait;\n                trailing = \"trailing\" in options ? !!options.trailing : trailing;\n            }\n            function invokeFunc(time) {\n                var args = lastArgs, thisArg = lastThis;\n                lastArgs = lastThis = void 0;\n                lastInvokeTime = time;\n                result = func.apply(thisArg, args);\n                return result;\n            }\n            function leadingEdge(time) {\n                lastInvokeTime = time;\n                timerId = setTimeout(timerExpired, wait);\n                return leading ? invokeFunc(time) : result;\n            }\n            function remainingWait(time) {\n                var timeSinceLastCall = time - lastCallTime, timeSinceLastInvoke = time - lastInvokeTime, result2 = wait - timeSinceLastCall;\n                return maxing ? nativeMin(result2, maxWait - timeSinceLastInvoke) : result2;\n            }\n            function shouldInvoke(time) {\n                var timeSinceLastCall = time - lastCallTime, timeSinceLastInvoke = time - lastInvokeTime;\n                return lastCallTime === void 0 || timeSinceLastCall >= wait || timeSinceLastCall < 0 || maxing && timeSinceLastInvoke >= maxWait;\n            }\n            function timerExpired() {\n                var time = now();\n                if (shouldInvoke(time)) {\n                    return trailingEdge(time);\n                }\n                timerId = setTimeout(timerExpired, remainingWait(time));\n            }\n            function trailingEdge(time) {\n                timerId = void 0;\n                if (trailing && lastArgs) {\n                    return invokeFunc(time);\n                }\n                lastArgs = lastThis = void 0;\n                return result;\n            }\n            function cancel() {\n                if (timerId !== void 0) {\n                    clearTimeout(timerId);\n                }\n                lastInvokeTime = 0;\n                lastArgs = lastCallTime = lastThis = timerId = void 0;\n            }\n            function flush() {\n                return timerId === void 0 ? result : trailingEdge(now());\n            }\n            function debounced() {\n                var time = now(), isInvoking = shouldInvoke(time);\n                lastArgs = arguments;\n                lastThis = this;\n                lastCallTime = time;\n                if (isInvoking) {\n                    if (timerId === void 0) {\n                        return leadingEdge(lastCallTime);\n                    }\n                    if (maxing) {\n                        timerId = setTimeout(timerExpired, wait);\n                        return invokeFunc(lastCallTime);\n                    }\n                }\n                if (timerId === void 0) {\n                    timerId = setTimeout(timerExpired, wait);\n                }\n                return result;\n            }\n            debounced.cancel = cancel;\n            debounced.flush = flush;\n            return debounced;\n        }\n        function isObject(value) {\n            var type = typeof value;\n            return !!value && (type == \"object\" || type == \"function\");\n        }\n        function isObjectLike(value) {\n            return !!value && typeof value == \"object\";\n        }\n        function isSymbol(value) {\n            return typeof value == \"symbol\" || isObjectLike(value) && objectToString.call(value) == symbolTag;\n        }\n        function toNumber(value) {\n            if (typeof value == \"number\") {\n                return value;\n            }\n            if (isSymbol(value)) {\n                return NAN;\n            }\n            if (isObject(value)) {\n                var other = typeof value.valueOf == \"function\" ? value.valueOf() : value;\n                value = isObject(other) ? other + \"\" : other;\n            }\n            if (typeof value != \"string\") {\n                return value === 0 ? value : +value;\n            }\n            value = value.replace(reTrim, \"\");\n            var isBinary = reIsBinary.test(value);\n            return isBinary || reIsOctal.test(value) ? freeParseInt(value.slice(2), isBinary ? 2 : 8) : reIsBadHex.test(value) ? NAN : +value;\n        }\n        module.exports = debounce3;\n    }\n});\n// ../../node_modules/.pnpm/basehub@8.2.7_@babel+runtim_3aebfa8e064bb601162c04844d38dd02/node_modules/basehub/src/next/toolbar/client-toolbar.tsx\n\n// esbuild-scss-modules-plugin:./toolbar.module.scss\nvar digest = \"dffb3111f2dbe90df2c9f44aa745e1eaea5704ee2372695a11b6c736b47349b7\";\nvar classes = {\n    \"wrapper\": \"_wrapper_ypbb5_1\",\n    \"branch\": \"_branch_ypbb5_32\",\n    \"in\": \"_in_ypbb5_1\",\n    \"root\": \"_root_ypbb5_53\",\n    \"draft\": \"_draft_ypbb5_67\",\n    \"breathe\": \"_breathe_ypbb5_1\",\n    \"tooltipWrapper\": \"_tooltipWrapper_ypbb5_122\",\n    \"tooltip\": \"_tooltip_ypbb5_122\",\n    \"dragHandle\": \"_dragHandle_ypbb5_131\",\n    \"dragging\": \"_dragging_ypbb5_135\",\n    \"forceVisible\": \"_forceVisible_ypbb5_158\",\n    \"top\": \"_top_ypbb5_161\",\n    \"bottom\": \"_bottom_ypbb5_172\",\n    \"right\": \"_right_ypbb5_182\",\n    \"left\": \"_left_ypbb5_193\",\n    \"branchSelect\": \"_branchSelect_ypbb5_219\",\n    \"branchSelectIcon\": \"_branchSelectIcon_ypbb5_245\"\n};\nvar css = '._wrapper_ypbb5_1 {\\n  box-sizing: border-box;\\n  font-size: 16px;\\n}\\n._wrapper_ypbb5_1 *,\\n._wrapper_ypbb5_1 *:before,\\n._wrapper_ypbb5_1 *:after {\\n  box-sizing: inherit;\\n}\\n._wrapper_ypbb5_1 h1,\\n._wrapper_ypbb5_1 h2,\\n._wrapper_ypbb5_1 h3,\\n._wrapper_ypbb5_1 h4,\\n._wrapper_ypbb5_1 h5,\\n._wrapper_ypbb5_1 h6,\\n._wrapper_ypbb5_1 p,\\n._wrapper_ypbb5_1 ol,\\n._wrapper_ypbb5_1 ul {\\n  margin: 0;\\n  padding: 0;\\n  font-weight: normal;\\n}\\n._wrapper_ypbb5_1 ol,\\n._wrapper_ypbb5_1 ul {\\n  list-style: none;\\n}\\n._wrapper_ypbb5_1 img {\\n  max-width: 100%;\\n  height: auto;\\n}\\n\\n._branch_ypbb5_32 {\\n  padding-left: 9px;\\n  padding-right: 12px;\\n  height: 100%;\\n  display: flex;\\n  align-items: center;\\n  font-weight: 500;\\n  user-select: none;\\n}\\n\\n._wrapper_ypbb5_1 {\\n  position: fixed;\\n  bottom: 32px;\\n  right: 32px;\\n  background: #0c0c0c;\\n  z-index: 1000;\\n  border-radius: 7px;\\n  animation: _in_ypbb5_1 0.3s ease-out;\\n  display: flex;\\n}\\n\\n._root_ypbb5_53 {\\n  --font-family: Inter, Segoe UI, Roboto, sans-serif, Apple Color Emoji,\\n    Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji, sans-serif;\\n  border-radius: 6px;\\n  height: 36px;\\n  color: white;\\n  display: flex;\\n  border: 1px solid #303030;\\n  font-family: var(--font-family);\\n}\\n._root_ypbb5_53[data-draft-active=true] {\\n  border-color: #ff6c02;\\n  background-color: rgba(255, 108, 2, 0.15);\\n}\\n._root_ypbb5_53[data-draft-active=true]:has(button._draft_ypbb5_67:enabled:hover) {\\n  border-color: #ff8b35;\\n}\\n\\n._draft_ypbb5_67 {\\n  all: unset;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 8px 10px;\\n  cursor: pointer;\\n  color: #646464;\\n  border-left: 1px solid #303030;\\n  border-radius: 0 5px 5px 0;\\n  margin: -1px;\\n}\\n._draft_ypbb5_67:disabled:hover {\\n  cursor: not-allowed;\\n}\\n._draft_ypbb5_67[data-active=true] {\\n  border-color: #ff6c02;\\n}\\n._draft_ypbb5_67[data-active=true]:enabled:hover {\\n  border-color: #ff8b35;\\n  background-color: #ff8b35;\\n}\\n._draft_ypbb5_67[data-active=false] {\\n  border: 1px solid #303030;\\n}\\n._draft_ypbb5_67[data-active=false]:enabled:hover {\\n  background-color: #0c0c0c;\\n}\\n._draft_ypbb5_67:focus-visible {\\n  outline: 1px solid;\\n  outline-offset: -1px;\\n  outline-color: #303030;\\n  border-radius: 0 6px 6px 0;\\n}\\n._draft_ypbb5_67[data-active=true] {\\n  color: #f3f3f3;\\n  background-color: #ff6c02;\\n}\\n._draft_ypbb5_67[data-loading=false] ._draft_ypbb5_67[data-active=true] {\\n  transition: color 0.2s, background-color 0.2s;\\n}\\n._draft_ypbb5_67[data-loading=false] ._draft_ypbb5_67[data-active=true]:enabled:hover {\\n  color: #fff;\\n}\\n._draft_ypbb5_67[data-loading=true] {\\n  cursor: wait !important;\\n}\\n._draft_ypbb5_67[data-loading=true] svg {\\n  animation: _breathe_ypbb5_1 1s infinite;\\n}\\n\\n._tooltipWrapper_ypbb5_122 {\\n  position: relative;\\n  display: flex;\\n  height: 100%;\\n}\\n._tooltipWrapper_ypbb5_122:hover ._tooltip_ypbb5_122 {\\n  visibility: visible;\\n}\\n\\n._dragHandle_ypbb5_131 {\\n  all: unset;\\n  cursor: grab;\\n}\\n._dragHandle_ypbb5_131._dragging_ypbb5_135 {\\n  cursor: grabbing;\\n}\\n._dragHandle_ypbb5_131:active {\\n  cursor: grabbing;\\n}\\n\\n._tooltip_ypbb5_122 {\\n  position: absolute;\\n  bottom: 40px;\\n  left: 50%;\\n  transform: translateX(-50%) translateY(0);\\n  background-color: #0c0c0c;\\n  border: 1px solid #303030;\\n  color: white;\\n  border-radius: 4px;\\n  max-width: 250px;\\n  width: max-content;\\n  font-size: 14px;\\n  z-index: 1000;\\n  visibility: hidden;\\n  --translate-x: -50%;\\n}\\n._tooltip_ypbb5_122._forceVisible_ypbb5_158 {\\n  visibility: visible;\\n}\\n._tooltip_ypbb5_122._top_ypbb5_161 {\\n  top: 40px;\\n  bottom: unset;\\n  transform: translateY(0) translateX(var(--translate-x));\\n}\\n._tooltip_ypbb5_122._top_ypbb5_161:before {\\n  mask-image: linear-gradient(135deg, rgb(0, 0, 0) 31%, rgba(0, 0, 0, 0) 31%, rgba(0, 0, 0, 0) 100%);\\n  top: -4.5px;\\n  bottom: unset;\\n  transform: translateX(var(--translate-x)) rotate(45deg);\\n}\\n._tooltip_ypbb5_122._bottom_ypbb5_172 {\\n  bottom: unset;\\n  top: -40px;\\n  transform: translateY(0) translateX(var(--translate-x));\\n}\\n._tooltip_ypbb5_122._bottom_ypbb5_172:before {\\n  bottom: -4.5px;\\n  top: unset;\\n  transform: translateX(0) rotate(45deg);\\n}\\n._tooltip_ypbb5_122._right_ypbb5_182 {\\n  right: 0;\\n  left: unset;\\n  transform: translateX(0);\\n  --translate-x: 0;\\n}\\n._tooltip_ypbb5_122._right_ypbb5_182:before {\\n  right: 8px;\\n  left: unset;\\n  transform: translateX(--translate-x) rotate(45deg);\\n}\\n._tooltip_ypbb5_122._left_ypbb5_193 {\\n  left: 50%;\\n  right: unset;\\n  transform: translateX(-50%);\\n  --translate-x: -50%;\\n}\\n._tooltip_ypbb5_122._left_ypbb5_193:before {\\n  left: 50%;\\n  right: unset;\\n  transform: translateX(-50%) rotate(45deg);\\n}\\n._tooltip_ypbb5_122:before {\\n  z-index: -1;\\n  mask-image: linear-gradient(-45deg, rgb(0, 0, 0) 31%, rgba(0, 0, 0, 0) 31%, rgba(0, 0, 0, 0) 100%);\\n  content: \"\";\\n  position: absolute;\\n  bottom: -4.5px;\\n  left: 50%;\\n  width: 20px;\\n  height: 20px;\\n  background-color: #0c0c0c;\\n  transform: rotate(45deg) translateX(-50%);\\n  border-radius: 2px;\\n  border: 1px solid #303030;\\n}\\n\\n._branchSelect_ypbb5_219 {\\n  height: 100%;\\n  background: none;\\n  border: none;\\n  font-weight: 500;\\n  font-size: 16px;\\n  padding-right: 8px;\\n  padding-bottom: 0px;\\n  padding-top: 0px;\\n  margin-bottom: 2px;\\n  min-width: 80px;\\n  max-width: 250px;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: normal;\\n  outline: none;\\n  color: inherit;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n  opacity: 1;\\n  font-family: var(--font-family);\\n  appearance: none;\\n  -webkit-appearance: none;\\n  -moz-appearance: none;\\n}\\n\\n._branchSelectIcon_ypbb5_245 {\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  right: 0;\\n  pointer-events: none;\\n}\\n\\n@keyframes _in_ypbb5_1 {\\n  0% {\\n    opacity: 0;\\n    transform: translateY(4px) scale(0.98);\\n  }\\n  100% {\\n    opacity: 1;\\n    transform: translateY(0) scale(1);\\n  }\\n}\\n@keyframes _breathe_ypbb5_1 {\\n  0% {\\n    opacity: 1;\\n  }\\n  50% {\\n    opacity: 0.45;\\n  }\\n  100% {\\n    opacity: 1;\\n  }\\n}';\n(function() {\n    if (typeof document !== \"undefined\" && !document.getElementById(digest)) {\n        var ele = document.createElement(\"style\");\n        ele.id = digest;\n        ele.textContent = css;\n        document.head.appendChild(ele);\n    }\n})();\nvar toolbar_module_default = classes;\n// ../../node_modules/.pnpm/basehub@8.2.7_@babel+runtim_3aebfa8e064bb601162c04844d38dd02/node_modules/basehub/src/next/toolbar/components/tooltip.tsx\nvar import_lodash = (0,_chunk_YSQDPG26_js__WEBPACK_IMPORTED_MODULE_0__.__toESM)(require_lodash(), 1);\n\nvar Tooltip = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_s((param, ref)=>{\n    let { children, content, forceVisible } = param;\n    _s();\n    const tooltipContentRef = react__WEBPACK_IMPORTED_MODULE_1__.useRef(null);\n    const checkOverflow = react__WEBPACK_IMPORTED_MODULE_1__.useCallback((0, import_lodash.default)({\n        \"Tooltip.useCallback[checkOverflow]\": ()=>{\n            if (tooltipContentRef.current) {\n                const rect = tooltipContentRef.current.getBoundingClientRect();\n                const paddingInline = tooltipContentRef.current.classList.contains(toolbar_module_default.left) ? 0 : rect.width / 2;\n                const paddingBlock = rect.height;\n                const tooltipOffset = 40 * 2;\n                const isAlreadyToTop = tooltipContentRef.current.classList.contains(toolbar_module_default.bottom);\n                if ((isAlreadyToTop ? rect.top : rect.top - tooltipOffset - paddingBlock) <= 0) {\n                    tooltipContentRef.current.classList.remove(toolbar_module_default.bottom);\n                    tooltipContentRef.current.classList.add(toolbar_module_default.top);\n                } else {\n                    tooltipContentRef.current.classList.remove(toolbar_module_default.top);\n                    tooltipContentRef.current.classList.add(toolbar_module_default.bottom);\n                }\n                if (rect.right + paddingInline > window.innerWidth) {\n                    tooltipContentRef.current.classList.remove(toolbar_module_default.left);\n                    tooltipContentRef.current.classList.add(toolbar_module_default.right);\n                } else {\n                    tooltipContentRef.current.classList.remove(toolbar_module_default.right);\n                    tooltipContentRef.current.classList.add(toolbar_module_default.left);\n                }\n            }\n        }\n    }[\"Tooltip.useCallback[checkOverflow]\"], 100), []);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"Tooltip.useEffect\": ()=>{\n            checkOverflow();\n            window.addEventListener(\"resize\", checkOverflow);\n            return ({\n                \"Tooltip.useEffect\": ()=>{\n                    window.removeEventListener(\"resize\", checkOverflow);\n                }\n            })[\"Tooltip.useEffect\"];\n        }\n    }[\"Tooltip.useEffect\"], [\n        checkOverflow\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle(ref, {\n        \"Tooltip.useImperativeHandle\": ()=>({\n                checkOverflow\n            })\n    }[\"Tooltip.useImperativeHandle\"], [\n        checkOverflow\n    ]);\n    return /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"div\", {\n        className: toolbar_module_default.tooltipWrapper\n    }, /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"p\", {\n        ref: tooltipContentRef,\n        style: {\n            padding: \"3px 8px\"\n        },\n        className: forceVisible ? \"\".concat(toolbar_module_default.tooltip, \" \").concat(toolbar_module_default.bottom, \" \").concat(toolbar_module_default.left, \" \").concat(toolbar_module_default.forceVisible) : \"\".concat(toolbar_module_default.tooltip, \" \").concat(toolbar_module_default.bottom, \" \").concat(toolbar_module_default.left)\n    }, content), children);\n}, \"NhslALngfU01jgMRtQNQrjIS1cw=\"));\n_c = Tooltip;\n// ../../node_modules/.pnpm/basehub@8.2.7_@babel+runtim_3aebfa8e064bb601162c04844d38dd02/node_modules/basehub/src/next/toolbar/components/drag-handle.tsx\n\nvar DragHandle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_s1((param, ref)=>{\n    let { onDrag, children } = param;\n    _s1();\n    const [isDragging, setIsDragging] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);\n    const initialPointer = react__WEBPACK_IMPORTED_MODULE_1__.useRef({\n        x: 0,\n        y: 0\n    });\n    const initialToolbar = react__WEBPACK_IMPORTED_MODULE_1__.useRef({\n        x: 0,\n        y: 0\n    });\n    const hasDragged = react__WEBPACK_IMPORTED_MODULE_1__.useRef(false);\n    react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle(ref, {\n        \"DragHandle.useImperativeHandle\": ()=>({\n                hasDragged: hasDragged.current\n            })\n    }[\"DragHandle.useImperativeHandle\"]);\n    const handleDrag = react__WEBPACK_IMPORTED_MODULE_1__.useCallback({\n        \"DragHandle.useCallback[handleDrag]\": (e)=>{\n            if (!isDragging) return;\n            const deltaX = e.clientX - initialPointer.current.x;\n            const deltaY = e.clientY - initialPointer.current.y;\n            const newToolbarX = initialToolbar.current.x + deltaX;\n            const newToolbarY = initialToolbar.current.y + deltaY;\n            if (Math.abs(deltaX) > 2 || Math.abs(deltaY) > 2) {\n                hasDragged.current = true;\n            }\n            onDrag({\n                x: newToolbarX,\n                y: newToolbarY\n            });\n        }\n    }[\"DragHandle.useCallback[handleDrag]\"], [\n        isDragging,\n        onDrag\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect({\n        \"DragHandle.useLayoutEffect\": ()=>{\n            if (!isDragging) return;\n            window.addEventListener(\"pointermove\", handleDrag);\n            return ({\n                \"DragHandle.useLayoutEffect\": ()=>{\n                    window.removeEventListener(\"pointermove\", handleDrag);\n                }\n            })[\"DragHandle.useLayoutEffect\"];\n        }\n    }[\"DragHandle.useLayoutEffect\"], [\n        isDragging,\n        onDrag,\n        handleDrag\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect({\n        \"DragHandle.useLayoutEffect\": ()=>{\n            if (!isDragging) {\n                hasDragged.current = false;\n                return;\n            }\n            const handlePointerUp = {\n                \"DragHandle.useLayoutEffect.handlePointerUp\": ()=>{\n                    setIsDragging(false);\n                }\n            }[\"DragHandle.useLayoutEffect.handlePointerUp\"];\n            window.addEventListener(\"pointerup\", handlePointerUp);\n            return ({\n                \"DragHandle.useLayoutEffect\": ()=>{\n                    window.removeEventListener(\"pointerup\", handlePointerUp);\n                }\n            })[\"DragHandle.useLayoutEffect\"];\n        }\n    }[\"DragHandle.useLayoutEffect\"], [\n        isDragging\n    ]);\n    return /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"span\", {\n        draggable: true,\n        className: \"\".concat(toolbar_module_default.dragHandle, \" \").concat(isDragging ? toolbar_module_default.dragging : \"\"),\n        onPointerDown: (e)=>{\n            if (e.target instanceof HTMLElement && (e.target.nodeName.toLowerCase() === \"select\" || e.target.closest(\"select\"))) {\n                return;\n            }\n            const handle = e.currentTarget;\n            if (!handle) return;\n            e.stopPropagation();\n            e.preventDefault();\n            initialPointer.current = {\n                x: e.clientX,\n                y: e.clientY\n            };\n            const rect = handle.getBoundingClientRect();\n            initialToolbar.current.x = rect.left;\n            initialToolbar.current.y = rect.top;\n            setIsDragging(true);\n        },\n        onPointerUp: ()=>{\n            setIsDragging(false);\n        }\n    }, children);\n}, \"8Zo+PEPaiEwIr9zU6k1GmyrHSu8=\"));\n_c1 = DragHandle;\n// ../../node_modules/.pnpm/basehub@8.2.7_@babel+runtim_3aebfa8e064bb601162c04844d38dd02/node_modules/basehub/src/next/toolbar/components/branch-swticher.tsx\n\nvar BranchSwitcher = (param)=>{\n    let { isForcedDraft, draft, apiRref, latestBranches, onRefChange, getAndSetLatestBranches } = param;\n    _s2();\n    const shadowRef = react__WEBPACK_IMPORTED_MODULE_1__.useRef(null);\n    const selectRef = react__WEBPACK_IMPORTED_MODULE_1__.useRef(null);\n    const sortedLatestBranches = react__WEBPACK_IMPORTED_MODULE_1__.useMemo({\n        \"BranchSwitcher.useMemo[sortedLatestBranches]\": ()=>{\n            return [\n                ...latestBranches\n            ].sort({\n                \"BranchSwitcher.useMemo[sortedLatestBranches]\": (a, b)=>{\n                    if (a.isDefault) return -1;\n                    if (b.isDefault) return 1;\n                    return a.name.localeCompare(b.name);\n                }\n            }[\"BranchSwitcher.useMemo[sortedLatestBranches]\"]);\n        }\n    }[\"BranchSwitcher.useMemo[sortedLatestBranches]\"], [\n        latestBranches\n    ]);\n    const refOptions = react__WEBPACK_IMPORTED_MODULE_1__.useMemo({\n        \"BranchSwitcher.useMemo[refOptions]\": ()=>{\n            const options = new Set(sortedLatestBranches.map({\n                \"BranchSwitcher.useMemo[refOptions]\": (branch)=>branch.name\n            }[\"BranchSwitcher.useMemo[refOptions]\"]));\n            options.add(apiRref);\n            return Array.from(options);\n        }\n    }[\"BranchSwitcher.useMemo[refOptions]\"], [\n        sortedLatestBranches,\n        apiRref\n    ]);\n    const [refetchLatestBranches, setRefetchLatestBranches] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"BranchSwitcher.useEffect\": ()=>{\n            if (refetchLatestBranches) {\n                getAndSetLatestBranches().then({\n                    \"BranchSwitcher.useEffect\": ()=>{\n                        setRefetchLatestBranches(false);\n                    }\n                }[\"BranchSwitcher.useEffect\"]);\n            }\n        }\n    }[\"BranchSwitcher.useEffect\"], [\n        refetchLatestBranches,\n        getAndSetLatestBranches\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"BranchSwitcher.useEffect\": ()=>{\n            const shadow = shadowRef.current;\n            const select = selectRef.current;\n            if (!shadow || !select) return;\n            const updateSelectWidth = {\n                \"BranchSwitcher.useEffect.updateSelectWidth\": ()=>{\n                    const width = shadow.offsetWidth;\n                    select.style.width = \"\".concat(width + 20, \"px\");\n                }\n            }[\"BranchSwitcher.useEffect.updateSelectWidth\"];\n            updateSelectWidth();\n            window.addEventListener(\"resize\", updateSelectWidth);\n            return ({\n                \"BranchSwitcher.useEffect\": ()=>{\n                    window.removeEventListener(\"resize\", updateSelectWidth);\n                    if (select) {\n                        select.style.removeProperty(\"width\");\n                    }\n                }\n            })[\"BranchSwitcher.useEffect\"];\n        }\n    }[\"BranchSwitcher.useEffect\"], [\n        apiRref\n    ]);\n    const isDraftActive = isForcedDraft || draft;\n    return /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"div\", {\n        className: toolbar_module_default.branch,\n        \"data-draft-active\": isDraftActive,\n        onMouseEnter: ()=>{\n            setRefetchLatestBranches(true);\n        }\n    }, /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(BranchIcon, null), \"\\xA0\", /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(Tooltip, {\n        content: !isDraftActive ? \"Switch branch and enter draft mode\" : \"Switch branch\"\n    }, /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"select\", {\n        ref: selectRef,\n        value: apiRref,\n        onChange: (e)=>onRefChange(e.target.value, {\n                enableDraftMode: !isDraftActive\n            }),\n        className: toolbar_module_default.branchSelect,\n        onMouseDown: (e)=>{\n            e.stopPropagation();\n        },\n        onClick: (e)=>{\n            e.stopPropagation();\n            setRefetchLatestBranches(true);\n        }\n    }, refOptions.map((r)=>{\n        return /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"option\", {\n            key: r,\n            value: r\n        }, r);\n    })), /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"svg\", {\n        width: \"15\",\n        height: \"15\",\n        viewBox: \"0 0 15 15\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        className: toolbar_module_default.branchSelectIcon\n    }, /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"path\", {\n        d: \"M4.93179 5.43179C4.75605 5.60753 4.75605 5.89245 4.93179 6.06819C5.10753 6.24392 5.39245 6.24392 5.56819 6.06819L7.49999 4.13638L9.43179 6.06819C9.60753 6.24392 9.89245 6.24392 10.0682 6.06819C10.2439 5.89245 10.2439 5.60753 10.0682 5.43179L7.81819 3.18179C7.73379 3.0974 7.61933 3.04999 7.49999 3.04999C7.38064 3.04999 7.26618 3.0974 7.18179 3.18179L4.93179 5.43179ZM10.0682 9.56819C10.2439 9.39245 10.2439 9.10753 10.0682 8.93179C9.89245 8.75606 9.60753 8.75606 9.43179 8.93179L7.49999 10.8636L5.56819 8.93179C5.39245 8.75606 5.10753 8.75606 4.93179 8.93179C4.75605 9.10753 4.75605 9.39245 4.93179 9.56819L7.18179 11.8182C7.35753 11.9939 7.64245 11.9939 7.81819 11.8182L10.0682 9.56819Z\",\n        fill: \"currentColor\",\n        fillRule: \"evenodd\",\n        clipRule: \"evenodd\"\n    }))), /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"span\", {\n        className: toolbar_module_default.branchSelect,\n        style: {\n            visibility: \"hidden\",\n            opacity: 0,\n            pointerEvents: \"none\",\n            position: \"absolute\",\n            top: 0,\n            left: 0\n        },\n        \"aria-hidden\": \"true\",\n        ref: shadowRef\n    }, apiRref));\n};\n_s2(BranchSwitcher, \"crIHnSJNjHvIuJkzUEG0iBbZ5PI=\");\n_c2 = BranchSwitcher;\nvar BranchIcon = ()=>{\n    return /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        width: \"18\",\n        height: \"18\",\n        fill: \"none\"\n    }, /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"path\", {\n        fill: \"#F3F3F3\",\n        fillRule: \"evenodd\",\n        d: \"M12.765 5.365a1.25 1.25 0 1 0 .002-2.502 1.25 1.25 0 0 0-.002 2.502Zm0 1.063a2.315 2.315 0 1 0-2.315-2.313 2.315 2.315 0 0 0 2.316 2.313ZM5.234 15.137a1.25 1.25 0 1 0 .001-2.501 1.25 1.25 0 0 0 0 2.501Zm0 1.064a2.315 2.315 0 1 0-2.316-2.314 2.315 2.315 0 0 0 2.316 2.314Z\",\n        clipRule: \"evenodd\"\n    }), /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"path\", {\n        fill: \"#F3F3F3\",\n        fillRule: \"evenodd\",\n        d: \"M5.767 8.98v3.648H4.702V8.98h1.065ZM13.298 5.798v2.694h-1.065V5.798h1.065Z\",\n        clipRule: \"evenodd\"\n    }), /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"path\", {\n        fill: \"#F3F3F3\",\n        fillRule: \"evenodd\",\n        d: \"M13.298 8.448a.532.532 0 0 1-.533.532H5.29a.532.532 0 1 1 0-1.064h7.476c.294 0 .533.238.533.532ZM5.234 2.864a1.25 1.25 0 1 1 .001 2.502 1.25 1.25 0 0 1 0-2.502Zm0-1.063a2.315 2.315 0 1 1-2.316 2.314A2.315 2.315 0 0 1 5.234 1.8Z\",\n        clipRule: \"evenodd\"\n    }), /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"path\", {\n        fill: \"#F3F3F3\",\n        fillRule: \"evenodd\",\n        d: \"M5.767 9.022V5.374H4.702v3.648h1.065Z\",\n        clipRule: \"evenodd\"\n    }));\n};\n_c3 = BranchIcon;\n// ../../node_modules/.pnpm/basehub@8.2.7_@babel+runtim_3aebfa8e064bb601162c04844d38dd02/node_modules/basehub/src/next/toolbar/client-toolbar.tsx\nvar import_lodash2 = (0,_chunk_YSQDPG26_js__WEBPACK_IMPORTED_MODULE_0__.__toESM)(require_lodash(), 1);\n\nvar TOOLBAR_POSITION_STORAGE_KEY = \"bshb_toolbar_pos\";\nvar ClientToolbar = (param)=>{\n    let { draft, isForcedDraft, enableDraftMode, disableDraftMode, bshbPreviewToken, shouldAutoEnableDraft, seekAndStoreBshbPreviewToken, resolvedRef, getLatestBranches } = param;\n    _s3();\n    const [toolbarRef, setToolbarRef] = react__WEBPACK_IMPORTED_MODULE_1__.useState(null);\n    const dragHandleRef = react__WEBPACK_IMPORTED_MODULE_1__.useRef(null);\n    const tooltipRef = react__WEBPACK_IMPORTED_MODULE_1__.useRef(null);\n    const [message, setMessage] = react__WEBPACK_IMPORTED_MODULE_1__.useState(\"\");\n    const [loading, setLoading] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);\n    const [previewRef, _setPreviewRef] = react__WEBPACK_IMPORTED_MODULE_1__.useState(resolvedRef.ref);\n    const [isDefaultRefSelected, setIsDefaultRefSelected] = react__WEBPACK_IMPORTED_MODULE_1__.useState(true);\n    const [isLoadingRef, setIsLoadingRef] = react__WEBPACK_IMPORTED_MODULE_1__.useState(true);\n    const [latestBranches, setLatestBranches] = react__WEBPACK_IMPORTED_MODULE_1__.useState([]);\n    const currentMessageTimeout = react__WEBPACK_IMPORTED_MODULE_1__.useRef(0);\n    const displayMessage = react__WEBPACK_IMPORTED_MODULE_1__.useCallback({\n        \"ClientToolbar.useCallback[displayMessage]\": (message2)=>{\n            window.clearTimeout(currentMessageTimeout.current);\n            setMessage(message2);\n            currentMessageTimeout.current = window.setTimeout({\n                \"ClientToolbar.useCallback[displayMessage]\": ()=>setMessage(\"\")\n            }[\"ClientToolbar.useCallback[displayMessage]\"], 5e3);\n        }\n    }[\"ClientToolbar.useCallback[displayMessage]\"], [\n        setMessage\n    ]);\n    const triggerDraftMode = react__WEBPACK_IMPORTED_MODULE_1__.useCallback({\n        \"ClientToolbar.useCallback[triggerDraftMode]\": (previewToken)=>{\n            setLoading(true);\n            enableDraftMode({\n                bshbPreviewToken: previewToken\n            }).then({\n                \"ClientToolbar.useCallback[triggerDraftMode]\": (param)=>{\n                    let { status, response } = param;\n                    if (status === 200) {\n                        setLatestBranches({\n                            \"ClientToolbar.useCallback[triggerDraftMode]\": (p)=>{\n                                var _response_latestBranches;\n                                return (_response_latestBranches = response.latestBranches) !== null && _response_latestBranches !== void 0 ? _response_latestBranches : p;\n                            }\n                        }[\"ClientToolbar.useCallback[triggerDraftMode]\"]);\n                        window.location.reload();\n                    } else if (\"error\" in response) {\n                        displayMessage(\"Draft mode activation error: \".concat(response.error));\n                    } else {\n                        displayMessage(\"Draft mode activation error\");\n                    }\n                }\n            }[\"ClientToolbar.useCallback[triggerDraftMode]\"]).finally({\n                \"ClientToolbar.useCallback[triggerDraftMode]\": ()=>setLoading(false)\n            }[\"ClientToolbar.useCallback[triggerDraftMode]\"]);\n        }\n    }[\"ClientToolbar.useCallback[triggerDraftMode]\"], [\n        enableDraftMode,\n        displayMessage\n    ]);\n    const bshbPreviewRefCookieName = \"bshb-preview-ref-\".concat(resolvedRef.repoHash);\n    const previewRefCookieManager = react__WEBPACK_IMPORTED_MODULE_1__.useMemo({\n        \"ClientToolbar.useMemo[previewRefCookieManager]\": ()=>({\n                set: ({\n                    \"ClientToolbar.useMemo[previewRefCookieManager]\": (ref)=>{\n                        document.cookie = \"\".concat(bshbPreviewRefCookieName, \"=\").concat(ref, \"; path=/; Max-Age=\").concat(60 * 60 * 24 * 30 * 365);\n                    }\n                })[\"ClientToolbar.useMemo[previewRefCookieManager]\"],\n                clear: ({\n                    \"ClientToolbar.useMemo[previewRefCookieManager]\": ()=>{\n                        document.cookie = \"\".concat(bshbPreviewRefCookieName, \"=; path=/; Max-Age=-1\");\n                    }\n                })[\"ClientToolbar.useMemo[previewRefCookieManager]\"],\n                get: ({\n                    \"ClientToolbar.useMemo[previewRefCookieManager]\": ()=>{\n                        var _document_cookie_split_find;\n                        var _document_cookie_split_find_split_;\n                        return (_document_cookie_split_find_split_ = (_document_cookie_split_find = document.cookie.split(\"; \").find({\n                            \"ClientToolbar.useMemo[previewRefCookieManager]\": (row)=>row.startsWith(bshbPreviewRefCookieName)\n                        }[\"ClientToolbar.useMemo[previewRefCookieManager]\"])) === null || _document_cookie_split_find === void 0 ? void 0 : _document_cookie_split_find.split(\"=\")[1]) !== null && _document_cookie_split_find_split_ !== void 0 ? _document_cookie_split_find_split_ : null;\n                    }\n                })[\"ClientToolbar.useMemo[previewRefCookieManager]\"]\n            })\n    }[\"ClientToolbar.useMemo[previewRefCookieManager]\"], [\n        bshbPreviewRefCookieName\n    ]);\n    const [hasAutoEnabledDraftOnce, setHasAutoEnabledDraftOnce] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);\n    react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect({\n        \"ClientToolbar.useLayoutEffect\": ()=>{\n            if (draft || hasAutoEnabledDraftOnce || !shouldAutoEnableDraft || isForcedDraft || !bshbPreviewToken) {\n                return;\n            }\n            triggerDraftMode(bshbPreviewToken);\n            setHasAutoEnabledDraftOnce(true);\n        }\n    }[\"ClientToolbar.useLayoutEffect\"], [\n        isForcedDraft,\n        enableDraftMode,\n        seekAndStoreBshbPreviewToken,\n        bshbPreviewToken,\n        displayMessage,\n        triggerDraftMode,\n        draft,\n        shouldAutoEnableDraft,\n        hasAutoEnabledDraftOnce\n    ]);\n    const getAndSetLatestBranches = react__WEBPACK_IMPORTED_MODULE_1__.useCallback({\n        \"ClientToolbar.useCallback[getAndSetLatestBranches]\": async ()=>{\n            let result = [];\n            const res = await getLatestBranches({\n                bshbPreviewToken\n            });\n            if (!res) return;\n            if (Array.isArray(res.response)) {\n                result = res.response;\n            } else if (\"error\" in res.response) {\n                console.error(\"BaseHub Toolbar Error: \".concat(res.response.error));\n            }\n            setLatestBranches(result);\n        }\n    }[\"ClientToolbar.useCallback[getAndSetLatestBranches]\"], [\n        bshbPreviewToken,\n        getLatestBranches\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"ClientToolbar.useEffect\": ()=>{\n            async function effect() {\n                while(true){\n                    try {\n                        getAndSetLatestBranches();\n                        await new Promise({\n                            \"ClientToolbar.useEffect.effect\": (resolve)=>setTimeout(resolve, 3e4)\n                        }[\"ClientToolbar.useEffect.effect\"]);\n                    } catch (error) {\n                        console.error(\"BaseHub Toolbar Error: \".concat(error));\n                        break;\n                    }\n                }\n            }\n            effect();\n        }\n    }[\"ClientToolbar.useEffect\"], [\n        getAndSetLatestBranches\n    ]);\n    const setRefWithEvents = react__WEBPACK_IMPORTED_MODULE_1__.useCallback({\n        \"ClientToolbar.useCallback[setRefWithEvents]\": (ref)=>{\n            _setPreviewRef(ref);\n            window.__bshb_ref = ref;\n            window.dispatchEvent(new CustomEvent(\"__bshb_ref_changed\"));\n            previewRefCookieManager.set(ref);\n            setIsDefaultRefSelected(ref === resolvedRef.ref);\n        }\n    }[\"ClientToolbar.useCallback[setRefWithEvents]\"], [\n        previewRefCookieManager,\n        resolvedRef.ref\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"ClientToolbar.useEffect\": ()=>{\n            const url = new URL(window.location.href);\n            let previewRef2 = url.searchParams.get(\"bshb-preview-ref\");\n            if (!previewRef2) {\n                previewRef2 = previewRefCookieManager.get();\n            }\n            setIsLoadingRef(false);\n            if (!previewRef2) return;\n            setRefWithEvents(previewRef2);\n        }\n    }[\"ClientToolbar.useEffect\"], [\n        previewRefCookieManager,\n        setRefWithEvents,\n        resolvedRef.repoHash\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"ClientToolbar.useEffect\": ()=>{\n            if (isLoadingRef) return;\n            setIsDefaultRefSelected(previewRef === resolvedRef.ref);\n        }\n    }[\"ClientToolbar.useEffect\"], [\n        previewRef,\n        resolvedRef.ref,\n        isLoadingRef\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"ClientToolbar.useEffect\": ()=>{\n            if (isLoadingRef) return;\n            if (isDefaultRefSelected) {\n                setRefWithEvents(resolvedRef.ref);\n                previewRefCookieManager.clear();\n                const url = new URL(window.location.href);\n                url.searchParams.delete(\"bshb-preview-ref\");\n                window.history.replaceState(null, \"\", url.toString());\n            }\n        }\n    }[\"ClientToolbar.useEffect\"], [\n        isDefaultRefSelected,\n        isLoadingRef,\n        previewRefCookieManager,\n        resolvedRef.ref,\n        setRefWithEvents\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect({\n        \"ClientToolbar.useLayoutEffect\": ()=>{\n            var _tooltipRef_current;\n            (_tooltipRef_current = tooltipRef.current) === null || _tooltipRef_current === void 0 ? void 0 : _tooltipRef_current.checkOverflow();\n        }\n    }[\"ClientToolbar.useLayoutEffect\"], [\n        message\n    ]);\n    const getStoredToolbarPosition = react__WEBPACK_IMPORTED_MODULE_1__.useCallback({\n        \"ClientToolbar.useCallback[getStoredToolbarPosition]\": ()=>{\n            if (!toolbarRef) return;\n            if ( false || !window.sessionStorage) return;\n            const toolbarPositionStored = window.sessionStorage.getItem(TOOLBAR_POSITION_STORAGE_KEY);\n            if (!toolbarPositionStored) return;\n            const toolbarPosition = JSON.parse(toolbarPositionStored);\n            if (!(\"x\" in toolbarPosition)) return;\n            if (!(\"y\" in toolbarPosition)) return;\n            return toolbarPosition;\n        }\n    }[\"ClientToolbar.useCallback[getStoredToolbarPosition]\"], [\n        toolbarRef\n    ]);\n    const updateToolbarStoredPositionDebounced = react__WEBPACK_IMPORTED_MODULE_1__.useCallback((0, import_lodash2.default)({\n        \"ClientToolbar.useCallback[updateToolbarStoredPositionDebounced]\": (position)=>{\n            if ( false || !window.sessionStorage) return;\n            var _getStoredToolbarPosition;\n            const storedPosition = (_getStoredToolbarPosition = getStoredToolbarPosition()) !== null && _getStoredToolbarPosition !== void 0 ? _getStoredToolbarPosition : {\n                x: 0,\n                y: 0\n            };\n            window.sessionStorage.setItem(TOOLBAR_POSITION_STORAGE_KEY, JSON.stringify({\n                ...storedPosition,\n                ...position\n            }));\n        }\n    }[\"ClientToolbar.useCallback[updateToolbarStoredPositionDebounced]\"], 250), []);\n    const dragToolbar = react__WEBPACK_IMPORTED_MODULE_1__.useCallback({\n        \"ClientToolbar.useCallback[dragToolbar]\": (position)=>{\n            const toolbar = toolbarRef;\n            if (!toolbar) return;\n            const rect = toolbar.getBoundingClientRect();\n            const padding = 32;\n            const newPositionForStore = {};\n            if (position.x - padding < 0) {\n                toolbar.style.left = \"\".concat(padding, \"px\");\n                toolbar.style.right = \"unset\";\n                newPositionForStore.x = padding;\n            } else if (position.x + rect.width + padding > window.innerWidth) {\n                toolbar.style.right = \"\".concat(padding, \"px\");\n                toolbar.style.left = \"unset\";\n                newPositionForStore.x = padding;\n            } else {\n                toolbar.style.right = \"unset\";\n                toolbar.style.left = \"\".concat(position.x, \"px\");\n                newPositionForStore.x = position.x;\n            }\n            if (position.y - padding < 0) {\n                toolbar.style.bottom = \"unset\";\n                toolbar.style.top = \"\".concat(padding, \"px\");\n                newPositionForStore.y = padding;\n            } else if (position.y + rect.height + padding > window.innerHeight) {\n                toolbar.style.top = \"unset\";\n                toolbar.style.bottom = \"\".concat(padding, \"px\");\n                newPositionForStore.y = padding;\n            } else {\n                toolbar.style.bottom = \"unset\";\n                toolbar.style.top = \"\".concat(position.y, \"px\");\n                newPositionForStore.x = position.y;\n            }\n            updateToolbarStoredPositionDebounced({\n                x: position.x,\n                y: position.y\n            });\n        }\n    }[\"ClientToolbar.useCallback[dragToolbar]\"], [\n        toolbarRef,\n        updateToolbarStoredPositionDebounced\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"ClientToolbar.useEffect\": ()=>{\n            if (false) {}\n            const repositionToolbar = {\n                \"ClientToolbar.useEffect.repositionToolbar\": ()=>{\n                    var _tooltipRef_current;\n                    const pos = getStoredToolbarPosition();\n                    if (!pos) return;\n                    dragToolbar(pos);\n                    (_tooltipRef_current = tooltipRef.current) === null || _tooltipRef_current === void 0 ? void 0 : _tooltipRef_current.checkOverflow();\n                }\n            }[\"ClientToolbar.useEffect.repositionToolbar\"];\n            repositionToolbar();\n            window.addEventListener(\"resize\", repositionToolbar);\n            return ({\n                \"ClientToolbar.useEffect\": ()=>{\n                    window.removeEventListener(\"resize\", repositionToolbar);\n                }\n            })[\"ClientToolbar.useEffect\"];\n        }\n    }[\"ClientToolbar.useEffect\"], [\n        getStoredToolbarPosition,\n        dragToolbar\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"ClientToolbar.useEffect\": ()=>{\n            if (!latestBranches) return;\n            const fromCookie = previewRefCookieManager.get();\n            if (!fromCookie) return;\n            if (!latestBranches.find({\n                \"ClientToolbar.useEffect\": (branch)=>branch.name === fromCookie\n            }[\"ClientToolbar.useEffect\"])) {\n                previewRefCookieManager.clear();\n            }\n        }\n    }[\"ClientToolbar.useEffect\"], [\n        latestBranches,\n        previewRefCookieManager\n    ]);\n    const tooltip = isForcedDraft ? \"Draft enforced by dev env\" : \"\".concat(draft ? \"Disable\" : \"Enable\", \" draft mode\");\n    return /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"div\", {\n        className: toolbar_module_default.wrapper,\n        ref: setToolbarRef\n    }, /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(DragHandle, {\n        ref: dragHandleRef,\n        onDrag: (pos)=>{\n            var _tooltipRef_current;\n            dragToolbar(pos);\n            (_tooltipRef_current = tooltipRef.current) === null || _tooltipRef_current === void 0 ? void 0 : _tooltipRef_current.checkOverflow();\n        }\n    }, /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"div\", {\n        className: toolbar_module_default.root,\n        \"data-draft-active\": isForcedDraft || draft\n    }, /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(BranchSwitcher, {\n        isForcedDraft,\n        draft,\n        apiRref: previewRef,\n        latestBranches,\n        onRefChange: (newRef, opts)=>{\n            const url = new URL(window.location.href);\n            url.searchParams.set(\"bshb-preview-ref\", newRef);\n            window.history.replaceState(null, \"\", url.toString());\n            setRefWithEvents(newRef);\n            if (opts.enableDraftMode) {\n                const previewToken = bshbPreviewToken !== null && bshbPreviewToken !== void 0 ? bshbPreviewToken : seekAndStoreBshbPreviewToken();\n                if (!previewToken) {\n                    return displayMessage(\"Preview token not found\");\n                }\n                triggerDraftMode(previewToken);\n            }\n        },\n        getAndSetLatestBranches\n    }), /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(AutoAddRefToUrlOnPathChangeIfRefIsNotDefault, {\n        previewRef,\n        resolvedRef,\n        isDraftModeEnabled: isForcedDraft || draft\n    }), /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(Tooltip, {\n        content: message || tooltip,\n        ref: tooltipRef,\n        forceVisible: Boolean(message)\n    }, /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"button\", {\n        className: toolbar_module_default.draft,\n        \"data-active\": isForcedDraft || draft,\n        \"aria-label\": \"\".concat(draft ? \"Disable\" : \"Enable\", \" draft mode\"),\n        \"data-loading\": loading,\n        disabled: isForcedDraft || loading,\n        onClick: ()=>{\n            var _dragHandleRef_current;\n            if (loading || ((_dragHandleRef_current = dragHandleRef.current) === null || _dragHandleRef_current === void 0 ? void 0 : _dragHandleRef_current.hasDragged)) return;\n            if (draft) {\n                setLoading(true);\n                disableDraftMode().then(()=>{\n                    const url = new URL(window.location.href);\n                    url.searchParams.delete(\"bshb-preview\");\n                    url.searchParams.delete(\"__vercel_draft\");\n                    window.location.href = url.toString();\n                }).finally(()=>setLoading(false));\n            } else {\n                const previewToken = bshbPreviewToken !== null && bshbPreviewToken !== void 0 ? bshbPreviewToken : seekAndStoreBshbPreviewToken();\n                if (!previewToken) {\n                    return displayMessage(\"Preview token not found\");\n                }\n                triggerDraftMode(previewToken);\n            }\n        }\n    }, draft || isForcedDraft ? /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(EyeIcon, null) : /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(EyeDashedIcon, null))))));\n};\n_s3(ClientToolbar, \"IQ4+EzxYsCgE2Bgb/VOSmx8qxRw=\");\n_c4 = ClientToolbar;\nvar AutoAddRefToUrlOnPathChangeIfRefIsNotDefault = (param)=>{\n    let { previewRef, resolvedRef, isDraftModeEnabled } = param;\n    _s4();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const [initialPathname, setInitialPathname] = react__WEBPACK_IMPORTED_MODULE_1__.useState(pathname);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"AutoAddRefToUrlOnPathChangeIfRefIsNotDefault.useEffect\": ()=>{\n            if (initialPathname) return;\n            setInitialPathname(pathname);\n        }\n    }[\"AutoAddRefToUrlOnPathChangeIfRefIsNotDefault.useEffect\"], [\n        pathname,\n        initialPathname\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"AutoAddRefToUrlOnPathChangeIfRefIsNotDefault.useEffect\": ()=>{\n            if (isDraftModeEnabled) return;\n            if (initialPathname === pathname) {\n                return;\n            }\n            if (previewRef !== resolvedRef.ref) {\n                const url = new URL(window.location.href);\n                url.searchParams.set(\"bshb-preview-ref\", previewRef);\n                window.history.replaceState(null, \"\", url.toString());\n            }\n        }\n    }[\"AutoAddRefToUrlOnPathChangeIfRefIsNotDefault.useEffect\"], [\n        isDraftModeEnabled,\n        previewRef,\n        resolvedRef.ref,\n        pathname,\n        initialPathname\n    ]);\n    return null;\n};\n_s4(AutoAddRefToUrlOnPathChangeIfRefIsNotDefault, \"6OlaD9VqixIv4hI8ylvxpPZQWYs=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname\n    ];\n});\n_c5 = AutoAddRefToUrlOnPathChangeIfRefIsNotDefault;\nvar EyeDashedIcon = ()=>{\n    return /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"svg\", {\n        \"data-testid\": \"geist-icon\",\n        height: \"16\",\n        strokeLinejoin: \"round\",\n        viewBox: \"0 0 16 16\",\n        width: \"16\",\n        style: {\n            color: \"currentcolor\"\n        }\n    }, /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"path\", {\n        fillRule: \"evenodd\",\n        clipRule: \"evenodd\",\n        d: \"M6.51404 3.15793C7.48217 2.87411 8.51776 2.87411 9.48589 3.15793L9.90787 1.71851C8.66422 1.35392 7.33571 1.35392 6.09206 1.71851L6.51404 3.15793ZM10.848 3.78166C11.2578 4.04682 11.6393 4.37568 11.9783 4.76932L13.046 6.00934L14.1827 5.03056L13.1149 3.79054C12.6818 3.28761 12.1918 2.86449 11.6628 2.52224L10.848 3.78166ZM4.02168 4.76932C4.36065 4.37568 4.74209 4.04682 5.15195 3.78166L4.33717 2.52225C3.80815 2.86449 3.3181 3.28761 2.88503 3.79054L1.81723 5.03056L2.95389 6.00934L4.02168 4.76932ZM14.1138 7.24936L14.7602 7.99999L14.1138 8.75062L15.2505 9.72941L16.3183 8.48938V7.5106L15.2505 6.27058L14.1138 7.24936ZM1.88609 7.24936L1.23971 7.99999L1.88609 8.75062L0.749437 9.72941L-0.318359 8.48938V7.5106L0.749436 6.27058L1.88609 7.24936ZM13.0461 9.99064L11.9783 11.2307C11.6393 11.6243 11.2578 11.9532 10.848 12.2183L11.6628 13.4777C12.1918 13.1355 12.6818 12.7124 13.1149 12.2094L14.1827 10.9694L13.0461 9.99064ZM4.02168 11.2307L2.95389 9.99064L1.81723 10.9694L2.88503 12.2094C3.3181 12.7124 3.80815 13.1355 4.33717 13.4777L5.15195 12.2183C4.7421 11.9532 4.36065 11.6243 4.02168 11.2307ZM9.90787 14.2815L9.48589 12.8421C8.51776 13.1259 7.48217 13.1259 6.51405 12.8421L6.09206 14.2815C7.33572 14.6461 8.66422 14.6461 9.90787 14.2815ZM6.49997 7.99999C6.49997 7.17157 7.17154 6.49999 7.99997 6.49999C8.82839 6.49999 9.49997 7.17157 9.49997 7.99999C9.49997 8.82842 8.82839 9.49999 7.99997 9.49999C7.17154 9.49999 6.49997 8.82842 6.49997 7.99999ZM7.99997 4.99999C6.34311 4.99999 4.99997 6.34314 4.99997 7.99999C4.99997 9.65685 6.34311 11 7.99997 11C9.65682 11 11 9.65685 11 7.99999C11 6.34314 9.65682 4.99999 7.99997 4.99999Z\",\n        fill: \"currentColor\"\n    }));\n};\n_c6 = EyeDashedIcon;\nvar EyeIcon = ()=>{\n    return /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"svg\", {\n        \"data-testid\": \"geist-icon\",\n        height: \"16\",\n        strokeLinejoin: \"round\",\n        viewBox: \"0 0 16 16\",\n        width: \"16\",\n        style: {\n            color: \"currentcolor\"\n        }\n    }, /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"path\", {\n        fillRule: \"evenodd\",\n        clipRule: \"evenodd\",\n        d: \"M4.02168 4.76932C6.11619 2.33698 9.88374 2.33698 11.9783 4.76932L14.7602 7.99999L11.9783 11.2307C9.88374 13.663 6.1162 13.663 4.02168 11.2307L1.23971 7.99999L4.02168 4.76932ZM13.1149 3.79054C10.422 0.663244 5.57797 0.663247 2.88503 3.79054L-0.318359 7.5106V8.48938L2.88503 12.2094C5.57797 15.3367 10.422 15.3367 13.1149 12.2094L16.3183 8.48938V7.5106L13.1149 3.79054ZM6.49997 7.99999C6.49997 7.17157 7.17154 6.49999 7.99997 6.49999C8.82839 6.49999 9.49997 7.17157 9.49997 7.99999C9.49997 8.82842 8.82839 9.49999 7.99997 9.49999C7.17154 9.49999 6.49997 8.82842 6.49997 7.99999ZM7.99997 4.99999C6.34311 4.99999 4.99997 6.34314 4.99997 7.99999C4.99997 9.65685 6.34311 11 7.99997 11C9.65682 11 11 9.65685 11 7.99999C11 6.34314 9.65682 4.99999 7.99997 4.99999Z\",\n        fill: \"currentColor\"\n    }));\n};\n_c7 = EyeIcon;\n\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7;\n$RefreshReg$(_c, \"Tooltip\");\n$RefreshReg$(_c1, \"DragHandle\");\n$RefreshReg$(_c2, \"BranchSwitcher\");\n$RefreshReg$(_c3, \"BranchIcon\");\n$RefreshReg$(_c4, \"ClientToolbar\");\n$RefreshReg$(_c5, \"AutoAddRefToUrlOnPathChangeIfRefIsNotDefault\");\n$RefreshReg$(_c6, \"EyeDashedIcon\");\n$RefreshReg$(_c7, \"EyeIcon\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/cms/.basehub/next-toolbar/client-toolbar-CIQDQ5LJ.js\n"));

/***/ })

}]);