"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_packages_cms_basehub_next-toolbar_client-toolbar-CIQDQ5LJ_js";
exports.ids = ["_ssr_packages_cms_basehub_next-toolbar_client-toolbar-CIQDQ5LJ_js"];
exports.modules = {

/***/ "(ssr)/../../packages/cms/.basehub/next-toolbar/client-toolbar-CIQDQ5LJ.js":
/*!***************************************************************************!*\
  !*** ../../packages/cms/.basehub/next-toolbar/client-toolbar-CIQDQ5LJ.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ClientToolbar: () => (/* binding */ ClientToolbar)\n/* harmony export */ });\n/* harmony import */ var _chunk_YSQDPG26_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-YSQDPG26.js */ \"(ssr)/../../packages/cms/.basehub/next-toolbar/chunk-YSQDPG26.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/api/navigation.js\");\n// This file was generated by basehub. Do not edit directly. Read more: https://basehub.com/docs/api-reference/basehub-sdk\n/* eslint-disable */ /* eslint-disable eslint-comments/no-restricted-disable */ /* tslint:disable */ /* __next_internal_client_entry_do_not_use__ ClientToolbar auto */ \n// ../../node_modules/.pnpm/lodash.debounce@4.0.8/node_modules/lodash.debounce/index.js\nvar require_lodash = (0,_chunk_YSQDPG26_js__WEBPACK_IMPORTED_MODULE_0__.__commonJS)({\n    \"../../node_modules/.pnpm/lodash.debounce@4.0.8/node_modules/lodash.debounce/index.js\" (exports, module) {\n        var FUNC_ERROR_TEXT = \"Expected a function\";\n        var NAN = 0 / 0;\n        var symbolTag = \"[object Symbol]\";\n        var reTrim = /^\\s+|\\s+$/g;\n        var reIsBadHex = /^[-+]0x[0-9a-f]+$/i;\n        var reIsBinary = /^0b[01]+$/i;\n        var reIsOctal = /^0o[0-7]+$/i;\n        var freeParseInt = parseInt;\n        var freeGlobal = typeof global == \"object\" && global && global.Object === Object && global;\n        var freeSelf = typeof self == \"object\" && self && self.Object === Object && self;\n        var root = freeGlobal || freeSelf || Function(\"return this\")();\n        var objectProto = Object.prototype;\n        var objectToString = objectProto.toString;\n        var nativeMax = Math.max;\n        var nativeMin = Math.min;\n        var now = function() {\n            return root.Date.now();\n        };\n        function debounce3(func, wait, options) {\n            var lastArgs, lastThis, maxWait, result, timerId, lastCallTime, lastInvokeTime = 0, leading = false, maxing = false, trailing = true;\n            if (typeof func != \"function\") {\n                throw new TypeError(FUNC_ERROR_TEXT);\n            }\n            wait = toNumber(wait) || 0;\n            if (isObject(options)) {\n                leading = !!options.leading;\n                maxing = \"maxWait\" in options;\n                maxWait = maxing ? nativeMax(toNumber(options.maxWait) || 0, wait) : maxWait;\n                trailing = \"trailing\" in options ? !!options.trailing : trailing;\n            }\n            function invokeFunc(time) {\n                var args = lastArgs, thisArg = lastThis;\n                lastArgs = lastThis = void 0;\n                lastInvokeTime = time;\n                result = func.apply(thisArg, args);\n                return result;\n            }\n            function leadingEdge(time) {\n                lastInvokeTime = time;\n                timerId = setTimeout(timerExpired, wait);\n                return leading ? invokeFunc(time) : result;\n            }\n            function remainingWait(time) {\n                var timeSinceLastCall = time - lastCallTime, timeSinceLastInvoke = time - lastInvokeTime, result2 = wait - timeSinceLastCall;\n                return maxing ? nativeMin(result2, maxWait - timeSinceLastInvoke) : result2;\n            }\n            function shouldInvoke(time) {\n                var timeSinceLastCall = time - lastCallTime, timeSinceLastInvoke = time - lastInvokeTime;\n                return lastCallTime === void 0 || timeSinceLastCall >= wait || timeSinceLastCall < 0 || maxing && timeSinceLastInvoke >= maxWait;\n            }\n            function timerExpired() {\n                var time = now();\n                if (shouldInvoke(time)) {\n                    return trailingEdge(time);\n                }\n                timerId = setTimeout(timerExpired, remainingWait(time));\n            }\n            function trailingEdge(time) {\n                timerId = void 0;\n                if (trailing && lastArgs) {\n                    return invokeFunc(time);\n                }\n                lastArgs = lastThis = void 0;\n                return result;\n            }\n            function cancel() {\n                if (timerId !== void 0) {\n                    clearTimeout(timerId);\n                }\n                lastInvokeTime = 0;\n                lastArgs = lastCallTime = lastThis = timerId = void 0;\n            }\n            function flush() {\n                return timerId === void 0 ? result : trailingEdge(now());\n            }\n            function debounced() {\n                var time = now(), isInvoking = shouldInvoke(time);\n                lastArgs = arguments;\n                lastThis = this;\n                lastCallTime = time;\n                if (isInvoking) {\n                    if (timerId === void 0) {\n                        return leadingEdge(lastCallTime);\n                    }\n                    if (maxing) {\n                        timerId = setTimeout(timerExpired, wait);\n                        return invokeFunc(lastCallTime);\n                    }\n                }\n                if (timerId === void 0) {\n                    timerId = setTimeout(timerExpired, wait);\n                }\n                return result;\n            }\n            debounced.cancel = cancel;\n            debounced.flush = flush;\n            return debounced;\n        }\n        function isObject(value) {\n            var type = typeof value;\n            return !!value && (type == \"object\" || type == \"function\");\n        }\n        function isObjectLike(value) {\n            return !!value && typeof value == \"object\";\n        }\n        function isSymbol(value) {\n            return typeof value == \"symbol\" || isObjectLike(value) && objectToString.call(value) == symbolTag;\n        }\n        function toNumber(value) {\n            if (typeof value == \"number\") {\n                return value;\n            }\n            if (isSymbol(value)) {\n                return NAN;\n            }\n            if (isObject(value)) {\n                var other = typeof value.valueOf == \"function\" ? value.valueOf() : value;\n                value = isObject(other) ? other + \"\" : other;\n            }\n            if (typeof value != \"string\") {\n                return value === 0 ? value : +value;\n            }\n            value = value.replace(reTrim, \"\");\n            var isBinary = reIsBinary.test(value);\n            return isBinary || reIsOctal.test(value) ? freeParseInt(value.slice(2), isBinary ? 2 : 8) : reIsBadHex.test(value) ? NAN : +value;\n        }\n        module.exports = debounce3;\n    }\n});\n// ../../node_modules/.pnpm/basehub@8.2.7_@babel+runtim_3aebfa8e064bb601162c04844d38dd02/node_modules/basehub/src/next/toolbar/client-toolbar.tsx\n\n// esbuild-scss-modules-plugin:./toolbar.module.scss\nvar digest = \"dffb3111f2dbe90df2c9f44aa745e1eaea5704ee2372695a11b6c736b47349b7\";\nvar classes = {\n    \"wrapper\": \"_wrapper_ypbb5_1\",\n    \"branch\": \"_branch_ypbb5_32\",\n    \"in\": \"_in_ypbb5_1\",\n    \"root\": \"_root_ypbb5_53\",\n    \"draft\": \"_draft_ypbb5_67\",\n    \"breathe\": \"_breathe_ypbb5_1\",\n    \"tooltipWrapper\": \"_tooltipWrapper_ypbb5_122\",\n    \"tooltip\": \"_tooltip_ypbb5_122\",\n    \"dragHandle\": \"_dragHandle_ypbb5_131\",\n    \"dragging\": \"_dragging_ypbb5_135\",\n    \"forceVisible\": \"_forceVisible_ypbb5_158\",\n    \"top\": \"_top_ypbb5_161\",\n    \"bottom\": \"_bottom_ypbb5_172\",\n    \"right\": \"_right_ypbb5_182\",\n    \"left\": \"_left_ypbb5_193\",\n    \"branchSelect\": \"_branchSelect_ypbb5_219\",\n    \"branchSelectIcon\": \"_branchSelectIcon_ypbb5_245\"\n};\nvar css = `._wrapper_ypbb5_1 {\n  box-sizing: border-box;\n  font-size: 16px;\n}\n._wrapper_ypbb5_1 *,\n._wrapper_ypbb5_1 *:before,\n._wrapper_ypbb5_1 *:after {\n  box-sizing: inherit;\n}\n._wrapper_ypbb5_1 h1,\n._wrapper_ypbb5_1 h2,\n._wrapper_ypbb5_1 h3,\n._wrapper_ypbb5_1 h4,\n._wrapper_ypbb5_1 h5,\n._wrapper_ypbb5_1 h6,\n._wrapper_ypbb5_1 p,\n._wrapper_ypbb5_1 ol,\n._wrapper_ypbb5_1 ul {\n  margin: 0;\n  padding: 0;\n  font-weight: normal;\n}\n._wrapper_ypbb5_1 ol,\n._wrapper_ypbb5_1 ul {\n  list-style: none;\n}\n._wrapper_ypbb5_1 img {\n  max-width: 100%;\n  height: auto;\n}\n\n._branch_ypbb5_32 {\n  padding-left: 9px;\n  padding-right: 12px;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  font-weight: 500;\n  user-select: none;\n}\n\n._wrapper_ypbb5_1 {\n  position: fixed;\n  bottom: 32px;\n  right: 32px;\n  background: #0c0c0c;\n  z-index: 1000;\n  border-radius: 7px;\n  animation: _in_ypbb5_1 0.3s ease-out;\n  display: flex;\n}\n\n._root_ypbb5_53 {\n  --font-family: Inter, Segoe UI, Roboto, sans-serif, Apple Color Emoji,\n    Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji, sans-serif;\n  border-radius: 6px;\n  height: 36px;\n  color: white;\n  display: flex;\n  border: 1px solid #303030;\n  font-family: var(--font-family);\n}\n._root_ypbb5_53[data-draft-active=true] {\n  border-color: #ff6c02;\n  background-color: rgba(255, 108, 2, 0.15);\n}\n._root_ypbb5_53[data-draft-active=true]:has(button._draft_ypbb5_67:enabled:hover) {\n  border-color: #ff8b35;\n}\n\n._draft_ypbb5_67 {\n  all: unset;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 8px 10px;\n  cursor: pointer;\n  color: #646464;\n  border-left: 1px solid #303030;\n  border-radius: 0 5px 5px 0;\n  margin: -1px;\n}\n._draft_ypbb5_67:disabled:hover {\n  cursor: not-allowed;\n}\n._draft_ypbb5_67[data-active=true] {\n  border-color: #ff6c02;\n}\n._draft_ypbb5_67[data-active=true]:enabled:hover {\n  border-color: #ff8b35;\n  background-color: #ff8b35;\n}\n._draft_ypbb5_67[data-active=false] {\n  border: 1px solid #303030;\n}\n._draft_ypbb5_67[data-active=false]:enabled:hover {\n  background-color: #0c0c0c;\n}\n._draft_ypbb5_67:focus-visible {\n  outline: 1px solid;\n  outline-offset: -1px;\n  outline-color: #303030;\n  border-radius: 0 6px 6px 0;\n}\n._draft_ypbb5_67[data-active=true] {\n  color: #f3f3f3;\n  background-color: #ff6c02;\n}\n._draft_ypbb5_67[data-loading=false] ._draft_ypbb5_67[data-active=true] {\n  transition: color 0.2s, background-color 0.2s;\n}\n._draft_ypbb5_67[data-loading=false] ._draft_ypbb5_67[data-active=true]:enabled:hover {\n  color: #fff;\n}\n._draft_ypbb5_67[data-loading=true] {\n  cursor: wait !important;\n}\n._draft_ypbb5_67[data-loading=true] svg {\n  animation: _breathe_ypbb5_1 1s infinite;\n}\n\n._tooltipWrapper_ypbb5_122 {\n  position: relative;\n  display: flex;\n  height: 100%;\n}\n._tooltipWrapper_ypbb5_122:hover ._tooltip_ypbb5_122 {\n  visibility: visible;\n}\n\n._dragHandle_ypbb5_131 {\n  all: unset;\n  cursor: grab;\n}\n._dragHandle_ypbb5_131._dragging_ypbb5_135 {\n  cursor: grabbing;\n}\n._dragHandle_ypbb5_131:active {\n  cursor: grabbing;\n}\n\n._tooltip_ypbb5_122 {\n  position: absolute;\n  bottom: 40px;\n  left: 50%;\n  transform: translateX(-50%) translateY(0);\n  background-color: #0c0c0c;\n  border: 1px solid #303030;\n  color: white;\n  border-radius: 4px;\n  max-width: 250px;\n  width: max-content;\n  font-size: 14px;\n  z-index: 1000;\n  visibility: hidden;\n  --translate-x: -50%;\n}\n._tooltip_ypbb5_122._forceVisible_ypbb5_158 {\n  visibility: visible;\n}\n._tooltip_ypbb5_122._top_ypbb5_161 {\n  top: 40px;\n  bottom: unset;\n  transform: translateY(0) translateX(var(--translate-x));\n}\n._tooltip_ypbb5_122._top_ypbb5_161:before {\n  mask-image: linear-gradient(135deg, rgb(0, 0, 0) 31%, rgba(0, 0, 0, 0) 31%, rgba(0, 0, 0, 0) 100%);\n  top: -4.5px;\n  bottom: unset;\n  transform: translateX(var(--translate-x)) rotate(45deg);\n}\n._tooltip_ypbb5_122._bottom_ypbb5_172 {\n  bottom: unset;\n  top: -40px;\n  transform: translateY(0) translateX(var(--translate-x));\n}\n._tooltip_ypbb5_122._bottom_ypbb5_172:before {\n  bottom: -4.5px;\n  top: unset;\n  transform: translateX(0) rotate(45deg);\n}\n._tooltip_ypbb5_122._right_ypbb5_182 {\n  right: 0;\n  left: unset;\n  transform: translateX(0);\n  --translate-x: 0;\n}\n._tooltip_ypbb5_122._right_ypbb5_182:before {\n  right: 8px;\n  left: unset;\n  transform: translateX(--translate-x) rotate(45deg);\n}\n._tooltip_ypbb5_122._left_ypbb5_193 {\n  left: 50%;\n  right: unset;\n  transform: translateX(-50%);\n  --translate-x: -50%;\n}\n._tooltip_ypbb5_122._left_ypbb5_193:before {\n  left: 50%;\n  right: unset;\n  transform: translateX(-50%) rotate(45deg);\n}\n._tooltip_ypbb5_122:before {\n  z-index: -1;\n  mask-image: linear-gradient(-45deg, rgb(0, 0, 0) 31%, rgba(0, 0, 0, 0) 31%, rgba(0, 0, 0, 0) 100%);\n  content: \"\";\n  position: absolute;\n  bottom: -4.5px;\n  left: 50%;\n  width: 20px;\n  height: 20px;\n  background-color: #0c0c0c;\n  transform: rotate(45deg) translateX(-50%);\n  border-radius: 2px;\n  border: 1px solid #303030;\n}\n\n._branchSelect_ypbb5_219 {\n  height: 100%;\n  background: none;\n  border: none;\n  font-weight: 500;\n  font-size: 16px;\n  padding-right: 8px;\n  padding-bottom: 0px;\n  padding-top: 0px;\n  margin-bottom: 2px;\n  min-width: 80px;\n  max-width: 250px;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: normal;\n  outline: none;\n  color: inherit;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  opacity: 1;\n  font-family: var(--font-family);\n  appearance: none;\n  -webkit-appearance: none;\n  -moz-appearance: none;\n}\n\n._branchSelectIcon_ypbb5_245 {\n  position: absolute;\n  top: 50%;\n  transform: translateY(-50%);\n  right: 0;\n  pointer-events: none;\n}\n\n@keyframes _in_ypbb5_1 {\n  0% {\n    opacity: 0;\n    transform: translateY(4px) scale(0.98);\n  }\n  100% {\n    opacity: 1;\n    transform: translateY(0) scale(1);\n  }\n}\n@keyframes _breathe_ypbb5_1 {\n  0% {\n    opacity: 1;\n  }\n  50% {\n    opacity: 0.45;\n  }\n  100% {\n    opacity: 1;\n  }\n}`;\n(function() {\n    if (typeof document !== \"undefined\" && !document.getElementById(digest)) {\n        var ele = document.createElement(\"style\");\n        ele.id = digest;\n        ele.textContent = css;\n        document.head.appendChild(ele);\n    }\n})();\nvar toolbar_module_default = classes;\n// ../../node_modules/.pnpm/basehub@8.2.7_@babel+runtim_3aebfa8e064bb601162c04844d38dd02/node_modules/basehub/src/next/toolbar/components/tooltip.tsx\nvar import_lodash = (0,_chunk_YSQDPG26_js__WEBPACK_IMPORTED_MODULE_0__.__toESM)(require_lodash(), 1);\n\nvar Tooltip = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ children, content, forceVisible }, ref)=>{\n    const tooltipContentRef = react__WEBPACK_IMPORTED_MODULE_1__.useRef(null);\n    const checkOverflow = react__WEBPACK_IMPORTED_MODULE_1__.useCallback((0, import_lodash.default)({\n        \"Tooltip.useCallback[checkOverflow]\": ()=>{\n            if (tooltipContentRef.current) {\n                const rect = tooltipContentRef.current.getBoundingClientRect();\n                const paddingInline = tooltipContentRef.current.classList.contains(toolbar_module_default.left) ? 0 : rect.width / 2;\n                const paddingBlock = rect.height;\n                const tooltipOffset = 40 * 2;\n                const isAlreadyToTop = tooltipContentRef.current.classList.contains(toolbar_module_default.bottom);\n                if ((isAlreadyToTop ? rect.top : rect.top - tooltipOffset - paddingBlock) <= 0) {\n                    tooltipContentRef.current.classList.remove(toolbar_module_default.bottom);\n                    tooltipContentRef.current.classList.add(toolbar_module_default.top);\n                } else {\n                    tooltipContentRef.current.classList.remove(toolbar_module_default.top);\n                    tooltipContentRef.current.classList.add(toolbar_module_default.bottom);\n                }\n                if (rect.right + paddingInline > window.innerWidth) {\n                    tooltipContentRef.current.classList.remove(toolbar_module_default.left);\n                    tooltipContentRef.current.classList.add(toolbar_module_default.right);\n                } else {\n                    tooltipContentRef.current.classList.remove(toolbar_module_default.right);\n                    tooltipContentRef.current.classList.add(toolbar_module_default.left);\n                }\n            }\n        }\n    }[\"Tooltip.useCallback[checkOverflow]\"], 100), []);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"Tooltip.useEffect\": ()=>{\n            checkOverflow();\n            window.addEventListener(\"resize\", checkOverflow);\n            return ({\n                \"Tooltip.useEffect\": ()=>{\n                    window.removeEventListener(\"resize\", checkOverflow);\n                }\n            })[\"Tooltip.useEffect\"];\n        }\n    }[\"Tooltip.useEffect\"], [\n        checkOverflow\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle(ref, {\n        \"Tooltip.useImperativeHandle\": ()=>({\n                checkOverflow\n            })\n    }[\"Tooltip.useImperativeHandle\"], [\n        checkOverflow\n    ]);\n    return /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"div\", {\n        className: toolbar_module_default.tooltipWrapper\n    }, /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"p\", {\n        ref: tooltipContentRef,\n        style: {\n            padding: \"3px 8px\"\n        },\n        className: forceVisible ? `${toolbar_module_default.tooltip} ${toolbar_module_default.bottom} ${toolbar_module_default.left} ${toolbar_module_default.forceVisible}` : `${toolbar_module_default.tooltip} ${toolbar_module_default.bottom} ${toolbar_module_default.left}`\n    }, content), children);\n});\n// ../../node_modules/.pnpm/basehub@8.2.7_@babel+runtim_3aebfa8e064bb601162c04844d38dd02/node_modules/basehub/src/next/toolbar/components/drag-handle.tsx\n\nvar DragHandle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ onDrag, children }, ref)=>{\n    const [isDragging, setIsDragging] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);\n    const initialPointer = react__WEBPACK_IMPORTED_MODULE_1__.useRef({\n        x: 0,\n        y: 0\n    });\n    const initialToolbar = react__WEBPACK_IMPORTED_MODULE_1__.useRef({\n        x: 0,\n        y: 0\n    });\n    const hasDragged = react__WEBPACK_IMPORTED_MODULE_1__.useRef(false);\n    react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle(ref, {\n        \"DragHandle.useImperativeHandle\": ()=>({\n                hasDragged: hasDragged.current\n            })\n    }[\"DragHandle.useImperativeHandle\"]);\n    const handleDrag = react__WEBPACK_IMPORTED_MODULE_1__.useCallback({\n        \"DragHandle.useCallback[handleDrag]\": (e)=>{\n            if (!isDragging) return;\n            const deltaX = e.clientX - initialPointer.current.x;\n            const deltaY = e.clientY - initialPointer.current.y;\n            const newToolbarX = initialToolbar.current.x + deltaX;\n            const newToolbarY = initialToolbar.current.y + deltaY;\n            if (Math.abs(deltaX) > 2 || Math.abs(deltaY) > 2) {\n                hasDragged.current = true;\n            }\n            onDrag({\n                x: newToolbarX,\n                y: newToolbarY\n            });\n        }\n    }[\"DragHandle.useCallback[handleDrag]\"], [\n        isDragging,\n        onDrag\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect({\n        \"DragHandle.useLayoutEffect\": ()=>{\n            if (!isDragging) return;\n            window.addEventListener(\"pointermove\", handleDrag);\n            return ({\n                \"DragHandle.useLayoutEffect\": ()=>{\n                    window.removeEventListener(\"pointermove\", handleDrag);\n                }\n            })[\"DragHandle.useLayoutEffect\"];\n        }\n    }[\"DragHandle.useLayoutEffect\"], [\n        isDragging,\n        onDrag,\n        handleDrag\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect({\n        \"DragHandle.useLayoutEffect\": ()=>{\n            if (!isDragging) {\n                hasDragged.current = false;\n                return;\n            }\n            const handlePointerUp = {\n                \"DragHandle.useLayoutEffect.handlePointerUp\": ()=>{\n                    setIsDragging(false);\n                }\n            }[\"DragHandle.useLayoutEffect.handlePointerUp\"];\n            window.addEventListener(\"pointerup\", handlePointerUp);\n            return ({\n                \"DragHandle.useLayoutEffect\": ()=>{\n                    window.removeEventListener(\"pointerup\", handlePointerUp);\n                }\n            })[\"DragHandle.useLayoutEffect\"];\n        }\n    }[\"DragHandle.useLayoutEffect\"], [\n        isDragging\n    ]);\n    return /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"span\", {\n        draggable: true,\n        className: `${toolbar_module_default.dragHandle} ${isDragging ? toolbar_module_default.dragging : \"\"}`,\n        onPointerDown: (e)=>{\n            if (e.target instanceof HTMLElement && (e.target.nodeName.toLowerCase() === \"select\" || e.target.closest(\"select\"))) {\n                return;\n            }\n            const handle = e.currentTarget;\n            if (!handle) return;\n            e.stopPropagation();\n            e.preventDefault();\n            initialPointer.current = {\n                x: e.clientX,\n                y: e.clientY\n            };\n            const rect = handle.getBoundingClientRect();\n            initialToolbar.current.x = rect.left;\n            initialToolbar.current.y = rect.top;\n            setIsDragging(true);\n        },\n        onPointerUp: ()=>{\n            setIsDragging(false);\n        }\n    }, children);\n});\n// ../../node_modules/.pnpm/basehub@8.2.7_@babel+runtim_3aebfa8e064bb601162c04844d38dd02/node_modules/basehub/src/next/toolbar/components/branch-swticher.tsx\n\nvar BranchSwitcher = ({ isForcedDraft, draft, apiRref, latestBranches, onRefChange, getAndSetLatestBranches })=>{\n    const shadowRef = react__WEBPACK_IMPORTED_MODULE_1__.useRef(null);\n    const selectRef = react__WEBPACK_IMPORTED_MODULE_1__.useRef(null);\n    const sortedLatestBranches = react__WEBPACK_IMPORTED_MODULE_1__.useMemo({\n        \"BranchSwitcher.useMemo[sortedLatestBranches]\": ()=>{\n            return [\n                ...latestBranches\n            ].sort({\n                \"BranchSwitcher.useMemo[sortedLatestBranches]\": (a, b)=>{\n                    if (a.isDefault) return -1;\n                    if (b.isDefault) return 1;\n                    return a.name.localeCompare(b.name);\n                }\n            }[\"BranchSwitcher.useMemo[sortedLatestBranches]\"]);\n        }\n    }[\"BranchSwitcher.useMemo[sortedLatestBranches]\"], [\n        latestBranches\n    ]);\n    const refOptions = react__WEBPACK_IMPORTED_MODULE_1__.useMemo({\n        \"BranchSwitcher.useMemo[refOptions]\": ()=>{\n            const options = new Set(sortedLatestBranches.map({\n                \"BranchSwitcher.useMemo[refOptions]\": (branch)=>branch.name\n            }[\"BranchSwitcher.useMemo[refOptions]\"]));\n            options.add(apiRref);\n            return Array.from(options);\n        }\n    }[\"BranchSwitcher.useMemo[refOptions]\"], [\n        sortedLatestBranches,\n        apiRref\n    ]);\n    const [refetchLatestBranches, setRefetchLatestBranches] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"BranchSwitcher.useEffect\": ()=>{\n            if (refetchLatestBranches) {\n                getAndSetLatestBranches().then({\n                    \"BranchSwitcher.useEffect\": ()=>{\n                        setRefetchLatestBranches(false);\n                    }\n                }[\"BranchSwitcher.useEffect\"]);\n            }\n        }\n    }[\"BranchSwitcher.useEffect\"], [\n        refetchLatestBranches,\n        getAndSetLatestBranches\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"BranchSwitcher.useEffect\": ()=>{\n            const shadow = shadowRef.current;\n            const select = selectRef.current;\n            if (!shadow || !select) return;\n            const updateSelectWidth = {\n                \"BranchSwitcher.useEffect.updateSelectWidth\": ()=>{\n                    const width = shadow.offsetWidth;\n                    select.style.width = `${width + 20}px`;\n                }\n            }[\"BranchSwitcher.useEffect.updateSelectWidth\"];\n            updateSelectWidth();\n            window.addEventListener(\"resize\", updateSelectWidth);\n            return ({\n                \"BranchSwitcher.useEffect\": ()=>{\n                    window.removeEventListener(\"resize\", updateSelectWidth);\n                    if (select) {\n                        select.style.removeProperty(\"width\");\n                    }\n                }\n            })[\"BranchSwitcher.useEffect\"];\n        }\n    }[\"BranchSwitcher.useEffect\"], [\n        apiRref\n    ]);\n    const isDraftActive = isForcedDraft || draft;\n    return /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"div\", {\n        className: toolbar_module_default.branch,\n        \"data-draft-active\": isDraftActive,\n        onMouseEnter: ()=>{\n            setRefetchLatestBranches(true);\n        }\n    }, /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(BranchIcon, null), \"\\xA0\", /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(Tooltip, {\n        content: !isDraftActive ? \"Switch branch and enter draft mode\" : \"Switch branch\"\n    }, /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"select\", {\n        ref: selectRef,\n        value: apiRref,\n        onChange: (e)=>onRefChange(e.target.value, {\n                enableDraftMode: !isDraftActive\n            }),\n        className: toolbar_module_default.branchSelect,\n        onMouseDown: (e)=>{\n            e.stopPropagation();\n        },\n        onClick: (e)=>{\n            e.stopPropagation();\n            setRefetchLatestBranches(true);\n        }\n    }, refOptions.map((r)=>{\n        return /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"option\", {\n            key: r,\n            value: r\n        }, r);\n    })), /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"svg\", {\n        width: \"15\",\n        height: \"15\",\n        viewBox: \"0 0 15 15\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        className: toolbar_module_default.branchSelectIcon\n    }, /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"path\", {\n        d: \"M4.93179 5.43179C4.75605 5.60753 4.75605 5.89245 4.93179 6.06819C5.10753 6.24392 5.39245 6.24392 5.56819 6.06819L7.49999 4.13638L9.43179 6.06819C9.60753 6.24392 9.89245 6.24392 10.0682 6.06819C10.2439 5.89245 10.2439 5.60753 10.0682 5.43179L7.81819 3.18179C7.73379 3.0974 7.61933 3.04999 7.49999 3.04999C7.38064 3.04999 7.26618 3.0974 7.18179 3.18179L4.93179 5.43179ZM10.0682 9.56819C10.2439 9.39245 10.2439 9.10753 10.0682 8.93179C9.89245 8.75606 9.60753 8.75606 9.43179 8.93179L7.49999 10.8636L5.56819 8.93179C5.39245 8.75606 5.10753 8.75606 4.93179 8.93179C4.75605 9.10753 4.75605 9.39245 4.93179 9.56819L7.18179 11.8182C7.35753 11.9939 7.64245 11.9939 7.81819 11.8182L10.0682 9.56819Z\",\n        fill: \"currentColor\",\n        fillRule: \"evenodd\",\n        clipRule: \"evenodd\"\n    }))), /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"span\", {\n        className: toolbar_module_default.branchSelect,\n        style: {\n            visibility: \"hidden\",\n            opacity: 0,\n            pointerEvents: \"none\",\n            position: \"absolute\",\n            top: 0,\n            left: 0\n        },\n        \"aria-hidden\": \"true\",\n        ref: shadowRef\n    }, apiRref));\n};\nvar BranchIcon = ()=>{\n    return /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        width: \"18\",\n        height: \"18\",\n        fill: \"none\"\n    }, /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"path\", {\n        fill: \"#F3F3F3\",\n        fillRule: \"evenodd\",\n        d: \"M12.765 5.365a1.25 1.25 0 1 0 .002-2.502 1.25 1.25 0 0 0-.002 2.502Zm0 1.063a2.315 2.315 0 1 0-2.315-2.313 2.315 2.315 0 0 0 2.316 2.313ZM5.234 15.137a1.25 1.25 0 1 0 .001-2.501 1.25 1.25 0 0 0 0 2.501Zm0 1.064a2.315 2.315 0 1 0-2.316-2.314 2.315 2.315 0 0 0 2.316 2.314Z\",\n        clipRule: \"evenodd\"\n    }), /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"path\", {\n        fill: \"#F3F3F3\",\n        fillRule: \"evenodd\",\n        d: \"M5.767 8.98v3.648H4.702V8.98h1.065ZM13.298 5.798v2.694h-1.065V5.798h1.065Z\",\n        clipRule: \"evenodd\"\n    }), /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"path\", {\n        fill: \"#F3F3F3\",\n        fillRule: \"evenodd\",\n        d: \"M13.298 8.448a.532.532 0 0 1-.533.532H5.29a.532.532 0 1 1 0-1.064h7.476c.294 0 .533.238.533.532ZM5.234 2.864a1.25 1.25 0 1 1 .001 2.502 1.25 1.25 0 0 1 0-2.502Zm0-1.063a2.315 2.315 0 1 1-2.316 2.314A2.315 2.315 0 0 1 5.234 1.8Z\",\n        clipRule: \"evenodd\"\n    }), /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"path\", {\n        fill: \"#F3F3F3\",\n        fillRule: \"evenodd\",\n        d: \"M5.767 9.022V5.374H4.702v3.648h1.065Z\",\n        clipRule: \"evenodd\"\n    }));\n};\n// ../../node_modules/.pnpm/basehub@8.2.7_@babel+runtim_3aebfa8e064bb601162c04844d38dd02/node_modules/basehub/src/next/toolbar/client-toolbar.tsx\nvar import_lodash2 = (0,_chunk_YSQDPG26_js__WEBPACK_IMPORTED_MODULE_0__.__toESM)(require_lodash(), 1);\n\nvar TOOLBAR_POSITION_STORAGE_KEY = \"bshb_toolbar_pos\";\nvar ClientToolbar = ({ draft, isForcedDraft, enableDraftMode, disableDraftMode, bshbPreviewToken, shouldAutoEnableDraft, seekAndStoreBshbPreviewToken, resolvedRef, getLatestBranches })=>{\n    const [toolbarRef, setToolbarRef] = react__WEBPACK_IMPORTED_MODULE_1__.useState(null);\n    const dragHandleRef = react__WEBPACK_IMPORTED_MODULE_1__.useRef(null);\n    const tooltipRef = react__WEBPACK_IMPORTED_MODULE_1__.useRef(null);\n    const [message, setMessage] = react__WEBPACK_IMPORTED_MODULE_1__.useState(\"\");\n    const [loading, setLoading] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);\n    const [previewRef, _setPreviewRef] = react__WEBPACK_IMPORTED_MODULE_1__.useState(resolvedRef.ref);\n    const [isDefaultRefSelected, setIsDefaultRefSelected] = react__WEBPACK_IMPORTED_MODULE_1__.useState(true);\n    const [isLoadingRef, setIsLoadingRef] = react__WEBPACK_IMPORTED_MODULE_1__.useState(true);\n    const [latestBranches, setLatestBranches] = react__WEBPACK_IMPORTED_MODULE_1__.useState([]);\n    const currentMessageTimeout = react__WEBPACK_IMPORTED_MODULE_1__.useRef(0);\n    const displayMessage = react__WEBPACK_IMPORTED_MODULE_1__.useCallback({\n        \"ClientToolbar.useCallback[displayMessage]\": (message2)=>{\n            window.clearTimeout(currentMessageTimeout.current);\n            setMessage(message2);\n            currentMessageTimeout.current = window.setTimeout({\n                \"ClientToolbar.useCallback[displayMessage]\": ()=>setMessage(\"\")\n            }[\"ClientToolbar.useCallback[displayMessage]\"], 5e3);\n        }\n    }[\"ClientToolbar.useCallback[displayMessage]\"], [\n        setMessage\n    ]);\n    const triggerDraftMode = react__WEBPACK_IMPORTED_MODULE_1__.useCallback({\n        \"ClientToolbar.useCallback[triggerDraftMode]\": (previewToken)=>{\n            setLoading(true);\n            enableDraftMode({\n                bshbPreviewToken: previewToken\n            }).then({\n                \"ClientToolbar.useCallback[triggerDraftMode]\": ({ status, response })=>{\n                    if (status === 200) {\n                        setLatestBranches({\n                            \"ClientToolbar.useCallback[triggerDraftMode]\": (p)=>response.latestBranches ?? p\n                        }[\"ClientToolbar.useCallback[triggerDraftMode]\"]);\n                        window.location.reload();\n                    } else if (\"error\" in response) {\n                        displayMessage(`Draft mode activation error: ${response.error}`);\n                    } else {\n                        displayMessage(\"Draft mode activation error\");\n                    }\n                }\n            }[\"ClientToolbar.useCallback[triggerDraftMode]\"]).finally({\n                \"ClientToolbar.useCallback[triggerDraftMode]\": ()=>setLoading(false)\n            }[\"ClientToolbar.useCallback[triggerDraftMode]\"]);\n        }\n    }[\"ClientToolbar.useCallback[triggerDraftMode]\"], [\n        enableDraftMode,\n        displayMessage\n    ]);\n    const bshbPreviewRefCookieName = `bshb-preview-ref-${resolvedRef.repoHash}`;\n    const previewRefCookieManager = react__WEBPACK_IMPORTED_MODULE_1__.useMemo({\n        \"ClientToolbar.useMemo[previewRefCookieManager]\": ()=>({\n                set: ({\n                    \"ClientToolbar.useMemo[previewRefCookieManager]\": (ref)=>{\n                        document.cookie = `${bshbPreviewRefCookieName}=${ref}; path=/; Max-Age=${60 * 60 * 24 * 30 * 365}`;\n                    }\n                })[\"ClientToolbar.useMemo[previewRefCookieManager]\"],\n                clear: ({\n                    \"ClientToolbar.useMemo[previewRefCookieManager]\": ()=>{\n                        document.cookie = `${bshbPreviewRefCookieName}=; path=/; Max-Age=-1`;\n                    }\n                })[\"ClientToolbar.useMemo[previewRefCookieManager]\"],\n                get: ({\n                    \"ClientToolbar.useMemo[previewRefCookieManager]\": ()=>{\n                        return document.cookie.split(\"; \").find({\n                            \"ClientToolbar.useMemo[previewRefCookieManager]\": (row)=>row.startsWith(bshbPreviewRefCookieName)\n                        }[\"ClientToolbar.useMemo[previewRefCookieManager]\"])?.split(\"=\")[1] ?? null;\n                    }\n                })[\"ClientToolbar.useMemo[previewRefCookieManager]\"]\n            })\n    }[\"ClientToolbar.useMemo[previewRefCookieManager]\"], [\n        bshbPreviewRefCookieName\n    ]);\n    const [hasAutoEnabledDraftOnce, setHasAutoEnabledDraftOnce] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);\n    react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect({\n        \"ClientToolbar.useLayoutEffect\": ()=>{\n            if (draft || hasAutoEnabledDraftOnce || !shouldAutoEnableDraft || isForcedDraft || !bshbPreviewToken) {\n                return;\n            }\n            triggerDraftMode(bshbPreviewToken);\n            setHasAutoEnabledDraftOnce(true);\n        }\n    }[\"ClientToolbar.useLayoutEffect\"], [\n        isForcedDraft,\n        enableDraftMode,\n        seekAndStoreBshbPreviewToken,\n        bshbPreviewToken,\n        displayMessage,\n        triggerDraftMode,\n        draft,\n        shouldAutoEnableDraft,\n        hasAutoEnabledDraftOnce\n    ]);\n    const getAndSetLatestBranches = react__WEBPACK_IMPORTED_MODULE_1__.useCallback({\n        \"ClientToolbar.useCallback[getAndSetLatestBranches]\": async ()=>{\n            let result = [];\n            const res = await getLatestBranches({\n                bshbPreviewToken\n            });\n            if (!res) return;\n            if (Array.isArray(res.response)) {\n                result = res.response;\n            } else if (\"error\" in res.response) {\n                console.error(`BaseHub Toolbar Error: ${res.response.error}`);\n            }\n            setLatestBranches(result);\n        }\n    }[\"ClientToolbar.useCallback[getAndSetLatestBranches]\"], [\n        bshbPreviewToken,\n        getLatestBranches\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"ClientToolbar.useEffect\": ()=>{\n            async function effect() {\n                while(true){\n                    try {\n                        getAndSetLatestBranches();\n                        await new Promise({\n                            \"ClientToolbar.useEffect.effect\": (resolve)=>setTimeout(resolve, 3e4)\n                        }[\"ClientToolbar.useEffect.effect\"]);\n                    } catch (error) {\n                        console.error(`BaseHub Toolbar Error: ${error}`);\n                        break;\n                    }\n                }\n            }\n            effect();\n        }\n    }[\"ClientToolbar.useEffect\"], [\n        getAndSetLatestBranches\n    ]);\n    const setRefWithEvents = react__WEBPACK_IMPORTED_MODULE_1__.useCallback({\n        \"ClientToolbar.useCallback[setRefWithEvents]\": (ref)=>{\n            _setPreviewRef(ref);\n            window.__bshb_ref = ref;\n            window.dispatchEvent(new CustomEvent(\"__bshb_ref_changed\"));\n            previewRefCookieManager.set(ref);\n            setIsDefaultRefSelected(ref === resolvedRef.ref);\n        }\n    }[\"ClientToolbar.useCallback[setRefWithEvents]\"], [\n        previewRefCookieManager,\n        resolvedRef.ref\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"ClientToolbar.useEffect\": ()=>{\n            const url = new URL(window.location.href);\n            let previewRef2 = url.searchParams.get(\"bshb-preview-ref\");\n            if (!previewRef2) {\n                previewRef2 = previewRefCookieManager.get();\n            }\n            setIsLoadingRef(false);\n            if (!previewRef2) return;\n            setRefWithEvents(previewRef2);\n        }\n    }[\"ClientToolbar.useEffect\"], [\n        previewRefCookieManager,\n        setRefWithEvents,\n        resolvedRef.repoHash\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"ClientToolbar.useEffect\": ()=>{\n            if (isLoadingRef) return;\n            setIsDefaultRefSelected(previewRef === resolvedRef.ref);\n        }\n    }[\"ClientToolbar.useEffect\"], [\n        previewRef,\n        resolvedRef.ref,\n        isLoadingRef\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"ClientToolbar.useEffect\": ()=>{\n            if (isLoadingRef) return;\n            if (isDefaultRefSelected) {\n                setRefWithEvents(resolvedRef.ref);\n                previewRefCookieManager.clear();\n                const url = new URL(window.location.href);\n                url.searchParams.delete(\"bshb-preview-ref\");\n                window.history.replaceState(null, \"\", url.toString());\n            }\n        }\n    }[\"ClientToolbar.useEffect\"], [\n        isDefaultRefSelected,\n        isLoadingRef,\n        previewRefCookieManager,\n        resolvedRef.ref,\n        setRefWithEvents\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect({\n        \"ClientToolbar.useLayoutEffect\": ()=>{\n            tooltipRef.current?.checkOverflow();\n        }\n    }[\"ClientToolbar.useLayoutEffect\"], [\n        message\n    ]);\n    const getStoredToolbarPosition = react__WEBPACK_IMPORTED_MODULE_1__.useCallback({\n        \"ClientToolbar.useCallback[getStoredToolbarPosition]\": ()=>{\n            if (!toolbarRef) return;\n            if (true) return;\n            const toolbarPositionStored = window.sessionStorage.getItem(TOOLBAR_POSITION_STORAGE_KEY);\n            if (!toolbarPositionStored) return;\n            const toolbarPosition = JSON.parse(toolbarPositionStored);\n            if (!(\"x\" in toolbarPosition)) return;\n            if (!(\"y\" in toolbarPosition)) return;\n            return toolbarPosition;\n        }\n    }[\"ClientToolbar.useCallback[getStoredToolbarPosition]\"], [\n        toolbarRef\n    ]);\n    const updateToolbarStoredPositionDebounced = react__WEBPACK_IMPORTED_MODULE_1__.useCallback((0, import_lodash2.default)({\n        \"ClientToolbar.useCallback[updateToolbarStoredPositionDebounced]\": (position)=>{\n            if (true) return;\n            const storedPosition = getStoredToolbarPosition() ?? {\n                x: 0,\n                y: 0\n            };\n            window.sessionStorage.setItem(TOOLBAR_POSITION_STORAGE_KEY, JSON.stringify({\n                ...storedPosition,\n                ...position\n            }));\n        }\n    }[\"ClientToolbar.useCallback[updateToolbarStoredPositionDebounced]\"], 250), []);\n    const dragToolbar = react__WEBPACK_IMPORTED_MODULE_1__.useCallback({\n        \"ClientToolbar.useCallback[dragToolbar]\": (position)=>{\n            const toolbar = toolbarRef;\n            if (!toolbar) return;\n            const rect = toolbar.getBoundingClientRect();\n            const padding = 32;\n            const newPositionForStore = {};\n            if (position.x - padding < 0) {\n                toolbar.style.left = `${padding}px`;\n                toolbar.style.right = \"unset\";\n                newPositionForStore.x = padding;\n            } else if (position.x + rect.width + padding > window.innerWidth) {\n                toolbar.style.right = `${padding}px`;\n                toolbar.style.left = \"unset\";\n                newPositionForStore.x = padding;\n            } else {\n                toolbar.style.right = \"unset\";\n                toolbar.style.left = `${position.x}px`;\n                newPositionForStore.x = position.x;\n            }\n            if (position.y - padding < 0) {\n                toolbar.style.bottom = \"unset\";\n                toolbar.style.top = `${padding}px`;\n                newPositionForStore.y = padding;\n            } else if (position.y + rect.height + padding > window.innerHeight) {\n                toolbar.style.top = \"unset\";\n                toolbar.style.bottom = `${padding}px`;\n                newPositionForStore.y = padding;\n            } else {\n                toolbar.style.bottom = \"unset\";\n                toolbar.style.top = `${position.y}px`;\n                newPositionForStore.x = position.y;\n            }\n            updateToolbarStoredPositionDebounced({\n                x: position.x,\n                y: position.y\n            });\n        }\n    }[\"ClientToolbar.useCallback[dragToolbar]\"], [\n        toolbarRef,\n        updateToolbarStoredPositionDebounced\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"ClientToolbar.useEffect\": ()=>{\n            if (true) return;\n            const repositionToolbar = {\n                \"ClientToolbar.useEffect.repositionToolbar\": ()=>{\n                    const pos = getStoredToolbarPosition();\n                    if (!pos) return;\n                    dragToolbar(pos);\n                    tooltipRef.current?.checkOverflow();\n                }\n            }[\"ClientToolbar.useEffect.repositionToolbar\"];\n            repositionToolbar();\n            window.addEventListener(\"resize\", repositionToolbar);\n            return ({\n                \"ClientToolbar.useEffect\": ()=>{\n                    window.removeEventListener(\"resize\", repositionToolbar);\n                }\n            })[\"ClientToolbar.useEffect\"];\n        }\n    }[\"ClientToolbar.useEffect\"], [\n        getStoredToolbarPosition,\n        dragToolbar\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"ClientToolbar.useEffect\": ()=>{\n            if (!latestBranches) return;\n            const fromCookie = previewRefCookieManager.get();\n            if (!fromCookie) return;\n            if (!latestBranches.find({\n                \"ClientToolbar.useEffect\": (branch)=>branch.name === fromCookie\n            }[\"ClientToolbar.useEffect\"])) {\n                previewRefCookieManager.clear();\n            }\n        }\n    }[\"ClientToolbar.useEffect\"], [\n        latestBranches,\n        previewRefCookieManager\n    ]);\n    const tooltip = isForcedDraft ? \"Draft enforced by dev env\" : `${draft ? \"Disable\" : \"Enable\"} draft mode`;\n    return /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"div\", {\n        className: toolbar_module_default.wrapper,\n        ref: setToolbarRef\n    }, /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(DragHandle, {\n        ref: dragHandleRef,\n        onDrag: (pos)=>{\n            dragToolbar(pos);\n            tooltipRef.current?.checkOverflow();\n        }\n    }, /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"div\", {\n        className: toolbar_module_default.root,\n        \"data-draft-active\": isForcedDraft || draft\n    }, /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(BranchSwitcher, {\n        isForcedDraft,\n        draft,\n        apiRref: previewRef,\n        latestBranches,\n        onRefChange: (newRef, opts)=>{\n            const url = new URL(window.location.href);\n            url.searchParams.set(\"bshb-preview-ref\", newRef);\n            window.history.replaceState(null, \"\", url.toString());\n            setRefWithEvents(newRef);\n            if (opts.enableDraftMode) {\n                const previewToken = bshbPreviewToken ?? seekAndStoreBshbPreviewToken();\n                if (!previewToken) {\n                    return displayMessage(\"Preview token not found\");\n                }\n                triggerDraftMode(previewToken);\n            }\n        },\n        getAndSetLatestBranches\n    }), /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(AutoAddRefToUrlOnPathChangeIfRefIsNotDefault, {\n        previewRef,\n        resolvedRef,\n        isDraftModeEnabled: isForcedDraft || draft\n    }), /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(Tooltip, {\n        content: message || tooltip,\n        ref: tooltipRef,\n        forceVisible: Boolean(message)\n    }, /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"button\", {\n        className: toolbar_module_default.draft,\n        \"data-active\": isForcedDraft || draft,\n        \"aria-label\": `${draft ? \"Disable\" : \"Enable\"} draft mode`,\n        \"data-loading\": loading,\n        disabled: isForcedDraft || loading,\n        onClick: ()=>{\n            if (loading || dragHandleRef.current?.hasDragged) return;\n            if (draft) {\n                setLoading(true);\n                disableDraftMode().then(()=>{\n                    const url = new URL(window.location.href);\n                    url.searchParams.delete(\"bshb-preview\");\n                    url.searchParams.delete(\"__vercel_draft\");\n                    window.location.href = url.toString();\n                }).finally(()=>setLoading(false));\n            } else {\n                const previewToken = bshbPreviewToken ?? seekAndStoreBshbPreviewToken();\n                if (!previewToken) {\n                    return displayMessage(\"Preview token not found\");\n                }\n                triggerDraftMode(previewToken);\n            }\n        }\n    }, draft || isForcedDraft ? /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(EyeIcon, null) : /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(EyeDashedIcon, null))))));\n};\nvar AutoAddRefToUrlOnPathChangeIfRefIsNotDefault = ({ previewRef, resolvedRef, isDraftModeEnabled })=>{\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const [initialPathname, setInitialPathname] = react__WEBPACK_IMPORTED_MODULE_1__.useState(pathname);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"AutoAddRefToUrlOnPathChangeIfRefIsNotDefault.useEffect\": ()=>{\n            if (initialPathname) return;\n            setInitialPathname(pathname);\n        }\n    }[\"AutoAddRefToUrlOnPathChangeIfRefIsNotDefault.useEffect\"], [\n        pathname,\n        initialPathname\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"AutoAddRefToUrlOnPathChangeIfRefIsNotDefault.useEffect\": ()=>{\n            if (isDraftModeEnabled) return;\n            if (initialPathname === pathname) {\n                return;\n            }\n            if (previewRef !== resolvedRef.ref) {\n                const url = new URL(window.location.href);\n                url.searchParams.set(\"bshb-preview-ref\", previewRef);\n                window.history.replaceState(null, \"\", url.toString());\n            }\n        }\n    }[\"AutoAddRefToUrlOnPathChangeIfRefIsNotDefault.useEffect\"], [\n        isDraftModeEnabled,\n        previewRef,\n        resolvedRef.ref,\n        pathname,\n        initialPathname\n    ]);\n    return null;\n};\nvar EyeDashedIcon = ()=>{\n    return /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"svg\", {\n        \"data-testid\": \"geist-icon\",\n        height: \"16\",\n        strokeLinejoin: \"round\",\n        viewBox: \"0 0 16 16\",\n        width: \"16\",\n        style: {\n            color: \"currentcolor\"\n        }\n    }, /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"path\", {\n        fillRule: \"evenodd\",\n        clipRule: \"evenodd\",\n        d: \"M6.51404 3.15793C7.48217 2.87411 8.51776 2.87411 9.48589 3.15793L9.90787 1.71851C8.66422 1.35392 7.33571 1.35392 6.09206 1.71851L6.51404 3.15793ZM10.848 3.78166C11.2578 4.04682 11.6393 4.37568 11.9783 4.76932L13.046 6.00934L14.1827 5.03056L13.1149 3.79054C12.6818 3.28761 12.1918 2.86449 11.6628 2.52224L10.848 3.78166ZM4.02168 4.76932C4.36065 4.37568 4.74209 4.04682 5.15195 3.78166L4.33717 2.52225C3.80815 2.86449 3.3181 3.28761 2.88503 3.79054L1.81723 5.03056L2.95389 6.00934L4.02168 4.76932ZM14.1138 7.24936L14.7602 7.99999L14.1138 8.75062L15.2505 9.72941L16.3183 8.48938V7.5106L15.2505 6.27058L14.1138 7.24936ZM1.88609 7.24936L1.23971 7.99999L1.88609 8.75062L0.749437 9.72941L-0.318359 8.48938V7.5106L0.749436 6.27058L1.88609 7.24936ZM13.0461 9.99064L11.9783 11.2307C11.6393 11.6243 11.2578 11.9532 10.848 12.2183L11.6628 13.4777C12.1918 13.1355 12.6818 12.7124 13.1149 12.2094L14.1827 10.9694L13.0461 9.99064ZM4.02168 11.2307L2.95389 9.99064L1.81723 10.9694L2.88503 12.2094C3.3181 12.7124 3.80815 13.1355 4.33717 13.4777L5.15195 12.2183C4.7421 11.9532 4.36065 11.6243 4.02168 11.2307ZM9.90787 14.2815L9.48589 12.8421C8.51776 13.1259 7.48217 13.1259 6.51405 12.8421L6.09206 14.2815C7.33572 14.6461 8.66422 14.6461 9.90787 14.2815ZM6.49997 7.99999C6.49997 7.17157 7.17154 6.49999 7.99997 6.49999C8.82839 6.49999 9.49997 7.17157 9.49997 7.99999C9.49997 8.82842 8.82839 9.49999 7.99997 9.49999C7.17154 9.49999 6.49997 8.82842 6.49997 7.99999ZM7.99997 4.99999C6.34311 4.99999 4.99997 6.34314 4.99997 7.99999C4.99997 9.65685 6.34311 11 7.99997 11C9.65682 11 11 9.65685 11 7.99999C11 6.34314 9.65682 4.99999 7.99997 4.99999Z\",\n        fill: \"currentColor\"\n    }));\n};\nvar EyeIcon = ()=>{\n    return /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"svg\", {\n        \"data-testid\": \"geist-icon\",\n        height: \"16\",\n        strokeLinejoin: \"round\",\n        viewBox: \"0 0 16 16\",\n        width: \"16\",\n        style: {\n            color: \"currentcolor\"\n        }\n    }, /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"path\", {\n        fillRule: \"evenodd\",\n        clipRule: \"evenodd\",\n        d: \"M4.02168 4.76932C6.11619 2.33698 9.88374 2.33698 11.9783 4.76932L14.7602 7.99999L11.9783 11.2307C9.88374 13.663 6.1162 13.663 4.02168 11.2307L1.23971 7.99999L4.02168 4.76932ZM13.1149 3.79054C10.422 0.663244 5.57797 0.663247 2.88503 3.79054L-0.318359 7.5106V8.48938L2.88503 12.2094C5.57797 15.3367 10.422 15.3367 13.1149 12.2094L16.3183 8.48938V7.5106L13.1149 3.79054ZM6.49997 7.99999C6.49997 7.17157 7.17154 6.49999 7.99997 6.49999C8.82839 6.49999 9.49997 7.17157 9.49997 7.99999C9.49997 8.82842 8.82839 9.49999 7.99997 9.49999C7.17154 9.49999 6.49997 8.82842 6.49997 7.99999ZM7.99997 4.99999C6.34311 4.99999 4.99997 6.34314 4.99997 7.99999C4.99997 9.65685 6.34311 11 7.99997 11C9.65682 11 11 9.65685 11 7.99999C11 6.34314 9.65682 4.99999 7.99997 4.99999Z\",\n        fill: \"currentColor\"\n    }));\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/cms/.basehub/next-toolbar/client-toolbar-CIQDQ5LJ.js\n");

/***/ })

};
;