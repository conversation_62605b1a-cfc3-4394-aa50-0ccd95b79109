"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073";
exports.ids = ["vendor-chunks/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073"];
exports.modules = {

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/autoLoader.js":
/*!*****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/autoLoader.js ***!
  \*****************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   registerInstrumentations: () => (/* binding */ registerInstrumentations)\n/* harmony export */ });\n/* harmony import */ var _opentelemetry_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @opentelemetry/api */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/trace-api.js\");\n/* harmony import */ var _opentelemetry_api__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @opentelemetry/api */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/metrics-api.js\");\n/* harmony import */ var _opentelemetry_api_logs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @opentelemetry/api-logs */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+api-logs@0.57.2/node_modules/@opentelemetry/api-logs/build/esm/index.js\");\n/* harmony import */ var _autoLoaderUtils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./autoLoaderUtils */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/autoLoaderUtils.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n\n\n/**\n * It will register instrumentations and plugins\n * @param options\n * @return returns function to unload instrumentation and plugins that were\n *   registered\n */\nfunction registerInstrumentations(options) {\n    var _a, _b;\n    var tracerProvider = options.tracerProvider || _opentelemetry_api__WEBPACK_IMPORTED_MODULE_0__.trace.getTracerProvider();\n    var meterProvider = options.meterProvider || _opentelemetry_api__WEBPACK_IMPORTED_MODULE_1__.metrics.getMeterProvider();\n    var loggerProvider = options.loggerProvider || _opentelemetry_api_logs__WEBPACK_IMPORTED_MODULE_2__.logs.getLoggerProvider();\n    var instrumentations = (_b = (_a = options.instrumentations) === null || _a === void 0 ? void 0 : _a.flat()) !== null && _b !== void 0 ? _b : [];\n    (0,_autoLoaderUtils__WEBPACK_IMPORTED_MODULE_3__.enableInstrumentations)(instrumentations, tracerProvider, meterProvider, loggerProvider);\n    return function () {\n        (0,_autoLoaderUtils__WEBPACK_IMPORTED_MODULE_3__.disableInstrumentations)(instrumentations);\n    };\n}\n//# sourceMappingURL=autoLoader.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/autoLoader.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/autoLoaderUtils.js":
/*!**********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/autoLoaderUtils.js ***!
  \**********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   disableInstrumentations: () => (/* binding */ disableInstrumentations),\n/* harmony export */   enableInstrumentations: () => (/* binding */ enableInstrumentations)\n/* harmony export */ });\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Enable instrumentations\n * @param instrumentations\n * @param tracerProvider\n * @param meterProvider\n */\nfunction enableInstrumentations(instrumentations, tracerProvider, meterProvider, loggerProvider) {\n    for (var i = 0, j = instrumentations.length; i < j; i++) {\n        var instrumentation = instrumentations[i];\n        if (tracerProvider) {\n            instrumentation.setTracerProvider(tracerProvider);\n        }\n        if (meterProvider) {\n            instrumentation.setMeterProvider(meterProvider);\n        }\n        if (loggerProvider && instrumentation.setLoggerProvider) {\n            instrumentation.setLoggerProvider(loggerProvider);\n        }\n        // instrumentations have been already enabled during creation\n        // so enable only if user prevented that by setting enabled to false\n        // this is to prevent double enabling but when calling register all\n        // instrumentations should be now enabled\n        if (!instrumentation.getConfig().enabled) {\n            instrumentation.enable();\n        }\n    }\n}\n/**\n * Disable instrumentations\n * @param instrumentations\n */\nfunction disableInstrumentations(instrumentations) {\n    instrumentations.forEach(function (instrumentation) { return instrumentation.disable(); });\n}\n//# sourceMappingURL=autoLoaderUtils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/autoLoaderUtils.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js":
/*!************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js ***!
  \************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InstrumentationBase: () => (/* reexport safe */ _platform_index__WEBPACK_IMPORTED_MODULE_1__.InstrumentationBase),\n/* harmony export */   InstrumentationNodeModuleDefinition: () => (/* reexport safe */ _instrumentationNodeModuleDefinition__WEBPACK_IMPORTED_MODULE_2__.InstrumentationNodeModuleDefinition),\n/* harmony export */   InstrumentationNodeModuleFile: () => (/* reexport safe */ _instrumentationNodeModuleFile__WEBPACK_IMPORTED_MODULE_3__.InstrumentationNodeModuleFile),\n/* harmony export */   isWrapped: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_4__.isWrapped),\n/* harmony export */   registerInstrumentations: () => (/* reexport safe */ _autoLoader__WEBPACK_IMPORTED_MODULE_0__.registerInstrumentations),\n/* harmony export */   safeExecuteInTheMiddle: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_4__.safeExecuteInTheMiddle),\n/* harmony export */   safeExecuteInTheMiddleAsync: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_4__.safeExecuteInTheMiddleAsync)\n/* harmony export */ });\n/* harmony import */ var _autoLoader__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./autoLoader */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/autoLoader.js\");\n/* harmony import */ var _platform_index__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./platform/index */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/instrumentation.js\");\n/* harmony import */ var _instrumentationNodeModuleDefinition__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./instrumentationNodeModuleDefinition */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/instrumentationNodeModuleDefinition.js\");\n/* harmony import */ var _instrumentationNodeModuleFile__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./instrumentationNodeModuleFile */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/instrumentationNodeModuleFile.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/utils.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n\n\n\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/instrumentation.js":
/*!**********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/instrumentation.js ***!
  \**********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InstrumentationAbstract: () => (/* binding */ InstrumentationAbstract)\n/* harmony export */ });\n/* harmony import */ var _opentelemetry_api__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @opentelemetry/api */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/diag-api.js\");\n/* harmony import */ var _opentelemetry_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @opentelemetry/api */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/trace-api.js\");\n/* harmony import */ var _opentelemetry_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @opentelemetry/api */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/metrics-api.js\");\n/* harmony import */ var _opentelemetry_api_logs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @opentelemetry/api-logs */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+api-logs@0.57.2/node_modules/@opentelemetry/api-logs/build/esm/index.js\");\n/* harmony import */ var shimmer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! shimmer */ \"(instrument)/../../node_modules/.pnpm/shimmer@1.2.1/node_modules/shimmer/index.js\");\n/* harmony import */ var shimmer__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(shimmer__WEBPACK_IMPORTED_MODULE_0__);\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __assign = (undefined && undefined.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\n\n\n\n/**\n * Base abstract internal class for instrumenting node and web plugins\n */\nvar InstrumentationAbstract = /** @class */ (function () {\n    function InstrumentationAbstract(instrumentationName, instrumentationVersion, config) {\n        this.instrumentationName = instrumentationName;\n        this.instrumentationVersion = instrumentationVersion;\n        this._config = {};\n        /* Api to wrap instrumented method */\n        this._wrap = shimmer__WEBPACK_IMPORTED_MODULE_0__.wrap;\n        /* Api to unwrap instrumented methods */\n        this._unwrap = shimmer__WEBPACK_IMPORTED_MODULE_0__.unwrap;\n        /* Api to mass wrap instrumented method */\n        this._massWrap = shimmer__WEBPACK_IMPORTED_MODULE_0__.massWrap;\n        /* Api to mass unwrap instrumented methods */\n        this._massUnwrap = shimmer__WEBPACK_IMPORTED_MODULE_0__.massUnwrap;\n        this.setConfig(config);\n        this._diag = _opentelemetry_api__WEBPACK_IMPORTED_MODULE_1__.diag.createComponentLogger({\n            namespace: instrumentationName,\n        });\n        this._tracer = _opentelemetry_api__WEBPACK_IMPORTED_MODULE_2__.trace.getTracer(instrumentationName, instrumentationVersion);\n        this._meter = _opentelemetry_api__WEBPACK_IMPORTED_MODULE_3__.metrics.getMeter(instrumentationName, instrumentationVersion);\n        this._logger = _opentelemetry_api_logs__WEBPACK_IMPORTED_MODULE_4__.logs.getLogger(instrumentationName, instrumentationVersion);\n        this._updateMetricInstruments();\n    }\n    Object.defineProperty(InstrumentationAbstract.prototype, \"meter\", {\n        /* Returns meter */\n        get: function () {\n            return this._meter;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    /**\n     * Sets MeterProvider to this plugin\n     * @param meterProvider\n     */\n    InstrumentationAbstract.prototype.setMeterProvider = function (meterProvider) {\n        this._meter = meterProvider.getMeter(this.instrumentationName, this.instrumentationVersion);\n        this._updateMetricInstruments();\n    };\n    Object.defineProperty(InstrumentationAbstract.prototype, \"logger\", {\n        /* Returns logger */\n        get: function () {\n            return this._logger;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    /**\n     * Sets LoggerProvider to this plugin\n     * @param loggerProvider\n     */\n    InstrumentationAbstract.prototype.setLoggerProvider = function (loggerProvider) {\n        this._logger = loggerProvider.getLogger(this.instrumentationName, this.instrumentationVersion);\n    };\n    /**\n     * @experimental\n     *\n     * Get module definitions defined by {@link init}.\n     * This can be used for experimental compile-time instrumentation.\n     *\n     * @returns an array of {@link InstrumentationModuleDefinition}\n     */\n    InstrumentationAbstract.prototype.getModuleDefinitions = function () {\n        var _a;\n        var initResult = (_a = this.init()) !== null && _a !== void 0 ? _a : [];\n        if (!Array.isArray(initResult)) {\n            return [initResult];\n        }\n        return initResult;\n    };\n    /**\n     * Sets the new metric instruments with the current Meter.\n     */\n    InstrumentationAbstract.prototype._updateMetricInstruments = function () {\n        return;\n    };\n    /* Returns InstrumentationConfig */\n    InstrumentationAbstract.prototype.getConfig = function () {\n        return this._config;\n    };\n    /**\n     * Sets InstrumentationConfig to this plugin\n     * @param config\n     */\n    InstrumentationAbstract.prototype.setConfig = function (config) {\n        // copy config first level properties to ensure they are immutable.\n        // nested properties are not copied, thus are mutable from the outside.\n        this._config = __assign({ enabled: true }, config);\n    };\n    /**\n     * Sets TraceProvider to this plugin\n     * @param tracerProvider\n     */\n    InstrumentationAbstract.prototype.setTracerProvider = function (tracerProvider) {\n        this._tracer = tracerProvider.getTracer(this.instrumentationName, this.instrumentationVersion);\n    };\n    Object.defineProperty(InstrumentationAbstract.prototype, \"tracer\", {\n        /* Returns tracer */\n        get: function () {\n            return this._tracer;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    /**\n     * Execute span customization hook, if configured, and log any errors.\n     * Any semantics of the trigger and info are defined by the specific instrumentation.\n     * @param hookHandler The optional hook handler which the user has configured via instrumentation config\n     * @param triggerName The name of the trigger for executing the hook for logging purposes\n     * @param span The span to which the hook should be applied\n     * @param info The info object to be passed to the hook, with useful data the hook may use\n     */\n    InstrumentationAbstract.prototype._runSpanCustomizationHook = function (hookHandler, triggerName, span, info) {\n        if (!hookHandler) {\n            return;\n        }\n        try {\n            hookHandler(span, info);\n        }\n        catch (e) {\n            this._diag.error(\"Error running span customization hook due to exception in handler\", { triggerName: triggerName }, e);\n        }\n    };\n    return InstrumentationAbstract;\n}());\n\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/instrumentation.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/instrumentationNodeModuleDefinition.js":
/*!******************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/instrumentationNodeModuleDefinition.js ***!
  \******************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InstrumentationNodeModuleDefinition: () => (/* binding */ InstrumentationNodeModuleDefinition)\n/* harmony export */ });\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar InstrumentationNodeModuleDefinition = /** @class */ (function () {\n    function InstrumentationNodeModuleDefinition(name, supportedVersions, \n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    patch, \n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    unpatch, files) {\n        this.name = name;\n        this.supportedVersions = supportedVersions;\n        this.patch = patch;\n        this.unpatch = unpatch;\n        this.files = files || [];\n    }\n    return InstrumentationNodeModuleDefinition;\n}());\n\n//# sourceMappingURL=instrumentationNodeModuleDefinition.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/instrumentationNodeModuleDefinition.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/instrumentationNodeModuleFile.js":
/*!************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/instrumentationNodeModuleFile.js ***!
  \************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InstrumentationNodeModuleFile: () => (/* binding */ InstrumentationNodeModuleFile)\n/* harmony export */ });\n/* harmony import */ var _platform_index__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./platform/index */ \"path\");\n/* harmony import */ var _platform_index__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_platform_index__WEBPACK_IMPORTED_MODULE_0__);\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nvar InstrumentationNodeModuleFile = /** @class */ (function () {\n    function InstrumentationNodeModuleFile(name, supportedVersions, \n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    patch, \n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    unpatch) {\n        this.supportedVersions = supportedVersions;\n        this.patch = patch;\n        this.unpatch = unpatch;\n        this.name = (0,_platform_index__WEBPACK_IMPORTED_MODULE_0__.normalize)(name);\n    }\n    return InstrumentationNodeModuleFile;\n}());\n\n//# sourceMappingURL=instrumentationNodeModuleFile.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/instrumentationNodeModuleFile.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/ModuleNameTrie.js":
/*!***********************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/ModuleNameTrie.js ***!
  \***********************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ModuleNameSeparator: () => (/* binding */ ModuleNameSeparator),\n/* harmony export */   ModuleNameTrie: () => (/* binding */ ModuleNameTrie)\n/* harmony export */ });\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __values = (undefined && undefined.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nvar __read = (undefined && undefined.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (undefined && undefined.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nvar ModuleNameSeparator = '/';\n/**\n * Node in a `ModuleNameTrie`\n */\nvar ModuleNameTrieNode = /** @class */ (function () {\n    function ModuleNameTrieNode() {\n        this.hooks = [];\n        this.children = new Map();\n    }\n    return ModuleNameTrieNode;\n}());\n/**\n * Trie containing nodes that represent a part of a module name (i.e. the parts separated by forward slash)\n */\nvar ModuleNameTrie = /** @class */ (function () {\n    function ModuleNameTrie() {\n        this._trie = new ModuleNameTrieNode();\n        this._counter = 0;\n    }\n    /**\n     * Insert a module hook into the trie\n     *\n     * @param {Hooked} hook Hook\n     */\n    ModuleNameTrie.prototype.insert = function (hook) {\n        var e_1, _a;\n        var trieNode = this._trie;\n        try {\n            for (var _b = __values(hook.moduleName.split(ModuleNameSeparator)), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var moduleNamePart = _c.value;\n                var nextNode = trieNode.children.get(moduleNamePart);\n                if (!nextNode) {\n                    nextNode = new ModuleNameTrieNode();\n                    trieNode.children.set(moduleNamePart, nextNode);\n                }\n                trieNode = nextNode;\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        trieNode.hooks.push({ hook: hook, insertedId: this._counter++ });\n    };\n    /**\n     * Search for matching hooks in the trie\n     *\n     * @param {string} moduleName Module name\n     * @param {boolean} maintainInsertionOrder Whether to return the results in insertion order\n     * @param {boolean} fullOnly Whether to return only full matches\n     * @returns {Hooked[]} Matching hooks\n     */\n    ModuleNameTrie.prototype.search = function (moduleName, _a) {\n        var e_2, _b;\n        var _c = _a === void 0 ? {} : _a, maintainInsertionOrder = _c.maintainInsertionOrder, fullOnly = _c.fullOnly;\n        var trieNode = this._trie;\n        var results = [];\n        var foundFull = true;\n        try {\n            for (var _d = __values(moduleName.split(ModuleNameSeparator)), _e = _d.next(); !_e.done; _e = _d.next()) {\n                var moduleNamePart = _e.value;\n                var nextNode = trieNode.children.get(moduleNamePart);\n                if (!nextNode) {\n                    foundFull = false;\n                    break;\n                }\n                if (!fullOnly) {\n                    results.push.apply(results, __spreadArray([], __read(nextNode.hooks), false));\n                }\n                trieNode = nextNode;\n            }\n        }\n        catch (e_2_1) { e_2 = { error: e_2_1 }; }\n        finally {\n            try {\n                if (_e && !_e.done && (_b = _d.return)) _b.call(_d);\n            }\n            finally { if (e_2) throw e_2.error; }\n        }\n        if (fullOnly && foundFull) {\n            results.push.apply(results, __spreadArray([], __read(trieNode.hooks), false));\n        }\n        if (results.length === 0) {\n            return [];\n        }\n        if (results.length === 1) {\n            return [results[0].hook];\n        }\n        if (maintainInsertionOrder) {\n            results.sort(function (a, b) { return a.insertedId - b.insertedId; });\n        }\n        return results.map(function (_a) {\n            var hook = _a.hook;\n            return hook;\n        });\n    };\n    return ModuleNameTrie;\n}());\n\n//# sourceMappingURL=ModuleNameTrie.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/ModuleNameTrie.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/RequireInTheMiddleSingleton.js":
/*!************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/RequireInTheMiddleSingleton.js ***!
  \************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RequireInTheMiddleSingleton: () => (/* binding */ RequireInTheMiddleSingleton)\n/* harmony export */ });\n/* harmony import */ var require_in_the_middle__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! require-in-the-middle */ \"require-in-the-middle\");\n/* harmony import */ var require_in_the_middle__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(require_in_the_middle__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ModuleNameTrie__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ModuleNameTrie */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/ModuleNameTrie.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __values = (undefined && undefined.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n\n\n\n/**\n * Whether Mocha is running in this process\n * Inspired by https://github.com/AndreasPizsa/detect-mocha\n *\n * @type {boolean}\n */\nvar isMocha = [\n    'afterEach',\n    'after',\n    'beforeEach',\n    'before',\n    'describe',\n    'it',\n].every(function (fn) {\n    // @ts-expect-error TS7053: Element implicitly has an 'any' type\n    return typeof global[fn] === 'function';\n});\n/**\n * Singleton class for `require-in-the-middle`\n * Allows instrumentation plugins to patch modules with only a single `require` patch\n * WARNING: Because this class will create its own `require-in-the-middle` (RITM) instance,\n * we should minimize the number of new instances of this class.\n * Multiple instances of `@opentelemetry/instrumentation` (e.g. multiple versions) in a single process\n * will result in multiple instances of RITM, which will have an impact\n * on the performance of instrumentation hooks being applied.\n */\nvar RequireInTheMiddleSingleton = /** @class */ (function () {\n    function RequireInTheMiddleSingleton() {\n        this._moduleNameTrie = new _ModuleNameTrie__WEBPACK_IMPORTED_MODULE_2__.ModuleNameTrie();\n        this._initialize();\n    }\n    RequireInTheMiddleSingleton.prototype._initialize = function () {\n        var _this = this;\n        new require_in_the_middle__WEBPACK_IMPORTED_MODULE_0__.Hook(\n        // Intercept all `require` calls; we will filter the matching ones below\n        null, { internals: true }, function (exports, name, basedir) {\n            var e_1, _a;\n            // For internal files on Windows, `name` will use backslash as the path separator\n            var normalizedModuleName = normalizePathSeparators(name);\n            var matches = _this._moduleNameTrie.search(normalizedModuleName, {\n                maintainInsertionOrder: true,\n                // For core modules (e.g. `fs`), do not match on sub-paths (e.g. `fs/promises').\n                // This matches the behavior of `require-in-the-middle`.\n                // `basedir` is always `undefined` for core modules.\n                fullOnly: basedir === undefined,\n            });\n            try {\n                for (var matches_1 = __values(matches), matches_1_1 = matches_1.next(); !matches_1_1.done; matches_1_1 = matches_1.next()) {\n                    var onRequire = matches_1_1.value.onRequire;\n                    exports = onRequire(exports, name, basedir);\n                }\n            }\n            catch (e_1_1) { e_1 = { error: e_1_1 }; }\n            finally {\n                try {\n                    if (matches_1_1 && !matches_1_1.done && (_a = matches_1.return)) _a.call(matches_1);\n                }\n                finally { if (e_1) throw e_1.error; }\n            }\n            return exports;\n        });\n    };\n    /**\n     * Register a hook with `require-in-the-middle`\n     *\n     * @param {string} moduleName Module name\n     * @param {OnRequireFn} onRequire Hook function\n     * @returns {Hooked} Registered hook\n     */\n    RequireInTheMiddleSingleton.prototype.register = function (moduleName, onRequire) {\n        var hooked = { moduleName: moduleName, onRequire: onRequire };\n        this._moduleNameTrie.insert(hooked);\n        return hooked;\n    };\n    /**\n     * Get the `RequireInTheMiddleSingleton` singleton\n     *\n     * @returns {RequireInTheMiddleSingleton} Singleton of `RequireInTheMiddleSingleton`\n     */\n    RequireInTheMiddleSingleton.getInstance = function () {\n        var _a;\n        // Mocha runs all test suites in the same process\n        // This prevents test suites from sharing a singleton\n        if (isMocha)\n            return new RequireInTheMiddleSingleton();\n        return (this._instance =\n            (_a = this._instance) !== null && _a !== void 0 ? _a : new RequireInTheMiddleSingleton());\n    };\n    return RequireInTheMiddleSingleton;\n}());\n\n/**\n * Normalize the path separators to forward slash in a module name or path\n *\n * @param {string} moduleNameOrPath Module name or path\n * @returns {string} Normalized module name or path\n */\nfunction normalizePathSeparators(moduleNameOrPath) {\n    return path__WEBPACK_IMPORTED_MODULE_1__.sep !== _ModuleNameTrie__WEBPACK_IMPORTED_MODULE_2__.ModuleNameSeparator\n        ? moduleNameOrPath.split(path__WEBPACK_IMPORTED_MODULE_1__.sep).join(_ModuleNameTrie__WEBPACK_IMPORTED_MODULE_2__.ModuleNameSeparator)\n        : moduleNameOrPath;\n}\n//# sourceMappingURL=RequireInTheMiddleSingleton.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/RequireInTheMiddleSingleton.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/instrumentation.js":
/*!************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/instrumentation.js ***!
  \************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InstrumentationBase: () => (/* binding */ InstrumentationBase)\n/* harmony export */ });\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var util__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! util */ \"util\");\n/* harmony import */ var util__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(util__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var semver__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! semver */ \"(instrument)/../../node_modules/.pnpm/semver@7.7.1/node_modules/semver/index.js\");\n/* harmony import */ var semver__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(semver__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var shimmer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! shimmer */ \"(instrument)/../../node_modules/.pnpm/shimmer@1.2.1/node_modules/shimmer/index.js\");\n/* harmony import */ var shimmer__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(shimmer__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _instrumentation__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../instrumentation */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/instrumentation.js\");\n/* harmony import */ var _RequireInTheMiddleSingleton__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./RequireInTheMiddleSingleton */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/RequireInTheMiddleSingleton.js\");\n/* harmony import */ var import_in_the_middle__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! import-in-the-middle */ \"import-in-the-middle\");\n/* harmony import */ var import_in_the_middle__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(import_in_the_middle__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _opentelemetry_api__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @opentelemetry/api */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/diag-api.js\");\n/* harmony import */ var require_in_the_middle__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! require-in-the-middle */ \"require-in-the-middle\");\n/* harmony import */ var require_in_the_middle__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(require_in_the_middle__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../utils */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/utils.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __values = (undefined && undefined.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n\n\n\n\n\n\n\n\n\n\n\n/**\n * Base abstract class for instrumenting node plugins\n */\nvar InstrumentationBase = /** @class */ (function (_super) {\n    __extends(InstrumentationBase, _super);\n    function InstrumentationBase(instrumentationName, instrumentationVersion, config) {\n        var _this = _super.call(this, instrumentationName, instrumentationVersion, config) || this;\n        _this._hooks = [];\n        _this._requireInTheMiddleSingleton = _RequireInTheMiddleSingleton__WEBPACK_IMPORTED_MODULE_7__.RequireInTheMiddleSingleton.getInstance();\n        _this._enabled = false;\n        _this._wrap = function (moduleExports, name, wrapper) {\n            if ((0,_utils__WEBPACK_IMPORTED_MODULE_8__.isWrapped)(moduleExports[name])) {\n                _this._unwrap(moduleExports, name);\n            }\n            if (!util__WEBPACK_IMPORTED_MODULE_1__.types.isProxy(moduleExports)) {\n                return (0,shimmer__WEBPACK_IMPORTED_MODULE_3__.wrap)(moduleExports, name, wrapper);\n            }\n            else {\n                var wrapped = (0,shimmer__WEBPACK_IMPORTED_MODULE_3__.wrap)(Object.assign({}, moduleExports), name, wrapper);\n                Object.defineProperty(moduleExports, name, {\n                    value: wrapped,\n                });\n                return wrapped;\n            }\n        };\n        _this._unwrap = function (moduleExports, name) {\n            if (!util__WEBPACK_IMPORTED_MODULE_1__.types.isProxy(moduleExports)) {\n                return (0,shimmer__WEBPACK_IMPORTED_MODULE_3__.unwrap)(moduleExports, name);\n            }\n            else {\n                return Object.defineProperty(moduleExports, name, {\n                    value: moduleExports[name],\n                });\n            }\n        };\n        _this._massWrap = function (moduleExportsArray, names, wrapper) {\n            if (!moduleExportsArray) {\n                _opentelemetry_api__WEBPACK_IMPORTED_MODULE_9__.diag.error('must provide one or more modules to patch');\n                return;\n            }\n            else if (!Array.isArray(moduleExportsArray)) {\n                moduleExportsArray = [moduleExportsArray];\n            }\n            if (!(names && Array.isArray(names))) {\n                _opentelemetry_api__WEBPACK_IMPORTED_MODULE_9__.diag.error('must provide one or more functions to wrap on modules');\n                return;\n            }\n            moduleExportsArray.forEach(function (moduleExports) {\n                names.forEach(function (name) {\n                    _this._wrap(moduleExports, name, wrapper);\n                });\n            });\n        };\n        _this._massUnwrap = function (moduleExportsArray, names) {\n            if (!moduleExportsArray) {\n                _opentelemetry_api__WEBPACK_IMPORTED_MODULE_9__.diag.error('must provide one or more modules to patch');\n                return;\n            }\n            else if (!Array.isArray(moduleExportsArray)) {\n                moduleExportsArray = [moduleExportsArray];\n            }\n            if (!(names && Array.isArray(names))) {\n                _opentelemetry_api__WEBPACK_IMPORTED_MODULE_9__.diag.error('must provide one or more functions to wrap on modules');\n                return;\n            }\n            moduleExportsArray.forEach(function (moduleExports) {\n                names.forEach(function (name) {\n                    _this._unwrap(moduleExports, name);\n                });\n            });\n        };\n        var modules = _this.init();\n        if (modules && !Array.isArray(modules)) {\n            modules = [modules];\n        }\n        _this._modules = modules || [];\n        if (_this._config.enabled) {\n            _this.enable();\n        }\n        return _this;\n    }\n    InstrumentationBase.prototype._warnOnPreloadedModules = function () {\n        var _this = this;\n        this._modules.forEach(function (module) {\n            var name = module.name;\n            try {\n                var resolvedModule = /*require.resolve*/(__webpack_require__(\"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/platform/node sync recursive\").resolve(name));\n                if (__webpack_require__.c[resolvedModule]) {\n                    // Module is already cached, which means the instrumentation hook might not work\n                    _this._diag.warn(\"Module \" + name + \" has been loaded before \" + _this.instrumentationName + \" so it might not work, please initialize it before requiring \" + name);\n                }\n            }\n            catch (_a) {\n                // Module isn't available, we can simply skip\n            }\n        });\n    };\n    InstrumentationBase.prototype._extractPackageVersion = function (baseDir) {\n        try {\n            var json = (0,fs__WEBPACK_IMPORTED_MODULE_6__.readFileSync)(path__WEBPACK_IMPORTED_MODULE_0__.join(baseDir, 'package.json'), {\n                encoding: 'utf8',\n            });\n            var version = JSON.parse(json).version;\n            return typeof version === 'string' ? version : undefined;\n        }\n        catch (error) {\n            _opentelemetry_api__WEBPACK_IMPORTED_MODULE_9__.diag.warn('Failed extracting version', baseDir);\n        }\n        return undefined;\n    };\n    InstrumentationBase.prototype._onRequire = function (module, exports, name, baseDir) {\n        var _this = this;\n        var _a;\n        if (!baseDir) {\n            if (typeof module.patch === 'function') {\n                module.moduleExports = exports;\n                if (this._enabled) {\n                    this._diag.debug('Applying instrumentation patch for nodejs core module on require hook', {\n                        module: module.name,\n                    });\n                    return module.patch(exports);\n                }\n            }\n            return exports;\n        }\n        var version = this._extractPackageVersion(baseDir);\n        module.moduleVersion = version;\n        if (module.name === name) {\n            // main module\n            if (isSupported(module.supportedVersions, version, module.includePrerelease)) {\n                if (typeof module.patch === 'function') {\n                    module.moduleExports = exports;\n                    if (this._enabled) {\n                        this._diag.debug('Applying instrumentation patch for module on require hook', {\n                            module: module.name,\n                            version: module.moduleVersion,\n                            baseDir: baseDir,\n                        });\n                        return module.patch(exports, module.moduleVersion);\n                    }\n                }\n            }\n            return exports;\n        }\n        // internal file\n        var files = (_a = module.files) !== null && _a !== void 0 ? _a : [];\n        var normalizedName = path__WEBPACK_IMPORTED_MODULE_0__.normalize(name);\n        var supportedFileInstrumentations = files\n            .filter(function (f) { return f.name === normalizedName; })\n            .filter(function (f) {\n            return isSupported(f.supportedVersions, version, module.includePrerelease);\n        });\n        return supportedFileInstrumentations.reduce(function (patchedExports, file) {\n            file.moduleExports = patchedExports;\n            if (_this._enabled) {\n                _this._diag.debug('Applying instrumentation patch for nodejs module file on require hook', {\n                    module: module.name,\n                    version: module.moduleVersion,\n                    fileName: file.name,\n                    baseDir: baseDir,\n                });\n                // patch signature is not typed, so we cast it assuming it's correct\n                return file.patch(patchedExports, module.moduleVersion);\n            }\n            return patchedExports;\n        }, exports);\n    };\n    InstrumentationBase.prototype.enable = function () {\n        var e_1, _a, e_2, _b, e_3, _c;\n        var _this = this;\n        if (this._enabled) {\n            return;\n        }\n        this._enabled = true;\n        // already hooked, just call patch again\n        if (this._hooks.length > 0) {\n            try {\n                for (var _d = __values(this._modules), _e = _d.next(); !_e.done; _e = _d.next()) {\n                    var module_1 = _e.value;\n                    if (typeof module_1.patch === 'function' && module_1.moduleExports) {\n                        this._diag.debug('Applying instrumentation patch for nodejs module on instrumentation enabled', {\n                            module: module_1.name,\n                            version: module_1.moduleVersion,\n                        });\n                        module_1.patch(module_1.moduleExports, module_1.moduleVersion);\n                    }\n                    try {\n                        for (var _f = (e_2 = void 0, __values(module_1.files)), _g = _f.next(); !_g.done; _g = _f.next()) {\n                            var file = _g.value;\n                            if (file.moduleExports) {\n                                this._diag.debug('Applying instrumentation patch for nodejs module file on instrumentation enabled', {\n                                    module: module_1.name,\n                                    version: module_1.moduleVersion,\n                                    fileName: file.name,\n                                });\n                                file.patch(file.moduleExports, module_1.moduleVersion);\n                            }\n                        }\n                    }\n                    catch (e_2_1) { e_2 = { error: e_2_1 }; }\n                    finally {\n                        try {\n                            if (_g && !_g.done && (_b = _f.return)) _b.call(_f);\n                        }\n                        finally { if (e_2) throw e_2.error; }\n                    }\n                }\n            }\n            catch (e_1_1) { e_1 = { error: e_1_1 }; }\n            finally {\n                try {\n                    if (_e && !_e.done && (_a = _d.return)) _a.call(_d);\n                }\n                finally { if (e_1) throw e_1.error; }\n            }\n            return;\n        }\n        this._warnOnPreloadedModules();\n        var _loop_1 = function (module_2) {\n            var hookFn = function (exports, name, baseDir) {\n                if (!baseDir && path__WEBPACK_IMPORTED_MODULE_0__.isAbsolute(name)) {\n                    var parsedPath = path__WEBPACK_IMPORTED_MODULE_0__.parse(name);\n                    name = parsedPath.name;\n                    baseDir = parsedPath.dir;\n                }\n                return _this._onRequire(module_2, exports, name, baseDir);\n            };\n            var onRequire = function (exports, name, baseDir) {\n                return _this._onRequire(module_2, exports, name, baseDir);\n            };\n            // `RequireInTheMiddleSingleton` does not support absolute paths.\n            // For an absolute paths, we must create a separate instance of the\n            // require-in-the-middle `Hook`.\n            var hook = path__WEBPACK_IMPORTED_MODULE_0__.isAbsolute(module_2.name)\n                ? new require_in_the_middle__WEBPACK_IMPORTED_MODULE_5__.Hook([module_2.name], { internals: true }, onRequire)\n                : this_1._requireInTheMiddleSingleton.register(module_2.name, onRequire);\n            this_1._hooks.push(hook);\n            var esmHook = new import_in_the_middle__WEBPACK_IMPORTED_MODULE_4__.Hook([module_2.name], { internals: false }, hookFn);\n            this_1._hooks.push(esmHook);\n        };\n        var this_1 = this;\n        try {\n            for (var _h = __values(this._modules), _j = _h.next(); !_j.done; _j = _h.next()) {\n                var module_2 = _j.value;\n                _loop_1(module_2);\n            }\n        }\n        catch (e_3_1) { e_3 = { error: e_3_1 }; }\n        finally {\n            try {\n                if (_j && !_j.done && (_c = _h.return)) _c.call(_h);\n            }\n            finally { if (e_3) throw e_3.error; }\n        }\n    };\n    InstrumentationBase.prototype.disable = function () {\n        var e_4, _a, e_5, _b;\n        if (!this._enabled) {\n            return;\n        }\n        this._enabled = false;\n        try {\n            for (var _c = __values(this._modules), _d = _c.next(); !_d.done; _d = _c.next()) {\n                var module_3 = _d.value;\n                if (typeof module_3.unpatch === 'function' && module_3.moduleExports) {\n                    this._diag.debug('Removing instrumentation patch for nodejs module on instrumentation disabled', {\n                        module: module_3.name,\n                        version: module_3.moduleVersion,\n                    });\n                    module_3.unpatch(module_3.moduleExports, module_3.moduleVersion);\n                }\n                try {\n                    for (var _e = (e_5 = void 0, __values(module_3.files)), _f = _e.next(); !_f.done; _f = _e.next()) {\n                        var file = _f.value;\n                        if (file.moduleExports) {\n                            this._diag.debug('Removing instrumentation patch for nodejs module file on instrumentation disabled', {\n                                module: module_3.name,\n                                version: module_3.moduleVersion,\n                                fileName: file.name,\n                            });\n                            file.unpatch(file.moduleExports, module_3.moduleVersion);\n                        }\n                    }\n                }\n                catch (e_5_1) { e_5 = { error: e_5_1 }; }\n                finally {\n                    try {\n                        if (_f && !_f.done && (_b = _e.return)) _b.call(_e);\n                    }\n                    finally { if (e_5) throw e_5.error; }\n                }\n            }\n        }\n        catch (e_4_1) { e_4 = { error: e_4_1 }; }\n        finally {\n            try {\n                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);\n            }\n            finally { if (e_4) throw e_4.error; }\n        }\n    };\n    InstrumentationBase.prototype.isEnabled = function () {\n        return this._enabled;\n    };\n    return InstrumentationBase;\n}(_instrumentation__WEBPACK_IMPORTED_MODULE_10__.InstrumentationAbstract));\n\nfunction isSupported(supportedVersions, version, includePrerelease) {\n    if (typeof version === 'undefined') {\n        // If we don't have the version, accept the wildcard case only\n        return supportedVersions.includes('*');\n    }\n    return supportedVersions.some(function (supportedVersion) {\n        return (0,semver__WEBPACK_IMPORTED_MODULE_2__.satisfies)(version, supportedVersion, { includePrerelease: includePrerelease });\n    });\n}\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/instrumentation.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/utils.js":
/*!************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/utils.js ***!
  \************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isWrapped: () => (/* binding */ isWrapped),\n/* harmony export */   safeExecuteInTheMiddle: () => (/* binding */ safeExecuteInTheMiddle),\n/* harmony export */   safeExecuteInTheMiddleAsync: () => (/* binding */ safeExecuteInTheMiddleAsync)\n/* harmony export */ });\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __awaiter = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __generator = (undefined && undefined.__generator) || function (thisArg, body) {\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n    function verb(n) { return function (v) { return step([n, v]); }; }\n    function step(op) {\n        if (f) throw new TypeError(\"Generator is already executing.\");\n        while (_) try {\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n            if (y = 0, t) op = [op[0] & 2, t.value];\n            switch (op[0]) {\n                case 0: case 1: t = op; break;\n                case 4: _.label++; return { value: op[1], done: false };\n                case 5: _.label++; y = op[1]; op = [0]; continue;\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\n                default:\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                    if (t[2]) _.ops.pop();\n                    _.trys.pop(); continue;\n            }\n            op = body.call(thisArg, _);\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n    }\n};\n/**\n * function to execute patched function and being able to catch errors\n * @param execute - function to be executed\n * @param onFinish - callback to run when execute finishes\n */\nfunction safeExecuteInTheMiddle(execute, onFinish, preventThrowingError) {\n    var error;\n    var result;\n    try {\n        result = execute();\n    }\n    catch (e) {\n        error = e;\n    }\n    finally {\n        onFinish(error, result);\n        if (error && !preventThrowingError) {\n            // eslint-disable-next-line no-unsafe-finally\n            throw error;\n        }\n        // eslint-disable-next-line no-unsafe-finally\n        return result;\n    }\n}\n/**\n * Async function to execute patched function and being able to catch errors\n * @param execute - function to be executed\n * @param onFinish - callback to run when execute finishes\n */\nfunction safeExecuteInTheMiddleAsync(execute, onFinish, preventThrowingError) {\n    return __awaiter(this, void 0, void 0, function () {\n        var error, result, e_1;\n        return __generator(this, function (_a) {\n            switch (_a.label) {\n                case 0:\n                    _a.trys.push([0, 2, 3, 4]);\n                    return [4 /*yield*/, execute()];\n                case 1:\n                    result = _a.sent();\n                    return [3 /*break*/, 4];\n                case 2:\n                    e_1 = _a.sent();\n                    error = e_1;\n                    return [3 /*break*/, 4];\n                case 3:\n                    onFinish(error, result);\n                    if (error && !preventThrowingError) {\n                        // eslint-disable-next-line no-unsafe-finally\n                        throw error;\n                    }\n                    // eslint-disable-next-line no-unsafe-finally\n                    return [2 /*return*/, result];\n                case 4: return [2 /*return*/];\n            }\n        });\n    });\n}\n/**\n * Checks if certain function has been already wrapped\n * @param func\n */\nfunction isWrapped(func) {\n    return (typeof func === 'function' &&\n        typeof func.__original === 'function' &&\n        typeof func.__unwrap === 'function' &&\n        func.__wrapped === true);\n}\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/utils.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/autoLoader.js":
/*!*****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/autoLoader.js ***!
  \*****************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   registerInstrumentations: () => (/* binding */ registerInstrumentations)\n/* harmony export */ });\n/* harmony import */ var _opentelemetry_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @opentelemetry/api */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/trace-api.js\");\n/* harmony import */ var _opentelemetry_api__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @opentelemetry/api */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/metrics-api.js\");\n/* harmony import */ var _opentelemetry_api_logs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @opentelemetry/api-logs */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+api-logs@0.57.2/node_modules/@opentelemetry/api-logs/build/esm/index.js\");\n/* harmony import */ var _autoLoaderUtils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./autoLoaderUtils */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/autoLoaderUtils.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n\n\n/**\n * It will register instrumentations and plugins\n * @param options\n * @return returns function to unload instrumentation and plugins that were\n *   registered\n */\nfunction registerInstrumentations(options) {\n    var _a, _b;\n    var tracerProvider = options.tracerProvider || _opentelemetry_api__WEBPACK_IMPORTED_MODULE_0__.trace.getTracerProvider();\n    var meterProvider = options.meterProvider || _opentelemetry_api__WEBPACK_IMPORTED_MODULE_1__.metrics.getMeterProvider();\n    var loggerProvider = options.loggerProvider || _opentelemetry_api_logs__WEBPACK_IMPORTED_MODULE_2__.logs.getLoggerProvider();\n    var instrumentations = (_b = (_a = options.instrumentations) === null || _a === void 0 ? void 0 : _a.flat()) !== null && _b !== void 0 ? _b : [];\n    (0,_autoLoaderUtils__WEBPACK_IMPORTED_MODULE_3__.enableInstrumentations)(instrumentations, tracerProvider, meterProvider, loggerProvider);\n    return function () {\n        (0,_autoLoaderUtils__WEBPACK_IMPORTED_MODULE_3__.disableInstrumentations)(instrumentations);\n    };\n}\n//# sourceMappingURL=autoLoader.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/autoLoader.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/autoLoaderUtils.js":
/*!**********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/autoLoaderUtils.js ***!
  \**********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   disableInstrumentations: () => (/* binding */ disableInstrumentations),\n/* harmony export */   enableInstrumentations: () => (/* binding */ enableInstrumentations)\n/* harmony export */ });\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Enable instrumentations\n * @param instrumentations\n * @param tracerProvider\n * @param meterProvider\n */\nfunction enableInstrumentations(instrumentations, tracerProvider, meterProvider, loggerProvider) {\n    for (var i = 0, j = instrumentations.length; i < j; i++) {\n        var instrumentation = instrumentations[i];\n        if (tracerProvider) {\n            instrumentation.setTracerProvider(tracerProvider);\n        }\n        if (meterProvider) {\n            instrumentation.setMeterProvider(meterProvider);\n        }\n        if (loggerProvider && instrumentation.setLoggerProvider) {\n            instrumentation.setLoggerProvider(loggerProvider);\n        }\n        // instrumentations have been already enabled during creation\n        // so enable only if user prevented that by setting enabled to false\n        // this is to prevent double enabling but when calling register all\n        // instrumentations should be now enabled\n        if (!instrumentation.getConfig().enabled) {\n            instrumentation.enable();\n        }\n    }\n}\n/**\n * Disable instrumentations\n * @param instrumentations\n */\nfunction disableInstrumentations(instrumentations) {\n    instrumentations.forEach(function (instrumentation) { return instrumentation.disable(); });\n}\n//# sourceMappingURL=autoLoaderUtils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/autoLoaderUtils.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js":
/*!************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js ***!
  \************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InstrumentationBase: () => (/* reexport safe */ _platform_index__WEBPACK_IMPORTED_MODULE_1__.InstrumentationBase),\n/* harmony export */   InstrumentationNodeModuleDefinition: () => (/* reexport safe */ _instrumentationNodeModuleDefinition__WEBPACK_IMPORTED_MODULE_2__.InstrumentationNodeModuleDefinition),\n/* harmony export */   InstrumentationNodeModuleFile: () => (/* reexport safe */ _instrumentationNodeModuleFile__WEBPACK_IMPORTED_MODULE_3__.InstrumentationNodeModuleFile),\n/* harmony export */   isWrapped: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_4__.isWrapped),\n/* harmony export */   registerInstrumentations: () => (/* reexport safe */ _autoLoader__WEBPACK_IMPORTED_MODULE_0__.registerInstrumentations),\n/* harmony export */   safeExecuteInTheMiddle: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_4__.safeExecuteInTheMiddle),\n/* harmony export */   safeExecuteInTheMiddleAsync: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_4__.safeExecuteInTheMiddleAsync)\n/* harmony export */ });\n/* harmony import */ var _autoLoader__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./autoLoader */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/autoLoader.js\");\n/* harmony import */ var _platform_index__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./platform/index */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/instrumentation.js\");\n/* harmony import */ var _instrumentationNodeModuleDefinition__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./instrumentationNodeModuleDefinition */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/instrumentationNodeModuleDefinition.js\");\n/* harmony import */ var _instrumentationNodeModuleFile__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./instrumentationNodeModuleFile */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/instrumentationNodeModuleFile.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/utils.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n\n\n\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/instrumentation.js":
/*!**********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/instrumentation.js ***!
  \**********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InstrumentationAbstract: () => (/* binding */ InstrumentationAbstract)\n/* harmony export */ });\n/* harmony import */ var _opentelemetry_api__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @opentelemetry/api */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/diag-api.js\");\n/* harmony import */ var _opentelemetry_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @opentelemetry/api */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/trace-api.js\");\n/* harmony import */ var _opentelemetry_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @opentelemetry/api */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/metrics-api.js\");\n/* harmony import */ var _opentelemetry_api_logs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @opentelemetry/api-logs */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+api-logs@0.57.2/node_modules/@opentelemetry/api-logs/build/esm/index.js\");\n/* harmony import */ var shimmer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! shimmer */ \"(rsc)/../../node_modules/.pnpm/shimmer@1.2.1/node_modules/shimmer/index.js\");\n/* harmony import */ var shimmer__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(shimmer__WEBPACK_IMPORTED_MODULE_0__);\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __assign = (undefined && undefined.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\n\n\n\n/**\n * Base abstract internal class for instrumenting node and web plugins\n */\nvar InstrumentationAbstract = /** @class */ (function () {\n    function InstrumentationAbstract(instrumentationName, instrumentationVersion, config) {\n        this.instrumentationName = instrumentationName;\n        this.instrumentationVersion = instrumentationVersion;\n        this._config = {};\n        /* Api to wrap instrumented method */\n        this._wrap = shimmer__WEBPACK_IMPORTED_MODULE_0__.wrap;\n        /* Api to unwrap instrumented methods */\n        this._unwrap = shimmer__WEBPACK_IMPORTED_MODULE_0__.unwrap;\n        /* Api to mass wrap instrumented method */\n        this._massWrap = shimmer__WEBPACK_IMPORTED_MODULE_0__.massWrap;\n        /* Api to mass unwrap instrumented methods */\n        this._massUnwrap = shimmer__WEBPACK_IMPORTED_MODULE_0__.massUnwrap;\n        this.setConfig(config);\n        this._diag = _opentelemetry_api__WEBPACK_IMPORTED_MODULE_1__.diag.createComponentLogger({\n            namespace: instrumentationName,\n        });\n        this._tracer = _opentelemetry_api__WEBPACK_IMPORTED_MODULE_2__.trace.getTracer(instrumentationName, instrumentationVersion);\n        this._meter = _opentelemetry_api__WEBPACK_IMPORTED_MODULE_3__.metrics.getMeter(instrumentationName, instrumentationVersion);\n        this._logger = _opentelemetry_api_logs__WEBPACK_IMPORTED_MODULE_4__.logs.getLogger(instrumentationName, instrumentationVersion);\n        this._updateMetricInstruments();\n    }\n    Object.defineProperty(InstrumentationAbstract.prototype, \"meter\", {\n        /* Returns meter */\n        get: function () {\n            return this._meter;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    /**\n     * Sets MeterProvider to this plugin\n     * @param meterProvider\n     */\n    InstrumentationAbstract.prototype.setMeterProvider = function (meterProvider) {\n        this._meter = meterProvider.getMeter(this.instrumentationName, this.instrumentationVersion);\n        this._updateMetricInstruments();\n    };\n    Object.defineProperty(InstrumentationAbstract.prototype, \"logger\", {\n        /* Returns logger */\n        get: function () {\n            return this._logger;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    /**\n     * Sets LoggerProvider to this plugin\n     * @param loggerProvider\n     */\n    InstrumentationAbstract.prototype.setLoggerProvider = function (loggerProvider) {\n        this._logger = loggerProvider.getLogger(this.instrumentationName, this.instrumentationVersion);\n    };\n    /**\n     * @experimental\n     *\n     * Get module definitions defined by {@link init}.\n     * This can be used for experimental compile-time instrumentation.\n     *\n     * @returns an array of {@link InstrumentationModuleDefinition}\n     */\n    InstrumentationAbstract.prototype.getModuleDefinitions = function () {\n        var _a;\n        var initResult = (_a = this.init()) !== null && _a !== void 0 ? _a : [];\n        if (!Array.isArray(initResult)) {\n            return [initResult];\n        }\n        return initResult;\n    };\n    /**\n     * Sets the new metric instruments with the current Meter.\n     */\n    InstrumentationAbstract.prototype._updateMetricInstruments = function () {\n        return;\n    };\n    /* Returns InstrumentationConfig */\n    InstrumentationAbstract.prototype.getConfig = function () {\n        return this._config;\n    };\n    /**\n     * Sets InstrumentationConfig to this plugin\n     * @param config\n     */\n    InstrumentationAbstract.prototype.setConfig = function (config) {\n        // copy config first level properties to ensure they are immutable.\n        // nested properties are not copied, thus are mutable from the outside.\n        this._config = __assign({ enabled: true }, config);\n    };\n    /**\n     * Sets TraceProvider to this plugin\n     * @param tracerProvider\n     */\n    InstrumentationAbstract.prototype.setTracerProvider = function (tracerProvider) {\n        this._tracer = tracerProvider.getTracer(this.instrumentationName, this.instrumentationVersion);\n    };\n    Object.defineProperty(InstrumentationAbstract.prototype, \"tracer\", {\n        /* Returns tracer */\n        get: function () {\n            return this._tracer;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    /**\n     * Execute span customization hook, if configured, and log any errors.\n     * Any semantics of the trigger and info are defined by the specific instrumentation.\n     * @param hookHandler The optional hook handler which the user has configured via instrumentation config\n     * @param triggerName The name of the trigger for executing the hook for logging purposes\n     * @param span The span to which the hook should be applied\n     * @param info The info object to be passed to the hook, with useful data the hook may use\n     */\n    InstrumentationAbstract.prototype._runSpanCustomizationHook = function (hookHandler, triggerName, span, info) {\n        if (!hookHandler) {\n            return;\n        }\n        try {\n            hookHandler(span, info);\n        }\n        catch (e) {\n            this._diag.error(\"Error running span customization hook due to exception in handler\", { triggerName: triggerName }, e);\n        }\n    };\n    return InstrumentationAbstract;\n}());\n\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF8wNGYzNzBkNTE1Y2VlMGJlOTU1MjcyZjgyNjE2NjA3My9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uL2J1aWxkL2VzbS9pbnN0cnVtZW50YXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQixTQUFJLElBQUksU0FBSTtBQUM1QjtBQUNBLGlEQUFpRCxPQUFPO0FBQ3hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDMkQ7QUFDWjtBQUNaO0FBQ25DO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQix5Q0FBWTtBQUNqQztBQUNBLHVCQUF1QiwyQ0FBYztBQUNyQztBQUNBLHlCQUF5Qiw2Q0FBZ0I7QUFDekM7QUFDQSwyQkFBMkIsK0NBQWtCO0FBQzdDO0FBQ0EscUJBQXFCLG9EQUFJO0FBQ3pCO0FBQ0EsU0FBUztBQUNULHVCQUF1QixxREFBSztBQUM1QixzQkFBc0IsdURBQU87QUFDN0IsdUJBQXVCLHlEQUFJO0FBQzNCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMENBQTBDLFdBQVc7QUFDckQ7QUFDQTtBQUNBLDZCQUE2QjtBQUM3QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0NBQWtDLGVBQWU7QUFDakQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvR0FBb0csMEJBQTBCO0FBQzlIO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDa0M7QUFDbkMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYW53YXJcXERvY3VtZW50c1xcQ3ViZW50d2ViXFxjdWJlbnRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF8wNGYzNzBkNTE1Y2VlMGJlOTU1MjcyZjgyNjE2NjA3M1xcbm9kZV9tb2R1bGVzXFxAb3BlbnRlbGVtZXRyeVxcaW5zdHJ1bWVudGF0aW9uXFxidWlsZFxcZXNtXFxpbnN0cnVtZW50YXRpb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiLypcbiAqIENvcHlyaWdodCBUaGUgT3BlblRlbGVtZXRyeSBBdXRob3JzXG4gKlxuICogTGljZW5zZWQgdW5kZXIgdGhlIEFwYWNoZSBMaWNlbnNlLCBWZXJzaW9uIDIuMCAodGhlIFwiTGljZW5zZVwiKTtcbiAqIHlvdSBtYXkgbm90IHVzZSB0aGlzIGZpbGUgZXhjZXB0IGluIGNvbXBsaWFuY2Ugd2l0aCB0aGUgTGljZW5zZS5cbiAqIFlvdSBtYXkgb2J0YWluIGEgY29weSBvZiB0aGUgTGljZW5zZSBhdFxuICpcbiAqICAgICAgaHR0cHM6Ly93d3cuYXBhY2hlLm9yZy9saWNlbnNlcy9MSUNFTlNFLTIuMFxuICpcbiAqIFVubGVzcyByZXF1aXJlZCBieSBhcHBsaWNhYmxlIGxhdyBvciBhZ3JlZWQgdG8gaW4gd3JpdGluZywgc29mdHdhcmVcbiAqIGRpc3RyaWJ1dGVkIHVuZGVyIHRoZSBMaWNlbnNlIGlzIGRpc3RyaWJ1dGVkIG9uIGFuIFwiQVMgSVNcIiBCQVNJUyxcbiAqIFdJVEhPVVQgV0FSUkFOVElFUyBPUiBDT05ESVRJT05TIE9GIEFOWSBLSU5ELCBlaXRoZXIgZXhwcmVzcyBvciBpbXBsaWVkLlxuICogU2VlIHRoZSBMaWNlbnNlIGZvciB0aGUgc3BlY2lmaWMgbGFuZ3VhZ2UgZ292ZXJuaW5nIHBlcm1pc3Npb25zIGFuZFxuICogbGltaXRhdGlvbnMgdW5kZXIgdGhlIExpY2Vuc2UuXG4gKi9cbnZhciBfX2Fzc2lnbiA9ICh0aGlzICYmIHRoaXMuX19hc3NpZ24pIHx8IGZ1bmN0aW9uICgpIHtcbiAgICBfX2Fzc2lnbiA9IE9iamVjdC5hc3NpZ24gfHwgZnVuY3Rpb24odCkge1xuICAgICAgICBmb3IgKHZhciBzLCBpID0gMSwgbiA9IGFyZ3VtZW50cy5sZW5ndGg7IGkgPCBuOyBpKyspIHtcbiAgICAgICAgICAgIHMgPSBhcmd1bWVudHNbaV07XG4gICAgICAgICAgICBmb3IgKHZhciBwIGluIHMpIGlmIChPYmplY3QucHJvdG90eXBlLmhhc093blByb3BlcnR5LmNhbGwocywgcCkpXG4gICAgICAgICAgICAgICAgdFtwXSA9IHNbcF07XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHQ7XG4gICAgfTtcbiAgICByZXR1cm4gX19hc3NpZ24uYXBwbHkodGhpcywgYXJndW1lbnRzKTtcbn07XG5pbXBvcnQgeyBkaWFnLCBtZXRyaWNzLCB0cmFjZSwgfSBmcm9tICdAb3BlbnRlbGVtZXRyeS9hcGknO1xuaW1wb3J0IHsgbG9ncyB9IGZyb20gJ0BvcGVudGVsZW1ldHJ5L2FwaS1sb2dzJztcbmltcG9ydCAqIGFzIHNoaW1tZXIgZnJvbSAnc2hpbW1lcic7XG4vKipcbiAqIEJhc2UgYWJzdHJhY3QgaW50ZXJuYWwgY2xhc3MgZm9yIGluc3RydW1lbnRpbmcgbm9kZSBhbmQgd2ViIHBsdWdpbnNcbiAqL1xudmFyIEluc3RydW1lbnRhdGlvbkFic3RyYWN0ID0gLyoqIEBjbGFzcyAqLyAoZnVuY3Rpb24gKCkge1xuICAgIGZ1bmN0aW9uIEluc3RydW1lbnRhdGlvbkFic3RyYWN0KGluc3RydW1lbnRhdGlvbk5hbWUsIGluc3RydW1lbnRhdGlvblZlcnNpb24sIGNvbmZpZykge1xuICAgICAgICB0aGlzLmluc3RydW1lbnRhdGlvbk5hbWUgPSBpbnN0cnVtZW50YXRpb25OYW1lO1xuICAgICAgICB0aGlzLmluc3RydW1lbnRhdGlvblZlcnNpb24gPSBpbnN0cnVtZW50YXRpb25WZXJzaW9uO1xuICAgICAgICB0aGlzLl9jb25maWcgPSB7fTtcbiAgICAgICAgLyogQXBpIHRvIHdyYXAgaW5zdHJ1bWVudGVkIG1ldGhvZCAqL1xuICAgICAgICB0aGlzLl93cmFwID0gc2hpbW1lci53cmFwO1xuICAgICAgICAvKiBBcGkgdG8gdW53cmFwIGluc3RydW1lbnRlZCBtZXRob2RzICovXG4gICAgICAgIHRoaXMuX3Vud3JhcCA9IHNoaW1tZXIudW53cmFwO1xuICAgICAgICAvKiBBcGkgdG8gbWFzcyB3cmFwIGluc3RydW1lbnRlZCBtZXRob2QgKi9cbiAgICAgICAgdGhpcy5fbWFzc1dyYXAgPSBzaGltbWVyLm1hc3NXcmFwO1xuICAgICAgICAvKiBBcGkgdG8gbWFzcyB1bndyYXAgaW5zdHJ1bWVudGVkIG1ldGhvZHMgKi9cbiAgICAgICAgdGhpcy5fbWFzc1Vud3JhcCA9IHNoaW1tZXIubWFzc1Vud3JhcDtcbiAgICAgICAgdGhpcy5zZXRDb25maWcoY29uZmlnKTtcbiAgICAgICAgdGhpcy5fZGlhZyA9IGRpYWcuY3JlYXRlQ29tcG9uZW50TG9nZ2VyKHtcbiAgICAgICAgICAgIG5hbWVzcGFjZTogaW5zdHJ1bWVudGF0aW9uTmFtZSxcbiAgICAgICAgfSk7XG4gICAgICAgIHRoaXMuX3RyYWNlciA9IHRyYWNlLmdldFRyYWNlcihpbnN0cnVtZW50YXRpb25OYW1lLCBpbnN0cnVtZW50YXRpb25WZXJzaW9uKTtcbiAgICAgICAgdGhpcy5fbWV0ZXIgPSBtZXRyaWNzLmdldE1ldGVyKGluc3RydW1lbnRhdGlvbk5hbWUsIGluc3RydW1lbnRhdGlvblZlcnNpb24pO1xuICAgICAgICB0aGlzLl9sb2dnZXIgPSBsb2dzLmdldExvZ2dlcihpbnN0cnVtZW50YXRpb25OYW1lLCBpbnN0cnVtZW50YXRpb25WZXJzaW9uKTtcbiAgICAgICAgdGhpcy5fdXBkYXRlTWV0cmljSW5zdHJ1bWVudHMoKTtcbiAgICB9XG4gICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KEluc3RydW1lbnRhdGlvbkFic3RyYWN0LnByb3RvdHlwZSwgXCJtZXRlclwiLCB7XG4gICAgICAgIC8qIFJldHVybnMgbWV0ZXIgKi9cbiAgICAgICAgZ2V0OiBmdW5jdGlvbiAoKSB7XG4gICAgICAgICAgICByZXR1cm4gdGhpcy5fbWV0ZXI7XG4gICAgICAgIH0sXG4gICAgICAgIGVudW1lcmFibGU6IGZhbHNlLFxuICAgICAgICBjb25maWd1cmFibGU6IHRydWVcbiAgICB9KTtcbiAgICAvKipcbiAgICAgKiBTZXRzIE1ldGVyUHJvdmlkZXIgdG8gdGhpcyBwbHVnaW5cbiAgICAgKiBAcGFyYW0gbWV0ZXJQcm92aWRlclxuICAgICAqL1xuICAgIEluc3RydW1lbnRhdGlvbkFic3RyYWN0LnByb3RvdHlwZS5zZXRNZXRlclByb3ZpZGVyID0gZnVuY3Rpb24gKG1ldGVyUHJvdmlkZXIpIHtcbiAgICAgICAgdGhpcy5fbWV0ZXIgPSBtZXRlclByb3ZpZGVyLmdldE1ldGVyKHRoaXMuaW5zdHJ1bWVudGF0aW9uTmFtZSwgdGhpcy5pbnN0cnVtZW50YXRpb25WZXJzaW9uKTtcbiAgICAgICAgdGhpcy5fdXBkYXRlTWV0cmljSW5zdHJ1bWVudHMoKTtcbiAgICB9O1xuICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eShJbnN0cnVtZW50YXRpb25BYnN0cmFjdC5wcm90b3R5cGUsIFwibG9nZ2VyXCIsIHtcbiAgICAgICAgLyogUmV0dXJucyBsb2dnZXIgKi9cbiAgICAgICAgZ2V0OiBmdW5jdGlvbiAoKSB7XG4gICAgICAgICAgICByZXR1cm4gdGhpcy5fbG9nZ2VyO1xuICAgICAgICB9LFxuICAgICAgICBlbnVtZXJhYmxlOiBmYWxzZSxcbiAgICAgICAgY29uZmlndXJhYmxlOiB0cnVlXG4gICAgfSk7XG4gICAgLyoqXG4gICAgICogU2V0cyBMb2dnZXJQcm92aWRlciB0byB0aGlzIHBsdWdpblxuICAgICAqIEBwYXJhbSBsb2dnZXJQcm92aWRlclxuICAgICAqL1xuICAgIEluc3RydW1lbnRhdGlvbkFic3RyYWN0LnByb3RvdHlwZS5zZXRMb2dnZXJQcm92aWRlciA9IGZ1bmN0aW9uIChsb2dnZXJQcm92aWRlcikge1xuICAgICAgICB0aGlzLl9sb2dnZXIgPSBsb2dnZXJQcm92aWRlci5nZXRMb2dnZXIodGhpcy5pbnN0cnVtZW50YXRpb25OYW1lLCB0aGlzLmluc3RydW1lbnRhdGlvblZlcnNpb24pO1xuICAgIH07XG4gICAgLyoqXG4gICAgICogQGV4cGVyaW1lbnRhbFxuICAgICAqXG4gICAgICogR2V0IG1vZHVsZSBkZWZpbml0aW9ucyBkZWZpbmVkIGJ5IHtAbGluayBpbml0fS5cbiAgICAgKiBUaGlzIGNhbiBiZSB1c2VkIGZvciBleHBlcmltZW50YWwgY29tcGlsZS10aW1lIGluc3RydW1lbnRhdGlvbi5cbiAgICAgKlxuICAgICAqIEByZXR1cm5zIGFuIGFycmF5IG9mIHtAbGluayBJbnN0cnVtZW50YXRpb25Nb2R1bGVEZWZpbml0aW9ufVxuICAgICAqL1xuICAgIEluc3RydW1lbnRhdGlvbkFic3RyYWN0LnByb3RvdHlwZS5nZXRNb2R1bGVEZWZpbml0aW9ucyA9IGZ1bmN0aW9uICgpIHtcbiAgICAgICAgdmFyIF9hO1xuICAgICAgICB2YXIgaW5pdFJlc3VsdCA9IChfYSA9IHRoaXMuaW5pdCgpKSAhPT0gbnVsbCAmJiBfYSAhPT0gdm9pZCAwID8gX2EgOiBbXTtcbiAgICAgICAgaWYgKCFBcnJheS5pc0FycmF5KGluaXRSZXN1bHQpKSB7XG4gICAgICAgICAgICByZXR1cm4gW2luaXRSZXN1bHRdO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBpbml0UmVzdWx0O1xuICAgIH07XG4gICAgLyoqXG4gICAgICogU2V0cyB0aGUgbmV3IG1ldHJpYyBpbnN0cnVtZW50cyB3aXRoIHRoZSBjdXJyZW50IE1ldGVyLlxuICAgICAqL1xuICAgIEluc3RydW1lbnRhdGlvbkFic3RyYWN0LnByb3RvdHlwZS5fdXBkYXRlTWV0cmljSW5zdHJ1bWVudHMgPSBmdW5jdGlvbiAoKSB7XG4gICAgICAgIHJldHVybjtcbiAgICB9O1xuICAgIC8qIFJldHVybnMgSW5zdHJ1bWVudGF0aW9uQ29uZmlnICovXG4gICAgSW5zdHJ1bWVudGF0aW9uQWJzdHJhY3QucHJvdG90eXBlLmdldENvbmZpZyA9IGZ1bmN0aW9uICgpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuX2NvbmZpZztcbiAgICB9O1xuICAgIC8qKlxuICAgICAqIFNldHMgSW5zdHJ1bWVudGF0aW9uQ29uZmlnIHRvIHRoaXMgcGx1Z2luXG4gICAgICogQHBhcmFtIGNvbmZpZ1xuICAgICAqL1xuICAgIEluc3RydW1lbnRhdGlvbkFic3RyYWN0LnByb3RvdHlwZS5zZXRDb25maWcgPSBmdW5jdGlvbiAoY29uZmlnKSB7XG4gICAgICAgIC8vIGNvcHkgY29uZmlnIGZpcnN0IGxldmVsIHByb3BlcnRpZXMgdG8gZW5zdXJlIHRoZXkgYXJlIGltbXV0YWJsZS5cbiAgICAgICAgLy8gbmVzdGVkIHByb3BlcnRpZXMgYXJlIG5vdCBjb3BpZWQsIHRodXMgYXJlIG11dGFibGUgZnJvbSB0aGUgb3V0c2lkZS5cbiAgICAgICAgdGhpcy5fY29uZmlnID0gX19hc3NpZ24oeyBlbmFibGVkOiB0cnVlIH0sIGNvbmZpZyk7XG4gICAgfTtcbiAgICAvKipcbiAgICAgKiBTZXRzIFRyYWNlUHJvdmlkZXIgdG8gdGhpcyBwbHVnaW5cbiAgICAgKiBAcGFyYW0gdHJhY2VyUHJvdmlkZXJcbiAgICAgKi9cbiAgICBJbnN0cnVtZW50YXRpb25BYnN0cmFjdC5wcm90b3R5cGUuc2V0VHJhY2VyUHJvdmlkZXIgPSBmdW5jdGlvbiAodHJhY2VyUHJvdmlkZXIpIHtcbiAgICAgICAgdGhpcy5fdHJhY2VyID0gdHJhY2VyUHJvdmlkZXIuZ2V0VHJhY2VyKHRoaXMuaW5zdHJ1bWVudGF0aW9uTmFtZSwgdGhpcy5pbnN0cnVtZW50YXRpb25WZXJzaW9uKTtcbiAgICB9O1xuICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eShJbnN0cnVtZW50YXRpb25BYnN0cmFjdC5wcm90b3R5cGUsIFwidHJhY2VyXCIsIHtcbiAgICAgICAgLyogUmV0dXJucyB0cmFjZXIgKi9cbiAgICAgICAgZ2V0OiBmdW5jdGlvbiAoKSB7XG4gICAgICAgICAgICByZXR1cm4gdGhpcy5fdHJhY2VyO1xuICAgICAgICB9LFxuICAgICAgICBlbnVtZXJhYmxlOiBmYWxzZSxcbiAgICAgICAgY29uZmlndXJhYmxlOiB0cnVlXG4gICAgfSk7XG4gICAgLyoqXG4gICAgICogRXhlY3V0ZSBzcGFuIGN1c3RvbWl6YXRpb24gaG9vaywgaWYgY29uZmlndXJlZCwgYW5kIGxvZyBhbnkgZXJyb3JzLlxuICAgICAqIEFueSBzZW1hbnRpY3Mgb2YgdGhlIHRyaWdnZXIgYW5kIGluZm8gYXJlIGRlZmluZWQgYnkgdGhlIHNwZWNpZmljIGluc3RydW1lbnRhdGlvbi5cbiAgICAgKiBAcGFyYW0gaG9va0hhbmRsZXIgVGhlIG9wdGlvbmFsIGhvb2sgaGFuZGxlciB3aGljaCB0aGUgdXNlciBoYXMgY29uZmlndXJlZCB2aWEgaW5zdHJ1bWVudGF0aW9uIGNvbmZpZ1xuICAgICAqIEBwYXJhbSB0cmlnZ2VyTmFtZSBUaGUgbmFtZSBvZiB0aGUgdHJpZ2dlciBmb3IgZXhlY3V0aW5nIHRoZSBob29rIGZvciBsb2dnaW5nIHB1cnBvc2VzXG4gICAgICogQHBhcmFtIHNwYW4gVGhlIHNwYW4gdG8gd2hpY2ggdGhlIGhvb2sgc2hvdWxkIGJlIGFwcGxpZWRcbiAgICAgKiBAcGFyYW0gaW5mbyBUaGUgaW5mbyBvYmplY3QgdG8gYmUgcGFzc2VkIHRvIHRoZSBob29rLCB3aXRoIHVzZWZ1bCBkYXRhIHRoZSBob29rIG1heSB1c2VcbiAgICAgKi9cbiAgICBJbnN0cnVtZW50YXRpb25BYnN0cmFjdC5wcm90b3R5cGUuX3J1blNwYW5DdXN0b21pemF0aW9uSG9vayA9IGZ1bmN0aW9uIChob29rSGFuZGxlciwgdHJpZ2dlck5hbWUsIHNwYW4sIGluZm8pIHtcbiAgICAgICAgaWYgKCFob29rSGFuZGxlcikge1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICBob29rSGFuZGxlcihzcGFuLCBpbmZvKTtcbiAgICAgICAgfVxuICAgICAgICBjYXRjaCAoZSkge1xuICAgICAgICAgICAgdGhpcy5fZGlhZy5lcnJvcihcIkVycm9yIHJ1bm5pbmcgc3BhbiBjdXN0b21pemF0aW9uIGhvb2sgZHVlIHRvIGV4Y2VwdGlvbiBpbiBoYW5kbGVyXCIsIHsgdHJpZ2dlck5hbWU6IHRyaWdnZXJOYW1lIH0sIGUpO1xuICAgICAgICB9XG4gICAgfTtcbiAgICByZXR1cm4gSW5zdHJ1bWVudGF0aW9uQWJzdHJhY3Q7XG59KCkpO1xuZXhwb3J0IHsgSW5zdHJ1bWVudGF0aW9uQWJzdHJhY3QgfTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluc3RydW1lbnRhdGlvbi5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/instrumentation.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/instrumentationNodeModuleDefinition.js":
/*!******************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/instrumentationNodeModuleDefinition.js ***!
  \******************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InstrumentationNodeModuleDefinition: () => (/* binding */ InstrumentationNodeModuleDefinition)\n/* harmony export */ });\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar InstrumentationNodeModuleDefinition = /** @class */ (function () {\n    function InstrumentationNodeModuleDefinition(name, supportedVersions, \n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    patch, \n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    unpatch, files) {\n        this.name = name;\n        this.supportedVersions = supportedVersions;\n        this.patch = patch;\n        this.unpatch = unpatch;\n        this.files = files || [];\n    }\n    return InstrumentationNodeModuleDefinition;\n}());\n\n//# sourceMappingURL=instrumentationNodeModuleDefinition.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/instrumentationNodeModuleDefinition.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/instrumentationNodeModuleFile.js":
/*!************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/instrumentationNodeModuleFile.js ***!
  \************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InstrumentationNodeModuleFile: () => (/* binding */ InstrumentationNodeModuleFile)\n/* harmony export */ });\n/* harmony import */ var _platform_index__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./platform/index */ \"path\");\n/* harmony import */ var _platform_index__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_platform_index__WEBPACK_IMPORTED_MODULE_0__);\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nvar InstrumentationNodeModuleFile = /** @class */ (function () {\n    function InstrumentationNodeModuleFile(name, supportedVersions, \n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    patch, \n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    unpatch) {\n        this.supportedVersions = supportedVersions;\n        this.patch = patch;\n        this.unpatch = unpatch;\n        this.name = (0,_platform_index__WEBPACK_IMPORTED_MODULE_0__.normalize)(name);\n    }\n    return InstrumentationNodeModuleFile;\n}());\n\n//# sourceMappingURL=instrumentationNodeModuleFile.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/instrumentationNodeModuleFile.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/ModuleNameTrie.js":
/*!***********************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/ModuleNameTrie.js ***!
  \***********************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ModuleNameSeparator: () => (/* binding */ ModuleNameSeparator),\n/* harmony export */   ModuleNameTrie: () => (/* binding */ ModuleNameTrie)\n/* harmony export */ });\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __values = (undefined && undefined.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nvar __read = (undefined && undefined.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (undefined && undefined.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nvar ModuleNameSeparator = '/';\n/**\n * Node in a `ModuleNameTrie`\n */\nvar ModuleNameTrieNode = /** @class */ (function () {\n    function ModuleNameTrieNode() {\n        this.hooks = [];\n        this.children = new Map();\n    }\n    return ModuleNameTrieNode;\n}());\n/**\n * Trie containing nodes that represent a part of a module name (i.e. the parts separated by forward slash)\n */\nvar ModuleNameTrie = /** @class */ (function () {\n    function ModuleNameTrie() {\n        this._trie = new ModuleNameTrieNode();\n        this._counter = 0;\n    }\n    /**\n     * Insert a module hook into the trie\n     *\n     * @param {Hooked} hook Hook\n     */\n    ModuleNameTrie.prototype.insert = function (hook) {\n        var e_1, _a;\n        var trieNode = this._trie;\n        try {\n            for (var _b = __values(hook.moduleName.split(ModuleNameSeparator)), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var moduleNamePart = _c.value;\n                var nextNode = trieNode.children.get(moduleNamePart);\n                if (!nextNode) {\n                    nextNode = new ModuleNameTrieNode();\n                    trieNode.children.set(moduleNamePart, nextNode);\n                }\n                trieNode = nextNode;\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        trieNode.hooks.push({ hook: hook, insertedId: this._counter++ });\n    };\n    /**\n     * Search for matching hooks in the trie\n     *\n     * @param {string} moduleName Module name\n     * @param {boolean} maintainInsertionOrder Whether to return the results in insertion order\n     * @param {boolean} fullOnly Whether to return only full matches\n     * @returns {Hooked[]} Matching hooks\n     */\n    ModuleNameTrie.prototype.search = function (moduleName, _a) {\n        var e_2, _b;\n        var _c = _a === void 0 ? {} : _a, maintainInsertionOrder = _c.maintainInsertionOrder, fullOnly = _c.fullOnly;\n        var trieNode = this._trie;\n        var results = [];\n        var foundFull = true;\n        try {\n            for (var _d = __values(moduleName.split(ModuleNameSeparator)), _e = _d.next(); !_e.done; _e = _d.next()) {\n                var moduleNamePart = _e.value;\n                var nextNode = trieNode.children.get(moduleNamePart);\n                if (!nextNode) {\n                    foundFull = false;\n                    break;\n                }\n                if (!fullOnly) {\n                    results.push.apply(results, __spreadArray([], __read(nextNode.hooks), false));\n                }\n                trieNode = nextNode;\n            }\n        }\n        catch (e_2_1) { e_2 = { error: e_2_1 }; }\n        finally {\n            try {\n                if (_e && !_e.done && (_b = _d.return)) _b.call(_d);\n            }\n            finally { if (e_2) throw e_2.error; }\n        }\n        if (fullOnly && foundFull) {\n            results.push.apply(results, __spreadArray([], __read(trieNode.hooks), false));\n        }\n        if (results.length === 0) {\n            return [];\n        }\n        if (results.length === 1) {\n            return [results[0].hook];\n        }\n        if (maintainInsertionOrder) {\n            results.sort(function (a, b) { return a.insertedId - b.insertedId; });\n        }\n        return results.map(function (_a) {\n            var hook = _a.hook;\n            return hook;\n        });\n    };\n    return ModuleNameTrie;\n}());\n\n//# sourceMappingURL=ModuleNameTrie.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/ModuleNameTrie.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/RequireInTheMiddleSingleton.js":
/*!************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/RequireInTheMiddleSingleton.js ***!
  \************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RequireInTheMiddleSingleton: () => (/* binding */ RequireInTheMiddleSingleton)\n/* harmony export */ });\n/* harmony import */ var require_in_the_middle__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! require-in-the-middle */ \"require-in-the-middle\");\n/* harmony import */ var require_in_the_middle__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(require_in_the_middle__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ModuleNameTrie__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ModuleNameTrie */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/ModuleNameTrie.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __values = (undefined && undefined.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n\n\n\n/**\n * Whether Mocha is running in this process\n * Inspired by https://github.com/AndreasPizsa/detect-mocha\n *\n * @type {boolean}\n */\nvar isMocha = [\n    'afterEach',\n    'after',\n    'beforeEach',\n    'before',\n    'describe',\n    'it',\n].every(function (fn) {\n    // @ts-expect-error TS7053: Element implicitly has an 'any' type\n    return typeof global[fn] === 'function';\n});\n/**\n * Singleton class for `require-in-the-middle`\n * Allows instrumentation plugins to patch modules with only a single `require` patch\n * WARNING: Because this class will create its own `require-in-the-middle` (RITM) instance,\n * we should minimize the number of new instances of this class.\n * Multiple instances of `@opentelemetry/instrumentation` (e.g. multiple versions) in a single process\n * will result in multiple instances of RITM, which will have an impact\n * on the performance of instrumentation hooks being applied.\n */\nvar RequireInTheMiddleSingleton = /** @class */ (function () {\n    function RequireInTheMiddleSingleton() {\n        this._moduleNameTrie = new _ModuleNameTrie__WEBPACK_IMPORTED_MODULE_2__.ModuleNameTrie();\n        this._initialize();\n    }\n    RequireInTheMiddleSingleton.prototype._initialize = function () {\n        var _this = this;\n        new require_in_the_middle__WEBPACK_IMPORTED_MODULE_0__.Hook(\n        // Intercept all `require` calls; we will filter the matching ones below\n        null, { internals: true }, function (exports, name, basedir) {\n            var e_1, _a;\n            // For internal files on Windows, `name` will use backslash as the path separator\n            var normalizedModuleName = normalizePathSeparators(name);\n            var matches = _this._moduleNameTrie.search(normalizedModuleName, {\n                maintainInsertionOrder: true,\n                // For core modules (e.g. `fs`), do not match on sub-paths (e.g. `fs/promises').\n                // This matches the behavior of `require-in-the-middle`.\n                // `basedir` is always `undefined` for core modules.\n                fullOnly: basedir === undefined,\n            });\n            try {\n                for (var matches_1 = __values(matches), matches_1_1 = matches_1.next(); !matches_1_1.done; matches_1_1 = matches_1.next()) {\n                    var onRequire = matches_1_1.value.onRequire;\n                    exports = onRequire(exports, name, basedir);\n                }\n            }\n            catch (e_1_1) { e_1 = { error: e_1_1 }; }\n            finally {\n                try {\n                    if (matches_1_1 && !matches_1_1.done && (_a = matches_1.return)) _a.call(matches_1);\n                }\n                finally { if (e_1) throw e_1.error; }\n            }\n            return exports;\n        });\n    };\n    /**\n     * Register a hook with `require-in-the-middle`\n     *\n     * @param {string} moduleName Module name\n     * @param {OnRequireFn} onRequire Hook function\n     * @returns {Hooked} Registered hook\n     */\n    RequireInTheMiddleSingleton.prototype.register = function (moduleName, onRequire) {\n        var hooked = { moduleName: moduleName, onRequire: onRequire };\n        this._moduleNameTrie.insert(hooked);\n        return hooked;\n    };\n    /**\n     * Get the `RequireInTheMiddleSingleton` singleton\n     *\n     * @returns {RequireInTheMiddleSingleton} Singleton of `RequireInTheMiddleSingleton`\n     */\n    RequireInTheMiddleSingleton.getInstance = function () {\n        var _a;\n        // Mocha runs all test suites in the same process\n        // This prevents test suites from sharing a singleton\n        if (isMocha)\n            return new RequireInTheMiddleSingleton();\n        return (this._instance =\n            (_a = this._instance) !== null && _a !== void 0 ? _a : new RequireInTheMiddleSingleton());\n    };\n    return RequireInTheMiddleSingleton;\n}());\n\n/**\n * Normalize the path separators to forward slash in a module name or path\n *\n * @param {string} moduleNameOrPath Module name or path\n * @returns {string} Normalized module name or path\n */\nfunction normalizePathSeparators(moduleNameOrPath) {\n    return path__WEBPACK_IMPORTED_MODULE_1__.sep !== _ModuleNameTrie__WEBPACK_IMPORTED_MODULE_2__.ModuleNameSeparator\n        ? moduleNameOrPath.split(path__WEBPACK_IMPORTED_MODULE_1__.sep).join(_ModuleNameTrie__WEBPACK_IMPORTED_MODULE_2__.ModuleNameSeparator)\n        : moduleNameOrPath;\n}\n//# sourceMappingURL=RequireInTheMiddleSingleton.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/RequireInTheMiddleSingleton.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/instrumentation.js":
/*!************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/instrumentation.js ***!
  \************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InstrumentationBase: () => (/* binding */ InstrumentationBase)\n/* harmony export */ });\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var util__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! util */ \"util\");\n/* harmony import */ var util__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(util__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var semver__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! semver */ \"(rsc)/../../node_modules/.pnpm/semver@7.7.1/node_modules/semver/index.js\");\n/* harmony import */ var semver__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(semver__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var shimmer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! shimmer */ \"(rsc)/../../node_modules/.pnpm/shimmer@1.2.1/node_modules/shimmer/index.js\");\n/* harmony import */ var shimmer__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(shimmer__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _instrumentation__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../instrumentation */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/instrumentation.js\");\n/* harmony import */ var _RequireInTheMiddleSingleton__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./RequireInTheMiddleSingleton */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/RequireInTheMiddleSingleton.js\");\n/* harmony import */ var import_in_the_middle__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! import-in-the-middle */ \"import-in-the-middle\");\n/* harmony import */ var import_in_the_middle__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(import_in_the_middle__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _opentelemetry_api__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @opentelemetry/api */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/diag-api.js\");\n/* harmony import */ var require_in_the_middle__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! require-in-the-middle */ \"require-in-the-middle\");\n/* harmony import */ var require_in_the_middle__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(require_in_the_middle__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../utils */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/utils.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __values = (undefined && undefined.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n\n\n\n\n\n\n\n\n\n\n\n/**\n * Base abstract class for instrumenting node plugins\n */\nvar InstrumentationBase = /** @class */ (function (_super) {\n    __extends(InstrumentationBase, _super);\n    function InstrumentationBase(instrumentationName, instrumentationVersion, config) {\n        var _this = _super.call(this, instrumentationName, instrumentationVersion, config) || this;\n        _this._hooks = [];\n        _this._requireInTheMiddleSingleton = _RequireInTheMiddleSingleton__WEBPACK_IMPORTED_MODULE_7__.RequireInTheMiddleSingleton.getInstance();\n        _this._enabled = false;\n        _this._wrap = function (moduleExports, name, wrapper) {\n            if ((0,_utils__WEBPACK_IMPORTED_MODULE_8__.isWrapped)(moduleExports[name])) {\n                _this._unwrap(moduleExports, name);\n            }\n            if (!util__WEBPACK_IMPORTED_MODULE_1__.types.isProxy(moduleExports)) {\n                return (0,shimmer__WEBPACK_IMPORTED_MODULE_3__.wrap)(moduleExports, name, wrapper);\n            }\n            else {\n                var wrapped = (0,shimmer__WEBPACK_IMPORTED_MODULE_3__.wrap)(Object.assign({}, moduleExports), name, wrapper);\n                Object.defineProperty(moduleExports, name, {\n                    value: wrapped,\n                });\n                return wrapped;\n            }\n        };\n        _this._unwrap = function (moduleExports, name) {\n            if (!util__WEBPACK_IMPORTED_MODULE_1__.types.isProxy(moduleExports)) {\n                return (0,shimmer__WEBPACK_IMPORTED_MODULE_3__.unwrap)(moduleExports, name);\n            }\n            else {\n                return Object.defineProperty(moduleExports, name, {\n                    value: moduleExports[name],\n                });\n            }\n        };\n        _this._massWrap = function (moduleExportsArray, names, wrapper) {\n            if (!moduleExportsArray) {\n                _opentelemetry_api__WEBPACK_IMPORTED_MODULE_9__.diag.error('must provide one or more modules to patch');\n                return;\n            }\n            else if (!Array.isArray(moduleExportsArray)) {\n                moduleExportsArray = [moduleExportsArray];\n            }\n            if (!(names && Array.isArray(names))) {\n                _opentelemetry_api__WEBPACK_IMPORTED_MODULE_9__.diag.error('must provide one or more functions to wrap on modules');\n                return;\n            }\n            moduleExportsArray.forEach(function (moduleExports) {\n                names.forEach(function (name) {\n                    _this._wrap(moduleExports, name, wrapper);\n                });\n            });\n        };\n        _this._massUnwrap = function (moduleExportsArray, names) {\n            if (!moduleExportsArray) {\n                _opentelemetry_api__WEBPACK_IMPORTED_MODULE_9__.diag.error('must provide one or more modules to patch');\n                return;\n            }\n            else if (!Array.isArray(moduleExportsArray)) {\n                moduleExportsArray = [moduleExportsArray];\n            }\n            if (!(names && Array.isArray(names))) {\n                _opentelemetry_api__WEBPACK_IMPORTED_MODULE_9__.diag.error('must provide one or more functions to wrap on modules');\n                return;\n            }\n            moduleExportsArray.forEach(function (moduleExports) {\n                names.forEach(function (name) {\n                    _this._unwrap(moduleExports, name);\n                });\n            });\n        };\n        var modules = _this.init();\n        if (modules && !Array.isArray(modules)) {\n            modules = [modules];\n        }\n        _this._modules = modules || [];\n        if (_this._config.enabled) {\n            _this.enable();\n        }\n        return _this;\n    }\n    InstrumentationBase.prototype._warnOnPreloadedModules = function () {\n        var _this = this;\n        this._modules.forEach(function (module) {\n            var name = module.name;\n            try {\n                var resolvedModule = /*require.resolve*/(__webpack_require__(\"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/platform/node sync recursive\").resolve(name));\n                if (__webpack_require__.c[resolvedModule]) {\n                    // Module is already cached, which means the instrumentation hook might not work\n                    _this._diag.warn(\"Module \" + name + \" has been loaded before \" + _this.instrumentationName + \" so it might not work, please initialize it before requiring \" + name);\n                }\n            }\n            catch (_a) {\n                // Module isn't available, we can simply skip\n            }\n        });\n    };\n    InstrumentationBase.prototype._extractPackageVersion = function (baseDir) {\n        try {\n            var json = (0,fs__WEBPACK_IMPORTED_MODULE_6__.readFileSync)(path__WEBPACK_IMPORTED_MODULE_0__.join(baseDir, 'package.json'), {\n                encoding: 'utf8',\n            });\n            var version = JSON.parse(json).version;\n            return typeof version === 'string' ? version : undefined;\n        }\n        catch (error) {\n            _opentelemetry_api__WEBPACK_IMPORTED_MODULE_9__.diag.warn('Failed extracting version', baseDir);\n        }\n        return undefined;\n    };\n    InstrumentationBase.prototype._onRequire = function (module, exports, name, baseDir) {\n        var _this = this;\n        var _a;\n        if (!baseDir) {\n            if (typeof module.patch === 'function') {\n                module.moduleExports = exports;\n                if (this._enabled) {\n                    this._diag.debug('Applying instrumentation patch for nodejs core module on require hook', {\n                        module: module.name,\n                    });\n                    return module.patch(exports);\n                }\n            }\n            return exports;\n        }\n        var version = this._extractPackageVersion(baseDir);\n        module.moduleVersion = version;\n        if (module.name === name) {\n            // main module\n            if (isSupported(module.supportedVersions, version, module.includePrerelease)) {\n                if (typeof module.patch === 'function') {\n                    module.moduleExports = exports;\n                    if (this._enabled) {\n                        this._diag.debug('Applying instrumentation patch for module on require hook', {\n                            module: module.name,\n                            version: module.moduleVersion,\n                            baseDir: baseDir,\n                        });\n                        return module.patch(exports, module.moduleVersion);\n                    }\n                }\n            }\n            return exports;\n        }\n        // internal file\n        var files = (_a = module.files) !== null && _a !== void 0 ? _a : [];\n        var normalizedName = path__WEBPACK_IMPORTED_MODULE_0__.normalize(name);\n        var supportedFileInstrumentations = files\n            .filter(function (f) { return f.name === normalizedName; })\n            .filter(function (f) {\n            return isSupported(f.supportedVersions, version, module.includePrerelease);\n        });\n        return supportedFileInstrumentations.reduce(function (patchedExports, file) {\n            file.moduleExports = patchedExports;\n            if (_this._enabled) {\n                _this._diag.debug('Applying instrumentation patch for nodejs module file on require hook', {\n                    module: module.name,\n                    version: module.moduleVersion,\n                    fileName: file.name,\n                    baseDir: baseDir,\n                });\n                // patch signature is not typed, so we cast it assuming it's correct\n                return file.patch(patchedExports, module.moduleVersion);\n            }\n            return patchedExports;\n        }, exports);\n    };\n    InstrumentationBase.prototype.enable = function () {\n        var e_1, _a, e_2, _b, e_3, _c;\n        var _this = this;\n        if (this._enabled) {\n            return;\n        }\n        this._enabled = true;\n        // already hooked, just call patch again\n        if (this._hooks.length > 0) {\n            try {\n                for (var _d = __values(this._modules), _e = _d.next(); !_e.done; _e = _d.next()) {\n                    var module_1 = _e.value;\n                    if (typeof module_1.patch === 'function' && module_1.moduleExports) {\n                        this._diag.debug('Applying instrumentation patch for nodejs module on instrumentation enabled', {\n                            module: module_1.name,\n                            version: module_1.moduleVersion,\n                        });\n                        module_1.patch(module_1.moduleExports, module_1.moduleVersion);\n                    }\n                    try {\n                        for (var _f = (e_2 = void 0, __values(module_1.files)), _g = _f.next(); !_g.done; _g = _f.next()) {\n                            var file = _g.value;\n                            if (file.moduleExports) {\n                                this._diag.debug('Applying instrumentation patch for nodejs module file on instrumentation enabled', {\n                                    module: module_1.name,\n                                    version: module_1.moduleVersion,\n                                    fileName: file.name,\n                                });\n                                file.patch(file.moduleExports, module_1.moduleVersion);\n                            }\n                        }\n                    }\n                    catch (e_2_1) { e_2 = { error: e_2_1 }; }\n                    finally {\n                        try {\n                            if (_g && !_g.done && (_b = _f.return)) _b.call(_f);\n                        }\n                        finally { if (e_2) throw e_2.error; }\n                    }\n                }\n            }\n            catch (e_1_1) { e_1 = { error: e_1_1 }; }\n            finally {\n                try {\n                    if (_e && !_e.done && (_a = _d.return)) _a.call(_d);\n                }\n                finally { if (e_1) throw e_1.error; }\n            }\n            return;\n        }\n        this._warnOnPreloadedModules();\n        var _loop_1 = function (module_2) {\n            var hookFn = function (exports, name, baseDir) {\n                if (!baseDir && path__WEBPACK_IMPORTED_MODULE_0__.isAbsolute(name)) {\n                    var parsedPath = path__WEBPACK_IMPORTED_MODULE_0__.parse(name);\n                    name = parsedPath.name;\n                    baseDir = parsedPath.dir;\n                }\n                return _this._onRequire(module_2, exports, name, baseDir);\n            };\n            var onRequire = function (exports, name, baseDir) {\n                return _this._onRequire(module_2, exports, name, baseDir);\n            };\n            // `RequireInTheMiddleSingleton` does not support absolute paths.\n            // For an absolute paths, we must create a separate instance of the\n            // require-in-the-middle `Hook`.\n            var hook = path__WEBPACK_IMPORTED_MODULE_0__.isAbsolute(module_2.name)\n                ? new require_in_the_middle__WEBPACK_IMPORTED_MODULE_5__.Hook([module_2.name], { internals: true }, onRequire)\n                : this_1._requireInTheMiddleSingleton.register(module_2.name, onRequire);\n            this_1._hooks.push(hook);\n            var esmHook = new import_in_the_middle__WEBPACK_IMPORTED_MODULE_4__.Hook([module_2.name], { internals: false }, hookFn);\n            this_1._hooks.push(esmHook);\n        };\n        var this_1 = this;\n        try {\n            for (var _h = __values(this._modules), _j = _h.next(); !_j.done; _j = _h.next()) {\n                var module_2 = _j.value;\n                _loop_1(module_2);\n            }\n        }\n        catch (e_3_1) { e_3 = { error: e_3_1 }; }\n        finally {\n            try {\n                if (_j && !_j.done && (_c = _h.return)) _c.call(_h);\n            }\n            finally { if (e_3) throw e_3.error; }\n        }\n    };\n    InstrumentationBase.prototype.disable = function () {\n        var e_4, _a, e_5, _b;\n        if (!this._enabled) {\n            return;\n        }\n        this._enabled = false;\n        try {\n            for (var _c = __values(this._modules), _d = _c.next(); !_d.done; _d = _c.next()) {\n                var module_3 = _d.value;\n                if (typeof module_3.unpatch === 'function' && module_3.moduleExports) {\n                    this._diag.debug('Removing instrumentation patch for nodejs module on instrumentation disabled', {\n                        module: module_3.name,\n                        version: module_3.moduleVersion,\n                    });\n                    module_3.unpatch(module_3.moduleExports, module_3.moduleVersion);\n                }\n                try {\n                    for (var _e = (e_5 = void 0, __values(module_3.files)), _f = _e.next(); !_f.done; _f = _e.next()) {\n                        var file = _f.value;\n                        if (file.moduleExports) {\n                            this._diag.debug('Removing instrumentation patch for nodejs module file on instrumentation disabled', {\n                                module: module_3.name,\n                                version: module_3.moduleVersion,\n                                fileName: file.name,\n                            });\n                            file.unpatch(file.moduleExports, module_3.moduleVersion);\n                        }\n                    }\n                }\n                catch (e_5_1) { e_5 = { error: e_5_1 }; }\n                finally {\n                    try {\n                        if (_f && !_f.done && (_b = _e.return)) _b.call(_e);\n                    }\n                    finally { if (e_5) throw e_5.error; }\n                }\n            }\n        }\n        catch (e_4_1) { e_4 = { error: e_4_1 }; }\n        finally {\n            try {\n                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);\n            }\n            finally { if (e_4) throw e_4.error; }\n        }\n    };\n    InstrumentationBase.prototype.isEnabled = function () {\n        return this._enabled;\n    };\n    return InstrumentationBase;\n}(_instrumentation__WEBPACK_IMPORTED_MODULE_10__.InstrumentationAbstract));\n\nfunction isSupported(supportedVersions, version, includePrerelease) {\n    if (typeof version === 'undefined') {\n        // If we don't have the version, accept the wildcard case only\n        return supportedVersions.includes('*');\n    }\n    return supportedVersions.some(function (supportedVersion) {\n        return (0,semver__WEBPACK_IMPORTED_MODULE_2__.satisfies)(version, supportedVersion, { includePrerelease: includePrerelease });\n    });\n}\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/instrumentation.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/utils.js":
/*!************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/utils.js ***!
  \************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isWrapped: () => (/* binding */ isWrapped),\n/* harmony export */   safeExecuteInTheMiddle: () => (/* binding */ safeExecuteInTheMiddle),\n/* harmony export */   safeExecuteInTheMiddleAsync: () => (/* binding */ safeExecuteInTheMiddleAsync)\n/* harmony export */ });\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __awaiter = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __generator = (undefined && undefined.__generator) || function (thisArg, body) {\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n    function verb(n) { return function (v) { return step([n, v]); }; }\n    function step(op) {\n        if (f) throw new TypeError(\"Generator is already executing.\");\n        while (_) try {\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n            if (y = 0, t) op = [op[0] & 2, t.value];\n            switch (op[0]) {\n                case 0: case 1: t = op; break;\n                case 4: _.label++; return { value: op[1], done: false };\n                case 5: _.label++; y = op[1]; op = [0]; continue;\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\n                default:\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                    if (t[2]) _.ops.pop();\n                    _.trys.pop(); continue;\n            }\n            op = body.call(thisArg, _);\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n    }\n};\n/**\n * function to execute patched function and being able to catch errors\n * @param execute - function to be executed\n * @param onFinish - callback to run when execute finishes\n */\nfunction safeExecuteInTheMiddle(execute, onFinish, preventThrowingError) {\n    var error;\n    var result;\n    try {\n        result = execute();\n    }\n    catch (e) {\n        error = e;\n    }\n    finally {\n        onFinish(error, result);\n        if (error && !preventThrowingError) {\n            // eslint-disable-next-line no-unsafe-finally\n            throw error;\n        }\n        // eslint-disable-next-line no-unsafe-finally\n        return result;\n    }\n}\n/**\n * Async function to execute patched function and being able to catch errors\n * @param execute - function to be executed\n * @param onFinish - callback to run when execute finishes\n */\nfunction safeExecuteInTheMiddleAsync(execute, onFinish, preventThrowingError) {\n    return __awaiter(this, void 0, void 0, function () {\n        var error, result, e_1;\n        return __generator(this, function (_a) {\n            switch (_a.label) {\n                case 0:\n                    _a.trys.push([0, 2, 3, 4]);\n                    return [4 /*yield*/, execute()];\n                case 1:\n                    result = _a.sent();\n                    return [3 /*break*/, 4];\n                case 2:\n                    e_1 = _a.sent();\n                    error = e_1;\n                    return [3 /*break*/, 4];\n                case 3:\n                    onFinish(error, result);\n                    if (error && !preventThrowingError) {\n                        // eslint-disable-next-line no-unsafe-finally\n                        throw error;\n                    }\n                    // eslint-disable-next-line no-unsafe-finally\n                    return [2 /*return*/, result];\n                case 4: return [2 /*return*/];\n            }\n        });\n    });\n}\n/**\n * Checks if certain function has been already wrapped\n * @param func\n */\nfunction isWrapped(func) {\n    return (typeof func === 'function' &&\n        typeof func.__original === 'function' &&\n        typeof func.__unwrap === 'function' &&\n        func.__wrapped === true);\n}\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/utils.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/autoLoader.js":
/*!*****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/autoLoader.js ***!
  \*****************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   registerInstrumentations: () => (/* binding */ registerInstrumentations)\n/* harmony export */ });\n/* harmony import */ var _opentelemetry_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @opentelemetry/api */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/trace-api.js\");\n/* harmony import */ var _opentelemetry_api__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @opentelemetry/api */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/metrics-api.js\");\n/* harmony import */ var _opentelemetry_api_logs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @opentelemetry/api-logs */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+api-logs@0.57.2/node_modules/@opentelemetry/api-logs/build/esm/index.js\");\n/* harmony import */ var _autoLoaderUtils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./autoLoaderUtils */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/autoLoaderUtils.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n\n\n/**\n * It will register instrumentations and plugins\n * @param options\n * @return returns function to unload instrumentation and plugins that were\n *   registered\n */\nfunction registerInstrumentations(options) {\n    var _a, _b;\n    var tracerProvider = options.tracerProvider || _opentelemetry_api__WEBPACK_IMPORTED_MODULE_0__.trace.getTracerProvider();\n    var meterProvider = options.meterProvider || _opentelemetry_api__WEBPACK_IMPORTED_MODULE_1__.metrics.getMeterProvider();\n    var loggerProvider = options.loggerProvider || _opentelemetry_api_logs__WEBPACK_IMPORTED_MODULE_2__.logs.getLoggerProvider();\n    var instrumentations = (_b = (_a = options.instrumentations) === null || _a === void 0 ? void 0 : _a.flat()) !== null && _b !== void 0 ? _b : [];\n    (0,_autoLoaderUtils__WEBPACK_IMPORTED_MODULE_3__.enableInstrumentations)(instrumentations, tracerProvider, meterProvider, loggerProvider);\n    return function () {\n        (0,_autoLoaderUtils__WEBPACK_IMPORTED_MODULE_3__.disableInstrumentations)(instrumentations);\n    };\n}\n//# sourceMappingURL=autoLoader.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/autoLoader.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/autoLoaderUtils.js":
/*!**********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/autoLoaderUtils.js ***!
  \**********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   disableInstrumentations: () => (/* binding */ disableInstrumentations),\n/* harmony export */   enableInstrumentations: () => (/* binding */ enableInstrumentations)\n/* harmony export */ });\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Enable instrumentations\n * @param instrumentations\n * @param tracerProvider\n * @param meterProvider\n */\nfunction enableInstrumentations(instrumentations, tracerProvider, meterProvider, loggerProvider) {\n    for (var i = 0, j = instrumentations.length; i < j; i++) {\n        var instrumentation = instrumentations[i];\n        if (tracerProvider) {\n            instrumentation.setTracerProvider(tracerProvider);\n        }\n        if (meterProvider) {\n            instrumentation.setMeterProvider(meterProvider);\n        }\n        if (loggerProvider && instrumentation.setLoggerProvider) {\n            instrumentation.setLoggerProvider(loggerProvider);\n        }\n        // instrumentations have been already enabled during creation\n        // so enable only if user prevented that by setting enabled to false\n        // this is to prevent double enabling but when calling register all\n        // instrumentations should be now enabled\n        if (!instrumentation.getConfig().enabled) {\n            instrumentation.enable();\n        }\n    }\n}\n/**\n * Disable instrumentations\n * @param instrumentations\n */\nfunction disableInstrumentations(instrumentations) {\n    instrumentations.forEach(function (instrumentation) { return instrumentation.disable(); });\n}\n//# sourceMappingURL=autoLoaderUtils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF8wNGYzNzBkNTE1Y2VlMGJlOTU1MjcyZjgyNjE2NjA3My9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uL2J1aWxkL2VzbS9hdXRvTG9hZGVyVXRpbHMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQLGlEQUFpRCxPQUFPO0FBQ3hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQLDBEQUEwRCxtQ0FBbUM7QUFDN0Y7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbndhclxcRG9jdW1lbnRzXFxDdWJlbnR3ZWJcXGN1YmVudFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQG9wZW50ZWxlbWV0cnkraW5zdHJ1bWVudGF0XzA0ZjM3MGQ1MTVjZWUwYmU5NTUyNzJmODI2MTY2MDczXFxub2RlX21vZHVsZXNcXEBvcGVudGVsZW1ldHJ5XFxpbnN0cnVtZW50YXRpb25cXGJ1aWxkXFxlc21cXGF1dG9Mb2FkZXJVdGlscy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKlxuICogQ29weXJpZ2h0IFRoZSBPcGVuVGVsZW1ldHJ5IEF1dGhvcnNcbiAqXG4gKiBMaWNlbnNlZCB1bmRlciB0aGUgQXBhY2hlIExpY2Vuc2UsIFZlcnNpb24gMi4wICh0aGUgXCJMaWNlbnNlXCIpO1xuICogeW91IG1heSBub3QgdXNlIHRoaXMgZmlsZSBleGNlcHQgaW4gY29tcGxpYW5jZSB3aXRoIHRoZSBMaWNlbnNlLlxuICogWW91IG1heSBvYnRhaW4gYSBjb3B5IG9mIHRoZSBMaWNlbnNlIGF0XG4gKlxuICogICAgICBodHRwczovL3d3dy5hcGFjaGUub3JnL2xpY2Vuc2VzL0xJQ0VOU0UtMi4wXG4gKlxuICogVW5sZXNzIHJlcXVpcmVkIGJ5IGFwcGxpY2FibGUgbGF3IG9yIGFncmVlZCB0byBpbiB3cml0aW5nLCBzb2Z0d2FyZVxuICogZGlzdHJpYnV0ZWQgdW5kZXIgdGhlIExpY2Vuc2UgaXMgZGlzdHJpYnV0ZWQgb24gYW4gXCJBUyBJU1wiIEJBU0lTLFxuICogV0lUSE9VVCBXQVJSQU5USUVTIE9SIENPTkRJVElPTlMgT0YgQU5ZIEtJTkQsIGVpdGhlciBleHByZXNzIG9yIGltcGxpZWQuXG4gKiBTZWUgdGhlIExpY2Vuc2UgZm9yIHRoZSBzcGVjaWZpYyBsYW5ndWFnZSBnb3Zlcm5pbmcgcGVybWlzc2lvbnMgYW5kXG4gKiBsaW1pdGF0aW9ucyB1bmRlciB0aGUgTGljZW5zZS5cbiAqL1xuLyoqXG4gKiBFbmFibGUgaW5zdHJ1bWVudGF0aW9uc1xuICogQHBhcmFtIGluc3RydW1lbnRhdGlvbnNcbiAqIEBwYXJhbSB0cmFjZXJQcm92aWRlclxuICogQHBhcmFtIG1ldGVyUHJvdmlkZXJcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGVuYWJsZUluc3RydW1lbnRhdGlvbnMoaW5zdHJ1bWVudGF0aW9ucywgdHJhY2VyUHJvdmlkZXIsIG1ldGVyUHJvdmlkZXIsIGxvZ2dlclByb3ZpZGVyKSB7XG4gICAgZm9yICh2YXIgaSA9IDAsIGogPSBpbnN0cnVtZW50YXRpb25zLmxlbmd0aDsgaSA8IGo7IGkrKykge1xuICAgICAgICB2YXIgaW5zdHJ1bWVudGF0aW9uID0gaW5zdHJ1bWVudGF0aW9uc1tpXTtcbiAgICAgICAgaWYgKHRyYWNlclByb3ZpZGVyKSB7XG4gICAgICAgICAgICBpbnN0cnVtZW50YXRpb24uc2V0VHJhY2VyUHJvdmlkZXIodHJhY2VyUHJvdmlkZXIpO1xuICAgICAgICB9XG4gICAgICAgIGlmIChtZXRlclByb3ZpZGVyKSB7XG4gICAgICAgICAgICBpbnN0cnVtZW50YXRpb24uc2V0TWV0ZXJQcm92aWRlcihtZXRlclByb3ZpZGVyKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAobG9nZ2VyUHJvdmlkZXIgJiYgaW5zdHJ1bWVudGF0aW9uLnNldExvZ2dlclByb3ZpZGVyKSB7XG4gICAgICAgICAgICBpbnN0cnVtZW50YXRpb24uc2V0TG9nZ2VyUHJvdmlkZXIobG9nZ2VyUHJvdmlkZXIpO1xuICAgICAgICB9XG4gICAgICAgIC8vIGluc3RydW1lbnRhdGlvbnMgaGF2ZSBiZWVuIGFscmVhZHkgZW5hYmxlZCBkdXJpbmcgY3JlYXRpb25cbiAgICAgICAgLy8gc28gZW5hYmxlIG9ubHkgaWYgdXNlciBwcmV2ZW50ZWQgdGhhdCBieSBzZXR0aW5nIGVuYWJsZWQgdG8gZmFsc2VcbiAgICAgICAgLy8gdGhpcyBpcyB0byBwcmV2ZW50IGRvdWJsZSBlbmFibGluZyBidXQgd2hlbiBjYWxsaW5nIHJlZ2lzdGVyIGFsbFxuICAgICAgICAvLyBpbnN0cnVtZW50YXRpb25zIHNob3VsZCBiZSBub3cgZW5hYmxlZFxuICAgICAgICBpZiAoIWluc3RydW1lbnRhdGlvbi5nZXRDb25maWcoKS5lbmFibGVkKSB7XG4gICAgICAgICAgICBpbnN0cnVtZW50YXRpb24uZW5hYmxlKCk7XG4gICAgICAgIH1cbiAgICB9XG59XG4vKipcbiAqIERpc2FibGUgaW5zdHJ1bWVudGF0aW9uc1xuICogQHBhcmFtIGluc3RydW1lbnRhdGlvbnNcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGRpc2FibGVJbnN0cnVtZW50YXRpb25zKGluc3RydW1lbnRhdGlvbnMpIHtcbiAgICBpbnN0cnVtZW50YXRpb25zLmZvckVhY2goZnVuY3Rpb24gKGluc3RydW1lbnRhdGlvbikgeyByZXR1cm4gaW5zdHJ1bWVudGF0aW9uLmRpc2FibGUoKTsgfSk7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1hdXRvTG9hZGVyVXRpbHMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/autoLoaderUtils.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js":
/*!************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js ***!
  \************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InstrumentationBase: () => (/* reexport safe */ _platform_index__WEBPACK_IMPORTED_MODULE_1__.InstrumentationBase),\n/* harmony export */   InstrumentationNodeModuleDefinition: () => (/* reexport safe */ _instrumentationNodeModuleDefinition__WEBPACK_IMPORTED_MODULE_2__.InstrumentationNodeModuleDefinition),\n/* harmony export */   InstrumentationNodeModuleFile: () => (/* reexport safe */ _instrumentationNodeModuleFile__WEBPACK_IMPORTED_MODULE_3__.InstrumentationNodeModuleFile),\n/* harmony export */   isWrapped: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_4__.isWrapped),\n/* harmony export */   registerInstrumentations: () => (/* reexport safe */ _autoLoader__WEBPACK_IMPORTED_MODULE_0__.registerInstrumentations),\n/* harmony export */   safeExecuteInTheMiddle: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_4__.safeExecuteInTheMiddle),\n/* harmony export */   safeExecuteInTheMiddleAsync: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_4__.safeExecuteInTheMiddleAsync)\n/* harmony export */ });\n/* harmony import */ var _autoLoader__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./autoLoader */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/autoLoader.js\");\n/* harmony import */ var _platform_index__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./platform/index */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/instrumentation.js\");\n/* harmony import */ var _instrumentationNodeModuleDefinition__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./instrumentationNodeModuleDefinition */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/instrumentationNodeModuleDefinition.js\");\n/* harmony import */ var _instrumentationNodeModuleFile__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./instrumentationNodeModuleFile */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/instrumentationNodeModuleFile.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/utils.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n\n\n\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/instrumentation.js":
/*!**********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/instrumentation.js ***!
  \**********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InstrumentationAbstract: () => (/* binding */ InstrumentationAbstract)\n/* harmony export */ });\n/* harmony import */ var _opentelemetry_api__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @opentelemetry/api */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/diag-api.js\");\n/* harmony import */ var _opentelemetry_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @opentelemetry/api */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/trace-api.js\");\n/* harmony import */ var _opentelemetry_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @opentelemetry/api */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/metrics-api.js\");\n/* harmony import */ var _opentelemetry_api_logs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @opentelemetry/api-logs */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+api-logs@0.57.2/node_modules/@opentelemetry/api-logs/build/esm/index.js\");\n/* harmony import */ var shimmer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! shimmer */ \"(ssr)/../../node_modules/.pnpm/shimmer@1.2.1/node_modules/shimmer/index.js\");\n/* harmony import */ var shimmer__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(shimmer__WEBPACK_IMPORTED_MODULE_0__);\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __assign = (undefined && undefined.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\n\n\n\n/**\n * Base abstract internal class for instrumenting node and web plugins\n */\nvar InstrumentationAbstract = /** @class */ (function () {\n    function InstrumentationAbstract(instrumentationName, instrumentationVersion, config) {\n        this.instrumentationName = instrumentationName;\n        this.instrumentationVersion = instrumentationVersion;\n        this._config = {};\n        /* Api to wrap instrumented method */\n        this._wrap = shimmer__WEBPACK_IMPORTED_MODULE_0__.wrap;\n        /* Api to unwrap instrumented methods */\n        this._unwrap = shimmer__WEBPACK_IMPORTED_MODULE_0__.unwrap;\n        /* Api to mass wrap instrumented method */\n        this._massWrap = shimmer__WEBPACK_IMPORTED_MODULE_0__.massWrap;\n        /* Api to mass unwrap instrumented methods */\n        this._massUnwrap = shimmer__WEBPACK_IMPORTED_MODULE_0__.massUnwrap;\n        this.setConfig(config);\n        this._diag = _opentelemetry_api__WEBPACK_IMPORTED_MODULE_1__.diag.createComponentLogger({\n            namespace: instrumentationName,\n        });\n        this._tracer = _opentelemetry_api__WEBPACK_IMPORTED_MODULE_2__.trace.getTracer(instrumentationName, instrumentationVersion);\n        this._meter = _opentelemetry_api__WEBPACK_IMPORTED_MODULE_3__.metrics.getMeter(instrumentationName, instrumentationVersion);\n        this._logger = _opentelemetry_api_logs__WEBPACK_IMPORTED_MODULE_4__.logs.getLogger(instrumentationName, instrumentationVersion);\n        this._updateMetricInstruments();\n    }\n    Object.defineProperty(InstrumentationAbstract.prototype, \"meter\", {\n        /* Returns meter */\n        get: function () {\n            return this._meter;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    /**\n     * Sets MeterProvider to this plugin\n     * @param meterProvider\n     */\n    InstrumentationAbstract.prototype.setMeterProvider = function (meterProvider) {\n        this._meter = meterProvider.getMeter(this.instrumentationName, this.instrumentationVersion);\n        this._updateMetricInstruments();\n    };\n    Object.defineProperty(InstrumentationAbstract.prototype, \"logger\", {\n        /* Returns logger */\n        get: function () {\n            return this._logger;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    /**\n     * Sets LoggerProvider to this plugin\n     * @param loggerProvider\n     */\n    InstrumentationAbstract.prototype.setLoggerProvider = function (loggerProvider) {\n        this._logger = loggerProvider.getLogger(this.instrumentationName, this.instrumentationVersion);\n    };\n    /**\n     * @experimental\n     *\n     * Get module definitions defined by {@link init}.\n     * This can be used for experimental compile-time instrumentation.\n     *\n     * @returns an array of {@link InstrumentationModuleDefinition}\n     */\n    InstrumentationAbstract.prototype.getModuleDefinitions = function () {\n        var _a;\n        var initResult = (_a = this.init()) !== null && _a !== void 0 ? _a : [];\n        if (!Array.isArray(initResult)) {\n            return [initResult];\n        }\n        return initResult;\n    };\n    /**\n     * Sets the new metric instruments with the current Meter.\n     */\n    InstrumentationAbstract.prototype._updateMetricInstruments = function () {\n        return;\n    };\n    /* Returns InstrumentationConfig */\n    InstrumentationAbstract.prototype.getConfig = function () {\n        return this._config;\n    };\n    /**\n     * Sets InstrumentationConfig to this plugin\n     * @param config\n     */\n    InstrumentationAbstract.prototype.setConfig = function (config) {\n        // copy config first level properties to ensure they are immutable.\n        // nested properties are not copied, thus are mutable from the outside.\n        this._config = __assign({ enabled: true }, config);\n    };\n    /**\n     * Sets TraceProvider to this plugin\n     * @param tracerProvider\n     */\n    InstrumentationAbstract.prototype.setTracerProvider = function (tracerProvider) {\n        this._tracer = tracerProvider.getTracer(this.instrumentationName, this.instrumentationVersion);\n    };\n    Object.defineProperty(InstrumentationAbstract.prototype, \"tracer\", {\n        /* Returns tracer */\n        get: function () {\n            return this._tracer;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    /**\n     * Execute span customization hook, if configured, and log any errors.\n     * Any semantics of the trigger and info are defined by the specific instrumentation.\n     * @param hookHandler The optional hook handler which the user has configured via instrumentation config\n     * @param triggerName The name of the trigger for executing the hook for logging purposes\n     * @param span The span to which the hook should be applied\n     * @param info The info object to be passed to the hook, with useful data the hook may use\n     */\n    InstrumentationAbstract.prototype._runSpanCustomizationHook = function (hookHandler, triggerName, span, info) {\n        if (!hookHandler) {\n            return;\n        }\n        try {\n            hookHandler(span, info);\n        }\n        catch (e) {\n            this._diag.error(\"Error running span customization hook due to exception in handler\", { triggerName: triggerName }, e);\n        }\n    };\n    return InstrumentationAbstract;\n}());\n\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/instrumentation.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/instrumentationNodeModuleDefinition.js":
/*!******************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/instrumentationNodeModuleDefinition.js ***!
  \******************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InstrumentationNodeModuleDefinition: () => (/* binding */ InstrumentationNodeModuleDefinition)\n/* harmony export */ });\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar InstrumentationNodeModuleDefinition = /** @class */ (function () {\n    function InstrumentationNodeModuleDefinition(name, supportedVersions, \n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    patch, \n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    unpatch, files) {\n        this.name = name;\n        this.supportedVersions = supportedVersions;\n        this.patch = patch;\n        this.unpatch = unpatch;\n        this.files = files || [];\n    }\n    return InstrumentationNodeModuleDefinition;\n}());\n\n//# sourceMappingURL=instrumentationNodeModuleDefinition.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/instrumentationNodeModuleDefinition.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/instrumentationNodeModuleFile.js":
/*!************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/instrumentationNodeModuleFile.js ***!
  \************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InstrumentationNodeModuleFile: () => (/* binding */ InstrumentationNodeModuleFile)\n/* harmony export */ });\n/* harmony import */ var _platform_index__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./platform/index */ \"path\");\n/* harmony import */ var _platform_index__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_platform_index__WEBPACK_IMPORTED_MODULE_0__);\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nvar InstrumentationNodeModuleFile = /** @class */ (function () {\n    function InstrumentationNodeModuleFile(name, supportedVersions, \n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    patch, \n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    unpatch) {\n        this.supportedVersions = supportedVersions;\n        this.patch = patch;\n        this.unpatch = unpatch;\n        this.name = (0,_platform_index__WEBPACK_IMPORTED_MODULE_0__.normalize)(name);\n    }\n    return InstrumentationNodeModuleFile;\n}());\n\n//# sourceMappingURL=instrumentationNodeModuleFile.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/instrumentationNodeModuleFile.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/ModuleNameTrie.js":
/*!***********************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/ModuleNameTrie.js ***!
  \***********************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ModuleNameSeparator: () => (/* binding */ ModuleNameSeparator),\n/* harmony export */   ModuleNameTrie: () => (/* binding */ ModuleNameTrie)\n/* harmony export */ });\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __values = (undefined && undefined.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nvar __read = (undefined && undefined.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (undefined && undefined.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nvar ModuleNameSeparator = '/';\n/**\n * Node in a `ModuleNameTrie`\n */\nvar ModuleNameTrieNode = /** @class */ (function () {\n    function ModuleNameTrieNode() {\n        this.hooks = [];\n        this.children = new Map();\n    }\n    return ModuleNameTrieNode;\n}());\n/**\n * Trie containing nodes that represent a part of a module name (i.e. the parts separated by forward slash)\n */\nvar ModuleNameTrie = /** @class */ (function () {\n    function ModuleNameTrie() {\n        this._trie = new ModuleNameTrieNode();\n        this._counter = 0;\n    }\n    /**\n     * Insert a module hook into the trie\n     *\n     * @param {Hooked} hook Hook\n     */\n    ModuleNameTrie.prototype.insert = function (hook) {\n        var e_1, _a;\n        var trieNode = this._trie;\n        try {\n            for (var _b = __values(hook.moduleName.split(ModuleNameSeparator)), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var moduleNamePart = _c.value;\n                var nextNode = trieNode.children.get(moduleNamePart);\n                if (!nextNode) {\n                    nextNode = new ModuleNameTrieNode();\n                    trieNode.children.set(moduleNamePart, nextNode);\n                }\n                trieNode = nextNode;\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        trieNode.hooks.push({ hook: hook, insertedId: this._counter++ });\n    };\n    /**\n     * Search for matching hooks in the trie\n     *\n     * @param {string} moduleName Module name\n     * @param {boolean} maintainInsertionOrder Whether to return the results in insertion order\n     * @param {boolean} fullOnly Whether to return only full matches\n     * @returns {Hooked[]} Matching hooks\n     */\n    ModuleNameTrie.prototype.search = function (moduleName, _a) {\n        var e_2, _b;\n        var _c = _a === void 0 ? {} : _a, maintainInsertionOrder = _c.maintainInsertionOrder, fullOnly = _c.fullOnly;\n        var trieNode = this._trie;\n        var results = [];\n        var foundFull = true;\n        try {\n            for (var _d = __values(moduleName.split(ModuleNameSeparator)), _e = _d.next(); !_e.done; _e = _d.next()) {\n                var moduleNamePart = _e.value;\n                var nextNode = trieNode.children.get(moduleNamePart);\n                if (!nextNode) {\n                    foundFull = false;\n                    break;\n                }\n                if (!fullOnly) {\n                    results.push.apply(results, __spreadArray([], __read(nextNode.hooks), false));\n                }\n                trieNode = nextNode;\n            }\n        }\n        catch (e_2_1) { e_2 = { error: e_2_1 }; }\n        finally {\n            try {\n                if (_e && !_e.done && (_b = _d.return)) _b.call(_d);\n            }\n            finally { if (e_2) throw e_2.error; }\n        }\n        if (fullOnly && foundFull) {\n            results.push.apply(results, __spreadArray([], __read(trieNode.hooks), false));\n        }\n        if (results.length === 0) {\n            return [];\n        }\n        if (results.length === 1) {\n            return [results[0].hook];\n        }\n        if (maintainInsertionOrder) {\n            results.sort(function (a, b) { return a.insertedId - b.insertedId; });\n        }\n        return results.map(function (_a) {\n            var hook = _a.hook;\n            return hook;\n        });\n    };\n    return ModuleNameTrie;\n}());\n\n//# sourceMappingURL=ModuleNameTrie.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/ModuleNameTrie.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/RequireInTheMiddleSingleton.js":
/*!************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/RequireInTheMiddleSingleton.js ***!
  \************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RequireInTheMiddleSingleton: () => (/* binding */ RequireInTheMiddleSingleton)\n/* harmony export */ });\n/* harmony import */ var require_in_the_middle__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! require-in-the-middle */ \"require-in-the-middle\");\n/* harmony import */ var require_in_the_middle__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(require_in_the_middle__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ModuleNameTrie__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ModuleNameTrie */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/ModuleNameTrie.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __values = (undefined && undefined.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n\n\n\n/**\n * Whether Mocha is running in this process\n * Inspired by https://github.com/AndreasPizsa/detect-mocha\n *\n * @type {boolean}\n */\nvar isMocha = [\n    'afterEach',\n    'after',\n    'beforeEach',\n    'before',\n    'describe',\n    'it',\n].every(function (fn) {\n    // @ts-expect-error TS7053: Element implicitly has an 'any' type\n    return typeof global[fn] === 'function';\n});\n/**\n * Singleton class for `require-in-the-middle`\n * Allows instrumentation plugins to patch modules with only a single `require` patch\n * WARNING: Because this class will create its own `require-in-the-middle` (RITM) instance,\n * we should minimize the number of new instances of this class.\n * Multiple instances of `@opentelemetry/instrumentation` (e.g. multiple versions) in a single process\n * will result in multiple instances of RITM, which will have an impact\n * on the performance of instrumentation hooks being applied.\n */\nvar RequireInTheMiddleSingleton = /** @class */ (function () {\n    function RequireInTheMiddleSingleton() {\n        this._moduleNameTrie = new _ModuleNameTrie__WEBPACK_IMPORTED_MODULE_2__.ModuleNameTrie();\n        this._initialize();\n    }\n    RequireInTheMiddleSingleton.prototype._initialize = function () {\n        var _this = this;\n        new require_in_the_middle__WEBPACK_IMPORTED_MODULE_0__.Hook(\n        // Intercept all `require` calls; we will filter the matching ones below\n        null, { internals: true }, function (exports, name, basedir) {\n            var e_1, _a;\n            // For internal files on Windows, `name` will use backslash as the path separator\n            var normalizedModuleName = normalizePathSeparators(name);\n            var matches = _this._moduleNameTrie.search(normalizedModuleName, {\n                maintainInsertionOrder: true,\n                // For core modules (e.g. `fs`), do not match on sub-paths (e.g. `fs/promises').\n                // This matches the behavior of `require-in-the-middle`.\n                // `basedir` is always `undefined` for core modules.\n                fullOnly: basedir === undefined,\n            });\n            try {\n                for (var matches_1 = __values(matches), matches_1_1 = matches_1.next(); !matches_1_1.done; matches_1_1 = matches_1.next()) {\n                    var onRequire = matches_1_1.value.onRequire;\n                    exports = onRequire(exports, name, basedir);\n                }\n            }\n            catch (e_1_1) { e_1 = { error: e_1_1 }; }\n            finally {\n                try {\n                    if (matches_1_1 && !matches_1_1.done && (_a = matches_1.return)) _a.call(matches_1);\n                }\n                finally { if (e_1) throw e_1.error; }\n            }\n            return exports;\n        });\n    };\n    /**\n     * Register a hook with `require-in-the-middle`\n     *\n     * @param {string} moduleName Module name\n     * @param {OnRequireFn} onRequire Hook function\n     * @returns {Hooked} Registered hook\n     */\n    RequireInTheMiddleSingleton.prototype.register = function (moduleName, onRequire) {\n        var hooked = { moduleName: moduleName, onRequire: onRequire };\n        this._moduleNameTrie.insert(hooked);\n        return hooked;\n    };\n    /**\n     * Get the `RequireInTheMiddleSingleton` singleton\n     *\n     * @returns {RequireInTheMiddleSingleton} Singleton of `RequireInTheMiddleSingleton`\n     */\n    RequireInTheMiddleSingleton.getInstance = function () {\n        var _a;\n        // Mocha runs all test suites in the same process\n        // This prevents test suites from sharing a singleton\n        if (isMocha)\n            return new RequireInTheMiddleSingleton();\n        return (this._instance =\n            (_a = this._instance) !== null && _a !== void 0 ? _a : new RequireInTheMiddleSingleton());\n    };\n    return RequireInTheMiddleSingleton;\n}());\n\n/**\n * Normalize the path separators to forward slash in a module name or path\n *\n * @param {string} moduleNameOrPath Module name or path\n * @returns {string} Normalized module name or path\n */\nfunction normalizePathSeparators(moduleNameOrPath) {\n    return path__WEBPACK_IMPORTED_MODULE_1__.sep !== _ModuleNameTrie__WEBPACK_IMPORTED_MODULE_2__.ModuleNameSeparator\n        ? moduleNameOrPath.split(path__WEBPACK_IMPORTED_MODULE_1__.sep).join(_ModuleNameTrie__WEBPACK_IMPORTED_MODULE_2__.ModuleNameSeparator)\n        : moduleNameOrPath;\n}\n//# sourceMappingURL=RequireInTheMiddleSingleton.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF8wNGYzNzBkNTE1Y2VlMGJlOTU1MjcyZjgyNjE2NjA3My9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uL2J1aWxkL2VzbS9wbGF0Zm9ybS9ub2RlL1JlcXVpcmVJblRoZU1pZGRsZVNpbmdsZXRvbi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IsU0FBSSxJQUFJLFNBQUk7QUFDNUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQjtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUM2QztBQUNoQjtBQUMwQztBQUN2RTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQ0FBbUMsMkRBQWM7QUFDakQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLHVEQUFJO0FBQ2hCLDBDQUEwQztBQUMxQyxnQkFBZ0IsaUJBQWlCO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBLHdGQUF3RixtQkFBbUI7QUFDM0c7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0QkFBNEIsUUFBUTtBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBLDBCQUEwQjtBQUMxQjtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsYUFBYTtBQUM1QixpQkFBaUIsUUFBUTtBQUN6QjtBQUNBO0FBQ0EsdUJBQXVCO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQiw2QkFBNkI7QUFDOUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDc0M7QUFDdkM7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CLGFBQWEsUUFBUTtBQUNyQjtBQUNBO0FBQ0EsV0FBVyxxQ0FBUSxLQUFLLGdFQUFtQjtBQUMzQyxpQ0FBaUMscUNBQVEsT0FBTyxnRUFBbUI7QUFDbkU7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFud2FyXFxEb2N1bWVudHNcXEN1YmVudHdlYlxcY3ViZW50XFxub2RlX21vZHVsZXNcXC5wbnBtXFxAb3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfMDRmMzcwZDUxNWNlZTBiZTk1NTI3MmY4MjYxNjYwNzNcXG5vZGVfbW9kdWxlc1xcQG9wZW50ZWxlbWV0cnlcXGluc3RydW1lbnRhdGlvblxcYnVpbGRcXGVzbVxccGxhdGZvcm1cXG5vZGVcXFJlcXVpcmVJblRoZU1pZGRsZVNpbmdsZXRvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKlxuICogQ29weXJpZ2h0IFRoZSBPcGVuVGVsZW1ldHJ5IEF1dGhvcnNcbiAqXG4gKiBMaWNlbnNlZCB1bmRlciB0aGUgQXBhY2hlIExpY2Vuc2UsIFZlcnNpb24gMi4wICh0aGUgXCJMaWNlbnNlXCIpO1xuICogeW91IG1heSBub3QgdXNlIHRoaXMgZmlsZSBleGNlcHQgaW4gY29tcGxpYW5jZSB3aXRoIHRoZSBMaWNlbnNlLlxuICogWW91IG1heSBvYnRhaW4gYSBjb3B5IG9mIHRoZSBMaWNlbnNlIGF0XG4gKlxuICogICAgICBodHRwczovL3d3dy5hcGFjaGUub3JnL2xpY2Vuc2VzL0xJQ0VOU0UtMi4wXG4gKlxuICogVW5sZXNzIHJlcXVpcmVkIGJ5IGFwcGxpY2FibGUgbGF3IG9yIGFncmVlZCB0byBpbiB3cml0aW5nLCBzb2Z0d2FyZVxuICogZGlzdHJpYnV0ZWQgdW5kZXIgdGhlIExpY2Vuc2UgaXMgZGlzdHJpYnV0ZWQgb24gYW4gXCJBUyBJU1wiIEJBU0lTLFxuICogV0lUSE9VVCBXQVJSQU5USUVTIE9SIENPTkRJVElPTlMgT0YgQU5ZIEtJTkQsIGVpdGhlciBleHByZXNzIG9yIGltcGxpZWQuXG4gKiBTZWUgdGhlIExpY2Vuc2UgZm9yIHRoZSBzcGVjaWZpYyBsYW5ndWFnZSBnb3Zlcm5pbmcgcGVybWlzc2lvbnMgYW5kXG4gKiBsaW1pdGF0aW9ucyB1bmRlciB0aGUgTGljZW5zZS5cbiAqL1xudmFyIF9fdmFsdWVzID0gKHRoaXMgJiYgdGhpcy5fX3ZhbHVlcykgfHwgZnVuY3Rpb24obykge1xuICAgIHZhciBzID0gdHlwZW9mIFN5bWJvbCA9PT0gXCJmdW5jdGlvblwiICYmIFN5bWJvbC5pdGVyYXRvciwgbSA9IHMgJiYgb1tzXSwgaSA9IDA7XG4gICAgaWYgKG0pIHJldHVybiBtLmNhbGwobyk7XG4gICAgaWYgKG8gJiYgdHlwZW9mIG8ubGVuZ3RoID09PSBcIm51bWJlclwiKSByZXR1cm4ge1xuICAgICAgICBuZXh0OiBmdW5jdGlvbiAoKSB7XG4gICAgICAgICAgICBpZiAobyAmJiBpID49IG8ubGVuZ3RoKSBvID0gdm9pZCAwO1xuICAgICAgICAgICAgcmV0dXJuIHsgdmFsdWU6IG8gJiYgb1tpKytdLCBkb25lOiAhbyB9O1xuICAgICAgICB9XG4gICAgfTtcbiAgICB0aHJvdyBuZXcgVHlwZUVycm9yKHMgPyBcIk9iamVjdCBpcyBub3QgaXRlcmFibGUuXCIgOiBcIlN5bWJvbC5pdGVyYXRvciBpcyBub3QgZGVmaW5lZC5cIik7XG59O1xuaW1wb3J0IHsgSG9vayB9IGZyb20gJ3JlcXVpcmUtaW4tdGhlLW1pZGRsZSc7XG5pbXBvcnQgKiBhcyBwYXRoIGZyb20gJ3BhdGgnO1xuaW1wb3J0IHsgTW9kdWxlTmFtZVRyaWUsIE1vZHVsZU5hbWVTZXBhcmF0b3IgfSBmcm9tICcuL01vZHVsZU5hbWVUcmllJztcbi8qKlxuICogV2hldGhlciBNb2NoYSBpcyBydW5uaW5nIGluIHRoaXMgcHJvY2Vzc1xuICogSW5zcGlyZWQgYnkgaHR0cHM6Ly9naXRodWIuY29tL0FuZHJlYXNQaXpzYS9kZXRlY3QtbW9jaGFcbiAqXG4gKiBAdHlwZSB7Ym9vbGVhbn1cbiAqL1xudmFyIGlzTW9jaGEgPSBbXG4gICAgJ2FmdGVyRWFjaCcsXG4gICAgJ2FmdGVyJyxcbiAgICAnYmVmb3JlRWFjaCcsXG4gICAgJ2JlZm9yZScsXG4gICAgJ2Rlc2NyaWJlJyxcbiAgICAnaXQnLFxuXS5ldmVyeShmdW5jdGlvbiAoZm4pIHtcbiAgICAvLyBAdHMtZXhwZWN0LWVycm9yIFRTNzA1MzogRWxlbWVudCBpbXBsaWNpdGx5IGhhcyBhbiAnYW55JyB0eXBlXG4gICAgcmV0dXJuIHR5cGVvZiBnbG9iYWxbZm5dID09PSAnZnVuY3Rpb24nO1xufSk7XG4vKipcbiAqIFNpbmdsZXRvbiBjbGFzcyBmb3IgYHJlcXVpcmUtaW4tdGhlLW1pZGRsZWBcbiAqIEFsbG93cyBpbnN0cnVtZW50YXRpb24gcGx1Z2lucyB0byBwYXRjaCBtb2R1bGVzIHdpdGggb25seSBhIHNpbmdsZSBgcmVxdWlyZWAgcGF0Y2hcbiAqIFdBUk5JTkc6IEJlY2F1c2UgdGhpcyBjbGFzcyB3aWxsIGNyZWF0ZSBpdHMgb3duIGByZXF1aXJlLWluLXRoZS1taWRkbGVgIChSSVRNKSBpbnN0YW5jZSxcbiAqIHdlIHNob3VsZCBtaW5pbWl6ZSB0aGUgbnVtYmVyIG9mIG5ldyBpbnN0YW5jZXMgb2YgdGhpcyBjbGFzcy5cbiAqIE11bHRpcGxlIGluc3RhbmNlcyBvZiBgQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uYCAoZS5nLiBtdWx0aXBsZSB2ZXJzaW9ucykgaW4gYSBzaW5nbGUgcHJvY2Vzc1xuICogd2lsbCByZXN1bHQgaW4gbXVsdGlwbGUgaW5zdGFuY2VzIG9mIFJJVE0sIHdoaWNoIHdpbGwgaGF2ZSBhbiBpbXBhY3RcbiAqIG9uIHRoZSBwZXJmb3JtYW5jZSBvZiBpbnN0cnVtZW50YXRpb24gaG9va3MgYmVpbmcgYXBwbGllZC5cbiAqL1xudmFyIFJlcXVpcmVJblRoZU1pZGRsZVNpbmdsZXRvbiA9IC8qKiBAY2xhc3MgKi8gKGZ1bmN0aW9uICgpIHtcbiAgICBmdW5jdGlvbiBSZXF1aXJlSW5UaGVNaWRkbGVTaW5nbGV0b24oKSB7XG4gICAgICAgIHRoaXMuX21vZHVsZU5hbWVUcmllID0gbmV3IE1vZHVsZU5hbWVUcmllKCk7XG4gICAgICAgIHRoaXMuX2luaXRpYWxpemUoKTtcbiAgICB9XG4gICAgUmVxdWlyZUluVGhlTWlkZGxlU2luZ2xldG9uLnByb3RvdHlwZS5faW5pdGlhbGl6ZSA9IGZ1bmN0aW9uICgpIHtcbiAgICAgICAgdmFyIF90aGlzID0gdGhpcztcbiAgICAgICAgbmV3IEhvb2soXG4gICAgICAgIC8vIEludGVyY2VwdCBhbGwgYHJlcXVpcmVgIGNhbGxzOyB3ZSB3aWxsIGZpbHRlciB0aGUgbWF0Y2hpbmcgb25lcyBiZWxvd1xuICAgICAgICBudWxsLCB7IGludGVybmFsczogdHJ1ZSB9LCBmdW5jdGlvbiAoZXhwb3J0cywgbmFtZSwgYmFzZWRpcikge1xuICAgICAgICAgICAgdmFyIGVfMSwgX2E7XG4gICAgICAgICAgICAvLyBGb3IgaW50ZXJuYWwgZmlsZXMgb24gV2luZG93cywgYG5hbWVgIHdpbGwgdXNlIGJhY2tzbGFzaCBhcyB0aGUgcGF0aCBzZXBhcmF0b3JcbiAgICAgICAgICAgIHZhciBub3JtYWxpemVkTW9kdWxlTmFtZSA9IG5vcm1hbGl6ZVBhdGhTZXBhcmF0b3JzKG5hbWUpO1xuICAgICAgICAgICAgdmFyIG1hdGNoZXMgPSBfdGhpcy5fbW9kdWxlTmFtZVRyaWUuc2VhcmNoKG5vcm1hbGl6ZWRNb2R1bGVOYW1lLCB7XG4gICAgICAgICAgICAgICAgbWFpbnRhaW5JbnNlcnRpb25PcmRlcjogdHJ1ZSxcbiAgICAgICAgICAgICAgICAvLyBGb3IgY29yZSBtb2R1bGVzIChlLmcuIGBmc2ApLCBkbyBub3QgbWF0Y2ggb24gc3ViLXBhdGhzIChlLmcuIGBmcy9wcm9taXNlcycpLlxuICAgICAgICAgICAgICAgIC8vIFRoaXMgbWF0Y2hlcyB0aGUgYmVoYXZpb3Igb2YgYHJlcXVpcmUtaW4tdGhlLW1pZGRsZWAuXG4gICAgICAgICAgICAgICAgLy8gYGJhc2VkaXJgIGlzIGFsd2F5cyBgdW5kZWZpbmVkYCBmb3IgY29yZSBtb2R1bGVzLlxuICAgICAgICAgICAgICAgIGZ1bGxPbmx5OiBiYXNlZGlyID09PSB1bmRlZmluZWQsXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgZm9yICh2YXIgbWF0Y2hlc18xID0gX192YWx1ZXMobWF0Y2hlcyksIG1hdGNoZXNfMV8xID0gbWF0Y2hlc18xLm5leHQoKTsgIW1hdGNoZXNfMV8xLmRvbmU7IG1hdGNoZXNfMV8xID0gbWF0Y2hlc18xLm5leHQoKSkge1xuICAgICAgICAgICAgICAgICAgICB2YXIgb25SZXF1aXJlID0gbWF0Y2hlc18xXzEudmFsdWUub25SZXF1aXJlO1xuICAgICAgICAgICAgICAgICAgICBleHBvcnRzID0gb25SZXF1aXJlKGV4cG9ydHMsIG5hbWUsIGJhc2VkaXIpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNhdGNoIChlXzFfMSkgeyBlXzEgPSB7IGVycm9yOiBlXzFfMSB9OyB9XG4gICAgICAgICAgICBmaW5hbGx5IHtcbiAgICAgICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgICAgICBpZiAobWF0Y2hlc18xXzEgJiYgIW1hdGNoZXNfMV8xLmRvbmUgJiYgKF9hID0gbWF0Y2hlc18xLnJldHVybikpIF9hLmNhbGwobWF0Y2hlc18xKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZmluYWxseSB7IGlmIChlXzEpIHRocm93IGVfMS5lcnJvcjsgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcmV0dXJuIGV4cG9ydHM7XG4gICAgICAgIH0pO1xuICAgIH07XG4gICAgLyoqXG4gICAgICogUmVnaXN0ZXIgYSBob29rIHdpdGggYHJlcXVpcmUtaW4tdGhlLW1pZGRsZWBcbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBtb2R1bGVOYW1lIE1vZHVsZSBuYW1lXG4gICAgICogQHBhcmFtIHtPblJlcXVpcmVGbn0gb25SZXF1aXJlIEhvb2sgZnVuY3Rpb25cbiAgICAgKiBAcmV0dXJucyB7SG9va2VkfSBSZWdpc3RlcmVkIGhvb2tcbiAgICAgKi9cbiAgICBSZXF1aXJlSW5UaGVNaWRkbGVTaW5nbGV0b24ucHJvdG90eXBlLnJlZ2lzdGVyID0gZnVuY3Rpb24gKG1vZHVsZU5hbWUsIG9uUmVxdWlyZSkge1xuICAgICAgICB2YXIgaG9va2VkID0geyBtb2R1bGVOYW1lOiBtb2R1bGVOYW1lLCBvblJlcXVpcmU6IG9uUmVxdWlyZSB9O1xuICAgICAgICB0aGlzLl9tb2R1bGVOYW1lVHJpZS5pbnNlcnQoaG9va2VkKTtcbiAgICAgICAgcmV0dXJuIGhvb2tlZDtcbiAgICB9O1xuICAgIC8qKlxuICAgICAqIEdldCB0aGUgYFJlcXVpcmVJblRoZU1pZGRsZVNpbmdsZXRvbmAgc2luZ2xldG9uXG4gICAgICpcbiAgICAgKiBAcmV0dXJucyB7UmVxdWlyZUluVGhlTWlkZGxlU2luZ2xldG9ufSBTaW5nbGV0b24gb2YgYFJlcXVpcmVJblRoZU1pZGRsZVNpbmdsZXRvbmBcbiAgICAgKi9cbiAgICBSZXF1aXJlSW5UaGVNaWRkbGVTaW5nbGV0b24uZ2V0SW5zdGFuY2UgPSBmdW5jdGlvbiAoKSB7XG4gICAgICAgIHZhciBfYTtcbiAgICAgICAgLy8gTW9jaGEgcnVucyBhbGwgdGVzdCBzdWl0ZXMgaW4gdGhlIHNhbWUgcHJvY2Vzc1xuICAgICAgICAvLyBUaGlzIHByZXZlbnRzIHRlc3Qgc3VpdGVzIGZyb20gc2hhcmluZyBhIHNpbmdsZXRvblxuICAgICAgICBpZiAoaXNNb2NoYSlcbiAgICAgICAgICAgIHJldHVybiBuZXcgUmVxdWlyZUluVGhlTWlkZGxlU2luZ2xldG9uKCk7XG4gICAgICAgIHJldHVybiAodGhpcy5faW5zdGFuY2UgPVxuICAgICAgICAgICAgKF9hID0gdGhpcy5faW5zdGFuY2UpICE9PSBudWxsICYmIF9hICE9PSB2b2lkIDAgPyBfYSA6IG5ldyBSZXF1aXJlSW5UaGVNaWRkbGVTaW5nbGV0b24oKSk7XG4gICAgfTtcbiAgICByZXR1cm4gUmVxdWlyZUluVGhlTWlkZGxlU2luZ2xldG9uO1xufSgpKTtcbmV4cG9ydCB7IFJlcXVpcmVJblRoZU1pZGRsZVNpbmdsZXRvbiB9O1xuLyoqXG4gKiBOb3JtYWxpemUgdGhlIHBhdGggc2VwYXJhdG9ycyB0byBmb3J3YXJkIHNsYXNoIGluIGEgbW9kdWxlIG5hbWUgb3IgcGF0aFxuICpcbiAqIEBwYXJhbSB7c3RyaW5nfSBtb2R1bGVOYW1lT3JQYXRoIE1vZHVsZSBuYW1lIG9yIHBhdGhcbiAqIEByZXR1cm5zIHtzdHJpbmd9IE5vcm1hbGl6ZWQgbW9kdWxlIG5hbWUgb3IgcGF0aFxuICovXG5mdW5jdGlvbiBub3JtYWxpemVQYXRoU2VwYXJhdG9ycyhtb2R1bGVOYW1lT3JQYXRoKSB7XG4gICAgcmV0dXJuIHBhdGguc2VwICE9PSBNb2R1bGVOYW1lU2VwYXJhdG9yXG4gICAgICAgID8gbW9kdWxlTmFtZU9yUGF0aC5zcGxpdChwYXRoLnNlcCkuam9pbihNb2R1bGVOYW1lU2VwYXJhdG9yKVxuICAgICAgICA6IG1vZHVsZU5hbWVPclBhdGg7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1SZXF1aXJlSW5UaGVNaWRkbGVTaW5nbGV0b24uanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/RequireInTheMiddleSingleton.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/instrumentation.js":
/*!************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/instrumentation.js ***!
  \************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InstrumentationBase: () => (/* binding */ InstrumentationBase)\n/* harmony export */ });\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var util__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! util */ \"util\");\n/* harmony import */ var util__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(util__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var semver__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! semver */ \"(ssr)/../../node_modules/.pnpm/semver@7.7.1/node_modules/semver/index.js\");\n/* harmony import */ var semver__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(semver__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var shimmer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! shimmer */ \"(ssr)/../../node_modules/.pnpm/shimmer@1.2.1/node_modules/shimmer/index.js\");\n/* harmony import */ var shimmer__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(shimmer__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _instrumentation__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../instrumentation */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/instrumentation.js\");\n/* harmony import */ var _RequireInTheMiddleSingleton__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./RequireInTheMiddleSingleton */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/RequireInTheMiddleSingleton.js\");\n/* harmony import */ var import_in_the_middle__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! import-in-the-middle */ \"import-in-the-middle\");\n/* harmony import */ var import_in_the_middle__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(import_in_the_middle__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _opentelemetry_api__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @opentelemetry/api */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/diag-api.js\");\n/* harmony import */ var require_in_the_middle__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! require-in-the-middle */ \"require-in-the-middle\");\n/* harmony import */ var require_in_the_middle__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(require_in_the_middle__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../utils */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/utils.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __values = (undefined && undefined.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n\n\n\n\n\n\n\n\n\n\n\n/**\n * Base abstract class for instrumenting node plugins\n */\nvar InstrumentationBase = /** @class */ (function (_super) {\n    __extends(InstrumentationBase, _super);\n    function InstrumentationBase(instrumentationName, instrumentationVersion, config) {\n        var _this = _super.call(this, instrumentationName, instrumentationVersion, config) || this;\n        _this._hooks = [];\n        _this._requireInTheMiddleSingleton = _RequireInTheMiddleSingleton__WEBPACK_IMPORTED_MODULE_7__.RequireInTheMiddleSingleton.getInstance();\n        _this._enabled = false;\n        _this._wrap = function (moduleExports, name, wrapper) {\n            if ((0,_utils__WEBPACK_IMPORTED_MODULE_8__.isWrapped)(moduleExports[name])) {\n                _this._unwrap(moduleExports, name);\n            }\n            if (!util__WEBPACK_IMPORTED_MODULE_1__.types.isProxy(moduleExports)) {\n                return (0,shimmer__WEBPACK_IMPORTED_MODULE_3__.wrap)(moduleExports, name, wrapper);\n            }\n            else {\n                var wrapped = (0,shimmer__WEBPACK_IMPORTED_MODULE_3__.wrap)(Object.assign({}, moduleExports), name, wrapper);\n                Object.defineProperty(moduleExports, name, {\n                    value: wrapped,\n                });\n                return wrapped;\n            }\n        };\n        _this._unwrap = function (moduleExports, name) {\n            if (!util__WEBPACK_IMPORTED_MODULE_1__.types.isProxy(moduleExports)) {\n                return (0,shimmer__WEBPACK_IMPORTED_MODULE_3__.unwrap)(moduleExports, name);\n            }\n            else {\n                return Object.defineProperty(moduleExports, name, {\n                    value: moduleExports[name],\n                });\n            }\n        };\n        _this._massWrap = function (moduleExportsArray, names, wrapper) {\n            if (!moduleExportsArray) {\n                _opentelemetry_api__WEBPACK_IMPORTED_MODULE_9__.diag.error('must provide one or more modules to patch');\n                return;\n            }\n            else if (!Array.isArray(moduleExportsArray)) {\n                moduleExportsArray = [moduleExportsArray];\n            }\n            if (!(names && Array.isArray(names))) {\n                _opentelemetry_api__WEBPACK_IMPORTED_MODULE_9__.diag.error('must provide one or more functions to wrap on modules');\n                return;\n            }\n            moduleExportsArray.forEach(function (moduleExports) {\n                names.forEach(function (name) {\n                    _this._wrap(moduleExports, name, wrapper);\n                });\n            });\n        };\n        _this._massUnwrap = function (moduleExportsArray, names) {\n            if (!moduleExportsArray) {\n                _opentelemetry_api__WEBPACK_IMPORTED_MODULE_9__.diag.error('must provide one or more modules to patch');\n                return;\n            }\n            else if (!Array.isArray(moduleExportsArray)) {\n                moduleExportsArray = [moduleExportsArray];\n            }\n            if (!(names && Array.isArray(names))) {\n                _opentelemetry_api__WEBPACK_IMPORTED_MODULE_9__.diag.error('must provide one or more functions to wrap on modules');\n                return;\n            }\n            moduleExportsArray.forEach(function (moduleExports) {\n                names.forEach(function (name) {\n                    _this._unwrap(moduleExports, name);\n                });\n            });\n        };\n        var modules = _this.init();\n        if (modules && !Array.isArray(modules)) {\n            modules = [modules];\n        }\n        _this._modules = modules || [];\n        if (_this._config.enabled) {\n            _this.enable();\n        }\n        return _this;\n    }\n    InstrumentationBase.prototype._warnOnPreloadedModules = function () {\n        var _this = this;\n        this._modules.forEach(function (module) {\n            var name = module.name;\n            try {\n                var resolvedModule = /*require.resolve*/(__webpack_require__(\"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/platform/node sync recursive\").resolve(name));\n                if (__webpack_require__.c[resolvedModule]) {\n                    // Module is already cached, which means the instrumentation hook might not work\n                    _this._diag.warn(\"Module \" + name + \" has been loaded before \" + _this.instrumentationName + \" so it might not work, please initialize it before requiring \" + name);\n                }\n            }\n            catch (_a) {\n                // Module isn't available, we can simply skip\n            }\n        });\n    };\n    InstrumentationBase.prototype._extractPackageVersion = function (baseDir) {\n        try {\n            var json = (0,fs__WEBPACK_IMPORTED_MODULE_6__.readFileSync)(path__WEBPACK_IMPORTED_MODULE_0__.join(baseDir, 'package.json'), {\n                encoding: 'utf8',\n            });\n            var version = JSON.parse(json).version;\n            return typeof version === 'string' ? version : undefined;\n        }\n        catch (error) {\n            _opentelemetry_api__WEBPACK_IMPORTED_MODULE_9__.diag.warn('Failed extracting version', baseDir);\n        }\n        return undefined;\n    };\n    InstrumentationBase.prototype._onRequire = function (module, exports, name, baseDir) {\n        var _this = this;\n        var _a;\n        if (!baseDir) {\n            if (typeof module.patch === 'function') {\n                module.moduleExports = exports;\n                if (this._enabled) {\n                    this._diag.debug('Applying instrumentation patch for nodejs core module on require hook', {\n                        module: module.name,\n                    });\n                    return module.patch(exports);\n                }\n            }\n            return exports;\n        }\n        var version = this._extractPackageVersion(baseDir);\n        module.moduleVersion = version;\n        if (module.name === name) {\n            // main module\n            if (isSupported(module.supportedVersions, version, module.includePrerelease)) {\n                if (typeof module.patch === 'function') {\n                    module.moduleExports = exports;\n                    if (this._enabled) {\n                        this._diag.debug('Applying instrumentation patch for module on require hook', {\n                            module: module.name,\n                            version: module.moduleVersion,\n                            baseDir: baseDir,\n                        });\n                        return module.patch(exports, module.moduleVersion);\n                    }\n                }\n            }\n            return exports;\n        }\n        // internal file\n        var files = (_a = module.files) !== null && _a !== void 0 ? _a : [];\n        var normalizedName = path__WEBPACK_IMPORTED_MODULE_0__.normalize(name);\n        var supportedFileInstrumentations = files\n            .filter(function (f) { return f.name === normalizedName; })\n            .filter(function (f) {\n            return isSupported(f.supportedVersions, version, module.includePrerelease);\n        });\n        return supportedFileInstrumentations.reduce(function (patchedExports, file) {\n            file.moduleExports = patchedExports;\n            if (_this._enabled) {\n                _this._diag.debug('Applying instrumentation patch for nodejs module file on require hook', {\n                    module: module.name,\n                    version: module.moduleVersion,\n                    fileName: file.name,\n                    baseDir: baseDir,\n                });\n                // patch signature is not typed, so we cast it assuming it's correct\n                return file.patch(patchedExports, module.moduleVersion);\n            }\n            return patchedExports;\n        }, exports);\n    };\n    InstrumentationBase.prototype.enable = function () {\n        var e_1, _a, e_2, _b, e_3, _c;\n        var _this = this;\n        if (this._enabled) {\n            return;\n        }\n        this._enabled = true;\n        // already hooked, just call patch again\n        if (this._hooks.length > 0) {\n            try {\n                for (var _d = __values(this._modules), _e = _d.next(); !_e.done; _e = _d.next()) {\n                    var module_1 = _e.value;\n                    if (typeof module_1.patch === 'function' && module_1.moduleExports) {\n                        this._diag.debug('Applying instrumentation patch for nodejs module on instrumentation enabled', {\n                            module: module_1.name,\n                            version: module_1.moduleVersion,\n                        });\n                        module_1.patch(module_1.moduleExports, module_1.moduleVersion);\n                    }\n                    try {\n                        for (var _f = (e_2 = void 0, __values(module_1.files)), _g = _f.next(); !_g.done; _g = _f.next()) {\n                            var file = _g.value;\n                            if (file.moduleExports) {\n                                this._diag.debug('Applying instrumentation patch for nodejs module file on instrumentation enabled', {\n                                    module: module_1.name,\n                                    version: module_1.moduleVersion,\n                                    fileName: file.name,\n                                });\n                                file.patch(file.moduleExports, module_1.moduleVersion);\n                            }\n                        }\n                    }\n                    catch (e_2_1) { e_2 = { error: e_2_1 }; }\n                    finally {\n                        try {\n                            if (_g && !_g.done && (_b = _f.return)) _b.call(_f);\n                        }\n                        finally { if (e_2) throw e_2.error; }\n                    }\n                }\n            }\n            catch (e_1_1) { e_1 = { error: e_1_1 }; }\n            finally {\n                try {\n                    if (_e && !_e.done && (_a = _d.return)) _a.call(_d);\n                }\n                finally { if (e_1) throw e_1.error; }\n            }\n            return;\n        }\n        this._warnOnPreloadedModules();\n        var _loop_1 = function (module_2) {\n            var hookFn = function (exports, name, baseDir) {\n                if (!baseDir && path__WEBPACK_IMPORTED_MODULE_0__.isAbsolute(name)) {\n                    var parsedPath = path__WEBPACK_IMPORTED_MODULE_0__.parse(name);\n                    name = parsedPath.name;\n                    baseDir = parsedPath.dir;\n                }\n                return _this._onRequire(module_2, exports, name, baseDir);\n            };\n            var onRequire = function (exports, name, baseDir) {\n                return _this._onRequire(module_2, exports, name, baseDir);\n            };\n            // `RequireInTheMiddleSingleton` does not support absolute paths.\n            // For an absolute paths, we must create a separate instance of the\n            // require-in-the-middle `Hook`.\n            var hook = path__WEBPACK_IMPORTED_MODULE_0__.isAbsolute(module_2.name)\n                ? new require_in_the_middle__WEBPACK_IMPORTED_MODULE_5__.Hook([module_2.name], { internals: true }, onRequire)\n                : this_1._requireInTheMiddleSingleton.register(module_2.name, onRequire);\n            this_1._hooks.push(hook);\n            var esmHook = new import_in_the_middle__WEBPACK_IMPORTED_MODULE_4__.Hook([module_2.name], { internals: false }, hookFn);\n            this_1._hooks.push(esmHook);\n        };\n        var this_1 = this;\n        try {\n            for (var _h = __values(this._modules), _j = _h.next(); !_j.done; _j = _h.next()) {\n                var module_2 = _j.value;\n                _loop_1(module_2);\n            }\n        }\n        catch (e_3_1) { e_3 = { error: e_3_1 }; }\n        finally {\n            try {\n                if (_j && !_j.done && (_c = _h.return)) _c.call(_h);\n            }\n            finally { if (e_3) throw e_3.error; }\n        }\n    };\n    InstrumentationBase.prototype.disable = function () {\n        var e_4, _a, e_5, _b;\n        if (!this._enabled) {\n            return;\n        }\n        this._enabled = false;\n        try {\n            for (var _c = __values(this._modules), _d = _c.next(); !_d.done; _d = _c.next()) {\n                var module_3 = _d.value;\n                if (typeof module_3.unpatch === 'function' && module_3.moduleExports) {\n                    this._diag.debug('Removing instrumentation patch for nodejs module on instrumentation disabled', {\n                        module: module_3.name,\n                        version: module_3.moduleVersion,\n                    });\n                    module_3.unpatch(module_3.moduleExports, module_3.moduleVersion);\n                }\n                try {\n                    for (var _e = (e_5 = void 0, __values(module_3.files)), _f = _e.next(); !_f.done; _f = _e.next()) {\n                        var file = _f.value;\n                        if (file.moduleExports) {\n                            this._diag.debug('Removing instrumentation patch for nodejs module file on instrumentation disabled', {\n                                module: module_3.name,\n                                version: module_3.moduleVersion,\n                                fileName: file.name,\n                            });\n                            file.unpatch(file.moduleExports, module_3.moduleVersion);\n                        }\n                    }\n                }\n                catch (e_5_1) { e_5 = { error: e_5_1 }; }\n                finally {\n                    try {\n                        if (_f && !_f.done && (_b = _e.return)) _b.call(_e);\n                    }\n                    finally { if (e_5) throw e_5.error; }\n                }\n            }\n        }\n        catch (e_4_1) { e_4 = { error: e_4_1 }; }\n        finally {\n            try {\n                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);\n            }\n            finally { if (e_4) throw e_4.error; }\n        }\n    };\n    InstrumentationBase.prototype.isEnabled = function () {\n        return this._enabled;\n    };\n    return InstrumentationBase;\n}(_instrumentation__WEBPACK_IMPORTED_MODULE_10__.InstrumentationAbstract));\n\nfunction isSupported(supportedVersions, version, includePrerelease) {\n    if (typeof version === 'undefined') {\n        // If we don't have the version, accept the wildcard case only\n        return supportedVersions.includes('*');\n    }\n    return supportedVersions.some(function (supportedVersion) {\n        return (0,semver__WEBPACK_IMPORTED_MODULE_2__.satisfies)(version, supportedVersion, { includePrerelease: includePrerelease });\n    });\n}\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/instrumentation.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/utils.js":
/*!************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/utils.js ***!
  \************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isWrapped: () => (/* binding */ isWrapped),\n/* harmony export */   safeExecuteInTheMiddle: () => (/* binding */ safeExecuteInTheMiddle),\n/* harmony export */   safeExecuteInTheMiddleAsync: () => (/* binding */ safeExecuteInTheMiddleAsync)\n/* harmony export */ });\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __awaiter = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __generator = (undefined && undefined.__generator) || function (thisArg, body) {\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n    function verb(n) { return function (v) { return step([n, v]); }; }\n    function step(op) {\n        if (f) throw new TypeError(\"Generator is already executing.\");\n        while (_) try {\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n            if (y = 0, t) op = [op[0] & 2, t.value];\n            switch (op[0]) {\n                case 0: case 1: t = op; break;\n                case 4: _.label++; return { value: op[1], done: false };\n                case 5: _.label++; y = op[1]; op = [0]; continue;\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\n                default:\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                    if (t[2]) _.ops.pop();\n                    _.trys.pop(); continue;\n            }\n            op = body.call(thisArg, _);\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n    }\n};\n/**\n * function to execute patched function and being able to catch errors\n * @param execute - function to be executed\n * @param onFinish - callback to run when execute finishes\n */\nfunction safeExecuteInTheMiddle(execute, onFinish, preventThrowingError) {\n    var error;\n    var result;\n    try {\n        result = execute();\n    }\n    catch (e) {\n        error = e;\n    }\n    finally {\n        onFinish(error, result);\n        if (error && !preventThrowingError) {\n            // eslint-disable-next-line no-unsafe-finally\n            throw error;\n        }\n        // eslint-disable-next-line no-unsafe-finally\n        return result;\n    }\n}\n/**\n * Async function to execute patched function and being able to catch errors\n * @param execute - function to be executed\n * @param onFinish - callback to run when execute finishes\n */\nfunction safeExecuteInTheMiddleAsync(execute, onFinish, preventThrowingError) {\n    return __awaiter(this, void 0, void 0, function () {\n        var error, result, e_1;\n        return __generator(this, function (_a) {\n            switch (_a.label) {\n                case 0:\n                    _a.trys.push([0, 2, 3, 4]);\n                    return [4 /*yield*/, execute()];\n                case 1:\n                    result = _a.sent();\n                    return [3 /*break*/, 4];\n                case 2:\n                    e_1 = _a.sent();\n                    error = e_1;\n                    return [3 /*break*/, 4];\n                case 3:\n                    onFinish(error, result);\n                    if (error && !preventThrowingError) {\n                        // eslint-disable-next-line no-unsafe-finally\n                        throw error;\n                    }\n                    // eslint-disable-next-line no-unsafe-finally\n                    return [2 /*return*/, result];\n                case 4: return [2 /*return*/];\n            }\n        });\n    });\n}\n/**\n * Checks if certain function has been already wrapped\n * @param func\n */\nfunction isWrapped(func) {\n    return (typeof func === 'function' &&\n        typeof func.__original === 'function' &&\n        typeof func.__unwrap === 'function' &&\n        func.__wrapped === true);\n}\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/utils.js\n");

/***/ })

};
;