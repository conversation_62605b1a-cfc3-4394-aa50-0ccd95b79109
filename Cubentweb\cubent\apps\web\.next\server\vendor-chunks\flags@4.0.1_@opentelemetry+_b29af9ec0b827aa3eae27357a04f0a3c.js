"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/flags@4.0.1_@opentelemetry+_b29af9ec0b827aa3eae27357a04f0a3c";
exports.ids = ["vendor-chunks/flags@4.0.1_@opentelemetry+_b29af9ec0b827aa3eae27357a04f0a3c"];
exports.modules = {

/***/ "(rsc)/../../node_modules/.pnpm/flags@4.0.1_@opentelemetry+_b29af9ec0b827aa3eae27357a04f0a3c/node_modules/flags/dist/chunk-ISCGLTLL.js":
/*!***************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/flags@4.0.1_@opentelemetry+_b29af9ec0b827aa3eae27357a04f0a3c/node_modules/flags/dist/chunk-ISCGLTLL.js ***!
  \***************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HeadersAdapter: () => (/* binding */ HeadersAdapter),\n/* harmony export */   RequestCookiesAdapter: () => (/* binding */ RequestCookiesAdapter),\n/* harmony export */   createAccessProof: () => (/* binding */ createAccessProof),\n/* harmony export */   decryptFlagDefinitions: () => (/* binding */ decryptFlagDefinitions),\n/* harmony export */   decryptFlagValues: () => (/* binding */ decryptFlagValues),\n/* harmony export */   decryptOverrides: () => (/* binding */ decryptOverrides),\n/* harmony export */   encryptFlagDefinitions: () => (/* binding */ encryptFlagDefinitions),\n/* harmony export */   encryptFlagValues: () => (/* binding */ encryptFlagValues),\n/* harmony export */   encryptOverrides: () => (/* binding */ encryptOverrides),\n/* harmony export */   internalReportValue: () => (/* binding */ internalReportValue),\n/* harmony export */   mergeProviderData: () => (/* binding */ mergeProviderData),\n/* harmony export */   reportValue: () => (/* binding */ reportValue),\n/* harmony export */   setSpanAttribute: () => (/* binding */ setSpanAttribute),\n/* harmony export */   setTracerProvider: () => (/* binding */ setTracerProvider),\n/* harmony export */   trace: () => (/* binding */ trace),\n/* harmony export */   verifyAccess: () => (/* binding */ verifyAccess),\n/* harmony export */   verifyAccessProof: () => (/* binding */ verifyAccessProof),\n/* harmony export */   version: () => (/* binding */ version)\n/* harmony export */ });\n/* harmony import */ var async_hooks__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! async_hooks */ \"async_hooks\");\n/* harmony import */ var jose__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jose */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/util/base64url.js\");\n/* harmony import */ var jose__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! jose */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/jwt/encrypt.js\");\n/* harmony import */ var jose__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! jose */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/jwt/decrypt.js\");\n// package.json\nvar name = \"flags\";\nvar version = \"4.0.1\";\n\n// src/lib/tracing.ts\n\nvar vercelFlagsTraceSymbol = Symbol.for(\"flags:global-trace\");\nfunction setTracerProvider(tracer) {\n  Reflect.set(globalThis, vercelFlagsTraceSymbol, tracer);\n}\nfunction getTracer() {\n  const maybeTraceApi = Reflect.get(globalThis, vercelFlagsTraceSymbol);\n  return maybeTraceApi?.getTracer(name, version);\n}\nfunction isPromise(p) {\n  return p !== null && typeof p === \"object\" && \"then\" in p && typeof p.then === \"function\";\n}\nvar spanContext = new async_hooks__WEBPACK_IMPORTED_MODULE_0__.AsyncLocalStorage();\nfunction setSpanAttribute(name2, value) {\n  spanContext.getStore()?.set(name2, value);\n}\nfunction trace(fn, options = {\n  name: fn.name\n}) {\n  const traced = function(...args) {\n    const tracer = getTracer();\n    if (!tracer)\n      return fn.apply(this, args);\n    const shouldTrace = process.env.VERCEL_FLAGS_TRACE_VERBOSE === \"true\" || options.isVerboseTrace === false;\n    if (!shouldTrace)\n      return fn.apply(this, args);\n    return spanContext.run(\n      /* @__PURE__ */ new Map(),\n      () => tracer.startActiveSpan(options.name, (span) => {\n        if (options.attributes)\n          span.setAttributes(options.attributes);\n        try {\n          const result = fn.apply(this, args);\n          if (isPromise(result)) {\n            result.then((value) => {\n              if (options.attributesSuccess) {\n                span.setAttributes(\n                  options.attributesSuccess(\n                    value\n                  )\n                );\n              }\n              spanContext.getStore()?.forEach((value2, key) => {\n                span.setAttribute(key, value2);\n              });\n              span.setStatus({ code: 1 });\n              span.end();\n            }).catch((error) => {\n              if (options.attributesError) {\n                span.setAttributes(options.attributesError(error));\n              }\n              span.setStatus({\n                code: 2,\n                // 2 = Error\n                message: error instanceof Error ? error.message : void 0\n              });\n              spanContext.getStore()?.forEach((value, key) => {\n                span.setAttribute(key, value);\n              });\n              span.end();\n            });\n          } else {\n            if (options.attributesSuccess) {\n              span.setAttributes(options.attributesSuccess(result));\n            }\n            spanContext.getStore()?.forEach((value, key) => {\n              span.setAttribute(key, value);\n            });\n            span.setStatus({ code: 1 });\n            span.end();\n          }\n          return result;\n        } catch (error) {\n          if (options.attributesError) {\n            span.setAttributes(options.attributesError(error));\n          }\n          span.setStatus({\n            code: 2,\n            // 2 = Error\n            message: error instanceof Error ? error.message : void 0\n          });\n          spanContext.getStore()?.forEach((value, key) => {\n            span.setAttribute(key, value);\n          });\n          span.end();\n          throw error;\n        }\n      })\n    );\n  };\n  return traced;\n}\n\n// src/lib/crypto.ts\n\nvar hasPurpose = (pur, expectedPurpose) => {\n  return Array.isArray(pur) ? pur.includes(expectedPurpose) : pur === expectedPurpose;\n};\nasync function encryptJwe(payload, secret, expirationTime) {\n  const encodedSecret = jose__WEBPACK_IMPORTED_MODULE_1__.decode(secret);\n  if (encodedSecret.length !== 32) {\n    throw new Error(\n      \"flags: Invalid secret, it must be a 256-bit key (32 bytes)\"\n    );\n  }\n  return new jose__WEBPACK_IMPORTED_MODULE_2__.EncryptJWT(payload).setExpirationTime(expirationTime).setProtectedHeader({ alg: \"dir\", enc: \"A256GCM\" }).encrypt(encodedSecret);\n}\nasync function decryptJwe(text, verify, secret) {\n  if (typeof text !== \"string\")\n    return;\n  const encodedSecret = jose__WEBPACK_IMPORTED_MODULE_1__.decode(secret);\n  if (encodedSecret.length !== 32) {\n    throw new Error(\n      \"flags: Invalid secret, it must be a 256-bit key (32 bytes)\"\n    );\n  }\n  try {\n    const { payload } = await (0,jose__WEBPACK_IMPORTED_MODULE_3__.jwtDecrypt)(text, encodedSecret);\n    const decoded = payload;\n    return verify(decoded) ? decoded : void 0;\n  } catch {\n    return void 0;\n  }\n}\nasync function encryptOverrides(overrides, secret = process?.env?.FLAGS_SECRET, expirationTime = \"1y\") {\n  if (!secret)\n    throw new Error(\"flags: Missing FLAGS_SECRET\");\n  return encryptJwe({ o: overrides, pur: \"overrides\" }, secret, expirationTime);\n}\nasync function decryptOverrides(encryptedData, secret = process?.env?.FLAGS_SECRET) {\n  if (!secret)\n    throw new Error(\"flags: Missing FLAGS_SECRET\");\n  const contents = await decryptJwe(\n    encryptedData,\n    (data) => hasPurpose(data.pur, \"overrides\") && Object.hasOwn(data, \"o\"),\n    secret\n  );\n  return contents?.o;\n}\nasync function encryptFlagValues(flagValues, secret = process?.env?.FLAGS_SECRET, expirationTime = \"1y\") {\n  if (!secret)\n    throw new Error(\"flags: Missing FLAGS_SECRET\");\n  return encryptJwe({ v: flagValues, pur: \"values\" }, secret, expirationTime);\n}\nasync function decryptFlagValues(encryptedData, secret = process?.env?.FLAGS_SECRET) {\n  if (!secret)\n    throw new Error(\"flags: Missing FLAGS_SECRET\");\n  const contents = await decryptJwe(\n    encryptedData,\n    (data) => hasPurpose(data.pur, \"values\") && Object.hasOwn(data, \"v\"),\n    secret\n  );\n  return contents?.v;\n}\nasync function encryptFlagDefinitions(flagDefinitions, secret = process?.env?.FLAGS_SECRET, expirationTime = \"1y\") {\n  if (!secret)\n    throw new Error(\"flags: Missing FLAGS_SECRET\");\n  return encryptJwe(\n    { d: flagDefinitions, pur: \"definitions\" },\n    secret,\n    expirationTime\n  );\n}\nasync function decryptFlagDefinitions(encryptedData, secret = process?.env?.FLAGS_SECRET) {\n  if (!secret)\n    throw new Error(\"flags: Missing FLAGS_SECRET\");\n  const contents = await decryptJwe(\n    encryptedData,\n    (data) => data.pur === \"definitions\" && Object.hasOwn(data, \"d\"),\n    secret\n  );\n  return contents?.d;\n}\nasync function createAccessProof(secret = process?.env?.FLAGS_SECRET, expirationTime = \"1y\") {\n  if (!secret)\n    throw new Error(\"flags: Missing FLAGS_SECRET\");\n  return encryptJwe({ pur: \"proof\" }, secret, expirationTime);\n}\nasync function verifyAccessProof(encryptedData, secret = process?.env?.FLAGS_SECRET) {\n  if (!secret)\n    throw new Error(\"flags: Missing FLAGS_SECRET\");\n  const contents = await decryptJwe(encryptedData, (data) => hasPurpose(data.pur, \"proof\"), secret);\n  return Boolean(contents);\n}\n\n// src/lib/verify-access.ts\nvar verifyAccess = trace(\n  async function verifyAccess2(authHeader, secret = process?.env?.FLAGS_SECRET) {\n    if (!authHeader)\n      return false;\n    if (!secret)\n      throw new Error(\n        \"flags: verifyAccess was called without a secret. Please set FLAGS_SECRET environment variable.\"\n      );\n    const valid = await verifyAccessProof(\n      authHeader.replace(/^Bearer /i, \"\"),\n      secret\n    );\n    return valid;\n  },\n  {\n    isVerboseTrace: false,\n    name: \"verifyAccess\"\n  }\n);\n\n// src/lib/report-value.ts\nfunction reportValue(key, value) {\n  const symbol = Symbol.for(\"@vercel/request-context\");\n  const ctx = Reflect.get(globalThis, symbol)?.get();\n  ctx?.flags?.reportValue(key, value, {\n    sdkVersion: version\n  });\n}\nfunction internalReportValue(key, value, data) {\n  const symbol = Symbol.for(\"@vercel/request-context\");\n  const ctx = Reflect.get(globalThis, symbol)?.get();\n  ctx?.flags?.reportValue(key, value, {\n    sdkVersion: version,\n    ...data\n  });\n}\n\n// src/spec-extension/adapters/reflect.ts\nvar ReflectAdapter = class {\n  static get(target, prop, receiver) {\n    const value = Reflect.get(target, prop, receiver);\n    if (typeof value === \"function\") {\n      return value.bind(target);\n    }\n    return value;\n  }\n  static set(target, prop, value, receiver) {\n    return Reflect.set(target, prop, value, receiver);\n  }\n  static has(target, prop) {\n    return Reflect.has(target, prop);\n  }\n  static deleteProperty(target, prop) {\n    return Reflect.deleteProperty(target, prop);\n  }\n};\n\n// src/spec-extension/adapters/headers.ts\nvar ReadonlyHeadersError = class _ReadonlyHeadersError extends Error {\n  constructor() {\n    super(\n      \"Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers\"\n    );\n  }\n  static callable() {\n    throw new _ReadonlyHeadersError();\n  }\n};\nvar HeadersAdapter = class _HeadersAdapter extends Headers {\n  constructor(headers) {\n    super();\n    this.headers = new Proxy(headers, {\n      get(target, prop, receiver) {\n        if (typeof prop === \"symbol\") {\n          return ReflectAdapter.get(target, prop, receiver);\n        }\n        const lowercased = prop.toLowerCase();\n        const original = Object.keys(headers).find(\n          (o) => o.toLowerCase() === lowercased\n        );\n        if (typeof original === \"undefined\")\n          return;\n        return ReflectAdapter.get(target, original, receiver);\n      },\n      set(target, prop, value, receiver) {\n        if (typeof prop === \"symbol\") {\n          return ReflectAdapter.set(target, prop, value, receiver);\n        }\n        const lowercased = prop.toLowerCase();\n        const original = Object.keys(headers).find(\n          (o) => o.toLowerCase() === lowercased\n        );\n        return ReflectAdapter.set(target, original ?? prop, value, receiver);\n      },\n      has(target, prop) {\n        if (typeof prop === \"symbol\")\n          return ReflectAdapter.has(target, prop);\n        const lowercased = prop.toLowerCase();\n        const original = Object.keys(headers).find(\n          (o) => o.toLowerCase() === lowercased\n        );\n        if (typeof original === \"undefined\")\n          return false;\n        return ReflectAdapter.has(target, original);\n      },\n      deleteProperty(target, prop) {\n        if (typeof prop === \"symbol\")\n          return ReflectAdapter.deleteProperty(target, prop);\n        const lowercased = prop.toLowerCase();\n        const original = Object.keys(headers).find(\n          (o) => o.toLowerCase() === lowercased\n        );\n        if (typeof original === \"undefined\")\n          return true;\n        return ReflectAdapter.deleteProperty(target, original);\n      }\n    });\n  }\n  /**\n   * Seals a Headers instance to prevent modification by throwing an error when\n   * any mutating method is called.\n   */\n  static seal(headers) {\n    return new Proxy(headers, {\n      get(target, prop, receiver) {\n        switch (prop) {\n          case \"append\":\n          case \"delete\":\n          case \"set\":\n            return ReadonlyHeadersError.callable;\n          default:\n            return ReflectAdapter.get(target, prop, receiver);\n        }\n      }\n    });\n  }\n  /**\n   * Merges a header value into a string. This stores multiple values as an\n   * array, so we need to merge them into a string.\n   *\n   * @param value a header value\n   * @returns a merged header value (a string)\n   */\n  merge(value) {\n    if (Array.isArray(value))\n      return value.join(\", \");\n    return value;\n  }\n  /**\n   * Creates a Headers instance from a plain object or a Headers instance.\n   *\n   * @param headers a plain object or a Headers instance\n   * @returns a headers instance\n   */\n  static from(headers) {\n    if (headers instanceof Headers)\n      return headers;\n    return new _HeadersAdapter(headers);\n  }\n  append(name2, value) {\n    const existing = this.headers[name2];\n    if (typeof existing === \"string\") {\n      this.headers[name2] = [existing, value];\n    } else if (Array.isArray(existing)) {\n      existing.push(value);\n    } else {\n      this.headers[name2] = value;\n    }\n  }\n  delete(name2) {\n    delete this.headers[name2];\n  }\n  get(name2) {\n    const value = this.headers[name2];\n    if (typeof value !== \"undefined\")\n      return this.merge(value);\n    return null;\n  }\n  has(name2) {\n    return typeof this.headers[name2] !== \"undefined\";\n  }\n  set(name2, value) {\n    this.headers[name2] = value;\n  }\n  forEach(callbackfn, thisArg) {\n    for (const [name2, value] of this.entries()) {\n      callbackfn.call(thisArg, value, name2, this);\n    }\n  }\n  *entries() {\n    for (const key of Object.keys(this.headers)) {\n      const name2 = key.toLowerCase();\n      const value = this.get(name2);\n      yield [name2, value];\n    }\n  }\n  *keys() {\n    for (const key of Object.keys(this.headers)) {\n      const name2 = key.toLowerCase();\n      yield name2;\n    }\n  }\n  *values() {\n    for (const key of Object.keys(this.headers)) {\n      const value = this.get(key);\n      yield value;\n    }\n  }\n  [Symbol.iterator]() {\n    return this.entries();\n  }\n};\n\n// src/spec-extension/adapters/request-cookies.ts\nvar ReadonlyRequestCookiesError = class _ReadonlyRequestCookiesError extends Error {\n  constructor() {\n    super(\n      \"Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options\"\n    );\n  }\n  static callable() {\n    throw new _ReadonlyRequestCookiesError();\n  }\n};\nvar RequestCookiesAdapter = class {\n  static seal(cookies) {\n    return new Proxy(cookies, {\n      get(target, prop, receiver) {\n        switch (prop) {\n          case \"clear\":\n          case \"delete\":\n          case \"set\":\n            return ReadonlyRequestCookiesError.callable;\n          default:\n            return ReflectAdapter.get(target, prop, receiver);\n        }\n      }\n    });\n  }\n};\nvar SYMBOL_MODIFY_COOKIE_VALUES = Symbol.for(\"next.mutated.cookies\");\n\n// src/lib/merge-provider-data.ts\nasync function mergeProviderData(itemsPromises) {\n  const items = await Promise.all(\n    itemsPromises.map((p) => Promise.resolve(p).catch(() => null))\n  );\n  return items.filter((item) => Boolean(item)).reduce(\n    (acc, item) => {\n      Object.entries(item.definitions).forEach(([key, definition]) => {\n        if (!acc.definitions[key])\n          acc.definitions[key] = {};\n        Object.assign(acc.definitions[key], definition);\n      });\n      if (Array.isArray(item.hints))\n        acc.hints.push(...item.hints);\n      return acc;\n    },\n    { definitions: {}, hints: [] }\n  );\n}\n\n\n//# sourceMappingURL=chunk-ISCGLTLL.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/flags@4.0.1_@opentelemetry+_b29af9ec0b827aa3eae27357a04f0a3c/node_modules/flags/dist/chunk-ISCGLTLL.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/flags@4.0.1_@opentelemetry+_b29af9ec0b827aa3eae27357a04f0a3c/node_modules/flags/dist/chunk-O6VYPARG.js":
/*!***************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/flags@4.0.1_@opentelemetry+_b29af9ec0b827aa3eae27357a04f0a3c/node_modules/flags/dist/chunk-O6VYPARG.js ***!
  \***************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deserialize: () => (/* binding */ deserialize),\n/* harmony export */   memoizeOne: () => (/* binding */ memoizeOne),\n/* harmony export */   normalizeOptions: () => (/* binding */ normalizeOptions),\n/* harmony export */   serialize: () => (/* binding */ serialize)\n/* harmony export */ });\n/* harmony import */ var jose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jose */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/jws/compact/verify.js\");\n/* harmony import */ var jose__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jose */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/util/base64url.js\");\n/* harmony import */ var jose__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! jose */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/jws/compact/sign.js\");\n// src/lib/normalize-options.ts\nfunction normalizeOptions(flagOptions) {\n  if (!Array.isArray(flagOptions))\n    return flagOptions;\n  return flagOptions.map((option) => {\n    if (typeof option === \"boolean\")\n      return { value: option };\n    if (typeof option === \"number\")\n      return { value: option };\n    if (typeof option === \"string\")\n      return { value: option };\n    if (option === null)\n      return { value: option };\n    return option;\n  });\n}\n\n// src/lib/async-memoize-one.ts\nfunction memoizeOne(fn, isEqual, { cachePromiseRejection = false } = {}) {\n  let calledOnce = false;\n  let oldArgs;\n  let lastResult;\n  function memoized(...newArgs) {\n    if (calledOnce && isEqual(newArgs, oldArgs))\n      return lastResult;\n    lastResult = fn.apply(this, newArgs);\n    if (!cachePromiseRejection && lastResult.catch) {\n      lastResult.catch(() => calledOnce = false);\n    }\n    calledOnce = true;\n    oldArgs = newArgs;\n    return lastResult;\n  }\n  return memoized;\n}\n\n// src/lib/serialization.ts\n\nvar memoizedVerify = memoizeOne(\n  (code, secret) => (0,jose__WEBPACK_IMPORTED_MODULE_0__.compactVerify)(code, jose__WEBPACK_IMPORTED_MODULE_1__.decode(secret), {\n    algorithms: [\"HS256\"]\n  }),\n  (a, b) => a[0] === b[0] && a[1] === b[1],\n  // only first two args matter\n  { cachePromiseRejection: true }\n);\nvar memoizedSign = memoizeOne(\n  (uint8Array, secret) => new jose__WEBPACK_IMPORTED_MODULE_2__.CompactSign(uint8Array).setProtectedHeader({ alg: \"HS256\" }).sign(jose__WEBPACK_IMPORTED_MODULE_1__.decode(secret)),\n  (a, b) => (\n    // matchedIndices array must be equal\n    a[0].length === b[0].length && a[0].every((v, i) => b[0][i] === v) && // secrets must be equal\n    a[1] === b[1]\n  ),\n  { cachePromiseRejection: true }\n);\nfunction splitUint8Array(array, index) {\n  const firstHalf = array.slice(0, index);\n  const secondHalf = array.slice(index);\n  return [firstHalf, secondHalf];\n}\nasync function deserialize(code, flags, secret) {\n  const { payload } = await memoizedVerify(code, secret);\n  const [matchedIndicesArray, valuesUint8Array] = payload.length === flags.length ? [payload] : splitUint8Array(payload, flags.length);\n  const valuesArray = valuesUint8Array ? (\n    // re-add opening and closing brackets since we remove them when serializing\n    JSON.parse(`[${new TextDecoder().decode(valuesUint8Array)}]`)\n  ) : null;\n  let spilled = 0;\n  return matchedIndicesArray.reduce(\n    (acc, valueIndex, index) => {\n      const flag = flags[index];\n      if (!flag) {\n        throw new Error(`flags: No flag at index ${index}`);\n      }\n      switch (valueIndex) {\n        case 253 /* BOOLEAN_FALSE */:\n          acc[flag.key] = false;\n          break;\n        case 254 /* BOOLEAN_TRUE */:\n          acc[flag.key] = true;\n          break;\n        case 255 /* UNLISTED_VALUE */:\n          acc[flag.key] = valuesArray[spilled++];\n          break;\n        case 252 /* NULL */:\n          acc[flag.key] = null;\n          break;\n        default:\n          acc[flag.key] = flag.options?.[valueIndex]?.value;\n      }\n      return acc;\n    },\n    {}\n  );\n}\nvar matchIndex = /* @__PURE__ */ function() {\n  const stringifiedOptionsCache = /* @__PURE__ */ new Map();\n  return function matchIndex2(options, value) {\n    const t = typeof value;\n    if (value === null || t === \"boolean\" || t === \"string\" || t === \"number\") {\n      return options.findIndex((v) => v.value === value);\n    }\n    const stringifiedValue = JSON.stringify(value);\n    let stringifiedOptions = stringifiedOptionsCache.get(options);\n    if (!stringifiedOptions) {\n      stringifiedOptions = options.map((o) => JSON.stringify(o.value));\n      stringifiedOptionsCache.set(options, stringifiedOptions);\n    }\n    return stringifiedOptions.findIndex(\n      (stringifiedOption) => stringifiedOption === stringifiedValue\n    );\n  };\n}();\nfunction joinUint8Arrays(array1, array2) {\n  const combined = new Uint8Array(array1.length + array2.length);\n  combined.set(array1);\n  combined.set(array2, array1.length);\n  return combined;\n}\nasync function serialize(flagSet, flags, secret) {\n  const unlistedValues = [];\n  const matchedIndices = new Uint8Array(\n    flags.map((flag) => {\n      const options = Array.isArray(flag.options) ? flag.options : [];\n      const value = flagSet[flag.key];\n      if (!Object.prototype.hasOwnProperty.call(flagSet, flag.key) || value === void 0) {\n        throw new Error(`flags: Missing value for flag \"${flag.key}\"`);\n      }\n      switch (value) {\n        case null:\n          return 252 /* NULL */;\n        case false:\n          return 253 /* BOOLEAN_FALSE */;\n        case true:\n          return 254 /* BOOLEAN_TRUE */;\n      }\n      const matchedIndex = matchIndex(options, value);\n      if (matchedIndex > -1)\n        return matchedIndex;\n      unlistedValues.push(value);\n      return 255 /* UNLISTED_VALUE */;\n    })\n  );\n  let joined;\n  if (unlistedValues.length > 0) {\n    const jsonArray = new TextEncoder().encode(\n      // slicing removes opening and closing array brackets as they'll always be\n      //  there and we can re-add them when deserializing\n      JSON.stringify(unlistedValues).slice(1, -1)\n    );\n    joined = joinUint8Arrays(matchedIndices, jsonArray);\n  } else {\n    joined = matchedIndices;\n  }\n  return memoizedSign(joined, secret);\n}\n\n\n//# sourceMappingURL=chunk-O6VYPARG.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/flags@4.0.1_@opentelemetry+_b29af9ec0b827aa3eae27357a04f0a3c/node_modules/flags/dist/chunk-O6VYPARG.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/flags@4.0.1_@opentelemetry+_b29af9ec0b827aa3eae27357a04f0a3c/node_modules/flags/dist/next.js":
/*!*****************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/flags@4.0.1_@opentelemetry+_b29af9ec0b827aa3eae27357a04f0a3c/node_modules/flags/dist/next.js ***!
  \*****************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearDedupeCacheForCurrentRequest: () => (/* binding */ clearDedupeCacheForCurrentRequest),\n/* harmony export */   combine: () => (/* binding */ combine),\n/* harmony export */   createFlagsDiscoveryEndpoint: () => (/* binding */ createFlagsDiscoveryEndpoint),\n/* harmony export */   dedupe: () => (/* binding */ dedupe),\n/* harmony export */   deserialize: () => (/* binding */ deserialize2),\n/* harmony export */   evaluate: () => (/* binding */ evaluate),\n/* harmony export */   flag: () => (/* binding */ flag),\n/* harmony export */   generatePermutations: () => (/* binding */ generatePermutations),\n/* harmony export */   getPrecomputed: () => (/* binding */ getPrecomputed),\n/* harmony export */   getProviderData: () => (/* binding */ getProviderData),\n/* harmony export */   precompute: () => (/* binding */ precompute),\n/* harmony export */   serialize: () => (/* binding */ serialize2)\n/* harmony export */ });\n/* harmony import */ var _chunk_O6VYPARG_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-O6VYPARG.js */ \"(rsc)/../../node_modules/.pnpm/flags@4.0.1_@opentelemetry+_b29af9ec0b827aa3eae27357a04f0a3c/node_modules/flags/dist/chunk-O6VYPARG.js\");\n/* harmony import */ var _chunk_ISCGLTLL_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./chunk-ISCGLTLL.js */ \"(rsc)/../../node_modules/.pnpm/flags@4.0.1_@opentelemetry+_b29af9ec0b827aa3eae27357a04f0a3c/node_modules/flags/dist/chunk-ISCGLTLL.js\");\n/* harmony import */ var _edge_runtime_cookies__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @edge-runtime/cookies */ \"(rsc)/../../node_modules/.pnpm/@edge-runtime+cookies@5.0.2/node_modules/@edge-runtime/cookies/dist/index.mjs\");\n\n\n\n\n// src/next/index.ts\n\n\n// src/next/overrides.ts\nvar memoizedDecrypt = (0,_chunk_O6VYPARG_js__WEBPACK_IMPORTED_MODULE_1__.memoizeOne)(\n  (text) => (0,_chunk_ISCGLTLL_js__WEBPACK_IMPORTED_MODULE_2__.decryptOverrides)(text),\n  (a, b) => a[0] === b[0],\n  // only the first argument gets compared\n  { cachePromiseRejection: true }\n);\nasync function getOverrides(cookie) {\n  if (typeof cookie === \"string\" && cookie !== \"\") {\n    const cookieOverrides = await memoizedDecrypt(cookie);\n    return cookieOverrides ?? null;\n  }\n  return null;\n}\n\n// src/next/precompute.ts\nasync function evaluate(flags) {\n  return Promise.all(flags.map((flag2) => flag2()));\n}\nasync function precompute(flags) {\n  const values = await evaluate(flags);\n  return serialize2(flags, values);\n}\nfunction combine(flags, values) {\n  return Object.fromEntries(flags.map((flag2, i) => [flag2.key, values[i]]));\n}\nasync function serialize2(flags, values, secret = process.env.FLAGS_SECRET) {\n  if (!secret) {\n    throw new Error(\"flags: Can not serialize due to missing secret\");\n  }\n  return (0,_chunk_O6VYPARG_js__WEBPACK_IMPORTED_MODULE_1__.serialize)(combine(flags, values), flags, secret);\n}\nasync function deserialize2(flags, code, secret = process.env.FLAGS_SECRET) {\n  if (!secret) {\n    throw new Error(\"flags: Can not serialize due to missing secret\");\n  }\n  return (0,_chunk_O6VYPARG_js__WEBPACK_IMPORTED_MODULE_1__.deserialize)(code, flags, secret);\n}\nasync function getPrecomputed(flagOrFlags, precomputeFlags, code, secret = process.env.FLAGS_SECRET) {\n  if (!secret) {\n    throw new Error(\n      \"flags: getPrecomputed was called without a secret. Please set FLAGS_SECRET environment variable.\"\n    );\n  }\n  const flagSet = await deserialize2(precomputeFlags, code, secret);\n  if (Array.isArray(flagOrFlags)) {\n    return flagOrFlags.map((flag2) => flagSet[flag2.key]);\n  } else {\n    return flagSet[flagOrFlags.key];\n  }\n}\nfunction* cartesianIterator(items) {\n  const remainder = items.length > 1 ? cartesianIterator(items.slice(1)) : [[]];\n  for (let r of remainder)\n    for (let h of items.at(0))\n      yield [h, ...r];\n}\nasync function generatePermutations(flags, filter = null, secret = process.env.FLAGS_SECRET) {\n  if (!secret) {\n    throw new Error(\n      \"flags: generatePermutations was called without a secret. Please set FLAGS_SECRET environment variable.\"\n    );\n  }\n  const options = flags.map((flag2) => {\n    if (!flag2.options)\n      return [false, true];\n    return flag2.options.map((option) => option.value);\n  });\n  const list = [];\n  for (const permutation of cartesianIterator(options)) {\n    const permObject = permutation.reduce(\n      (acc, value, index) => {\n        acc[flags[index].key] = value;\n        return acc;\n      },\n      {}\n    );\n    if (!filter || filter(permObject))\n      list.push(permObject);\n  }\n  return Promise.all(list.map((values) => (0,_chunk_O6VYPARG_js__WEBPACK_IMPORTED_MODULE_1__.serialize)(values, flags, secret)));\n}\n\n// src/next/is-internal-next-error.ts\nvar REACT_POSTPONE_TYPE = Symbol.for(\"react.postpone\");\nfunction isPostpone(error) {\n  return typeof error === \"object\" && error !== null && \"$$typeof\" in error && error.$$typeof === REACT_POSTPONE_TYPE;\n}\nfunction isInternalNextError(error) {\n  if (isPostpone(error))\n    return true;\n  if (typeof error !== \"object\" || error === null || !(\"digest\" in error) || typeof error.digest !== \"string\") {\n    return false;\n  }\n  const errorCode = error.digest.split(\";\")[0];\n  return errorCode === \"NEXT_REDIRECT\" || errorCode === \"DYNAMIC_SERVER_USAGE\" || errorCode === \"BAILOUT_TO_CLIENT_SIDE_RENDERING\" || errorCode === \"NEXT_NOT_FOUND\";\n}\n\n// src/next/dedupe.ts\nfunction createCacheNode() {\n  return {\n    s: 0 /* UNTERMINATED */,\n    v: void 0,\n    o: null,\n    p: null\n  };\n}\nvar cacheRegistry = /* @__PURE__ */ new WeakMap();\nfunction dedupe(fn) {\n  const requestStore = /* @__PURE__ */ new WeakMap();\n  const dedupedFn = async function(...args) {\n    const { headers } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! next/headers */ \"(rsc)/../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/api/headers.js\"));\n    const h = await headers();\n    let cacheNode = requestStore.get(h);\n    if (!cacheNode) {\n      cacheNode = createCacheNode();\n      requestStore.set(h, cacheNode);\n    }\n    for (let i = 0; i < args.length; i++) {\n      const arg = args[i];\n      if (typeof arg === \"function\" || typeof arg === \"object\" && arg !== null) {\n        let objectCache = cacheNode.o;\n        if (objectCache === null) {\n          cacheNode.o = objectCache = /* @__PURE__ */ new WeakMap();\n        }\n        const objectNode = objectCache.get(arg);\n        if (objectNode === void 0) {\n          cacheNode = createCacheNode();\n          objectCache.set(arg, cacheNode);\n        } else {\n          cacheNode = objectNode;\n        }\n      } else {\n        let primitiveCache = cacheNode.p;\n        if (primitiveCache === null) {\n          cacheNode.p = primitiveCache = /* @__PURE__ */ new Map();\n        }\n        const primitiveNode = primitiveCache.get(arg);\n        if (primitiveNode === void 0) {\n          cacheNode = createCacheNode();\n          primitiveCache.set(arg, cacheNode);\n        } else {\n          cacheNode = primitiveNode;\n        }\n      }\n    }\n    if (cacheNode.s === 1 /* TERMINATED */) {\n      return cacheNode.v;\n    }\n    if (cacheNode.s === 2 /* ERRORED */) {\n      throw cacheNode.v;\n    }\n    try {\n      const result = fn.apply(this, args);\n      cacheNode.s = 1 /* TERMINATED */;\n      cacheNode.v = result;\n      return result;\n    } catch (error) {\n      cacheNode.s = 2 /* ERRORED */;\n      cacheNode.v = error;\n      throw error;\n    }\n  };\n  cacheRegistry.set(dedupedFn, requestStore);\n  return dedupedFn;\n}\nasync function clearDedupeCacheForCurrentRequest(dedupedFn) {\n  if (typeof dedupedFn !== \"function\") {\n    throw new Error(\"dedupe: not a function\");\n  }\n  const requestStore = cacheRegistry.get(dedupedFn);\n  if (!requestStore) {\n    throw new Error(\"dedupe: cache not found\");\n  }\n  const { headers } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! next/headers */ \"(rsc)/../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/api/headers.js\"));\n  const h = await headers();\n  return requestStore.delete(h);\n}\n\n// src/next/create-flags-discovery-endpoint.ts\nfunction createFlagsDiscoveryEndpoint(getApiData, options) {\n  return async (request) => {\n    const access = await (0,_chunk_ISCGLTLL_js__WEBPACK_IMPORTED_MODULE_2__.verifyAccess)(\n      request.headers.get(\"Authorization\"),\n      options?.secret\n    );\n    if (!access)\n      return Response.json(null, { status: 401 });\n    const apiData = await getApiData(request);\n    return new Response(JSON.stringify(apiData), {\n      headers: {\n        \"x-flags-sdk-version\": _chunk_ISCGLTLL_js__WEBPACK_IMPORTED_MODULE_2__.version,\n        \"content-type\": \"application/json\"\n      }\n    });\n  };\n}\n\n// src/next/index.ts\nvar evaluationCache = /* @__PURE__ */ new WeakMap();\nfunction getCachedValuePromise(headers, flagKey, entitiesKey) {\n  const map = evaluationCache.get(headers)?.get(flagKey);\n  if (!map)\n    return void 0;\n  return map.get(entitiesKey);\n}\nfunction setCachedValuePromise(headers, flagKey, entitiesKey, flagValue) {\n  const byHeaders = evaluationCache.get(headers);\n  if (!byHeaders) {\n    evaluationCache.set(\n      headers,\n      /* @__PURE__ */ new Map([[flagKey, /* @__PURE__ */ new Map([[entitiesKey, flagValue]])]])\n    );\n    return;\n  }\n  const byFlagKey = byHeaders.get(flagKey);\n  if (!byFlagKey) {\n    byHeaders.set(flagKey, /* @__PURE__ */ new Map([[entitiesKey, flagValue]]));\n    return;\n  }\n  byFlagKey.set(entitiesKey, flagValue);\n}\nvar transformMap = /* @__PURE__ */ new WeakMap();\nvar headersMap = /* @__PURE__ */ new WeakMap();\nvar cookiesMap = /* @__PURE__ */ new WeakMap();\nvar identifyArgsMap = /* @__PURE__ */ new WeakMap();\nfunction transformToHeaders(incomingHeaders) {\n  const cached = transformMap.get(incomingHeaders);\n  if (cached !== void 0)\n    return cached;\n  const headers = new Headers();\n  for (const [key, value] of Object.entries(incomingHeaders)) {\n    if (Array.isArray(value)) {\n      value.forEach((item) => headers.append(key, item));\n    } else if (value !== void 0) {\n      headers.append(key, value);\n    }\n  }\n  transformMap.set(incomingHeaders, headers);\n  return headers;\n}\nfunction sealHeaders(headers) {\n  const cached = headersMap.get(headers);\n  if (cached !== void 0)\n    return cached;\n  const sealed = _chunk_ISCGLTLL_js__WEBPACK_IMPORTED_MODULE_2__.HeadersAdapter.seal(headers);\n  headersMap.set(headers, sealed);\n  return sealed;\n}\nfunction sealCookies(headers) {\n  const cached = cookiesMap.get(headers);\n  if (cached !== void 0)\n    return cached;\n  const sealed = _chunk_ISCGLTLL_js__WEBPACK_IMPORTED_MODULE_2__.RequestCookiesAdapter.seal(new _edge_runtime_cookies__WEBPACK_IMPORTED_MODULE_0__.RequestCookies(headers));\n  cookiesMap.set(headers, sealed);\n  return sealed;\n}\nfunction isIdentifyFunction(identify) {\n  return typeof identify === \"function\";\n}\nasync function getEntities(identify, dedupeCacheKey, readonlyHeaders, readonlyCookies) {\n  if (!identify)\n    return void 0;\n  if (!isIdentifyFunction(identify))\n    return identify;\n  const args = identifyArgsMap.get(dedupeCacheKey);\n  if (args)\n    return identify(...args);\n  const nextArgs = [\n    { headers: readonlyHeaders, cookies: readonlyCookies }\n  ];\n  identifyArgsMap.set(dedupeCacheKey, nextArgs);\n  return identify(...nextArgs);\n}\nfunction getDecide(definition) {\n  return function decide(params) {\n    if (typeof definition.decide === \"function\") {\n      return definition.decide(params);\n    }\n    if (typeof definition.adapter?.decide === \"function\") {\n      return definition.adapter.decide({ key: definition.key, ...params });\n    }\n    throw new Error(`flags: No decide function provided for ${definition.key}`);\n  };\n}\nfunction getIdentify(definition) {\n  return function identify(params) {\n    if (typeof definition.identify === \"function\") {\n      return definition.identify(params);\n    }\n    if (typeof definition.adapter?.identify === \"function\") {\n      return definition.adapter.identify(params);\n    }\n    return definition.identify;\n  };\n}\nfunction getRun(definition, decide) {\n  return async function run(options) {\n    let readonlyHeaders;\n    let readonlyCookies;\n    let dedupeCacheKey;\n    if (options.request) {\n      const headers = transformToHeaders(options.request.headers);\n      readonlyHeaders = sealHeaders(headers);\n      readonlyCookies = sealCookies(headers);\n      dedupeCacheKey = options.request.headers;\n    } else {\n      const { headers, cookies } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! next/headers */ \"(rsc)/../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/api/headers.js\"));\n      const [headersStore, cookiesStore] = await Promise.all([\n        headers(),\n        cookies()\n      ]);\n      readonlyHeaders = headersStore;\n      readonlyCookies = cookiesStore;\n      dedupeCacheKey = headersStore;\n    }\n    const overrides = await getOverrides(\n      readonlyCookies.get(\"vercel-flag-overrides\")?.value\n    );\n    const entities = await getEntities(\n      options.identify,\n      dedupeCacheKey,\n      readonlyHeaders,\n      readonlyCookies\n    );\n    const entitiesKey = JSON.stringify(entities) ?? \"\";\n    const cachedValue = getCachedValuePromise(\n      readonlyHeaders,\n      definition.key,\n      entitiesKey\n    );\n    if (cachedValue !== void 0) {\n      (0,_chunk_ISCGLTLL_js__WEBPACK_IMPORTED_MODULE_2__.setSpanAttribute)(\"method\", \"cached\");\n      const value = await cachedValue;\n      return value;\n    }\n    if (overrides && overrides[definition.key] !== void 0) {\n      (0,_chunk_ISCGLTLL_js__WEBPACK_IMPORTED_MODULE_2__.setSpanAttribute)(\"method\", \"override\");\n      const decision2 = overrides[definition.key];\n      setCachedValuePromise(\n        readonlyHeaders,\n        definition.key,\n        entitiesKey,\n        Promise.resolve(decision2)\n      );\n      (0,_chunk_ISCGLTLL_js__WEBPACK_IMPORTED_MODULE_2__.internalReportValue)(definition.key, decision2, {\n        reason: \"override\"\n      });\n      return decision2;\n    }\n    const decisionPromise = (async () => {\n      return decide({\n        // @ts-expect-error TypeScript will not be able to process `getPrecomputed` when added to `Decide`. It is, however, part of the `Adapter` type\n        defaultValue: definition.defaultValue,\n        headers: readonlyHeaders,\n        cookies: readonlyCookies,\n        entities\n      });\n    })().then(\n      (value) => {\n        if (value !== void 0)\n          return value;\n        if (definition.defaultValue !== void 0)\n          return definition.defaultValue;\n        throw new Error(\n          `flags: Flag \"${definition.key}\" must have a defaultValue or a decide function that returns a value`\n        );\n      },\n      (error) => {\n        if (isInternalNextError(error))\n          throw error;\n        if (definition.defaultValue !== void 0) {\n          if (true) {\n            console.info(\n              `flags: Flag \"${definition.key}\" is falling back to its defaultValue`\n            );\n          } else {}\n          return definition.defaultValue;\n        }\n        console.warn(\n          `flags: Flag \"${definition.key}\" could not be evaluated`\n        );\n        throw error;\n      }\n    );\n    setCachedValuePromise(\n      readonlyHeaders,\n      definition.key,\n      entitiesKey,\n      decisionPromise\n    );\n    const decision = await decisionPromise;\n    if (definition.config?.reportValue !== false) {\n      (0,_chunk_ISCGLTLL_js__WEBPACK_IMPORTED_MODULE_2__.reportValue)(definition.key, decision);\n    }\n    return decision;\n  };\n}\nfunction getOrigin(definition) {\n  if (definition.origin)\n    return definition.origin;\n  if (typeof definition.adapter?.origin === \"function\")\n    return definition.adapter.origin(definition.key);\n  return definition.adapter?.origin;\n}\nfunction flag(definition) {\n  const decide = getDecide(definition);\n  const identify = getIdentify(definition);\n  const run = getRun(definition, decide);\n  const origin = getOrigin(definition);\n  const flag2 = (0,_chunk_ISCGLTLL_js__WEBPACK_IMPORTED_MODULE_2__.trace)(\n    async (...args) => {\n      (0,_chunk_ISCGLTLL_js__WEBPACK_IMPORTED_MODULE_2__.setSpanAttribute)(\"method\", \"decided\");\n      if (typeof args[0] === \"string\" && Array.isArray(args[1])) {\n        const [precomputedCode, precomputedGroup, secret] = args;\n        if (precomputedCode && precomputedGroup) {\n          (0,_chunk_ISCGLTLL_js__WEBPACK_IMPORTED_MODULE_2__.setSpanAttribute)(\"method\", \"precomputed\");\n          return getPrecomputed(\n            flag2,\n            precomputedGroup,\n            precomputedCode,\n            secret\n          );\n        }\n      }\n      if (args[0] && typeof args[0] === \"object\" && \"headers\" in args[0]) {\n        const [request] = args;\n        return run({ identify, request });\n      }\n      return run({ identify, request: void 0 });\n    },\n    {\n      name: \"flag\",\n      isVerboseTrace: false,\n      attributes: { key: definition.key }\n    }\n  );\n  flag2.key = definition.key;\n  flag2.defaultValue = definition.defaultValue;\n  flag2.origin = origin;\n  flag2.options = (0,_chunk_O6VYPARG_js__WEBPACK_IMPORTED_MODULE_1__.normalizeOptions)(definition.options);\n  flag2.description = definition.description;\n  flag2.identify = identify ? (0,_chunk_ISCGLTLL_js__WEBPACK_IMPORTED_MODULE_2__.trace)(identify, {\n    isVerboseTrace: false,\n    name: \"identify\",\n    attributes: { key: definition.key }\n  }) : identify;\n  flag2.decide = (0,_chunk_ISCGLTLL_js__WEBPACK_IMPORTED_MODULE_2__.trace)(decide, {\n    isVerboseTrace: false,\n    name: \"decide\",\n    attributes: { key: definition.key }\n  });\n  flag2.run = (0,_chunk_ISCGLTLL_js__WEBPACK_IMPORTED_MODULE_2__.trace)(run, {\n    isVerboseTrace: false,\n    name: \"run\",\n    attributes: { key: definition.key }\n  });\n  return flag2;\n}\nfunction getProviderData(flags) {\n  const definitions = Object.values(flags).filter((i) => !Array.isArray(i)).reduce((acc, d) => {\n    acc[d.key] = {\n      options: d.options,\n      origin: d.origin,\n      description: d.description,\n      defaultValue: d.defaultValue,\n      declaredInCode: true\n    };\n    return acc;\n  }, {});\n  return { definitions, hints: [] };\n}\n\n//# sourceMappingURL=next.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/flags@4.0.1_@opentelemetry+_b29af9ec0b827aa3eae27357a04f0a3c/node_modules/flags/dist/next.js\n");

/***/ })

};
;