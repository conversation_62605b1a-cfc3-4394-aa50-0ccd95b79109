"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@opentelemetry+instrumentat_abf4b6a813f0c904766ef5a676a2a270";
exports.ids = ["vendor-chunks/@opentelemetry+instrumentat_abf4b6a813f0c904766ef5a676a2a270"];
exports.modules = {

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_abf4b6a813f0c904766ef5a676a2a270/node_modules/@opentelemetry/instrumentation-mongoose/build/src/index.js":
/*!*********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_abf4b6a813f0c904766ef5a676a2a270/node_modules/@opentelemetry/instrumentation-mongoose/build/src/index.js ***!
  \*********************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n__exportStar(__webpack_require__(/*! ./mongoose */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_abf4b6a813f0c904766ef5a676a2a270/node_modules/@opentelemetry/instrumentation-mongoose/build/src/mongoose.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_abf4b6a813f0c904766ef5a676a2a270/node_modules/@opentelemetry/instrumentation-mongoose/build/src/types.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_abf4b6a813f0c904766ef5a676a2a270/node_modules/@opentelemetry/instrumentation-mongoose/build/src/index.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_abf4b6a813f0c904766ef5a676a2a270/node_modules/@opentelemetry/instrumentation-mongoose/build/src/mongoose.js":
/*!************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_abf4b6a813f0c904766ef5a676a2a270/node_modules/@opentelemetry/instrumentation-mongoose/build/src/mongoose.js ***!
  \************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.MongooseInstrumentation = exports._STORED_PARENT_SPAN = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst core_1 = __webpack_require__(/*! @opentelemetry/core */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+core@1.30.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/core/build/esm/index.js\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_abf4b6a813f0c904766ef5a676a2a270/node_modules/@opentelemetry/instrumentation-mongoose/build/src/utils.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_abf4b6a813f0c904766ef5a676a2a270/node_modules/@opentelemetry/instrumentation-mongoose/build/src/version.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst contextCaptureFunctionsCommon = [\n    'deleteOne',\n    'deleteMany',\n    'find',\n    'findOne',\n    'estimatedDocumentCount',\n    'countDocuments',\n    'distinct',\n    'where',\n    '$where',\n    'findOneAndUpdate',\n    'findOneAndDelete',\n    'findOneAndReplace',\n];\nconst contextCaptureFunctions6 = [\n    'remove',\n    'count',\n    'findOneAndRemove',\n    ...contextCaptureFunctionsCommon,\n];\nconst contextCaptureFunctions7 = [\n    'count',\n    'findOneAndRemove',\n    ...contextCaptureFunctionsCommon,\n];\nconst contextCaptureFunctions8 = [...contextCaptureFunctionsCommon];\nfunction getContextCaptureFunctions(moduleVersion) {\n    /* istanbul ignore next */\n    if (!moduleVersion) {\n        return contextCaptureFunctionsCommon;\n    }\n    else if (moduleVersion.startsWith('6.') || moduleVersion.startsWith('5.')) {\n        return contextCaptureFunctions6;\n    }\n    else if (moduleVersion.startsWith('7.')) {\n        return contextCaptureFunctions7;\n    }\n    else {\n        return contextCaptureFunctions8;\n    }\n}\nfunction instrumentRemove(moduleVersion) {\n    return ((moduleVersion &&\n        (moduleVersion.startsWith('5.') || moduleVersion.startsWith('6.'))) ||\n        false);\n}\n// when mongoose functions are called, we store the original call context\n// and then set it as the parent for the spans created by Query/Aggregate exec()\n// calls. this bypass the unlinked spans issue on thenables await operations.\nexports._STORED_PARENT_SPAN = Symbol('stored-parent-span');\nclass MongooseInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, config);\n    }\n    init() {\n        const module = new instrumentation_1.InstrumentationNodeModuleDefinition('mongoose', ['>=5.9.7 <9'], this.patch.bind(this), this.unpatch.bind(this));\n        return module;\n    }\n    patch(moduleExports, moduleVersion) {\n        this._wrap(moduleExports.Model.prototype, 'save', this.patchOnModelMethods('save', moduleVersion));\n        // mongoose applies this code on module require:\n        // Model.prototype.$save = Model.prototype.save;\n        // which captures the save function before it is patched.\n        // so we need to apply the same logic after instrumenting the save function.\n        moduleExports.Model.prototype.$save = moduleExports.Model.prototype.save;\n        if (instrumentRemove(moduleVersion)) {\n            this._wrap(moduleExports.Model.prototype, 'remove', this.patchOnModelMethods('remove', moduleVersion));\n        }\n        this._wrap(moduleExports.Query.prototype, 'exec', this.patchQueryExec(moduleVersion));\n        this._wrap(moduleExports.Aggregate.prototype, 'exec', this.patchAggregateExec(moduleVersion));\n        const contextCaptureFunctions = getContextCaptureFunctions(moduleVersion);\n        contextCaptureFunctions.forEach((funcName) => {\n            this._wrap(moduleExports.Query.prototype, funcName, this.patchAndCaptureSpanContext(funcName));\n        });\n        this._wrap(moduleExports.Model, 'aggregate', this.patchModelAggregate());\n        return moduleExports;\n    }\n    unpatch(moduleExports, moduleVersion) {\n        const contextCaptureFunctions = getContextCaptureFunctions(moduleVersion);\n        this._unwrap(moduleExports.Model.prototype, 'save');\n        // revert the patch for $save which we applied by aliasing it to patched `save`\n        moduleExports.Model.prototype.$save = moduleExports.Model.prototype.save;\n        if (instrumentRemove(moduleVersion)) {\n            this._unwrap(moduleExports.Model.prototype, 'remove');\n        }\n        this._unwrap(moduleExports.Query.prototype, 'exec');\n        this._unwrap(moduleExports.Aggregate.prototype, 'exec');\n        contextCaptureFunctions.forEach((funcName) => {\n            this._unwrap(moduleExports.Query.prototype, funcName);\n        });\n        this._unwrap(moduleExports.Model, 'aggregate');\n    }\n    patchAggregateExec(moduleVersion) {\n        const self = this;\n        return (originalAggregate) => {\n            return function exec(callback) {\n                var _a;\n                if (self.getConfig().requireParentSpan &&\n                    api_1.trace.getSpan(api_1.context.active()) === undefined) {\n                    return originalAggregate.apply(this, arguments);\n                }\n                const parentSpan = this[exports._STORED_PARENT_SPAN];\n                const attributes = {};\n                const { dbStatementSerializer } = self.getConfig();\n                if (dbStatementSerializer) {\n                    attributes[semantic_conventions_1.SEMATTRS_DB_STATEMENT] = dbStatementSerializer('aggregate', {\n                        options: this.options,\n                        aggregatePipeline: this._pipeline,\n                    });\n                }\n                const span = self._startSpan(this._model.collection, (_a = this._model) === null || _a === void 0 ? void 0 : _a.modelName, 'aggregate', attributes, parentSpan);\n                return self._handleResponse(span, originalAggregate, this, arguments, callback, moduleVersion);\n            };\n        };\n    }\n    patchQueryExec(moduleVersion) {\n        const self = this;\n        return (originalExec) => {\n            return function exec(callback) {\n                if (self.getConfig().requireParentSpan &&\n                    api_1.trace.getSpan(api_1.context.active()) === undefined) {\n                    return originalExec.apply(this, arguments);\n                }\n                const parentSpan = this[exports._STORED_PARENT_SPAN];\n                const attributes = {};\n                const { dbStatementSerializer } = self.getConfig();\n                if (dbStatementSerializer) {\n                    attributes[semantic_conventions_1.SEMATTRS_DB_STATEMENT] = dbStatementSerializer(this.op, {\n                        condition: this._conditions,\n                        updates: this._update,\n                        options: this.options,\n                        fields: this._fields,\n                    });\n                }\n                const span = self._startSpan(this.mongooseCollection, this.model.modelName, this.op, attributes, parentSpan);\n                return self._handleResponse(span, originalExec, this, arguments, callback, moduleVersion);\n            };\n        };\n    }\n    patchOnModelMethods(op, moduleVersion) {\n        const self = this;\n        return (originalOnModelFunction) => {\n            return function method(options, callback) {\n                if (self.getConfig().requireParentSpan &&\n                    api_1.trace.getSpan(api_1.context.active()) === undefined) {\n                    return originalOnModelFunction.apply(this, arguments);\n                }\n                const serializePayload = { document: this };\n                if (options && !(options instanceof Function)) {\n                    serializePayload.options = options;\n                }\n                const attributes = {};\n                const { dbStatementSerializer } = self.getConfig();\n                if (dbStatementSerializer) {\n                    attributes[semantic_conventions_1.SEMATTRS_DB_STATEMENT] = dbStatementSerializer(op, serializePayload);\n                }\n                const span = self._startSpan(this.constructor.collection, this.constructor.modelName, op, attributes);\n                if (options instanceof Function) {\n                    callback = options;\n                    options = undefined;\n                }\n                return self._handleResponse(span, originalOnModelFunction, this, arguments, callback, moduleVersion);\n            };\n        };\n    }\n    // we want to capture the otel span on the object which is calling exec.\n    // in the special case of aggregate, we need have no function to path\n    // on the Aggregate object to capture the context on, so we patch\n    // the aggregate of Model, and set the context on the Aggregate object\n    patchModelAggregate() {\n        const self = this;\n        return (original) => {\n            return function captureSpanContext() {\n                const currentSpan = api_1.trace.getSpan(api_1.context.active());\n                const aggregate = self._callOriginalFunction(() => original.apply(this, arguments));\n                if (aggregate)\n                    aggregate[exports._STORED_PARENT_SPAN] = currentSpan;\n                return aggregate;\n            };\n        };\n    }\n    patchAndCaptureSpanContext(funcName) {\n        const self = this;\n        return (original) => {\n            return function captureSpanContext() {\n                this[exports._STORED_PARENT_SPAN] = api_1.trace.getSpan(api_1.context.active());\n                return self._callOriginalFunction(() => original.apply(this, arguments));\n            };\n        };\n    }\n    _startSpan(collection, modelName, operation, attributes, parentSpan) {\n        return this.tracer.startSpan(`mongoose.${modelName}.${operation}`, {\n            kind: api_1.SpanKind.CLIENT,\n            attributes: Object.assign(Object.assign(Object.assign({}, attributes), (0, utils_1.getAttributesFromCollection)(collection)), { [semantic_conventions_1.SEMATTRS_DB_OPERATION]: operation, [semantic_conventions_1.SEMATTRS_DB_SYSTEM]: 'mongoose' }),\n        }, parentSpan ? api_1.trace.setSpan(api_1.context.active(), parentSpan) : undefined);\n    }\n    _handleResponse(span, exec, originalThis, args, callback, moduleVersion = undefined) {\n        const self = this;\n        if (callback instanceof Function) {\n            return self._callOriginalFunction(() => (0, utils_1.handleCallbackResponse)(callback, exec, originalThis, span, args, self.getConfig().responseHook, moduleVersion));\n        }\n        else {\n            const response = self._callOriginalFunction(() => exec.apply(originalThis, args));\n            return (0, utils_1.handlePromiseResponse)(response, span, self.getConfig().responseHook, moduleVersion);\n        }\n    }\n    _callOriginalFunction(originalFunction) {\n        if (this.getConfig().suppressInternalInstrumentation) {\n            return api_1.context.with((0, core_1.suppressTracing)(api_1.context.active()), originalFunction);\n        }\n        else {\n            return originalFunction();\n        }\n    }\n}\nexports.MongooseInstrumentation = MongooseInstrumentation;\n//# sourceMappingURL=mongoose.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_abf4b6a813f0c904766ef5a676a2a270/node_modules/@opentelemetry/instrumentation-mongoose/build/src/mongoose.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_abf4b6a813f0c904766ef5a676a2a270/node_modules/@opentelemetry/instrumentation-mongoose/build/src/types.js":
/*!*********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_abf4b6a813f0c904766ef5a676a2a270/node_modules/@opentelemetry/instrumentation-mongoose/build/src/types.js ***!
  \*********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9Ab3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfYWJmNGI2YTgxM2YwYzkwNDc2NmVmNWE2NzZhMmEyNzAvbm9kZV9tb2R1bGVzL0BvcGVudGVsZW1ldHJ5L2luc3RydW1lbnRhdGlvbi1tb25nb29zZS9idWlsZC9zcmMvdHlwZXMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYW53YXJcXERvY3VtZW50c1xcQ3ViZW50d2ViXFxjdWJlbnRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF9hYmY0YjZhODEzZjBjOTA0NzY2ZWY1YTY3NmEyYTI3MFxcbm9kZV9tb2R1bGVzXFxAb3BlbnRlbGVtZXRyeVxcaW5zdHJ1bWVudGF0aW9uLW1vbmdvb3NlXFxidWlsZFxcc3JjXFx0eXBlcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXR5cGVzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_abf4b6a813f0c904766ef5a676a2a270/node_modules/@opentelemetry/instrumentation-mongoose/build/src/types.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_abf4b6a813f0c904766ef5a676a2a270/node_modules/@opentelemetry/instrumentation-mongoose/build/src/utils.js":
/*!*********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_abf4b6a813f0c904766ef5a676a2a270/node_modules/@opentelemetry/instrumentation-mongoose/build/src/utils.js ***!
  \*********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.handleCallbackResponse = exports.handlePromiseResponse = exports.getAttributesFromCollection = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nfunction getAttributesFromCollection(collection) {\n    return {\n        [semantic_conventions_1.SEMATTRS_DB_MONGODB_COLLECTION]: collection.name,\n        [semantic_conventions_1.SEMATTRS_DB_NAME]: collection.conn.name,\n        [semantic_conventions_1.SEMATTRS_DB_USER]: collection.conn.user,\n        [semantic_conventions_1.SEMATTRS_NET_PEER_NAME]: collection.conn.host,\n        [semantic_conventions_1.SEMATTRS_NET_PEER_PORT]: collection.conn.port,\n    };\n}\nexports.getAttributesFromCollection = getAttributesFromCollection;\nfunction setErrorStatus(span, error = {}) {\n    span.recordException(error);\n    span.setStatus({\n        code: api_1.SpanStatusCode.ERROR,\n        message: `${error.message} ${error.code ? `\\nMongoose Error Code: ${error.code}` : ''}`,\n    });\n}\nfunction applyResponseHook(span, response, responseHook, moduleVersion = undefined) {\n    if (!responseHook) {\n        return;\n    }\n    (0, instrumentation_1.safeExecuteInTheMiddle)(() => responseHook(span, { moduleVersion, response }), e => {\n        if (e) {\n            api_1.diag.error('mongoose instrumentation: responseHook error', e);\n        }\n    }, true);\n}\nfunction handlePromiseResponse(execResponse, span, responseHook, moduleVersion = undefined) {\n    if (!(execResponse instanceof Promise)) {\n        applyResponseHook(span, execResponse, responseHook, moduleVersion);\n        span.end();\n        return execResponse;\n    }\n    return execResponse\n        .then(response => {\n        applyResponseHook(span, response, responseHook, moduleVersion);\n        return response;\n    })\n        .catch(err => {\n        setErrorStatus(span, err);\n        throw err;\n    })\n        .finally(() => span.end());\n}\nexports.handlePromiseResponse = handlePromiseResponse;\nfunction handleCallbackResponse(callback, exec, originalThis, span, args, responseHook, moduleVersion = undefined) {\n    let callbackArgumentIndex = 0;\n    if (args.length === 2) {\n        callbackArgumentIndex = 1;\n    }\n    args[callbackArgumentIndex] = (err, response) => {\n        err\n            ? setErrorStatus(span, err)\n            : applyResponseHook(span, response, responseHook, moduleVersion);\n        span.end();\n        return callback(err, response);\n    };\n    return exec.apply(originalThis, args);\n}\nexports.handleCallbackResponse = handleCallbackResponse;\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_abf4b6a813f0c904766ef5a676a2a270/node_modules/@opentelemetry/instrumentation-mongoose/build/src/utils.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_abf4b6a813f0c904766ef5a676a2a270/node_modules/@opentelemetry/instrumentation-mongoose/build/src/version.js":
/*!***********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_abf4b6a813f0c904766ef5a676a2a270/node_modules/@opentelemetry/instrumentation-mongoose/build/src/version.js ***!
  \***********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.46.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-mongoose';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_abf4b6a813f0c904766ef5a676a2a270/node_modules/@opentelemetry/instrumentation-mongoose/build/src/version.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_abf4b6a813f0c904766ef5a676a2a270/node_modules/@opentelemetry/instrumentation-mongoose/build/src/index.js":
/*!*********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_abf4b6a813f0c904766ef5a676a2a270/node_modules/@opentelemetry/instrumentation-mongoose/build/src/index.js ***!
  \*********************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n__exportStar(__webpack_require__(/*! ./mongoose */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_abf4b6a813f0c904766ef5a676a2a270/node_modules/@opentelemetry/instrumentation-mongoose/build/src/mongoose.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_abf4b6a813f0c904766ef5a676a2a270/node_modules/@opentelemetry/instrumentation-mongoose/build/src/types.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_abf4b6a813f0c904766ef5a676a2a270/node_modules/@opentelemetry/instrumentation-mongoose/build/src/index.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_abf4b6a813f0c904766ef5a676a2a270/node_modules/@opentelemetry/instrumentation-mongoose/build/src/mongoose.js":
/*!************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_abf4b6a813f0c904766ef5a676a2a270/node_modules/@opentelemetry/instrumentation-mongoose/build/src/mongoose.js ***!
  \************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.MongooseInstrumentation = exports._STORED_PARENT_SPAN = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst core_1 = __webpack_require__(/*! @opentelemetry/core */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+core@1.30.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/core/build/esm/index.js\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_abf4b6a813f0c904766ef5a676a2a270/node_modules/@opentelemetry/instrumentation-mongoose/build/src/utils.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_abf4b6a813f0c904766ef5a676a2a270/node_modules/@opentelemetry/instrumentation-mongoose/build/src/version.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst contextCaptureFunctionsCommon = [\n    'deleteOne',\n    'deleteMany',\n    'find',\n    'findOne',\n    'estimatedDocumentCount',\n    'countDocuments',\n    'distinct',\n    'where',\n    '$where',\n    'findOneAndUpdate',\n    'findOneAndDelete',\n    'findOneAndReplace',\n];\nconst contextCaptureFunctions6 = [\n    'remove',\n    'count',\n    'findOneAndRemove',\n    ...contextCaptureFunctionsCommon,\n];\nconst contextCaptureFunctions7 = [\n    'count',\n    'findOneAndRemove',\n    ...contextCaptureFunctionsCommon,\n];\nconst contextCaptureFunctions8 = [...contextCaptureFunctionsCommon];\nfunction getContextCaptureFunctions(moduleVersion) {\n    /* istanbul ignore next */\n    if (!moduleVersion) {\n        return contextCaptureFunctionsCommon;\n    }\n    else if (moduleVersion.startsWith('6.') || moduleVersion.startsWith('5.')) {\n        return contextCaptureFunctions6;\n    }\n    else if (moduleVersion.startsWith('7.')) {\n        return contextCaptureFunctions7;\n    }\n    else {\n        return contextCaptureFunctions8;\n    }\n}\nfunction instrumentRemove(moduleVersion) {\n    return ((moduleVersion &&\n        (moduleVersion.startsWith('5.') || moduleVersion.startsWith('6.'))) ||\n        false);\n}\n// when mongoose functions are called, we store the original call context\n// and then set it as the parent for the spans created by Query/Aggregate exec()\n// calls. this bypass the unlinked spans issue on thenables await operations.\nexports._STORED_PARENT_SPAN = Symbol('stored-parent-span');\nclass MongooseInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, config);\n    }\n    init() {\n        const module = new instrumentation_1.InstrumentationNodeModuleDefinition('mongoose', ['>=5.9.7 <9'], this.patch.bind(this), this.unpatch.bind(this));\n        return module;\n    }\n    patch(moduleExports, moduleVersion) {\n        this._wrap(moduleExports.Model.prototype, 'save', this.patchOnModelMethods('save', moduleVersion));\n        // mongoose applies this code on module require:\n        // Model.prototype.$save = Model.prototype.save;\n        // which captures the save function before it is patched.\n        // so we need to apply the same logic after instrumenting the save function.\n        moduleExports.Model.prototype.$save = moduleExports.Model.prototype.save;\n        if (instrumentRemove(moduleVersion)) {\n            this._wrap(moduleExports.Model.prototype, 'remove', this.patchOnModelMethods('remove', moduleVersion));\n        }\n        this._wrap(moduleExports.Query.prototype, 'exec', this.patchQueryExec(moduleVersion));\n        this._wrap(moduleExports.Aggregate.prototype, 'exec', this.patchAggregateExec(moduleVersion));\n        const contextCaptureFunctions = getContextCaptureFunctions(moduleVersion);\n        contextCaptureFunctions.forEach((funcName) => {\n            this._wrap(moduleExports.Query.prototype, funcName, this.patchAndCaptureSpanContext(funcName));\n        });\n        this._wrap(moduleExports.Model, 'aggregate', this.patchModelAggregate());\n        return moduleExports;\n    }\n    unpatch(moduleExports, moduleVersion) {\n        const contextCaptureFunctions = getContextCaptureFunctions(moduleVersion);\n        this._unwrap(moduleExports.Model.prototype, 'save');\n        // revert the patch for $save which we applied by aliasing it to patched `save`\n        moduleExports.Model.prototype.$save = moduleExports.Model.prototype.save;\n        if (instrumentRemove(moduleVersion)) {\n            this._unwrap(moduleExports.Model.prototype, 'remove');\n        }\n        this._unwrap(moduleExports.Query.prototype, 'exec');\n        this._unwrap(moduleExports.Aggregate.prototype, 'exec');\n        contextCaptureFunctions.forEach((funcName) => {\n            this._unwrap(moduleExports.Query.prototype, funcName);\n        });\n        this._unwrap(moduleExports.Model, 'aggregate');\n    }\n    patchAggregateExec(moduleVersion) {\n        const self = this;\n        return (originalAggregate) => {\n            return function exec(callback) {\n                var _a;\n                if (self.getConfig().requireParentSpan &&\n                    api_1.trace.getSpan(api_1.context.active()) === undefined) {\n                    return originalAggregate.apply(this, arguments);\n                }\n                const parentSpan = this[exports._STORED_PARENT_SPAN];\n                const attributes = {};\n                const { dbStatementSerializer } = self.getConfig();\n                if (dbStatementSerializer) {\n                    attributes[semantic_conventions_1.SEMATTRS_DB_STATEMENT] = dbStatementSerializer('aggregate', {\n                        options: this.options,\n                        aggregatePipeline: this._pipeline,\n                    });\n                }\n                const span = self._startSpan(this._model.collection, (_a = this._model) === null || _a === void 0 ? void 0 : _a.modelName, 'aggregate', attributes, parentSpan);\n                return self._handleResponse(span, originalAggregate, this, arguments, callback, moduleVersion);\n            };\n        };\n    }\n    patchQueryExec(moduleVersion) {\n        const self = this;\n        return (originalExec) => {\n            return function exec(callback) {\n                if (self.getConfig().requireParentSpan &&\n                    api_1.trace.getSpan(api_1.context.active()) === undefined) {\n                    return originalExec.apply(this, arguments);\n                }\n                const parentSpan = this[exports._STORED_PARENT_SPAN];\n                const attributes = {};\n                const { dbStatementSerializer } = self.getConfig();\n                if (dbStatementSerializer) {\n                    attributes[semantic_conventions_1.SEMATTRS_DB_STATEMENT] = dbStatementSerializer(this.op, {\n                        condition: this._conditions,\n                        updates: this._update,\n                        options: this.options,\n                        fields: this._fields,\n                    });\n                }\n                const span = self._startSpan(this.mongooseCollection, this.model.modelName, this.op, attributes, parentSpan);\n                return self._handleResponse(span, originalExec, this, arguments, callback, moduleVersion);\n            };\n        };\n    }\n    patchOnModelMethods(op, moduleVersion) {\n        const self = this;\n        return (originalOnModelFunction) => {\n            return function method(options, callback) {\n                if (self.getConfig().requireParentSpan &&\n                    api_1.trace.getSpan(api_1.context.active()) === undefined) {\n                    return originalOnModelFunction.apply(this, arguments);\n                }\n                const serializePayload = { document: this };\n                if (options && !(options instanceof Function)) {\n                    serializePayload.options = options;\n                }\n                const attributes = {};\n                const { dbStatementSerializer } = self.getConfig();\n                if (dbStatementSerializer) {\n                    attributes[semantic_conventions_1.SEMATTRS_DB_STATEMENT] = dbStatementSerializer(op, serializePayload);\n                }\n                const span = self._startSpan(this.constructor.collection, this.constructor.modelName, op, attributes);\n                if (options instanceof Function) {\n                    callback = options;\n                    options = undefined;\n                }\n                return self._handleResponse(span, originalOnModelFunction, this, arguments, callback, moduleVersion);\n            };\n        };\n    }\n    // we want to capture the otel span on the object which is calling exec.\n    // in the special case of aggregate, we need have no function to path\n    // on the Aggregate object to capture the context on, so we patch\n    // the aggregate of Model, and set the context on the Aggregate object\n    patchModelAggregate() {\n        const self = this;\n        return (original) => {\n            return function captureSpanContext() {\n                const currentSpan = api_1.trace.getSpan(api_1.context.active());\n                const aggregate = self._callOriginalFunction(() => original.apply(this, arguments));\n                if (aggregate)\n                    aggregate[exports._STORED_PARENT_SPAN] = currentSpan;\n                return aggregate;\n            };\n        };\n    }\n    patchAndCaptureSpanContext(funcName) {\n        const self = this;\n        return (original) => {\n            return function captureSpanContext() {\n                this[exports._STORED_PARENT_SPAN] = api_1.trace.getSpan(api_1.context.active());\n                return self._callOriginalFunction(() => original.apply(this, arguments));\n            };\n        };\n    }\n    _startSpan(collection, modelName, operation, attributes, parentSpan) {\n        return this.tracer.startSpan(`mongoose.${modelName}.${operation}`, {\n            kind: api_1.SpanKind.CLIENT,\n            attributes: Object.assign(Object.assign(Object.assign({}, attributes), (0, utils_1.getAttributesFromCollection)(collection)), { [semantic_conventions_1.SEMATTRS_DB_OPERATION]: operation, [semantic_conventions_1.SEMATTRS_DB_SYSTEM]: 'mongoose' }),\n        }, parentSpan ? api_1.trace.setSpan(api_1.context.active(), parentSpan) : undefined);\n    }\n    _handleResponse(span, exec, originalThis, args, callback, moduleVersion = undefined) {\n        const self = this;\n        if (callback instanceof Function) {\n            return self._callOriginalFunction(() => (0, utils_1.handleCallbackResponse)(callback, exec, originalThis, span, args, self.getConfig().responseHook, moduleVersion));\n        }\n        else {\n            const response = self._callOriginalFunction(() => exec.apply(originalThis, args));\n            return (0, utils_1.handlePromiseResponse)(response, span, self.getConfig().responseHook, moduleVersion);\n        }\n    }\n    _callOriginalFunction(originalFunction) {\n        if (this.getConfig().suppressInternalInstrumentation) {\n            return api_1.context.with((0, core_1.suppressTracing)(api_1.context.active()), originalFunction);\n        }\n        else {\n            return originalFunction();\n        }\n    }\n}\nexports.MongooseInstrumentation = MongooseInstrumentation;\n//# sourceMappingURL=mongoose.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_abf4b6a813f0c904766ef5a676a2a270/node_modules/@opentelemetry/instrumentation-mongoose/build/src/mongoose.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_abf4b6a813f0c904766ef5a676a2a270/node_modules/@opentelemetry/instrumentation-mongoose/build/src/types.js":
/*!*********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_abf4b6a813f0c904766ef5a676a2a270/node_modules/@opentelemetry/instrumentation-mongoose/build/src/types.js ***!
  \*********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF9hYmY0YjZhODEzZjBjOTA0NzY2ZWY1YTY3NmEyYTI3MC9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLW1vbmdvb3NlL2J1aWxkL3NyYy90eXBlcy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbndhclxcRG9jdW1lbnRzXFxDdWJlbnR3ZWJcXGN1YmVudFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQG9wZW50ZWxlbWV0cnkraW5zdHJ1bWVudGF0X2FiZjRiNmE4MTNmMGM5MDQ3NjZlZjVhNjc2YTJhMjcwXFxub2RlX21vZHVsZXNcXEBvcGVudGVsZW1ldHJ5XFxpbnN0cnVtZW50YXRpb24tbW9uZ29vc2VcXGJ1aWxkXFxzcmNcXHR5cGVzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dHlwZXMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_abf4b6a813f0c904766ef5a676a2a270/node_modules/@opentelemetry/instrumentation-mongoose/build/src/types.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_abf4b6a813f0c904766ef5a676a2a270/node_modules/@opentelemetry/instrumentation-mongoose/build/src/utils.js":
/*!*********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_abf4b6a813f0c904766ef5a676a2a270/node_modules/@opentelemetry/instrumentation-mongoose/build/src/utils.js ***!
  \*********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.handleCallbackResponse = exports.handlePromiseResponse = exports.getAttributesFromCollection = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nfunction getAttributesFromCollection(collection) {\n    return {\n        [semantic_conventions_1.SEMATTRS_DB_MONGODB_COLLECTION]: collection.name,\n        [semantic_conventions_1.SEMATTRS_DB_NAME]: collection.conn.name,\n        [semantic_conventions_1.SEMATTRS_DB_USER]: collection.conn.user,\n        [semantic_conventions_1.SEMATTRS_NET_PEER_NAME]: collection.conn.host,\n        [semantic_conventions_1.SEMATTRS_NET_PEER_PORT]: collection.conn.port,\n    };\n}\nexports.getAttributesFromCollection = getAttributesFromCollection;\nfunction setErrorStatus(span, error = {}) {\n    span.recordException(error);\n    span.setStatus({\n        code: api_1.SpanStatusCode.ERROR,\n        message: `${error.message} ${error.code ? `\\nMongoose Error Code: ${error.code}` : ''}`,\n    });\n}\nfunction applyResponseHook(span, response, responseHook, moduleVersion = undefined) {\n    if (!responseHook) {\n        return;\n    }\n    (0, instrumentation_1.safeExecuteInTheMiddle)(() => responseHook(span, { moduleVersion, response }), e => {\n        if (e) {\n            api_1.diag.error('mongoose instrumentation: responseHook error', e);\n        }\n    }, true);\n}\nfunction handlePromiseResponse(execResponse, span, responseHook, moduleVersion = undefined) {\n    if (!(execResponse instanceof Promise)) {\n        applyResponseHook(span, execResponse, responseHook, moduleVersion);\n        span.end();\n        return execResponse;\n    }\n    return execResponse\n        .then(response => {\n        applyResponseHook(span, response, responseHook, moduleVersion);\n        return response;\n    })\n        .catch(err => {\n        setErrorStatus(span, err);\n        throw err;\n    })\n        .finally(() => span.end());\n}\nexports.handlePromiseResponse = handlePromiseResponse;\nfunction handleCallbackResponse(callback, exec, originalThis, span, args, responseHook, moduleVersion = undefined) {\n    let callbackArgumentIndex = 0;\n    if (args.length === 2) {\n        callbackArgumentIndex = 1;\n    }\n    args[callbackArgumentIndex] = (err, response) => {\n        err\n            ? setErrorStatus(span, err)\n            : applyResponseHook(span, response, responseHook, moduleVersion);\n        span.end();\n        return callback(err, response);\n    };\n    return exec.apply(originalThis, args);\n}\nexports.handleCallbackResponse = handleCallbackResponse;\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_abf4b6a813f0c904766ef5a676a2a270/node_modules/@opentelemetry/instrumentation-mongoose/build/src/utils.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_abf4b6a813f0c904766ef5a676a2a270/node_modules/@opentelemetry/instrumentation-mongoose/build/src/version.js":
/*!***********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_abf4b6a813f0c904766ef5a676a2a270/node_modules/@opentelemetry/instrumentation-mongoose/build/src/version.js ***!
  \***********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.46.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-mongoose';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_abf4b6a813f0c904766ef5a676a2a270/node_modules/@opentelemetry/instrumentation-mongoose/build/src/version.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_abf4b6a813f0c904766ef5a676a2a270/node_modules/@opentelemetry/instrumentation-mongoose/build/src/index.js":
/*!*********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_abf4b6a813f0c904766ef5a676a2a270/node_modules/@opentelemetry/instrumentation-mongoose/build/src/index.js ***!
  \*********************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n__exportStar(__webpack_require__(/*! ./mongoose */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_abf4b6a813f0c904766ef5a676a2a270/node_modules/@opentelemetry/instrumentation-mongoose/build/src/mongoose.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_abf4b6a813f0c904766ef5a676a2a270/node_modules/@opentelemetry/instrumentation-mongoose/build/src/types.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_abf4b6a813f0c904766ef5a676a2a270/node_modules/@opentelemetry/instrumentation-mongoose/build/src/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_abf4b6a813f0c904766ef5a676a2a270/node_modules/@opentelemetry/instrumentation-mongoose/build/src/mongoose.js":
/*!************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_abf4b6a813f0c904766ef5a676a2a270/node_modules/@opentelemetry/instrumentation-mongoose/build/src/mongoose.js ***!
  \************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.MongooseInstrumentation = exports._STORED_PARENT_SPAN = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst core_1 = __webpack_require__(/*! @opentelemetry/core */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+core@1.30.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/core/build/esm/index.js\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_abf4b6a813f0c904766ef5a676a2a270/node_modules/@opentelemetry/instrumentation-mongoose/build/src/utils.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_abf4b6a813f0c904766ef5a676a2a270/node_modules/@opentelemetry/instrumentation-mongoose/build/src/version.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst contextCaptureFunctionsCommon = [\n    'deleteOne',\n    'deleteMany',\n    'find',\n    'findOne',\n    'estimatedDocumentCount',\n    'countDocuments',\n    'distinct',\n    'where',\n    '$where',\n    'findOneAndUpdate',\n    'findOneAndDelete',\n    'findOneAndReplace',\n];\nconst contextCaptureFunctions6 = [\n    'remove',\n    'count',\n    'findOneAndRemove',\n    ...contextCaptureFunctionsCommon,\n];\nconst contextCaptureFunctions7 = [\n    'count',\n    'findOneAndRemove',\n    ...contextCaptureFunctionsCommon,\n];\nconst contextCaptureFunctions8 = [...contextCaptureFunctionsCommon];\nfunction getContextCaptureFunctions(moduleVersion) {\n    /* istanbul ignore next */\n    if (!moduleVersion) {\n        return contextCaptureFunctionsCommon;\n    }\n    else if (moduleVersion.startsWith('6.') || moduleVersion.startsWith('5.')) {\n        return contextCaptureFunctions6;\n    }\n    else if (moduleVersion.startsWith('7.')) {\n        return contextCaptureFunctions7;\n    }\n    else {\n        return contextCaptureFunctions8;\n    }\n}\nfunction instrumentRemove(moduleVersion) {\n    return ((moduleVersion &&\n        (moduleVersion.startsWith('5.') || moduleVersion.startsWith('6.'))) ||\n        false);\n}\n// when mongoose functions are called, we store the original call context\n// and then set it as the parent for the spans created by Query/Aggregate exec()\n// calls. this bypass the unlinked spans issue on thenables await operations.\nexports._STORED_PARENT_SPAN = Symbol('stored-parent-span');\nclass MongooseInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, config);\n    }\n    init() {\n        const module = new instrumentation_1.InstrumentationNodeModuleDefinition('mongoose', ['>=5.9.7 <9'], this.patch.bind(this), this.unpatch.bind(this));\n        return module;\n    }\n    patch(moduleExports, moduleVersion) {\n        this._wrap(moduleExports.Model.prototype, 'save', this.patchOnModelMethods('save', moduleVersion));\n        // mongoose applies this code on module require:\n        // Model.prototype.$save = Model.prototype.save;\n        // which captures the save function before it is patched.\n        // so we need to apply the same logic after instrumenting the save function.\n        moduleExports.Model.prototype.$save = moduleExports.Model.prototype.save;\n        if (instrumentRemove(moduleVersion)) {\n            this._wrap(moduleExports.Model.prototype, 'remove', this.patchOnModelMethods('remove', moduleVersion));\n        }\n        this._wrap(moduleExports.Query.prototype, 'exec', this.patchQueryExec(moduleVersion));\n        this._wrap(moduleExports.Aggregate.prototype, 'exec', this.patchAggregateExec(moduleVersion));\n        const contextCaptureFunctions = getContextCaptureFunctions(moduleVersion);\n        contextCaptureFunctions.forEach((funcName) => {\n            this._wrap(moduleExports.Query.prototype, funcName, this.patchAndCaptureSpanContext(funcName));\n        });\n        this._wrap(moduleExports.Model, 'aggregate', this.patchModelAggregate());\n        return moduleExports;\n    }\n    unpatch(moduleExports, moduleVersion) {\n        const contextCaptureFunctions = getContextCaptureFunctions(moduleVersion);\n        this._unwrap(moduleExports.Model.prototype, 'save');\n        // revert the patch for $save which we applied by aliasing it to patched `save`\n        moduleExports.Model.prototype.$save = moduleExports.Model.prototype.save;\n        if (instrumentRemove(moduleVersion)) {\n            this._unwrap(moduleExports.Model.prototype, 'remove');\n        }\n        this._unwrap(moduleExports.Query.prototype, 'exec');\n        this._unwrap(moduleExports.Aggregate.prototype, 'exec');\n        contextCaptureFunctions.forEach((funcName) => {\n            this._unwrap(moduleExports.Query.prototype, funcName);\n        });\n        this._unwrap(moduleExports.Model, 'aggregate');\n    }\n    patchAggregateExec(moduleVersion) {\n        const self = this;\n        return (originalAggregate) => {\n            return function exec(callback) {\n                var _a;\n                if (self.getConfig().requireParentSpan &&\n                    api_1.trace.getSpan(api_1.context.active()) === undefined) {\n                    return originalAggregate.apply(this, arguments);\n                }\n                const parentSpan = this[exports._STORED_PARENT_SPAN];\n                const attributes = {};\n                const { dbStatementSerializer } = self.getConfig();\n                if (dbStatementSerializer) {\n                    attributes[semantic_conventions_1.SEMATTRS_DB_STATEMENT] = dbStatementSerializer('aggregate', {\n                        options: this.options,\n                        aggregatePipeline: this._pipeline,\n                    });\n                }\n                const span = self._startSpan(this._model.collection, (_a = this._model) === null || _a === void 0 ? void 0 : _a.modelName, 'aggregate', attributes, parentSpan);\n                return self._handleResponse(span, originalAggregate, this, arguments, callback, moduleVersion);\n            };\n        };\n    }\n    patchQueryExec(moduleVersion) {\n        const self = this;\n        return (originalExec) => {\n            return function exec(callback) {\n                if (self.getConfig().requireParentSpan &&\n                    api_1.trace.getSpan(api_1.context.active()) === undefined) {\n                    return originalExec.apply(this, arguments);\n                }\n                const parentSpan = this[exports._STORED_PARENT_SPAN];\n                const attributes = {};\n                const { dbStatementSerializer } = self.getConfig();\n                if (dbStatementSerializer) {\n                    attributes[semantic_conventions_1.SEMATTRS_DB_STATEMENT] = dbStatementSerializer(this.op, {\n                        condition: this._conditions,\n                        updates: this._update,\n                        options: this.options,\n                        fields: this._fields,\n                    });\n                }\n                const span = self._startSpan(this.mongooseCollection, this.model.modelName, this.op, attributes, parentSpan);\n                return self._handleResponse(span, originalExec, this, arguments, callback, moduleVersion);\n            };\n        };\n    }\n    patchOnModelMethods(op, moduleVersion) {\n        const self = this;\n        return (originalOnModelFunction) => {\n            return function method(options, callback) {\n                if (self.getConfig().requireParentSpan &&\n                    api_1.trace.getSpan(api_1.context.active()) === undefined) {\n                    return originalOnModelFunction.apply(this, arguments);\n                }\n                const serializePayload = { document: this };\n                if (options && !(options instanceof Function)) {\n                    serializePayload.options = options;\n                }\n                const attributes = {};\n                const { dbStatementSerializer } = self.getConfig();\n                if (dbStatementSerializer) {\n                    attributes[semantic_conventions_1.SEMATTRS_DB_STATEMENT] = dbStatementSerializer(op, serializePayload);\n                }\n                const span = self._startSpan(this.constructor.collection, this.constructor.modelName, op, attributes);\n                if (options instanceof Function) {\n                    callback = options;\n                    options = undefined;\n                }\n                return self._handleResponse(span, originalOnModelFunction, this, arguments, callback, moduleVersion);\n            };\n        };\n    }\n    // we want to capture the otel span on the object which is calling exec.\n    // in the special case of aggregate, we need have no function to path\n    // on the Aggregate object to capture the context on, so we patch\n    // the aggregate of Model, and set the context on the Aggregate object\n    patchModelAggregate() {\n        const self = this;\n        return (original) => {\n            return function captureSpanContext() {\n                const currentSpan = api_1.trace.getSpan(api_1.context.active());\n                const aggregate = self._callOriginalFunction(() => original.apply(this, arguments));\n                if (aggregate)\n                    aggregate[exports._STORED_PARENT_SPAN] = currentSpan;\n                return aggregate;\n            };\n        };\n    }\n    patchAndCaptureSpanContext(funcName) {\n        const self = this;\n        return (original) => {\n            return function captureSpanContext() {\n                this[exports._STORED_PARENT_SPAN] = api_1.trace.getSpan(api_1.context.active());\n                return self._callOriginalFunction(() => original.apply(this, arguments));\n            };\n        };\n    }\n    _startSpan(collection, modelName, operation, attributes, parentSpan) {\n        return this.tracer.startSpan(`mongoose.${modelName}.${operation}`, {\n            kind: api_1.SpanKind.CLIENT,\n            attributes: Object.assign(Object.assign(Object.assign({}, attributes), (0, utils_1.getAttributesFromCollection)(collection)), { [semantic_conventions_1.SEMATTRS_DB_OPERATION]: operation, [semantic_conventions_1.SEMATTRS_DB_SYSTEM]: 'mongoose' }),\n        }, parentSpan ? api_1.trace.setSpan(api_1.context.active(), parentSpan) : undefined);\n    }\n    _handleResponse(span, exec, originalThis, args, callback, moduleVersion = undefined) {\n        const self = this;\n        if (callback instanceof Function) {\n            return self._callOriginalFunction(() => (0, utils_1.handleCallbackResponse)(callback, exec, originalThis, span, args, self.getConfig().responseHook, moduleVersion));\n        }\n        else {\n            const response = self._callOriginalFunction(() => exec.apply(originalThis, args));\n            return (0, utils_1.handlePromiseResponse)(response, span, self.getConfig().responseHook, moduleVersion);\n        }\n    }\n    _callOriginalFunction(originalFunction) {\n        if (this.getConfig().suppressInternalInstrumentation) {\n            return api_1.context.with((0, core_1.suppressTracing)(api_1.context.active()), originalFunction);\n        }\n        else {\n            return originalFunction();\n        }\n    }\n}\nexports.MongooseInstrumentation = MongooseInstrumentation;\n//# sourceMappingURL=mongoose.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_abf4b6a813f0c904766ef5a676a2a270/node_modules/@opentelemetry/instrumentation-mongoose/build/src/mongoose.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_abf4b6a813f0c904766ef5a676a2a270/node_modules/@opentelemetry/instrumentation-mongoose/build/src/types.js":
/*!*********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_abf4b6a813f0c904766ef5a676a2a270/node_modules/@opentelemetry/instrumentation-mongoose/build/src/types.js ***!
  \*********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF9hYmY0YjZhODEzZjBjOTA0NzY2ZWY1YTY3NmEyYTI3MC9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLW1vbmdvb3NlL2J1aWxkL3NyYy90eXBlcy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbndhclxcRG9jdW1lbnRzXFxDdWJlbnR3ZWJcXGN1YmVudFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQG9wZW50ZWxlbWV0cnkraW5zdHJ1bWVudGF0X2FiZjRiNmE4MTNmMGM5MDQ3NjZlZjVhNjc2YTJhMjcwXFxub2RlX21vZHVsZXNcXEBvcGVudGVsZW1ldHJ5XFxpbnN0cnVtZW50YXRpb24tbW9uZ29vc2VcXGJ1aWxkXFxzcmNcXHR5cGVzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dHlwZXMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_abf4b6a813f0c904766ef5a676a2a270/node_modules/@opentelemetry/instrumentation-mongoose/build/src/types.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_abf4b6a813f0c904766ef5a676a2a270/node_modules/@opentelemetry/instrumentation-mongoose/build/src/utils.js":
/*!*********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_abf4b6a813f0c904766ef5a676a2a270/node_modules/@opentelemetry/instrumentation-mongoose/build/src/utils.js ***!
  \*********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.handleCallbackResponse = exports.handlePromiseResponse = exports.getAttributesFromCollection = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nfunction getAttributesFromCollection(collection) {\n    return {\n        [semantic_conventions_1.SEMATTRS_DB_MONGODB_COLLECTION]: collection.name,\n        [semantic_conventions_1.SEMATTRS_DB_NAME]: collection.conn.name,\n        [semantic_conventions_1.SEMATTRS_DB_USER]: collection.conn.user,\n        [semantic_conventions_1.SEMATTRS_NET_PEER_NAME]: collection.conn.host,\n        [semantic_conventions_1.SEMATTRS_NET_PEER_PORT]: collection.conn.port,\n    };\n}\nexports.getAttributesFromCollection = getAttributesFromCollection;\nfunction setErrorStatus(span, error = {}) {\n    span.recordException(error);\n    span.setStatus({\n        code: api_1.SpanStatusCode.ERROR,\n        message: `${error.message} ${error.code ? `\\nMongoose Error Code: ${error.code}` : ''}`,\n    });\n}\nfunction applyResponseHook(span, response, responseHook, moduleVersion = undefined) {\n    if (!responseHook) {\n        return;\n    }\n    (0, instrumentation_1.safeExecuteInTheMiddle)(() => responseHook(span, { moduleVersion, response }), e => {\n        if (e) {\n            api_1.diag.error('mongoose instrumentation: responseHook error', e);\n        }\n    }, true);\n}\nfunction handlePromiseResponse(execResponse, span, responseHook, moduleVersion = undefined) {\n    if (!(execResponse instanceof Promise)) {\n        applyResponseHook(span, execResponse, responseHook, moduleVersion);\n        span.end();\n        return execResponse;\n    }\n    return execResponse\n        .then(response => {\n        applyResponseHook(span, response, responseHook, moduleVersion);\n        return response;\n    })\n        .catch(err => {\n        setErrorStatus(span, err);\n        throw err;\n    })\n        .finally(() => span.end());\n}\nexports.handlePromiseResponse = handlePromiseResponse;\nfunction handleCallbackResponse(callback, exec, originalThis, span, args, responseHook, moduleVersion = undefined) {\n    let callbackArgumentIndex = 0;\n    if (args.length === 2) {\n        callbackArgumentIndex = 1;\n    }\n    args[callbackArgumentIndex] = (err, response) => {\n        err\n            ? setErrorStatus(span, err)\n            : applyResponseHook(span, response, responseHook, moduleVersion);\n        span.end();\n        return callback(err, response);\n    };\n    return exec.apply(originalThis, args);\n}\nexports.handleCallbackResponse = handleCallbackResponse;\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_abf4b6a813f0c904766ef5a676a2a270/node_modules/@opentelemetry/instrumentation-mongoose/build/src/utils.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_abf4b6a813f0c904766ef5a676a2a270/node_modules/@opentelemetry/instrumentation-mongoose/build/src/version.js":
/*!***********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_abf4b6a813f0c904766ef5a676a2a270/node_modules/@opentelemetry/instrumentation-mongoose/build/src/version.js ***!
  \***********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.46.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-mongoose';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_abf4b6a813f0c904766ef5a676a2a270/node_modules/@opentelemetry/instrumentation-mongoose/build/src/version.js\n");

/***/ })

};
;