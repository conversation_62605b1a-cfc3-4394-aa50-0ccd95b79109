import crypto from "crypto"
import EventEmitter from "events"
import { v4 as uuidv4 } from "uuid"

import axios from "axios"
import * as vscode from "vscode"
import { z } from "zod"

import type { CloudUserInfo } from "@cubent/types"

export interface DeviceOAuthEvents {
	"authentication-started": [data: { deviceId: string; state: string }]
	"authentication-success": [data: { token: string; userInfo?: CloudUserInfo }]
	"authentication-failed": [data: { error: string }]
	"authentication-cancelled": []
}

const tokenResponseSchema = z.object({
	token: z.string().min(1, "Token cannot be empty"),
	user: z.object({
		id: z.string(),
		email: z.string(),
		name: z.string().optional(),
		picture: z.string().optional(),
	}).optional(),
})

type TokenResponse = z.infer<typeof tokenResponseSchema>

const DEVICE_AUTH_TOKEN_KEY = "cubent-device-auth-token"
const DEVICE_AUTH_STATE_KEY = "cubent-device-auth-state"
const DEVICE_AUTH_DEVICE_ID_KEY = "cubent-device-auth-device-id"

type AuthState = "idle" | "authenticating" | "authenticated" | "failed"

export class DeviceOAuthService extends EventEmitter<DeviceOAuthEvents> {
	private context: vscode.ExtensionContext
	private state: AuthState = "idle"
	private pollingTimer: NodeJS.Timeout | null = null
	private currentDeviceId: string | null = null
	private currentState: string | null = null
	private authToken: string | null = null
	private userInfo: CloudUserInfo | null = null

	// Configuration
	private readonly AUTH_BASE_URL = "https://app-cubent.vercel.app"
	private readonly POLLING_INTERVAL = 3000 // 3 seconds
	private readonly MAX_POLLING_ATTEMPTS = 120 // 6 minutes total

	constructor(context: vscode.ExtensionContext) {
		super()
		this.context = context
	}

	/**
	 * Initialize the service and load any existing authentication state
	 */
	public async initialize(): Promise<void> {
		try {
			// Load existing token if available
			const storedToken = await this.context.secrets.get(DEVICE_AUTH_TOKEN_KEY)
			if (storedToken) {
				this.authToken = storedToken
				this.state = "authenticated"
				
				// Try to load user info (optional)
				try {
					await this.fetchUserInfo()
				} catch (error) {
					console.warn("[DeviceOAuth] Failed to fetch user info on initialization:", error)
				}
			}

			console.log("[DeviceOAuth] Initialized with state:", this.state)
		} catch (error) {
			console.error("[DeviceOAuth] Failed to initialize:", error)
			this.state = "failed"
		}
	}

	/**
	 * Start the OAuth authentication flow
	 */
	public async startAuthentication(): Promise<void> {
		if (this.state === "authenticating") {
			console.log("[DeviceOAuth] Authentication already in progress")
			return
		}

		try {
			// Generate secure device ID and state
			const deviceId = uuidv4()
			const state = crypto.randomBytes(32).toString("hex")

			// Store state for validation
			await this.context.globalState.update(DEVICE_AUTH_STATE_KEY, state)
			await this.context.globalState.update(DEVICE_AUTH_DEVICE_ID_KEY, deviceId)

			this.currentDeviceId = deviceId
			this.currentState = state
			this.state = "authenticating"

			console.log("[DeviceOAuth] Starting authentication with device ID:", deviceId)

			// Emit event for UI updates
			this.emit("authentication-started", { deviceId, state })

			// Open browser to authentication URL
			const authUrl = `${this.AUTH_BASE_URL}/sign-in?device_id=${encodeURIComponent(deviceId)}&state=${encodeURIComponent(state)}`
			await vscode.env.openExternal(vscode.Uri.parse(authUrl))

			// Start polling for token
			this.startPolling()

		} catch (error) {
			console.error("[DeviceOAuth] Failed to start authentication:", error)
			this.state = "failed"
			this.emit("authentication-failed", { error: error instanceof Error ? error.message : "Unknown error" })
		}
	}

	/**
	 * Handle authentication callback (alternative to polling)
	 */
	public async handleCallback(token: string): Promise<void> {
		try {
			console.log("[DeviceOAuth] Handling callback with token")

			// Validate and store token
			await this.storeToken(token)
			console.log("[DeviceOAuth] Token stored successfully")

			// Fetch user info
			await this.fetchUserInfo()
			console.log("[DeviceOAuth] User info fetched:", this.userInfo)

			// Update state
			this.state = "authenticated"
			this.stopPolling()

			// Clean up temporary state
			await this.cleanupAuthState()

			console.log("[DeviceOAuth] Authentication successful via callback, emitting success event with userInfo:", this.userInfo)
			this.emit("authentication-success", { token, userInfo: this.userInfo || undefined })

		} catch (error) {
			console.error("[DeviceOAuth] Callback handling failed:", error)
			this.state = "failed"
			this.emit("authentication-failed", { error: error instanceof Error ? error.message : "Callback failed" })
		}
	}

	/**
	 * Start polling the token endpoint
	 */
	private startPolling(): void {
		if (!this.currentDeviceId || !this.currentState) {
			console.error("[DeviceOAuth] Cannot start polling: missing device ID or state")
			return
		}

		let attempts = 0
		
		const poll = async () => {
			if (this.state !== "authenticating" || attempts >= this.MAX_POLLING_ATTEMPTS) {
				this.stopPolling()
				if (attempts >= this.MAX_POLLING_ATTEMPTS) {
					this.state = "failed"
					this.emit("authentication-failed", { error: "Authentication timeout" })
				}
				return
			}

			attempts++

			try {
				const response = await axios.get(`${this.AUTH_BASE_URL}/api/token`, {
					params: {
						device_id: this.currentDeviceId,
						state: this.currentState,
					},
					timeout: 5000,
				})

				if (response.status === 200 && response.data?.token) {
					const tokenData = tokenResponseSchema.parse(response.data)
					
					// Store token securely
					await this.storeToken(tokenData.token)
					
					// Store user info if provided
					if (tokenData.user) {
						this.userInfo = {
							name: tokenData.user.name || `${tokenData.user.email}`,
							email: tokenData.user.email,
							picture: tokenData.user.picture,
						}
					}

					// Update state
					this.state = "authenticated"
					this.stopPolling()

					// Clean up temporary state
					await this.cleanupAuthState()

					console.log("[DeviceOAuth] Authentication successful via polling")
					this.emit("authentication-success", { token: tokenData.token, userInfo: this.userInfo || undefined })
				}
			} catch (error) {
				// Continue polling on error (unless it's a permanent error)
				if (axios.isAxiosError(error) && error.response?.status === 404) {
					// Token not ready yet, continue polling
				} else {
					console.warn("[DeviceOAuth] Polling error:", error)
				}
			}
		}

		// Start immediate poll, then set interval
		poll()
		this.pollingTimer = setInterval(poll, this.POLLING_INTERVAL)
	}

	/**
	 * Stop polling
	 */
	private stopPolling(): void {
		if (this.pollingTimer) {
			clearInterval(this.pollingTimer)
			this.pollingTimer = null
		}
	}

	/**
	 * Store authentication token securely
	 */
	private async storeToken(token: string): Promise<void> {
		await this.context.secrets.store(DEVICE_AUTH_TOKEN_KEY, token)
		this.authToken = token
	}

	/**
	 * Fetch user information using the stored token
	 */
	private async fetchUserInfo(): Promise<void> {
		if (!this.authToken) {
			throw new Error("No authentication token available")
		}

		try {
			const response = await axios.get(`${this.AUTH_BASE_URL}/api/extension/auth`, {
				headers: {
					Authorization: `Bearer ${this.authToken}`,
				},
				timeout: 10000,
			})

			if (response.data?.user) {
				this.userInfo = {
					name: response.data.user.name || response.data.user.email,
					email: response.data.user.email,
					picture: response.data.user.picture,
				}
			}
		} catch (error) {
			console.warn("[DeviceOAuth] Failed to fetch user info:", error)
			// Don't throw - user info is optional
		}
	}

	/**
	 * Clean up temporary authentication state
	 */
	private async cleanupAuthState(): Promise<void> {
		await this.context.globalState.update(DEVICE_AUTH_STATE_KEY, undefined)
		await this.context.globalState.update(DEVICE_AUTH_DEVICE_ID_KEY, undefined)
		this.currentDeviceId = null
		this.currentState = null
	}

	/**
	 * Log out and clear all stored data
	 */
	public async logout(): Promise<void> {
		try {
			// Stop any ongoing polling
			this.stopPolling()

			// Clear stored token
			await this.context.secrets.delete(DEVICE_AUTH_TOKEN_KEY)

			// Clean up state
			await this.cleanupAuthState()

			// Reset internal state
			this.authToken = null
			this.userInfo = null
			this.state = "idle"

			console.log("[DeviceOAuth] Logged out successfully")

		} catch (error) {
			console.error("[DeviceOAuth] Logout failed:", error)
			throw error
		}
	}

	/**
	 * Cancel ongoing authentication
	 */
	public async cancelAuthentication(): Promise<void> {
		if (this.state === "authenticating") {
			this.stopPolling()
			await this.cleanupAuthState()
			this.state = "idle"
			this.emit("authentication-cancelled")
			console.log("[DeviceOAuth] Authentication cancelled")
		}
	}

	// Getters
	public getState(): AuthState {
		return this.state
	}

	public getToken(): string | null {
		return this.authToken
	}

	public getUserInfo(): CloudUserInfo | null {
		return this.userInfo
	}

	public isAuthenticated(): boolean {
		return this.state === "authenticated" && !!this.authToken
	}

	public isAuthenticating(): boolean {
		return this.state === "authenticating"
	}

	/**
	 * Dispose of the service
	 */
	public dispose(): void {
		this.stopPolling()
		this.removeAllListeners()
	}
}
