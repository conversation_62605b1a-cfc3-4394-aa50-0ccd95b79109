import React, { useState, useEffect } from "react"
import { VSCodeButton } from "@vscode/webview-ui-toolkit/react"
import { Activity, RefreshCw } from "lucide-react"
import { Section } from "./Section"

const vscode = acquireVsCodeApi()

interface UsageStats {
	currentMonth: {
		cubentUnits: number
		cubentUnitsLimit: number
		usagePercentage: number
		tokensUsed: number
		requestsMade: number
		costAccrued: number
	}
	totalUsage: {
		tokensUsed: number
		requestsMade: number
		costAccrued: number
		cubentUnits: number
	}
	dailyUsage: Array<{
		date: string
		tokensUsed: number
		requestsMade: number
		costAccrued: number
		cubentUnits: number
	}>
}

export const UsageSettings: React.FC = () => {
	const [usageStats, setUsageStats] = useState<UsageStats | null>(null)
	const [loading, setLoading] = useState(false)

	const loadUsageData = () => {
		setLoading(true)
		vscode.postMessage({ type: "getUserUsageStats" })
	}

	useEffect(() => {
		// Load usage data on component mount
		loadUsageData()

		// Listen for responses from extension
		const handleMessage = (event: MessageEvent) => {
			const message = event.data
			switch (message.type) {
				case "usageStats":
					setUsageStats(message.data)
					setLoading(false)
					break
			}
		}

		window.addEventListener("message", handleMessage)
		return () => window.removeEventListener("message", handleMessage)
	}, [])

	const handleRefresh = () => {
		loadUsageData()
	}

	const getUsageColor = (percentage: number) => {
		if (percentage >= 90) return "text-vscode-errorForeground"
		if (percentage >= 75) return "text-yellow-500"
		return "text-green-500"
	}

	const getProgressBarColor = (percentage: number) => {
		if (percentage >= 90) return "bg-vscode-errorForeground"
		if (percentage >= 75) return "bg-yellow-500"
		return "bg-green-500"
	}

	return (
		<div className="space-y-4">
			{/* Header */}
			<div className="flex items-center justify-between">
				<div className="flex items-center gap-2">
					<Activity className="w-4 h-4 text-vscode-foreground" />
					<h3 className="text-sm font-medium text-vscode-foreground">Usage Tracking</h3>
				</div>
				<VSCodeButton appearance="icon" onClick={handleRefresh} disabled={loading}>
					<RefreshCw className={`w-4 h-4 ${loading ? "animate-spin" : ""}`} />
				</VSCodeButton>
			</div>

			{/* Loading State */}
			{loading && (
				<Section>
					<div className="flex items-center justify-center py-8">
						<div className="flex items-center gap-2 text-vscode-descriptionForeground">
							<RefreshCw className="w-4 h-4 animate-spin" />
							<span className="text-sm">Loading usage data...</span>
						</div>
					</div>
				</Section>
			)}

			{/* Authentication Required Message */}
			{!usageStats && !loading && (
				<Section>
					<div className="text-center py-12">
						<div className="w-16 h-16 mx-auto mb-4 rounded-full bg-vscode-button-background flex items-center justify-center">
							<Activity className="w-8 h-8 text-vscode-button-foreground" />
						</div>
						<div className="text-lg font-medium text-vscode-foreground mb-2">Authentication Required</div>
						<div className="text-sm text-vscode-descriptionForeground mb-6 max-w-md mx-auto">
							To view your usage statistics, you need to sign in to your Cubent account first.
						</div>
						<div className="text-xs text-vscode-descriptionForeground mb-6">
							Go to the <strong>Account</strong> section to sign in to Cubent.
						</div>
						<VSCodeButton appearance="secondary" onClick={() => vscode.postMessage({ type: "accountButtonClicked" })}>
							Go to Account
						</VSCodeButton>
					</div>
				</Section>
			)}

			{/* Usage Data */}
			{usageStats && !loading && (
				<>
					{/* Current Month Usage */}
					<Section>
						<div className="space-y-4">
							<div className="text-sm font-medium text-vscode-foreground">Current Month</div>
							
							{/* Cubent Units Progress */}
							<div className="space-y-2">
								<div className="flex items-center justify-between">
									<span className="text-sm text-vscode-foreground">Cubent Units</span>
									<span className={`text-sm font-medium ${getUsageColor(usageStats.currentMonth.usagePercentage)}`}>
										{usageStats.currentMonth.cubentUnits.toFixed(1)} / {usageStats.currentMonth.cubentUnitsLimit}
									</span>
								</div>
								<div className="w-full bg-vscode-progressBar-background rounded-full h-2">
									<div
										className={`h-2 rounded-full transition-all duration-300 ${getProgressBarColor(usageStats.currentMonth.usagePercentage)}`}
										style={{ width: `${Math.min(usageStats.currentMonth.usagePercentage, 100)}%` }}
									/>
								</div>
								<div className="text-xs text-vscode-descriptionForeground">
									{usageStats.currentMonth.usagePercentage.toFixed(1)}% used
								</div>
							</div>

							{/* Usage Warning */}
							{usageStats.currentMonth.usagePercentage >= 90 && (
								<div className="p-3 bg-vscode-inputValidation-errorBackground border border-vscode-inputValidation-errorBorder rounded text-vscode-inputValidation-errorForeground text-sm">
									⚠️ You're approaching your Cubent Units limit. Consider upgrading your plan.
								</div>
							)}

							{/* Quick Stats */}
							<div className="grid grid-cols-3 gap-4 pt-2">
								<div className="text-center">
									<div className="text-lg font-semibold text-vscode-foreground">
										{usageStats.currentMonth.requestsMade}
									</div>
									<div className="text-xs text-vscode-descriptionForeground">Requests</div>
								</div>
								<div className="text-center">
									<div className="text-lg font-semibold text-vscode-foreground">
										{(usageStats.currentMonth.tokensUsed / 1000).toFixed(1)}K
									</div>
									<div className="text-xs text-vscode-descriptionForeground">Tokens</div>
								</div>
								<div className="text-center">
									<div className="text-lg font-semibold text-vscode-foreground">
										${usageStats.currentMonth.costAccrued.toFixed(2)}
									</div>
									<div className="text-xs text-vscode-descriptionForeground">Cost</div>
								</div>
							</div>
						</div>
					</Section>

					{/* Actions */}
					<Section>
						<div className="space-y-3">
							<div className="text-sm font-medium text-vscode-foreground">Actions</div>
							<div className="flex gap-2">
								<VSCodeButton appearance="secondary" onClick={() => vscode.postMessage({ type: "showUpgradePrompt" })}>
									Upgrade Plan
								</VSCodeButton>
								<VSCodeButton appearance="secondary" onClick={() => vscode.postMessage({ type: "openExternal", url: "https://cubent.dev/profile" })}>
									View Detailed Usage
								</VSCodeButton>
							</div>
						</div>
					</Section>
				</>
			)}
		</div>
	)
}
